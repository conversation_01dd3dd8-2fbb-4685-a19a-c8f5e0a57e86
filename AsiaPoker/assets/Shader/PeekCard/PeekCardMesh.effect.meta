{"ver": "1.0.27", "uuid": "88c34d22-2f6a-4d26-b40c-e57c8efda47a", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matWorld;\nuniform mat4 u_matViewProj;\nuniform vec4 u_line;\nuniform float u_rotation;\nuniform float u_radius;\nuniform float u_percent;\nuniform float u_angle;\nuniform float u_shadow;\nconst float PI = 3.141592653589793;\nattribute vec2 a_position;\nattribute vec2 a_uv0;\nattribute vec2 a_uv1;\nvarying vec2 v_uv0;\nvarying vec2 v_uv1;\nvarying float shadowRatio;\nvec3 rotateAroundAxis(vec3 P, vec3 p1, vec3 p2, float rad) {\n    vec3 axis = normalize(p2 - p1);\n    float cosA = cos(rad);\n    float sinA = sin(rad);\n    float oneMinusCos = 1.0 - cosA;\n    mat3 K = mat3(\n        0, -axis.z, axis.y,\n        axis.z, 0, -axis.x,\n        -axis.y, axis.x, 0\n    );\n    mat3 R = mat3(1.0) + sinA * K + oneMinusCos * (K * K);\n    return R * (P - p1) + p1;\n}\nvec2 calculatePointFromArcLength(float r, float arcLength)\n{\n    float quarterCircumference = 3.14159 * r / 2.0;\n    if (arcLength <= quarterCircumference)\n    {\n        float theta = arcLength / r;\n        float x = r - r * cos(theta);\n        float y = r * sin(theta);\n        return vec2(x, y);\n    }\n    else\n    {\n        float remainingArcLength = arcLength - quarterCircumference;\n        float theta = max(0.0, u_percent - 1.0) * .5 * PI;\n        float x2 = r + remainingArcLength;\n        float y2 = r + remainingArcLength * tan(theta);\n        return vec2(x2, y2);\n    }\n}\nvec2 rotatePoint(vec2 v, float theta) {\n    float cosTheta = cos(theta);\n    float sinTheta = sin(theta);\n    float xNew = v.x * cosTheta - v.y * sinTheta;\n    float yNew = v.x * sinTheta + v.y * cosTheta;\n    return vec2(xNew, yNew);\n}\nvec2 transformCoordinate(vec2 p0, vec2 p1, vec2 originalCoordinate) {\n    vec2 direction = normalize(p1 - p0);\n    float angle = atan(direction.y, direction.x);\n    vec2 midpoint = (p0 + p1) / 2.0;\n    mat2 rotationMatrix = mat2(\n        cos(angle), -sin(angle),\n        sin(angle), cos(angle)\n    );\n    vec2 translatedCoordinate = originalCoordinate - midpoint;\n    return rotationMatrix * translatedCoordinate;\n}\nvec2 inverseTransformCoordinate(vec2 p0, vec2 p1, vec2 transformedCoordinate) {\n    vec2 direction = normalize(p1 - p0);\n    float angle = atan(direction.y, direction.x);\n    vec2 midpoint = (p0 + p1) / 2.0;\n    mat2 inverseRotationMatrix = mat2(\n        cos(-angle), -sin(-angle),\n        sin(-angle), cos(-angle)\n    );\n    vec2 rotatedCoordinate = inverseRotationMatrix * transformedCoordinate;\n    return rotatedCoordinate + midpoint;\n}\nvoid main () {\n  vec4 pos = vec4(a_position, 0.0, 1);\n  if(u_line.xy != u_line.zw) {\n    vec2 p0 = u_line.xy;\n    vec2 p1 = u_line.zw;\n    vec2 pos1 = transformCoordinate(p0, p1, pos.xy);\n    vec2 pos2 = calculatePointFromArcLength(u_radius, abs(pos1.x));\n    shadowRatio = min(1.0, abs(pos1.x) / u_shadow);\n    if(u_percent > 1.0) {\n      float percent = min(1.0, u_percent - 1.0);\n      vec2 pos3 = rotatePoint(vec2(abs(pos1.x), 0), PI * (0.25 * percent + 0.25));\n      vec2 posMix = mix(pos2, pos3, percent);\n      if(posMix.y > pos2.y) {\n        pos2 = posMix;\n      }\n    }\n    float z = -pos2.y * sign(pos1.x);\n    pos1.x = pos2.x;\n    pos.xy = inverseTransformCoordinate(p0, p1, pos1);\n    pos.z += z;\n    mat2 rotate90 = mat2(0.0, -1.0, 1.0, 0.0);\n    vec2 forward = normalize(rotate90 * (p0 - p1));\n    vec4 origin = vec4((p0 + p1) * 0.5, 0.0, 1.0);\n    pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, vec3(origin.xy + forward, 0.0), u_rotation);\n    if(u_rotation != 0.0) {\n      shadowRatio = 1.0;\n    }\n    pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, origin.xyz + vec3(0.0, 0.0, 1.0), u_angle);\n  }\n  pos = u_matViewProj * cc_matWorld * pos;\n  v_uv0 = a_uv0;\n  v_uv1 = a_uv1;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\nvarying vec2 v_uv0;\nvarying float shadowRatio;\nuniform sampler2D tex_front;\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  vec4 tex_front_tmp = texture2D(tex_front, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_tex_front\n      tex_front_tmp.a *= texture2D(tex_front, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (tex_front_tmp.rgb * tex_front_tmp.rgb);\n    o.a *= tex_front_tmp.a;\n  #else\n    o *= tex_front_tmp;\n  #endif\n  if(o.a < .1) {\n    discard;\n  }\n  o = mix(o, vec4(vec3(.25), 1.0), 1.0 - shadowRatio);\n  gl_FragColor = o.rgba;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nuniform properties{\n  mat4 u_matViewProj;\n  vec4 u_line;\n  float u_rotation;\n  float u_radius;\n  float u_percent;\n  float u_angle;\n  float u_shadow;\n};\nconst float PI = 3.141592653589793;\nin vec2 a_position;\nin vec2 a_uv0;\nin vec2 a_uv1;\nout vec2 v_uv0;\nout vec2 v_uv1;\nout float shadowRatio;\nvec3 rotateAroundAxis(vec3 P, vec3 p1, vec3 p2, float rad) {\n    vec3 axis = normalize(p2 - p1);\n    float cosA = cos(rad);\n    float sinA = sin(rad);\n    float oneMinusCos = 1.0 - cosA;\n    mat3 K = mat3(\n        0, -axis.z, axis.y,\n        axis.z, 0, -axis.x,\n        -axis.y, axis.x, 0\n    );\n    mat3 R = mat3(1.0) + sinA * K + oneMinusCos * (K * K);\n    return R * (P - p1) + p1;\n}\nvec2 calculatePointFromArcLength(float r, float arcLength)\n{\n    float quarterCircumference = 3.14159 * r / 2.0;\n    if (arcLength <= quarterCircumference)\n    {\n        float theta = arcLength / r;\n        float x = r - r * cos(theta);\n        float y = r * sin(theta);\n        return vec2(x, y);\n    }\n    else\n    {\n        float remainingArcLength = arcLength - quarterCircumference;\n        float theta = max(0.0, u_percent - 1.0) * .5 * PI;\n        float x2 = r + remainingArcLength;\n        float y2 = r + remainingArcLength * tan(theta);\n        return vec2(x2, y2);\n    }\n}\nvec2 rotatePoint(vec2 v, float theta) {\n    float cosTheta = cos(theta);\n    float sinTheta = sin(theta);\n    float xNew = v.x * cosTheta - v.y * sinTheta;\n    float yNew = v.x * sinTheta + v.y * cosTheta;\n    return vec2(xNew, yNew);\n}\nvec2 transformCoordinate(vec2 p0, vec2 p1, vec2 originalCoordinate) {\n    vec2 direction = normalize(p1 - p0);\n    float angle = atan(direction.y, direction.x);\n    vec2 midpoint = (p0 + p1) / 2.0;\n    mat2 rotationMatrix = mat2(\n        cos(angle), -sin(angle),\n        sin(angle), cos(angle)\n    );\n    vec2 translatedCoordinate = originalCoordinate - midpoint;\n    return rotationMatrix * translatedCoordinate;\n}\nvec2 inverseTransformCoordinate(vec2 p0, vec2 p1, vec2 transformedCoordinate) {\n    vec2 direction = normalize(p1 - p0);\n    float angle = atan(direction.y, direction.x);\n    vec2 midpoint = (p0 + p1) / 2.0;\n    mat2 inverseRotationMatrix = mat2(\n        cos(-angle), -sin(-angle),\n        sin(-angle), cos(-angle)\n    );\n    vec2 rotatedCoordinate = inverseRotationMatrix * transformedCoordinate;\n    return rotatedCoordinate + midpoint;\n}\nvoid main () {\n  vec4 pos = vec4(a_position, 0.0, 1);\n  if(u_line.xy != u_line.zw) {\n    vec2 p0 = u_line.xy;\n    vec2 p1 = u_line.zw;\n    vec2 pos1 = transformCoordinate(p0, p1, pos.xy);\n    vec2 pos2 = calculatePointFromArcLength(u_radius, abs(pos1.x));\n    shadowRatio = min(1.0, abs(pos1.x) / u_shadow);\n    if(u_percent > 1.0) {\n      float percent = min(1.0, u_percent - 1.0);\n      vec2 pos3 = rotatePoint(vec2(abs(pos1.x), 0), PI * (0.25 * percent + 0.25));\n      vec2 posMix = mix(pos2, pos3, percent);\n      if(posMix.y > pos2.y) {\n        pos2 = posMix;\n      }\n    }\n    float z = -pos2.y * sign(pos1.x);\n    pos1.x = pos2.x;\n    pos.xy = inverseTransformCoordinate(p0, p1, pos1);\n    pos.z += z;\n    mat2 rotate90 = mat2(0.0, -1.0, 1.0, 0.0);\n    vec2 forward = normalize(rotate90 * (p0 - p1));\n    vec4 origin = vec4((p0 + p1) * 0.5, 0.0, 1.0);\n    pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, vec3(origin.xy + forward, 0.0), u_rotation);\n    if(u_rotation != 0.0) {\n      shadowRatio = 1.0;\n    }\n    pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, origin.xyz + vec3(0.0, 0.0, 1.0), u_angle);\n  }\n  pos = u_matViewProj * cc_matWorld * pos;\n  v_uv0 = a_uv0;\n  v_uv1 = a_uv1;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\nin vec2 v_uv0;\nin float shadowRatio;\nuniform sampler2D tex_front;\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  vec4 tex_front_tmp = texture(tex_front, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_tex_front\n      tex_front_tmp.a *= texture(tex_front, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (tex_front_tmp.rgb * tex_front_tmp.rgb);\n    o.a *= tex_front_tmp.a;\n  #else\n    o *= tex_front_tmp;\n  #endif\n  if(o.a < .1) {\n    discard;\n  }\n  o = mix(o, vec4(vec3(.25), 1.0), 1.0 - shadowRatio);\n  gl_FragColor = o.rgba;\n}"}}, {"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matWorld;\nuniform mat4 u_matViewProj;\nuniform vec4 u_line;\nuniform float u_rotation;\nuniform float u_radius;\nuniform float u_percent;\nuniform float u_angle;\nuniform float u_shadow;\nconst float PI = 3.141592653589793;\nattribute vec2 a_position;\nattribute vec2 a_uv0;\nattribute vec2 a_uv1;\nvarying vec2 v_uv0;\nvarying vec2 v_uv1;\nvarying float shadowRatio;\nvec3 rotateAroundAxis(vec3 P, vec3 p1, vec3 p2, float rad) {\n    vec3 axis = normalize(p2 - p1);\n    float cosA = cos(rad);\n    float sinA = sin(rad);\n    float oneMinusCos = 1.0 - cosA;\n    mat3 K = mat3(\n        0, -axis.z, axis.y,\n        axis.z, 0, -axis.x,\n        -axis.y, axis.x, 0\n    );\n    mat3 R = mat3(1.0) + sinA * K + oneMinusCos * (K * K);\n    return R * (P - p1) + p1;\n}\nvec2 calculatePointFromArcLength(float r, float arcLength)\n{\n    float quarterCircumference = 3.14159 * r / 2.0;\n    if (arcLength <= quarterCircumference)\n    {\n        float theta = arcLength / r;\n        float x = r - r * cos(theta);\n        float y = r * sin(theta);\n        return vec2(x, y);\n    }\n    else\n    {\n        float remainingArcLength = arcLength - quarterCircumference;\n        float theta = max(0.0, u_percent - 1.0) * .5 * PI;\n        float x2 = r + remainingArcLength;\n        float y2 = r + remainingArcLength * tan(theta);\n        return vec2(x2, y2);\n    }\n}\nvec2 rotatePoint(vec2 v, float theta) {\n    float cosTheta = cos(theta);\n    float sinTheta = sin(theta);\n    float xNew = v.x * cosTheta - v.y * sinTheta;\n    float yNew = v.x * sinTheta + v.y * cosTheta;\n    return vec2(xNew, yNew);\n}\nvec2 transformCoordinate(vec2 p0, vec2 p1, vec2 originalCoordinate) {\n    vec2 direction = normalize(p1 - p0);\n    float angle = atan(direction.y, direction.x);\n    vec2 midpoint = (p0 + p1) / 2.0;\n    mat2 rotationMatrix = mat2(\n        cos(angle), -sin(angle),\n        sin(angle), cos(angle)\n    );\n    vec2 translatedCoordinate = originalCoordinate - midpoint;\n    return rotationMatrix * translatedCoordinate;\n}\nvec2 inverseTransformCoordinate(vec2 p0, vec2 p1, vec2 transformedCoordinate) {\n    vec2 direction = normalize(p1 - p0);\n    float angle = atan(direction.y, direction.x);\n    vec2 midpoint = (p0 + p1) / 2.0;\n    mat2 inverseRotationMatrix = mat2(\n        cos(-angle), -sin(-angle),\n        sin(-angle), cos(-angle)\n    );\n    vec2 rotatedCoordinate = inverseRotationMatrix * transformedCoordinate;\n    return rotatedCoordinate + midpoint;\n}\nvoid main () {\n  vec4 pos = vec4(a_position, 0.0, 1);\n  if(u_line.xy != u_line.zw) {\n    vec2 p0 = u_line.xy;\n    vec2 p1 = u_line.zw;\n    vec2 pos1 = transformCoordinate(p0, p1, pos.xy);\n    vec2 pos2 = calculatePointFromArcLength(u_radius, abs(pos1.x));\n    shadowRatio = min(1.0, abs(pos1.x) / u_shadow);\n    if(u_percent > 1.0) {\n      float percent = min(1.0, u_percent - 1.0);\n      vec2 pos3 = rotatePoint(vec2(abs(pos1.x), 0), PI * (0.25 * percent + 0.25));\n      vec2 posMix = mix(pos2, pos3, percent);\n      if(posMix.y > pos2.y) {\n        pos2 = posMix;\n      }\n    }\n    float z = -pos2.y * sign(pos1.x);\n    pos1.x = pos2.x;\n    pos.xy = inverseTransformCoordinate(p0, p1, pos1);\n    pos.z += z;\n    mat2 rotate90 = mat2(0.0, -1.0, 1.0, 0.0);\n    vec2 forward = normalize(rotate90 * (p0 - p1));\n    vec4 origin = vec4((p0 + p1) * 0.5, 0.0, 1.0);\n    pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, vec3(origin.xy + forward, 0.0), u_rotation);\n    if(u_rotation != 0.0) {\n      shadowRatio = 1.0;\n    }\n    pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, origin.xyz + vec3(0.0, 0.0, 1.0), u_angle);\n  }\n  pos = u_matViewProj * cc_matWorld * pos;\n  v_uv0 = a_uv0;\n  v_uv1 = a_uv1;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\nvarying vec2 v_uv1;\nuniform sampler2D tex_back;\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  vec4 tex_back_tmp = texture2D(tex_back, v_uv1);\n  #if CC_USE_ALPHA_ATLAS_tex_back\n      tex_back_tmp.a *= texture2D(tex_back, v_uv1 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (tex_back_tmp.rgb * tex_back_tmp.rgb);\n    o.a *= tex_back_tmp.a;\n  #else\n    o *= tex_back_tmp;\n  #endif\n  if(o.a < .1) {\n    discard;\n  }\n  gl_FragColor = o.rgba;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nuniform properties{\n  mat4 u_matViewProj;\n  vec4 u_line;\n  float u_rotation;\n  float u_radius;\n  float u_percent;\n  float u_angle;\n  float u_shadow;\n};\nconst float PI = 3.141592653589793;\nin vec2 a_position;\nin vec2 a_uv0;\nin vec2 a_uv1;\nout vec2 v_uv0;\nout vec2 v_uv1;\nout float shadowRatio;\nvec3 rotateAroundAxis(vec3 P, vec3 p1, vec3 p2, float rad) {\n    vec3 axis = normalize(p2 - p1);\n    float cosA = cos(rad);\n    float sinA = sin(rad);\n    float oneMinusCos = 1.0 - cosA;\n    mat3 K = mat3(\n        0, -axis.z, axis.y,\n        axis.z, 0, -axis.x,\n        -axis.y, axis.x, 0\n    );\n    mat3 R = mat3(1.0) + sinA * K + oneMinusCos * (K * K);\n    return R * (P - p1) + p1;\n}\nvec2 calculatePointFromArcLength(float r, float arcLength)\n{\n    float quarterCircumference = 3.14159 * r / 2.0;\n    if (arcLength <= quarterCircumference)\n    {\n        float theta = arcLength / r;\n        float x = r - r * cos(theta);\n        float y = r * sin(theta);\n        return vec2(x, y);\n    }\n    else\n    {\n        float remainingArcLength = arcLength - quarterCircumference;\n        float theta = max(0.0, u_percent - 1.0) * .5 * PI;\n        float x2 = r + remainingArcLength;\n        float y2 = r + remainingArcLength * tan(theta);\n        return vec2(x2, y2);\n    }\n}\nvec2 rotatePoint(vec2 v, float theta) {\n    float cosTheta = cos(theta);\n    float sinTheta = sin(theta);\n    float xNew = v.x * cosTheta - v.y * sinTheta;\n    float yNew = v.x * sinTheta + v.y * cosTheta;\n    return vec2(xNew, yNew);\n}\nvec2 transformCoordinate(vec2 p0, vec2 p1, vec2 originalCoordinate) {\n    vec2 direction = normalize(p1 - p0);\n    float angle = atan(direction.y, direction.x);\n    vec2 midpoint = (p0 + p1) / 2.0;\n    mat2 rotationMatrix = mat2(\n        cos(angle), -sin(angle),\n        sin(angle), cos(angle)\n    );\n    vec2 translatedCoordinate = originalCoordinate - midpoint;\n    return rotationMatrix * translatedCoordinate;\n}\nvec2 inverseTransformCoordinate(vec2 p0, vec2 p1, vec2 transformedCoordinate) {\n    vec2 direction = normalize(p1 - p0);\n    float angle = atan(direction.y, direction.x);\n    vec2 midpoint = (p0 + p1) / 2.0;\n    mat2 inverseRotationMatrix = mat2(\n        cos(-angle), -sin(-angle),\n        sin(-angle), cos(-angle)\n    );\n    vec2 rotatedCoordinate = inverseRotationMatrix * transformedCoordinate;\n    return rotatedCoordinate + midpoint;\n}\nvoid main () {\n  vec4 pos = vec4(a_position, 0.0, 1);\n  if(u_line.xy != u_line.zw) {\n    vec2 p0 = u_line.xy;\n    vec2 p1 = u_line.zw;\n    vec2 pos1 = transformCoordinate(p0, p1, pos.xy);\n    vec2 pos2 = calculatePointFromArcLength(u_radius, abs(pos1.x));\n    shadowRatio = min(1.0, abs(pos1.x) / u_shadow);\n    if(u_percent > 1.0) {\n      float percent = min(1.0, u_percent - 1.0);\n      vec2 pos3 = rotatePoint(vec2(abs(pos1.x), 0), PI * (0.25 * percent + 0.25));\n      vec2 posMix = mix(pos2, pos3, percent);\n      if(posMix.y > pos2.y) {\n        pos2 = posMix;\n      }\n    }\n    float z = -pos2.y * sign(pos1.x);\n    pos1.x = pos2.x;\n    pos.xy = inverseTransformCoordinate(p0, p1, pos1);\n    pos.z += z;\n    mat2 rotate90 = mat2(0.0, -1.0, 1.0, 0.0);\n    vec2 forward = normalize(rotate90 * (p0 - p1));\n    vec4 origin = vec4((p0 + p1) * 0.5, 0.0, 1.0);\n    pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, vec3(origin.xy + forward, 0.0), u_rotation);\n    if(u_rotation != 0.0) {\n      shadowRatio = 1.0;\n    }\n    pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, origin.xyz + vec3(0.0, 0.0, 1.0), u_angle);\n  }\n  pos = u_matViewProj * cc_matWorld * pos;\n  v_uv0 = a_uv0;\n  v_uv1 = a_uv1;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\nin vec2 v_uv1;\nuniform sampler2D tex_back;\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  vec4 tex_back_tmp = texture(tex_back, v_uv1);\n  #if CC_USE_ALPHA_ATLAS_tex_back\n      tex_back_tmp.a *= texture(tex_back, v_uv1 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (tex_back_tmp.rgb * tex_back_tmp.rgb);\n    o.a *= tex_back_tmp.a;\n  #else\n    o *= tex_back_tmp;\n  #endif\n  if(o.a < .1) {\n    discard;\n  }\n  gl_FragColor = o.rgba;\n}"}}], "subMetas": {}}