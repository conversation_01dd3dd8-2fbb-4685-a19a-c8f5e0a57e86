const { ccclass, property } = cc._decorator;

enum State {
    None,
    Peek,
    PeekBack,
    PeekAuto,
    OpenAnimate,
    Open
}

@ccclass
export default class PeekCardMesh extends cc.Component {

    @property(cc.Size)
    size: cc.Size = new cc.Size(560, 824);

    @property(cc.Material)
    material: cc.Material = null;

    private _state: State = State.None;

    private _p0: cc.Vec2;
    private _p1: cc.Vec2;
    private _dir: cc.Vec2;
    private _duration = 0;

    private _mat: cc.MaterialVariant = null;
    private _shadowWidth: number = 0;

    private _tex_front: cc.SpriteFrame;
    private _tex_back: cc.SpriteFrame;

    private _radius = 0;
    private _radiusTarget = 0;
    private _aniStep = 0;

    private _time = 0;

    onLoad() {
        let meshRender = this.getComponent(cc.MeshRenderer);
        if (!meshRender) {
            meshRender = this.addComponent(cc.MeshRenderer);
        }
        meshRender.setMaterial(0, this.material);
        this._mat = meshRender.getMaterial(0);
    }

    //! please don't add tex to dynamic atlas, because uv will change
    //u can uncheck packable to avoid add to dynamic atlas
    public setCardFace(tex: cc.SpriteFrame) {
        this._mat?.setProperty('tex_front', tex.getTexture(), 0);
        this._tex_front = tex;
    }

    //! please don't add tex to dynamic atlas, because uv will change
    //u can uncheck packable to avoid add to dynamic atlas
    public setCardBack(tex: cc.SpriteFrame) {
        this._mat?.setProperty('tex_back', tex.getTexture(), 1);
        this._tex_back = tex;
    }

    public setSize(width: number, height: number) {
        this.size.width = width;
        this.size.height = height;
    }

    public reset() {
        this._state = State.None;
        this._p0 = null;
        this._p1 = null;
        this._time = 0;

        this._applayLine(cc.Vec2.ZERO, cc.Vec2.ZERO);
        this._applyRadius(0);
        this._applyAngle(0);
        this._applyPercent(0);
        this._rotateFace(0);
        this._mat.setProperty('u_shadow', this._shadowWidth);
    }

    public setShadowWidth(width: number) {
        this._shadowWidth = width;
        this._mat?.setProperty('u_shadow', width);
    }

    private _isOrtho = false;
    public setOrtho(isOrtho: boolean) {
        this._isOrtho = isOrtho;
    }

    /**
     * if you want to open the card manually, set it to true
     * otherwise, set it to false
     * @param on default false
     */
    public touchOn(on: boolean) {
        this.node.targetOff(this);
        if (on) {
            this.node.on(cc.Node.EventType.TOUCH_MOVE, this._onTouchMove, this);
            this.node.on(cc.Node.EventType.TOUCH_END, this._onTouchEnd, this);
            this.node.on(cc.Node.EventType.TOUCH_CANCEL, this._onTouchEnd, this);
        }
    }

    /**
     * @param segment: number
     * how many segments need to be divided, the larger the smoother the edge but the more performance it consumes
     * when u changed size, face or back spriteframe, u need to call this function
     */
    public updateMesh(segment: number) {
        const mesh = new cc.Mesh();
        let gfx: any = cc['gfx'];
        let vfmt: any = new gfx.VertexFormat([
            { name: gfx.ATTR_POSITION, type: gfx.ATTR_TYPE_FLOAT32, num: 2 },
            { name: gfx.ATTR_UV0, type: gfx.ATTR_TYPE_FLOAT32, num: 2 },
            { name: gfx.ATTR_UV1, type: gfx.ATTR_TYPE_FLOAT32, num: 2 }
        ]);
        mesh.init(vfmt, segment * 2 + 2);

        const rect = this._getRect();

        let backRect = new cc.Rect(0, 0, 1, 1);
        const _back = this._tex_back;
        if (_back) {
            //@ts-ignore
            let backUV: number[] = _back.uv;
            backRect.xMin = backRect.xMax = backUV[0];
            backRect.yMin = backRect.yMax = backUV[1];
            for (let i = 0; i < backUV.length; i += 2) {
                let u = backUV[i];
                let v = backUV[i + 1];
                backRect.xMin = Math.min(backRect.xMin, u);
                backRect.xMax = Math.max(backRect.xMax, u);
                backRect.yMin = Math.min(backRect.yMin, v);
                backRect.yMax = Math.max(backRect.yMax, v);
            }
            this._mat?.setProperty('tex_back', _back.getTexture(), 1);
        }

        const faceRect = new cc.Rect(0, 0, 1, 1);
        const _tex_front = this._tex_front;
        if (_tex_front) {
            //@ts-ignore
            let faceUV: number[] = _tex_front.uv;
            faceRect.xMin = faceRect.xMax = faceUV[0];
            faceRect.yMin = faceRect.yMax = faceUV[1];
            for (let i = 0; i < faceUV.length; i += 2) {
                let u = faceUV[i];
                let v = faceUV[i + 1];
                faceRect.xMin = Math.min(faceRect.xMin, u);
                faceRect.xMax = Math.max(faceRect.xMax, u);
                faceRect.yMin = Math.min(faceRect.yMin, v);
                faceRect.yMax = Math.max(faceRect.yMax, v);
            }
            this._mat?.setProperty('tex_front', _tex_front.getTexture(), 0);
        }

        const verts = [
            new Vertex(rect.xMin, rect.yMax, faceRect.xMax, faceRect.yMax, backRect.xMin, backRect.yMin),
            new Vertex(rect.xMax, rect.yMax, faceRect.xMin, faceRect.yMax, backRect.xMax, backRect.yMin),
            new Vertex(rect.xMin, rect.yMin, faceRect.xMax, faceRect.yMin, backRect.xMin, backRect.yMax),
            new Vertex(rect.xMax, rect.yMin, faceRect.xMin, faceRect.yMin, backRect.xMax, backRect.yMax),
        ];

        const { vertices, indices } = this._generateVerticesAndIndices(verts[2], verts[0], verts[1], verts[3], segment);

        const positions = [];
        const uv0 = [];
        const uv1 = [];
        for (let vertex of vertices) {
            positions.push(vertex.x, vertex.y);
            uv0.push(vertex.u, vertex.v);
            uv1.push(vertex.u1, vertex.v1);
        }

        mesh.setVertices(gfx.ATTR_POSITION, positions);
        mesh.setVertices(gfx.ATTR_UV0, uv0);
        mesh.setVertices(gfx.ATTR_UV1, uv1);
        mesh.setIndices(indices);

        if(!this._isOrtho) {
            const viewProjMatrix = this._createViewProjMatrix(new cc.Vec3(rect.center.x, rect.center.y, 0));
            this._mat.setProperty('u_matViewProj', viewProjMatrix);
        }else {
            const viewProjMatrix = this._createOrthographicViewProjMatrix(new cc.Vec3(rect.center.x, rect.center.y, 0));
            this._mat.setProperty('u_matViewProj', viewProjMatrix);
        }

        this.getComponent(cc.MeshRenderer).mesh = mesh;
    }

    protected _applayLine(p0: cc.Vec2, p1: cc.Vec2) {
        this._mat.setProperty('u_line', new cc.Vec4(p0.x, p0.y, p1.x, p1.y));
    }

    protected _applyRadius(radius: number) {
        this._mat.setProperty('u_radius', radius);
    }

    protected _applyPercent(percent: number) {
        this._mat.setProperty('u_percent', percent);
    }

    protected _applyAngle(angle: number) {
        this._mat.setProperty('u_angle', angle);
    }

    private _getRect(offset = 0) {
        let { width, height } = this.size;
        width += offset;
        height += offset;
        const position = this.node.convertToWorldSpaceAR(cc.Vec2.ZERO);
        let x = position.x - width * .5;
        let y = position.y - height * .5;
        return cc.rect(x, y, width, height);
    }

    private _checkP0(coordinate: cc.Vec2) {
        const edge = 130;
        const rect = this._getRect();

        if (!rect.contains(coordinate)) {
            return;
        }

        const p0 = coordinate.clone();
        this._p0 = p0;

        if (coordinate.x < rect.xMin + edge) {
            p0.x = rect.xMin;
        } else if (coordinate.x > rect.xMax - edge) {
            p0.x = rect.xMax;
        }
        if (coordinate.y < rect.yMin + edge) {
            p0.y = rect.yMin;
        } else if (coordinate.y > rect.yMax - edge) {
            p0.y = rect.yMax;
        }
    }

    private _isPointInRect(p1: cc.Vec2) {
        const rect = this._getRect();
        return rect.contains(p1);
    }

    private _onTouchMove(event: cc.Event.EventTouch) {
        if (this._state !== State.None && this._state !== State.Peek) return;
        const location = event.getLocation();
        const camera = cc.Camera.main;
        const coordinate = camera.getScreenToWorldPoint(location);
        if (!this._p0) {
            this._checkP0(cc.v2(coordinate.x, coordinate.y));
        } else {
            const rect = this._getRect();
            const p1 = cc.v2(coordinate.x, coordinate.y);
            const p0p1 = p1.sub(this._p0);
            const p0Center = rect.center.sub(this._p0);
            const signAngle = p0p1.signAngle(p0Center);

            if (this._p0.x === rect.xMax || this._p0.x === rect.xMin) {
                if (p0p1.x * p0Center.x <= 0) {
                    p1.x = this._p0.x;
                    this._p1 = p1;
                    return;
                }
            } else if (this._p0.y === rect.yMax || this._p0.y === rect.yMin) {
                if (p0p1.y * p0Center.y <= 0) {
                    p1.y = this._p0.y;
                    this._p1 = p1;
                    return;
                }
            }

            if (Math.abs(signAngle) > Math.PI * .5) {
                if (Math.abs(signAngle) > Math.PI * .7) {
                    return;
                }
                this._p1 = p1;
                this._triggerOpen();
                return;
            }

            if (!this._isPointInRect(p1)) {

                if (p1.sub(this._p0).lengthSqr() > Math.pow((this.size.width * .5), 2)) {
                    this._p1 = p1;
                    this._triggerOpen();
                    return;
                }
            }
            if (this._state !== State.Peek) {
                this._state = State.Peek;
                this._time = 0;
                this.node.emit('peek-start');
            }
            this._p1 = p1;
        }
    }

    private _onTouchEnd() {
        if (this._state !== State.None && this._state !== State.Peek) return;
        this._state = State.PeekBack;
        this._time = 0;
    }

    private _triggerOpen() {
        this.node.emit('trigger');
        this._autoOpen();
    }

    private _autoOpen() {
        const { _p0, _p1 } = this;
        const p0p1 = _p1.sub(_p0);
        this._dir = p0p1.normalize();

        let angle = this._dir.angle(cc.Vec2.RIGHT);
        let scale = (((this.size.height / this.size.width) - 1) * Math.sin(angle) + 1);
        this._radiusTarget = scale * this.size.width * .2;

        this._state = State.OpenAnimate;
        this._time = 0;
        this._aniStep = 0;
        this.node.emit('start-animate');
    }

    public get isClose() {
        return this._state === State.None;
    }

    update(dt: number) {
        this._time += dt;
        if (this._state === State.PeekBack) {
            let ratio = Math.min(1, this._time / .1);
            if (!this._p0 || !this._p1 || ratio === 1) {
                this.reset();
                return;
            }
            let p1 = this._p1.lerp(this._p0, ratio);
            this._applayLine(this._p0, p1);
            let radius = cc.misc.lerp(10, 0, ratio);
            this._applyRadius(radius);
        } else if (this._state === State.Peek) {
            let ratio = Math.min(1, this._time / .1);
            let p1 = this._p0.lerp(this._p1, ratio);
            this._applayLine(this._p0, p1);
            let radius = cc.misc.lerp(0, 10, ratio);
            this._applyRadius(radius);

        } else if (this._state === State.PeekAuto) {
            const duration = this._duration;
            const ratio = Math.min(1, this._time / duration);
            const p = this._p0.lerp(this._p1, ratio);
            this._applayLine(this._p0, p);
            if (ratio === 1) {
                this._autoOpen();
            }
        } else if (this._state === State.OpenAnimate) {
            const duration = .3;
            const d1 = .2;
            const d2 = duration - d1;

            if (this._aniStep === 1) {
                let ratio = Math.min(1, (this._time - d1) / (d2));
                let radius = cc.misc.lerp(this._radius, this._radiusTarget, ratio);
                this._applyRadius(radius);
                this._applyPercent(1.25 + ratio * .75);
                if (ratio === 1) {
                    this._state = State.Open;
                    this._time = 0;
                }
                return;
            }

            let ratio = Math.min(1, this._time / d1);
            const asix = cc.v2(Math.sign(this._p1.x - this._p0.x), 0);

            ratio = cc.easeSineIn().easing(ratio);

            if (Math.abs(this._dir.x) < Math.abs(this._dir.y)) {
                asix.x = 0;
                asix.y = Math.sign(this._p1.y - this._p0.y);
            }

            const { _p0, _p1 } = this;

            const o = this.node.convertToWorldSpaceAR(cc.Vec2.ZERO);
            const center = this._p0.add(this._p1).mul(.5);

            const offset = center.lerp(o, ratio).sub(center);

            const p0 = _p0.add(offset);
            const p1 = _p1.add(offset);
            this._applayLine(p0, p1);

            let angle0 = cc.misc.lerp(0, this._dir.signAngle(asix), ratio);
            this._applyAngle(-angle0 * 2);

            let radius = cc.misc.lerp(10, this._radiusTarget, ratio);
            this._radius = radius;
            this._applyRadius(radius);

            const rotation = cc.misc.lerp(0, 90, Math.min(1, ratio * 2));
            this._rotateFace(rotation);

            this._applyPercent(ratio * .25 + 1);

            if (ratio === 1 && this._aniStep === 0) {
                this._aniStep = 1;
                this.node.emit('will-open');
                let angle = this._dir.angle(cc.Vec2.RIGHT);
                let scale = (((this.size.height / this.size.width) - 1) * Math.sin(angle) + 1);
                this._radiusTarget = scale * this.size.width;
            }
        }
    }

    private _rotateFace(rotation: number) {
        let radian = -rotation * Math.PI / 180;
        this._mat.setProperty('u_rotation', radian);
    }

    public open(duration = 0.8, direction: 'left' | 'right' | 'up' | 'down' = 'right') {
        if (this._state === State.OpenAnimate || this._state === State.Open) {
            return;
        }
        if (this._state === State.PeekAuto) {
            let ratio = Math.min(1, this._time / this._duration);
            this._time = (ratio * duration) / (1 - ratio);
            this._duration = this._time + duration;
            return;
        }
        const rect = this._getRect();
        if (!this._p0 || !this._p1) {
            const p0 = cc.v2(rect.center.x, rect.center.y);
            const p1 = cc.v2(rect.center.x, rect.center.y);
            if (direction === 'left') {
                p0.x = rect.xMax;
                p1.x = rect.xMin;
            } else if (direction === 'right') {
                p0.x = rect.xMin;
                p1.x = rect.xMax;
            } else if (direction === 'up') {
                p0.y = rect.yMin;
                p1.y = rect.yMax;
            } else {
                p0.y = rect.yMax;
                p1.y = rect.yMin;
            }
            this._p0 = p0;
            this._p1 = p1;

            this._state = State.PeekAuto;
            this._time = 0;
            this._duration = duration;
            return;
        }
        this._autoOpen();
    }

    private _generateVerticesAndIndices(
        v0: Vertex,
        v1: Vertex,
        v2: Vertex,
        v3: Vertex,
        n: number
    ) {
        const vertices: Vertex[] = [];
        const indices: number[] = [];

        const grid: Vertex[][] = [];

        for (let i = 0; i <= n; i++) {
            const t = i / n;
            const row: Vertex[] = [];

            for (let j = 0; j <= n; j++) {
                const s = j / n;
                const interpolatedBottom = v0.lerp(v1, s);
                const interpolatedTop = v3.lerp(v2, s);
                const vertex = interpolatedBottom.lerp(interpolatedTop, t);
                row.push(vertex);
            }
            grid.push(row);
        }

        grid.forEach(row => {
            vertices.push(...row);
        });

        for (let i = 0; i < n; i++) {
            for (let j = 0; j < n; j++) {
                const topLeft = i + j * (n + 1);
                const topRight = (i + 1) + j * (n + 1);
                const bottomLeft = i + (j + 1) * (n + 1);
                const bottomRight = (i + 1) + (j + 1) * (n + 1);

                indices.push(topLeft, bottomLeft, topRight);
                indices.push(topRight, bottomLeft, bottomRight);
            }
        }

        return { vertices, indices };
    }

    private _createViewProjMatrix(targetPos: cc.Vec3): cc.Mat4 {
        let near = 0.1;
        let far = 4086;
        let aspect = cc.winSize.width / cc.winSize.height;
        let fov = Math.PI / 3;
    
        let orthoHeight = cc.winSize.height;
        let distance = orthoHeight / (2 * Math.tan(fov / 2));
    
        let worldPos = targetPos;
        let eye = new cc.Vec3(worldPos.x, worldPos.y, worldPos.z + distance);
        let up = new cc.Vec3(0, 1, 0);
    
        let projMatrix = cc.Mat4.perspective(new cc.Mat4(), fov, aspect, near, far);
        let viewMatrix = cc.Mat4.lookAt(new cc.Mat4(), eye, worldPos, up);
    
        let offsetX = -cc.winSize.width * .5; 
        let offsetY = -cc.winSize.height * .5; 
    
        let offset = new cc.Mat4();
        cc.Mat4.translate(offset, cc.Mat4.IDENTITY, new cc.Vec3(offsetX, offsetY, 0));
    
        cc.Mat4.multiply(viewMatrix, offset, viewMatrix);
    
        let viewProjMatrix = new cc.Mat4();
        cc.Mat4.multiply(viewProjMatrix, projMatrix, viewMatrix);
    
        return viewProjMatrix;
    }
    
    private _createOrthographicViewProjMatrix(targetPos: cc.Vec3): cc.Mat4 {
        let near = 0.1;  
        let far = 4086;  
    
        let worldPos = targetPos;
        let eye = new cc.Vec3(worldPos.x, worldPos.y, worldPos.z + 1000);  
        let up = new cc.Vec3(0, 1, 0);
    
        let orthoHeight = cc.winSize.height;
        let orthoWidth = cc.winSize.width;
    
        let projMatrix = cc.Mat4.ortho(new cc.Mat4(), -orthoWidth * 0.5, orthoWidth * 0.5, -orthoHeight * 0.5, orthoHeight * 0.5, near, far);
    
        let viewMatrix = cc.Mat4.lookAt(new cc.Mat4(), eye, worldPos, up);
    
        let offsetX = -cc.winSize.width * 0.5;
        let offsetY = -cc.winSize.height * 0.5;
    
        let offset = new cc.Mat4();
        cc.Mat4.translate(offset, cc.Mat4.IDENTITY, new cc.Vec3(offsetX, offsetY, 0));
    
        cc.Mat4.multiply(viewMatrix, offset, viewMatrix);
    
        let viewProjMatrix = new cc.Mat4();
        cc.Mat4.multiply(viewProjMatrix, projMatrix, viewMatrix);
    
        return viewProjMatrix;
    }
    

}

class Vertex {
    x: number;
    y: number;

    u: number;
    v: number;

    u1: number;
    v1: number;

    constructor(x: number, y: number, u: number, v: number, u1: number, v1: number) {
        this.x = x;
        this.y = y;
        this.u = u;
        this.v = v;
        this.u1 = u1;
        this.v1 = v1;
    }

    public lerp(target: Vertex, ratio: number) {
        let x = this.x + (target.x - this.x) * ratio;
        let y = this.y + (target.y - this.y) * ratio;
        let u = this.u + (target.u - this.u) * ratio;
        let v = this.v + (target.v - this.v) * ratio;
        let u1 = this.u1 + (target.u1 - this.u1) * ratio;
        let v1 = this.v1 + (target.v1 - this.v1) * ratio;
        return new Vertex(x, y, u, v, u1, v1);
    }
}

