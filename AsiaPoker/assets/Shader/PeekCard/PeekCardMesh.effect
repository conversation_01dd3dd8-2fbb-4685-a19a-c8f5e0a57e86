// Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      rasterizerState:
        cullMode: front
      depthStencilState:
        depthWrite: true
        depthTest: true
      blendState:
        targets:
        - blend: true
      properties:
        tex_front: { value: white }
    - vert: vs
      frag: fs-back
      rasterizerState:
        cullMode: back
      blendState:
        targets:
        - blend: true
      depthStencilState:
        depthWrite: true
        depthTest: true
      properties:
        tex_back: { value: white }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>

  uniform properties{
    mat4 u_matViewProj;
    vec4 u_line;
    float u_rotation; // radian
    float u_radius;
    float u_percent;
    float u_angle; // radian
    float u_shadow;
  };

  const float PI = 3.141592653589793;

  in vec2 a_position;
  in vec2 a_uv0;
  in vec2 a_uv1;

  out vec2 v_uv0;
  out vec2 v_uv1;
  out float shadowRatio;


  vec3 rotateAroundAxis(vec3 P, vec3 p1, vec3 p2, float rad) {
      vec3 axis = normalize(p2 - p1); 
      
      float cosA = cos(rad);
      float sinA = sin(rad);
      float oneMinusCos = 1.0 - cosA;

      mat3 K = mat3(
          0, -axis.z, axis.y,
          axis.z, 0, -axis.x,
          -axis.y, axis.x, 0
      );

      mat3 R = mat3(1.0) + sinA * K + oneMinusCos * (K * K);

      return R * (P - p1) + p1;
  }

  vec2 calculatePointFromArcLength(float r, float arcLength)
  {
      float quarterCircumference = 3.14159 * r / 2.0;
      if (arcLength <= quarterCircumference)
      {
          float theta = arcLength / r;
          float x = r - r * cos(theta);
          float y = r * sin(theta);
          return vec2(x, y);
      }
      else
      {
          float remainingArcLength = arcLength - quarterCircumference;

          float theta = max(0.0, u_percent - 1.0) * .5 * PI;
          
          float x2 = r + remainingArcLength;
          float y2 = r + remainingArcLength * tan(theta);

          return vec2(x2, y2);
      }
  }

  vec2 rotatePoint(vec2 v, float theta) {
      float cosTheta = cos(theta);
      float sinTheta = sin(theta);
      
      float xNew = v.x * cosTheta - v.y * sinTheta;
      float yNew = v.x * sinTheta + v.y * cosTheta;
      
      return vec2(xNew, yNew);
  }

  vec2 transformCoordinate(vec2 p0, vec2 p1, vec2 originalCoordinate) {
      vec2 direction = normalize(p1 - p0);
      
      float angle = atan(direction.y, direction.x); 
      
      vec2 midpoint = (p0 + p1) / 2.0;
      
      mat2 rotationMatrix = mat2(
          cos(angle), -sin(angle),
          sin(angle), cos(angle)
      );
      
      vec2 translatedCoordinate = originalCoordinate - midpoint;
      return rotationMatrix * translatedCoordinate;
  }

  vec2 inverseTransformCoordinate(vec2 p0, vec2 p1, vec2 transformedCoordinate) {
      vec2 direction = normalize(p1 - p0);
      
      float angle = atan(direction.y, direction.x); 
      
      vec2 midpoint = (p0 + p1) / 2.0;
      
      mat2 inverseRotationMatrix = mat2(
          cos(-angle), -sin(-angle),
          sin(-angle), cos(-angle)
      );
      
      vec2 rotatedCoordinate = inverseRotationMatrix * transformedCoordinate;
      return rotatedCoordinate + midpoint;
  }

  void main () {
    vec4 pos = vec4(a_position, 0.0, 1);

    if(u_line.xy != u_line.zw) {
      vec2 p0 = u_line.xy;
      vec2 p1 = u_line.zw;

      //Transform to p0p1 as x axis
      vec2 pos1 = transformCoordinate(p0, p1, pos.xy);

      //  Bend along p0p1
      vec2 pos2 = calculatePointFromArcLength(u_radius, abs(pos1.x));

      shadowRatio = min(1.0, abs(pos1.x) / u_shadow);

      if(u_percent > 1.0) {
        float percent = min(1.0, u_percent - 1.0);
        // Unfold along p0p1
        vec2 pos3 = rotatePoint(vec2(abs(pos1.x), 0), PI * (0.25 * percent + 0.25));
        vec2 posMix = mix(pos2, pos3, percent);
        if(posMix.y > pos2.y) {
          pos2 = posMix;
        }
      }

      float z = -pos2.y * sign(pos1.x);
      pos1.x = pos2.x;  

      // Transform back to original coordinate
      pos.xy = inverseTransformCoordinate(p0, p1, pos1);
      pos.z += z;

      mat2 rotate90 = mat2(0.0, -1.0, 1.0, 0.0);
      vec2 forward = normalize(rotate90 * (p0 - p1));

      vec4 origin = vec4((p0 + p1) * 0.5, 0.0, 1.0); // cc_matWorld * vec4(0.0, 0.0, 0.0, 1.0);

      pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, vec3(origin.xy + forward, 0.0), u_rotation);

      if(u_rotation != 0.0) {
        shadowRatio = 1.0;
      }

      pos.xyz = rotateAroundAxis(pos.xyz, origin.xyz, origin.xyz + vec3(0.0, 0.0, 1.0), u_angle);
    }

    pos = u_matViewProj * cc_matWorld * pos;

    v_uv0 = a_uv0;
    v_uv1 = a_uv1;

    gl_Position = pos;
  }

}%

CCProgram fs %{
  precision highp float;

  #include <texture>
  in vec2 v_uv0;
  in float shadowRatio;
  uniform sampler2D tex_front;

  void main () {
    vec4 o = vec4(1, 1, 1, 1);

    CCTexture(tex_front, v_uv0, o);

    if(o.a < .1) {
      discard;
    }
    o = mix(o, vec4(vec3(.25), 1.0), 1.0 - shadowRatio);

    gl_FragColor = o.rgba;
  }
}%

CCProgram fs-back %{
  precision highp float;

  #include <texture>

  in vec2 v_uv1;
  uniform sampler2D tex_back;

  void main () {
    vec4 o = vec4(1, 1, 1, 1);

    CCTexture(tex_back, v_uv1, o);

    if(o.a < .1) {
      discard;
    }

    gl_FragColor = o.rgba;
  }
}%