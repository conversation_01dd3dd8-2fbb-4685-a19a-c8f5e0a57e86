// Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        texture: { value: white }
        alphaThreshold: { value: 0.5 }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>

  in vec3 a_position;
  in vec4 a_color;
  out vec4 v_color;
  out vec2 v_pos;
  out float v_width;

  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;
  #endif

  uniform PointData {
    vec2 startPos;
    vec2 endPos;
    float u_angle;
    float width;
  };

  vec2 transformCoordinate(vec2 p0, vec2 p1, vec2 originalCoordinate) {
    vec2 direction = normalize(p1 - p0);
    
    float angle = atan(direction.y, direction.x);
    
    vec2 midpoint = (p0 + p1) / 2.0;
    
    mat2 rotationMatrix = mat2(
        cos(angle), -sin(angle),
        sin(angle), cos(angle)
    );
    
    vec2 translatedCoordinate = originalCoordinate - midpoint;
    return rotationMatrix * translatedCoordinate;
  }

  vec2 inverseTransformCoordinate(vec2 p0, vec2 p1, vec2 transformedCoordinate) {
    vec2 direction = normalize(p1 - p0);
    
    float angle = atan(direction.y, direction.x);
    
    vec2 midpoint = (p0 + p1) / 2.0;
    
    mat2 inverseRotationMatrix = mat2(
        cos(-angle), -sin(-angle),
        sin(-angle), cos(-angle)
    );
    
    vec2 rotatedCoordinate = inverseRotationMatrix * transformedCoordinate;
    return rotatedCoordinate + midpoint;
  }

  vec2 rotateAroundPoint(vec2 v, vec2 p, float theta) {
    vec2 translated = v - p;

    float cosTheta = cos(theta);
    float sinTheta = sin(theta);

    mat2 rotationMatrix = mat2(
        cosTheta, -sinTheta,
        sinTheta, cosTheta
    );

    vec2 rotated = rotationMatrix * translated;

    return rotated + p;
  }
  

  void main () {
    vec4 pos = vec4(a_position, 1);

    vec2 pos1 = transformCoordinate(startPos, endPos, pos.xy);

    v_pos = pos1;
    if(width != 0.0) {
      pos1.x -= 2.0 * width;
      pos.xy = inverseTransformCoordinate(startPos, endPos, pos1);
    }

    vec2 midpoint = (startPos + endPos) / 2.0;
    pos.xy = rotateAroundPoint(pos.xy, midpoint, u_angle);

    v_width = width;

    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif

    #if USE_TEXTURE
    v_uv0 = a_uv0;
    #endif

    v_color = a_color;

    gl_Position = pos;
  }
}%


CCProgram fs %{
  precision highp float;

  #include <alpha-test>
  #include <texture>

  in vec4 v_color;
  in vec2 v_pos;
  in float v_width;

  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;
  #endif

  void main () {
    vec4 o = vec4(1, 1, 1, 1);

    if(v_pos.x < v_width) {
      discard;
    }

    #if USE_TEXTURE
      CCTexture(texture, v_uv0, o);
    #endif

    o *= v_color;

    ALPHA_TEST(o);

    #if USE_BGRA
      gl_FragColor = o.bgra;
    #else
      gl_FragColor = o.rgba;
    #endif
  }
}%
