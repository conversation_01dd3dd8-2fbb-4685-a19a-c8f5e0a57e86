{"ver": "1.0.27", "uuid": "0f4ee004-d3d9-46e8-a16d-aba72fe99411", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\nvarying vec2 v_pos;\nvarying float v_width;\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nuniform vec2 startPos;\nuniform vec2 endPos;\nuniform float u_angle;\nuniform float width;\nvec2 transformCoordinate(vec2 p0, vec2 p1, vec2 originalCoordinate) {\n  vec2 direction = normalize(p1 - p0);\n  float angle = atan(direction.y, direction.x);\n  vec2 midpoint = (p0 + p1) / 2.0;\n  mat2 rotationMatrix = mat2(\n      cos(angle), -sin(angle),\n      sin(angle), cos(angle)\n  );\n  vec2 translatedCoordinate = originalCoordinate - midpoint;\n  return rotationMatrix * translatedCoordinate;\n}\nvec2 inverseTransformCoordinate(vec2 p0, vec2 p1, vec2 transformedCoordinate) {\n  vec2 direction = normalize(p1 - p0);\n  float angle = atan(direction.y, direction.x);\n  vec2 midpoint = (p0 + p1) / 2.0;\n  mat2 inverseRotationMatrix = mat2(\n      cos(-angle), -sin(-angle),\n      sin(-angle), cos(-angle)\n  );\n  vec2 rotatedCoordinate = inverseRotationMatrix * transformedCoordinate;\n  return rotatedCoordinate + midpoint;\n}\nvec2 rotateAroundPoint(vec2 v, vec2 p, float theta) {\n  vec2 translated = v - p;\n  float cosTheta = cos(theta);\n  float sinTheta = sin(theta);\n  mat2 rotationMatrix = mat2(\n      cosTheta, -sinTheta,\n      sinTheta, cosTheta\n  );\n  vec2 rotated = rotationMatrix * translated;\n  return rotated + p;\n}\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  vec2 pos1 = transformCoordinate(startPos, endPos, pos.xy);\n  v_pos = pos1;\n  if(width != 0.0) {\n    pos1.x -= 2.0 * width;\n    pos.xy = inverseTransformCoordinate(startPos, endPos, pos1);\n  }\n  vec2 midpoint = (startPos + endPos) / 2.0;\n  pos.xy = rotateAroundPoint(pos.xy, midpoint, u_angle);\n  v_width = width;\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_color;\nvarying vec2 v_pos;\nvarying float v_width;\n#if USE_TEXTURE\nvarying vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  if(v_pos.x < v_width) {\n    discard;\n  }\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture2D(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  #if USE_BGRA\n    gl_FragColor = o.bgra;\n  #else\n    gl_FragColor = o.rgba;\n  #endif\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\nout vec2 v_pos;\nout float v_width;\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nuniform PointData {\n  vec2 startPos;\n  vec2 endPos;\n  float u_angle;\n  float width;\n};\nvec2 transformCoordinate(vec2 p0, vec2 p1, vec2 originalCoordinate) {\n  vec2 direction = normalize(p1 - p0);\n  float angle = atan(direction.y, direction.x);\n  vec2 midpoint = (p0 + p1) / 2.0;\n  mat2 rotationMatrix = mat2(\n      cos(angle), -sin(angle),\n      sin(angle), cos(angle)\n  );\n  vec2 translatedCoordinate = originalCoordinate - midpoint;\n  return rotationMatrix * translatedCoordinate;\n}\nvec2 inverseTransformCoordinate(vec2 p0, vec2 p1, vec2 transformedCoordinate) {\n  vec2 direction = normalize(p1 - p0);\n  float angle = atan(direction.y, direction.x);\n  vec2 midpoint = (p0 + p1) / 2.0;\n  mat2 inverseRotationMatrix = mat2(\n      cos(-angle), -sin(-angle),\n      sin(-angle), cos(-angle)\n  );\n  vec2 rotatedCoordinate = inverseRotationMatrix * transformedCoordinate;\n  return rotatedCoordinate + midpoint;\n}\nvec2 rotateAroundPoint(vec2 v, vec2 p, float theta) {\n  vec2 translated = v - p;\n  float cosTheta = cos(theta);\n  float sinTheta = sin(theta);\n  mat2 rotationMatrix = mat2(\n      cosTheta, -sinTheta,\n      sinTheta, cosTheta\n  );\n  vec2 rotated = rotationMatrix * translated;\n  return rotated + p;\n}\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  vec2 pos1 = transformCoordinate(startPos, endPos, pos.xy);\n  v_pos = pos1;\n  if(width != 0.0) {\n    pos1.x -= 2.0 * width;\n    pos.xy = inverseTransformCoordinate(startPos, endPos, pos1);\n  }\n  vec2 midpoint = (startPos + endPos) / 2.0;\n  pos.xy = rotateAroundPoint(pos.xy, midpoint, u_angle);\n  v_width = width;\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_color;\nin vec2 v_pos;\nin float v_width;\n#if USE_TEXTURE\nin vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  if(v_pos.x < v_width) {\n    discard;\n  }\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  #if USE_BGRA\n    gl_FragColor = o.bgra;\n  #else\n    gl_FragColor = o.rgba;\n  #endif\n}"}}], "subMetas": {}}