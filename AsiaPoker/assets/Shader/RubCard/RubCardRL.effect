// Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.  

// Note: Current format version is experiment, the format may be changed.
// The future format may not be compatible, you may need to update the script manually.

// 注意：当前版本的格式是实验性的，之后还会进行修改。
// 后续版本的格式不保证兼容当前格式，可能需要手动升级到最新版本。，
CCEffect %{
  techniques: 
    
    - passes: 
        
        - vert: vs
          frag: fsBack
          rasterizerState:
            cullMode: back
          blendState:
          targets:
          - blend: true
          depthStencilState:
            depthWrite: true
            depthTest: true
          properties: 
            textureBack: {value: default}
            alphaThreshold: {value: 0.5}
            ratio: {value: 0}
            radius: {value: 0}
            width: {value: 0}
            height: {value: 0}
            offx: {value: 0}
            offy: {value: 0}
            rotation: {value: 0}
        
        - vert: vs
          frag: fsFront
          rasterizerState:
            cullMode: front
          blendState:
          targets:
          - blend: true
          depthStencilState:
            depthWrite: true
            depthTest: true
          layer: 0
          properties:
            textureFront: {value: default}
            alphaThreshold: {value: 0.5}
            ratio: {value: 0}
            radius: {value: 0}
            width: {value: 0}
            height: {value: 0}
            offx: {value: 0}
            offy: {value: 0}
            rotation: {value: 0}
}%

CCProgram vs %{

  precision highp float;

  
  #include <cc-local>
  #include <cc-global>

  attribute vec3 a_position;

  #if USE_TEXTURE
    attribute mediump vec2 a_uv0;
    varying mediump vec2 v_uv0;
  #endif

  uniform ToonFrasr {
    float ratio;
    float radius;
    float width;
    float height;
    float offx;
    float offy;
    float rotation;
  };

  void main () {
    mat4 mvp;
    
    #if USE_MODEL
      mvp = cc_matViewProj * cc_matWorld;
    #else
      mvp = cc_matViewProj;
    #endif

    v_uv0 = vec2(1.0 - a_uv0.x,1.0 - a_uv0.y);

    vec4 tmp_pos = vec4(width - a_position.x, a_position.y, 0.0, 1.0);
    float halfPeri = radius * 3.14159; 
    float hr = width * ratio;
    if(hr > 0.0 && hr <= halfPeri){
        if(tmp_pos.x < hr){
              float rad = hr/ 3.14159;
              float arc = (hr-tmp_pos.x)/rad;
              tmp_pos.x = hr - sin(arc)*rad;
              tmp_pos.z = rad * (1.0-cos(arc)); 
          }
    }
    if(hr > halfPeri){
        float straight = (hr - halfPeri)/2.0;
        if(tmp_pos.x < straight){
            tmp_pos.x = hr  - tmp_pos.x;
            tmp_pos.z = radius * 2.0; 
        }
        else if(tmp_pos.x < (straight + halfPeri)) {
            float dy = halfPeri - (tmp_pos.x - straight);
            float arc = dy/radius;
            tmp_pos.x = hr - straight - sin(arc)*radius;
            tmp_pos.z = radius * (1.0-cos(arc)); 
        }
    }
    float x1 = tmp_pos.x;
    float z1 = tmp_pos.z;
    float x2 = width;
    float z2 = 0.0;
    float sinRat = sin(rotation);
    float cosRat = cos(rotation);
    tmp_pos.x=(x1-x2)*cosRat-(z1-z2)*sinRat+x2;
    tmp_pos.z=(z1-z2)*cosRat+(x1-x2)*sinRat+z2;
    tmp_pos.x = tmp_pos.x - width/2.0*(1.0-cosRat);
    tmp_pos.x = width - tmp_pos.x;
    tmp_pos += vec4(offx, offy, 0.0, 0.0);
    gl_Position = mvp * tmp_pos;
  }

}%

CCProgram fsBack %{

  precision highp float;
  uniform sampler2D textureBack;
  varying mediump vec2 v_uv0;

  #include <alpha-test>

  void main () {
    vec4 color = vec4(1.);
    color *= texture2D(textureBack, v_uv0);
    gl_FragColor = color;
  }

}%

CCProgram fsFront %{

  precision highp float;
  uniform sampler2D textureFront;
  varying mediump vec2 v_uv0;

  #include <alpha-test>

  void main () {
    vec4 color = vec4(1.);
    color *= texture2D(textureFront, v_uv0);
    gl_FragColor = color;
  }

}%
