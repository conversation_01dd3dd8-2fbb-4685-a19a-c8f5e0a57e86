{"ver": "1.0.27", "uuid": "6d813663-5c83-4604-b081-c947ba190d65", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matWorld;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\n#if USE_TEXTURE\n  attribute mediump vec2 a_uv0;\n  varying mediump vec2 v_uv0;\n#endif\nuniform float ratio;\nuniform float radius;\nuniform float width;\nuniform float offx;\nuniform float offy;\nuniform float rotation;\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  v_uv0 = vec2(1.0 - a_uv0.x,1.0 - a_uv0.y);\n  vec4 tmp_pos = vec4(width - a_position.x, a_position.y, 0.0, 1.0);\n  float halfPeri = radius * 3.14159;\n  float hr = width * ratio;\n  if(hr > 0.0 && hr <= halfPeri){\n      if(tmp_pos.x < hr){\n            float rad = hr/ 3.14159;\n            float arc = (hr-tmp_pos.x)/rad;\n            tmp_pos.x = hr - sin(arc)*rad;\n            tmp_pos.z = rad * (1.0-cos(arc));\n        }\n  }\n  if(hr > halfPeri){\n      float straight = (hr - halfPeri)/2.0;\n      if(tmp_pos.x < straight){\n          tmp_pos.x = hr  - tmp_pos.x;\n          tmp_pos.z = radius * 2.0;\n      }\n      else if(tmp_pos.x < (straight + halfPeri)) {\n          float dy = halfPeri - (tmp_pos.x - straight);\n          float arc = dy/radius;\n          tmp_pos.x = hr - straight - sin(arc)*radius;\n          tmp_pos.z = radius * (1.0-cos(arc));\n      }\n  }\n  float x1 = tmp_pos.x;\n  float z1 = tmp_pos.z;\n  float x2 = width;\n  float z2 = 0.0;\n  float sinRat = sin(rotation);\n  float cosRat = cos(rotation);\n  tmp_pos.x=(x1-x2)*cosRat-(z1-z2)*sinRat+x2;\n  tmp_pos.z=(z1-z2)*cosRat+(x1-x2)*sinRat+z2;\n  tmp_pos.x = tmp_pos.x - width/2.0*(1.0-cosRat);\n  tmp_pos.x = width - tmp_pos.x;\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureBack;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureBack, v_uv0);\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nattribute vec3 a_position;\n#if USE_TEXTURE\n  attribute mediump vec2 a_uv0;\n  varying mediump vec2 v_uv0;\n#endif\nuniform ToonFrasr {\n  float ratio;\n  float radius;\n  float width;\n  float height;\n  float offx;\n  float offy;\n  float rotation;\n};\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  v_uv0 = vec2(1.0 - a_uv0.x,1.0 - a_uv0.y);\n  vec4 tmp_pos = vec4(width - a_position.x, a_position.y, 0.0, 1.0);\n  float halfPeri = radius * 3.14159;\n  float hr = width * ratio;\n  if(hr > 0.0 && hr <= halfPeri){\n      if(tmp_pos.x < hr){\n            float rad = hr/ 3.14159;\n            float arc = (hr-tmp_pos.x)/rad;\n            tmp_pos.x = hr - sin(arc)*rad;\n            tmp_pos.z = rad * (1.0-cos(arc));\n        }\n  }\n  if(hr > halfPeri){\n      float straight = (hr - halfPeri)/2.0;\n      if(tmp_pos.x < straight){\n          tmp_pos.x = hr  - tmp_pos.x;\n          tmp_pos.z = radius * 2.0;\n      }\n      else if(tmp_pos.x < (straight + halfPeri)) {\n          float dy = halfPeri - (tmp_pos.x - straight);\n          float arc = dy/radius;\n          tmp_pos.x = hr - straight - sin(arc)*radius;\n          tmp_pos.z = radius * (1.0-cos(arc));\n      }\n  }\n  float x1 = tmp_pos.x;\n  float z1 = tmp_pos.z;\n  float x2 = width;\n  float z2 = 0.0;\n  float sinRat = sin(rotation);\n  float cosRat = cos(rotation);\n  tmp_pos.x=(x1-x2)*cosRat-(z1-z2)*sinRat+x2;\n  tmp_pos.z=(z1-z2)*cosRat+(x1-x2)*sinRat+z2;\n  tmp_pos.x = tmp_pos.x - width/2.0*(1.0-cosRat);\n  tmp_pos.x = width - tmp_pos.x;\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureBack;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureBack, v_uv0);\n  gl_FragColor = color;\n}"}}, {"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matWorld;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\n#if USE_TEXTURE\n  attribute mediump vec2 a_uv0;\n  varying mediump vec2 v_uv0;\n#endif\nuniform float ratio;\nuniform float radius;\nuniform float width;\nuniform float offx;\nuniform float offy;\nuniform float rotation;\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  v_uv0 = vec2(1.0 - a_uv0.x,1.0 - a_uv0.y);\n  vec4 tmp_pos = vec4(width - a_position.x, a_position.y, 0.0, 1.0);\n  float halfPeri = radius * 3.14159;\n  float hr = width * ratio;\n  if(hr > 0.0 && hr <= halfPeri){\n      if(tmp_pos.x < hr){\n            float rad = hr/ 3.14159;\n            float arc = (hr-tmp_pos.x)/rad;\n            tmp_pos.x = hr - sin(arc)*rad;\n            tmp_pos.z = rad * (1.0-cos(arc));\n        }\n  }\n  if(hr > halfPeri){\n      float straight = (hr - halfPeri)/2.0;\n      if(tmp_pos.x < straight){\n          tmp_pos.x = hr  - tmp_pos.x;\n          tmp_pos.z = radius * 2.0;\n      }\n      else if(tmp_pos.x < (straight + halfPeri)) {\n          float dy = halfPeri - (tmp_pos.x - straight);\n          float arc = dy/radius;\n          tmp_pos.x = hr - straight - sin(arc)*radius;\n          tmp_pos.z = radius * (1.0-cos(arc));\n      }\n  }\n  float x1 = tmp_pos.x;\n  float z1 = tmp_pos.z;\n  float x2 = width;\n  float z2 = 0.0;\n  float sinRat = sin(rotation);\n  float cosRat = cos(rotation);\n  tmp_pos.x=(x1-x2)*cosRat-(z1-z2)*sinRat+x2;\n  tmp_pos.z=(z1-z2)*cosRat+(x1-x2)*sinRat+z2;\n  tmp_pos.x = tmp_pos.x - width/2.0*(1.0-cosRat);\n  tmp_pos.x = width - tmp_pos.x;\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureFront;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureFront, v_uv0);\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nattribute vec3 a_position;\n#if USE_TEXTURE\n  attribute mediump vec2 a_uv0;\n  varying mediump vec2 v_uv0;\n#endif\nuniform ToonFrasr {\n  float ratio;\n  float radius;\n  float width;\n  float height;\n  float offx;\n  float offy;\n  float rotation;\n};\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  v_uv0 = vec2(1.0 - a_uv0.x,1.0 - a_uv0.y);\n  vec4 tmp_pos = vec4(width - a_position.x, a_position.y, 0.0, 1.0);\n  float halfPeri = radius * 3.14159;\n  float hr = width * ratio;\n  if(hr > 0.0 && hr <= halfPeri){\n      if(tmp_pos.x < hr){\n            float rad = hr/ 3.14159;\n            float arc = (hr-tmp_pos.x)/rad;\n            tmp_pos.x = hr - sin(arc)*rad;\n            tmp_pos.z = rad * (1.0-cos(arc));\n        }\n  }\n  if(hr > halfPeri){\n      float straight = (hr - halfPeri)/2.0;\n      if(tmp_pos.x < straight){\n          tmp_pos.x = hr  - tmp_pos.x;\n          tmp_pos.z = radius * 2.0;\n      }\n      else if(tmp_pos.x < (straight + halfPeri)) {\n          float dy = halfPeri - (tmp_pos.x - straight);\n          float arc = dy/radius;\n          tmp_pos.x = hr - straight - sin(arc)*radius;\n          tmp_pos.z = radius * (1.0-cos(arc));\n      }\n  }\n  float x1 = tmp_pos.x;\n  float z1 = tmp_pos.z;\n  float x2 = width;\n  float z2 = 0.0;\n  float sinRat = sin(rotation);\n  float cosRat = cos(rotation);\n  tmp_pos.x=(x1-x2)*cosRat-(z1-z2)*sinRat+x2;\n  tmp_pos.z=(z1-z2)*cosRat+(x1-x2)*sinRat+z2;\n  tmp_pos.x = tmp_pos.x - width/2.0*(1.0-cosRat);\n  tmp_pos.x = width - tmp_pos.x;\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureFront;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureFront, v_uv0);\n  gl_FragColor = color;\n}"}}], "subMetas": {}}