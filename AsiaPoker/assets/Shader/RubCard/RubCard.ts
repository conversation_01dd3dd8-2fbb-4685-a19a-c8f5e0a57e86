import { HashMap } from "../../Script/common/tools/HashMap";


/**
 * "搓牌"方位
 */
enum RubCardDirection {
    SC_DIR_NONE = 0,                                                                                // 不搓
    // SC_DIR_UP_DOWN,                                                                                 // 上下
    SC_DIR_DOWN_UP,                                                                                 // 下上
    SC_DIR_LEFT_RIGHT,                                                                              // 左右
    SC_DIR_RIGHT_LEFT,                                                                              // 右左
}

/**
 * "搓牌"材质枚举
 */
enum RubCardMatType {
    SC_MAT_None = 1,
    SC_MAT_DOWN_UP,
    SC_MAT_DOWN_UP_AUTO,
    SC_MAT_DOWN_LEFT_RIGHT,
    SC_MAT_DOWN_RIGHT_LEFT,
    SC_MAT_DOWN_LEFT_RIGHT_AUTO,
}

/**
 * "搓牌"对齐方式
 */
enum RubCardAlignment {
    SC_ALIGNMENT_NONE = 0,                                                                          // 无
    SC_ALIGNMENT_VERTICAL,                                                                          // 纵向
    SC_ALIGNMENT_HORIZONTAL,                                                                        // 横向
}

/**
 * "搓牌"状态
 */
enum RubCardStatus {
    SC_STATUS_MOVE = 0,                                                                             // 移动
    SC_STATUS_SMOOTH,                                                                               // 弹开
}

/**
 * "搓牌"逻辑
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class RubCard extends cc.Component {
    private _pokerFaceTexture: cc.Texture2D = null;                                                 // 牌正面
    private _pokerBackTexture: cc.Texture2D = null;                                                 // 牌背面

    private _pokerFaceTextureFileName: string = null;                                               // 牌正面资源路径
    private _pokerBackTextureFileName: string = null;                                               // 牌背面资源路径

    private _rotationAnger: number = Math.PI / 3;
    private _rotationTotalFrame: number = 10;

    private _smoothAnger: number = Math.PI / 6;
    private _smoothFrame: number = 0;
    private _smoothTotalFrame: number = 10;

    private _status: RubCardStatus = RubCardStatus.SC_STATUS_MOVE;                                  //
    private _isCreateNum: boolean = false;
    private _bChangedMatOnce: boolean = false;
    private _pokerRatio: number = 0;
    private _pokerAlign: RubCardAlignment = RubCardAlignment.SC_ALIGNMENT_HORIZONTAL;               //
    private _pokerDir: RubCardDirection = RubCardDirection.SC_DIR_NONE;                             // 

    private _pokerSrcWidth: number = 0;                                                             // 牌原始宽度
    private _pokerSrcHeight: number = 0;                                                            // 牌原始高度
    private _pokerDestWidth: number = 0;                                                            // 牌目标宽度
    private _pokerDestHeight: number = 0;                                                           // 牌目标高度

    private _touchStartY: number = 0;                                                               // 底部y
    private _touchStartLRX: number = 0;                                                             // 左边x
    private _touchStartRLX: number = 0;                                                             // 右边x

    private _meshRenderer: cc.MeshRenderer = null;
    private _mapMat: HashMap<RubCardMatType, cc.Material> = new HashMap();                          // 材质容器
    private _udMesh: cc.Mesh = null;
    private _lrMesh: cc.Mesh = null;

    private _scaleRatio: number = 1;                                                                // 缩放比例
    private _position: cc.Vec2 = cc.Vec2.ZERO;                                                      // 位置
    private _callback: (dir: RubCardDirection, angle: number) => void = null;                       // 牌翻开后的回调
    private _filpNum: number = 1.2;
    private _autoFlip: boolean = false;
    private _autoShowTime: number = 2;
    public unregisterEvent = false;

    /**
     * 设置牌正面纹理资源路径(此方法只能用于 resource 路径下的动态资源)
     * @param texture 
     */
    setFaceTextureFileName(fileName: string): void {
        this._pokerFaceTextureFileName = fileName;
    }

    /**
     * 设置牌背面纹理资源路径(此方法只能用于 resource 路径下的动态资源)
     * @param texture 
     */
    setBackTextureFileName(fileName: string): void {
        this._pokerBackTextureFileName = fileName;
    }

    /**
     * 设置"翻牌"后的回调
     * @param cb 
     */
    setCallBack(cb: (dir: RubCardDirection, angle: number) => void): void {
        this._callback = cb;
    }

    /**
     * 设置"搓牌"位置
     * @param newPosOrX 
     * @param y 
     */
    setPosition(x: number, y: number): void {
        if (x === this._position.x && y === this._position.y) {
            return;
        }

        this._position.x = x;
        this._position.y = y;
    }

    /**
     * 设置"搓牌"缩放比例
     * @param scale 
     */
    setScale(scale: number): void {
        this._scaleRatio = scale;
    }

    protected onLoad(): void {
        if (!CC_EDITOR) {
            this._registerTouchEvent();
        }
    }

    protected start(): void {
        this._loadFaceTexture();
        this._loadBackTexture();
    }

    protected onEnable(): void {
    }

    protected onDisable(): void {
        if (!CC_EDITOR) {
            this._unregisterTouchEvent();
        }
    }

    protected update(dt: number): void {
        if (this._meshRenderer) {
            if (this._autoFlip) {
                this._pokerRatio += this._filpNum / (this._autoShowTime * 60);
                if (this._pokerRatio >= this._filpNum) {
                    this._status = RubCardStatus.SC_STATUS_SMOOTH;
                    this._autoFlip = false;
                }
            }
            let mat: any = this._meshRenderer.getMaterial(0);
            switch (this._status) {
                case RubCardStatus.SC_STATUS_MOVE: {
                    mat.setProperty('ratio', this._pokerRatio);
                } break;

                case RubCardStatus.SC_STATUS_SMOOTH: {
                    if (this._smoothFrame <= this._rotationTotalFrame) {
                        let rot: number = -this._rotationAnger * this._smoothFrame / this._rotationTotalFrame;
                        mat.setProperty('rotation', rot);
                    }
                    else if (this._smoothFrame < (this._rotationTotalFrame + this._smoothTotalFrame)) {
                        this._changeToAutoMatOnce()
                        let scale: number = (this._smoothFrame - this._rotationTotalFrame) / this._smoothTotalFrame;
                        let rot: number = Math.max(0.01, this._smoothAnger * (1 - scale));
                        mat.setProperty('rotation', rot);
                    }
                    else {
                        //第一次到这里就铺平了
                        if (this._isCreateNum == false) {
                            this._isCreateNum = true
                            mat.setProperty('rotation', 0.0);

                            this.node.removeFromParent(true);
                            this.node.destroy();

                            if (this._callback) {
                                let angle: number = 0;
                                switch (this._pokerDir) {
                                    case RubCardDirection.SC_DIR_NONE: angle = 0; break;
                                    // case RubCardDirection.SC_DIR_UP_DOWN: angle = 0; break;
                                    case RubCardDirection.SC_DIR_DOWN_UP: angle = -90; break;
                                    case RubCardDirection.SC_DIR_LEFT_RIGHT: angle = -90; break;
                                    case RubCardDirection.SC_DIR_RIGHT_LEFT: angle = 90; break;
                                    default: break;
                                }
                                this._callback(this._pokerDir, angle);
                            }

                            // this._unregisterTouchEvent();
                            // this.schedule((t: number): void => {
                            //     // this.node.active = false
                            //     // this._meshRenderer.onDisable()
                            //     // this.destroy()

                            //     // this._bChangedMatOnce = false;
                            //     // this._smoothFrame = 0;
                            //     // this._status = RubCardStatus.SC_STATUS_MOVE;
                            //     // this._pokerRatio = 0;
                            //     // this._changeTouchMeshAndMat(RubCardDirection.SC_DIR_DOWN_UP);
                            //     // mat.setProperty('rotation', 0);
                            //     // this._meshRenderer.onDisable()

                            //     this.node.active = true;
                            //     this.node.runAction(cc.repeatForever(cc.rotateBy(1.0, 360)));
                            // }, 2);
                        }
                    }
                    this._smoothFrame = this._smoothFrame + 1
                } break;
            }
        }
    }

    /**
     * 动态加载"牌正面"纹理
     */
    private _loadFaceTexture(): void {
        let fileName: string = this._pokerFaceTextureFileName;

        let cb: (texture: cc.Texture2D) => void = (texture: cc.Texture2D): void => {
            this._pokerFaceTexture = texture;
            this._preInit();
        }

        let texture: cc.Texture2D = cc.resources.get(fileName, cc.Texture2D);
        if (texture) {
            cb(texture);
        }
        else {
            cc.resources.load(fileName, cc.Texture2D, (error: Error, res: cc.Texture2D): void => {
                if (error) {
                    console.error(error.message || error);
                    return;
                }
                // 警告: 这里用 console.error 是为了提示更醒目(一般情况下资源都预加载了, 若执行这里, 请确保该出资源是何种加载方式, 若是异步的就请忽略)
                console.warn(`RubCard - warning: This resource is loaded asynchronously - [path =${fileName}]`);
                cb(res);
            });
        }
    }

    /**
     * 动态加载"牌正面"纹理
     */
    private _loadBackTexture(): void {
        let fileName: string = this._pokerBackTextureFileName;
        let self = this;
        let cb: (texture: cc.Texture2D) => void = (texture: cc.Texture2D): void => {
            self._pokerBackTexture = texture;
            self._preInit();
        }

        let texture: cc.Texture2D = cc.resources.get(fileName, cc.Texture2D);
        if (texture) {
            cb(texture);
        }
        else {
            
            cc.resources.load(fileName, cc.Texture2D, (error: Error, res: cc.Texture2D): void => {
                if (error) {
                    console.error(error.message || error);
                    return;
                }
                // 警告: 这里用 console.error 是为了提示更醒目(一般情况下资源都预加载了, 若执行这里, 请确保该出资源是何种加载方式, 若是异步的就请忽略)
                console.warn(`RubCard - warning: This resource is loaded asynchronously - [path =${fileName}]`);
                cb(res);
            });
        }
    }

    /**
     * 初始化预处理
     */
    private _preInit(): void {
        if (this._pokerFaceTexture && this._pokerBackTexture) {
            this._init();
        }
    }

    /**
     * 初始化
     */
    private _init(): void {
        // 设置摄像机为"透视投影"
        let camera: cc.Camera = this.node.getComponent(cc.Camera);
        camera.ortho = false;
        camera.fov = 60;

        this._initMatInfo();
        this._udMesh = this._createMesh(RubCardAlignment.SC_ALIGNMENT_VERTICAL);
        this._lrMesh = this._createMesh(RubCardAlignment.SC_ALIGNMENT_HORIZONTAL);

        this._pokerDir = RubCardDirection.SC_DIR_NONE;
        this._changeTouchMeshAndMat(RubCardDirection.SC_DIR_DOWN_UP);
    }

    /**
     * 初始化"材质/网格"信息
     */
    private _initMatInfo(): void {
        let meshRenderer: cc.MeshRenderer = this.node.getComponent(cc.MeshRenderer);
        if (!meshRenderer) {
            meshRenderer = this.node.addComponent(cc.MeshRenderer);
        }
        this._meshRenderer = meshRenderer

        this._pokerSrcWidth = this._pokerBackTexture.width;
        this._pokerSrcHeight = this._pokerBackTexture.height;
        let poker_tar_w: number = this._pokerSrcWidth * this._scaleRatio;
        let poker_tar_h: number = this._pokerSrcHeight * this._scaleRatio;

        let poker_w: number = 0;
        let poker_h: number = 0;
        switch (this._pokerAlign) {
            case RubCardAlignment.SC_ALIGNMENT_NONE: break;

            case RubCardAlignment.SC_ALIGNMENT_VERTICAL: {
                poker_w = Math.min(poker_tar_w, poker_tar_h);
                poker_h = Math.max(poker_tar_w, poker_tar_h);
            } break;

            case RubCardAlignment.SC_ALIGNMENT_HORIZONTAL: {
                poker_w = Math.max(poker_tar_w, poker_tar_h);
                poker_h = Math.min(poker_tar_w, poker_tar_h);
            } break;

            default: break;
        }

        let pos: cc.Vec3 = cc.v3(0, 0, cc.winSize.height * 3 / (2 * 1.732050807568877));
        this.node.setPosition(pos);
        this.node.setContentSize(cc.winSize.width, cc.winSize.height);
        this.node.convertToWorldSpaceAR(this.node.position, pos);

        let pokerRadius: number = poker_h / 10;
        let poker_offset_x: number = pos.x - poker_w * this.node.anchorX + this._position.x;
        let poker_offset_y: number = pos.y - poker_h * this.node.anchorY + this._position.y;

        this._touchStartY = poker_offset_y;
        this._touchStartLRX = poker_offset_x
        this._touchStartRLX = poker_offset_x + poker_w;

        this._pokerDestWidth = poker_w;
        this._pokerDestHeight = poker_h;

        // init materials
        let matTypeArray: RubCardMatType[] = [
            RubCardMatType.SC_MAT_None,
            RubCardMatType.SC_MAT_DOWN_UP,
            RubCardMatType.SC_MAT_DOWN_UP_AUTO,
            RubCardMatType.SC_MAT_DOWN_LEFT_RIGHT,
            RubCardMatType.SC_MAT_DOWN_RIGHT_LEFT,
            RubCardMatType.SC_MAT_DOWN_LEFT_RIGHT_AUTO,
        ]
        for (let i = 0; i < this._meshRenderer.getMaterials().length; ++i) {
            if (i < matTypeArray.length) {
                let material: cc.MaterialVariant = this._meshRenderer.getMaterial(i);
                let pLen = material._effect.passes.length;
                // let haveBack = false;
                // let haveFront = false;
                // for (let j = 0; i < pLen; i++) {
                //     let pass = material._effect.passes[j];
                //     haveBack = pass._properties.hasOwnProperty('textureBack');
                //     haveFront = pass._properties.hasOwnProperty('textureFront');
                // }
                material.setProperty('radius', pokerRadius);
                material.setProperty('width', poker_w);
                material.setProperty('height', poker_h);
                material.setProperty('offx', poker_offset_x);
                material.setProperty('offy', poker_offset_y);
                material.setProperty('rotation', 0);

                if (pLen == 2) {
                    material.setProperty('textureBack', this._pokerBackTexture,0);
                    material.setProperty('textureFront', this._pokerFaceTexture,1);
                }
                else if (pLen == 1) {
                    material.setProperty('textureFront', this._pokerFaceTexture,0);
                }
                this._mapMat.add(matTypeArray[i], material);
            }
        }
    }

    /**
     * 创建网格
     */
    private _createMesh(align: RubCardAlignment): cc.Mesh {
        let namespcae_cc: any = cc;
        let gfx: any = namespcae_cc.gfx;
        let vfmt: any = new namespcae_cc.gfx.VertexFormat([                                         // 顶点格式
            { name: gfx.ATTR_POSITION, type: gfx.ATTR_TYPE_FLOAT32, num: 2 },
            { name: gfx.ATTR_UV0, type: gfx.ATTR_TYPE_FLOAT32, num: 2 },
        ]);

        let div: number = 30;                                                                       // 等份
        let vertices: number = div * 2 + 2;                                                         // 顶点数

        let mesh: cc.Mesh = new cc.Mesh();                                                          // 网格
        mesh.init(vfmt, vertices, true);                                                            // 网格初始化

        let isTextureVertical: boolean = this._pokerSrcWidth < this._pokerSrcHeight;                // 原始牌是否是纵/横
        let texture_w: number = this._pokerDestWidth;                                               // 纹理实际宽
        let texture_h: number = this._pokerDestHeight;                                              // 纹理实际高
        let div_w: number = texture_w / div;                                                        // 纹理每等份宽
        let div_h: number = texture_h / div;                                                        // 纹理每等份高
        let div_texture = 1 / div;                                                                  // 纹理uv贴图每等份值

        let arrayNum: number = vertices * 2;                                                        // 数据数组长度
        let vertexArray: Float32Array = new Float32Array(arrayNum);                                 // 顶点数组
        let uvArray: Float32Array = new Float32Array(arrayNum);                                     // uv贴图数组

        let triangleNum: number = (vertices / 2 - 1) * 6;                                           // 三角形数量
        let indiceArray: Uint16Array = new Uint16Array(triangleNum);                                // 子网格数据

        let start_x = 0;
        let start_y = 0;
        let index = 0;

        switch (align) {
            // 横向
            case RubCardAlignment.SC_ALIGNMENT_HORIZONTAL: {
                for (let i = 0; i < vertices; i++) {
                    // vertex
                    let x = start_x;
                    let y = start_y;
                    if (i % 2 == 0) {
                        x = (i / 2) * div_w;
                    }
                    else {
                        y = start_y + texture_h;
                        x = ((i - 1) / 2) * div_w;
                    }
                    x = x + start_x;

                    vertexArray[i * 2] = x;
                    vertexArray[i * 2 + 1] = y;

                    // uv
                    if (i % 2 == 0) {
                        if (isTextureVertical) {
                            uvArray[i * 2] = 1;
                            uvArray[i * 2 + 1] = (i / 2) * div_texture;
                        }
                        else {
                            uvArray[i * 2] = (i / 2) * div_texture;
                            uvArray[i * 2 + 1] = 0;
                        }
                    }
                    else {
                        if (isTextureVertical) {
                            uvArray[i * 2] = 0;
                            uvArray[i * 2 + 1] = ((i - 1) / 2) * div_texture;
                        }
                        else {
                            uvArray[i * 2] = ((i - 1) / 2) * div_texture;
                            uvArray[i * 2 + 1] = 1;
                        }
                    }
                }

                // triangle
                for (let i = 0; i < (vertices - 2); i = i + 2) {
                    indiceArray[index++] = i;
                    indiceArray[index++] = i + 2;
                    indiceArray[index++] = i + 1;
                    indiceArray[index++] = i + 1;
                    indiceArray[index++] = i + 2;
                    indiceArray[index++] = i + 3;
                }
            } break;

            // 纵向
            case RubCardAlignment.SC_ALIGNMENT_VERTICAL: {
                for (let i = 0; i < vertices; i++) {
                    // vertex
                    let x = start_x;
                    let y = start_y;

                    if (i % 2 == 0) {
                        y = (i / 2) * div_h;
                    }
                    else {
                        x = start_x + texture_w;
                        y = ((i - 1) / 2) * div_h;
                    }
                    y = y + start_y;

                    vertexArray[i * 2] = x;
                    vertexArray[i * 2 + 1] = y;

                    // uv
                    // if (i % 2 == 0) {
                    //     uvArray[i * 2] = 1 - (i / 2) * div_texture;
                    //     uvArray[i * 2 + 1] = 1;
                    // } else {
                    //     uvArray[i * 2] = 1 - ((i - 1) / 2) * div_texture;
                    //     uvArray[i * 2 + 1] = 0;
                    // }

                    if (i % 2 == 0) {
                        if (isTextureVertical) {
                            uvArray[i * 2] = (i / 2) * div_texture;
                            uvArray[i * 2 + 1] = 1;
                        }
                        else {
                            uvArray[i * 2] = 0;
                            uvArray[i * 2 + 1] = (i / 2) * div_texture;
                        }
                    }
                    else {
                        if (isTextureVertical) {
                            uvArray[i * 2] = ((i - 1) / 2) * div_texture;
                            uvArray[i * 2 + 1] = 0;
                        }
                        else {
                            uvArray[i * 2] = 1;
                            uvArray[i * 2 + 1] = ((i - 1) / 2) * div_texture;
                        }
                    }
                }

                // triangle
                for (let i = 0; i < (vertices - 2); i = i + 2) {
                    indiceArray[index++] = i;
                    indiceArray[index++] = i + 1;
                    indiceArray[index++] = i + 2;
                    indiceArray[index++] = i + 1;
                    indiceArray[index++] = i + 3;
                    indiceArray[index++] = i + 2;
                }
            } break;
        }

        // 设置顶点数据
        mesh.setVertices(gfx.ATTR_POSITION, vertexArray);
        mesh.setVertices(gfx.ATTR_UV0, uvArray);
        mesh.setIndices(indiceArray, 0);

        return mesh;
    }

    /**
     * 动态改变材质和网格
     * @param dir 
     */
    private _changeTouchMeshAndMat(dir: RubCardDirection): void {
        if (this._pokerDir === dir) return;
        this._pokerDir = dir;

        switch (dir) {
            // 左右
            case RubCardDirection.SC_DIR_LEFT_RIGHT: {
                let mat: cc.Material = this._mapMat.get(RubCardMatType.SC_MAT_DOWN_LEFT_RIGHT);
                if (mat) {
                    this._meshRenderer.setMaterial(0, mat);
                    this._meshRenderer.mesh = this._lrMesh;
                }
            } break;

            // 右左
            case RubCardDirection.SC_DIR_RIGHT_LEFT: {
                let mat: cc.Material = this._mapMat.get(RubCardMatType.SC_MAT_DOWN_RIGHT_LEFT);
                if (mat) {
                    this._meshRenderer.setMaterial(0, mat);
                    this._meshRenderer.mesh = this._lrMesh;
                }
            } break;

            // 上下
            case RubCardDirection.SC_DIR_DOWN_UP: {
                let mat: cc.Material = this._mapMat.get(RubCardMatType.SC_MAT_DOWN_UP);
                if (mat) {
                    this._meshRenderer.setMaterial(0, mat);
                    this._meshRenderer.mesh = this._udMesh;
                }
            } break;

            default: break;
        }
    }

    /**
     * 动态改变"自动弹性"材质
     */
    private _changeToAutoMatOnce(): void {
        if (this._bChangedMatOnce) return;
        this._bChangedMatOnce = true

        switch (this._pokerDir) {
            case RubCardDirection.SC_DIR_NONE: break;

            // case RubCardDirection.SC_DIR_UP_DOWN:
            case RubCardDirection.SC_DIR_DOWN_UP: {
                let mat: cc.Material = this._mapMat.get(RubCardMatType.SC_MAT_DOWN_UP_AUTO);
                if (mat) this._meshRenderer.setMaterial(0, mat);
            } break;

            case RubCardDirection.SC_DIR_LEFT_RIGHT:
            case RubCardDirection.SC_DIR_RIGHT_LEFT: {
                let mat: cc.Material = this._mapMat.get(RubCardMatType.SC_MAT_DOWN_LEFT_RIGHT_AUTO);
                if (mat) this._meshRenderer.setMaterial(0, mat);
            } break;

            default: break;
        }
    }

    /**
     * 注册触摸事件
     */
    private _registerTouchEvent(): void {
        this.unregisterEvent = false;
        this.node.on(cc.Node.EventType.TOUCH_START, this._onTouchEventStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this._onTouchEventMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this._onTouchEventEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this._onTuchEventCancel, this);
    }

    /**
     * 反注册触摸事件
     */
    private _unregisterTouchEvent(): void {
        this.node.off(cc.Node.EventType.TOUCH_START, this._onTouchEventStart, this);
        this.node.off(cc.Node.EventType.TOUCH_MOVE, this._onTouchEventMove, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this._onTouchEventEnd, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this._onTuchEventCancel, this);
        this.unregisterEvent = true;
    }

    /**
     * 当手指触摸到屏幕时
     * @param event 
     */
    private _onTouchEventStart(event: cc.Event.EventTouch): void {
        let touchOffset: number = 100;
        let location: cc.Vec2 = event.getLocation();
        let dir: RubCardDirection = RubCardDirection.SC_DIR_NONE;
        if (location.x > this._touchStartLRX && location.x < this._touchStartRLX && location.y < (this._touchStartY + touchOffset)) {
            dir = RubCardDirection.SC_DIR_DOWN_UP;
        }
        else if (location.y > this._touchStartY && location.y < (this._touchStartY + this._pokerDestHeight) && location.x < (this._touchStartLRX + touchOffset)) {
            dir = RubCardDirection.SC_DIR_LEFT_RIGHT;
        }
        else if (location.y > this._touchStartY && location.y < (this._touchStartY + this._pokerDestHeight) && location.x > (this._touchStartRLX - touchOffset)) {
            dir = RubCardDirection.SC_DIR_RIGHT_LEFT;
        }
        else {
            // dir = RubCardDirection.SC_DIR_DOWN_UP;
        }
        this._changeTouchMeshAndMat(dir);
    }

    /**
     * 当手指在屏幕上移动时
     * @param event 
     */
    private _onTouchEventMove(event: cc.Event.EventTouch): void {
        let location: cc.Vec2 = event.getLocation();
        //this.pokerRatio = (location.y-this.pokerOffY)/this.pokerHeight

        switch (this._pokerDir) {
            case RubCardDirection.SC_DIR_NONE: break;
            // case RubCardDirection.SC_DIR_UP_DOWN: {
            // } break;

            case RubCardDirection.SC_DIR_DOWN_UP: {
                this._pokerRatio = (location.y - this._touchStartY) / this._pokerDestHeight;
            } break;

            case RubCardDirection.SC_DIR_LEFT_RIGHT: {
                this._pokerRatio = (location.x - this._touchStartLRX) / this._pokerDestWidth;
            } break;

            case RubCardDirection.SC_DIR_RIGHT_LEFT: {
                this._pokerRatio = (this._touchStartRLX - location.x) / this._pokerDestWidth;
            } break;

            default: break;
        }

        this._pokerRatio = Math.max(0, this._pokerRatio)
        this._pokerRatio = Math.min(this._filpNum, this._pokerRatio)
        if (this._pokerRatio >= this._filpNum) {
            this._status = RubCardStatus.SC_STATUS_SMOOTH;

            this._unregisterTouchEvent();
        }
    }

    /**
     * 当手指在目标节点区域内离开屏幕时
     * @param event 
     */
    private _onTouchEventEnd(event: cc.Event.EventTouch): void {
        if (this._pokerRatio >= this._filpNum) {
            this._status = RubCardStatus.SC_STATUS_SMOOTH;

            this._unregisterTouchEvent();
        }
        else {
            this._pokerRatio = 0
        }
    }

    /**
     * 当手指触摸到屏幕时
     * @param event 
     */
    private _onTuchEventCancel(event: cc.Event.EventTouch): void {
        if (this._pokerRatio >= this._filpNum) {
            this._status = RubCardStatus.SC_STATUS_SMOOTH;
            this._unregisterTouchEvent();
        }
        else {
            this._pokerRatio = 0
        }
    }

    public showAuto() {
        let dir = RubCardDirection.SC_DIR_DOWN_UP;
        this._changeTouchMeshAndMat(dir);
        this._unregisterTouchEvent();
    }

    public beginFlip() {
        this._autoFlip = true;
    }
}
