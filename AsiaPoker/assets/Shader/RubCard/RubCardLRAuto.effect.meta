{"ver": "1.0.27", "uuid": "94040832-7a14-45f6-99a1-50d73b738b60", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matWorld;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\n#if USE_TEXTURE\n  attribute mediump vec2 a_uv0;\n  varying mediump vec2 v_uv0;\n#endif\nuniform float width;\nuniform float offx;\nuniform float offy;\nuniform float rotation;\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  v_uv0 = vec2(1.0 - a_uv0.x,a_uv0.y);\n  vec4 tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  float cl = width/5.0;\n  float sl = (width - cl)/2.0;\n  float radii = (cl/rotation)/2.0;\n  float sinRot = sin(rotation);\n  float cosRot = cos(rotation);\n  float distance = radii*sinRot;\n  float centerX = width/2.0;\n  float posX1 = centerX - distance;\n  float posX2 = centerX + distance;\n  float posZ = sl*sinRot;\n  if(tmp_pos.x <= sl){\n    float length = sl - tmp_pos.x;\n    tmp_pos.x = posX1 - length*cosRot;\n    tmp_pos.z = posZ - length*sinRot;\n  }\n  else if(tmp_pos.x < (sl+cl)){\n    float el = tmp_pos.x - sl;\n    float rotation2 = -el/radii;\n    float x1 = posX1;\n    float y1 = posZ;\n    float x2 = centerX;\n    float y2 = posZ - radii*cosRot;\n    float sinRot2 = sin(rotation2);\n    float cosRot2 = cos(rotation2);\n    tmp_pos.x=(x1-x2)*cosRot2-(y1-y2)*sinRot2+x2;\n    tmp_pos.z=(y1-y2)*cosRot2+(x1-x2)*sinRot2+y2;\n  }\n  else if(tmp_pos.x <= width){\n      float length = tmp_pos.x - cl - sl;\n      tmp_pos.x = posX2 + length*cosRot;\n      tmp_pos.z = posZ - length*sinRot;\n  }\n  if(rotation <= 0.1){\n      tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  }\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureFront;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureFront, v_uv0);\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nattribute vec3 a_position;\n#if USE_TEXTURE\n  attribute mediump vec2 a_uv0;\n  varying mediump vec2 v_uv0;\n#endif\nuniform ToonFrasr {\n  float ratio;\n  float radius;\n  float width;\n  float height;\n  float offx;\n  float offy;\n  float rotation;\n};\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  v_uv0 = vec2(1.0 - a_uv0.x,a_uv0.y);\n  vec4 tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  float cl = width/5.0;\n  float sl = (width - cl)/2.0;\n  float radii = (cl/rotation)/2.0;\n  float sinRot = sin(rotation);\n  float cosRot = cos(rotation);\n  float distance = radii*sinRot;\n  float centerX = width/2.0;\n  float posX1 = centerX - distance;\n  float posX2 = centerX + distance;\n  float posZ = sl*sinRot;\n  if(tmp_pos.x <= sl){\n    float length = sl - tmp_pos.x;\n    tmp_pos.x = posX1 - length*cosRot;\n    tmp_pos.z = posZ - length*sinRot;\n  }\n  else if(tmp_pos.x < (sl+cl)){\n    float el = tmp_pos.x - sl;\n    float rotation2 = -el/radii;\n    float x1 = posX1;\n    float y1 = posZ;\n    float x2 = centerX;\n    float y2 = posZ - radii*cosRot;\n    float sinRot2 = sin(rotation2);\n    float cosRot2 = cos(rotation2);\n    tmp_pos.x=(x1-x2)*cosRot2-(y1-y2)*sinRot2+x2;\n    tmp_pos.z=(y1-y2)*cosRot2+(x1-x2)*sinRot2+y2;\n  }\n  else if(tmp_pos.x <= width){\n      float length = tmp_pos.x - cl - sl;\n      tmp_pos.x = posX2 + length*cosRot;\n      tmp_pos.z = posZ - length*sinRot;\n  }\n  if(rotation <= 0.1){\n      tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  }\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureFront;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureFront, v_uv0);\n  gl_FragColor = color;\n}"}}], "subMetas": {}}