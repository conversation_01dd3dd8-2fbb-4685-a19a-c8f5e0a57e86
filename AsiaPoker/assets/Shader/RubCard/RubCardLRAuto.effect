// Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.  

// Note: Current format version is experiment, the format may be changed.
// The future format may not be compatible, you may need to update the script manually.

// 注意：当前版本的格式是实验性的，之后还会进行修改。
// 后续版本的格式不保证兼容当前格式，可能需要手动升级到最新版本。，
CCEffect %{
  techniques: 
    
    - passes: 
        - vert: vs
          frag: fsFront
          rasterizerState:
            cullMode: back
          blendState:
          targets:
          - blend: true
          depthStencilState:
            depthWrite: true
            depthTest: true
          layer: 0
          properties:
            textureFront: {value: default}
            alphaThreshold: {value: 0.5}
            ratio: {value: 0}
            radius: {value: 0}
            width: {value: 0}
            height: {value: 0}
            offx: {value: 0}
            offy: {value: 0}
            rotation: {value: 0}
}%

CCProgram vs %{

  precision highp float;

  
  #include <cc-local>
  #include <cc-global>

  attribute vec3 a_position;

  #if USE_TEXTURE
    attribute mediump vec2 a_uv0;
    varying mediump vec2 v_uv0;
  #endif

  uniform ToonFrasr {
    float ratio;
    float radius;
    float width;
    float height;
    float offx;
    float offy;
    float rotation;
  };

  void main () {
    mat4 mvp;
    
    #if USE_MODEL
      mvp = cc_matViewProj * cc_matWorld;
    #else
      mvp = cc_matViewProj;
    #endif

    v_uv0 = vec2(1.0 - a_uv0.x,a_uv0.y);

    vec4 tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);
    float cl = width/5.0;
    float sl = (width - cl)/2.0;
    float radii = (cl/rotation)/2.0;
    float sinRot = sin(rotation);
    float cosRot = cos(rotation);
    float distance = radii*sinRot;
    float centerX = width/2.0;
    float posX1 = centerX - distance;
    float posX2 = centerX + distance;
    float posZ = sl*sinRot;
    if(tmp_pos.x <= sl){
      float length = sl - tmp_pos.x;
      tmp_pos.x = posX1 - length*cosRot;
      tmp_pos.z = posZ - length*sinRot;
    }
    else if(tmp_pos.x < (sl+cl)){
      float el = tmp_pos.x - sl;
      float rotation2 = -el/radii;
      float x1 = posX1;
      float y1 = posZ;
      float x2 = centerX;
      float y2 = posZ - radii*cosRot;
      float sinRot2 = sin(rotation2);
      float cosRot2 = cos(rotation2);
      tmp_pos.x=(x1-x2)*cosRot2-(y1-y2)*sinRot2+x2;
      tmp_pos.z=(y1-y2)*cosRot2+(x1-x2)*sinRot2+y2;
    }
    else if(tmp_pos.x <= width){
        float length = tmp_pos.x - cl - sl;
        tmp_pos.x = posX2 + length*cosRot;
        tmp_pos.z = posZ - length*sinRot;
    }
    if(rotation <= 0.1){
        tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);
    }
    tmp_pos += vec4(offx, offy, 0.0, 0.0);
    gl_Position = mvp * tmp_pos;
  }

}%

CCProgram fsFront %{

  precision highp float;
  uniform sampler2D textureFront;
  varying mediump vec2 v_uv0;

  #include <alpha-test>

  void main () {
    vec4 color = vec4(1.);
    color *= texture2D(textureFront, v_uv0);
    gl_FragColor = color;
  }

}%
