{"ver": "1.0.27", "uuid": "675d9523-efdf-4552-804b-c4cbf6478a74", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matWorld;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\n#if USE_TEXTURE\nattribute mediump vec2 a_uv0;\nvarying mediump vec2 v_uv0;\n#endif\nuniform float ratio;\nuniform float radius;\nuniform float width;\nuniform float height;\nuniform float offx;\nuniform float offy;\nuniform float rotation;\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  #if USE_TEXTURE\n    v_uv0 = a_uv0;\n  #endif\n  vec4 tmp_pos = vec4(0.0, 0.0, 0.0, 0.0);\n  tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  float halfPeri = radius * 3.14159;\n  float hr = height * ratio;\n  if(tmp_pos.x < 0.0 || tmp_pos.x > width || tmp_pos.y < 0.0 || tmp_pos.y > height)\n  {\n      tmp_pos.x = 0.0;tmp_pos.y = 0.0;\n  }\n  if(hr > 0.0 && hr <= halfPeri){\n    if(tmp_pos.y < hr){\n       float rad = hr/ 3.14159;\n       float arc = (hr-tmp_pos.y)/rad;\n       tmp_pos.y = hr - sin(arc)*rad;\n       tmp_pos.z = rad * (1.0-cos(arc));\n    }\n  }\n  if(hr > halfPeri){\n    float straight = (hr - halfPeri)/2.0;\n    if(tmp_pos.y < straight){\n        tmp_pos.y = hr  - tmp_pos.y;\n        tmp_pos.z = radius * 2.0;\n    }\n    else if(tmp_pos.y < (straight + halfPeri)) {\n        float dy = halfPeri - (tmp_pos.y - straight);\n        float arc = dy/radius;\n        tmp_pos.y = hr - straight - sin(arc)*radius;\n        tmp_pos.z = radius * (1.0-cos(arc));\n    }\n  }\n  float y1 = tmp_pos.y;\n  float z1 = tmp_pos.z;\n  float y2 = height;\n  float z2 = 0.0;\n  float sinRat = sin(rotation);\n  float cosRat = cos(rotation);\n  tmp_pos.y=(y1-y2)*cosRat-(z1-z2)*sinRat+y2;\n  tmp_pos.z=(z1-z2)*cosRat+(y1-y2)*sinRat+z2;\n  tmp_pos.y = tmp_pos.y - height/2.0*(1.0-cosRat);\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureBack;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureBack, v_uv0);\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nattribute vec3 a_position;\n#if USE_TEXTURE\nattribute mediump vec2 a_uv0;\nvarying mediump vec2 v_uv0;\n#endif\nuniform ToonFra {\n    float ratio;\n    float radius;\n    float width;\n    float height;\n    float offx;\n    float offy;\n    float rotation;\n};\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  #if USE_TEXTURE\n    v_uv0 = a_uv0;\n  #endif\n  vec4 tmp_pos = vec4(0.0, 0.0, 0.0, 0.0);\n  tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  float halfPeri = radius * 3.14159;\n  float hr = height * ratio;\n  if(tmp_pos.x < 0.0 || tmp_pos.x > width || tmp_pos.y < 0.0 || tmp_pos.y > height)\n  {\n      tmp_pos.x = 0.0;tmp_pos.y = 0.0;\n  }\n  if(hr > 0.0 && hr <= halfPeri){\n    if(tmp_pos.y < hr){\n       float rad = hr/ 3.14159;\n       float arc = (hr-tmp_pos.y)/rad;\n       tmp_pos.y = hr - sin(arc)*rad;\n       tmp_pos.z = rad * (1.0-cos(arc));\n    }\n  }\n  if(hr > halfPeri){\n    float straight = (hr - halfPeri)/2.0;\n    if(tmp_pos.y < straight){\n        tmp_pos.y = hr  - tmp_pos.y;\n        tmp_pos.z = radius * 2.0;\n    }\n    else if(tmp_pos.y < (straight + halfPeri)) {\n        float dy = halfPeri - (tmp_pos.y - straight);\n        float arc = dy/radius;\n        tmp_pos.y = hr - straight - sin(arc)*radius;\n        tmp_pos.z = radius * (1.0-cos(arc));\n    }\n  }\n  float y1 = tmp_pos.y;\n  float z1 = tmp_pos.z;\n  float y2 = height;\n  float z2 = 0.0;\n  float sinRat = sin(rotation);\n  float cosRat = cos(rotation);\n  tmp_pos.y=(y1-y2)*cosRat-(z1-z2)*sinRat+y2;\n  tmp_pos.z=(z1-z2)*cosRat+(y1-y2)*sinRat+z2;\n  tmp_pos.y = tmp_pos.y - height/2.0*(1.0-cosRat);\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureBack;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureBack, v_uv0);\n  gl_FragColor = color;\n}"}}, {"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matWorld;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\n#if USE_TEXTURE\nattribute mediump vec2 a_uv0;\nvarying mediump vec2 v_uv0;\n#endif\nuniform float ratio;\nuniform float radius;\nuniform float width;\nuniform float height;\nuniform float offx;\nuniform float offy;\nuniform float rotation;\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  #if USE_TEXTURE\n    v_uv0 = a_uv0;\n  #endif\n  vec4 tmp_pos = vec4(0.0, 0.0, 0.0, 0.0);\n  tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  float halfPeri = radius * 3.14159;\n  float hr = height * ratio;\n  if(tmp_pos.x < 0.0 || tmp_pos.x > width || tmp_pos.y < 0.0 || tmp_pos.y > height)\n  {\n      tmp_pos.x = 0.0;tmp_pos.y = 0.0;\n  }\n  if(hr > 0.0 && hr <= halfPeri){\n    if(tmp_pos.y < hr){\n       float rad = hr/ 3.14159;\n       float arc = (hr-tmp_pos.y)/rad;\n       tmp_pos.y = hr - sin(arc)*rad;\n       tmp_pos.z = rad * (1.0-cos(arc));\n    }\n  }\n  if(hr > halfPeri){\n    float straight = (hr - halfPeri)/2.0;\n    if(tmp_pos.y < straight){\n        tmp_pos.y = hr  - tmp_pos.y;\n        tmp_pos.z = radius * 2.0;\n    }\n    else if(tmp_pos.y < (straight + halfPeri)) {\n        float dy = halfPeri - (tmp_pos.y - straight);\n        float arc = dy/radius;\n        tmp_pos.y = hr - straight - sin(arc)*radius;\n        tmp_pos.z = radius * (1.0-cos(arc));\n    }\n  }\n  float y1 = tmp_pos.y;\n  float z1 = tmp_pos.z;\n  float y2 = height;\n  float z2 = 0.0;\n  float sinRat = sin(rotation);\n  float cosRat = cos(rotation);\n  tmp_pos.y=(y1-y2)*cosRat-(z1-z2)*sinRat+y2;\n  tmp_pos.z=(z1-z2)*cosRat+(y1-y2)*sinRat+z2;\n  tmp_pos.y = tmp_pos.y - height/2.0*(1.0-cosRat);\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureFront;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureFront, v_uv0);\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nattribute vec3 a_position;\n#if USE_TEXTURE\nattribute mediump vec2 a_uv0;\nvarying mediump vec2 v_uv0;\n#endif\nuniform ToonFra {\n    float ratio;\n    float radius;\n    float width;\n    float height;\n    float offx;\n    float offy;\n    float rotation;\n};\nvoid main () {\n  mat4 mvp;\n  #if USE_MODEL\n    mvp = cc_matViewProj * cc_matWorld;\n  #else\n    mvp = cc_matViewProj;\n  #endif\n  #if USE_TEXTURE\n    v_uv0 = a_uv0;\n  #endif\n  vec4 tmp_pos = vec4(0.0, 0.0, 0.0, 0.0);\n  tmp_pos = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  float halfPeri = radius * 3.14159;\n  float hr = height * ratio;\n  if(tmp_pos.x < 0.0 || tmp_pos.x > width || tmp_pos.y < 0.0 || tmp_pos.y > height)\n  {\n      tmp_pos.x = 0.0;tmp_pos.y = 0.0;\n  }\n  if(hr > 0.0 && hr <= halfPeri){\n    if(tmp_pos.y < hr){\n       float rad = hr/ 3.14159;\n       float arc = (hr-tmp_pos.y)/rad;\n       tmp_pos.y = hr - sin(arc)*rad;\n       tmp_pos.z = rad * (1.0-cos(arc));\n    }\n  }\n  if(hr > halfPeri){\n    float straight = (hr - halfPeri)/2.0;\n    if(tmp_pos.y < straight){\n        tmp_pos.y = hr  - tmp_pos.y;\n        tmp_pos.z = radius * 2.0;\n    }\n    else if(tmp_pos.y < (straight + halfPeri)) {\n        float dy = halfPeri - (tmp_pos.y - straight);\n        float arc = dy/radius;\n        tmp_pos.y = hr - straight - sin(arc)*radius;\n        tmp_pos.z = radius * (1.0-cos(arc));\n    }\n  }\n  float y1 = tmp_pos.y;\n  float z1 = tmp_pos.z;\n  float y2 = height;\n  float z2 = 0.0;\n  float sinRat = sin(rotation);\n  float cosRat = cos(rotation);\n  tmp_pos.y=(y1-y2)*cosRat-(z1-z2)*sinRat+y2;\n  tmp_pos.z=(z1-z2)*cosRat+(y1-y2)*sinRat+z2;\n  tmp_pos.y = tmp_pos.y - height/2.0*(1.0-cosRat);\n  tmp_pos += vec4(offx, offy, 0.0, 0.0);\n  gl_Position = mvp * tmp_pos;\n}", "frag": "\nprecision highp float;\nuniform sampler2D textureFront;\nvarying mediump vec2 v_uv0;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid main () {\n  vec4 color = vec4(1.);\n  color *= texture2D(textureFront, v_uv0);\n  gl_FragColor = color;\n}"}}], "subMetas": {}}