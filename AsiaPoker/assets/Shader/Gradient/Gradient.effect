CCEffect %{
  techniques:
  - passes:
    - name: gradient
      vert: vs-gradient
      frag: fs-gradient
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        texture:        { value: white }
        alphaThreshold: { value: 0.5 }
        nodeSize:       { value: [1, 1], editor: { tooltip: "Node size" } }
        nodeOffset:     { value: [1, 1], editor: { tooltip: "Node offset from left bottom corner" } }
        gradientTex:    { value: white }
        startColor:     { value: [1, 1, 1, 1], editor: { type: color, tooltip: "Start color of the gradient" } }
        middleColor:    { value: [1, 1, 0, 1], editor: { type: color, tooltip: "Middle color of the gradient" } }
        endColor:       { value: [1, 0, 0, 1], editor: { type: color, tooltip: "End color of the gradient" } }
        startPos:       { value: [0, 0.5], editor: { tooltip: "Start position of the gradient, used to calculate gradient direction, left top point is 0 0" } }
        endPos:         { value: [1, 0.5], editor: { tooltip: "End position of the gradient, used to calculate gradient direction, right bottom point is 1 1" } }
        middlePos:      { value: 0.5, editor: { tooltip: "Position of middle color on gradient direction (between 0 and 1)" } }
}%


CCProgram vs-gradient %{
    precision highp float;

    #include <cc-global>
    #include <cc-local>

    in vec3 a_position;
    in vec4 a_color;
    out vec4 v_color;

    in vec2 a_uv0;
    out vec2 v_uv0;
    out vec2 v_relativePos;

    uniform ConstantVS1 {
        vec2 nodeSize;
        vec2 nodeOffset;
    };

    #if USE_GRADIENT_COLORS
        uniform ConstantVS2 {
            vec2 startPos;
            vec2 endPos;
        };
        out float lerpt;
    #endif

    void main () {
        vec4 pos = vec4(a_position, 1.0);
        
        #if CC_USE_MODEL
            pos = cc_matViewProj * cc_matWorld * pos;
        #else
            pos = cc_matViewProj * pos;
        #endif

        v_uv0 = a_uv0;
        v_relativePos = vec2((a_position.x - nodeOffset.x) / nodeSize.x, (a_position.y - nodeOffset.y) / nodeSize.y);

        #if USE_GRADIENT_COLORS
            vec2 direction = endPos - startPos;
            float lenSq = dot(direction, direction);          // len(direction)^2 
            vec2 relativeCoord = v_relativePos - startPos;
            lerpt = dot(relativeCoord, direction) / lenSq;
        #endif

        v_color = a_color;
        gl_Position = pos;
    }
}%


CCProgram fs-gradient %{
    precision mediump float;
  
    #include <alpha-test>
    #include <texture>

    in vec4 v_color;
    in vec2 v_uv0;
    in vec2 v_relativePos;

    uniform sampler2D texture;

    #if USE_GRADIENT_TEXTURE
        uniform sampler2D gradientTex;
    #elif USE_GRADIENT_COLORS
        in float lerpt;
        uniform ConstantFS1 {
            vec4 startColor;
            vec4 endColor;
        };
        #if USE_EXTRA_COLOR
            uniform ConstantFS2 {
                vec4 middleColor;
                float middlePos;
            };
        #endif
    #endif

    void main () {
        vec4 o = vec4(1, 1, 1, 1);
        CCTexture(texture, v_uv0, o);
        o *= v_color;
        
        #if USE_GRADIENT_TEXTURE
            o *= texture2D(gradientTex, v_relativePos);
        #elif USE_GRADIENT_COLORS
            // mix = lerp
            #if USE_EXTRA_COLOR
                o *= mix(mix(startColor, middleColor, lerpt/middlePos), mix(middleColor, endColor, (lerpt - middlePos)/(1.0 - middlePos)), step(middlePos, lerpt));
            #else
                o *= mix(startColor, endColor, lerpt);
            #endif
        #endif
        
        ALPHA_TEST(o);
        
        #if USE_BGRA
            gl_FragColor = o.bgra;
        #else
            gl_FragColor = o.rgba;
        #endif
    }
}%
