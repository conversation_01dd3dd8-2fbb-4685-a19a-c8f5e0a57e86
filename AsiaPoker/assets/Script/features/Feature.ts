// eslint-disable-next-line max-classes-per-file
import {FeatureManager} from "./FeatureManager";
import { CashGameFeatureCreator } from "./cashGames/CashGameFeature";

export enum BaseGameType {
    CashGame,
    None
}

function getFeatureKey(baseGameType: BaseGameType, featureId: number) {
    return `${baseGameType}-${featureId}`;
}

/**
 * Decorator for the class, which is the main class for new feature
 */
export function registerCashGameFeatrueData(target, key) {
    const valueKey = `__${key}`;
    Object.defineProperty(target, key, {
        get() {
            return this[valueKey];
        },
        set(prefab) {
            if (prefab instanceof cc.Prefab) {
                const featureManager : FeatureManager = cc.instantiate(prefab).getComponent(FeatureManager); // Instantiate the prefab and store the node
                featureManager.featureWithId.forEach(item => {
                    FeatureCreator.gameFeatureMap.set(getFeatureKey(BaseGameType.CashGame, CashGameFeatureCreator.getFeatureType(item.id)), item);
                })
                this[valueKey] = prefab;
            } else {
                this[valueKey] = prefab;
            }
        },
        enumerable: true,
        configurable: true,
    });
}
 
/**
 * Base class for main class of feature
 */
export abstract class FeatureBase extends cc.Component {
    abstract getFeatureData();
    emit(msg: string, param?: any) {
        if (cc.isValid(this.node)) {
            this.node.emit(msg, param);
        }
    }
}

export class FeatureCreator {
    static featureDataMap:Map<string, any> = new Map();
    static gameFeatureMap:Map<string, any> = new Map();

    protected static addFeatureToNode(baseGameType: BaseGameType, featureId: number, node: cc.Node) {
        const featureKey = getFeatureKey(baseGameType, featureId);
        if (!FeatureCreator.gameFeatureMap.has(featureKey) || !cc.isValid(node)) {
            return null;
        }

        const tempFeature:FeatureBase = FeatureCreator.gameFeatureMap.get(featureKey).gameFeature;
        const instancePrototype = Object.getPrototypeOf(tempFeature);
        const constructorFunction = instancePrototype.constructor;

        const feature: FeatureBase = node.addComponent(constructorFunction);

        // const feature: FeatureBase = node.addComponent(FeatureCreator.gameFeatureMap.get(featureKey).gameFeature);
        // Add feature Data getter
        FeatureCreator.featureDataMap.set(featureKey, feature.getFeatureData.bind(feature))
        return feature;
    }

    static getFeaturePrefebs(baseGameType: number,id: number) {
        return FeatureCreator.gameFeatureMap.get(getFeatureKey(baseGameType,id)).groupedPrefabs;
    }

    protected static getFeatureData(featureId: number, baseGameType: BaseGameType ) {
        const featureKey = getFeatureKey(baseGameType, featureId);
        const featureGetter = FeatureCreator.featureDataMap.get(featureKey);
        if(!featureGetter || !(featureGetter instanceof Function)) {
            cc.warn(`No valid feature data getter added for feature game type ${baseGameType} and feature id ${featureId}`);
            return null;
        }
        return featureGetter();
    }

    protected static removeFeatureDataGetter (baseGameType: BaseGameType, featureId: number) {
        const featureKey = getFeatureKey(baseGameType, featureId);
        FeatureCreator.featureDataMap.delete(featureKey);   
    }
}