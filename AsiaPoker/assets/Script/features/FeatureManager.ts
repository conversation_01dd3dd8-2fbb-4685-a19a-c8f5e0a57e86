import { SquidFeature } from "./cashGames/squid/scripts/SquidFeature";

export interface iFeature{
    gameFeature: any;
    groupedPrefabs: any;
    id: number;
}

const { ccclass, property } = cc._decorator;

@ccclass('FeatureManager')
export class FeatureManager extends cc.Component {

    @property(SquidFeature)
    squidFeature: iFeature = new SquidFeature();

    get featureWithId() {
        return [this.squidFeature]; // Add all feature within this array;
    }

}