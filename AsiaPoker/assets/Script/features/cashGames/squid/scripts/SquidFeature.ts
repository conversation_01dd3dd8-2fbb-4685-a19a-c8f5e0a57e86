// eslint-disable-next-line max-classes-per-file
import { FeatureBase } from "assets/Script/features/Feature";
import { GameId } from "../../../../../Script/common/tools/Enum";
import { iFeature } from "../../../FeatureManager";

const { ccclass, property } = cc._decorator;

// Defining custom class for grouping prefabs
@ccclass('SquidPrefabs')
export class SquidPrefabs {
    @property(cc.Prefab)
    start_popup: cc.Prefab = null;

    @property(cc.Prefab)
    gameEffects: cc.Prefab = null;

    @property(cc.Prefab)
    playerSeat: cc.Prefab = null;

    @property(cc.Prefab)
    gameMessages: cc.Prefab = null;

    @property(cc.Prefab)
    gameStatus: cc.Prefab = null;
}

@ccclass('SquidFeature')
export class SquidFeature implements iFeature {

    @property({type: cc.Enum(GameId)}) id: GameId = GameId.Squid;

    @property(SquidPrefabs)
    groupedPrefabs: SquidPrefabs = new SquidPrefabs(); // Reference the group

    @property(cc.Component)
    gameFeature: FeatureBase = null;
}