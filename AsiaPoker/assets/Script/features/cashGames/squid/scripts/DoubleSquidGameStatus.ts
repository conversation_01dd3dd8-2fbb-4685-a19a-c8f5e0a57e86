import cv from "../../../../components/lobby/cv";
import SquidGame from "./SquidGame";
import SquidStatusPlayerInfo from "./SquidStatusPlayerInfo";
import DoubleSquidRules from "./DoubleSquidRules";

const { ccclass, property } = cc._decorator;

const Viewtype = {
    Status: "Status",
    Rules: "Rules"
}

const TextColor = {
    unseleted: new cc.Color(148, 149, 149),
    selected: new cc.Color(251, 216, 136)
}

@ccclass
export default class DoubleSquidGameStatus extends cc.Component {

    @property(cc.Node) mask_Content: cc.Node = null;
    @property(cc.Label) RoundCount: cc.Label = null;
    @property(cc.Label) label_SquidStatus: cc.Label = null;
    // Squid settlement
    @property(cc.Node) playerInfoTemplate: cc.Node = null;
    @property(cc.Node) listInfoContent: cc.Node = null;
    @property(cc.RichText) ruleTextStart:cc.RichText=null;
    @property(cc.Node) rulesBtn: cc.Node = null;
    @property(cc.Node) statusBtn: cc.Node = null;
    @property(cc.Node) seletor: cc.Node = null;
    @property(cc.Node) statusPopup:  cc.Node = null;
    @property(DoubleSquidRules) doubleSquidRules:DoubleSquidRules = null;
    @property(cc.Node) gameStartWithoutStatusDataPopup: cc.Node = null;
    @property(cc.Label) startConditionMsg: cc.Label = null;

    private _squidGame: SquidGame = null;
    private viewType:string = Viewtype.Status;

    public init(squidGame: SquidGame):void {
        this._squidGame = squidGame;
        this.doubleSquidRules.initialise(squidGame);
    }

    public show(isShowRules=false) {

        if(!this.node.active){
            this.node.active = true;
            cc.tween(this.mask_Content)
                .set({
                    opacity: 0,
                    scale: 0.95
                })
                .to(0.15, {
                    scale: 1,
                    opacity: 255
                })
                .start();
        }else if(this.listInfoContent.childrenCount > 0)
            this.listInfoContent.destroyAllChildren();

        const squidHuntGamePlayers = this._squidGame.getSquidHuntGameInfo()?.squidHuntGamePlayers;
        squidHuntGamePlayers?.sort(this.sortPlayersOrder.bind(this));
        squidHuntGamePlayers?.forEach(player => {
            this._addPlayerInfo(player);
        });
        if(isShowRules)
            this.viewType=Viewtype.Rules;
        else
            this.viewType=Viewtype.Status;
        this.toggleView(this.viewType);
        this.startConditionMsg.string = cv.StringTools.formatC(cv.config.getStringData("squid_Start_When_player_join"), this._squidGame.getSquidHuntGameParams().minPlayersToStartSquidHunt);

        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            this.label_SquidStatus.string = cv.config.getStringData(this._squidGame.isDoubleSquidMode() ? "Double_squid_title" : "Squid_hunt_title");
        }
        else this.label_SquidStatus.string = cv.config.getStringData("Squid_Status");

    }

    public hide() {
        if(this.listInfoContent.childrenCount > 0)
            this.listInfoContent.destroyAllChildren();

        this.node.active = false;
    }

    private _addPlayerInfo(playerInfo) {
        const playerNode:SquidStatusPlayerInfo = cc.instantiate(this.playerInfoTemplate).getComponent(SquidStatusPlayerInfo);
        playerNode.node.active = true;
        playerNode.addPlayerInfo(playerInfo,this._squidGame);
        this.listInfoContent.addChild(playerNode.node);
    }

    sortPlayersOrder(a: any, b: any): number {
        if (a.totalSquidsWon !== b.totalSquidsWon) {
            return a.totalSquidsWon < b.totalSquidsWon ? 1 : -1;
        }
        return (a.isAway) ? 0 : -1;
    }

    onBtnsClick (event:cc.Event, type:string){
        this.toggleView(type);
    }


    toggleView(type) {
        const isStatus = type === Viewtype.Status;
        this.seletor.position = new cc.Vec2(isStatus?-this.seletor.width : 0, this.seletor.y);
        if(!isStatus) {
            this.doubleSquidRules.show();
        }else{
            this.doubleSquidRules.hide();
        }
        if(this._squidGame?.getSquidGameData()?.isSquidRunning()){
            this.statusPopup.active = isStatus;
            this.gameStartWithoutStatusDataPopup.active= false;
            this.RoundCount.string=this._squidGame.getSquidGameData().remainingSquid+ "/"+this._squidGame.getSquidHuntGameInfo().totalSquidCounts;
        }
        else{
            this.gameStartWithoutStatusDataPopup.active= isStatus;
            this.statusPopup.active = false;
           
            const entries = Object.entries(this._squidGame.getSquidHuntGameParams()?.squidValue);
            this.ruleTextStart.string=cv.StringTools.formatC(cv.config.getStringData("Squid_game_Rule_start"), cv.StringTools.clientGoldByServer(entries[0][1]))+this._squidGame.getCurrencyString();
            if(this._squidGame.getSquidHuntGameParams().isFirstRoundDoubleSquid)
                this.ruleTextStart.string+=cv.config.getStringData("DoubleSquid_game_Rule_start");
            if(this._squidGame.isDoubleSquidMode()) {
                const multiplerData = this._squidGame.getSquidHuntGameParams()?.squidMultiplier;
                this.doubleSquidRules?.squidMultiplier?.forEach((range)=>{
                    if(range.multiplier !== 1 && multiplerData[range.min].isDisplay ) {
                        this.ruleTextStart.string+=cv.StringTools.formatC(cv.config.getStringData("DoubleSquid_game_Rule_start_extend"), range.min, range.multiplier);
                    }
                });
            }
               
        }
        this.statusBtn.getComponent(cc.Label).enableBold = isStatus;
        this.rulesBtn.getComponent(cc.Label).enableBold = !isStatus;
        this.rulesBtn.color = isStatus ? TextColor.unseleted : TextColor.selected;
        this.statusBtn.color = isStatus ? TextColor.selected : TextColor.unseleted;
    }
}
