import cv from "../../../../components/lobby/cv";

const {ccclass, property} = cc._decorator;

@ccclass
export default class SquidGracePeriod extends cc.Component {

    @property(cc.Label) remaining_timer_sec: cc.Label = null;
    @property(cc.Label) label_Description: cc.Label = null;
    private _squid_grace_period: number = null;

    showCountDown (seconds: number, message: string = "") {
        this.unscheduleAllCallbacks();
        if(seconds <= 0 || isNaN(seconds)) return;
        this.node.active = true;
        this._squid_grace_period = seconds;
        
        this.label_Description.string = message;
        this.label_Description.lineHeight = cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? 50 : 36;

        // Update the countdown immediately to reflect the initial time
        this.updateCountdown();
    
        // Schedule the countdown updates
        this.schedule(this.updateCountdown.bind(this), 1, seconds - 1, 1);
        
    }

    private updateCountdown(): void {
        // Decrement the grace period time
        this._squid_grace_period--;
    
        // Update the display
        this.remaining_timer_sec.string = this.formatTime(this._squid_grace_period); 
    
        // Stop the countdown if the grace period reaches zero
        if (this._squid_grace_period <= 0) {
            this.unscheduleAllCallbacks();
            this.removeTimer();
        }
    }

    private formatTime(totalSeconds: number): string {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
    
        return `${this.padTime(hours)}:${this.padTime(minutes)}:${this.padTime(seconds)}`;
    }

    private padTime(value: number): string {
        return value.toString().padStart(2, "0");
    }

    removeTimer () {
        this.unscheduleAllCallbacks();
        this.node.active = false;
    }
}
