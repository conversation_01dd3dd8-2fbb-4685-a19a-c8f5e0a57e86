import SquidCounter from "../SquidCounter";
import SquidPlayerSeat from "../SquidPlayerSeat";
import { Ease } from "./Ease";
import SquidGameEffect from "./SquidGameEffect";
import cv from "../../../../../components/lobby/cv";

const { ccclass, property } = cc._decorator;


const MINI_SQUID_SCALE = 1;

@ccclass
export default class SquidGameSeatEffect extends cc.Component {
    
    @property(cc.Node) miniSquid_Hooked: cc.Node = null;
    private _squidSeat: SquidPlayerSeat = null;
    private _squidCounter: SquidCounter = null;
    private _gameEffect: SquidGameEffect = null;

    private getDefaultPosOnSeat() {
        return cc.v3(0, 70, 0); // cc.v3(this.isFlipX() ? 10 : -10, 34, 0);
    }

    private getDefaultAngleOnSeat() {
        return this.isFlipX() ? 26 : -26;
    }
    
    public init(squidEffect: SquidGameEffect) {
        this._squidCounter = squidEffect.squidAnim.squidCounter;
        this._squidSeat =  this.node.getComponent(SquidPlayerSeat);
        this._gameEffect = squidEffect;
        const isFlipX = this.isFlipX();
        const scale = MINI_SQUID_SCALE;
        this.miniSquid_Hooked.scaleY = scale;
        this.miniSquid_Hooked.scaleX = isFlipX ? -scale : scale;
        // this._squidSeat.getFishingRod()?.init(this._animPanel);
    }

    public showSquid() {
        if (this._squidSeat) {
            this.miniSquid_Hooked.active = true;
        }
    }

    private updateSprite(_isGoldenSquid: boolean) {
        this.miniSquid_Hooked.getComponent(cc.Sprite).spriteFrame = _isGoldenSquid ? this._squidSeat?.miniSquid_SuperSquid : this._squidSeat?.miniSquid_Standard;
    }

    public playHookedAnim(goldenSquid : boolean = false) { // goldenSquid = Either "Super squid" or 'First Round Double Squid'
        if (!this._squidSeat) return;
        this.show();
        this.updateSprite(goldenSquid);
        const startWorldPos = this._squidCounter.node.parent.convertToWorldSpaceAR(this._squidCounter.node.position);
        const startPos = this.miniSquid_Hooked.parent.convertToNodeSpaceAR(startWorldPos);
        let seatCountHolder = this._squidSeat.countHolder; // Cirle which holds squid count label 
        if (this._squidSeat.currSquidCount < 1) {
            seatCountHolder = this._squidSeat.squid_Hat;
            seatCountHolder.scale = 0;
         } else {
            seatCountHolder.parent.scale = 1;
         }
        const targetNode = this._squidSeat.node;

        const endWorldPos = targetNode.convertToWorldSpaceAR(this.getDefaultPosOnSeat());
        const endPos = this.miniSquid_Hooked.parent.convertToNodeSpaceAR(endWorldPos);

        // Calculate the direction from startPos to endPos
        const direction = endPos.sub(cc.v3(startPos.x, startPos.y, 0)).normalize();
        const endPos1 = endPos.add(direction.mul(-200));

        const isFlip = this.isFlipX();
        const _scaleY = 1;
        const _scaleX = isFlip ? _scaleY : - _scaleY;
        const totalSpeed = 30;


        cc.tween(this.miniSquid_Hooked)
            .set({
                active: true,
                opacity: 0,
                angle: this.getDefaultAngleOnSeat(),
                scaleX: _scaleX,
                scaleY: _scaleY,
                position: startPos
            })
            .show() // could be hiden after squid swim to the seat-counter
            .parallel(
                cc.tween().to(SquidGameEffect.toTweenTime(1 / totalSpeed), { opacity: 255 }),
                cc.tween().to(SquidGameEffect.toTweenTime(8 / totalSpeed), {
                    position: endPos1,
                    scaleX: _scaleX,
                    scaleY: _scaleY
                }),
                cc.tween().delay(SquidGameEffect.toTweenTime(8 / totalSpeed)) // Delay to ensure it starts after the previous tween
                    .to(SquidGameEffect.toTweenTime(1 / totalSpeed), {
                        position: endPos,
                        scaleX: 0.2,
                        scaleY: 0.2
                    }).hide()
            )
            .call(() => {
                // add to the seat
                this.miniSquid_Hooked.setPosition(this.getDefaultPosOnSeat());
                this.miniSquid_Hooked.active = false;
            }).
            call(()=>{
                const squidData = this._gameEffect.squidGame.getSquidGameData();
                if(!squidData.isDoubleSquid) return;
                const _2xMultiplierMin  = squidData.squidMultiplier.find(range=>range.multiplier === 2)?.min;
                const _4xMultiplierMin = squidData.squidMultiplier.find(range=>range.multiplier === 4)?.min;
                if(Number(this._squidSeat.counterLabel.string) < _2xMultiplierMin && this._squidSeat.currSquidCount >= _2xMultiplierMin){
                    this.playDoubleSquid(false);
                }else if (Number(this._squidSeat.counterLabel.string) < _4xMultiplierMin && this._squidSeat.currSquidCount >= _4xMultiplierMin){
                    this.playDoubleSquid(true);
                }else{
                    this._squidSeat.countHolder.getComponent(cc.Sprite).spriteFrame = this._squidSeat.counterBgFrames[this._squidSeat.currSquidCount >= _2xMultiplierMin ? 1: 0];
                }
            })
            .start();
        if (goldenSquid) {
            this.scheduleOnce(this.playShowGoldHatSS, SquidGameEffect.toTweenTime(10 / 30));
            this.scheduleOnce(this._squidSeat.updateCounterForSuperSquid.bind(this._squidSeat), 2.0);

        }
        else {
            cc.tween(seatCountHolder)
                .set({
                    active: this._squidSeat.currSquidCount > 1, // Only show in starting if count is greater than 2.
                })
                .delay(SquidGameEffect.toTweenTime(10 / 30)) // Delay to ensure it starts after Squid travels to the seat
                .to(SquidGameEffect.toTweenTime(3 / 30), {
                    scaleX: 1.2,
                    scaleY: 1.2,
                })
                .call(() => {
                    targetNode.active = true;
                    this._squidSeat.setSquidCount()
                })
                .to(SquidGameEffect.toTweenTime(2 / 30), {
                    scaleX: 1,
                    scaleY: 1,
                    active: true, 
                })
                .start();
        }
    }
    
    playDoubleSquid(isDoubleAgain: boolean) {
        this._squidSeat.countHolder.getComponent(cc.Sprite).spriteFrame = this._squidSeat.counterBgFrames[1];
        this._squidSeat.doubleSquidNode.active = false;
        const anim = this._squidSeat.getComponent(cc.Animation);
        anim.play("DoublingDisplay");
        anim.on('finished', ()=>{
            this._squidSeat.doubleSquidNode.active = false;
        })
        this._squidSeat.doubleParticle.resetSystem();
        const squidData = this._gameEffect.squidGame.getSquidGameData();
        const isSelf = this._squidSeat?.seat?.PlayerInfo?.playerid === cv.dataHandler.getUserData().u32Uid; // doubling effect sound for self only 
        if(!isSelf || squidData?.looser.length === 1 || squidData?.remainingSquid === 0 || !this._gameEffect.squidCounter?.node.active) return;
        this._gameEffect.squidSound.playDoubleSound(isDoubleAgain); // no audio on game end.
        
    }

    public playShowGoldHatSS() {
        this._squidSeat.node.getComponent(cc.Animation).play("Show_Hat_Gold");
        // Wait for 0.75 seconds before proceeding to the next actions
        this.scheduleOnce(() => {
            this.updateSprite(false); // Revert to the normal sprite
            this._squidCounter.sparkle_Particle.active = false; // Deactivate the sparkle particle effect
        }, 0.75);
       
    }

    // swim to big squid (losing effect)
    public playSwimToBigSquidAnim() {
        this.show();
        const bigSquidPosition =  cc.v2(cc.winSize.width / 2, cc.winSize.height * 0.6);
        const convertToTweenTime = SquidGameEffect.toTweenTime;
        const squid_Hat = this._squidSeat.squid_Hat;
        const bigSquidPos = squid_Hat.parent.convertToNodeSpaceAR(bigSquidPosition);
        const endPos = cc.v3(bigSquidPos.x, bigSquidPos.y, 0);
        const moveToBigSquid = cc.tween(squid_Hat)
            .to(convertToTweenTime(0.33), { position: endPos, scale: 0 }, { easing: Ease.cubicInOut })
            .call(this.hideSquid.bind(this));

        moveToBigSquid.start();
    }

    private show() {
        this.node.active = true;
    }

    private isFlipX(): boolean {
        const targetSeat = this._squidSeat
        if (targetSeat && cc.isValid(targetSeat)) {
            return targetSeat.node.convertToWorldSpaceAR(cc.Vec3.ZERO).x < cc.winSize.width / 2 + 20;
        }

        return false;
    }

    public belongsToSeat(seatNode: cc.Node) {
        return this.node.parent === seatNode;
    }

    public hide() {
        this.node.active = false;
    }

    public hideSquid() {
        this.miniSquid_Hooked.active = false;
        //  this.resetSpine();
    }


}
