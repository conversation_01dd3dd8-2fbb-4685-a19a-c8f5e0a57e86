const { ccclass, property } = cc._decorator;

@ccclass
export default class FishingRod extends cc.Component {

    @property(cc.Animation) fishingRodAnim: cc.Animation = null;

    public playThrowAnim(targetWorldPos: cc.Vec2) {
        this.unscheduleAllCallbacks();
        this.node.active = true;
      
        // calculate angle
        const squidCounterPos = this.node.parent.convertToNodeSpaceAR(targetWorldPos);
        const angle1 = cc.v3(0, -1, 0).signAngle(this.node.position.add(squidCounterPos.mul(-1))) * 180 / Math.PI;
        this.node.angle = angle1;

        // calculate distance
        const distance = this.node.position.sub(squidCounterPos).mag();
        this.node.scaleY = distance / this.node.getContentSize().height;

        this.fishingRodAnim.play('FishingRod_Seq');

        this.scheduleOnce(() => {
            this.node.active = false;
        }, 1.1);
    }
}
