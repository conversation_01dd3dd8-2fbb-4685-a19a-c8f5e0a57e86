
const { ccclass, property } = cc._decorator;

@ccclass
export default class SquidGameAnimShadow extends cc.Component {

    @property(cc.Node)
    shadow_BG: cc.Node = null;

    @property(cc.Node)
    shadow: cc.Node = null;

    @property(cc.Animation)
    animation: cc.Animation = null;

    public playGameStartShadowAnim(isSuperSquid : boolean =false ) {

        // Activate the node to make it visible or interactable.
        this.node.active = true;
        this.shadow.active = !isSuperSquid;
        // Play the "ShowDarkTint_GameStart" animation and get its duration.
        const tintAnimationDuration = this.animation.play("ShowDarkTint_GameStart").duration;

        // Define a buffer time to add to the duration.
        const bufferTime = 0.5;

        // Schedule the hide function to be called after the animation and buffer time have elapsed.
        this.scheduleOnce(this.hide, tintAnimationDuration + bufferTime);
    }

    public playGameEndShadowAnim() {

        // Activate the node to make it visible or interactable.
        this.node.active = true;

        // Play the "ShowDarkTint_GameStart" animation and get its duration.
        const tintAnimationDuration = this.animation.play("ShowDarkTint_GameEnd").duration;

        // Define a buffer time to add to the duration.
        const bufferTime = 0.5;

        // Schedule the hide function to be called after the animation and buffer time have elapsed.
        this.scheduleOnce(this.hide, tintAnimationDuration + bufferTime);

    }

    public playHideSettlement () {
         // Activate the node to make it visible or interactable.
         this.node.active = true;

         // Play the "ShowDarkTint_GameStart" animation and get its duration.
         const tintAnimationDuration = this.animation.play("HideDarkTint_Settlement").duration;
 
         // Define a buffer time to add to the duration.
         const bufferTime = 0.5;
 
         // Schedule the hide function to be called after the animation and buffer time have elapsed.
         this.scheduleOnce(this.hide, tintAnimationDuration + bufferTime);
    }

    public hide() {
        this.node.active = false;
    }
}
