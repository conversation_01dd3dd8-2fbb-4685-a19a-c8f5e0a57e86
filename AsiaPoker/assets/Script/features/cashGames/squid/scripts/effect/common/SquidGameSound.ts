import { LANGUAGE_TYPE } from "./../../../../../../common/tools/Enum";
import cv from "../../../../../../components/lobby/cv";

const { ccclass } = cc._decorator;



@ccclass
export default class SquidGameSound extends cc.Component {
    private _soundBasePath = "zh_CN/game/dzpoker/audio/squid_sounds/"

    public play_GameStartSound() {
        this.playClip(this._soundBasePath + "SG_GameStart");
    }

    public play_SuperSquidSound() {
        this.playClip(this._soundBasePath + "SuperSquid");
    }

    public play_GameEndSound() {
        this.playClip(this._soundBasePath + "SG_GameEnd");
    }

    public play_FishingSound() {
        this.playClip(this._soundBasePath + "SG_FishingRod");
    }

    
    public play_LastSquidVoiceOver() {
        this.playMusic(this._soundBasePath + (cv.config.getCurrentLanguageV2() === LANGUAGE_TYPE.zh_CN ? "OneSquidLeft_CN": "OneSquidLeft_EN"));
    }

    public play_LastSquidBG() {
        this.playClip(this._soundBasePath + "LastSquid");
    }

    public playDoubleSound (isDoubleAgain: boolean) {
        this.playMusic(this._soundBasePath + (isDoubleAgain ?  "Rewards_doubled_again_" : "Rewards_doubled_") + (cv.config.getCurrentLanguageV2() === LANGUAGE_TYPE.zh_CN ? "CN": "EN"));
    }

    private playClip(url: string) {
        if (cv.tools.isSoundEffectOpen()) {
            cv.AudioMgr.playEffect(url);
        }
    }

    private playMusic (url: string) {
        if (cv.tools.isSoundEffectOpen()) {
            cv.AudioMgr.playMusic(url);
        }
    }
}
