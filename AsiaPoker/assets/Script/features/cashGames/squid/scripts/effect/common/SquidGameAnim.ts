import cv from "../../../../../../components/lobby/cv";
import SquidCounter from "../../SquidCounter";
import SquidPlayerSeat from "../../SquidPlayerSeat";
import SquidGameEffect from "../SquidGameEffect";
import SquidGame from "../../SquidGame";
import SquidGameAnimShadow from "../SquidGameAnimShadow";


const { ccclass, property } = cc._decorator;

// Define a custom class for the frame
@ccclass
export class TextFrames {
    @property(cc.SpriteFrame) yn_TH: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) zh_CN: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) en_US: cc.SpriteFrame = null;
}

@ccclass
export default class SquidGameAnim extends cc.Component {

    @property(cc.Node) bigSquidContainer: cc.Node = null;
    @property(cc.Node) miniSquidsContainer: cc.Node = null;
    @property({ type: sp.Skeleton }) bigSquid: sp.Skeleton = null;
    @property({ type: sp.Skeleton }) big_SuperSquid: sp.Skeleton = null;
    @property(SquidCounter) squidCounter: SquidCounter = null;
    @property(SquidGameAnimShadow) squidAnimShadow: SquidGameAnimShadow = null;
    @property(cc.Node) superSquid: cc.Node = null;
    @property(cc.SpriteFrame) miniSquid_Standard: cc.SpriteFrame = null;  // For the hooked mini squid. Standard (Normal)
    @property(cc.SpriteFrame) miniSquid_SuperSquid: cc.SpriteFrame = null; // or the hooked mini squid. Gold (SS)
    @property(cc.Node) LastSquidAnim: cc.Node = null;
    @property({ type: sp.Skeleton }) SpineLastSquid: sp.Skeleton = null!;
    @property(cc.Sprite) lastSquidTextSprite: cc.Sprite = null;
    @property(cc.Node) WayPointsLastSquid: cc.Node = null;
    @property(TextFrames) squidFames: TextFrames = new TextFrames();

    private miniSquid: cc.Node[] = [null];
    private WayPoints: cc.Node[] = [null]; // Used for both normal and SS

    public squidGame: SquidGame = null;
    private _needToshowLastSquid: boolean = true;

    init (squidGame: SquidGame) {
        this.squidGame = squidGame;
        this.squidAnimShadow.hide();
        this.miniSquid  = this.miniSquidsContainer.children.slice(0,9);
        this.WayPoints  = this.miniSquidsContainer.children.slice(9);
        const lang = cv.config.getCurrentLanguageV2();
        this.lastSquidTextSprite.spriteFrame = this.squidFames[lang];
    }

    get winnningPlayerSeat() : SquidPlayerSeat{
        const winnerId = this.squidGame.getSquidGameSettlementData()?.whoGetSquid;
        const seat = this.squidGame.squidSeats[winnerId]; 
        return seat || null;
    }

    public playFirstRoundAnim(_isSuperSquid: boolean = false) {
        this._needToshowLastSquid = true;
        cc.tween(this.node)
            .call(() => {
                if (_isSuperSquid) { this.playSuperSquidFirstRoundAnim() }
                else {
                    this.playBigSquidFirstRoundAnim();
                }
            })
            .call(() => { this.initSquidAnim() })
            .call(() => { SquidGameEffect.resetTimeScale(); })
            .start();
    }

    public playFishingAnim() {
        const isFirstRoundDoubleSquid = this.squidGame?.getSquidGameData()?.isFirstRoundDoubleSquid;
        cc.tween(this.node)
            .parallel(
                cc.tween().call(() => { this.playThrowingRodAnim() }),
                cc.tween().call(() => { this.playCounterShakeAnim() }),
                cc.tween().delay(0.5).call(()=>{
                    this.squidCounter.squid_Sprite.spriteFrame = this.miniSquid_Standard;
                    this.squidCounter.sparkle_Particle.active = false;
                    this.squidCounter.show();
                }),
                cc.tween().delay(SquidGameEffect.toTweenTime(0.7)).call(() => {
                    this.playMiniSquidsHookedAnim(isFirstRoundDoubleSquid);
                })
            )
            .delay(2.5).call(() => {
                SquidGameEffect.resetTimeScale();
            })
            .start();
    }

    checkForLastRemainingSquid() {
        let squidData = this.squidGame.getSquidGameData();
        if(!this._needToshowLastSquid || !this.squidCounter.node.active) return;
        if (squidData.remainingSquid == 1) {
                this.playLastSquidAnim();
        }
    }

    

    public playCounterShakeAnim()
    {
        const squidSprite = this.squidCounter.squid_Sprite.node;
        cc.tween(squidSprite)
            .set({
                active: true,
                scale: 1,
            })
            .delay(0.4)
            .to(0.03, { scale: 1.25 })  // Scale to 1.25 in 0.03 seconds
            .to(0.07, { scale: 1 })     // Scale back to 1 in the next 0.07 seconds
            .start();
    }
    public playThrowingRodAnim() {
        const squidCounterWorldPos = this.squidCounter.node.parent.convertToWorldSpaceAR(this.squidCounter.node.position);
        this.winnningPlayerSeat?.fishingRod?.playThrowAnim(squidCounterWorldPos);
    
    }

    public playMiniSquidsHookedAnim(isFirstRoundDoubleSquid : boolean = false) {
            this.winnningPlayerSeat?.show();
            this.winnningPlayerSeat?.seatEffect?.playHookedAnim(this.squidGame.getSquidGameData().isSuperSquidRound() || isFirstRoundDoubleSquid);
    }

    public initSquidAnim() {  
        if ( this.squidGame.getSquidGameData().isSuperSquidRound())
            this.onSquidMoveUpSS();
        else
            this.onSquidMoveUp();
    }

    private playSuperSquidFirstRoundAnim() {
        this.bigSquidContainer.scale = 0.38;
        this.big_SuperSquid.node.position =  cc.v2(-38,90);
        this.bigSquidContainer.active = true;
        this.bigSquid.node.active = false;
        this.big_SuperSquid.node.active = true;
        const anim_Clip = cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? "1_start_end_SS_CN" : "1_start_end_SS_EN";
        this.big_SuperSquid.setAnimation(0, anim_Clip, false).timeScale = 1.08;// SquidGameEffect.convertToSpineTime(1.08);
        this.squidCounter.node.active = true;
        this.squidCounter.node.getComponent(cc.Animation).play("RadialShine");
        this.squidAnimShadow.playGameStartShadowAnim(true);
        this.squidCounter.sparkle_Particle.active  = true;
        this.superSquid.active  = true;
        this.superSquid.getComponent(cc.Animation).play("GameStart_SuperSquid", 0);
    }

    private playBigSquidFirstRoundAnim() {
        this.bigSquidContainer.scale = 0.38;
        this.bigSquidContainer.active = true;
        this.bigSquid.node.active = true;
        this.big_SuperSquid.node.active = false;
        this.bigSquid.node.position =  cc.v2(-22,10) ;
        this.bigSquid.setAnimation(0, `1_start_end_${this.startAnimationName}`, false).timeScale = 1.08;
        this.squidCounter.node.active = true;
        this.squidCounter.node.getComponent(cc.Animation).play("TargetCounter_CollectMiniSquid");
        this.squidAnimShadow.playGameStartShadowAnim(false);
        this.squidCounter.sparkle_Particle.active  = false;
        this.superSquid.active  = false;
        if (this.squidGame.getSquidGameData().squidHuntGameParams.isFirstRoundDoubleSquid) {
            this.scheduleOnce(() => {
                this.switchToGoldenSquid(true);
            }, 1.6);
        }
    }

    ResetLastsquid() {
        this.SpineLastSquid.node.scale = 0;
        this.SpineLastSquid.node.opacity = 0;
        this.SpineLastSquid.node.angle = 30;
        this.SpineLastSquid.node.parent.x = 0;
        this.SpineLastSquid.node.parent.y = 0;
        this.SpineLastSquid.setSkin("standard");
    }

    private playLastSquidAnim() {
        this.ResetLastsquid();
        this._needToshowLastSquid = false;
        this.LastSquidAnim.getComponent(cc.Animation).play("LastSquidUI");
        //Start Last squid
        this.SpineLastSquid.setAnimation(0, '2_idle', true).timeScale = 6;//1.65
        this.squidGame.squidEffect.squidSound.play_LastSquidBG();
        this.squidGame.squidEffect.squidSound.play_LastSquidVoiceOver();
        this.scheduleOnce(function () { //loop a whle before flying
            //Fly Up Swimming
            this.SpineLastSquid.setMix('2_idle', '3_2swim_loop', 0.2); //blend current and next
            this.SpineLastSquid.addAnimation(0, '3_2swim_loop', true).timeScale = 1.25;//0.9
        }, 1);

        cc.tween(this.SpineLastSquid.node.parent)// just for moving only
            .show()
            .delay(1)
            .bezierTo(0.8, cc.v2(this.SpineLastSquid.node.parent), this.WayPointsLastSquid.position, cc.v2({ x: this.counterPos.x, y: this.counterPos.y - 40 }))
            .hide()
            .start()

        cc.tween(this.SpineLastSquid.node)// while moving, do something
            .to(0.25, { angle: 0, scale: 0.37, opacity: 255 }, { easing: 'backOut' }) //show squid
            .delay(1)
            .to(0.6, { scale: 0.1 }, { easing: 'sineInOut' })
            .start()

        cc.tween(this.node).delay(1.85).call(() => {
            this.squidCounter.getComponent(cc.Animation).play("LastSquid");
        }).start();

        this.scheduleOnce(() => {
            this.squidGame.getSquidGameData().looser.forEach((id) => { this.squidGame?.squidSeats[id]?.lastSquidAvatarAnim(); })
        }, 2)
    }


    private get startAnimationName () {
        const isDouble = this.squidGame.getSquidGameData()?.isDoubleSquid;
        const name = isDouble ? "FB" : "SH";
        const lang = cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? "CN" : "EN";
        return `${name}_${lang}`;
    }

    public playBigSquidEndGameAnim() {

        this.squidAnimShadow.playGameEndShadowAnim();
        const animationSequence = {
            start: '1_start_no_text',
            out2: '3_out2',
            two_idle: "2_idle",
            in3: '4_in3',
            end: '5_end'
          };
        const bigSquid = this.bigSquid;
        this.bigSquid.node.active = true;
        const bigSquidContainer = this.bigSquidContainer;
        bigSquidContainer.scale = 0.34;
        // bigSquidContainer.y = 130;
        bigSquidContainer.active = true;
        const convertToSpineTime = SquidGameEffect.convertToSpineTime;
        
        bigSquid.setAnimation(0, animationSequence.start, false).timeScale = convertToSpineTime(1.65);
        bigSquid.setMix(animationSequence.start, animationSequence.out2, 0.2);

        bigSquid.addAnimation(0, animationSequence.out2, false).timeScale = convertToSpineTime(1);
        
        bigSquid.addAnimation(0, animationSequence.two_idle, false).timeScale = convertToSpineTime(2.5);
       
        bigSquid.setMix(animationSequence.two_idle, animationSequence.end, 0.1);
        bigSquid.addAnimation(0, animationSequence.end, false).timeScale = convertToSpineTime(0.75);
    }

    public stopAll() {
        this.bigSquid.clearTracks();
        this.bigSquidContainer.active = false;
        cc.tween(this.node).stop();
    }
    // region - indiviudal mini-squids : Max - 9 , minimum as per current Squid count 


    get counterPos () {
        const targetWorldPos = this.squidCounter.node.parent.convertToWorldSpaceAR(this.squidCounter.node.position);
        return cc.v2(this.miniSquidsContainer.convertToNodeSpaceAR(targetWorldPos));
    }

    onSquidMoveUpSS() { // Fly to TargetCounter
            this.ResetSquid();
            this.miniSquid[0].getComponent(sp.Skeleton).setSkin('gold');
        this.scheduleOnce(function () {
            let sq = cc.tween;
            // --------Squid 0 First squid to reach target
            const s = 0;
            this.SquidMoveUpSpineAnimSS(s);
            sq(this.miniSquid[s])// 1st to reach!!!!!!! More Special animation
                .show() // because later will hide
                .parallel(
                    sq().to(0.3, { opacity: 255 }),

                    sq().bezierTo(1, cc.v2(this.miniSquid[s]), cc.v2(this.WayPoints[s]), cc.v2({ x: this.counterPos.x, y: this.counterPos.y + 5})),// move higher than TargetCounter
                    sq().delay(1).by(0.1, { position: new cc.Vec3(0, -37, 0) }), // land down on Target

                    sq().to(1, { angle: 0, scale: 0.12 } , { easing: "sineInOut" }),// 0.16
                    sq().delay(1.7).to(0.1, { opacity: 0 }) // 0.85
                )
                .hide()
                .start()

            // Squitch Target Counter from Standard to Super
            this.scheduleOnce(() => {
                this.switchToGoldenSquid(true);
            }, 1.6);
           
        }, 2.15);// Show later when Squid hands spread out. was 2.4
    }

    switchToGoldenSquid(_isGoldenSquid : boolean = true) {
        this.squidCounter.squid_Sprite.spriteFrame = _isGoldenSquid ? this.miniSquid_SuperSquid : this.miniSquid_Standard;
        this.squidCounter.sparkle_Particle.active =_isGoldenSquid;
    }

    SquidMoveUpSpineAnimSS(x: number) { // Fly to TargetCounter
        this.miniSquid[x].getComponent(sp.Skeleton).setAnimation(0, '3_1swim_start', false).timeScale = 1;
        this.miniSquid[x].getComponent(sp.Skeleton).addAnimation(0, '3_2swim_loop', true);

        this.scheduleOnce(function () {
                this.miniSquid[0].getComponent(sp.Skeleton).setMix('3_2swim_loop', '1_start', 0.2); // blend current and next
                this.miniSquid[0].getComponent(sp.Skeleton).addAnimation(0, '1_start', false).timeScale = 1.5;// 1.3 from WPTG

            // Tween Squid Counter to big
            cc.tween(this.squidCounter.squidCounterCircle)
                .delay(0.5)
                .to(0.25, { scale: 1 }, { easing: 'backOut' })
                .start()

        }, 0.5);
    }

    onSquidMoveUp() { // Fly to squidCounter TargetCounter

        this.ResetSquid();
        let totalSquid = this.squidGame.getSquidHuntGameInfo().totalSquidCounts;
        if(totalSquid>9) totalSquid = 9; // Max - 9 , minimum as per current Squid count 

        for (let i = 0; i < this.miniSquid.length; i++) {
            this.miniSquid[i].getComponent(sp.Skeleton).setSkin('standard');
        }

        this.scheduleOnce(function () {
            const sq = cc.tween;

            // --------Squid 0 First squid to reach target
            let s = 0;
            this.SquidMoveUpSpineAnim(s);
            sq(this.miniSquid[s])// 1st to reach!!!!!!! More Special animation
                .show() // because later will hide
                .parallel(
                    sq().to(0.3, { opacity: 255 }),

                    sq().bezierTo(0.6, cc.v2(this.miniSquid[s]), cc.v2(this.WayPoints[s]), cc.v2({ x: this.counterPos.x, y: this.counterPos.y + 5 })),// move higher than squidCounter
                    sq().delay(0.6).by(0.1, { position: new cc.Vec3(0, -37, 0) }), // land down on Target

                    sq().to(0.6, { angle: 0, scale: 0.12 }, { easing: "sineInOut" }),
                    sq().delay(1.2).to(0.05, { opacity: 0 }) // 0.85
            )
                .hide()
                .start()

                for (let s = 1; s < totalSquid; s++) {
                    const delayTime = [0.1, 0.2, 0.2, 0.3, 0.4, 0.5, 0.6, 0.6][s - 1]; // Custom delays for each squid
                    const duration = [1, 1.2, 1, 0.75, 0.75, 0.6, 0.8, 1][s - 1]; // Custom durations for each squid
                    const angle = [70, 10, 45, 60, 90, 2, 10, -350][s - 1]; // Custom angles for each squid
                    const angleDelay = [0.1, 0.3, 0, 0, 0, 0, 0, 0][s - 1]; // Correct angle delays for each squid
                    const fadeDelay = [0.95, 1.15, 0.95, 0.7, 0.7, 0.55, 0.75, 0.95][s - 1]; // Correct fade delays for opacity
                
                    this.SquidMoveUpSpineAnim(s);
                
                    sq(this.miniSquid[s])
                        .delay(delayTime)
                        .show()
                        .parallel(
                            sq().to(0.2, { opacity: 255 }),
                            sq().bezierTo(duration, cc.v2(this.miniSquid[s]), cc.v2(this.WayPoints[s]), cc.v2(this.counterPos)),
                            sq().delay(angleDelay).to(0.5, { angle, scale: 0.12 }, { easing: "sineInOut" }),
                            sq().delay(fadeDelay).to(0.04, { opacity: 0 })
                        )
                        .hide()
                        .start();
                }

        }, 2.15);// Show later when Squid hands spread out. was 2.4
    }

    SquidMoveUpSpineAnim(x: number) { // Fly to squidCounter
        this.miniSquid[x].getComponent(sp.Skeleton).setAnimation(0, '3_1swim_start', false).timeScale = 2;
        this.miniSquid[x].getComponent(sp.Skeleton).addAnimation(0, '3_2swim_loop', true);

        if (x == 0) {
            this.miniSquid[0].getComponent(sp.Skeleton).setMix('3_2swim_loop', '1_start', 0.2); // blend current and next
            this.miniSquid[0].getComponent(sp.Skeleton).addAnimation(0, '1_start', false).timeScale = 1.5;
        }
    }

    ResetSquid() {
        // RESET all position/rotation/opacity...
        // this.squidCounter.opacity = 0; still reset opacity, now from test button 'RESET'
        this.squidCounter.squid_Sprite.spriteFrame = this.miniSquid_Standard;
        const angle = [40, -120, 170, -160, -50, 0, 80, 140, -162]
        // Spine MiniSquid
        for (let i = 0; i < this.miniSquid.length; i++) {
            this.miniSquid[i].setPosition(cc.v3(0, 200, 0));
            this.miniSquid[i].opacity = 0;
            this.miniSquid[i].scale = 0.16;
            this.miniSquid[i].angle = angle[i];
        }
    }

    syncSquidCounter() {
        const squidData = this.squidGame.getSquidGameData();
        if (squidData?.isSquidRunning()) {
            const isSuperSquid = squidData?.isSuperSquidRound?.();
            const squidCount = squidData?.squidHuntGameInfo?.remainingSquidCounts || 0;
            const isDoubleSquid = squidData.isFirstRoundDoubleSquid;
            this.squidCounter?.syncSquidCounter(squidCount);
            this.switchToGoldenSquid(isSuperSquid || isDoubleSquid);
        }
        else {
            this.squidCounter?.hide();
            this.switchToGoldenSquid(false); // Switch to normal Squid if it is not running 
        }
    }

    // end-region 

}
