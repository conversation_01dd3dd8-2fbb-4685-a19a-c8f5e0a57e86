import SquidGameAnim from "./common/SquidGameAnim";
import SquidGameSound from "./common/SquidGameSound";
import SquidGameParticle from "./common/SquidGameParticle";
import SquidCounter from "../SquidCounter";
import SquidPlayerSeat from "../SquidPlayerSeat";
import SquidGame from "../SquidGame";
import cv from "../../../../../components/lobby/cv";

const { ccclass, property } = cc._decorator;

/*
 * Note: an effect could be an animation (Cocos Anim, Spine, Tween), a particle system, or a sound effect. 
 */
@ccclass
export default class SquidGameEffect extends cc.Component {

    @property({ type: SquidGameAnim }) squidAnim: SquidGameAnim = null;
    @property({ type: SquidGameSound }) squidSound: SquidGameSound = null;
    @property({ type: SquidGameParticle }) squidParticle: SquidGameParticle = null;
    @property({ type: SquidCounter }) squidCounter: SquidCounter = null;
    
    // bigger is faster animation
    private static _effectTimeScale = 1;
    public squidGame: SquidGame = null;
    
    public static setTimeScale(scale: number) {
        if (scale > 0) {
            SquidGameEffect._effectTimeScale = scale;
        }
    }

    public static resetTimeScale() {
        SquidGameEffect._effectTimeScale = 1;
    }

    public static convertToSpineTime(time: number) {
        return SquidGameEffect._effectTimeScale * time;
    }

    public static toTweenTime(time: number) {
        return time / SquidGameEffect._effectTimeScale;
    }

    public init(squidGame: SquidGame) {
        this.squidGame = squidGame;
        this.squidCounter.init(this.squidGame);
        this.squidAnim.init(this.squidGame);
    }

    public playFirstRoundEffect() {
        this.squidCounter.show();
        this.squidSound.play_GameStartSound();
        this.squidAnim.playFirstRoundAnim();
    }

    public playSuperSquidAnimEffect() {
        this.squidSound.play_SuperSquidSound();
        this.squidAnim.playFirstRoundAnim(true);
    }

    public playFishingAnim() {
        this.squidSound.play_FishingSound();
        this.squidAnim.playFishingAnim();
    }


    public playEndGameEffect(msg: any, onComplete: Function) {
        this.squidCounter.node.active = false;
        this.hideAvatarAnimation();
        const squidAnim = this.squidAnim;
        const squidSound = this.squidSound;
        const squidParticle = this.squidParticle;

        // const hideCardsTween = cc.tween()
        //     .call(() => {
        //         // if (allowCallbacks) {
        //         //     // SQUIDGAME TODO: fire a msg to hide cards holdemRoom.HideHoleCard()
        //         // }
        //     });

        const bigSquidTween = cc.tween()
            .call(() => {
                squidAnim?.playBigSquidEndGameAnim();
                squidSound?.play_GameEndSound();
            });

        const winSquidPlayer = this.squidGame.getSquidGameSettlementData().winSquidPlayer;
        const winnerSeats: SquidPlayerSeat[] = Object.keys(winSquidPlayer)
            .map(key => this.squidGame?.squidSeats[Number(key)]).filter(seat=>seat instanceof SquidPlayerSeat);

        const allPlayers: SquidPlayerSeat[] = msg?.players?.map(player => this.squidGame?.squidSeats[Number(player?.userid)]).filter(seat=>seat instanceof SquidPlayerSeat);

        const refundTween = cc.tween()
            .call(() => { squidParticle?.playCoinRefundAnim(winnerSeats.map(seat => seat.node))});

        const squidReturnTween = cc.tween()
            .call(() => {winnerSeats.forEach((seat)=>seat.seatEffect.playSwimToBigSquidAnim())});

        const winAmountAnimation = cc.tween()
            .call(() => {allPlayers.forEach((seat)=>seat?.playWinTextAnimation(msg))});

        const hideSettlement = cc.tween().call(()=>{
            squidAnim.squidAnimShadow.playHideSettlement();
        });
        const convertToTweenTime = SquidGameEffect.toTweenTime;

        cc.tween(this.node).parallel(
            // cc.tween().delay(convertToTweenTime(0.75)).then(hideCardsTween),
            cc.tween().delay(convertToTweenTime(0)).then(bigSquidTween),
            cc.tween().delay(convertToTweenTime(1.35)).then(refundTween),
            cc.tween().delay(convertToTweenTime(1.8)).then(winAmountAnimation),
            cc.tween().delay(convertToTweenTime(2.35)).then(squidReturnTween),
            cc.tween().delay(convertToTweenTime(3.5)).then(hideSettlement),
            cc.tween().delay(convertToTweenTime(3.5)).call(() => { 
                if (onComplete) 
                    onComplete(); 
                this.squidCounter.node.active = true;}))
            .delay(convertToTweenTime(3)) // wait for big squid to finish moving up
            .call(SquidGameEffect.resetTimeScale)
            .start()
    }

    public stopAllSquidAnim() {
        cc.tween(this.node).stop();
        this.squidAnim.stopAll();
        SquidGameEffect.resetTimeScale();
    }

    public setNormalSquid()
    {
        this.squidAnim?.switchToGoldenSquid(false);
    }

    public updateSquidCounter() {
        this.squidCounter.show();
    }

    public hideSquidCounter() {
        this.squidCounter.hide();
    }

    public playAvatarAnimation (turnid:number) {
        const looser = cv.GameDataManager.featureData?.looser;
        this.scheduleOnce(()=>{
            looser.forEach((id)=>{
                if(id !== this.squidGame.getSelfUserID() ||  (id !== turnid))
                    this.squidGame?.squidSeats[id]?.playAvatarAnim();
            })
        }, 1)
    }

    public hideAvatarAnimation(turnid?: number) {
        const looserIds = this.squidGame.getSquidGameData()?.looser || [];
        // If turnid is provided, only execute for the matching ID
        const idsToHide = turnid ? [turnid] : looserIds;
        idsToHide.forEach(id => {
            this.squidGame?.squidSeats[id]?.hideAvatarAnim();
        });
    }
    
}
