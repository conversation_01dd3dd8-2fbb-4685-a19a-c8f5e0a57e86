
const { ccclass, property } = cc._decorator;

@ccclass
export default class SquidGameParticle extends cc.Component {

    @property(cc.ParticleSystem3D)
    coinUpParticle: cc.ParticleSystem3D = null;

    @property(cc.ParticleSystem3D)
    coinRefundParticle: cc.ParticleSystem3D = null;

    public playCoinUpAnim(targetNodes: cc.Node[]) {
        targetNodes
            .map(node => node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(cc.v3(0, -105, 0)))
            .forEach(pos => { this.playCoinUp(pos, false); });
    }

    public playCoinRefundAnim(targetNodes: cc.Node[]) {
        targetNodes
            .map(node => node.convertToWorldSpaceAR(cc.Vec3.ZERO))
            .forEach(pos => { this.playCoinRefundToSeat(pos); });
    }

    private playCoinUp(worldPos: cc.Vec3, isFlipX: boolean) {
        const node = cc.instantiate(this.coinUpParticle.node);
        node.parent = this.node;
        node.setPosition(this.node.convertToNodeSpaceAR(worldPos));
        node.active = true;

        const particle = node.getComponent(cc.ParticleSystem3D);
        const scale = 0.8;
        particle.node.scaleY = scale;
        particle.node.scaleX = isFlipX ? -scale : scale;
        particle.stop();
        particle.play();
    }

    private playCoinRefundToSeat(worldPos: cc.Vec3) {
        const startPos = cc.Vec3.ZERO;
        const endPos = this.node.convertToNodeSpaceAR(worldPos);
        endPos.addSelf(endPos.normalize().mul(200)); // end pos to travel to top of avatar
        const target_angle = cc.v3(0, -1, 0).signAngle(endPos) * 180 / Math.PI;

        const node = cc.instantiate(this.coinRefundParticle.node);
        node.parent = this.node;
        node.active = true;
        const particle = node.getComponent(cc.ParticleSystem3D);
        particle.stop();
        particle.play();

        cc.tween(particle.node)
            .set({
                active: true,
                angle : target_angle,
                position: startPos,
            })
            .to(0.75, { position: endPos })
            .call(() => {
                particle.stop();
            })
            .removeSelf()
            .start();
    }
}
