export enum Ease {
    quadIn = 'quadIn',
    quadOut = 'quadOut',
    quadInOut = 'quadInOut',
    cubicIn = 'cubicIn',
    cubicOut = 'cubicOut',
    cubicInOut = 'cubicInOut',
    quartIn = 'quartIn',
    quartOut = 'quartOut',
    quartInOut = 'quartInOut',
    quintIn = 'quintIn',
    quintOut = 'quintOut',
    quintInOut = 'quintInOut',
    sineIn = 'sineIn',
    sineOut = 'sineOut',
    sineInOut = 'sineInOut',
    expoIn = 'expoIn',
    expoOut = 'expoOut',
    expoInOut = 'expoInOut',
    circIn = 'circIn',
    circOut = 'circOut',
    circInOut = 'circInOut',
    elasticIn = 'elasticIn',
    elasticOut = 'elasticOut',
    elasticInOut = 'elasticInOut',
    backIn = 'backIn',
    backOut = 'backOut',
    backInOut = 'backInOut',
    bounceIn = 'bounceIn',
    bounceOut = 'bounceOut',
    bounceInOut = 'bounceInOut',
    smooth = 'smooth',
    fade = 'fade'
}


