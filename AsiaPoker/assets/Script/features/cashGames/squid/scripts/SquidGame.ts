import game_protocol = require("./../../../../../Script/common/pb/gs_protocol");
import game_pb = game_protocol.protocol;
import StartSquidPopup from "./StartSquidPopup";
import { CashGameFeatureBase, CashGameFeatureCreator } from "../../CashGameFeature";
import cv from "../../../../components/lobby/cv";
import {Squid_GameNet} from "./net/Squid_GameNet";
import { SquidGameData } from "./SquidGameData";
import SquidGameEffect from "./effect/SquidGameEffect";
import SquidPlayerSeat from "./SquidPlayerSeat";
import { Seat } from "../../../../components/game/dzPoker/Seat";
import UserBalanceAndExchange from "../../../../../Script/components/lobby/hall/UserBalanceAndExchange";
import SquidInGameMessage from "./SquidInGameMessage";
import DoubleSquidGameStatus from "./DoubleSquidGameStatus";
import { SquidPrefabs } from "./SquidFeature";
import { BaseGameType } from '../../../Feature';

const { ccclass } = cc._decorator;

/**
 * SquidGame main class
 */
@ccclass
export default abstract class SquidGame extends CashGameFeatureBase {
    protected squidData: SquidGameData = null;
    protected squidGameNet: Squid_GameNet = null;
    startSquidPopup: StartSquidPopup = null;
    squidEffect: SquidGameEffect = null;
    squidPlayerSeatPrefab: cc.Prefab = null;
    squidInGameMessage: SquidInGameMessage = null;
    doubleSquidGameStatus: DoubleSquidGameStatus=null;
    protected _userBalAndExchange: UserBalanceAndExchange = null;
    private readyColor:cc.Color=new cc.Color(240, 221, 152);
    squidSeats: Map<number, SquidPlayerSeat> = new Map();
    public onlyLeftPlayerAnimation:boolean=true;
    private squidLast2LooserAnimationPlay:boolean=false;
    private firstTurnOfHand:boolean = true;
    public waitingOnceOnSnapshot: boolean = true;

    protected onLoad(): void {
        this.squidData = this.squidData || new SquidGameData();
        this.squidGameNet = new Squid_GameNet();
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeSpanshotRoomInfo, this.onNoticeGameSnapShot.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGameFailed, this.onCancelSquidHuntGameNotice.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.ResponseJoinSquidHuntGame, this.onResponseJoinSquidHuntGame.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeJoinSquidHuntGame, this.onNoticeJoinSquidHuntGame.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.ShowSquidRules, this.showSquidRules.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGame, this.onStartSquidHuntGameNotice.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.SquidHuntGameFinalSettlement, this.onSquidHuntGameFinalSettlement.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeGameSettlement, this.onNoticeGameSettlement.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.OnStandupSucc, this.OnStandUpSucc.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.OnSitdownSucc, this.OnSitDownSucc.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.OnStartgameNotice, this.OnStartgameNotice.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.OnResetTableDoneForWaitttingStatus,this.OnResetTableDoneForWaitttingStatus.bind(this),this);
        cv.MessageCenter.register("syncGameMain_SnapShot",this.syncGameMainSnapShot.bind(this),this);
        cv.MessageCenter.register("on_sitdown_success_response",this.OnResponseSitDownSucc.bind(this),this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.ShowSquidStatus, this.showSquidGameStatus.bind(this), this);
        cv.MessageCenter.register("squidLast2LooserAnimationPlay",this.SquidLast2LooserAnimationPlay.bind(this),this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.OnPlayerInfoUpdate,this.OnPlayerInfoUpdate.bind(this),this);
    }

    protected start(): void {
        this.afterLoadingAssets();
    }

    protected onDestroy(): void {
        this.squidGameNet.destroy();
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeSpanshotRoomInfo, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGameFailed, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.ResponseJoinSquidHuntGame, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeJoinSquidHuntGame, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGame, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.ShowSquidRules, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeGameSettlement, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.SquidHuntGameFinalSettlement, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.OnStandupSucc, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.OnSitdownSucc, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.OnStartgameNotice, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.OnResetTableDoneForWaitttingStatus, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.OnPlayerInfoUpdate, this);
        cv.MessageCenter.unregister("syncGameMain_SnapShot", this);
        cv.MessageCenter.unregister("on_sitdown_success_response", this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.ShowSquidStatus, this);
        cv.MessageCenter.unregister("squidLast2LooserAnimationPlay", this);
    }

    private syncGameMainSnapShot()
    {
        this.recoverPlayerSquidSeats();
        this.squidEffect?.squidAnim?.syncSquidCounter();
    }

    private onNoticeGameSnapShot(result: any) {
        if(!this.squidData) this.squidData = new SquidGameData();
        this.squidData.squidHuntGameInfo = result.squidHuntGameInfo;
        this.squidData.squidHuntGameParams = result.squidHuntGameParams;
        this.squidData.squidHuntGameSettlement = result.squidHuntGameSettlement;
        this.squidData.serverTime = result.serverTime;
        this.squidEffect?.squidAnim.syncSquidCounter();
        const isStarted = this.squidData?.squidHuntGameInfo?.isStarted;
        if (isStarted){
            if(this.getSquidGameData()?.isUnRegisteredPlayer() && this.waitingOnceOnSnapshot) {
                this.squidInGameMessage.showWaitingForNextGameMsg();
            }
            this.setSpectatorStatus();
            this.onlyLeftPlayerAnimation=this.squidData.looser.length!==2;
        }
        this.squidInGameMessage?.showWelcomeSquidGameMsg(this.squidData?.squidHuntGameParams.minPlayersToStartSquidHunt);
        this.showInGameCloth();
        this.recoverPlayerSquidSeats();
    }

    private afterLoadingAssets() {
        const  assets : SquidPrefabs = CashGameFeatureCreator.getFeaturePrefebs(BaseGameType.CashGame,CashGameFeatureCreator.getFeatureType(cv.Enum.GameId.Squid));
    //    const  assets: SquidPrefabs = CashGameFeatureCreator.getFeaturePrefebs(cv.Enum.GameId.Squid);

        // hide loading screen
        this.hideLoadingScreen();
        // Request for latest Exchange rate 
        cv.userBalanceAndExchange.requestLatestExchange();
        
        // squid animations
        this.squidEffect = this.createNodeFromPrefab(assets.gameEffects).getComponent(SquidGameEffect);
        this.squidEffect.node.active = true;
        this.squidEffect.init(this);
        this.squidEffect?.squidAnim?.syncSquidCounter();

        // start squid popup
        this.startSquidPopup = this.createNodeFromPrefab(assets.start_popup)?.getComponent(StartSquidPopup);
        this.startSquidPopup.init(this);
    
        
        // squid player seat
        this.squidPlayerSeatPrefab = assets.playerSeat;
        this.recoverPlayerSquidSeats();

        this.squidInGameMessage = this.createNodeFromPrefab(assets.gameMessages)?.getComponent(SquidInGameMessage);
        this.squidInGameMessage.initialise(this);

        this.doubleSquidGameStatus=this.createNodeFromPrefab(assets.gameStatus)?.getComponent(DoubleSquidGameStatus);
        this.doubleSquidGameStatus?.init(this); 
        this.requestSnapShot();
        this.setGameMenuOnTop();

    }

    private setGameMenuOnTop () {
        const menuBtn = this.gameScene.gameMain.menu_button;
        const siblingIdx = this.squidEffect.node.getSiblingIndex()
        const menuWorldPos = this.gameScene.gameMain_panel.convertToWorldSpaceAR(menuBtn.position)
        const positionInScene = this.gameScene.node.convertToNodeSpaceAR(menuWorldPos);
        menuBtn.parent = this.gameScene.node;
        menuBtn.setPosition(positionInScene);
        menuBtn.setSiblingIndex(siblingIdx + 1)
        this.gameScene.menu_Panel.setSiblingIndex(siblingIdx + 2); // To keep menu on top
        this.squidEffect.node.setSiblingIndex(this.gameScene.recallbuyin_panel.getSiblingIndex() - 2); // Squid animation below buyin, recall buyin panel
        this.scheduleOnce(()=>{
            this.squidEffect.squidCounter.updateSquidCounterPos(menuWorldPos);
       }, 0.2)
    }

    private onLoadingAssetsFailed(error) {
         cc.error(`loading Squid Assests Fail Handler`+error);
    }

    /**
     * create node from prefab and set parent to squid game
     */
    private createNodeFromPrefab(prefab: cc.Prefab, parent = this.node) {
        const node = cc.instantiate(prefab);
        node.parent = parent;
        node.active = false;
        return node;
    }


    private onCancelSquidHuntGameNotice(msg: game_pb.NoticeStartSquidHuntGameFailed) {
        if (cv.GameDataManager.tRoomData.GetTablePlayer(this.getSelfUserID())) { // Display a message if the player is seated.
            cv.TP.showMsg(cv.config.getStringData('Squid_Not_Enough_Players'), cv.Enum.ButtonStyle.GOLD_BUTTON, null);
            this.scheduleOnce(this.hideTipsPanel, 7);
            this.squidInGameMessage?.removeTimer();
            this.squidData.resetOnEndSquid();
            this.showInGameCloth();
        }
    }

    showInGameCloth() { 
        if(!this.gameScene?.gameMain?.gameTableInfo) return;
        const isGameStarted = this.squidData?.squidHuntGameInfo?.isStarted  || false;
        
        let _gameClothMsg = "";
        if (!isGameStarted) {
            const playerCount = this.squidData?.squidHuntGameParams?.minPlayersToStartSquidHunt ?? 3; // in case of backend data is undefined or null so for user perspective added default 3.
            _gameClothMsg = cv.StringTools.formatC(cv.config.getStringData("Squid_Game_Start_With_X_player_msg"), playerCount);
        }
        else {
            _gameClothMsg = cv.StringTools.formatC(cv.config.getStringData("Squid_one_squid_value"), cv.StringTools.clientGoldByServer(this.getSquidHuntGameParams()?.squidValue[1])) + this.getCurrencyString();
            if (this.getSquidHuntGameParams()?.isFirstRoundDoubleSquid) {
                const doubleSquidMessage = cv.config.getStringData("Squid_Game_first_round_double_squid");
                _gameClothMsg = `${_gameClothMsg}\n${doubleSquidMessage}`;
            }
        }

        this.gameScene.gameMain.gameTableInfo.setSquidText(_gameClothMsg,isGameStarted); // Set GAME-TABLE-CLOTH INFO
    }

    public changeSeatStatusToWaiting(){// when only one seat is occupide then status should be waiting in squid game
        const occupiedSeats = this.gameScene.gameMain.seatList.filter(seat => seat && !seat.isSeatEmpty());
        if(occupiedSeats.length === 1)
        {
            occupiedSeats[0].showStatusText(cv.config.getStringData('GameUiWaiting'));
            occupiedSeats[0].countDown_text.node.active = false;
        }
    }

    private showSquidRules () {
        this.doubleSquidGameStatus.show(true);
    }

    private showSquidGameStatus () {
        this.doubleSquidGameStatus.show();
    }

    private onResponseJoinSquidHuntGame(data: { msg: game_pb.ResponseJoinSquidHuntGame}) {
        // Update own player Seat status  as : "Ready" 
    }

    private onNoticeJoinSquidHuntGame(msg: game_pb.NoticeJoinSquidHuntGame) {
        // Update Seat status of sit down players as : "Ready" for whoevers registered for Squid hunt;  "Pending" for those whovevers still waiting to pay escrow for Squid hunt
        this.squidData.squidHuntGameInfo = msg.info;
        this.gameScene.gameMain.seatList.forEach(seat => {
            if (seat && !seat.isSeatEmpty()) {
                if(this.getSquidGameData()?.isRegisteredPlayer(seat.PlayerInfo?.playerid)){
                    seat.showStatusText(cv.config.getStringData('Ready'),'Ready',34,this.readyColor);
                    seat.countDown_text.node.active=false;
                    seat.doBgLayerGray(true);
                }
                else if(this.getSquidGameData()?.isUnRegisteredPlayer(seat.PlayerInfo?.playerid) && !seat.PlayerInfo.inStay){
                    seat.showStatusText(cv.config.getStringData('Pending'));
                    seat.countDown_text.node.active=false;
                    seat.doBgLayerGray(true);
                }
            }
        });
    }

    private onStartSquidHuntGameNotice(msg: game_pb.NoticeStartSquidHuntGame) {
        this.squidData.squidHuntGameInfo = msg.info;
        this.resetSquidElements();
        this.squidData.resetOnStartSquid();
        this.recoverPlayerSquidSeats(); 
        // start animation
        this.squidEffect?.playFirstRoundEffect();
        this.showInGameCloth();
        if(this.doubleSquidGameStatus?.node?.active){
            this.showSquidGameStatus();
        }
        cv.MessageCenter.send("setForbidSquidChat", cv.GameDataManager.isSquidSpectator())
        cv.MessageCenter.send("action_review_hide");
    }

    private onNoticeGameSettlement(msg : game_pb.NoticeGameSettlement)
    {
        if (!this.squidData?.squidHuntGameInfo?.isStarted) return;
        if (msg.squidHuntGameSettlement) {
            this.squidData.squidHuntGameSettlement = msg.squidHuntGameSettlement;
            this.updateEachSeatSquidCount();
            this.squidEffect?.playFishingAnim();
            this.squidData.squidHuntGameInfo.isFirstRound = false; // Once we get NoticeGameSettlement after Squid starts ( First round will be completed), set isFirstRound = false as BE data not updating on NoticeGameSettlement
            this.squidLast2LooserAnimationPlay = false;
            this.firstTurnOfHand = true;
        }
        else
        {
            this.squidEffect?.setNormalSquid();
        }

        this.scheduleOnce(()=>{
            this.squidEffect.squidAnim.checkForLastRemainingSquid();
        }, 3.7);
    }

    private onSquidHuntGameFinalSettlement(msg : game_pb.SquidHuntGameFinalSettlement){
        this.squidInGameMessage?.removeGracePeriod();
        this.onlyLeftPlayerAnimation=true;
        this.squidLast2LooserAnimationPlay=false;
        this.doubleSquidGameStatus?.hide();
        this.squidEffect?.playEndGameEffect(msg, ()=>{
            // Show Result popup
            // this scheduleOnce use for conflictiong 2 animation to open gamereview popup work properly 
            this.scheduleOnce(()=>{
                this.gameScene.gameMain.onbtnAllreviewClick("SquidFinalSettlement");
            },0.1);
            this.squidData?.resetOnEndSquid();
            this.resetSquidElements();
            this.recoverPlayerSquidSeats();
            this.gameScene?.gameMain?.resetTable();
            this.gameScene?.gameMain?.setFaceBtnEnabled(true);
            this.showInGameCloth();
        });
        cv.MessageCenter.send("action_review_hide");
    }

    private OnStandUpSucc()
    {
        this.recoverPlayerSquidSeats();
        this.showInGameCloth();
        this.changeSeatStatusToWaiting();
    }

    private OnSitDownSucc()
    {
        this.recoverPlayerSquidSeats();
    }

    private OnPlayerInfoUpdate(msg : any)
    {
        if(!msg.squidHuntGameInfo) return;
        if(!this.squidData) this.squidData = new SquidGameData();
        this.squidData.squidHuntGameInfo = msg.squidHuntGameInfo;
        if(this.doubleSquidGameStatus?.node?.active){
            this.showSquidGameStatus();
        }
    }

    private OnResponseSitDownSucc(msg : game_pb.ResponseSitDown)
    {
        if(!msg.squidHuntGameInfo) return;
        if(!this.squidData) this.squidData = new SquidGameData();
        this.squidData.squidHuntGameInfo = msg.squidHuntGameInfo;
        this.squidData.serverTime = msg.serverTime;

    }

    private SquidLast2LooserAnimationPlay(turnId) {
       this.squidEffect.hideAvatarAnimation(turnId); // Hide animation for player with turn
        if (this.squidData.remainingSquid === 1) return; // Last squid left animation will run
        if (this.firstTurnOfHand) {
            this.firstTurnOfHand = false;
            if (this.onlyLeftPlayerAnimation &&
                this.squidData?.looser?.length === 2) { // Used local variable to show notice one time only, as if same user can won hand and there are chances of multiple times this notice can get with 2 loosers
                this.squidInGameMessage.showOnlyPlayerLeftNotice();
                this.onlyLeftPlayerAnimation = false;
                this.squidEffect.playAvatarAnimation(turnId);
            }
        }
    }

    private OnStartgameNotice(msg : game_pb.NoticeStartGame)
    {// non squid game do not enter in this function this call on notice other game also
        if(!msg || !(cv.GameDataManager.tRoomData.u32GameID === cv.Enum.GameId.Squid)) return;
        if(!this.squidData) this.squidData = new SquidGameData();
         this.squidData.squidHuntGameInfo = msg.squidHuntGameInfo;
         this.squidInGameMessage?.removeTimer();
         this.squidInGameMessage?.removeGracePeriod();
        if (this.squidData.isSuperSquidRound()) {
            this.squidEffect?.playSuperSquidAnimEffect();
            cv.MessageCenter.send('action_review_hide');
        }
        cv.MessageCenter.send("setForbidSquidChat", cv.GameDataManager.isSquidSpectator())
    }

    private OnResetTableDoneForWaitttingStatus(){
        if (!this.getSquidGameData()?.squidHuntGameInfo?.isStarted) return;
        const allSeats = this.gameScene?.gameMain?.seatList;
        allSeats?.forEach(seat => {
            const playerId = seat?.PlayerInfo?.playerid;
            if (playerId && this.getSquidGameData()?.isUnRegisteredPlayer(playerId)) {
                seat.updateSeatStatus(cv.Enum.SeatStatus.SeatStatus_waiting);
            }
        });
    }

    private setSpectatorStatus() {
        this.gameScene.gameMain.seatList.forEach(seat => {
            if (seat && !seat.isSeatEmpty()) {
                if (this.getSquidGameData()?.squidHuntGameInfo.satOutUntilEndOfSquidGamePlayers.includes(seat.PlayerInfo?.playerid) && !seat.PlayerInfo.inStay) {
                        seat.showStatusText(cv.config.getStringData('GameUiWaiting'));
                        seat.countDown_text.node.active = false;
                    }
            }
        });
    }

    private updateEachSeatSquidCount()
    {
        const winSquidPlayer  = this.getSquidGameSettlementData()?.winSquidPlayer;
        if (winSquidPlayer &&  Object.keys(winSquidPlayer).length > 0) {
            Object.entries(winSquidPlayer).forEach(([key, value]) => {
                const squidSeat = this.squidSeats[Number(key)];
                if (squidSeat) {
                    squidSeat.currSquidCount = value;
                }
            });
        }
    }

    public resetSquidElements()
    {
        this.squidEffect?.hideSquidCounter();
        Object.values(this.squidSeats).forEach((squidSeat)=>{
            squidSeat.currSquidCount = 0;
            squidSeat.setSquidCount(); // Update Squid counts of all players after Anim ends
        })
    }

    private recoverPlayerSquidSeats() {
        console.log('recoverPlayerSquidSeats ');
        if(!this.gameScene)
            return;
        const isGameStarted = this.getSquidHuntGameInfo()?.isStarted;
        this.addSquidSeatToPlayerSeats();
        
        if (isGameStarted) {
            this.updateEachSeatSquidCount();
        }

        this.gameScene.gameMain.seatList.forEach(seat => {
            const squidSeat = this.getSquidPlayerSeat(seat);
            if (squidSeat) {
                if (isGameStarted && !seat.isSeatEmpty() && this.getSquidGameData()?.isRegisteredPlayer(seat.PlayerInfo?.playerid)){
                    squidSeat.setSquidCount();
                    squidSeat.show();
                }
                else
                    squidSeat.hide();
            }
        });
    }

    private addSquidSeatToPlayerSeats() {
        if (!this.squidPlayerSeatPrefab) {
            return;
        }
        this.gameScene.gameMain.seatList.forEach(seat => {
            let squidSeat: SquidPlayerSeat = this.getSquidPlayerSeat(seat);
            if (!squidSeat) {
                squidSeat = cc.instantiate(this.squidPlayerSeatPrefab).getComponent(SquidPlayerSeat);
                squidSeat.addSquidToSeat(seat);
                squidSeat.seatEffect.init(this.squidEffect)
            }
            if(seat.PlayerInfo?.playerid) 
            this.squidSeats[seat.PlayerInfo?.playerid] = squidSeat;
        });
    }
    
    private getSquidPlayerSeat(seat: Seat): SquidPlayerSeat {
        return seat.node.getComponentInChildren(SquidPlayerSeat);
    }

    private hideTipsPanel()
    {
        this.unscheduleAllCallbacks();
        cv.TP.hideTipsPanel();
    }

    public isRegisteredPlayers(playerid: any):boolean
    {
        return this.getSquidHuntGameInfo()?.registeredPlayers?.includes(playerid);
    }

    private isUnRegisteredPlayers(playerid: any):boolean
    {
        return this.getSquidHuntGameInfo()?.unRegisteredPlayers?.includes(playerid);
    }
    
    public getSquidGameData() {
        if(!this.squidData) this.squidData = new SquidGameData();
        return this.squidData;
    }

    public getSquidGameSettlementData() {
        return this.getSquidGameData()?.squidHuntGameSettlement;
    }

    public getSquidHuntGameInfo() {
        return this.squidData?.squidHuntGameInfo;
    }

    public getSquidHuntGameParams() {
        return this.squidData?.squidHuntGameParams;
    }

    public onGoToDepositClick() {
        cv.AudioMgr.playButtonSound('button_click');
        cv.worldNet.requestGetUserData();
        cv.SHOP.RechargeClick();
    }

    public getSquidGameNet(): Squid_GameNet {
        return this.squidGameNet;
    }

    public checkIsSufficientFund(amount:number){
        return cv.userBalanceAndExchange.checkIsSufficientFund(amount);
    }

    public isDoubleSquidMode():boolean
    {
        return this.getSquidHuntGameParams()?.mode === game_pb.SquidHuntGameMode.MULTIPLIER_MODE;
    }
    
    abstract getFeatureData();
    abstract requestSnapShot();
    abstract hideLoadingScreen();
    abstract getSelfUserID();
    abstract leaveTable();
    abstract sitOut();
    abstract isAutoJoinSquidGame(): boolean;
    abstract turnOnAutoJoinSquidGame();
    abstract getCurrencyString();
}
