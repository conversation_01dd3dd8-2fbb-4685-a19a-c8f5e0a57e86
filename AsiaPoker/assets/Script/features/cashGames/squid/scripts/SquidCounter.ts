import cv from "../../../../components/lobby/cv";
import SquidGame from "./SquidGame";

const { ccclass, property } = cc._decorator;

@ccclass
export default class SquidCounter extends cc.Component {

    @property(cc.Label) numLabel: cc.Label = null;
    @property(cc.Sprite) squid_Sprite: cc.Sprite = null;
    @property(cc.Node) sparkle_Particle: cc.Node = null;
    private squidGame : SquidGame = null;
    
    public init(_squidGame: SquidGame) {
        this.squidGame = _squidGame;
        if (_squidGame.getSquidGameData()?.squidHuntGameInfo?.isStarted) {
            this.syncSquidCounter(_squidGame.getSquidGameData()?.squidHuntGameInfo?.remainingSquidCounts);
        }
    }

    updateSquidCounterPos(menuPos: cc.Vec2|cc.Vec3){
        const posInNode = this.node.parent.convertToNodeSpaceAR(menuPos);
        this.node.setPosition(posInNode.x + 120, posInNode.y);
        this.node.active = true;
    }

    // eslint-disable-next-line consistent-return
    public show() {
        if(!this.squidGame)
            return;
        const squidData = this.squidGame?.getSquidGameData?.();
        const remain =  squidData.squidHuntGameSettlement ? squidData.squidHuntGameSettlement?.remainingSquidCounts || 0 : squidData.squidHuntGameInfo?.remainingSquidCounts || 0;
        if (remain === 0) {
            this.scheduleOnce(this.hide.bind(this), 0.20); // In case it's last squid
            return;
        }
        if (squidData) {
            this.squidCounterCircle.active = true;
            this.numLabel.string =String(remain);
           
        } else {
            this.hide();
        }

         
    }

    public syncSquidCounter(_squidCounts: number) {
        if (this.squidGame?.getSquidGameData()?.isSquidRunning()) {
            this.squidCounterCircle.active = true;
            this.numLabel.string = String(_squidCounts);
        }
        else {
            this.hide();
        }
    }

    public hide() {
        this.squidCounterCircle.active = false;
    }

    public get squidCounterCircle () {
        return this.numLabel.node.parent; 
    }

    public squidStatusClick() {
        cv.MessageCenter.send(cv.Enum.SQUID_EVENT.ShowSquidStatus);
    }

}
