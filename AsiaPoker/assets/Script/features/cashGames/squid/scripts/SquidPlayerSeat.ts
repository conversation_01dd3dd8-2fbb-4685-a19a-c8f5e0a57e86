import cv from "../../../../../Script/components/lobby/cv";
import { ShowBBSwitchType, ShowBBTagCom } from "../../../../common/tools/ShowBBTagCom";
import { Seat } from "../../../../components/game/dzPoker/Seat";
import FishingRod from "./effect/FishingRod";
import SquidGameEffect from "./effect/SquidGameEffect";
import SquidGameSeatEffect from "./effect/SquidGameSeatEffect";

const {ccclass, property} = cc._decorator;

const SEAT_SQUID_POSITION = cc.v2(45, 48);
const SEAT_SQUID_POSITION_SELF =  cc.v2(55, 55);
const SEAT_SQUID_POSITION_FLIPPED = cc.v2(-45, 48);

const WIN_NUMBER__POSITION = cc.v2(-40, -18);
const WIN_NUMBER__POSITION_SELF =  cc.v2(-50, -25);
const WIN_NUMBER__POSITION_FLIPPED = cc.v2(-40, -18);

export interface ISeatEffect {
    isSelfSeat() : boolean;
    setSquidCount();
    show(): void;
}

@ccclass
export default class SquidPlayerSeat extends cc.Component implements ISeatEffect {
    @property(cc.Node) public avatarAnimNode: cc.Node = null;
    @property(cc.Node) public lastSquid: cc.Node = null;
    @property(cc.Node) public squid_Hat: cc.Node = null;
    @property(cc.Node) public countHolder: cc.Node = null;
    @property(cc.Node) private label_Gold_Plus: cc.Node = null;
    @property(FishingRod) fishingRod: FishingRod = null;
    @property(cc.SpriteFrame) miniSquid_Standard: cc.SpriteFrame = null; // For the hooked mini squid. Standard (Normal)
    @property(cc.SpriteFrame) miniSquid_SuperSquid: cc.SpriteFrame = null; // For the hooked mini squid. Gold (SS)
    @property(cc.Label) winLooseAmountText: cc.Label = null;
    @property([cc.Font]) fonts: cc.Font[] = [];
    @property(cc.Node) doubleSquidNode: cc.Node = null;
    @property(cc.Label) counterLabel: cc.Label = null;
    @property(cc.ParticleSystem) doubleParticle: cc.ParticleSystem = null;
    @property(cc.SpriteFrame) counterBgFrames: cc.SpriteFrame[] = [];

    public seatEffect: SquidGameSeatEffect = null;
    private _seat: Seat = null;
    private _currSquidCount: number  =  0;
    public get seat(): Seat {
        return this._seat;
    }

    public set currSquidCount(count: number) {
        this._currSquidCount = count;
    }

    public get currSquidCount(): number {
       return this._currSquidCount;
    }

    protected onLoad(): void {
        this.seatEffect = this.node.getComponent(SquidGameSeatEffect);
        cv.tools.addShowBBTagCom(this.winLooseAmountText.node, false);
    }

    addSquidToSeat(seat: Seat) {
        this._seat = seat;
        this.node.parent = seat.node;
        this.node.setSiblingIndex(seat.number_action_panel.getSiblingIndex() - 1);
        this.node.scale /= seat.node.scale;
        this.avatarAnimNode.parent = seat.role_img.node;
        this.avatarAnimNode.setSiblingIndex(0);
        this.lastSquid.parent = seat.role_img.node;
        this.lastSquid.setSiblingIndex(1);
        this.showSquidOnSeat();
    }

    private reloadUI() {
       let _nodeScale = 1;
        if (this.isFlip()) {
            this.node.position = SEAT_SQUID_POSITION_FLIPPED;
            _nodeScale = -1;
            this.winLooseAmountText.node.position = WIN_NUMBER__POSITION_FLIPPED;
        } else if (this._seat.isSelfSeat()) {
            this.node.position = SEAT_SQUID_POSITION_SELF;
            _nodeScale = 1.2;
            this.winLooseAmountText.node.position = WIN_NUMBER__POSITION_SELF;
            this.lastSquid.scale = 1.2;
        } else {
            _nodeScale = 1;
            this.node.position = SEAT_SQUID_POSITION;
            this.winLooseAmountText.node.position = WIN_NUMBER__POSITION;
        }

        this.node.setScale(_nodeScale, Math.abs(_nodeScale));
        this.label_Gold_Plus.setScale( _nodeScale>0 ?1 :-1,1); // Super Squid +2 label
        this.counterLabel.node.setScale( _nodeScale>0 ?1 :-1,1);  // Squid counter label 
        this.winLooseAmountText.node.setScale(_nodeScale>0 ?1 :-1,1);
        this.doubleSquidNode.setScale(_nodeScale>0 ?1 :-1,1);
        this.squid_Hat.setPosition(0,0,0);
        this.squid_Hat.scale = 0;
        this.countHolder.scale = 0;

        if(this.currSquidCount > 0) // Should scale up if the count is more than 0, else first Fishing Rod anim will handle it 
        {
            this.squid_Hat.setScale( 1,1);
            if(this.currSquidCount > 0)
            this.countHolder.setScale(1,1);
        }
    }

    private isFlip() : boolean{
        const seat = this._seat;
        return seat.isFlip && !seat.isSelfSeat();
    }

    public setSquidCount() : void {
        // squid count for sq
        this.counterLabel.string =  this.currSquidCount.toString();
        this.countHolder.active = this.currSquidCount > 0;
        const squidData = cv.GameDataManager?.featureData;
        if(!squidData?.isDoubleSquid) return;
        this.countHolder.getComponent(cc.Sprite).spriteFrame = this.counterBgFrames[ squidData.squidHuntGameParams?.squidMultiplier[this.currSquidCount]?.multiplier >  1 ?  1 : 0];
    }

    showSquidOnSeat() {
        this.hide();
    }

    show() {
        this.node.active = true;
        this.reloadUI();
    }

    hide() {
        this.node.active = false;
    }

    public isSelfSeat(): boolean {
        return this._seat?.isSelfSeat();
    }

    public playWinTextAnimation (msg: any) {
        const playerInfo = msg.players?.find((player)=>player.userid === this._seat?.PlayerInfo?.playerid);
        if(!playerInfo?.winAmount) return; 
        const winAmount = playerInfo.winAmount;
        this._setWinLoseText(winAmount);
        this.reloadUI();
        cc.tween(this.winLooseAmountText.node).parallel(
            cc.tween().to(SquidGameEffect.toTweenTime(0.20), { opacity: 255 }, { easing: cc.easing.sineOut }),
            cc.tween().to(SquidGameEffect.toTweenTime(0.20), { position: new cc.Vec2(this.winLooseAmountText.node.x, this.winLooseAmountText.node.position.y  + 106) }, { easing: cc.easing.sineOut }),
            cc.tween().delay(0.20).to(SquidGameEffect.toTweenTime(0.10), { position: cc.v2(this.winLooseAmountText.node.x, this.winLooseAmountText.node.y + 110) }, { easing: cc.easing.sineOut }),
            cc.tween().delay(1.45).to(SquidGameEffect.toTweenTime(0.15), { opacity: 0 }, { easing: cc.easing.sineOut }))
            .start()
    }
 
    updateCounterForSuperSquid() {
        // when has initially 0 squid and now got 1 super squid  which is equal to 2 squid
        if(this.currSquidCount === 2) {
            this.setSquidCount();
        }else{  
        let initialVal = 1;
        const counter = this.counterLabel;
        this.schedule(()=>{
            initialVal++;
            counter.string = Math.min(initialVal, 9).toString();
                if(initialVal >= 8) {
                   this.setSquidCount();
                }
        }, 0, 10, 0);
      }
   }

    lastSquidAvatarAnim() {
        if (!this.seat.head_panel_self?.active && !this.seat.head_panel.active) return; // Do not show in case of player turn and avatar hidden
        this.lastSquid.active = true;
        this.lastSquid.getComponent(cc.Animation)?.play("LastSquid_Avatar_Lobby");
    }

    playAvatarAnim() {
        if (!this.seat.head_panel_self?.active && !this.seat.head_panel.active) return; // Do not show in case of player turn and avatar hidden
        const anim = {
            self: "big",
            other: "Other players"
        }
        this.avatarAnimNode.active = true;
        const isSelf = this._seat.PlayerInfo.playerid === cv.dataHandler.getUserData().u32Uid;
        this.avatarAnimNode.getComponent(sp.Skeleton)?.setAnimation(0, isSelf ? anim.self : anim.other, false);
    }


    hideAvatarAnim() {
        this.avatarAnimNode.active = false;
        this.lastSquid.active = false;
    }

   private _setWinLoseText(winAmount: number) {
        const [looseFont, winFont] = this.fonts;
        this.winLooseAmountText.font = winAmount > 0 ? winFont :  looseFont;
        const transferAmount = cv.tools.showChipAsBBorAnte() ? winAmount : Math.round(winAmount);
        let prefix = winAmount > 0 ? "+" : "";
        this.winLooseAmountText.getComponent(ShowBBTagCom).setTag(transferAmount, ShowBBSwitchType.NumToFloatStr, prefix).updateLabel();
   }
}
