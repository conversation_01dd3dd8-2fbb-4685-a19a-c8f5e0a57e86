import game_protocol = require("../../../../common/pb/gs_protocol");
import game_pb = game_protocol.protocol;
import cv from "../../../../components/lobby/cv";
import SquidGame from "./SquidGame";
import { CurrencyType, LANGUAGE_TYPE } from "../../../../common/tools/Enum";
import SquidGracePeriod from "./SquidGracePeriod";
import GameTableInfo from "../../../../components/game/dzPoker/GameTableInfo";
import { InGameMessageKey, InGameMessage ,InGameMessageType } from "../../../../components/game/dzPoker/BaseGameTableMessage";

const {ccclass, property} = cc._decorator


@ccclass
export default class SquidInGameMessage extends cc.Component {
    squidGame: SquidGame;
    @property({ type: SquidGracePeriod }) squidGracePeriod: SquidGracePeriod = null;
    private showWelcomeOneTime:boolean=true;
    private looserMsgActive: boolean = false;
    @property(cc.Label) reveal_txt: cc.Label = null;
    @property(cc.Node) handReveal: cc.Node = null;    

    onLoad() {
        this.showWelcomeOneTime=true;
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.ResponseJoinSquidHuntGame, this.onResponseJoinSquidHuntGame.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGame, this.onStartSquidHuntGameNotice.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeSquidHuntRefund, this.onNoticeSquidHuntRefund.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.OnForceShowCard, this.OnForceShowCard.bind(this), this); 
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.OnSitdownSucc, this.OnSitdownSucc.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeGameSettlement, this.onNoticeGameSettlement.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeWaitingOtherPlayerRebuyIn, this.OnOtherPlayerRebuyInNotice.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGameGracePeriod, this.onSquidGracePeriodNotice.bind(this), this);
        cv.MessageCenter.register("control_button_status_updated", this.onControlBtnStatusUpdate.bind(this), this);

    }

    initialise (squidGame: SquidGame) {
        this.squidGame = squidGame;
        this.node.active  =true; 
    }

    protected onDestroy(): void {
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.ResponseJoinSquidHuntGame, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGame, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeSquidHuntRefund,  this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.OnForceShowCard, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.OnSitdownSucc, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeWaitingOtherPlayerRebuyIn, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGameGracePeriod, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeGameSettlement, this);
        cv.MessageCenter.unregister("control_button_status_updated", this);
    }

    public onStartSquidHuntGameNotice(msg: game_pb.NoticeStartSquidHuntGame) {
            this.removeTimer();
            this.removeGracePeriod();
    }

    removeGracePeriod(){
        this.squidGracePeriod?.removeTimer();
        
    } 

    removeTimer () {
        this.squidGame?.gameScene?.gameMain_panel?.getComponent(GameTableInfo)?.tableMsgs?.removeTimer();
    }

    OnOtherPlayerRebuyInNotice(msg: game_pb.NoticeWaitingOtherPlayerRebuyIn) {
        console.error("  OnOtherPlayerRebuyInNotice  "+JSON.stringify(msg));
        if (this.squidGame) {
            this.squidGame.getSquidGameData().serverTime = msg.serverTime;
            this.squidGame.getSquidGameData().rebuyInTimeoutTime = msg.rebuyInTimeoutTime;
            if (!msg.players.includes(this.squidGame.getSelfUserID())) {
                const seconds = Math.floor(msg.rebuyInTimeoutTime - msg.serverTime);
                // this.showTimerForSeconds(seconds, cv.config.getStringData("Squid_Waiting_For_Player_To_Rebuy"));
                const waitingForPlayerMsg: InGameMessage = {
                    type: InGameMessageType.MessageWithTimer,
                    duration: seconds,
                    content: cv.config.getStringData("Squid_Waiting_For_Player_To_Rebuy"),
                  };
                  
                cv.MessageCenter.send(InGameMessageKey, waitingForPlayerMsg)
            }
        }
    }

    OnForceShowCard(pkForceShowCard: game_pb.NoticeForceShowCard) {
        if (!pkForceShowCard.isFromSquid) return;
        const settlementData = this.squidGame?.getSquidGameData()?.squidHuntGameSettlement;
        if (settlementData && settlementData.whoGetSquid === this.squidGame?.getSelfUserID()) { // ensures that both of toast messages are private and only visible to the player who wins the Squid.
            const str = this.squidGame?.getSquidGameData().checksIfItsFirstSquid() ? "Squid_Random_Hand_Card_Reveal_first" : "Squid_Random_Hand_Card_Reveal";
            this.reveal_txt.string = cv.config.getStringData(str);
            this.handReveal.stopAllActions();
            const gameIcon = this.squidGame.gameScene.gameIcon_img
            const gameIconPos = this.node.convertToNodeSpaceAR(gameIcon.parent.convertToWorldSpaceAR(gameIcon.position));
            this.handReveal.position = gameIconPos;
            this.handReveal.active = true;
            this.handReveal.runAction(cc.sequence(cc.show(), cc.scaleTo(0.1, 1.0), cc.delayTime(2.4), cc.hide()));
        }
    }

    onControlBtnStatusUpdate() {
        if (this.looserMsgActive) return;
        const refundNode = this.squidGame?.gameScene?.gameMain_panel.getComponent(GameTableInfo)?.tableMsgs?.msgLabelContainer;
        const actionBtn = this.squidGame?.gameScene?.gameMain?.actionButtonView;
        if (!refundNode.active || refundNode.opacity === 0 || (!this.squidGame?.gameScene?.gameMain?.dichi_button1?.node.active)) return;
        const nodePos = this.node.convertToNodeSpaceAR(actionBtn.parent.convertToWorldSpaceAR(actionBtn.position));
        refundNode.position = new cc.Vec2(0, nodePos.y + actionBtn.height + 56); // Offset to avoid overlapping
    }


        
    public onResponseJoinSquidHuntGame( msg: game_pb.ResponseJoinSquidHuntGame) {
        const startTime = msg.gameStartTime;
        if(!startTime) return;
        // this.showTimerForSeconds(Math.floor(startTime - msg.serverTime), cv.config.getStringData("Squid_Waiting_For_Player"));
        const waitingForPlayerMsg: InGameMessage = {
            type: InGameMessageType.MessageWithTimer,
            duration: Math.floor(startTime - msg.serverTime),
            content: cv.config.getStringData("Squid_Waiting_For_Player"),
          };
          
        cv.MessageCenter.send(InGameMessageKey, waitingForPlayerMsg)
    }

    get selfSeatPosInThisNode () : cc.Vec2 {
        const selfSeat = this.squidGame?.gameScene?.gameMain?.seatList.sort((a, b)=> a.node?.y - b.node?.y)?.[0]; // Bottom seat
        if(!selfSeat) return null;
        const worldPos = selfSeat.node.parent.convertToWorldSpaceAR(selfSeat.node.position);
        return this.node.convertToNodeSpaceAR(worldPos);
    }

    OnSitdownSucc (playerId: any) {
        if(Number(playerId) === Number(cv.dataHandler.getUserData().u32Uid)) this.showWaitingForNextGameMsg();
    }


    onNoticeSquidHuntRefund(msg: game_pb.NoticeSquidHuntRefund) {
        if (!msg) return;

        const refundTypeKey = `SquidHuntRefundType_${msg.refundType}`;
        let str = cv.config.getStringData(refundTypeKey);
        if (msg.refundType === 0) {
            const currencyLabelKey = cv.GameDataManager.tRoomData.currency === CurrencyType.USDT
                ? "Buyin_usd_balance_label"
                : "Buyin_coin_balance_label";
            let currency = cv.config.getStringData(currencyLabelKey);
            if (cv.config.getCurrentLanguage() !== LANGUAGE_TYPE.zh_CN) {
                currency = ` ${currency}`;
            }
            const amount = `${cv.StringTools.numToFloatString(msg?.amount)}${currency}`;
            const totalPlayer = this.squidGame.getSquidGameData().squidHuntGameInfo?.registeredPlayers.length;
            str = cv.StringTools.formatC(str, totalPlayer, amount);
        }
        const refundMsg: InGameMessage = {
            type: InGameMessageType.NormalText,
            duration: 2.4,
            content: str,
          };
          
        cv.MessageCenter.send(InGameMessageKey, refundMsg)
    }

    private onNoticeGameSettlement(msg : game_pb.NoticeGameSettlement){
        if(!this.squidGame?.getSquidGameData()?.squidHuntGameInfo?.isStarted || msg?.squidHuntGameSettlement?.whoGetSquid) return;
        // this.showInFloorMsg(cv.config.getStringData("Squid_No_Squid_For_Joint_Wining"));
        const setlementMsg: InGameMessage = {
            type: InGameMessageType.NormalText,
            duration: 2.4,
            content: cv.config.getStringData("Squid_No_Squid_For_Joint_Wining"),
          };
          
        cv.MessageCenter.send(InGameMessageKey, setlementMsg)
       
    }

    showWaitingForNextGameMsg () {
        // isGameStarted and not a registered player.
        if(!this.squidGame?.getSquidGameData()?.squidHuntGameInfo?.isStarted || this.squidGame.getSquidGameData()?.isRegisteredPlayer()) return;
            // this.showInFloorMsg(cv.config.getStringData("Squid_Wait_For_Next_Round"));
            const waitMsg: InGameMessage = {
                type: InGameMessageType.NormalText,
                duration: 2.4,
                content: cv.config.getStringData("Squid_Wait_For_Next_Round"),
              };
              
            cv.MessageCenter.send(InGameMessageKey, waitMsg)
            this.squidGame.waitingOnceOnSnapshot = false;
    }

    onSquidGracePeriodNotice(msg: game_pb.NoticeStartSquidHuntGameGracePeriod) {
        if(this.squidGame === null ) return;
        let strMsg =  cv.config.getStringData("Squid_grace_specatator_desc");
        if (msg.lastSitPlayerUid === this.squidGame?.getSelfUserID()) {
            strMsg = msg.isLastPlayerWinSquid ? cv.config.getStringData("Squid_grace_won_desc") : cv.config.getStringData("Squid_grace_without_won_desc");
        }
        const seconds = Math.floor(msg.gracePeriodTimeoutTime - msg.serverTime);
        this.squidGracePeriod?.showCountDown(seconds, strMsg);
    }
    
    showWelcomeSquidGameMsg(playerCount: number) {
        if (this.squidGame.getSquidGameData()?.isRegisteredPlayer()) return;
        if (this.showWelcomeOneTime && cv.GameDataManager.tRoomData.i32SelfSeat === -1) {
            this.showWelcomeOneTime = false;
            const pos = new cc.Vec2(0, this.node.getContentSize().height * 0.30);
            const isGameStarted = this.squidGame?.getSquidGameData()?.squidHuntGameInfo?.isStarted;
            let str;
            if (!isGameStarted)
                str = cv.StringTools.formatC(cv.config.getStringData("Squid_Welcom_Squid_Game_msg"), playerCount); // this.showInFloorMsgWithPos(cv.StringTools.formatC(cv.config.getStringData("Squid_Welcom_Squid_Game_msg"),playerCount),pos);
            else
                str = cv.StringTools.formatC(cv.config.getStringData("Squid_Welcom_Squid_Game_msg_When_Game_Running"))// this.showInFloorMsgWithPos(cv.StringTools.formatC(cv.config.getStringData("Squid_Welcom_Squid_Game_msg_When_Game_Running")),pos);


            const welcomeMsg: InGameMessage = {
                type: InGameMessageType.NormalText,
                duration: 2.4,
                content: str,
                position: pos
            };
            cv.MessageCenter.send(InGameMessageKey, welcomeMsg)
            const msgLabelContainer = this.squidGame?.gameScene?.gameMain_panel.getComponent(GameTableInfo)?.tableMsgs?.msgLabelContainer;
            msgLabelContainer.anchorY = 1;
        }
    }

    showOnlyPlayerLeftNotice() {
        const looser = cv.GameDataManager.featureData?.looser;
        const looser1 = this.squidGame?.squidSeats?.[looser[0]]?.seat?.PlayerInfo?.name;
        const looser2 = this.squidGame?.squidSeats?.[looser[1]]?.seat?.PlayerInfo?.name;
        if (!looser1 || !looser2) return;
        const pos: cc.Vec2 = new cc.Vec2(0, this.node.getContentSize().height * 0.16); 
        const lastTwoPlayer: InGameMessage = {
            type: InGameMessageType.RichText,
            duration: 2.4,
            content: cv.StringTools.formatC(cv.config.getStringData("Squid_Last_Two_Player_Message"),  cv.StringTools.truncateString(looser1, 8), cv.StringTools.truncateString(looser2, 8)),
            position: pos
        };
        cv.MessageCenter.send(InGameMessageKey, lastTwoPlayer)
        this.looserMsgActive = true;
        this.scheduleOnce(() => { // Pos  shouldn't be update based on control status
            this.looserMsgActive = false;
        }, 2.4);
    }
}

