import cv from "../../../../../components/lobby/cv";
import game_protocol = require('../../../../../common/pb/gs_protocol');
import game_pb = game_protocol.protocol;

export class Squid_GameNet {
    constructor() {
        this.registerMsg()
    }

    registerMsg() {
        const registerMsg = cv.gameNet.registerMsg.bind(cv.gameNet);
        registerMsg(game_pb.MSGID.MsgID_JoinSquidHuntGame_Response, this.onResponseJoinSquidHuntGame.bind(this));
        registerMsg(game_pb.MSGID.MsgID_RegisterSquidHuntGame_Notice, pbbuf =>this.withRoomIdCheck(pbbuf, cv.Enum.SQUID_EVENT.NoticeRegisterSquidHuntGame));
        registerMsg(game_pb.MSGID.MsgID_JoinSquidHuntGame_Notice, pbbuf => this.withRoomIdCheck(pbbuf, cv.Enum.SQUID_EVENT.NoticeJoinSquidHuntGame));
        registerMsg(game_pb.MSGID.MsgID_StartSquidHuntGameFailed_Notice, pbbuf => this.withRoomIdCheck(pbbuf,  cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGameFailed));
        registerMsg(game_pb.MSGID.MsgID_StartSquidHuntGame_Notice, pbbuf => this.withRoomIdCheck(pbbuf, cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGame));
        registerMsg(game_pb.MSGID.MsgID_SquidHuntRefund_Notice, pbbuf => this.withRoomIdCheck(pbbuf,  cv.Enum.SQUID_EVENT.NoticeSquidHuntRefund));
        registerMsg(game_pb.MSGID.MsgID_FinalSquidHuntGame_Notice, pbbuf => this.withRoomIdCheck(pbbuf, cv.Enum.SQUID_EVENT.SquidHuntGameFinalSettlement));
        registerMsg(game_pb.MSGID.MsgID_Waiting_OtherPlayer_RebuyIn, pbbuf => this.withRoomIdCheck(pbbuf, cv.Enum.SQUID_EVENT.NoticeWaitingOtherPlayerRebuyIn));
        registerMsg(game_pb.MSGID.MsgID_StartSquidHuntGameGracePeriod_Notice, pbbuf => this.withRoomIdCheck(pbbuf, cv.Enum.SQUID_EVENT.NoticeStartSquidHuntGameGracePeriod));
    }

    destroy() {
        const unregisterMsg = cv.gameNet.unregisterMsg.bind(cv.gameNet);
        unregisterMsg(game_pb.MSGID.MsgID_RegisterSquidHuntGame_Notice);
        unregisterMsg(game_pb.MSGID.MsgID_StartSquidHuntGameFailed_Notice);
        unregisterMsg(game_pb.MSGID.MsgID_JoinSquidHuntGame_Notice);
        unregisterMsg(game_pb.MSGID.MsgID_JoinSquidHuntGame_Response);
        unregisterMsg(game_pb.MSGID.MsgID_StartSquidHuntGame_Notice);
        unregisterMsg(game_pb.MSGID.MsgID_SquidHuntRefund_Notice);
        unregisterMsg(game_pb.MSGID.MsgID_FinalSquidHuntGame_Notice);
        unregisterMsg(game_pb.MSGID.MsgID_Waiting_OtherPlayer_RebuyIn);
        unregisterMsg(game_pb.MSGID.MsgID_StartSquidHuntGameGracePeriod_Notice);
    }

    private withRoomIdCheck(pbbuf, eventName: string ) {
       const msg =  cv.gameNet.decodePB(eventName, pbbuf)
        cc.log("SquidGame" + eventName, msg);
        if (msg.roomid === cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send(eventName, msg);
        }
    }

    public onRequestJoinSquidHuntGame(_isApply : boolean) {  //  true : join register list, false: leave squid hunt game
        const RequestJoinSquidGame = cv.gamePB.lookupType(cv.Enum.SQUID_EVENT.RequestJoinSquidHuntGame);
        const sendGameMsg: object = { roomid: cv.GameDataManager.tRoomData.u32RoomId , isApply : _isApply};
       cc.log("SquidGame requestJoinSquidGame", sendGameMsg);
        const pbbuf = RequestJoinSquidGame.encode(sendGameMsg).finish();
        cv.gameNet.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_JoinSquidHuntGame_Request, cv.GameDataManager.tRoomData.u32RoomId);
    }

    public onResponseJoinSquidHuntGame(pbbuf, msgid, headerServertype, headerServerid, headerRoomid) {
        const msg: game_pb.ResponseJoinSquidHuntGame = cv.gameNet.decodePB(cv.Enum.SQUID_EVENT.ResponseJoinSquidHuntGame, pbbuf);
       cc.log("SquidGame onResponseJoinSquidHuntGame", msg, msgid, headerRoomid);
        if (msg && msg.error === 1) {
            cv.MessageCenter.send(cv.Enum.SQUID_EVENT.ResponseJoinSquidHuntGame, msg);
        }
    }
}

