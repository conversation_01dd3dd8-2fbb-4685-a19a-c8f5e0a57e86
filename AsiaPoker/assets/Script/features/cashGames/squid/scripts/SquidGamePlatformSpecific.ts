import cv from "../../../../../../assets/Script/components/lobby/cv";
import { CashGameFeatureCreator, CashGameFeatureType } from "../../CashGameFeature";
import SquidGame from "./SquidGame";
import { SquidGameData } from "./SquidGameData";
import { CurrencyType, LANGUAGE_TYPE } from "../../../../../Script/common/tools/Enum";

const {ccclass} = cc._decorator;

@ccclass
export class SquidGamePlatformSpecific extends SquidGame {

    protected onDestroy(): void {
        super.onDestroy();
        CashGameFeatureCreator.removeFeature(CashGameFeatureType.SquidGame);
    }

    playSound(soundName: string) {
        cv.AudioMgr.playButtonSound(soundName);
    }

    requestSnapShot() {
        cv.gameNet.RequestSnapshot(cv.roomManager.getCurrentRoomID());
    }

    hideLoadingScreen() {
        // hide loading screen if needed
    }

    leaveTable() {
        cv.roomManager.RequestLeaveRoom();
    }

    sitOut(){
       // cv.gameNet.requestJoinSquidGame(gameData.tRoomData.u32RoomId,true); // Request join Squid game with Sit Out option
    }

    isAutoJoinSquidGame(): boolean {
        return cv.tools.isAutoJoinSquidGame();
    }

    turnOnAutoJoinSquidGame() {
        cv.tools.setAutoJoinSquidGame(true);
    }

    getSelfUserID() : number {
        return cv.dataHandler.getUserData().u32Uid;
    }

    getFeatureData(): SquidGameData {
        // eslint-disable-next-line prefer-object-spread
        return Object.create(Object.getPrototypeOf(this.squidData), Object.getOwnPropertyDescriptors(this.squidData));
    }

    getCurrencyString(): string{
        let currencyString = cv.GameDataManager.tRoomData.currency === CurrencyType.USDT ?  cv.config.getStringData("USD_Label_Display") : cv.config.getStringData("USDTView_coin_label");
        currencyString = " "+currencyString;
        return currencyString;
    }
}