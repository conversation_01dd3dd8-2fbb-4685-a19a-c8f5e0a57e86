import game_protocol = require("./../../../../../Script/common/pb/gs_protocol");
import game_pb = game_protocol.protocol;
import { CurrencyType, LANGUAGE_TYPE } from "../../../../common/tools/Enum";
import SquidGame from "./SquidGame";
import cv from "../../../../components/lobby/cv";

const { ccclass, property } = cc._decorator;

@ccclass
export default class StartSquidPopup extends cc.Component {

    @property(cc.Node) private pages: cc.Node[] = [];

    @property(cc.RichText) private pageDesc_1: cc.RichText = null;
    
    @property(cc.Button) private btnNextPage: cc.Button = null;
    @property(cc.Button) private btnPrevPage: cc.Button = null;
    
    @property(cc.Label) private labelTimeLeft: cc.Label = null;
    @property(cc.Label) private labelJoinSquid: cc.Label = null;
    @property(cc.Label) private labelConfirm: cc.Label = null;
    
    @property(cc.Toggle) private toggleAutoJoin: cc.Toggle = null;
    @property(cc.Node) private item: cc.Node = null;
    @property(cc.Node) private content: cc.Node = null;
    @property(cc.ProgressBar) private tab_bars: cc.ProgressBar[] = [];

    @property(cc.Sprite) private page1_sprite: cc.Sprite = null;
    @property(cc.SpriteFrame) private startSquid_imgs: cc.SpriteFrame[] = []; // 0 : Squid hunt - EN, 1 : Squid Hunt - CH, 2 : Double Squid - EN, 3 : Double Squid - CH
    @property(cc.Label) private lblGoldCoinLooser:cc.Label=null;

    private _curPageIndex: number = 0;

    private barProgressAnim: cc.Tween = null;
    private stopBarPorgress: boolean = false;
    private fadeIn_Anim: cc.Tween = null;
    private fadeOut_Anim: cc.Tween = null;

    
    private get totalPage() {
        return this.pages.length;
    }

    private data: { deposit: number, endWaitingTimestamp: number, squidValue:{ [k: string]: number }} = { deposit: 10, endWaitingTimestamp: 0 , squidValue: null};
    private squidGame: SquidGame = null;

    protected onLoad(): void {
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeRegisterSquidHuntGame, this.onRegisterSquidHuntGameNotice.bind(this), this);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.NoticeSpanshotRoomInfo, this.onNoticeGameSnapShot.bind(this), this);
        // set page
        this.setPage(0);
    }

    protected onEnable(): void {
        cv.MessageCenter.send("action_review_hide");
    }

    private onNoticeGameSnapShot(result: any) {
       const msg  =  result.squidHuntGameInfo;
       if(!msg) return;
        const remainSec = msg.gameStartTime - (Date.now()/1000);
        if(remainSec <= 0 || msg.isStarted ||  !result.squidHuntGameInfo.unRegisteredPlayers.includes( cv.dataHandler.getUserData().u32Uid)) return;
        const registerInfo = new game_pb.NoticeRegisterSquidHuntGame();
        registerInfo.roomParams = result?.squidHuntGameParams;
        registerInfo.needFreezeDeposit = msg?.totalDeposit;
        registerInfo.gameStartTime = msg?.gameStartTime;
        registerInfo.serverTime = result?.serverTime;
        this.onRegisterSquidHuntGameNotice(registerInfo);
    }

    protected onDestroy(): void {
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeSpanshotRoomInfo, this);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.NoticeRegisterSquidHuntGame, this);
    }

    public init(squidGame:SquidGame){
        this.squidGame=squidGame;
    }

    /**
     * Message squid game join notice from server
     */
    public onRegisterSquidHuntGameNotice(msg: game_pb.NoticeRegisterSquidHuntGame) {
        this.squidGame?.gameScene?.gameMain?.resetTable();
        this.squidGame?.resetSquidElements();
        this.squidGame.getSquidGameData().squidHuntGameParams = msg.roomParams;
        cv.TP.hideTipsPanel();
        this.data.deposit =cv.StringTools.clientGoldByServer(msg.needFreezeDeposit);
        if (this.squidGame.isAutoJoinSquidGame() && this.squidGame.checkIsSufficientFund(this.data.deposit)) 
            this.squidGame.getSquidGameNet().onRequestJoinSquidHuntGame(true);
        else this.show(msg);
    }

    show(msg: game_pb.NoticeRegisterSquidHuntGame) {
        // fill data from game data instance
        const squidGameParams=this.squidGame.getSquidGameData().squidHuntGameParams;
        this.data.squidValue=squidGameParams.squidValue;
        this.data.endWaitingTimestamp =  (Date.now() / 1000) + msg.gameStartTime - msg.serverTime;
        // load remaining time
        this.schedule(this.scheduleWaitingTime, 1, cc.macro.REPEAT_FOREVER, 0);
        this.scheduleWaitingTime();
        this.setCapLimit();
        this.node.active = true;
        this.stopBarPorgress = false;

        this.toggleAutoJoin.isChecked=cv.tools.isAutoJoinSquidGame();
        this.setStartSquidImage(squidGameParams.mode);
        this.pages.forEach((page, _index) => {
            page.active = _index === 0;
            page.opacity = 255;
        });
        this.setTabBarUI();
        this.setPage(0);
        
        if(cv.config.getCurrentLanguage() === LANGUAGE_TYPE.yn_TH) this._updatePopupSize();
        const currencyString = this.squidGame.getCurrencyString();
        this.pageDesc_1.string = cv.StringTools.formatC(cv.config.getStringData(cv.GameDataManager.tRoomData.currency === CurrencyType.USDT?"Squid_Start_Page_Desc_1_USD":"Squid_Start_Page_Desc_1"), this.data.deposit+currencyString);
        this.lblGoldCoinLooser.string=cv.config.getStringData(cv.GameDataManager.tRoomData.currency === CurrencyType.USDT?"Squid_Start_Gold_Loser_USD":"Squid_Start_Gold_Loser");

        if (this.squidGame.checkIsSufficientFund(this.data.deposit)) {
            // If the Buy-in value is enough with other currency
            this.labelConfirm.string=cv.config.getStringData("Squid_Start_Join_CTA");
            this.labelJoinSquid.node.active=true;
            this.labelJoinSquid.string= cv.StringTools.formatC(cv.config.getStringData("Squid_Start_Join_Deposit"), this.data.deposit+currencyString);
        }
        else
        {
            // If both currencies are not enough for Buy-In value.
            this.labelConfirm.string=cv.config.getStringData("Make_A_Deposit");
            this.labelJoinSquid.node.active=false;
        }
        cv.MessageCenter.send("hideRoleInfoView");
    }

    private setPage(index: number, _fadOut : boolean =false ) {
        if (index < 0 || index >= this.totalPage) {
            return;
        }
        this._curPageIndex = index;
        this.toggleAutoJoin.node.parent.active = this._curPageIndex !== 3; // It should disable for Rules page
        if (_fadOut) {
            this.switchPage_Anim();
            return;
        }
        if(this.fadeIn_Anim) this.fadeIn_Anim.stop();
        if(this.fadeOut_Anim) this.fadeOut_Anim.stop();
        this.setTabBarUI();
        this.pages.forEach((page, _index) => {
            page.active = _index === this._curPageIndex;
            page.opacity = 255;
        });

        this.btnNextPage.interactable = index < this.totalPage - 1;
        this.btnPrevPage.interactable = index > 0;
    }

    private nextPage() {
        this.stopBarPorgress = true;
        this.setPage(this._curPageIndex + 1);
    }

    private prevPage() {
        this.stopBarPorgress = true;
        this.setPage(this._curPageIndex - 1);
    }


    private setCapLimit()
    {
        const length = Object.keys(this.data.squidValue).length;
        if(this.content.childrenCount>=length)
            return;
        this.content.destroyAllChildren();
        for(let i= 1 ; i<= length; i++)
        {
            const node = cc.instantiate(this.item);
            node.active=true;
            node.getComponent(cc.Sprite).enabled = (i%2 !== 0);
            node.getChildByName("lblSC").getComponent(cc.Label).string=i+"";
            node.getChildByName("lblGCFL").getComponent(cc.Label).string=cv.StringTools.clientGoldByServer(this.data.squidValue[i])+"";
            node.parent = this.content;
        }
    }

    private onLeaveClicked() {
        this.squidGame.playSound('button_click');
        this.standUp();
    }

    private onJoinClicked() {
        // play sound
        this.squidGame.playSound('button_click');

        if (this.squidGame.checkIsSufficientFund(this.data.deposit)) {
            this.joinTable();
        }
        else
            this.squidGame.onGoToDepositClick();
    }

    private standUp()
    {
        cv.gameNet.RequestStandup(cv.GameDataManager.tRoomData.u32RoomId);
        this.hide();
    }

    private joinTable() {
        if (this.toggleAutoJoin.isChecked) {
            // save to settings
            this.squidGame.turnOnAutoJoinSquidGame();
        }
        this.squidGame.getSquidGameNet().onRequestJoinSquidHuntGame(true);  //  true : join register list, false: leave squid hunt game
        this.hide();
    }

    private scheduleWaitingTime() {
        const leftSecond = Math.ceil(((this.data.endWaitingTimestamp - Date.now() / 1000)) - 1);
        if (leftSecond <= 0) {
            this.unschedule(this.scheduleWaitingTime);
            this.standUp();
        }
        this.labelTimeLeft.string = cv.StringTools.formatC(cv.config.getStringData("Squid_Start_Time_Left"), `${leftSecond}`);
    }

    private hide() {
        this.node.active = false;
        if (this.barProgressAnim) {
            this.barProgressAnim.stop();
        }
        if(this.fadeOut_Anim) this.fadeOut_Anim.stop();
        if(this.fadeIn_Anim) this.fadeIn_Anim.stop();
        this.unschedule(this.scheduleWaitingTime);
    }

    private setTabBarUI() {
        if (this.barProgressAnim) {
            this.barProgressAnim.stop();
        }

        this.tab_bars.forEach((bar, index) => {
            bar.progress = index < this._curPageIndex || (this.stopBarPorgress && index <= this._curPageIndex) ? 1 : 0;
        });

        // Start the progress animation if not stopped
        if (!this.stopBarPorgress) {
            this.startTabProgress(this._curPageIndex);
        }
    }

    private startTabProgress(tabIndex: number) {
        // Reset the progress bar for the current tab
        const progressBar = this.tab_bars[tabIndex];
        progressBar.progress = 0;
        this.pages[this._curPageIndex].opacity = 255;
        // Animate the progress bar
        this.barProgressAnim = cc.tween(progressBar)
            .to(4, { progress: 1 })
            .call(() => {
                // Move to the next tab when the current one is done
                if (this._curPageIndex < this.tab_bars.length) {
                    this.setPage(this._curPageIndex+1 ,true);
                }
            })
            .start();
    }

    private switchPage_Anim() {

        const currentPage = this.pages[this._curPageIndex - 1];
        const newPage = this.pages[this._curPageIndex];
        if (this.fadeIn_Anim) this.fadeIn_Anim.stop();
        if (this.fadeOut_Anim) this.fadeOut_Anim.stop();

        this.fadeOut_Anim = cc.tween(currentPage)
            .to(0.25, { opacity: 0 }, { easing: 'sineOut' }) // Fade out current page
            .call(() => {
                currentPage.active = false; // Deactivate the current page after fade out
                currentPage.opacity = 255;
                this.btnNextPage.interactable = this._curPageIndex < this.totalPage - 1;
                this.btnPrevPage.interactable = this._curPageIndex > 0;
                if (this._curPageIndex < 4) this.setTabBarUI();
            })
            .start();

            this.fadeIn_Anim = cc.tween(newPage)
            .set({ active: true, opacity: 0 }) // Activate new page and set opacity to 0
            .to(0.25, { opacity: 255 }, { easing: 'sineIn' }) // Fade in new page
            .start();
    }

    private _updatePopupSize()
    {
        this.pages[0].parent.height = 1110;
        this.pages[0].parent.parent.height = 1526;
        this.content.parent.height = 950;
        this.pages[0].parent.parent.getComponent(cc.Layout).updateLayout();
    }

    
    onClickAutoJoin()
    {
        cv.tools.setAutoJoinSquidGame(this.toggleAutoJoin.isChecked);
        this.stopBarPorgress = true;
        this.setPage(this._curPageIndex);
    }

    setStartSquidImage(squidMode: game_pb.SquidHuntGameMode) {
        let index: number;
        if (squidMode === game_pb.SquidHuntGameMode.MULTIPLIER_MODE) {
            index = cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? 3 : 2;
        } else {
            index = cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? 1 : 0;
        }
        // Set the sprite frame.
        this.page1_sprite.spriteFrame = this.startSquid_imgs[index];  // 0 : Squid hunt - EN, 1 : Squid Hunt - CH, 2 : Double Squid - EN, 3 : Double Squid - CH
    }
}
