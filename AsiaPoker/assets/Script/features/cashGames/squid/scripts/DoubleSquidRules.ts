import cv from "../../../../components/lobby/cv";
import SquidGame from "./SquidGame";
import { CurrencyType } from "../../../../common/tools/Enum";

const {ccclass, property} = cc._decorator;


@ccclass
export default class DoubleSquidRules extends cc.Component {
    @property(cc.Node) sHBonusItem: cc.Node = null;
    @property(cc.Node) dSMultiplierItem: cc.Node = null;
    @property(cc.Node) game_instruction: cc.Node = null;
    @property(cc.Node) double_squid_instruction: cc.Node = null;
    @property(cc.Label) lbl_coinFromLoser: cc.Label = null;

    private squidGame:SquidGame=null;

    initialise (squidGame:SquidGame) {
        this.squidGame=squidGame;
    }

    public updateNormalBonusItem () {
        this.lbl_coinFromLoser.string=cv.config.getStringData(cv.GameDataManager.tRoomData.currency === CurrencyType.USDT ? "Squid_Start_Gold_Loser_USD":"Squid_Start_Gold_Loser");
        const squidValue = this.squidGame.getSquidHuntGameParams()?.squidValue;
        if(!squidValue || this.sHBonusItem.parent.childrenCount>2)
            return;
        const entries = Object.entries(squidValue);
        this.sHBonusItem.getChildByName("squidCount").getComponent(cc.Label).string  = entries[0][0].toString();
        this.sHBonusItem.getChildByName("gold_coins").getComponent(cc.Label).string = cv.StringTools.clientGoldByServer(entries[0][1])+"";
        for(let i= 1 ; i < entries.length; i++)
        {   const [squidCount, winningVal] = entries[i];
            const node = cc.instantiate(this.sHBonusItem);
            node.getChildByName("squidCount").getComponent(cc.Label).string  = squidCount.toString();
            node.getChildByName("gold_coins").getComponent(cc.Label).string = cv.StringTools.clientGoldByServer(winningVal)+"";
            node.parent = this.sHBonusItem.parent;
        }
    }

    
    public updateMultiplierBonusItem () {
        if(!this.squidMultiplier || this.dSMultiplierItem.parent.childrenCount>2)
            return;
        this.dSMultiplierItem.getChildByName("squidCount").getComponent(cc.Label).string  = this.getSquidRange(`${this.squidMultiplier[0].min}-${this.squidMultiplier[0].max}`);
        this.dSMultiplierItem.getChildByName("gold_coins").getComponent(cc.Label).string = this.getMultiplierString(this.squidMultiplier[0].multiplier);
        for(let i= 1 ; i < this.squidMultiplier.length; i++){
            const node = cc.instantiate(this.dSMultiplierItem);
            node.getChildByName("squidCount").getComponent(cc.Label).string  = this.getSquidRange( `${this.squidMultiplier[i].min}-${this.squidMultiplier[i].max}`);
            if(i === this.squidMultiplier.length -1) // For last Index
                node.getChildByName("squidCount").getComponent(cc.Label).string  =  cv.StringTools.formatC(cv.config.getStringData("Squid_max_multiplier"), this.squidMultiplier[i].min);
            node.getChildByName("gold_coins").getComponent(cc.Label).string = this.getMultiplierString(this.squidMultiplier[i].multiplier);
            node.parent = this.dSMultiplierItem.parent;
        }
    }

    getSquidRange (rangeStr: string) {
        switch(cv.config.getCurrentLanguage()){
            case cv.Enum.LANGUAGE_TYPE.yn_TH:
                return rangeStr;
            case cv.Enum.LANGUAGE_TYPE.zh_CN:
                return rangeStr + cv.config.getStringData("squid_counts");
            default: 
            return rangeStr +" "+ cv.config.getStringData("squid_squids");
        }
    }

    getMultiplierString(multiplier : number){
        if(multiplier === 1) return cv.config.getStringData("Squid_no_multiplier");
        return cv.StringTools.formatC(cv.config.getStringData("Squid_multiplier"), multiplier);
    }

    get squidMultiplier() {
        return this.squidGame.getSquidGameData().squidMultiplier;
    }

    show () {
        this.getComponent(cc.ScrollView)?.scrollToTop();
        if(!this.squidGame.getSquidHuntGameParams()) return;
        if(this.squidGame.isDoubleSquidMode()) this.updateMultiplierBonusItem();
        else this.updateNormalBonusItem();
        this.game_instruction.active  = !this.squidGame.isDoubleSquidMode()
        this.double_squid_instruction.active = this.squidGame.isDoubleSquidMode();
        this.node.active = true;
    }
      

    hide () {
        this.node.active  = false;
    }

}