import cv from "../../../../components/lobby/cv";
import SquidStatusPlayerInfo from "./SquidStatusPlayerInfo";
import { PokerHandData } from '../../../../components/game/dzPoker/data/RecordData';
import { GameReview } from "../../../../components/game/dzPoker/gameReview/GameReview";

export enum SquidResultPopupType {
    WIN = 0,
    LOSE = 1,
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class SquidSettlementReview extends cc.Component {

    @property(cc.Node) playerInfoTemplate: cc.Node = null;

    @property(cc.Node) listInfoContent: cc.Node = null;
    @property(cc.Node) looserlistInfoContent: cc.Node = null;
    @property(cc.Label) lblNext: cc.Label = null;
    @property(cc.Label) lblPrev: cc.Label = null;
    @property(cc.Label) lblRound: cc.Label = null;
    // private gameUUID_SquidFinalSettelment:string[]=[];
    private gameReview:GameReview=null; 
    private _autoShow = false;
    public get autoShow() {
        return this._autoShow;
    }
    
    public set autoShow(value) {
        this._autoShow = value;
    }

    public reset(): void {
        // this.counter=0;
        // this.gameUUID_SquidFinalSettelment=[];
    }

    public show(msg: PokerHandData,totalCount:number,_gameReview:GameReview) {
        this.reset();
        this.node.active = true;
        this.gameReview=_gameReview;
        // this.gameUUID_SquidFinalSettelment=this.gameReview._vGameUUID_SquidFinalSettelment;
        // this commented lines is for next release use 
        // this.lblRound.string=cv.StringTools.formatC(cv.config.getStringData("review_round"), this.counter+"/"+this.gameUUID_SquidFinalSettelment.length);
        this.clearList();
        if(this.autoShow){
            this.endWaitingTimes = Date.now() + 1000 * 7;// for 10 second
            this.schedule(this.scheduleWaitingTime, 1, cc.macro.REPEAT_FOREVER, 0);
            this.scheduleWaitingTime();
        }
        const winnerCount:number=msg.vPlayerRecords.filter(vPlayerRecords => vPlayerRecords.squidWinLoseAmount > 0).length;
        const _noOfLosers = totalCount-winnerCount;
        msg.vPlayerRecords.sort(this.sortListAssendingOrder.bind(this));
        msg.vPlayerRecords.forEach(player => {
            const playerInfo: any = {
                userid: player.nPlayerID,
                nickname: player.sPlayerName,
                avatar: player.sPlayerHead,
                plat: player.plat,
                squidNumber:player.squidCount?player.squidCount:0,
                winAmount:player.squidWinLoseAmount,
                squidValue:player.squidValue,
                squidMultiplier:player.squidMultiplier,
                noOfLosers:_noOfLosers,
            };

            this._addPlayerInfo(playerInfo);
        });
    }

    endWaitingTimes: number = 0;
    private scheduleWaitingTime() {
        let leftSecond = Math.ceil(((this.endWaitingTimes - Date.now()) / 1000) - 1);
        if (leftSecond < 0) {
            leftSecond = 0;
            this.hide();
        }
    }

    public hide() {
        // this.clearList();
        this.autoShow=false;
        if(this.scheduleWaitingTime)
            this.unschedule(this.scheduleWaitingTime);
        this.gameReview.autoHide();
    }

    private clearList():void
    {
        if (this.looserlistInfoContent.childrenCount > 0)
            this.looserlistInfoContent.destroyAllChildren();

        if (this.listInfoContent.childrenCount > 0)
            this.listInfoContent.destroyAllChildren();
    }

    private _addPlayerInfo(playerInfo: any) {
        const playerNode: SquidStatusPlayerInfo = cc.instantiate(this.playerInfoTemplate).getComponent(SquidStatusPlayerInfo);
        playerNode.node.active = true;
        if (playerInfo.squidNumber > 0) {
            playerNode.addSettlementWinnerReviewPlayerInfo(playerInfo);
            this.listInfoContent.addChild(playerNode.node);
        }
        else {
            playerNode.addSettlementLoserPlayerInfo(playerInfo);
            this.looserlistInfoContent.addChild(playerNode.node);
        }

    }

    sortListAssendingOrder(a: any, b: any): number {
        return a.squidWinLoseAmount < b.squidWinLoseAmount ? 1 : -1;
    }
    
     // this commented line is for next release use 
/*
    private counter: number = 0;

    onClickNext() {
        if (this.counter < this.gameUUID_SquidFinalSettelment.length) {
            this.counter++;
            this.updateButtonStates();
        }
    }

    onClickPrev() {
        if (this.counter > 0) {
            this.counter--;
            this.updateButtonStates();
        }
    }

    private updateButtonStates() {
        this.gameReview.jumpSquidFinalSettelmentPage(this.gameUUID_SquidFinalSettelment[this.counter]);
        // Update 'Prev' button state
        this.lblPrev.node.opacity = this.counter > 0 ? 255 : 78;
        this.lblPrev.node.parent.getComponent(cc.Button).interactable = this.counter > 0;

        // Update 'Next' button state
        this.lblNext.node.opacity = this.counter < this.gameUUID_SquidFinalSettelment.length ? 255 : 78;
        this.lblNext.node.parent.getComponent(cc.Button).interactable = this.counter < this.gameUUID_SquidFinalSettelment.length;
        this.lblRound.string=cv.StringTools.formatC(cv.config.getStringData("review_round"), this.counter+"/"+this.gameUUID_SquidFinalSettelment.length);
    }
*/
}
