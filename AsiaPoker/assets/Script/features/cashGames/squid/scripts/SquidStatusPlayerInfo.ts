import game_protocol = require("./../../../../../Script/common/pb/gs_protocol");
import game_pb = game_protocol.protocol;
import cv from "../../../../components/lobby/cv";
import { CircleSprite, Head_Mode } from "../../../../common/tools/CircleSprite";
import { CurrencyType } from "../../../../common/tools/Enum";
import SquidGame from "./SquidGame";

const {ccclass, property} = cc._decorator;

@ccclass
export default class SquidStatusPlayerInfo extends cc.Component {

    @property(cc.Label) lblName: cc.Label = null;
    @property(cc.Label) lblSquidCount: cc.Label = null;
    @property(cc.Label) lblValue: cc.Label = null;
    @property(cc.Label) lblMult: cc.Label = null;
    @property(cc.Label) lblLooser: cc.Label = null;
    @property(cc.Label) lblProfit: cc.Label = null;
    @property(cc.Node) avatar: cc.Node = null;
    @property(cc.Node) bgLight: cc.Node = null;
    @property(cc.Node) imgUSD: cc.Node = null;
    @property(cc.Node) imgCoin: cc.Node = null;
    @property(cc.Node) groupHide: cc.Node = null;

     public addPlayerInfo(playerInfo:any,squidGame:SquidGame)
     {
        this._setPlayerProfileInfo(playerInfo.uid,playerInfo.nickname,playerInfo.avatar,playerInfo.platform,playerInfo.isAway);
        if (playerInfo.totalSquidsWon > 0) {
            this.lblSquidCount.node.opacity=255;
            this.lblSquidCount.string = ""+ playerInfo.totalSquidsWon;
            const value=squidGame.getSquidHuntGameParams().squidValue[1];
            const multi=squidGame.getSquidHuntGameParams().squidMultiplier[playerInfo.totalSquidsWon].multiplier;
            const looserCount=squidGame.getSquidHuntGameInfo().squidHuntGamePlayers.length-Object.keys(squidGame.getSquidHuntGameInfo().winSquidPlayer).length;
            const profit=playerInfo.totalSquidsWon*value*multi*looserCount;
            this.lblValue.string =  ""+cv.StringTools.clientGoldByServer(value);
            this.lblMult.string=""+multi;
            this.lblLooser.string=""+looserCount;
            this.lblProfit.string=""+cv.StringTools.clientGoldByServer(profit);
            this.imgUSD.active = cv.GameDataManager.tRoomData.currency === CurrencyType.USDT;
            this.imgCoin.active = !(cv.GameDataManager.tRoomData.currency === CurrencyType.USDT);
            if(this.lblValue.string.length>5)// for only long range value should be shrink showing correct
            {
                this.lblValue.overflow=cc.Label.Overflow.SHRINK;
                this.lblValue.node.width=113;
            }
        }
        else {
            this.lblSquidCount.node.opacity=127;
            this.groupHide.active = false;
        }
    }

    public addSettlementWinnerReviewPlayerInfo(playerInfo: any) 
    {
        this._setPlayerProfileInfo(playerInfo.userid,playerInfo.nickname,playerInfo.avatar,playerInfo.plat);
        this.lblSquidCount.string = "" + playerInfo.squidNumber;
        this.lblValue.string =  ""+cv.StringTools.clientGoldByServer(playerInfo.squidValue);
        this.lblMult.string = "" + playerInfo.squidMultiplier;
        this.lblLooser.string = "" + playerInfo.noOfLosers;
        cv.StringTools.setLabelValueAndColor(this.lblProfit.node, playerInfo.winAmount);
        if(this.lblValue.string.length>5)// for only long range value should be shrink showing correct
        {
            this.lblValue.overflow=cc.Label.Overflow.SHRINK;
            this.lblValue.node.width=113;
        }
    }

    public addSettlementLoserPlayerInfo(playerInfo: game_pb.ISquidLeaderBoard) 
    {
        this._setPlayerProfileInfo(playerInfo.userid,playerInfo.nickname,playerInfo.avatar,playerInfo.plat);
        cv.StringTools.setLabelValueAndColor(this.lblProfit.node, playerInfo.winAmount);
        this.groupHide.active = false;
    }

    _setPlayerProfileInfo(_userdId : number,_nickName: string,_avatar : string,_platform : number,isAway:boolean=false)
    {
        this.lblName.string = cv.StringTools.ellipsisRichText(_nickName,4);
        this.lblName.node.color = cc.Color.WHITE;
        CircleSprite.setCircleSprite(this.avatar, _avatar, _platform, false, Head_Mode.CLUB);
        
        if ( _userdId===  cv.dataHandler.getUserData().u32Uid) {
            this.lblName.node.color = cc.color(251, 216, 136);
        }else if(isAway){
            this.lblName.node.opacity =127;
        }
    }
}
