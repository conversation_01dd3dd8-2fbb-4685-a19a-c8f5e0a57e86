import game_protocol = require("../../../../common/pb/gs_protocol");
import game_pb = game_protocol.protocol;
import cv from "../../../../components/lobby/cv";

export class SquidGameData {
    public squidHuntGameInfo: game_pb.ISquidHuntGameInfo = null;
    public squidHuntGameParams: game_pb.ISquidHuntGameParams = null;
    public squidHuntGameSettlement: game_pb.ISquidHuntGameSettlement = null;

    public serverTime: number = 0;
    public rebuyInTimeoutTime: number = 0;


    resetOnStartSquid() {
        if (this.squidHuntGameInfo) {
            this.squidHuntGameSettlement = null;
        }
        if (this.squidHuntGameInfo) {
            this.squidHuntGameInfo.isStarted = true;
        }
    }

    resetOnEndSquid() {
        if (this.squidHuntGameInfo) {
            this.squidHuntGameInfo.isStarted = false;
            this.squidHuntGameSettlement = null;
            this.squidHuntGameInfo = null;
        }
    }

    public isSquidRunning(): boolean {
        return cv.GameDataManager.tRoomData.u32GameID === cv.Enum.GameId.Squid && this.squidHuntGameInfo?.isStarted;
    }

    public isSuperSquidRound(): boolean {
        return this.squidHuntGameInfo?.isSuperSquidRound || false;
    }

    public hasWonSquidHand(_playerId: number = cv.dataHandler.getUserData().u32Uid): boolean {
        return this.squidHuntGameSettlement?.winSquidPlayer[_playerId] !== undefined || false;
    }

    public isRegisteredPlayer(_playerId: number = cv.dataHandler.getUserData().u32Uid): boolean {
        return this.squidHuntGameInfo?.registeredPlayers?.includes(_playerId) || false;
    }

    public isUnRegisteredPlayer(_playerId: number = cv.dataHandler.getUserData().u32Uid): boolean {
        return this.squidHuntGameInfo?.unRegisteredPlayers?.includes(_playerId) || false;
    }

    public isPreparationPhase(): boolean{
		return !this.isSquidRunning() && this.isRegisteredPlayer();
	}

    public getSquidHuntGameParams()
    {
        return this.squidHuntGameParams;
    }

    public checksIfItsFirstSquid(): boolean {
        const entries = Object.entries(this.squidHuntGameSettlement?.winSquidPlayer);
        // Check if there's exactly one entry and its value is 1
        return entries.length === 1 && entries[0][1] === 1;
    }
    
    public get looser() {
        if(!this.squidHuntGameInfo?.registeredPlayers || !this.squidHuntGameSettlement?.winSquidPlayer) return [];
        return this.squidHuntGameInfo.registeredPlayers.reduce((loosers, id) => {
            if (!(this.squidHuntGameSettlement.winSquidPlayer?.[id] > 0)) {
                loosers.push(id)
            }
            return loosers;
        }, []);
    }

    public get remainingSquid () {
        return this.squidHuntGameSettlement ? this.squidHuntGameSettlement?.remainingSquidCounts || 0 : this.squidHuntGameInfo?.remainingSquidCounts || 0;
    }

    public get isDoubleSquid () {
        return this.squidHuntGameParams?.mode === game_pb.SquidHuntGameMode.MULTIPLIER_MODE;
    }

    public get isFirstRoundDoubleSquid () {
        return this.squidHuntGameParams?.isFirstRoundDoubleSquid && this.squidHuntGameInfo?.isFirstRound;   // first hand with Double Squid 
    }

    get squidMultiplier() {
        const input = this.squidHuntGameParams?.squidMultiplier;
        if(!input) return[];
        const result = [];
        let min = 1;
        const squid = Object.keys(input);
        for (let i = 2; i <= squid.length; i++) {
            if (input[i].multiplier !== input[i - 1].multiplier) {
                result.push({ min, max: i - 1, multiplier: input[i - 1].multiplier });
                min = i;
            }
        }
        result.push({ min, max: squid.length, multiplier: input[squid.length].multiplier });
        return result.sort((a, b) => a.multiplier - b.multiplier);
    }

}