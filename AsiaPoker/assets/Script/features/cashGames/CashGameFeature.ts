// eslint-disable-next-line max-classes-per-file
import cv from "../../components/lobby/cv";
import { GameScene } from "../../../Script/components/game/dzPoker/GameScene";
import { FeatureBase, FeatureCreator, BaseGameType } from "../Feature";

/**
 * Type of features
 */
export enum CashGameFeatureType {
	None,
	SquidGame
}

/**
 * Base class for main class of feature
 */
export abstract class CashGameFeatureBase extends FeatureBase {
    gameScene: GameScene;
	abstract playSound(soundName: string);
	abstract getFeatureData();
	emit(msg: string, param?: any) {
		if (cc.isValid(this.node)) {
			this.node.emit(msg, param);
		}
	}
}

export class CashGameFeatureCreator extends FeatureCreator {
	/**
	 * Create feature component and add it to target node
	 * @param gameId: gameId 
	 * @param node: node to add the feature, should be attached a GameScene component
	 * @returns the new feature component
	 */
	public static addFeatureByGameId(gameId: number, node: cc.Node) {
		const feature = CashGameFeatureCreator.getFeatureType(gameId);
		return CashGameFeatureCreator.addFeature(feature, node);
	}

	/**
	 * Create feature component and add it to target node
	 * @param feature: CashGameFeature type of feature 
	 * @param node: cc.Node node to add the main class of feature, should be attached a GameScene component
	 * @returns the new feature component
	 */
	public static addFeature(feature: CashGameFeatureType, node: cc.Node) {
		const featureComp = super.addFeatureToNode(BaseGameType.CashGame, feature, node) as CashGameFeatureBase;
		if (!cc.isValid(featureComp)) {
			return null;
		}
		featureComp.gameScene = node.getComponent(GameScene);
		return featureComp;
	}

	public static getFeatureData(gameId: number = cv.roomManager.getCurrentGameID(), gametype: BaseGameType = BaseGameType.CashGame) {
		return super.getFeatureData(CashGameFeatureCreator.getFeatureType(gameId), gametype);
	}
	
	public static removeFeature(featureId: CashGameFeatureType) {
		// Remove feature getter method
		super.removeFeatureDataGetter(BaseGameType.CashGame, featureId);
	}

	/**
	 * Convert gameId to CashGameFeature type
	 */
	public static getFeatureType(gameId: number) {
		switch (gameId) {
			case cv.Enum.GameId.Squid:
				return CashGameFeatureType.SquidGame;
			default:
				return CashGameFeatureType.None;
		}
	}
}