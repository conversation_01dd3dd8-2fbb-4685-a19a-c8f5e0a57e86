import cb from '../components/game/cowboy/cb';
import { NetWork } from '../common/net/NetWork';
import { HashMap } from '../common/tools/HashMap';
import { cowboy_proto } from '../common/pb/cowboy';
import { GameId } from '../common/tools/Enum';
import { CowboyNetWork } from '../common/net/CowboyNetWork';
import { pokermaster_proto } from '../common/pb/pokermaster';
import { PokerMasterBaseSocket } from '../components/game/pokerMaster/PokerMasterBaseSocket';
import { HumanboyBaseSocket } from '../components/game/humanboy/HumanboyBaseSocket';
import { humanboy_proto } from '../common/pb/humanboy';
import HumanboyDataMgr from '../components/game/humanboy/HumanboyDataMgr';
import PokerMasterDataMgr from '../components/game/pokerMaster/PokerMasterDataMgr';
import { NetWorkProxy } from '../common/net/NetWorkProxy';
import { WorldNetWork } from '../common/net/WorldNetWork';
import { pb } from '../common/pb/ws_protocol';
import cv from '../components/lobby/cv';
import ActivityBase from '../../minigames/Script/components/game/common/rebatePromotion/ActivityBase';
import { TagCom } from '../common/tools/TagCom';
import { Activity1 } from '../../minigames/Script/components/game/common/rebatePromotion/Activity1';
import { Activity2 } from '../../minigames/Script/components/game/common/rebatePromotion/Activity2';
import { Activity4 } from '../../minigames/Script/components/game/common/rebatePromotion/Activity4';
import { VideoCowboyNetWork } from '../common/net/VideoCowboyNetWork';
import { video_cowboy_proto } from '../common/pb/video_cowboy';

export default class MockMiniGameLogic {
    private static _instance: MockMiniGameLogic;

    static getInstance(): MockMiniGameLogic {
        if (!this._instance) {
            this._instance = new MockMiniGameLogic();
        }
        return this._instance;
    }

    _mockCowboyAutoBetAdd(amount: number = 5) {  
        let resMsg: object = { 
            code: 1,
            autoBetCount: cb.getCowboyRoom().iSelectAutoBetCount + amount,
            usedAutoBetCount: 2 
        };

        let pbbuf = CowboyNetWork.getInstance().encodePB("AdvanceAutoBetAddRsp", resMsg);
        console.log("mock pbbuf = "+ pbbuf);

        this._execFunc(cowboy_proto.CMD.ADVANCE_AUTO_BET_ADD_RSP, GameId.CowBoy, pbbuf);
        
      }

      _mockHumanboyAutoBetAdd(amount: number = 5) {  
        let resMsg: object = { 
            code: 1,
            autoBetCount: HumanboyDataMgr.getHumanboyRoom().iSelectAutoBetCount + amount,
            usedAutoBetCount: 2 
        };

        let pbbuf = HumanboyBaseSocket.getInstance().encodePB("AdvanceAutoBetAddRsp", resMsg);
        console.log("mock pbbuf = "+ pbbuf);

        this._execFunc(humanboy_proto.CMD.ADVANCE_AUTO_BET_ADD_RSP, GameId.HumanBoy, pbbuf);
        
      }

      _mockPokerMasterAutoBetAdd(amount: number = 5) {  
        let resMsg: object = { 
            code: 1,
            autoBetCount: PokerMasterDataMgr.getPokerMasterRoom().iSelectAutoBetCount + amount,
            usedAutoBetCount: 2 
        };

        let pbbuf = PokerMasterBaseSocket.getInstance().encodePB("AdvanceAutoBetAddRsp", resMsg);
        console.log("mock pbbuf = "+ pbbuf);

        this._execFunc(pokermaster_proto.CMD.ADVANCE_AUTO_BET_ADD_RSP, GameId.PokerMaster, pbbuf);
        
      }

      _mockReachLimitBet(gameId: GameId) {         
        type MockMiniGameConfig = {
            [key in GameId]: { network: NetWorkProxy; u16Msgid: number, code: number };
        };

        const config: Partial<MockMiniGameConfig> = {
            [GameId.CowBoy]: {
                network: CowboyNetWork.getInstance(),
                u16Msgid: cowboy_proto.CMD.ADVANCE_AUTO_BET_ADD_RSP,
                code: cowboy_proto.ErrorCode.REACH_LIMIT_BET
            },
            [GameId.HumanBoy]: {
                network: HumanboyBaseSocket.getInstance(),
                u16Msgid: humanboy_proto.CMD.ADVANCE_AUTO_BET_ADD_RSP,
                code: humanboy_proto.ErrorCode.REACH_LIMIT_BET
            },
            [GameId.PokerMaster]: {
                network: PokerMasterBaseSocket.getInstance(),
                u16Msgid: pokermaster_proto.CMD.ADVANCE_AUTO_BET_ADD_RSP,
                code: pokermaster_proto.ErrorCode.REACH_LIMIT_BET
            }
        };

        let resMsg: object = { 
            code: config[gameId].code,
            autoBetCount: cb.getCowboyRoom().iSelectAutoBetCount,
            usedAutoBetCount: 2,
            reachLimitBet: true,
            numberHandAdded: 99,
        };

        let pbbuf = config[gameId].network.encodePB('AdvanceAutoBetAddRsp', resMsg);
        console.log('mock pbbuf = ' + pbbuf);

        this._execFunc(config[gameId].u16Msgid, gameId, pbbuf);

    }

    _mockAutoBetNoBalance(gameId: GameId) {         
        type MockMiniGameConfig = {
            [key in GameId]: { network: NetWorkProxy; u16Msgid: number, code: number };
        };

        const config: Partial<MockMiniGameConfig> = {
            [GameId.CowBoy]: {
                network: CowboyNetWork.getInstance(),
                u16Msgid: cowboy_proto.CMD.ADVANCE_AUTO_BET_RSP,
                code: cowboy_proto.ErrorCode.AUTO_BET_NO_MONEY
            },
            [GameId.HumanBoy]: {
                network: HumanboyBaseSocket.getInstance(),
                u16Msgid: humanboy_proto.CMD.ADVANCE_AUTO_BET_RSP,
                code: humanboy_proto.ErrorCode.AUTO_BET_NO_MONEY
            },
            [GameId.PokerMaster]: {
                network: PokerMasterBaseSocket.getInstance(),
                u16Msgid: pokermaster_proto.CMD.ADVANCE_AUTO_BET_RSP,
                code: pokermaster_proto.ErrorCode.AUTO_BET_NO_MONEY
            }
        };

        const resMsg: object = { 
            code: config[gameId].code,
            CalmDownDeadLineTimeStamp: 0,
            CalmDownLeftSeconds: 0,
            bill: null,
            usedAutoBetCount: 0,
        };

        const pbbuf = config[gameId].network.encodePB('AdvanceAutoBetAddRsp', resMsg);
        console.log('mock pbbuf = ' + pbbuf);

        this._execFunc(config[gameId].u16Msgid, gameId, pbbuf);

    }
    _mockISlotOpenning() {  
        let resMsg: object = { 
            error: 1,
            gameURL: "https://islot.com/"
        };

        let pbbuf = WorldNetWork.getInstance().encodePB("ISlotsLoginResponse", resMsg);
        console.log("mock pbbuf = "+ pbbuf);
        cv.roomManager.setCurrentGameID(pb.GameId.ISlot);
        this._execFunc(pb.MSGID.MsgId_ISlots_Login_Response, GameId.World, pbbuf);
      }

      private _execFunc(u16Msgid: number, u32serverid: number, pbbuf: Uint8Array) {
        let value: HashMap<number, Function> = NetWork.getInstance().handlers.get(u32serverid);
        if (value) {
            let func = value.get(u16Msgid);
            if (typeof func == "function") {
                func(pbbuf, u16Msgid);
            }
            else {
                console.log("未注册消息id = " + u16Msgid);
            }
        }
        else {
            console.log("未注册游戏id = " + u32serverid);
        }
    }
    public mockGetRebateEventStatus(isRank?:number) {
        let data = pb.GetEventStatusResponse.create({
            error: 1,
            id: 2,
            setting: pb.GetEventStatusResponse.RebateSetting.create({
                event_type: 1,
                type4: pb.GetEventStatusResponse.RebateSetting.EventType4.create({
                    is_daily: true,
                    reward_progress: [
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 10000,
                            currency_type: 2
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 9000, 
                            currency_type: 3
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 8000, 
                            currency_type: 3
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 7000, 
                            currency_type: 3
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 6000, 
                            currency_type: 3
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 5000, 
                            currency_type: 2
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 4000, 
                            currency_type: 3
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 3000, 
                            currency_type: 3
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 2000, 
                            currency_type: 2
                        }),
                        pb.EventDataWithType4.BetTime.RewardProgress.create({
                            reward: 1000, 
                            currency_type: 3
                        })
                    ]
                })
            }),
            event_data_type1: pb.EventData.create({
                bet_time: []
            }),
            event_data_type2: pb.EventData.create({
                bet_time: []
            }),
            event_data_type3: pb.EventData.create({
                bet_time: []
            }),
            event_data_type4: pb.EventDataWithType4.create({
                bet_time: []
            })
        });

        let betTime = this.createDefaultBetTime();
        //betTime.reward_progress[2].can_get = false;
        data.event_data_type1.bet_time.push(betTime);
        //Mock data for activity 2
        betTime = this.createDefaultBetTime();
        betTime.start_time = Date.now() / 1000 + 3600 * 24;
        betTime.reward_progress[4].amount_gte = 99999999;
        data.event_data_type2.bet_time.push(betTime);
        data.event_data_type3.bet_time.push(betTime);
        betTime = this.createDefaultBetTime();
        betTime.reward_progress[4].can_get = true;
        betTime.start_time = Date.now() / 1000 + 3600 * 24 * 2;
        data.event_data_type2.bet_time.push(betTime);
        data.event_data_type3.bet_time.push(betTime);
        betTime = this.createDefaultBetTime();
        betTime.start_time = Date.now() / 1000 + 3600 * 24 * 3;
        betTime.reward_progress[4].amount_gte = 8888888;
        data.event_data_type2.bet_time.push(betTime);
        data.event_data_type3.bet_time.push(betTime);
        betTime = this.createDefaultBetTime();
        betTime.start_time = Date.now() / 1000 + 3600 * 24 * 4;
        data.event_data_type2.bet_time.push(betTime);
        data.event_data_type3.bet_time.push(betTime);
        betTime = this.createDefaultBetTime();
        betTime.start_time = Date.now() / 1000 + 3600 * 24 * 5;
        data.event_data_type2.bet_time.push(betTime);
        data.event_data_type3.bet_time.push(betTime);
        betTime = this.createDefaultBetTime();
        betTime.start_time = Date.now() / 1000 + 3600 * 24 * 6;
        data.event_data_type2.bet_time.push(betTime);
        data.event_data_type3.bet_time.push(betTime);
        betTime = pb.EventDataWithType4.BetTime.create({
            betting_amount: 93000,
            end_time: Date.now() / 1000 + 3600 * 24 + 40,
            global_player_rank: [
                pb.EventDataWithType4.BetTime.GlobalPlayerRank.create({
                    player_id: 10001,
                    nickname: '玩家4玩家2玩家1',
                    avatar: '/img/230918/1758k9SOp1.png',
                    betting_amount: 10000000
                }),
                pb.EventDataWithType4.BetTime.GlobalPlayerRank.create({
                    player_id: 10002,
                    nickname: '玩家5玩家2玩家2',
                    avatar: 'avatar_41313_1673519448.jpg',
                    betting_amount: 9000000
                }),
                pb.EventDataWithType4.BetTime.GlobalPlayerRank.create({
                    player_id: 10003,
                    nickname: 'a5team456',
                    avatar: 'https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50',
                    betting_amount: 5000000
                }),
                pb.EventDataWithType4.BetTime.GlobalPlayerRank.create({
                    player_id: 10004,
                    nickname: 'user 40000000000',
                    avatar: 'https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50',
                    betting_amount: 400000
                }),
                pb.EventDataWithType4.BetTime.GlobalPlayerRank.create({
                    player_id: cv.dataHandler.getUserData().u32Uid,
                    nickname: 'user 5000000',
                    avatar: 'https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50',
                    betting_amount: 500000
                }),
                pb.EventDataWithType4.BetTime.GlobalPlayerRank.create({
                    player_id: 10006,
                    nickname: 'user 6',
                    avatar: 'https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50',
                    betting_amount: 600000
                })
            ],
            surpassed_reward: pb.EventDataWithType4.BetTime.SurpassedReward.create({
                surpassed_gte: 10,
                is_enabled: true,
                can_get: false,
                got: false,
                reward: 8000,
                top_n_can_get: 20
            }),
            player_status: pb.EventDataWithType4.BetTime.PlayerStatus.create({
                surpassed: isRank === 1 ? 100 : null,
                rank: 4
            })
        });
        data.event_data_type4.bet_time.push(betTime);
    /*
        data.event_data_type2.bet_time.forEach((o)=>{
            this._fakeAllCurrencyType(o, 2);
        });
    */
        let pbbuf = WorldNetWork.getInstance().encodePB('GetEventStatusResponse', data);
        console.log('mock pbbuf = ' + pbbuf);

        this._execFunc(pb.MSGID.MsgId_Rebate_GetEventStatus_Response, GameId.World, pbbuf);
    }

    private createDefaultBetTime() {
        let progresses = [
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 1000,
                reward: 800,
                can_get: false,
                got: true,
                currency_type: 3
            }),
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 5000,
                reward: 3800,
                can_get: false,
                got: true,
                currency_type: 2
            }),
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 10000,
                reward: 8800,
                can_get: true,
                got: false,
                currency_type: 3
            }),
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 50000,
                reward: 3880000,
                can_get: true,
                got: false,
                currency_type: 2
            }),
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 100000,
                reward: 8880000,
                can_get: false,
                got: false,
                currency_type: 3
            }),
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 500000,
                reward: 2888000,
                can_get: false,
                got: false,
                currency_type: 3
            }),
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 1000000,
                reward: 5888000,
                can_get: false,
                got: false,
                currency_type: 2
            }),
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 100000,
                reward: 8880000,
                can_get: false,
                got: false,
                currency_type: 3
            })
            ,
            pb.EventData.BetTime.RewardProgress.create({
                amount_gte: 100000,
                reward: 8880000,
                can_get: false,
                got: false,
                currency_type: 3
            })
        ];

        let betTime = pb.EventData.BetTime.create({
            betting_amount: 53000,
            reward_progress: progresses,
            start_time: Date.now(),
            end_time: Date.now() / 1000 + 3600 * 24
        });
        return betTime;
    }

    private _fakeAllCurrencyType(beTime: pb.EventData.IBetTime, currencyType: number) {
        beTime.reward_progress.forEach((o)=>{
            o.currency_type = currencyType;
        });
    }

    public showActivityDialog() {
        // instantiate activity window here.
        const data : pb.GetEventStatusResponse = cb.getCowboyRoom().rebateEventStatus;
        const eventType: number = data.setting.event_type;
        const rewardProgressIndex = eventType == 4? -1: 0;
    
        if(data.setting.event_type === pb.RebateEventType.TYPE_4) {
          const checkReward = (betTime: pb.EventDataWithType4.IBetTime): boolean => {
            if (!!betTime==false){
              return false;
            }
            return betTime.surpassed_reward.can_get && !betTime.surpassed_reward.got || betTime.reward_progress.some((progress)=>progress.can_get && !progress.got);
          }
    
          const betTime = data.event_data_type4.bet_time.find(bet => checkReward(bet));
          if(betTime && data.system_time > betTime.end_time) {
            cv.worldNet.requestRebateReward(data.id, 0, rewardProgressIndex);
            return;
           }
        }
        cv.dialogMager.onInit((node)=>{
          const activity = node.getComponent(ActivityBase);
          activity?.onSelected((idx)=>{
              cv.worldNet.requestRebateReward(data.id, idx, rewardProgressIndex);
          });
          
          this._updateActivityData(node, eventType, data);
        })
        .showPopup({  
          popupId: eventType, 
          title: "title",
          content: "content",
          horizontalAlign: cc.Label.HorizontalAlign.LEFT
        });
      
    }

    private _updateActivityData(activityNode: cc. Node, activityId: number, data: pb.IGetEventStatusResponse): void {
        let tag = activityNode.getComponent(TagCom);
        if (tag == null){
          tag = activityNode.addComponent(TagCom);
        }
        tag.nIdx = activityId;
        tag.nTag = data.system_time;
    
        switch (activityId) {
          case 1:
            activityNode.getComponent(Activity1).updateData(data.event_data_type1);
            break;
          case 2:
            activityNode.getComponent(Activity2).updateData(data.event_data_type2);
            break;
          case 3:
            activityNode.getComponent(Activity2).updateData(data.event_data_type3); //use same component as activity 2
            break;
          case 4:
            activityNode.getComponent(Activity4).updateData(data);
            break;
          default:
            break;
        }
    }

    public showLeaderboardReward(eventType: number) {
        const reward :{[key: string]: number} = {
            "2": 10000,
            "3": 9000
        };
        const data = pb.ClaimRewardResponse.create({
            error: 1,
            reward_amount: reward
        });
        cb.getCowboyRoom().rebateEventStatus.setting.event_type = eventType;
        const pbbuf = WorldNetWork.getInstance().encodePB('ClaimRewardResponse', data);
        console.log('mock pbbuf = ' + pbbuf);

        this._execFunc(pb.MSGID.MsgId_Rebate_ReceiveReward_Response, GameId.World, pbbuf);
    }

    mockConsumingNotify(gameId: GameId) {
        const cmdConfig: { [key: number]: { network: NetWorkProxy, cmd: number } } = {
            [cv.Enum.GameId.CowBoy]: { network: CowboyNetWork.getInstance(), cmd: cowboy_proto.CMD.LEFT_GAME_COIN_NOTIFY },
            [cv.Enum.GameId.PokerMaster]: { network: PokerMasterBaseSocket.getInstance(), cmd: pokermaster_proto.CMD.LEFT_GAME_COIN_NOTIFY },
            [cv.Enum.GameId.HumanBoy]: { network: HumanboyBaseSocket.getInstance(), cmd: humanboy_proto.CMD.LEFT_GAME_COIN_NOTIFY },
            [cv.Enum.GameId.VideoCowboy]: { network: VideoCowboyNetWork.getInstance(), cmd: video_cowboy_proto.CMD.LEFT_GAME_COIN_NOTIFY }
        };
        const data = cowboy_proto.LeftGameCoinNotify.create({
            cur_game_coin: 100000,
            lost_game_coin: 1000
        });

        const pbbuf = cmdConfig[gameId].network.encodePB('LeftGameCoinNotify', data);
        this._execFunc(cmdConfig[gameId].cmd, gameId, pbbuf);
    }

    mockMiniGameAtmosphereMessage(){
        const data = [
            pb.MiniGameAtmosphereMessage.create({
                template: 5,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45762,
                    player_head: "avatar_45762_1706593854.jpg",
                    currency_amount: 1,
                    game_id: 10,
                    target: 305,
                    multiplier: 50
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 5,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45762,
                    currency_amount: 2,
                    game_id: 70,
                    target: 305,
                    multiplier: 50
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 19,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id:45762,
                    currency_amount:3,
                    game_id:1030,
                    game_name:"{\"language_list\": [{\"language_msg\": \"武力忍者\", \"language_name\": \"zh_CN\"}, {\"language_msg\": \"Power of Ninja\", \"language_name\": \"en_US\"}, {\"language_msg\": \"Power of Ninja\", \"language_name\": \"yn_TH\"}, {\"language_msg\": \"Power of Ninja\", \"language_name\": \"th_PH\"}, {\"language_msg\": \"Power of Ninja\", \"language_name\": \"hi_IN\"}]}"  }
                )
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 20,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id:45762,
                    currency_amount:4,
                    game_id:1030,
                    game_name:"{\"language_list\": [{\"language_msg\": \"武力忍者\", \"language_name\": \"zh_CN\"}, {\"language_msg\": \"Power of Ninja\", \"language_name\": \"en_US\"}, {\"language_msg\": \"Power of Ninja\", \"language_name\": \"yn_TH\"}, {\"language_msg\": \"Power of Ninja\", \"language_name\": \"th_PH\"}, {\"language_msg\": \"Power of Ninja\", \"language_name\": \"hi_IN\"}]}"  )
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 13,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45762,
                    player_name: "九尾狐",
                    player_head: "avatar_45762_1706593854.jpg",
                    currency_amount: 5,
                    currency_type: 3,
                    game_id: 10,
                    win_count: 1,
                    target : 305,
                    player_plat : 0,
                    game_name : `{"language_list": [{"language_msg": "ppTesty 測試", "language_name": "zh_CN"}, {"language_msg": "ppTesty en", "language_name": "en_US"}, {"language_msg": "ppTesty vn", "language_name": "yn_TH"}, {"language_msg": "ppTesty tai", "language_name": "th_PH"}, {"language_msg": "ppTesty IND", "language_name": "hi_IN"}]}`
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 14,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45762,
                    player_name: "九尾狐",
                    player_head: "avatar_45762_1706593854.jpg",
                    currency_amount: 6,
                    currency_type: 3,
                    game_id: 10,
                    win_count: 1,
                    target : 305,
                    player_plat : 0,
                    game_name : `{"language_list": [{"language_msg": "ppTesty 測試", "language_name": "zh_CN"}, {"language_msg": "ppTesty en", "language_name": "en_US"}, {"language_msg": "ppTesty vn", "language_name": "yn_TH"}, {"language_msg": "ppTesty tai", "language_name": "th_PH"}, {"language_msg": "ppTesty IND", "language_name": "hi_IN"}]}`
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 15,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45762,
                    player_name: "九尾狐",
                    player_head: "avatar_45762_1706593854.jpg",
                    currency_amount: 7,
                    currency_type: 3,
                    game_id: 10,
                    win_count: 1,
                    target : 305,
                    player_plat : 0,
                    game_name : `{"language_list": [{"language_msg": "ppTesty 測試", "language_name": "zh_CN"}, {"language_msg": "ppTesty en", "language_name": "en_US"}, {"language_msg": "ppTesty vn", "language_name": "yn_TH"}, {"language_msg": "ppTesty tai", "language_name": "th_PH"}, {"language_msg": "ppTesty IND", "language_name": "hi_IN"}]}`
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 16,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45762,
                    player_name: "九尾狐",
                    player_head: "avatar_45762_1706593854.jpg",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 10,
                    win_count: 1,
                    target : 305,
                    player_plat : 0,
                    game_name : `{"language_list": [{"language_msg": "ppTesty 測試", "language_name": "zh_CN"}, {"language_msg": "ppTesty en", "language_name": "en_US"}, {"language_msg": "ppTesty vn", "language_name": "yn_TH"}, {"language_msg": "ppTesty tai", "language_name": "th_PH"}, {"language_msg": "ppTesty IND", "language_name": "hi_IN"}]}`
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 2,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45640,
                    player_name: "泰坦",
                    player_head: "avatar_46324_1710924252.jpg",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 30,
                    win_count: 1,
                    player_plat : 0,
                    game_name : `{"language_list": [{"language_msg": "ppTesty 測試", "language_name": "zh_CN"}, {"language_msg": "ppTesty en", "language_name": "en_US"}, {"language_msg": "ppTesty vn", "language_name": "yn_TH"}, {"language_msg": "ppTesty tai", "language_name": "th_PH"}, {"language_msg": "ppTesty IND", "language_name": "hi_IN"}]}`

                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 3,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45640,
                    player_name: "a5team0016",
                    player_head: "191",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 70,
                    win_count: 1,
                    player_plat : 0
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 4,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45640,
                    player_name: "dev0001",
                    player_head: "1",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 1000,
                    win_count: 1,
                    player_plat : 0
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 5,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45640,
                    player_name: "唐釧鐠2",
                    player_head: "5",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 1010,
                    win_count: 1,
                    player_plat : 0,
                    target: 101,
                    multiplier: 2,

                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 6,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45640,
                    player_name: "Shark",
                    player_head: "/img/240409/232833pPoK.jpg?x-oss-process=image/resize,w_120",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 1021,
                    win_count: 1,
                    player_plat : 0,
                    target: 102,
                    multiplier: 3,
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 7,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45640,
                    player_name: "Kevin001",
                    player_head: "avatar_41269_1710227560.jpg",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 1030,
                    win_count: 1,
                    player_plat : 0,
                    multiplier: 4,
                    loss: 100,
                })
            }),
            pb.MiniGameAtmosphereMessage.create({
                template: 8,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45640,
                    player_name: "唐釧鐠",
                    player_head: "1",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 10,
                    win_count: 1,
                    player_plat : 0,
                    loss: 100,
                })
            }),
           
            pb.MiniGameAtmosphereMessage.create({
                template: 20,
                time_left: 1000,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45640,
                    player_name: "a5team10",
                    player_head: "avatar_41313_1673519448.jpg",
                    currency_amount: 1000,
                    currency_type: 3,
                    game_id: 1030,
                    win_count: 1,
                    player_plat : 0,
                    multiplier: 4,
                    loss: 100,
                })
            }),
        ];

        const notices = pb.MiniGamesAtmosphereMessagesResponse.create({
            messages: data
        });
        cc.warn("HTN hereeeee");
        const pbbuf = WorldNetWork.getInstance().encodePB('MiniGamesAtmosphereMessagesResponse', notices);
        console.log('mock pbbuf = ' + pbbuf);
        this._execFunc(pb.MSGID.MsgID_MiniGamesAtmosphereResponse, GameId.World, pbbuf);
    }

    mockBroadCastMessage(start, end)
    {
        const data = [];
        for (let i = start; i <= end; i++) {
          const msg =  pb.MiniGameAtmosphereMessage.create({
                template: 1,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45762,
                    player_name: `Player${i}`,
                    player_head: "avatar_45762_1706593854.jpg",
                    currency_amount: i*111100000 ,
                    game_id: 10,
                    target: 305,
                    multiplier: 50
                })
            });
            data.push(msg);
        }

        const notices = pb.MiniGamesAtmosphereMessagesResponse.create({
            messages: data
        });
        
        const pbbuf = WorldNetWork.getInstance().encodePB('MiniGamesAtmosphereMessagesResponse', notices);
        console.log('mock pbbuf = ' + pbbuf);
        this._execFunc(pb.MSGID.MsgID_MiniGamesAtmosphereResponse, GameId.World, pbbuf);
    }

    mockBroadCastNotice(idx)
    {
          const msg =  pb.MiniGameAtmosphereMessage.create({
                template: 1,
                params: pb.MiniGameAtmosphereMessageParams.create({
                    player_id: 45762,
                    player_name: `Player${idx}`,
                    player_head: "avatar_45762_1706593854.jpg",
                    currency_amount: idx*111100000 ,
                    game_id: 10,
                    target: 305,
                    multiplier: 50
                })
            });

        const notices = pb.MiniGamesAtmosphereNotice.create({
            message: msg
        });
        
        const pbbuf = WorldNetWork.getInstance().encodePB('MiniGamesAtmosphereNotice', notices);
        console.log('mock pbbuf = ' + pbbuf);
        this._execFunc(pb.MSGID.MsgID_MiniGamesAtmosphereNotice, GameId.World, pbbuf);
    }
}