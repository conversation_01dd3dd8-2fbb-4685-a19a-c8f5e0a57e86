import cv from "../components/lobby/cv";

const {ccclass, property} = cc._decorator;

@ccclass
export default class TestToolButton extends cc.Component {
    
    @property(cc.Node)
    public buttonPanel: cc.Node = null;
    
    @property(cc.Label)
    public buttonLabel: cc.Label = null;
    
    private _mouseMove: boolean = false;
    private _onButtonClick = null;

    public registerButton(label: string, callback) {
        this.buttonLabel.string = label;
        this._onButtonClick = callback;
        
        this.buttonPanel.on(cc.Node.EventType.TOUCH_END, this._onMouseUp, this);
        this.buttonPanel.on(cc.Node.EventType.TOUCH_CANCEL, this._onMouseUp, this);
        
        cv.MessageCenter.register("OnTestToolMove", this._onMouseMove.bind(this), this.node);
    }
    
    private _onMouseUp(event) {
        !this._mouseMove && this._onButtonClick?.();
        this._mouseMove = false;
    }
    
    private _onMouseMove(mouseMove: boolean) {
        this._mouseMove = mouseMove;    
    }
}
