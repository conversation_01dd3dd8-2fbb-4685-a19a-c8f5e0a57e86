import cv from '../components/lobby/cv';

export default class MockToolDataSet {
    static instance: MockToolDataSet;

    static getInstance(): MockToolDataSet {
        if (!this.instance) {
            this.instance = new MockToolDataSet();
        }
        return this.instance;
    }

    public getGameReviewData(): any {
        //根据需要调节牌局回顾的牌局数量
        let count: number = 100;
        let data = {
            nClubID: 101,
            nRoomID: 1877482,
            sGameUUID: '877921548139433984',
            sRoomUUID: '876224870312611840',
            nCreateTime: 1684115696,
            nTotalPot: 5000,
            nMaxPot: 37000,
            nInsuranceWinbet: 0,
            nJackpotWinbet: 0,
            nGameMode: 1,
            nShortFull: 0,
            bMirco: false,
            nGameid: 2,
            bAssociatedJackpot: true,
            objReplay: {
                RoomInfo: {
                    type: "No-Limit Hold'em",
                    mode: 1,
                    blind: 5000,
                    ante: 0,
                    players_count: 8,
                    double_ante: false
                },
                TableInfo: {
                    dealer_seat: 7,
                    sb_seat: 7,
                    bb_seat: 0,
                    straddle_seat: -1,
                    post_seats: null,
                    showdown_seats: null
                },
                SeatsInfo: {
                    seats_info: [
                        {
                            seat_no: 0,
                            UID: 41335,
                            name: 'a5team0030',
                            head_url: '8',
                            stake: 495000,
                            holecards: [
                                {
                                    number: 6,
                                    suit: 1
                                },
                                {
                                    number: 5,
                                    suit: 2
                                }
                            ],
                            label: 'BB',
                            is_muck: false,
                            plat: 0,
                            is_pro: 2
                        },
                        {
                            seat_no: 7,
                            UID: 41325,
                            name: 'a5team0020',
                            head_url: 'avatar_41325_1678913767.jpg',
                            stake: 505000,
                            holecards: [],
                            label: 'SB/BTN',
                            is_muck: false,
                            plat: 0,
                            is_pro: 2
                        }
                    ]
                },
                RoundsInfo: {
                    ante_round: false,
                    end_ante_round: {
                        pots_info: null
                    },
                    blind_round: true,
                    preflop: [
                        {
                            seq: 1,
                            seat_no: 7,
                            action_type: 2,
                            amount: 0,
                            action_time: 14
                        }
                    ],
                    end_preflop_round: {
                        pots_info: [
                            {
                                pot_id: 1,
                                amount: 7500
                            }
                        ]
                    },
                    flop_community_cards: null,
                    flop: null,
                    end_flop_round: {
                        pots_info: null
                    },
                    turn_community_card: null,
                    turn: null,
                    end_turn_round: {
                        pots_info: null
                    },
                    river_community_card: null,
                    river: null,
                    end_river_round: {
                        pots_info: null
                    },
                    jp_total_winbet: 0,
                    settlement_round: [
                        {
                            win_seat_no: 0,
                            win_amount: 2500,
                            jackpot_type: 0
                        },
                        {
                            win_seat_no: 7,
                            win_amount: -2500,
                            jackpot_type: 0
                        }
                    ],
                    is_now_crit_time: false
                }
            },
            objReplayInsurance: null,
            vPlayerRecords: [
                {
                    nPlayerBettingRoundBet: 5000,
                    nWinBet: 2500,
                    nInsuranceBet: 0,
                    nInsuranceAmount: 0,
                    nJackWinbet: 0,
                    nPlayerID: 41335,
                    sPlayerName: 'a5team0030',
                    sPlayerHead: '8',
                    bMuck: false,
                    bActiveShow: false,
                    bForceShowDown: false,
                    nLastRoundType: 1,
                    vCards: [
                        {
                            eCardNum: 6,
                            eCardSuit: 1
                        },
                        {
                            eCardNum: 5,
                            eCardSuit: 2
                        }
                    ],
                    plat: 0,
                    seatNo: 0,
                    seatInfo: 4,
                    bFold: false,
                    nReviewSendOutLen: 0,
                    nReviewSendOutActLen: 0,
                    nForceShowedActLen: 0,
                    jackpotType: 0
                }
            ],
            vPublicCards: [],
            vUnsendPublicCards: [
                {
                    eCardNum: 9,
                    eCardSuit: 2
                },
                {
                    eCardNum: 2,
                    eCardSuit: 1
                },
                {
                    eCardNum: 7,
                    eCardSuit: 2
                },
                {
                    eCardNum: 4,
                    eCardSuit: 1
                },
                {
                    eCardNum: 11,
                    eCardSuit: 1
                }
            ],
            bForceShowcard: true,
            bStarClosed: true,
            vShowCardByStanderUID: [],
            nForceShowCoin: 0,
            nSendOutCoin: 0,
            nJackpotTotalWinbet: 0
        };
        for (let i = 0; i < count; i++) {
            data.vPlayerRecords.push({
                nPlayerBettingRoundBet: 5000,
                nWinBet: 2500,
                nInsuranceBet: 0,
                nInsuranceAmount: 0,
                nJackWinbet: 0,
                nPlayerID: 41335,
                sPlayerName: 'a5team0030',
                sPlayerHead: '8',
                bMuck: false,
                bActiveShow: false,
                bForceShowDown: false,
                nLastRoundType: 1,
                vCards: [
                    {
                        eCardNum: 6,
                        eCardSuit: 1
                    },
                    {
                        eCardNum: 5,
                        eCardSuit: 2
                    }
                ],
                plat: 0,
                seatNo: 0,
                seatInfo: 4,
                bFold: false,
                nReviewSendOutLen: 0,
                nReviewSendOutActLen: 0,
                nForceShowedActLen: 0,
                jackpotType: 0
            });
        }
        return data;
    }

    public getWinRateData() {
        let mockWinRateData = [
            {
                playerCards: [
                    {
                        seatID: 0,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_2 },
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 1,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_2 },
                            { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    }
                ],

                publicCards: [
                    { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_9 },
                    { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_9 },
                    { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_9 }
                ]
            },
            {
                playerCards: [
                    {
                        seatID: 0,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_2 },
                            { suit: 3, num: cv.Enum.CardNum.CARD_A }
                        ]
                    },
                    {
                        seatID: 1,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_8 },
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_8 }
                        ]
                    }
                ],

                publicCards: []
            },
            {
                playerCards: [
                    {
                        seatID: 0,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_2 },
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 1,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_4 },
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_4 }
                        ]
                    },
                    {
                        seatID: 2,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_10 },
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_9 }
                        ]
                    }
                ],

                publicCards: []
            },
            {
                playerCards: [
                    {
                        seatID: 0,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_5 },
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 1,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_5 },
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 2,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_5 },
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    }
                ],

                publicCards: [
                    { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_10 },
                    { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_10 },
                    { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_J },
                    { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_J }
                ]
            },
            {
                playerCards: [
                    {
                        seatID: 0,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 1,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 2,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    }
                ],

                publicCards: [
                    { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_5 },
                    { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_J },
                    { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_10 }
                ]
            },
            {
                playerCards: [
                    {
                        seatID: 0,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 1,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_CLUB, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 2,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    }
                ],

                publicCards: []
            },
            {
                playerCards: [
                    {
                        seatID: 0,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 1,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_SPADE, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    },
                    {
                        seatID: 2,
                        handCards: [
                            { suit: cv.Enum.CardSuit.CARD_HEART, num: cv.Enum.CardNum.CARD_3 },
                            { suit: cv.Enum.CardSuit.CARD_DIAMOND, num: cv.Enum.CardNum.CARD_2 }
                        ]
                    }
                ],

                publicCards: []
            }
        ];

        return mockWinRateData;
    }

    public get2PlayerJackPotData() {
        return [
            {
                award_playid: 41316,
                award_amount: 1000,
                hand_level: 10,
                award_player_name: 'Player 1',
                type: 1 // earth player
            },
            {
                award_playid: 41308,
                award_amount: 2000,
                hand_level: 10,
                award_player_name: 'Player 2',
                type: 2 // mars player
            }
        ];
    }
    public get3PlayerJackPotData() {
        return [
            {
                award_playid: 41316,
                award_amount: 1000,
                hand_level: 10,
                award_player_name: 'Player 1',
                type: 1 // earth player
            },
            {
                award_playid: 41308,
                award_amount: 2000,
                hand_level: 10,
                award_player_name: 'Player 2',
                type: 2 // mars player
            },
            {
                award_playid: 41308,
                award_amount: 3000,
                hand_level: 10,
                award_player_name: 'Player 3',
                type: 2 // mars player
            }
        ];
    }
    public get4PlayerJackPotData() {
        return [
            {
                award_playid: 41316,
                award_amount: 1000,
                hand_level: 10,
                award_player_name: 'Player 1',
                type: 1 // earth player
            },
            {
                award_playid: 41308,
                award_amount: 2000,
                hand_level: 10,
                award_player_name: 'Player 2',
                type: 2 // mars player
            },
            {
                award_playid: 41316,
                award_amount: 1000,
                hand_level: 10,
                award_player_name: 'Player 3',
                type: 1 // earth player
            },
            {
                award_playid: 41308,
                award_amount: 2000,
                hand_level: 10,
                award_player_name: 'Player 4',
                type: 2 // mars player
            }
        ];
    }
}
