class TestClass {
  private readonly _upperBonds = [1, 10, 100, 1000, 10000];
  private readonly _minAdjNum = 0.01;
  private readonly _maxAdjNum = 9999.99;
  private readonly _diffs = [
      {
          // 0.xx
          pBias: 200,
          nBias: -1,
          maxDigit: 4,
          minDigit: 2,
      },
      {
          // 5.xx
          pBias: 200,
          nBias: -1,
          maxDigit: 4,
          minDigit: 2,
      },
      {
          //  23.xx
          pBias: 400,
          nBias: -1,
          maxDigit: 4,
          minDigit: 3,
      },
      {
          //511.xx
          pBias: 800,
          nBias: 80,
          maxDigit: 4,
          minDigit: 0,
      },
      {
          //9988.xx
          pBias: -1,
          nBias: 800,
          maxDigit: 3,
          minDigit: 0,
      },
  ];


  private _genAdjentNum(winNum: number): number[] {
    let section = this._upperBonds.length - 1;
    for(let i=0; i < this._upperBonds.length; ++i) {
        if(winNum < this._upperBonds[i]) {
            section = i;
            break;
        }
    }

    let excludeDigits = [];

    //define: digits of 0.xx is 0
    const winNumDigit = Math.floor(Math.log10(Math.abs(winNum))) +1 
    excludeDigits.push(winNumDigit)
    const adjNumDigit1 = this._genExpectDigit(excludeDigits, this._diffs[section].minDigit, this._diffs[section].maxDigit);
    let adjentNum1 = this._genNumWithDigit(adjNumDigit1);
    adjentNum1 = this._fixAdjNumber(adjentNum1, winNum, section);


    const adjNumDigit = Math.floor(Math.log10(Math.abs(adjentNum1))) +1 ;
    excludeDigits.push(adjNumDigit);
    const adjNumDigit2 = this._genExpectDigit(excludeDigits, this._diffs[section].minDigit, this._diffs[section].maxDigit);
    let adjentNum2 = this._genNumWithDigit(adjNumDigit2);
    adjentNum2 = this._fixAdjNumber(adjentNum2, winNum, section);

    return [adjentNum1,adjentNum2];
  }

  private _fixAdjNumber(adjentNum: number, winNum: number, section: number) {

    // check the `diff` of adjentNum and winNum is big enough
    if(adjentNum >= winNum) {
        // adjentNum bigger than winNum, but not big enough
        if(adjentNum < winNum + this._diffs[section].pBias) {

            //re-gen adjentNum, such it is at least `pBias` bigger than winNum
            const rangeMin = winNum + this._diffs[section].pBias;
            const rangeMax = this._maxAdjNum;
            adjentNum = Math.random() * (rangeMax - rangeMin) + rangeMin;
        }

    } else {
        // adjentNum smaller than winNum, but not small enough
        if(adjentNum > winNum - this._diffs[section].nBias) {

            //re-gen adjentNum, such it is at least `nBias` smaller than winNum
            const rangeMin = this._minAdjNum;
            const rangeMax = winNum - this._diffs[section].nBias
            adjentNum = Math.random() * (rangeMax - rangeMin) + rangeMin;
        }
    }

    return adjentNum;
  }

  private _genExpectDigit(exludeDigits: number[], minDigit: number, maxDigit: number): number {
    let iter = 100;
    let randomDigit = maxDigit;
    while(--iter) {
        randomDigit = Math.floor(Math.random() * (maxDigit - minDigit+1)) + minDigit;
        if(!exludeDigits.includes(randomDigit)) {
          break;
        }
    }
    
    //   console.log("min: ", minDigit, "  max: ", maxDigit, " randD: ", randomDigit);
    return randomDigit;
  }

  private _genNumWithDigit(digit: number): number {

      const min = Math.pow(10, digit - 1);
      const max = Math.pow(10, digit) - 1;
    
      return Math.random() * (max - min) + min;
  }

    
  private runTest(): void {
    for (let i = 0; i < 200; i++) {
      let winNum: number;
      let range: string;
      if (i < 10) {
        winNum = Math.random()+0.01; // 生成 0~9.99 的隨機數
        range = "0~0.99";
      } else if (i < 20) {
        winNum = Math.random() * 10+1; // 生成 0~9.99 的隨機數
        range = "1~9.99";
      } else if (i < 30) {
        winNum = Math.random() * 90 + 10; // 生成 10~99.99 的隨機數
        range = "10~99.99";
      } else if (i < 40) {
        winNum = Math.random() * 900 + 100; // 生成 100~999.99 的隨機數
        range = "100~999.99";
      } else {
        winNum = Math.random() * 9000 + 1000; // 生成 1000~9999.99 的隨機數
        range = "1000~9999.99";
      }

      const adjentNums = this._genAdjentNum(winNum);
      //const adjentNum2 = this.genAdjentNum(winNum);
      console.log(`${adjentNums[0].toFixed(2)} === win: ${winNum.toFixed(2)} === ${adjentNums[1].toFixed(2)}`);
    }
  }

  public run(): void {
    this.runTest();
  }
  
}
  
  const testInstance = new TestClass();
  testInstance.run();
    