import { PushNotice, PushNoticeData, PushNoticeType } from '../common/prefab/PushNotice';
import { TNParams, TNTypes } from '../common/prefab/TopNotice';
import { CreateGameMode, GameId } from '../common/tools/Enum';
import { WinRateTools } from '../common/winRate/winRateTools';

import ws_protocol = require("./../../Script/common/pb/ws_protocol");
import gs_protocol = require("./../../Script/common/pb/gs_protocol");
import world_pb = ws_protocol.pb;

import cv from '../components/lobby/cv';

import MockMiniGameLogic from './MockMiniGameLogic';
import MockToolDataSet from './MockToolDataSet';
import { jackfruit_proto } from '../common/pb/jackfruit';

const EventEmitter = require('events');

const eventEmitter = new EventEmitter();

declare global {
    interface Window {
        mockEventEmitter: any;
    }
}

window.mockEventEmitter = window.mockEventEmitter || {};
export default class MockTool {
    static instance: MockTool;

    _config: any;

    private _mockDataSet: MockToolDataSet = null;

    static getInstance(): MockTool {
        if (!this.instance) {
            this.instance = new MockTool();
        }
        return this.instance;
    }

    init() {
        this._mockDataSet = MockToolDataSet.getInstance();

        this._loadConfig();

        window.mockEventEmitter = eventEmitter;

        window.mockEventEmitter.on('MockTurnTableEvent', this._mockTurnTableEvent.bind(this));
        window.mockEventEmitter.on('MockTurnTableReadyEvent', this._mockTurnTableReadyEvent.bind(this));

        window.mockEventEmitter.on('MockTurnTableResultNotice', this._mockTurnTableResultNotice.bind(this));

        window.mockEventEmitter.on('SportLogin', this._mockSportLogin.bind(this));

        window.mockEventEmitter.on('MockSportsBettingMatches', this._mockSportsBettingMatches.bind(this));

        window.mockEventEmitter.on('ExitClick', this._mockExitGame.bind(this));

        window.mockEventEmitter.on('ExitHappyFishing', this._mockExitHappyFishingGame.bind(this));

        window.mockEventEmitter.on('TriggerJackpotAwardNotice', this._mockNoticeJackPotAwardInfo.bind(this));

        window.mockEventEmitter.on('JackpotAwardRecord', this._mockNoticeJackpotAwardRecordInfo.bind(this));

        window.mockEventEmitter.on('MockGameReview', this._mockGameReview.bind(this));

        window.mockEventEmitter.on('MockStarRedPacket', this._mockStarRedPacketResult.bind(this));

        window.mockEventEmitter.on('MockNeedRelogin', this._mockNeedRelogin.bind(this));

        window.mockEventEmitter.on('MockNoticeData', this._mockNoticeData.bind(this));

        window.mockEventEmitter.on('MockWinRateTools', this._mockWinRateTools.bind(this));

        window.mockEventEmitter.on('MockFriendLinesTools', this._mockFriendLinesTools.bind(this));

        window.mockEventEmitter.on('MockJackpotNumberAmountUpdate', this._mockJackpotNumberAmount.bind(this));

        window.mockEventEmitter.on('MockLaba', this._mockLaba.bind(this));

        window.mockEventEmitter.on("MockRebateRank",this._mockRebateRank.bind(this));


        window.mockEventEmitter.on('MockTopNotice', this._mockTopNotice.bind(this));
        window.mockEventEmitter.on('MockMttGameStartNoti', this._mockMttGameStartNoti.bind(this));

        // mini game
        window.mockEventEmitter.on('MockCowboyAutoBetAdd', this._mockCowboyAutoBetAdd.bind(this));
        window.mockEventEmitter.on('MockHumanboyAutoBetAdd', this._mockHumanboyAutoBetAdd.bind(this));
        window.mockEventEmitter.on('MockPokerMasterAutoBetAdd', this._mockPokerMasterAutoBetAdd.bind(this));
        window.mockEventEmitter.on('MockReachLimitBet', this._mockReachLimitBet.bind(this));
        window.mockEventEmitter.on('MockAutoBetNoBalance', this._mockAutoBetNoBalance.bind(this));

        window.mockEventEmitter.on('MockFavSpectatorRevealHand', this._mockFavSpectatorRevealHand.bind(this));

        window.mockEventEmitter.on('MockToggleMtt', this._mockToggleMtt.bind(this));
        window.mockEventEmitter.on('MockOpenISlot', () => MockMiniGameLogic.getInstance()._mockISlotOpenning(), this);
        window.mockEventEmitter.on(
            'mockGetRebateEventStatus',
            () => MockMiniGameLogic.getInstance().mockGetRebateEventStatus(),
            this
        );
        window.mockEventEmitter.on(
            'mockShowActivityDialog',
            () => MockMiniGameLogic.getInstance().showActivityDialog(),
            this
        );

        window.mockEventEmitter.on('MockTicketGiftNotice', this._mockTicketGiftNotice.bind(this));
        window.mockEventEmitter.on('MockTicketGiftResponse', this._mockTicketGiftResponse.bind(this));
        window.mockEventEmitter.on('MockRedPacketGiftResponse', this._mockRedPacketGift.bind(this));
        window.mockEventEmitter.on("MockGiftNotice",this._mockGiftNotice.bind(this));
        window.mockEventEmitter.on("MockEllipsisRichText",this._mockEllipsisRichText.bind(this));
        window.mockEventEmitter.on("MockTipsPanel",this._mockTipsPanel.bind(this));
        window.mockEventEmitter.on("MockSwtichLineBadDomain",this._mockSwtichLineBadDomain.bind(this));
        window.mockEventEmitter.on("MockAddSocketMsg",this._mockAddSocketMsg.bind(this));
        
        window.mockEventEmitter.on("MockLeaderboardReward", (eventId)=>{
            //window.mockEventEmitter.emit("MockLeaderboardReward");
            MockMiniGameLogic.getInstance().showLeaderboardReward(eventId);
        });
        window.mockEventEmitter.on("MockConsume",this._mockConsume.bind(this));
        window.mockEventEmitter.on("MockBlackListApiCall",this._mockBlackListApiCall.bind(this));
        window.mockEventEmitter.on("MockMiniGameAtmosphereMessage",this._mockMiniGameAtmosphereMessage.bind(this));
        window.mockEventEmitter.on("mockTwiceMd5",this._mockTwiceMd5.bind(this));
        window.mockEventEmitter.on("mockBroadCast", (start, end)=>{
            MockMiniGameLogic.getInstance().mockBroadCastMessage(start, end);
        });

        window.mockEventEmitter.on("mockBroadCastNotice", (idx)=>{
            MockMiniGameLogic.getInstance().mockBroadCastNotice(idx);
        });

        window.mockEventEmitter.on("Clear_Barrage_History",this._mockClearBarrageHistory.bind(this));
        window.mockEventEmitter.on("DestroyRoom", ()=>{
            const pub = gs_protocol.protocol.NoticeDestroyRoom.create(); 
            pub.room_uuid_js = "1114264502192680960";
            cv.MessageCenter.send("on_room_destroy_noti", pub);
        });

        window.mockEventEmitter.on(cv.Enum.LobbySecurityGuardMessage.PlayerBanMainRequest, this._mockLobbySecurityGuardMainInformation.bind(this));
        window.mockEventEmitter.on(cv.Enum.LobbySecurityGuardMessage.PlayerBanListRequest, this._mockLobbySecurityGuardPlayerBanList.bind(this));

        window.mockEventEmitter.on('MockLikeBarrage', this.mockLikeBarrage.bind(this));

        window.mockEventEmitter.on("MockGetUserData_Request", (uid)=>{
            cv.worldNet.requestGetUserData(uid);
        });

        window.mockEventEmitter.on('MockFreeEmoji', this.mockFreeEmojis.bind(this));
    }

    mockFreeEmojis() {
        const map = new Map<number, world_pb.IBackPackItem>();
  
        map.set(3, { itemId: 3, quality: 5 });
        map.set(4, { itemId: 4, quality: 0 });      // For WPK diamond lobby this should be > 0

        cv.dataHandler.getUserData().setUserBackpackData(map);
    }

    _mockClearBarrageHistory()
    {
        cv.MessageCenter.send("Reset_Barrage_History_Data");
        cv.MessageCenter.send("Reset_Barrage_History_Data_JF");
    }

    _loadConfig() {
        const ynJson = { path: 'dev/MockConfig', type: 'json' };
        cv.resMgr.load(
            ynJson.path,
            cc.JsonAsset,
            (resource: cc.JsonAsset): void => {
                this._config = resource.json;
            }
        );
    }

    /**
        export enum CreateGameMode {
            CreateGame_Mode_None = 0,           // 无
            CreateGame_Mode_Normal,             // 普通牌局
            CreateGame_Mode_MATCH,              //
            CreateGame_Mode_Short,              // 短牌局
            CreateGame_Mode_Other,              //
        }
        GameId: 2 德州，90 plo
    */
    _mockWinRateTools(mockDataIdx: number = 0, gameId: GameId = 2, gameMode: CreateGameMode = 1) {
        const mockWinRateData = this._mockDataSet.getWinRateData();
        const playerCards = mockWinRateData[mockDataIdx].playerCards;
        const publicCards = mockWinRateData[mockDataIdx].publicCards;

        let winRateResult = [];
        winRateResult = WinRateTools.getInstance().getWinRateByCards(gameId, gameMode, playerCards, publicCards);
    }

    _mockTurnTableReadyEvent(message: any)
    {
        const msg = message || this._config.MockTurnTableReadyEvent;
        cv.worldNet.OnLuckTurnTableReady(msg);
    }

    _mockTurnTableEvent(message) {
        const msg = message || this._config.MockTurnTableEvent;
        console.log(msg);
        console.log('mock turn table event');
        cv.dataHandler.getUserData().luckTurntables = [];

        for (let i = 0; i < msg.draw_list.length; i++) {
            cv.dataHandler.getUserData().luckTurntables.push(msg.draw_list[i]);
        }
        if (cv.dataHandler.getUserData().luckTurntables.length > 0) {
            cv.MessageCenter.send('drawRedPackage');
        } else {
            cv.MessageCenter.send('updataLuckTurntablesButton');
        }
    }

    _mockTurnTableResultNotice(msg) {
        if (msg.uid !== cv.dataHandler.getUserData().u32Uid) {
            cv.MessageCenter.send('turntableResultNotice', msg);
        }
    }

    _mockSportLogin(msg) {
        const { gameid, matchId } = msg;
        cv.roomManager.RequestJoinSportsRoom(gameid, matchId);
    }

    _mockExitGame() {
        cv.MessageCenter.send('Exit_click');
    }

    _mockExitHappyFishingGame() {
        cv.MessageCenter.send('fishingKingCloseWindowNotice');
    }

    _mockSportsBettingMatches(message: any) {
        const msg = message || this._config.MockSportsBettingMatches;
        cv.MessageCenter.send('sportMatchListResponse', msg);
    }

    _mockNoticeJackPotAwardInfo(msg: any) {
        cv.worldNet.noticeJackPotAwardMsg(msg);
    }

    _mockNoticeJackpotAwardRecordInfo(msg: any) {
        cv.worldNet.NoticeJackpotAwardRecordMsg(msg);
    }

    _mockGameReview(message: any) {
        const msg = message || this._config.MockGameReviewResults;
        console.log(`Mock Game Review Results added: ${msg} (Normal game mode only)`);
        cv.GameDataManager.tRoomData.pkRoomParam.is_associated_jackpot = true;
        cv.httpHandler._onGameHand(msg);
    }

    /*
    msg type = game_pb.LuckStarSeatDrawResultNotic
    {
        user_id: number;
        amount: number;
        is_help_wrap: boolean;
    }
    */
    _mockStarRedPacketResult() {
        const msg = {
            user_id: 41315,
            amount: 999,
            is_help_wrap: true
        };
        cv.MessageCenter.send('star_redpacket_result', msg);
    }

    private _mockJackpotNumberAmount(message: any) {
        const msg = message || this._config.JackpotNumberAmountUpdate;
        cv.worldNet.onNoticeJackpotAmountUpdateMsg(msg);
    }

    // e.g., mockEventEmitter.emit("MockNeedRelogin", 197)
    // 197: need re-login due to acount is banned
    _mockNeedRelogin(errorCode) {
        console.log(`Mock Game relogin msg, error code: ${errorCode}`);
        cv.netWorkManager.OnNeedRelogin(errorCode);
    }

    // mock Marquee (跑马灯 ) text
    _mockNoticeData(str: string, type: PushNoticeType = PushNoticeType.PUSH_WORLD) {
        const data: PushNoticeData = new PushNoticeData();
        data.str = str;
        data.msgType.push(type);
        PushNotice.getInstance().addPushNotice(data);
    }

    private _mockFriendLinesTools(message: any) {
        const msg = message || this._config.FriendLinesAnimation;
        cv.MessageCenter.send('friendLines_Result', msg);
    }

    _mockCowboyAutoBetAdd(amount: number = 5) {
        MockMiniGameLogic.getInstance()._mockCowboyAutoBetAdd(amount);
    }

    _mockHumanboyAutoBetAdd(amount: number = 5) {
        MockMiniGameLogic.getInstance()._mockHumanboyAutoBetAdd(amount);
    }

    _mockPokerMasterAutoBetAdd(amount: number = 5) {
        MockMiniGameLogic.getInstance()._mockPokerMasterAutoBetAdd(amount);
    }

    _mockReachLimitBet() {
        MockMiniGameLogic.getInstance()._mockReachLimitBet(cv.roomManager.getCurrentGameID());
    }

    _mockAutoBetNoBalance() {
        MockMiniGameLogic.getInstance()._mockAutoBetNoBalance(cv.roomManager.getCurrentGameID());
    }

    _mockFavSpectatorRevealHand() {
        const value = this._config.MockFavSpectatorRevealHand;
        cv.httpHandler._onFavoriteHand(value);
    }

    _mockToggleMtt() {
        cv.config.HAVE_MTT = !cv.config.HAVE_MTT;
        cv.MessageCenter.send('update_mtt_state');
    }

    _mockLaba(message) {
        const msg = message || this._config.MockLaba;
        cv.worldNet.onNoticeLuckDrawMsg(msg);
    }

    // mockEventEmitter.emit("MockTicketGiftNotice")
    /**
     * 
     * "MockTicketGift": {
        "toolsInfos": [
            {
                "currency_type": 2,  // currency_type 0 is MTT ticket
                ...
            }            
        ]
    }, 
    case of "currency_type"
        case 0: // 门票
        case 1:         // 积分
        case 2:         // 金币
        case 3:         // 小游戏币
        case 4:         // usdt
        case 6:         // Trial coins
        case 7:         // PG免費小遊戲 (次 單位)
        case 8:         // PG紅利小遊戲(紅利 單位)
     */
    _mockTicketGiftNotice(message) {
        const msg = message || this._config.MockTicketGift;
        cv.MessageCenter.send('MockTicketGiftShow', msg);
    }

    // mockEventEmitter.emit("MockTicketGiftResponse")
    _mockTicketGiftResponse() {
        const msg = {
            error: 1
        }
        cv.MessageCenter.send('ReceiveToolsResponse', msg);
        
    }

    // window.mockEventEmitter.emit("MockTopNotice")
    _mockTopNotice(message) {
        const param: Partial<TNParams> = {
            type: TNTypes.MTT_GAME_START,
            title: " super test mtt gameName",
            roomId: 12345,
            time_seconds: 2459,
            mtt_game_start_type : world_pb.MttNotifyType.notify_type_60min,
         };
         
        cv.TN.showMsg(param as TNParams);
    }

    _mockMttGameStartNoti(message) {
        const msg = message || this._config.MockMttGameStart;
        const encodeMsg = cv.worldNet.encodePB("NoticeGlobalMessage", msg);
        cv.worldNet.HandleNoticeGlobalMessage(encodeMsg);
    }

    /**
     * From console window.mockEventEmitter.emit("MockRedPacketGiftResponse")
     */
    _mockRedPacketGift() {
        const msg = {"help_wrap_data":[{"helper_data":[],
        "captcha_data":{"code":522434,"create_time":Math.floor(Date.now()/1000),
        "expire_time":60,"is_available":false,"help_count":1,
        "user_prizes_data":{"luck_warp_type":0,"amount":100000,"ticket_url":"","ticket_name":"","ticket_count":0,
        "ticket_title":"","red_type":0},"share_image_url":"#"}}],"error":1,"left_help_count":50}
        if (msg) {
            if (msg.error == 1) {
                cv.dataHandler.getUserData().left_help_count = msg.left_help_count;
                cv.dataHandler.getUserData().help_wrap_list = [];
                cv.dataHandler.getUserData().isShow_help_warp = null;
                for (let i = 0; i < msg.help_wrap_data.length; i++) {
                    cv.dataHandler.getUserData().help_wrap_list.push(world_pb.HelpWrapInfo.create(msg.help_wrap_data[i]));
                }
                cv.MessageCenter.send("update_help_Warp_list");
                cv.MessageCenter.send("update_left_help_count");
                cv.MessageCenter.send("updata_my_redpackets_pos");
                cv.MessageCenter.send("sortATLView");
            }
            else {
                cv.ToastError(msg.error);
            }
        }
    }

    _mockGiftNotice (message) {
        const msg = message || this._config.MockGiftNotice ;
        cv.dataHandler.getUserData().giftToolNotice = msg;
        cv.MessageCenter.send("ReceiveToolsNotice", msg);
    }

    // window.mockEventEmitter.emit("MockTipsPanel", "test contnet")
    _mockTipsPanel(message) {
        cv.TP.showMsg(message, cv.Enum.ButtonStyle.TWO_BUTTON, null, null, false, "title");
    }
    
    // window.mockEventEmitter.emit("MockEllipsisRichText", "test RichText") 
    _mockEllipsisRichText(opt, strlen, message) {
        let msg=""
        switch(opt) {
            case 1: {
                msg = this._config.MockRichText.longRichText;
                break;
            }
            case 2: {
                msg = this._config.MockRichText.shortRichText;
                break;
            }
            case 3: {
                msg = this._config.MockRichText.longText;
                break;
            }
            case 4: {
                msg = this._config.MockRichText.shortText;
                break;
            }
            case 5: {
                msg = this._config.MockRichText.illegalLongRichText;
                break;
            }
            case 6: {
                msg = this._config.MockRichText.illegalShortRichText;
                break;
            }
            default: {
                msg = message
                break;
            }
        }
        msg = cv.StringTools.ellipsisRichText(msg , strlen);
        cv.TP.showRichText(msg, cv.Enum.ButtonStyle.TWO_BUTTON, null, null, false, "title");
    }

     // window.mockEventEmitter.emit("MockSwtichLineBadDomain", 1) 
    _mockSwtichLineBadDomain(lineIdx) {
        //lineIdx: this._domain[i]
        cv.domainMgr.mockSwitchLineBadDomain(this._config.MockBadDomain,lineIdx);
    }

    // window.mockEventEmitter.emit("MockAddSocketMsg","webSocekt") 
    // window.mockEventEmitter.emit("MockAddSocketMsg","http") 
    _mockAddSocketMsg(msgType:string) {
        cv.LoadingView.mockAddSocketMsg(msgType);
    }
    
    // window.mockEventEmitter.emit("MockConsume") 
    _mockConsume() {
        MockMiniGameLogic.getInstance().mockConsumingNotify(cv.roomManager.getCurrentGameID());
    }

    // window.mockEventEmitter.emit("MockMiniGameAtmosphereMessage") 
    _mockMiniGameAtmosphereMessage(){
        MockMiniGameLogic.getInstance().mockMiniGameAtmosphereMessage();
    }

    // window.mockEventEmitter.emit("MockBlackListApiCall") 
    _mockBlackListApiCall(){
        cv.http.requestUrl("https:/hghjfhjsgcv.com/ghdfhg", null, null);
    }

    // window.mockEventEmitter.emit("mockTwiceMd5", "aaa")
    _mockTwiceMd5(msg) {
        let res = cv.native.GetDeviceUUID(undefined, undefined, true);
        //res = cv.md5.md5(msg);
        //res = cv.md5.md5(res);
        console.log('_mockTwiceMd5, res:' + res);
    }

    _mockLobbySecurityGuardMainInformation() {
        const msg: world_pb.PlayerBanMainResponse = new world_pb.PlayerBanMainResponse();
        msg.total_ban_player_num = Math.floor(Math.random() * 5000);
        msg.total_month_ban_number = Math.floor(Math.random() * 300);
        cv.MessageCenter.send(cv.Enum.LobbySecurityGuardMessage.PlayerBanMainResponse, msg);
    }

    _mockLobbySecurityGuardPlayerBanList(pageSize: number, pageIndex : number) {


        function getRandomInt(min: number, max: number) : number {
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }

        function randomizePlayerData(index : number): pb.PlayerBanListResponse.IplayerData {
            const avatar = Math.random() < 0.5
                ? `https://uploadfile.dev.liuxinyi1.cn/avatar_45892_1738056132.jpg`
                : "https://uploadfile.dev.liuxinyi1.cn/avatar_45892_1738935956.jpg";
            const usdt = (index >= 15 && pageIndex === 0) ? 43364447 : (pageIndex === 0 ? 0 : getRandomInt(1, 99999));
            const money = (index >= 15 && pageIndex === 0) ? 43364447 : (pageIndex === 0 ? 0 : getRandomInt(1, 99999));

            // stable first page
            // const uid = pageIndex === 0 ? index : getRandomInt(1, 99999);

            // first page randomize everytime
            const uid = getRandomInt(1, 99999);

            let name : string = null;
            switch (index) {
                case 0:
                    name = `名称超级长名称名称超级长名称名${getRandomInt(1, 20000)}`;
                    break;
                case 1:
                    name = `名称超级长名称名称超级长名称名名称超级长名称名称超级长名称名`;
                    break;
                default:
                    name = `名称超级长名`;
                    break;
            }

            return {
                uid,
                name,
                avatar,
                plat: getRandomInt(0, 4),
                ban_time: Date.now() / 1000 + getRandomInt(1, 100) * 86400,
                is_related: Math.random() <= 0.5,
                usdt,
                money,
                violation_type: getRandomInt(3, 6),
            };
        }

        const playerBanListResponse: world_pb.PlayerBanListResponse = new world_pb.PlayerBanListResponse();

        playerBanListResponse.total = 50;
        playerBanListResponse.page_num = pageIndex;
        playerBanListResponse.page_size = pageSize;
        playerBanListResponse.players = Array.from({length : pageSize}, (_, index) => randomizePlayerData(index));

        cv.MessageCenter.send(cv.Enum.LobbySecurityGuardMessage.PlayerBanListResponse, playerBanListResponse);
    }

    mockLikeBarrage()
    {
        const likeNotice = {
            playerid: 45902,
            nickname: "a5team0009",
            notice: {
                roomid: 231525,
                ctype: 6,
                content: "3",
                playerid: 45902,
                nickname: "a5team0009",
                avatar: "avatar_45902_1739954294.jpg",
                send_time: 1742963899,
                thump_up_status: 4,
                liked_nickname: "",
                liked_playerid: 0,
                liked_avatar: "",
                plat: 0
            }
        };
        const noti: jackfruit_proto.NoticeSendBarrage  = likeNotice.notice as  jackfruit_proto.NoticeSendBarrage;
        cv.GameDataManager.addDanmuMsg(noti);
    }

    _mockRebateRank(data) {

        if (!data) {
            data = this._config.MockRebateRank;
        }

        cv.MessageCenter.send("onPopupMessageNotice", data);

    }
}
