import cv from "../components/lobby/cv";
import TestToolButton from "./TestToolButton";

const {ccclass, property} = cc._decorator;

@ccclass
export default class TestToolOverlay extends cc.Component {
    
    @property(cc.Node)
    public triggerBtn: cc.Node = null;
    
    @property(cc.Node)
    public buttonList: cc.Node = null;
    
    @property(cc.Node)
    public panelNode: cc.Node = null;
    
    @property(cc.Label)
    public versionLabel: cc.Label = null;
    
    @property(cc.Prefab)
    public buttonPrefab: cc.Prefab = null;
    
    private _isOpened: boolean = false;
    private _maxClickInterval: number = 300;
    private _clickCount: number = 0;
    private _lastClickTime: number = 0;
    
    private _hMax: number = 0;
    private _mosueDown: boolean = false;
    
    public init() {
        this.node.zIndex = cc.macro.MAX_ZINDEX;   
        this.node.parent.sortAllChildren();
        
        this.triggerBtn.on(cc.Node.EventType.TOUCH_END, this._onCheckClick, this);
        cv.MessageCenter.register("OnGoReconnect", this._onResumeConnection.bind(this), this.node);
        
        this.panelNode.on(cc.Node.EventType.TOUCH_START, this._onMouseDown, this);
        this.panelNode.on(cc.Node.EventType.TOUCH_END, this._onMouseUp, this);
        this.panelNode.on(cc.Node.EventType.TOUCH_CANCEL, this._onMouseUp, this);
        this.panelNode.on(cc.Node.EventType.TOUCH_MOVE, this._onMouseMove, this);  
        this.panelNode.on(cc.Node.EventType.SIZE_CHANGED, this._onSizeChanged, this);  
        
        this._hMax = (cc.winSize.height - this.panelNode.height) * -1;
        
        this._initButtons();
        
        this.versionLabel.string = `v${cv.config.GET_CLIENT_VERSION()}`;
    }
    
    private _initButtons() {
        this._initButton("Disconnect", this._onDisconnectClick);
        this._initButton("Refresh", this._onRefreshClick);
        this.buttonList.active = false;
    }
    
    private _initButton(label, callback) {
        const button = cc.instantiate(this.buttonPrefab);
        button.getComponent(TestToolButton).registerButton(label, callback);
        this.panelNode.addChild(button);
    }
    
    private _onCheckClick() {
        const currentTime = Date.now();
        
        if (currentTime - this._lastClickTime < this._maxClickInterval) {
            this._clickCount++;
        } else {
            this._clickCount = 1;
        }
        
        this._lastClickTime = currentTime;
        
        if(this._clickCount === 2) {
            this._isOpened = !this._isOpened;
            this.buttonList.active = this._isOpened;
            
            this._clickCount = 0;
        }
    }
    
    private _onMouseDown(event) {
        this._mosueDown = true;
    }
    
    private _onMouseUp(event) {
        this._mosueDown = false;
        cv.MessageCenter.send("OnTestToolMove", false);
    }
    
    private _onMouseMove(event: cc.Event.EventTouch) {
        if (!this._mosueDown) return;
        
        cv.MessageCenter.send("OnTestToolMove", true);
        
        let delta = event.getDelta();
        let newX = this.panelNode.x + delta.x;
        let newY = this.panelNode.y + delta.y;
            
        const wMin = this.panelNode.width;
        const wMax = cc.winSize.width;
        const hMin = 0;
        
        if(newX < wMax && newX > wMin) {
            this.panelNode.x = newX;
        }
        
        if(newY > this._hMax && newY < hMin) {
            this.panelNode.y = newY;
        }
    }
    
    private _onSizeChanged(event) {
        this.panelNode.position.y = 0;
        this._hMax = (cc.winSize.height - this.panelNode.height) * -1;
    }
    
    // Event triggered
    private _onResumeConnection() {
        Object.defineProperty(navigator, "onLine", {
            get: () => true,
            configurable: true
        });   
    }
    
    // Button click function
    private _onDisconnectClick() {
        Object.defineProperty(navigator, "onLine", {
            get: () => false,
            configurable: true
        });
        
        cv.netWork.close();
        cv.netWorkManager.closeGameConnect(true);
        cv.netWorkManager.closeWorldConnect(true);
        cv.netWorkManager.UpdateNetwork(0);
    }
    
    private _onRefreshClick() {
        cv.gameNet.RequestSnapshot(cv.GameDataManager.tRoomData.u32RoomId);
    }
}