import cv from "../components/lobby/cv";
import TestToolOverlay from "./TestToolOverlay";

const {ccclass} = cc._decorator;

@ccclass
export default class TestToolManager extends cc.Component {
    
    private testToolNode: cc.Node = null;
    
    private _testToolOverlay: TestToolOverlay = null;
    
    private static instance: TestToolManager;
    
    preloadRes(callback: Function): void {
        cv.resMgr.load("zh_CN/commonPrefab/TestToolOverlay", cc.Prefab, (prefab: cc.Prefab): void => {
            if (callback) callback();
        });
    }
    
    public static getInstance(): TestToolManager {
        if (!this.instance) {
            this.instance = new TestToolManager();
        }
        return this.instance;
    }
    
    public init() {
        const prefab: cc.Prefab = cv.resMgr.get("zh_CN/commonPrefab/TestToolOverlay", cc.Prefab);
        this.testToolNode = cc.instantiate(prefab);
        cc.game.addPersistRootNode(this.testToolNode);
        
        this._testToolOverlay = this.testToolNode.getComponent(TestToolOverlay);
        this._testToolOverlay.init();
    }
}
