// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import cv from "../cv";

export enum NodeGroupType {
    H5LiveStreamWebview,
    PokerWebview,
}
  
export type NodeGroupInfo = {
    enabledNodeCount: number;
    nodeList: Map<string, boolean>;
};
  
export class NodeStatusCenter extends cc.Component {
    public static nodeCountChangeEventName = 'NodeStatusCenter_NodeCountChange';
    private _nodeMap: Map<NodeGroupType, NodeGroupInfo> = new Map<NodeGroupType, NodeGroupInfo>();

    private static instance: NodeStatusCenter = null;
    public static getInstance(): NodeStatusCenter {
      if (!NodeStatusCenter.instance) {
        NodeStatusCenter.instance = new NodeStatusCenter();
      }
      return NodeStatusCenter.instance;
    }
  
    public static getNodeCountChangeEventName(nodeGroupType: NodeGroupType) : string {
        return NodeStatusCenter.nodeCountChangeEventName + "_" + NodeGroupType[nodeGroupType];
    }

    private _getNodeListKeyName(nodeId: string, nodeName: string):string {
      return nodeId + "_" + nodeName;
    }

    public updateNodeStatus(nodeGroup: NodeGroupType, nodeId: string, nodeName: string, enabled: boolean) {
      let groupInfo = this._nodeMap.get(nodeGroup);
      if (!groupInfo) {
        groupInfo = { enabledNodeCount: 0, nodeList: new Map<string, boolean>() };
        this._nodeMap.set(nodeGroup, groupInfo);
      }

      groupInfo.nodeList.set(this._getNodeListKeyName(nodeId, nodeName), enabled);

      this._udpatNodeGruopCounter(nodeGroup, enabled);  
    }
  
    private _udpatNodeGruopCounter(nodeGroup: NodeGroupType, enabled: boolean) {
        let groupInfo = this._nodeMap.get(nodeGroup);

        const preCount = groupInfo.enabledNodeCount;

        if(enabled) {
            groupInfo.enabledNodeCount++;
        } else {
            if(preCount > 0) groupInfo.enabledNodeCount--;
        }

        const curCount = groupInfo.enabledNodeCount;

        if( (preCount == 0 && curCount > 0) ||  (preCount > 0 && curCount == 0)) {
            let eventName = NodeStatusCenter.getNodeCountChangeEventName(nodeGroup);

            //send `nodeGroup` event
            cv.MessageCenter.send(eventName, curCount);
        }
    }

  
    public getEnabledNodeCount(nodeGroup: NodeGroupType): number {
      const groupInfo = this._nodeMap.get(nodeGroup);
      return groupInfo ? groupInfo.enabledNodeCount : 0;
    }


    public traverseValuesByGroup(nodeGroup: NodeGroupType) {
        const groupInfo = this._nodeMap.get(nodeGroup);
        if (!groupInfo) {
          return;
        }
    
        groupInfo.nodeList.forEach((nodeStatus, nodeName) => {
          console.log(`Node Name: ${nodeName}, Value: ${nodeStatus}`);
        });
    }
          
}