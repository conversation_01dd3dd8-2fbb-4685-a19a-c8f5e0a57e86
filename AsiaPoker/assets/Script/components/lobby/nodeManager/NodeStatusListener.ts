// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { NodeGroupType, NodeStatusCenter } from "./NodeStatusCenter";

const {ccclass, property} = cc._decorator;

@ccclass
export default class NodeStatusListener extends cc.Component {
    private _nodeStatusCenter: NodeStatusCenter = NodeStatusCenter.getInstance();
    private _nodeGroupSet: Set<NodeGroupType> = new Set<NodeGroupType>();
    private _isInit : boolean = false;

    protected onEnable(): void {
        if(!this._isInit) return;
        this._nodeGroupSet.forEach(nodeGroup =>{
            this._nodeStatusCenter.updateNodeStatus(nodeGroup, this.node.uuid, this.node.name, true);
        })
    }

    protected onDisable(): void {
        if(!this._isInit) return;
        this._nodeGroupSet.forEach(nodeGroup =>{
            this._nodeStatusCenter.updateNodeStatus(nodeGroup, this.node.uuid, this.node.name, false);
        })
    }

    public init(nodeGroups: NodeGroupType[]) {
        nodeGroups.forEach(group=>{
            this._nodeGroupSet.add(group);
        });

        this._nodeGroupSet.forEach(group =>{
            this._nodeStatusCenter.updateNodeStatus(group, this.node.uuid, this.node.name, this.node.active);
        });

        this._isInit = true;
    }


}

