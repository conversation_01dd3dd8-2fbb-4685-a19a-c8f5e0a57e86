import cv from '../cv';
import { GenericWebviewScene } from './GenericWebviewScene';

const { ccclass, property } = cc._decorator;
@ccclass
export class CaribbeanPokerScene extends GenericWebviewScene {
    @property(cc.Layout) webViewLayout: cc.Layout = null;

    protected onEnable(): void {
        super.onEnable();
        this.webViewLayout.enabled = false; // fix layout will auto alignment web view position, causing hide and show webview failure.
    }

    protected setCurrentScene() {
        cv.config.setCurrentScene(cv.Enum.SCENE.CARIBBEAN_POKER_SCENE);
    }

    protected setWebUrl() {
        this.web.url = cv.roomManager.getHabaCaribbeanUrl();
    }

    protected _tempFixViewIssue() {
        cv.SwitchLoadingView.show(cv.config.getStringData("Loading_resource"));

        this.scheduleOnce(() => {
            cv.SwitchLoadingView.hide();
            this.setWebActive(true);
        }, 2);
    }
}
