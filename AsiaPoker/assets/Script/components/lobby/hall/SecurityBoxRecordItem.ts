// Learn TypeScript:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html

import { pb } from "../../../../Script/common/pb/ws_protocol";
import { eTimeType } from "../../../common/tools/Enum";
import cv from "../cv";

const { ccclass, property } = cc._decorator;

@ccclass
export default class SecurityBoxRecordItem extends cc.Component {
    @property(cc.Label)
    lab_name: cc.Label = null;

    @property(cc.Label)
    lab_num: cc.Label = null;

    @property(cc.Label)
    lab_time: cc.Label = null;

    @property(cc.Sprite)
    lab_num_bg: cc.Sprite = null;

    @property(cc.Sprite)
    lab_icon_sprite: cc.Sprite = null;
    
    @property(cc.SpriteFrame)
    lab_icons: cc.SpriteFrame[] = [];

    @property(cc.Label)
    amountSign: cc.Label = null;

    @property(cc.Sprite)
    safeTypeIcon: cc.Sprite = null;

    @property(cc.SpriteFrame)
    cachedSafeTypeIcons: cc.SpriteFrame[] = [];

    //
    color_lab_plus: cc.Color = cc.color(240, 213, 170);
    color_lab_less: cc.Color = cc.color(122, 121, 122);

    color_lab_bg_plus: cc.Color = cc.color(49, 46, 44);
    color_lab_bg_less: cc.Color = cc.color(41, 41, 44);
    // msg: pb.BankDetailsSnapshot = null;

    msg = null;

    onLoad() {
        console.log(`SecurityBoxRecordItem - onLoad`);
        cv.resMgr.adaptWidget(this.node, true);
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);
    }
    onDestroy() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
    }
    initLanguage() {
        // console.log(`SecurityBoxRecordItem - initLanguage`);
        if (!this.msg) return;

        let isPlus = false;
        // let isPlus = this.msg.amount > 0;
        // this.lab_name.string = cv.config.getStringData("InquireView_content_" + this.msg.source_type);

        let typeString = "";
        if (this.msg.type === 1) {
            isPlus = true;
            typeString = cv.config.getStringData("Safe_deposit");
        } else if (this.msg.type === 2) {
            isPlus = false;
            typeString = cv.config.getStringData("Safe_takeout");
        } else if (this.msg.type === 3) {
            isPlus = false;
            typeString = cv.config.getStringData("Safe_frozen");
        } else if (this.msg.type === 4) {
            isPlus = true;
            typeString = cv.config.getStringData("Safe_thaw");
        }
        this.lab_name.string = typeString;

        this.lab_num.string = cv.StringTools.serverGoldToShowString(this.msg.amount);
        // this.lab_time.string = cv.StringTools.formatTime(this.msg.create_time, eTimeType.Month_Day_Hour_Min_Sec);
        this.lab_time.string = cv.StringTools.formatTime(this.msg.time, eTimeType.Month_Day_Hour_Min_Sec);
        this.lab_num.node.color = isPlus ? this.color_lab_plus : this.color_lab_less;
        this.lab_num_bg.node.color = isPlus ? this.color_lab_bg_plus : this.color_lab_bg_less;
        this.lab_icon_sprite.spriteFrame =  this.lab_icons[isPlus ? 1 : 0];

        this.amountSign.string = (isPlus ? "+" : "-");
        this.amountSign.node.color = isPlus ? this.color_lab_plus : this.color_lab_less;
        this.safeTypeIcon.spriteFrame = this.cachedSafeTypeIcons[this.msg.kind];
    }

    updateSVReuseData(index: number, dataArray: Array<any>): void {
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;

        this.msg = dataArray[index];
        console.log(`SecurityBoxRecordItem - updateSVReuseData`);
        // console.log(dataArray[index]);
        this.initLanguage();
    }

    updateItemData(index: number, data: any): void {
        console.log(`SecurityBoxRecordItem - updateItemData`);
        // console.log(data);
        this.msg = data;
        this.initLanguage();
    }
}
