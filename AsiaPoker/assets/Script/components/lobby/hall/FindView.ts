/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-continue */
import BJPVPConnector from '../../../../blackjackpvp/script/common/BJPVPConnector';
import { BJTranslation } from '../../../../blackjackpvp/script/common/BJPVPLangData';
import { Translate as BJTranslate } from '../../../../blackjackpvp/script/common/BJPVPTranslator';
import { BJPVPGameList } from '../../../../blackjackpvp/script/hall/BJPVPGameList';
import MTTConnector, { FindViewShowType } from '../../../../mtt/script/common/MTTConnector';
import { WorldWebSocket } from '../../../../mtt/script/common/net/worldWebsocket';
import { ImpokerHallFeature } from '../../../../mtt/script/feature/ImpokerHallFeature';
import PKFConnector from '../../../../pkf/script/common/PKFConnector';
import FindItemPKFStarLive from '../../../../pkf/script/hall/FindItemPKFStarLive';
import { ClickCounterCategory, CurrencyType, GameSizeType, SportAidEnum } from '../../../common/tools/Enum';

import ListView from '../../../common/tools/ListView';
import cv from '../cv';
import FindItem from './FindItem';
import FindItemStar from './FindItemStar';
import HallScene from './HallScene';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import FilterPanel from './FilterPanel';
import JackpotSngGameList from "../../../../jsng/script/game_list/JackpotSngGameList";
import { Translate, translationSet } from "../../../../mtt/script/common/Translator";
import { Translation } from "../../../../mtt/script/common/lang";
import { ImpokerHall } from '../../../../mtt/prefab/impoker/hall/hall_script/ImpokerHall';
import { MttPrefabPkw } from '../../../../mtt/prefab/impoker/hall/game_list/mtt/pkw/MttPrefabPkw';
import GameListFilter from '../../../../mtt/prefab/impoker/hall/game_list/mtt/pkw/GameListFilter';
import { BannerInfo } from '../../../data/DataHandler';
import RefreshTop from '../../../../Script/common/tools/RefreshTop';


export enum DiscoverGameType {
    ALL = 0,
    DZPK,
    DZPK_SHORT,
    AOF,
    BET,
    ZOOM_TEXAS,
    MTT,
    JACKFRUIT,
    PLO,
    BLACKJACKPVP,
    JSNG
}

const REQUEST_BOARD_LIST_TIME_LIMIT = 5 // second

const { ccclass, property } = cc._decorator;

@ccclass
export default class FindView extends cc.Component {
    @property(cc.ScrollView) scrollView: cc.ScrollView = null;

    @property(cc.ScrollView) scrollView_mtt: cc.ScrollView = null;

    @property(cc.Prefab) findItem: cc.Prefab = null;

    @property(RefreshTop) refreshTop: RefreshTop = null;

    itemData: world_pb.ClubGameSnapshotV3[] = [];

    weiData: Array<any> = [];

    xiaoData: Array<any> = [];

    zhongData: Array<any> = [];

    daData: Array<any> = [];

    helpData: Array<any> = [];

    noSeeFull: boolean = true;

    viewIndex: number = 0;

    isTopSend: boolean = false;

    isHelpView: boolean = false;

    sHelpName: string = '';

    quickPanelPos: cc.Vec2 = new cc.Vec2(0, 0);

    topScrollSize: cc.Size = new cc.Size(0, 0);

    isStart: boolean = false;

    isGetData: boolean = false;

    private fillter_txt: cc.Label;

    private fillter_line: cc.Node;
    private fillter_line_Pos: cc.Vec2;

    @property(cc.PageView) img_PageView: cc.PageView = null;

    @property(cc.ScrollView) gameType_scrollView: cc.ScrollView = null;

    @property(FilterPanel) filterPanelComponent: FilterPanel = null;

    @property(cc.Node) discover_panel: cc.Node = null;

    @property(cc.Node) gameType_panel: cc.Node = null;

    @property(cc.Node) pokerPage_panel: cc.Node = null;

    @property(cc.Node) findView: cc.Node = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) top: cc.Node = null;

    @property(cc.Node) topOriginal: cc.Node = null;

    @property(cc.Node) scrollviewPart: cc.Node = null;

    @property(cc.Node) bottom: cc.Node = null;

    @property(cc.Node) bottomOriginal: cc.Node = null;

    @property(cc.Layout) quickSeatlayout: cc.Layout = null;

    @property(cc.Node) quickSeatSafearea: cc.Node = null;

    @property(cc.Color) labelColorActiveToggle: cc.Color = cc.Color.BLACK;

    @property(cc.Node) quickSeatPanelRoot: cc.Node = null;

    _whiteCircleList: cc.Node[] = [];

    _gamebuttonList: cc.Node[] = [];

    _gameType: number = DiscoverGameType.DZPK;

    // 可配置  0 全部 1德州 2短牌 3AOF 4必下 5急速 6 mtt 7菠萝蜜 8奥马哈 9:21点 10:spins（数组包含的游戏 可以打乱顺序）
    PKW_GAME_TYPE: number[] = [1, 2, 8, 4, 7];

    // 可配置  0 全部 1德州 2短牌 3AOF 4必下 5急速
    PKC_GAME_TYPE: number[] = [1, 2, 4];

    _jspkArr: any[] = [];

    /** 记录游戏目录 */
    SAVE_gameType: string = 'last_gameType';

    /** 记录微小中大 */
    SAVE_wxzd: string = 'FindView_wxzd';

    /** 记录是否查看满桌 */
    SAVE_seeFull: string = 'FindView_noSeeFull';

    matchWeb: cc.WebView = null;

    matchWebNode: cc.Node = null;

    matchUrl: string = '';

    matchWeb_pos: cc.Vec2 = cc.v2(0, 0);

    webIsView: boolean = true;

    MTT_NUM: number = 6;

    BLACKJACK_NUM: number = 9;

    JSNG_NUM: number = 10;

    mtt_img: cc.Node = null;

    isClickScreenbtn: Boolean = false;

    isClickSeatbtn: Boolean = false;

    gameModeBtnScreenNum: number = 0;

    mttUseWebView: boolean = false;

    mtt: ImpokerHallFeature = null;

    mttUrl: string = '';

    /* mttInitPos = cc.v2(
        628,
        cc.sys.os === cc.sys.OS_IOS && cv.config.IS_FULLSCREEN ? 125 + cv.config.FULLSCREEN_OFFSETY_B : 125
    ); */

    mttInitPos = cc.v2(0, 0);

    mttResRetryCount: number = 0;

    mttMaxResRetryCount: number = 3;

    hasInitMTT: boolean = false;

    mttSetTimeout: any = null;

    @property(cc.Prefab) mttPrefab: cc.Prefab = null;

    @property(cc.Prefab) bjpvpPrefab: cc.Prefab = null;

    @property(cc.Prefab) jsngPrefab: cc.Prefab = null;

    bjpvp: BJPVPGameList = null;
    jsngGameList: JackpotSngGameList = null;

    scrollViewTop: number = 0;
    showJSNG: boolean = true;
    _button_margin = 31;
    _button_panel_side_margin = 8;
    
    _selectedItem: any = null;

    squid_tab_icon : cc.Node = null;
    isBlockFetchBoardList = false;
    lastFetchTime: number = 0;
    lastBoardListMd5: string = undefined;
    lastPKFStarLiveMd5: string = undefined;

    onLoad() {
        this.mttInitPos = cc.v2(
            cv.SafeAreaWithDifferentDevices.getSafeAreaHall() +
            this.topOriginal.height +
            this.img_PageView.node.height +
            this.gameType_panel.height,
            125
        );
        // 初始化添加21点
        if (cv.config.HAVE_BLACKJACK) {
            this.PKW_GAME_TYPE.splice(2, 0, this.BLACKJACK_NUM);
        }

        this.showJSNG = cv.config.HAVE_SPIN; // cv.config.HAVE_MTT && MTTConnector.instance.showJSNG;

        // 初始化添加mtt
        if (cv.config.HAVE_MTT) {
            this.PKW_GAME_TYPE.unshift(this.MTT_NUM);
        }

        if( this.showJSNG )
        {
            this.PKW_GAME_TYPE.splice(cv.config.HAVE_MTT? 1: 0, 0, this.JSNG_NUM);
        }

        cv.tools.SaveStringByCCFile('FINDVIEW_isFASTENTER', '0');
        cv.tools.SaveStringByCCFile('FINDVIEW_CURRENT_FILTER', CurrencyType.GOLD.toString());

        this.quickPanelPos = new cc.Vec2(this.quickSeatPanelRoot.position.x, cc.winSize.height * 1.5);

        this.topScrollSize = cc.size(this.filterPanelComponent.filterButtonsScrollContainer.parent.width, this.filterPanelComponent.filterButtonsScrollContainer.parent.height);

        this.mtt_img = cc.find('discover_panel/LayoutParent/ScrollViewPart/mttImg', this.node);

        const mtt_img_widget = this.mtt_img.getComponent(cc.Widget);

        if (mtt_img_widget) {
            mtt_img_widget.bottom =
                cc.sys.os === cc.sys.OS_IOS && cv.config.IS_FULLSCREEN ? 120 + cv.config.FULLSCREEN_OFFSETY_B : 120;
        }

        this.registerMsg();

        this.img_PageView.node.on('page-turning', this.touchImgPageview, this);

        this.fillter_txt = cc
            .find('discover_panel/LayoutParent/pokerPage_panel/button4/label', this.node)
            .getComponent(cc.Label);

        this.fillter_line = cc.find('discover_panel/LayoutParent/pokerPage_panel/button4/selectimg', this.node);

        this.fillter_line_Pos = this.fillter_line.getPosition();
        this._gamebuttonList = [];

        const cell = this.gameType_panel.getChildByName('button_0');

        cc.find('new_icon', cell).active = false;

        const len = cv.config.isOverSeas() ? this.PKC_GAME_TYPE.length : this.PKW_GAME_TYPE.length;

        // 关闭滚动(按照原来算法逻辑的表现效果是不滚动的, 若后续需要滚动屏蔽此处修改)
        this.gameType_scrollView.horizontal = false;

        this.gameType_scrollView.vertical = false;

        // // 排版起点坐标

        for (let i = 0; i < len; i++) {
            const button = cc.instantiate(cell);

            let tag;

            if (cv.config.isOverSeas()) {
                tag = this.PKC_GAME_TYPE[i];
            } else {
                tag = this.PKW_GAME_TYPE[i];
            }

            button.name = 'btn_' + tag;

            button.on(
                'click',
                (event: cc.Event) => {
                    this.setViewGametype(tag, true);
                    this.filterPanelComponent.reset();

                    cv.AudioMgr.playButtonSound('tab');

                    if (tag === this.MTT_NUM && cv.config.HAVE_MTT) {
                        this.onShowMttTab();
                    } else if (tag === this.BLACKJACK_NUM && cv.config.HAVE_BLACKJACK) {
                        this.onShowBJPVPTab();
                    } else if (tag === this.JSNG_NUM && cv.config.HAVE_SPIN) {
                        this.onShowJSNGTab();
                    } else {
                        this.HandleCheckMTT(false);

                        this.handleCheckBJPVP(false);

                        this.handleCheckJSNG(false);

                        this.updateGameTypeDataAndView();
                    }

                    this.scrollView.scrollToTop(0.05);

                    // 跟踪用户行为, 发送事件
                    const gameType: string = button.getChildByName('group').getChildByName('text').getComponent(cc.Label).string;

                    const properties = { gameType };

                    cv.segmentTool.track(
                        cv.Enum.CurrentScreen.lobby,
                        cv.Enum.segmentEvent.LobbyGameTypeSelected,
                        cv.Enum.Functionality.poker,
                        properties
                    );
                },
                this
            );

            this._gamebuttonList.push(button);

            this.gameType_scrollView.content.addChild(button);
            
        }
        

        cell.active = false;

        this.noSeeFull = cv.tools.GetStringByCCFile(this.SAVE_seeFull) === '1';

        this.viewIndex = cv.Number(cv.tools.GetStringByCCFile(this.SAVE_wxzd));

        if (this._gameType === DiscoverGameType.PLO && this.viewIndex !== 0) {
            this.viewIndex = 0;
        }

        this.initGameType();

        for (let i = 0; i < 4; i++) {
            cc.find('discover_panel/LayoutParent/pokerPage_panel/button' + i, this.node).on(
                cc.Node.EventType.TOUCH_END,

                this.onBtnCardListTypeClick,

                this
            );
        }

        cc.find('discover_panel/LayoutParent/pokerPage_panel/button3/action_node', this.node).active = false;

        this.changeView(this.viewIndex, true);

        this.changeNoSeeFull(this.noSeeFull);

        this.quickSeatPanelRoot.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
            this.onClickCloseBtn();
        });

        this.initLanguage();

        // 第一次加载脚本时请求数据(因为"HallScen"的默认界面现在改为配置下发, 不一定是"FindView",
        // 所以要重新请求下, 原来是登录wold服就请求, 但是该界面没有激活, 也就是没有提前注册接受消息, 所以数据会被过滤掉)
        PKFConnector.instance.StarLiveManager.requestPKFStarLiveList();

        // 下拉滚动时请求刷新列表数据
        this.refreshTop.setRefreshListener(() => {
            this.isTopSend = true;
            if (!this.isBlockFetchBoardList) {
              this.isBlockFetchBoardList = true;
              this.scheduleOnce(this.enableFetchBoardList.bind(this), REQUEST_BOARD_LIST_TIME_LIMIT);
              cv.worldNet.requestCurrentBoardList();
              PKFConnector.instance.StarLiveManager.requestPKFStarLiveList();
              this.resetAutoFetchBoardList();
            } else {
              this.scheduleOnce(this.hideTopSend.bind(this), 0.1);
            }
        });

        if (cv.config.HAVE_MTT) {
            this.initMTT();
        }

        this.resetButton();
        this.resetAutoFetchBoardList();
    }

    start() {
        cv.worldNet.BannerRequest();

        this.isStart = true;

        this.scrollView.getComponent(ListView).bindScrollEventTarget(this);

        this.scrollView.getComponent(ListView).init(this.bindcallfunc.bind(this), this.getItemType.bind(this));

        if (this.isGetData) {
            this.showViewForData();
        }

        if ((this._gameType === this.MTT_NUM || this._gameType === this.JSNG_NUM) && (cv.config.HAVE_MTT || cv.config.HAVE_SPIN) ) {
            switch (this._gameType) {
                case this.MTT_NUM:
                    this.onShowMttTab();
                    break;
                case this.JSNG_NUM:
                    this.onShowJSNGTab();
                    break;
            }

            this.scrollView.node.active = false;

            this.updatePokerPagePanelActive();
        } else if (this._gameType === this.BLACKJACK_NUM && cv.config.HAVE_BLACKJACK) {
            this.onShowBJPVPTab();

            this.scrollView.node.active = false;

            this.updatePokerPagePanelActive();
        } else {
            this.mtt_img.active = false;
        }

        cv.resMgr.adaptWidget(this.node, true);
        this.resetButton();
    }

    resetButton()
    {
        const layout:cc.Layout=this.gameType_scrollView.content.getComponent(cc.Layout);
        const diff=this.gameType_scrollView.content.width-(this.calculateTotalWidth()+(this._button_margin/2));
        const padding=diff/this._gamebuttonList.length;
        layout.spacingX=padding;
        layout.paddingLeft=this._button_margin;
        layout.updateLayout();
        cv.resMgr.adaptWidget(this.gameType_scrollView.content, true);
    }

    calculateTotalWidth():number
    {
        let allButtonSize:number=0;
        for(let i = 0; i < this._gamebuttonList.length; i++){
            const button=this._gamebuttonList[i];
            let buttonSize= button.getChildByName('group').getChildByName("text").width;
            if(this.squid_tab_icon?.active){
                const squidIcon=this._gamebuttonList[i].getChildByName('group').getChildByName("squid_hat_icon");
                if(squidIcon?.active)
                    buttonSize+=squidIcon.width+1;// +1 is padding this is for exact match with layaot value
            }
            button.setContentSize(buttonSize, button.height);
            button.getChildByName('group').getComponent(cc.Layout).updateLayout();
            allButtonSize+=buttonSize;
        }
        return allButtonSize;
    }

    protected onEnable(): void {
        this.updateGameBtn();
    }
    

    onDestroy() {
        cv.dataHandler.clearBanner();

        cv.MessageCenter.unregister('noticePKFStarLiveList', this.node);

        cv.MessageCenter.unregister('noticeCurrentBoardList', this.node);
        
        cv.MessageCenter.unregister('hideTopSend', this.node);

        cv.MessageCenter.unregister('Join_room', this.node);

        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);

        cv.MessageCenter.unregister('update_bannerImg', this.node);

        cv.MessageCenter.unregister('update_mtt_state', this.node);
        cv.MessageCenter.unregister('update_JSNG_state', this.node);

        cv.MessageCenter.unregister('update_blackJack_state', this.node);

        cv.MessageCenter.unregister('FindView_showMttError', this.node);

        cv.MessageCenter.unregister('mttNotify', this.node);

        cv.MessageCenter.unregister('onAuthBlackJackSucc', this.node);

        cv.MessageCenter.unregister('onAuthBlackJackError', this.node);

        cv.MessageCenter.unregister('onAuthMttSucc', this.node);

        cv.MessageCenter.unregister('onAuthMttError', this.node);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.SquidGameBlockStatusResponse, this.node);
        cv.MessageCenter.unregister(cv.Enum.SQUID_EVENT.SquidGameLeaveExistingRoomResponse, this.node);
        cv.MessageCenter.unregister('FindView_RefreshBoardList', this.node);
        this.unschedule(this.fetchBoardList);
    }

    setLanguage() {
        cv.worldNet.BannerRequest();

        this.initLanguage();

        this.setStarTableDotPos();
    }

    initLanguage() {
        if (cv.config.HAVE_BLACKJACK) {
            BJTranslate.SetLanguage(BJPVPConnector.instance.getSelfCurrentLanguage());
        }

        cc.find('discover_panel/title_text', this.node).getComponent(cc.Label).string = cv.config.getStringData(
            'MainScene_Scene_discover_panel_title_text'
        );

        cc.find('discover_panel/club_button/Label', this.node).getComponent(cc.Label).string = cv.config.getStringData(
            'MainScene_Scene_discover_panel_club_button'
        );

        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            cc
                .find('discover_panel/LayoutParent/pokerPage_panel/button0/text', this.node)
                .getComponent(cc.Label).fontSize = 44;

            cc
                .find('discover_panel/LayoutParent/pokerPage_panel/button1/text', this.node)
                .getComponent(cc.Label).fontSize = 44;

            cc
                .find('discover_panel/LayoutParent/pokerPage_panel/button2/text', this.node)
                .getComponent(cc.Label).fontSize = 44;

            cc
                .find('discover_panel/LayoutParent/pokerPage_panel/button3/text', this.node)
                .getComponent(cc.Label).fontSize = 44;
        } else {
            cc
                .find('discover_panel/LayoutParent/pokerPage_panel/button0/text', this.node)
                .getComponent(cc.Label).fontSize = 35;

            cc
                .find('discover_panel/LayoutParent/pokerPage_panel/button1/text', this.node)
                .getComponent(cc.Label).fontSize = 35;

            cc
                .find('discover_panel/LayoutParent/pokerPage_panel/button2/text', this.node)
                .getComponent(cc.Label).fontSize = 35;

            cc
                .find('discover_panel/LayoutParent/pokerPage_panel/button3/text', this.node)
                .getComponent(cc.Label).fontSize = 35;
        }

        // 筛选
        this.setFillterStatus();

        cv.StringTools.setLabelString(
            this.node,
            'discover_panel/LayoutParent/pokerPage_panel/button0/text',
            'MainScene_Scene_pokerPage_panel_button0_text'
        );

        cv.StringTools.setLabelString(
            this.node,
            'discover_panel/LayoutParent/pokerPage_panel/button1/text',
            'MainScene_Scene_pokerPage_panel_button1_text'
        );

        cv.StringTools.setLabelString(
            this.node,
            'discover_panel/LayoutParent/pokerPage_panel/button2/text',
            'MainScene_Scene_pokerPage_panel_button2_text'
        );

        cv.StringTools.setLabelString(
            this.node,
            'discover_panel/LayoutParent/pokerPage_panel/button3/text',
            'MainScene_Scene_pokerPage_panel_button3_text'
        );

        cv.StringTools.setLabelString(
            this.node,
            'discover_panel/LayoutParent/pokerPage_panel/checkBoxButton/openseat_text',
            'MainScene_Scene_pokerPage_panel_Text_2'
        );

        cv.StringTools.setLabelString(
            this.node,
            'quick_seat_touch_panel/quick_seat_panel/LayoutParent/top/title_text',
            'MainScene_Scene_pokerPage_panel_seat_button_text'
        );

        cv.StringTools.setLabelString(
            this.node,
            'quick_seat_touch_panel/quick_seat_panel/LayoutParent/game_mode_panel/LayoutParent/bottom/seat_button/Label',
            'MainScene_Scene_pokerPage_panel_seat_button_text'
        );

        cv.StringTools.setLabelString(
            this.node,
            'quick_seat_touch_panel/quick_seat_panel/LayoutParent/game_mode_panel/LayoutParent/bottom/screen_button/Label',
            'MainScene_Scene_pokerPage_panel_screen_button_text'
        );

        const btnLen = this._gamebuttonList.length;

        for (let i = 0; i < btnLen; i++) {
            const isNewYear: boolean = cv.config.isShowNewYear();

            const text = this._gamebuttonList[i].getChildByName('group').getChildByName('text').getComponent(cc.Label);

            const newyear_icon_left = this._gamebuttonList[i].getChildByName('newyear_icon_left');

            const newyear_icon_right = this._gamebuttonList[i].getChildByName('newyear_icon_right');

            let tag;

            if (cv.config.isOverSeas()) {
                tag = this.PKC_GAME_TYPE[i];
            } else {
                tag = this.PKW_GAME_TYPE[i];
            }

            if (tag === this.MTT_NUM && cv.dataHandler.getUserData().isViewWPT) {
                text.string = 'WPT';
            } else if (tag === this.BLACKJACK_NUM) {
                text.string = BJTranslate(BJTranslation.HALL.GAME_LIST.GAME_NAME);
            } else if (tag == this.JSNG_NUM) {
                text.string = Translate(Translation.GAME_LIST.JSNG as unknown as translationSet);
            } else {
                text.string = cv.config.getStringData(
                    cv.StringTools.formatC('MainScene_Scene_gameType_panel_button%d_text', tag)
                );
            }

            if (btnLen > 6) {
                if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
                    text.fontSize = 36;
                } else {
                    text.fontSize = isNewYear ? 26 : 36;// 29;
                }
            } else if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
                text.fontSize = 44;
            } else {
                text.fontSize = isNewYear ? 26 : 36;// 32;
            }

            const isShowNew: boolean = false;

            const button = this._gamebuttonList[i];

            const _newIcon = cc.find('new_icon', button);

            if (isShowNew) {
                const text = cc.find('text', button);

                text.getComponent(cc.Label).string = cv.config.getStringData(
                    cv.StringTools.formatC('MainScene_Scene_gameType_panel_button%d_text', tag)
                );

                _newIcon.x = text.x + cv.resMgr.getLabelStringSize(text.getComponent(cc.Label)).width / 2;
            }

            _newIcon.active = isShowNew;

            const textpos = text.node.getPosition();

            const labelWidth = cv.resMgr.getLabelStringSize(text).width;

            newyear_icon_left.setPosition(
                textpos.x - labelWidth / 2 - newyear_icon_left.getContentSize().width / 2 - 2,
                newyear_icon_left.getPosition().y
            );

            newyear_icon_right.setPosition(
                textpos.x + labelWidth / 2 + newyear_icon_right.getContentSize().width / 2 + 2,
                newyear_icon_left.getPosition().y
            );
        }

        // 适配不看满桌
        const tab01_line2 = cc.find('discover_panel/LayoutParent/pokerPage_panel/tab01_line2', this.node);

        const openseat_text = cc.find(
            'discover_panel/LayoutParent/pokerPage_panel/checkBoxButton/openseat_text',
            this.node
        );

        const CheckBox = cc.find('discover_panel/LayoutParent/pokerPage_panel/checkBoxButton/CheckBox', this.node);

        const checkBoxButton = cc.find('discover_panel/LayoutParent/pokerPage_panel/checkBoxButton', this.node);

        const contentWidth = openseat_text.getContentSize().width + CheckBox.getContentSize().width;

        const offsetX =
            contentWidth > checkBoxButton.getContentSize().width
                ? contentWidth - checkBoxButton.getContentSize().width
                : 0;

        const posX = tab01_line2.x - 8 - checkBoxButton.getContentSize().width / 2 - offsetX;

        checkBoxButton.setPosition(posX, checkBoxButton.y);
        this.updateGameBtn();
    }

    private registerMsg() {
        cv.MessageCenter.register('noticePKFStarLiveList', this.noticePKFStarLiveList.bind(this), this.node);

        cv.MessageCenter.register('noticeCurrentBoardList', this.noticeCurrentBoardList.bind(this), this.node);
        
        cv.MessageCenter.register('hideTopSend', this.hideTopSend.bind(this), this.node);

        cv.MessageCenter.register('Join_room', this.inputPsdForJoinRoom.bind(this), this.node);

        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.setLanguage.bind(this), this.node);

        cv.MessageCenter.register('update_bannerImg', this.initBanner.bind(this), this.node);

        cv.MessageCenter.register('update_mtt_state', this.updateMTTState.bind(this), this.node);
        cv.MessageCenter.register('update_JSNG_state', this.updateJSNGState.bind(this), this.node);

        cv.MessageCenter.register('update_blackJack_state', this.updateBlackJackState.bind(this), this.node);

        cv.MessageCenter.register('FindView_showMttError', this.showMttError.bind(this), this.node);

        cv.MessageCenter.register('mttNotify', this.mttNotify.bind(this), this.node);

        cv.MessageCenter.register('onAuthMttSucc', this.onAuthMttSucc.bind(this), this.node);

        cv.MessageCenter.register('onAuthMttError', this.onAuthMttError.bind(this), this.node);

        cv.MessageCenter.register('onAuthBlackJackSucc', this.onAuthBlackJackSucc.bind(this), this.node);

        cv.MessageCenter.register('onAuthBlackJackError', this.onAuthBlackJackError.bind(this), this.node);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.SquidGameBlockStatusResponse, this._onSquidGameBlockStatusResponse.bind(this), this.node);
        cv.MessageCenter.register(cv.Enum.SQUID_EVENT.SquidGameLeaveExistingRoomResponse, this._onSquidGameLeavingResponse.bind(this), this.node);
        cv.MessageCenter.register('FindView_RefreshBoardList', this.refreshBoardList.bind(this), this.node);
        cc.game.on('OnJoinQuickSeatCaptchaResult', this.joinFastSeat, this);
        cc.game.on('OnFilterTablesButtonClicked', this.onFilterTablesButtonClickedListener, this);
    }

    inputPsdForJoinRoom(room_id: number) {
        cv.TP.showMsg(
            '',
            cv.Enum.ButtonStyle.TWO_BUTTON,
            this.checkPwd.bind(this, room_id),
            null,
            true,
            cv.config.getStringData('TipsPanel_Title1')
        );
    }

    checkPwd(room_id: number) {
        const str = cv.TP.getEditBoxString();

        const index = str.search(' ');

        if (str.length <= 0 || index !== -1) {
            cv.TT.showMsg(cv.config.getStringData('ErrorCode2'), cv.Enum.ToastType.ToastTypeWarning);

            return;
        }

        cv.roomManager.RequestJoinRoom(cv.Enum.GameId.Texas, room_id, false, true, str);
    }

    onSVEventBounceTop(arg: cc.ScrollView): void { }

    // 滑动列表中
    onSVEventScrolling(arg: cc.ScrollView): void { }

    // 滑动列表松手
    onSVEventTouchCancel(arg: cc.ScrollView) { }

    noticePKFStarLiveList() {
        if (this.isStart && this.isGetData) {
            this.showScrollView();
        }
    }

    hideTopSend() {
        if (this.isTopSend) {
            this.isTopSend = false;
            this.refreshTop.hideRefresh(null);
        }
    }

    noticeCurrentBoardList() {
        if (this.isTopSend) {
            this.isTopSend = false;

            this.refreshTop.hideRefresh(() => {
                this.itemData = cv.clubDataMgr.getClubCurrentBoardList();
                this.initJspkList();
                this.updateGameTypeDataAndView();
                this.filterPanelComponent.masterTableList = this.GetAllEntries();
            });

            return;
        }

        this.itemData = cv.clubDataMgr.getClubCurrentBoardList();
        this.initJspkList();
        this.updateGameTypeDataAndView();
        this.filterPanelComponent.masterTableList = this.GetAllEntries();
    }

    setStarTableDotPos() {
        // 中英文切换的时候，保证红点在文字右上角
        const btnLen = this._gamebuttonList.length;

        for (let i = 0; i < btnLen; i++) {
            // 红点标识
            const star_icon_dot = cc.find('star_icon_dot', this._gamebuttonList[i]);

            const text = cc.find('group/text', this._gamebuttonList[i]);

            star_icon_dot.x = text.x + cv.resMgr.getLabelStringSize(text.getComponent(cc.Label)).width / 2 + 8;

            // new图标
            const _newIcon = cc.find('new_icon', this._gamebuttonList[i]);

            if (_newIcon && _newIcon.active) {
                _newIcon.x = text.x + cv.resMgr.getLabelStringSize(text.getComponent(cc.Label)).width / 2;
            }
        }

        for (let i = 0; i < 4; i++) {
            const icon = cc.find(
                cv.StringTools.formatC('discover_panel/LayoutParent/pokerPage_panel/button%d/live_icon', i),
                this.node
            );

            const text = cc.find(
                cv.StringTools.formatC('discover_panel/LayoutParent/pokerPage_panel/button%d/text', i),
                this.node
            );

            icon.x = text.x + cv.resMgr.getLabelStringSize(text.getComponent(cc.Label)).width / 2 + 5;
        }
    }

    // 设置明星桌live和红点状态
    setStarTableStatus() {
        // 德州明星桌
        const haveTexasStar = this.getHaveTexasStarTable(this.itemData, cv.Enum.CreateGameMode.CreateGame_Mode_Normal);

        // 短牌明星桌
        const haveShortStar = this.getHaveTexasStarTable(this.itemData, cv.Enum.CreateGameMode.CreateGame_Mode_Short);

        // 显示live图标
        const btnLen = this._gamebuttonList.length;

        for (let i = 0; i < btnLen; i++) {
            let tag = -1;

            if (cv.config.isOverSeas()) {
                tag = this.PKC_GAME_TYPE[i];
            } else {
                tag = this.PKW_GAME_TYPE[i];
            }

            // live标识
            const star_icon_live = cc.find('star_icon_live', this._gamebuttonList[i]);

            // 红点标识
            const star_icon_dot = cc.find('star_icon_dot', this._gamebuttonList[i]);

            star_icon_live.active = false;

            star_icon_dot.active = false;

            let _curHaveStar = 0;

            if (tag === 1) {
                // 德州
                _curHaveStar = haveTexasStar;
            } else if (tag === 2) {
                // 短牌
                _curHaveStar = haveShortStar;
            }

            if (_curHaveStar === 1) {
                // 有在线明星桌
                star_icon_dot.active = true;
            } else if (_curHaveStar === 2) {
                // 有已经下播的明星桌
                star_icon_live.active = true;
            }
        }

        // 级别红点显示
        const haveWeiStar = this.getHaveTexasStarTable(this.weiData);

        const haveXiaoStar = this.getHaveTexasStarTable(this.xiaoData);

        const haveZhongStar = this.getHaveTexasStarTable(this.zhongData);

        const haveDaStar = this.getHaveTexasStarTable(this.daData);

        cc.find('discover_panel/LayoutParent/pokerPage_panel/button0/live_icon', this.node).active = !!(
            haveWeiStar === 2 && this.viewIndex !== 0
        );

        cc.find('discover_panel/LayoutParent/pokerPage_panel/button1/live_icon', this.node).active = !!(
            haveXiaoStar === 2 && this.viewIndex !== 1
        );

        cc.find('discover_panel/LayoutParent/pokerPage_panel/button2/live_icon', this.node).active = !!(
            haveZhongStar === 2 && this.viewIndex !== 2
        );

        cc.find('discover_panel/LayoutParent/pokerPage_panel/button3/live_icon', this.node).active = !!(
            haveDaStar === 2 && this.viewIndex !== 3
        );

        this.setStarTableDotPos();
    }

    setSquidTableStatus() {
        if (!this.squid_tab_icon) this.squid_tab_icon = this.getSquidTabIcon();
        const _isSquidGameTableAvailable = this.itemData.filter(table => table.game_id === cv.Enum.GameId.Squid).length > 0;
        if (_isSquidGameTableAvailable && cv.dataHandler?.getUserData()?.featureFlags?.show_squid_icon && this.squid_tab_icon){
            this.squid_tab_icon.active = true;
            this.resetButton();
        }
        else if (this.squid_tab_icon) this.squid_tab_icon.active = false;

    }

    getSquidTabIcon(): cc.Node {
        const btnLen = this._gamebuttonList.length;
        for (let i = 0; i < btnLen; i++) {
            let tag = -1;

            if (cv.config.isOverSeas()) {
                tag = this.PKC_GAME_TYPE[i];
            } else {
                tag = this.PKW_GAME_TYPE[i];
            }
            const squid_icon = this._gamebuttonList[i].getChildByName('group').getChildByName("squid_hat_icon");
            squid_icon.active = false;

            if (tag === 1) {
                return squid_icon;
            }
        }
        return null;
    }

    // 获取房间数据中是否有明星桌
    // roomData: 搜索房间数据
    // gameMode：指定的类型，默认CreateGame_Mode_None，为搜索全部
    // 返回值:0  表示该房间类型没有任何明星桌
    //       1  表示有明星桌，全部明星处于下播状态
    //       2  表示有明星桌，明星桌有明星处于在线状态 （包括未开播状态）
    getHaveTexasStarTable(roomData: any[], gameMode: number = cv.Enum.CreateGameMode.CreateGame_Mode_None): number {
        const itemLen = roomData.length;

        let ret = 0;

        const starRoomData: Array<any> = [];

        for (let i = 0; i < itemLen; i++) {
            if (gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_None) {
                if (roomData[i].game_id === cv.Enum.GameId.StarSeat) {
                    starRoomData.push(roomData[i]);
                }
            } else if (roomData[i].game_id === cv.Enum.GameId.StarSeat && gameMode === roomData[i].game_mode) {
                starRoomData.push(roomData[i]);
            }
        }

        if (starRoomData.length > 0) {
            ret = 1;
        }

        for (let i = 0; i < starRoomData.length; i++) {
            const _roomData = starRoomData[i];

            const _starData = _roomData.starData;

            for (let j = 0; j < _starData.length; j++) {
                if (_starData[j].status !== 2) {
                    // 0.未开播  1. 在线 2.已下播
                    ret = 2;

                    break;
                }
            }
        }

        return ret;
    }

    public GetAllEntries(): world_pb.ClubGameSnapshotV3[] {
        return [...this.weiData, ...this.xiaoData, ...this.zhongData, ...this.daData];
    }

    updateGameTypeDataAndView(): void {
        const tempData = this.screenByGame();
        const itemLen = cv.StringTools.getArrayLength(tempData);
        this.updateGameBtn();

        this.weiData = [];
        this.xiaoData = [];
        this.zhongData = [];
        this.daData = [];

        // 必下 除开微距  <=5 ante的是小局 10-100 ante是中局 >100 ante是大局
        if (this._gameType === DiscoverGameType.ALL || this._gameType === DiscoverGameType.BET) {
            for (let i = 0; i < itemLen; i++) {
                const item = tempData[i];

                if (item.stickOnLevelTab !== world_pb.GameLevelEnum.GameLevelEnumNone) {
                    continue;
                  }

                if (item.game_id !== cv.Enum.GameId.Bet) {
                    continue;
                }

                if (this.splash_GameSizeType(item) === cv.Enum.GameSizeType.Micro)
                    this.weiData.push(item);
                else if (this.splash_GameSizeType(item) === cv.Enum.GameSizeType.Small)
                    this.xiaoData.push(item);
                if (this.splash_GameSizeType(item) === cv.Enum.GameSizeType.Medium)
                    this.zhongData.push(item);
                if (this.splash_GameSizeType(item) === cv.Enum.GameSizeType.High)
                    this.daData.push(item);
            }
        }

        // 菠萝蜜 <= 2 ante是小局  5 -20ante之间是中局 超过20ante是大局
        if (this._gameType === DiscoverGameType.JACKFRUIT) {
            for (let i = 0; i < itemLen; i++) {
                const item = tempData[i];

                if(item.stickOnLevelTab !== world_pb.GameLevelEnum.GameLevelEnumNone){
                    continue;
                }

                if (item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
                    const big_blind = item.big_blind;

                    if (item.is_mirco === 1) {
                        this.weiData.push(item);
                    } else if (big_blind <= 2 * 100) {
                        this.xiaoData.push(item);
                    } else if (big_blind >= 5 * 100 && big_blind <= 20 * 100) {
                        this.zhongData.push(item);
                    } else {
                        this.daData.push(item);
                    }
                } else {
                    const ante = item.ante;

                    if (item.is_mirco === 1) {
                        this.weiData.push(item);
                    } else if (ante <= 2 * 100) {
                        this.xiaoData.push(item);
                    } else if (ante >= 5 * 100 && ante <= 20 * 100) {
                        this.zhongData.push(item);
                    } else {
                        this.daData.push(item);
                    }
                }
            }
        } else if (this._gameType === DiscoverGameType.PLO) {
            for (let i = 0; i < itemLen; i++) {
                const item = tempData[i];

                if(item.stickOnLevelTab !== world_pb.GameLevelEnum.GameLevelEnumNone){
                    continue;
                }

                this.weiData.push(item);
            }
        } else if (this._gameType !== DiscoverGameType.BET) {
            for (let i = 0; i < itemLen; i++) {
                const item = tempData[i];

                if(item.stickOnLevelTab !== world_pb.GameLevelEnum.GameLevelEnumNone){
                    continue;
                }
                if (this._gameType !== DiscoverGameType.DZPK && item.game_id === cv.Enum.GameId.Bet) {
                    continue;
                }

                if (this._gameType === DiscoverGameType.DZPK && item.game_id === cv.Enum.GameId.Bet) {

                    if (this.splash_GameSizeType(item) === cv.Enum.GameSizeType.Micro)
                        this.weiData.push(item);
                    else if (this.splash_GameSizeType(item) === cv.Enum.GameSizeType.Small)
                        this.xiaoData.push(item);
                    if (this.splash_GameSizeType(item) === cv.Enum.GameSizeType.Medium)
                        this.zhongData.push(item);
                    if (this.splash_GameSizeType(item) === cv.Enum.GameSizeType.High)
                        this.daData.push(item);
                }

                else if (item.currencyType === (CurrencyType.USDT as unknown as ws_protocol.pb.CurrencyType)) {
                    const b_blind =
                        item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? item.ante : item.big_blind;
                    if (b_blind <= 20) {
                        this.weiData.push(item);
                    } else if (b_blind <= 200) {
                        this.xiaoData.push(item);
                    } else if (b_blind <= 1000) {
                        this.zhongData.push(item);
                    } else {
                        this.daData.push(item);
                    }
                } else if (item.is_mirco === 1) {
                    this.weiData.push(item);
                }

                // 德州长牌 <= 6大盲是小局 10-200之间是中局（如果不是急速并开启staraddle的200大盲局是大局） 大于200大盲是大局
                else if (item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
                    const big_blind = item.big_blind;
                    if (big_blind <= 6 * 100) {
                        this.xiaoData.push(item);
                    } else if (big_blind >= 10 * 100 && big_blind <= 200 * 100) {
                        if (big_blind === 200 * 100 && item.straddle && !cv.roomManager.checkGameIsZoom(item.game_id)) {
                            this.daData.push(item);
                        } else {
                            this.zhongData.push(item);
                        }
                    } else {
                        this.daData.push(item);
                    }
                }
                // 短牌 <=3ante 是小局 5- 100之间是中局 > 100是大局
                else {
                    const ante = item.ante;
                    if (ante <= 3 * 100) {
                        this.xiaoData.push(item);
                    } else if (ante >= 5 * 100 && ante <= 100 * 100) {
                        this.zhongData.push(item);
                    } else {
                        this.daData.push(item);
                    }
                }
            }
        }

        // const sortWxzd = (tempData: any, pageview: number) => {
        //     const itemLen = tempData.length;

        //     if (itemLen > 1) {
        //         this.sortListByTime(tempData);
        //     }
        // };

        // sortWxzd(this.weiData, 0);

        // sortWxzd(this.xiaoData, 1);

        // sortWxzd(this.zhongData, 2);

        // sortWxzd(this.daData, 3);

        if (
            this._gameType !== 3 &&
            this._gameType !== 4 &&
            this._gameType !== DiscoverGameType.JACKFRUIT &&
            this._gameType !== DiscoverGameType.PLO
        ) {
            const jspkLen = cv.StringTools.getArrayLength(this._jspkArr);

            for (let i = jspkLen - 1; i >= 0; i--) {
                const item = this._jspkArr[i];

                if (item.stickOnLevelTab !== world_pb.GameSizeType.GameSizeTypeNone) {
                    continue;
                }

                if (
                    this._gameType !== DiscoverGameType.DZPK_SHORT &&
                    cv.roomManager.checkGameIsZoomShortDeck(item.game_id, item.game_mode)
                ) {
                    // Exclude Short Deck- Zoom games from ALL except Short-Deck tab
                    continue;
                }

                if (this._gameType === 2 && item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
                    continue;
                }

                if (item.is_mirco === 1) {
                    this.weiData.unshift(item);
                } else if (item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
                    const big_blind = item.straddle ? 2 * item.big_blind : item.big_blind;

                    if (big_blind < 10 * 100) {
                        this.xiaoData.unshift(item);
                    } else if (big_blind >= 10 * 100 && big_blind <= 200 * 100) {
                        if (big_blind === 200 * 100 && item.straddle && !cv.roomManager.checkGameIsZoom(item.game_id)) {
                            this.daData.unshift(item);
                        } else {
                            this.zhongData.unshift(item);
                        }
                    } else {
                        this.daData.unshift(item);
                    }
                } else {
                    //  微: < 1， 小: < 5，中: <=100，大 : >100
                    const bettingSize = item.ante;
                    if (bettingSize < 100) {
                        this.weiData.unshift(item);
                    } else if (bettingSize < 500) {
                        this.xiaoData.unshift(item);
                    } else if (bettingSize <= 10000) {
                        this.zhongData.unshift(item);
                    } else {
                        this.daData.unshift(item);
                    }
                }
            }
        }

        for (let i = 0; i < itemLen; i++) {
            const item = tempData[i];
            console.log('stickOnLevelTab' + item.stickOnLevelTab);
              if(item.stickOnLevelTab ===  world_pb.GameLevelEnum.GameLevelEnumMicro){
                if (this.weiData.indexOf(item) === -1) {
                    this.weiData.push(item);
                }
            } else if (item.stickOnLevelTab ===  world_pb.GameLevelEnum.GameLevelEnumSmall) {
                if (this.xiaoData.indexOf(item) === -1) {
                    this.xiaoData.push(item);
                }
            } else if (item.stickOnLevelTab === world_pb.GameLevelEnum.GameLevelEnumMedium) {
                if (this.zhongData.indexOf(item) === -1) {
                    this.zhongData.push(item);
                }
            } else if (item.stickOnLevelTab === world_pb.GameLevelEnum.GameLevelEnumHigh) {
                if (this.daData.indexOf(item) === -1) {
                    this.daData.push(item);
                }
            }
        }

        this.weiData.sort(this.sortByBlindOrName.bind(this)); // Sorting AT-366
        this.xiaoData.sort(this.sortByBlindOrName.bind(this));
        this.zhongData.sort(this.sortByBlindOrName.bind(this));
        this.daData.sort(this.sortByBlindOrName.bind(this));

        this.weiData.sort(this.sortByVacantSeats.bind(this)); // Sorting AT-366
        this.xiaoData.sort(this.sortByVacantSeats.bind(this));
        this.zhongData.sort(this.sortByVacantSeats.bind(this));
        this.daData.sort(this.sortByVacantSeats.bind(this));

        if (this._gameType === DiscoverGameType.DZPK) {
            this.weiData.sort(this.sortByShortMode.bind(this)); // Sorting AT-366
            this.xiaoData.sort(this.sortByShortMode.bind(this));
            this.zhongData.sort(this.sortByShortMode.bind(this));
            this.daData.sort(this.sortByShortMode.bind(this));
        }

        this.weiData.sort(this.sortByTableStarted.bind(this)); // Sorting AT-366
        this.xiaoData.sort(this.sortByTableStarted.bind(this));
        this.zhongData.sort(this.sortByTableStarted.bind(this));
        this.daData.sort(this.sortByTableStarted.bind(this));

        this.weiData.sort(this.sortByFullTable.bind(this)); // Sorting AT-366
        this.xiaoData.sort(this.sortByFullTable.bind(this));
        this.zhongData.sort(this.sortByFullTable.bind(this));
        this.daData.sort(this.sortByFullTable.bind(this));

        if (this._gameType === DiscoverGameType.DZPK) {
            // Sorting AT-1265
            this.weiData.sort(this.sortByOnlyOneSpotLeft.bind(this));
            this.xiaoData.sort(this.sortByOnlyOneSpotLeft.bind(this));
            this.zhongData.sort(this.sortByOnlyOneSpotLeft.bind(this));
            this.daData.sort(this.sortByOnlyOneSpotLeft.bind(this));
        }

        this.weiData.sort(this.sortByUSDNLHE.bind(this));
        this.xiaoData.sort(this.sortByUSDNLHE.bind(this));
        this.zhongData.sort(this.sortByUSDNLHE.bind(this));
        this.daData.sort(this.sortByUSDNLHE.bind(this));

        if (this._checkLooseModeOnTop()) {
            this.weiData.sort(this.sortByLooseMode.bind(this));
            this.xiaoData.sort(this.sortByLooseMode.bind(this));
            this.zhongData.sort(this.sortByLooseMode.bind(this));
            this.daData.sort(this.sortByLooseMode.bind(this));
        }

        if (this._gameType === DiscoverGameType.DZPK && cv.dataHandler?.getUserData()?.featureFlags?.squid_stick_top) {
            this.weiData.sort(this.sortSquidGameToTop.bind(this));
            this.xiaoData.sort(this.sortSquidGameToTop.bind(this));
            this.zhongData.sort(this.sortSquidGameToTop.bind(this));
            this.daData.sort(this.sortSquidGameToTop.bind(this));
        }

        this.weiData.sort(this.sortByZOOM.bind(this));
        this.xiaoData.sort(this.sortByZOOM.bind(this));
        this.zhongData.sort(this.sortByZOOM.bind(this));
        this.daData.sort(this.sortByZOOM.bind(this));

        this.weiData.sort(this.sortByUSDZOOM.bind(this));
        this.xiaoData.sort(this.sortByUSDZOOM.bind(this));
        this.zhongData.sort(this.sortByUSDZOOM.bind(this));
        this.daData.sort(this.sortByUSDZOOM.bind(this));

        // add stick on top logic sorting
        this.weiData.sort(this.sortByStickOnTop.bind(this));
        this.xiaoData.sort(this.sortByStickOnTop.bind(this));
        this.zhongData.sort(this.sortByStickOnTop.bind(this));
        this.daData.sort(this.sortByStickOnTop.bind(this));

        this.weiData.sort(this.sortByStar.bind(this));
        this.xiaoData.sort(this.sortByStar.bind(this));
        this.zhongData.sort(this.sortByStar.bind(this));
        this.daData.sort(this.sortByStar.bind(this));

        this.showDaAction(this.daData);

        this.setStarTableStatus();
        this.setSquidTableStatus();

        this.isGetData = true;

        if (this.isStart) {
            this.showViewForData();
        }
    }

    sortByStickOnTop(a: any, b: any): number {
        return a.stick_on_top < b.stick_on_top ? 1 : a.stick_on_top > b.stick_on_top ? -1 : 0;
    }

    sortByBlindOrName(a: any, b: any): number {
        if (a.game_id === cv.Enum.GameId.Allin && b.game_id === cv.Enum.GameId.Allin) {
            return a.big_blind < b.big_blind ? 1 : -1;
        }

        // 牌局从大到小
        let num1;

        let num2;

        if (a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            num1 = a.small_blind;
        } else {
            num1 = a.ante;
        }

        if (b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            num2 = b.small_blind;
        } else {
            num2 = b.ante;
        }
        if (num1 === num2) {
            const nameA = a.room_name.match(/\d+|\D+/g)[1];
            const nameB = b.room_name.match(/\d+|\D+/g)[1];
            return nameA > nameB ? 1 : -1;
        }
        return num1 > num2 ? 1 : -1;
    }

    sortByTableStarted(a: any, b: any): number {
        if (a.player_count > 0 && b.player_count > 0) {
            const vacancyA = a.player_count_max - a.player_count;
            const vacancyB = b.player_count_max - b.player_count;
            if (vacancyA === vacancyB) {
                return b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? 1 : 0;
            }
            return vacancyA > vacancyB ? 1 : -1;
        }
        return a.player_count > b.player_count ? -1 : 1;
    }

    sortByOnlyOneSpotLeft(a: any, b: any): number {
        const vacancyA = a.player_count_max - a.player_count;
        const vacancyB = b.player_count_max - b.player_count;
        if (vacancyA === vacancyB) return vacancyA === 1 ? cv.tools.getRandomIntInclusive(-1, 1) : 0; // If both have same 1 vacancy it will give random order, else 0 to stay at is
        if (vacancyA === 1) return -1;
        return 0;
    }

    sortByFullTable(a: any, b: any): number {
        if (a.player_count === a.player_count_max && b.player_count === b.player_count_max) {
            return b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? 1 : -1;
        }
        return b.player_count === b.player_count_max ? -1 : 1;
    }

    sortByVacantSeats(a: any, b: any): number {
        const vacancyA = a.player_count_max - a.player_count;
        const vacancyB = b.player_count_max - b.player_count;
        if (vacancyA === vacancyB) return 0;
        return vacancyA > vacancyB ? 1 : -1;
    }

    sortByPlayerCount(a: any, b: any): number {
        const vacancyA = a.player_count_max - a.player_count;
        const vacancyB = b.player_count_max - b.player_count;

        let bothFull: boolean = false;
        if (a.player_count_max === a.player_count && b.player_count_max === b.player_count) {
            bothFull = true;
        }

        if (a.player_count_max === a.player_count && !bothFull) {
            return 1;
        }

        if (b.player_count_max === b.player_count && !bothFull) {
            return -1;
        }

        if (vacancyA === vacancyB || bothFull) {
            return this.sortByBlindOrName(a, b);
        }

        if (a.player_count > 0 || b.player_count > 0) {
            if (a.player_count === b.player_count) {
                return this.sortByBlindOrName(a, b);
            }

            return a.player_count < b.player_count ? 1 : -1;
        }

        return vacancyA > vacancyB ? 1 : -1;
    }

    sortByShortMode(a: any, b: any): number {
        if (
            a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short &&
            b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short
        ) {
            return 1;
        }
        if (a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            return -1;
        }

        if (b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            return 1;
        }

        return 1;
    }

    sortByZOOM(a: any, b: any): number {
        if (cv.roomManager.checkGameIsZoom(a.game_id) && cv.roomManager.checkGameIsZoom(b.game_id)) {
            return 1;
        }

        if (cv.roomManager.checkGameIsZoom(a.game_id)) {
            return -1;
        }

        if (cv.roomManager.checkGameIsZoom(b.game_id)) {
            return 1;
        }

        return 1;
    }

    sortByUSDZOOM(a: any, b: any): number {
        if (
            cv.roomManager.checkGameIsZoom(a.game_id) &&
            a.currencyType === CurrencyType.USDT &&
            cv.roomManager.checkGameIsZoom(b.game_id) &&
            b.currencyType === CurrencyType.USDT
        ) {
            return 1;
        }

        if (cv.roomManager.checkGameIsZoom(a.game_id) && a.currencyType === CurrencyType.USDT) {
            return -1;
        }

        if (cv.roomManager.checkGameIsZoom(b.game_id) && b.currencyType === CurrencyType.USDT) {
            return 1;
        }

        return 1;
    }

    sortByUSDNLHE(a: any, b: any): number {
        if (a.currencyType === CurrencyType.USDT && b.currencyType === CurrencyType.USDT) {
            return 1;
        }

        if (a.currencyType === CurrencyType.USDT) {
            return -1;
        }

        if (b.currencyType === CurrencyType.USDT) {
            return 1;
        }

        return 1;
    }

    showDaAction(tempData) {
        let isShow = false;

        if (this.getHaveTexasStarTable(tempData) !== 2) {
            for (let i = 0; i < this.daData.length; i++) {
                if (this.daData[i].player_count > 0) {
                    isShow = true;

                    break;
                }
            }
        }

        const action_node = cc.find('discover_panel/LayoutParent/pokerPage_panel/button3/action_node', this.node);

        if (isShow !== action_node.active) {
            const action = action_node.getComponent(cc.Animation);

            action.stop();

            action_node.active = isShow;

            if (isShow) {
                action.play('da');
            }
        }

        this.updateDaAction();
    }

    showViewForData() {
        if (!this.isHelpView) {
            this.showScrollView();
        } else {
            this.updateHelpView(this.sHelpName);
        }
    }

    sortWeiArr() {
        this.weiData.sort((a: any, b: any): number => {
            if (a.player_count === b.player_count) {
                return b.player_count_max - a.player_count_max;
            }

            if (a.player_count === 0) {
                return -1;
            }

            if (b.player_count === 0) {
                return 1;
            }

            const result: number = b.player_count_max - b.player_count - (a.player_count_max - a.player_count);

            if (result === 0) {
                return b.player_count_max - a.player_count_max;
            }

            return result;
        });
    }

    onBtnCardListTypeClick(event: cc.Event) {
        const index = event.target.name.charAt(6);

        if (index !== 0 && this._gameType === DiscoverGameType.PLO) {
            return;
        }

        if (this.viewIndex === index && !this.isHelpView) {
            return;
        }

        cv.AudioMgr.playButtonSound('tab');

        this.setViewIndex(index);

        this.scrollView.scrollToTop(0.01);

        this.setStarTableStatus();
        this.setSquidTableStatus();
        // 跟踪用户行为, 发送事件
        const stakeSize: string = event.target.getChildByName('text').getComponent(cc.Label).string;

        const properties = { stakeSize };

        cv.segmentTool.track(
            cv.Enum.CurrentScreen.lobby,
            cv.Enum.segmentEvent.LobbyStakeSelected,
            cv.Enum.Functionality.poker,
            properties
        );
    }

    setViewIndex(index: number) {
        this.changeView(this.viewIndex, false);

        this.changeView(index, true);

        this.viewIndex = cv.Number(index);

        cv.tools.SaveStringByCCFile(this.SAVE_wxzd, this.viewIndex.toString());

        if (this.isHelpView) {
            this.isHelpView = false;

            this.changeView(4, false);
        }

        this.showScrollView();

        this.updateDaAction();
    }

    updateDaAction() {
        const action_node = cc.find('discover_panel/LayoutParent/pokerPage_panel/button3/action_node', this.node);

        if (action_node.active) {
            cc.find('da_01', action_node).active = this.viewIndex === 3;
        }
    }

    onBtnNoSeeFullClick(event: cc.Event) {
        this.noSeeFull = !this.noSeeFull;

        this.changeNoSeeFull(this.noSeeFull);

        this.showScrollView();

        cv.tools.SaveStringByCCFile(this.SAVE_seeFull, this.noSeeFull ? '1' : '0');
    }

    onBtnFastEnterClick(event: cc.Event) {
        cv.AudioMgr.playButtonSound('button_click');

        this.filterPanelComponent.masterTableList = this.GetAllEntries();
        this.filterPanelComponent.refreshFilterWindowContent();

        this.quickSeatPanelRoot.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_5;

        this.quickSeatPanelRoot.stopAllActions();

        this.quickSeatPanelRoot.setPosition(this.quickPanelPos);

        const endPos = this.quickSeatPanelRoot.getPosition();

        // 添加屏蔽层
        cv.action.createShieldLayer(
            this.node,
            'shieldLayer-quickEnter',
            cv.Enum.ZORDER_TYPE.ZORDER_1,
            cc.Color.BLACK,
            153
        );

        this.quickSeatPanelRoot.runAction(cc.moveTo(0.15, endPos.x, cc.winSize.height * 0.5));

        // 跟踪用户行为, 发送事件
        const properties = { item: 'showFiltersButton' };

        cv.segmentTool.track(
            cv.Enum.CurrentScreen.lobby,
            cv.Enum.segmentEvent.Clicked,
            cv.Enum.Functionality.poker,
            properties
        );
    }

    joinFastSeat(captchaPassed: boolean) {
        // did not pass captcha test
        if (captchaPassed === false) {
            return;
        }

        const isSimulator: boolean = cv.native.IsSimulator();

        let arr: any[] = [];
        this.filterPanelComponent.masterTableList = this.GetAllEntries();
        arr = this.filterPanelComponent.FiltereTableListForQuickEntry(isSimulator);

        if (arr.length <= 0) {
            cv.TT.showMsg(cv.config.getStringData('UIMainTips01'), cv.Enum.ToastType.ToastTypeError);

            return;
        }

        arr.sort(this.sortByTimeAndPeople.bind(this));

        cv.GameDataManager.tRoomData.entry_clubid = arr[0].club_id;
        cv.GameDataManager.tRoomData.u32GameID = arr[0].game_id;
        cv.GameDataManager.tappedCurrency = arr[0].currencyType;

        cv.roomManager.RequestJoinRoom(arr[0].game_id, arr[0].room_id, true);

        // 是否有GPS限制
        const anti_cheating = arr[0].anti_cheating;

        if (arr[0].has_buyin === 0) {
            if ((anti_cheating || !arr[0].anti_simulator) && !cv.native.HaveGps(false)) {
                // 如果开启GPS限制，且没有打开定位。
                // 不设置FINDVIEW_isFASTENTER为1，在进入房间后，不自动弹出自动带入提示
            } else {
                cv.tools.SaveStringByCCFile('FINDVIEW_isFASTENTER', '1');
            }
        } else if (arr[0].has_buyin === 1) {
            cv.tools.SaveStringByCCFile('FINDVIEW_isFASTENTER', '2');
        }
    }

    onFilterTablesButtonClickedListener(name: string = '') {
        this.updateHelpView(name);
        this.hideQuickEnterView();
    }

    updateHelpView(name: string = '') {
        this.sHelpName = name;

        if (name !== '') {
            this.setHelpViewByName(name);

            return;
        }

        this.helpData = [];

        this.filterPanelComponent.masterTableList = this.GetAllEntries();
        this.helpData = this.filterPanelComponent.filterMasterTableList();

        this.sortListByTime(this.helpData);

        this.helpData.sort(this.sortByUSDNLHE.bind(this));

        this.helpData.sort(this.sortByZOOM.bind(this));

        this.helpData.sort(this.sortByUSDZOOM.bind(this));

        this.helpData.sort(this.sortByStar.bind(this));

        this.changeView(this.viewIndex, false);

        this.changeView(4, true);

        this.isHelpView = true;

        this.showScrollView();
    }

    setHelpViewByName(name: string) {
        this.sHelpName = name;

        this.helpData = [];

        this.filterPanelComponent.masterTableList = this.screenByGame();
        this.helpData = this.filterPanelComponent.filterMasterTableList(name);

        if (this.helpData.length === 0) {
            cv.TT.showMsg(cv.config.getStringData('UIMainTips01'), cv.Enum.ToastType.ToastTypeError);

            return;
        }

        this.sortListByTime(this.helpData);

        this.changeView(this.viewIndex, false);

        this.changeView(4, true);

        this.isHelpView = true;

        this.showScrollView();

        this.hideQuickEnterView();
    }

    compareItem(a: any, b: any): number {
        let manzhuA = a.small_blind;

        let manzhuB = b.small_blind;

        if ((a.start_time === 0 && b.start_time !== 0) || (a.start_time !== 0 && b.start_time === 0)) {
            return b.start_time - a.start_time;
        }

        if (a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            manzhuA = a.ante;
        }

        if (b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            manzhuB = b.ante;
        }

        if (manzhuA === manzhuB) {
            if (a.start_time === b.start_time && a.start_time === 0) {
                return a.create_time - b.create_time;
            }

            return a.start_time - b.start_time;
        }

        return manzhuB - manzhuA;
    }

    compareManZhu(a: any, b: any): number {
        let manzhuA = a.small_blind;

        let manzhuB = b.small_blind;

        if (a.game_mode === b.game_mode && a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            if (manzhuA === manzhuB) {
                if (a.straddle !== b.straddle) {
                    const aS = a.straddle ? 1 : 0;

                    const bS = b.straddle ? 1 : 0;

                    return bS - aS;
                }

                if (a.ante === b.ante) {
                    if (a.start_time === b.start_time && a.start_time === 0) {
                        return b.create_time - a.create_time;
                    }

                    return b.start_time - a.start_time;
                }

                return b.ante - a.ante;
            }

            return manzhuB - manzhuA;
        }

        if (a.game_mode === b.game_mode && a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            if (a.ante === b.ante) {
                if (a.start_time === b.start_time && a.start_time === 0) {
                    return b.create_time - a.create_time;
                }

                return b.start_time - a.start_time;
            }

            return b.ante - a.ante;
        }

        if (b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            manzhuB = b.ante;
        }

        if (a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            manzhuA = a.ante;
        }

        if (manzhuA === manzhuB) {
            return b.game_mode - a.game_mode;
        }

        return manzhuB - manzhuA;
    }

    changeView(index: number, isChoose: boolean) {
        const data = [
            { url: 'zh_CN/hall/ui/common_paiju2', color: cc.color(197, 197, 215) },
            { url: 'zh_CN/hall/ui/common_paiju1', color: cc.color(229, 211, 141) }
        ];

        const choose = isChoose ? 1 : 0;

        cc.find('discover_panel/LayoutParent/pokerPage_panel/button' + index + '/selectimg', this.node).active =
            isChoose;

        if (index !== 4) {
            cc.find('discover_panel/LayoutParent/pokerPage_panel/button' + index + '/text', this.node).color =
                data[choose].color;
        }
    }

    changeNoSeeFull(isNoSee: boolean) {
        cc.find('discover_panel/LayoutParent/pokerPage_panel/checkBoxButton/choose_img', this.node).active = isNoSee;
    }

    // 设置筛选的状态
    setFillterStatus() {
        this.fillter_line.active = true;

        if (this.isHelpView) {
            this.fillter_txt.node.color = cc.color(229, 211, 141);

            cv.StringTools.setLabelString(
                this.node,
                'discover_panel/LayoutParent/pokerPage_panel/button4/label',
                'MainScene_Scene_pokerPage_panel_button_scsx_4_text'
            );

            this.fillter_line.setContentSize(
                cc.size(this.fillter_txt.node.getContentSize().width, this.fillter_line.getContentSize().height)
            );

            this.fillter_line.color = cc.color(229, 211, 141);
        } else {
            cv.StringTools.setLabelString(
                this.node,
                'discover_panel/LayoutParent/pokerPage_panel/button4/label',
                'MainScene_Scene_pokerPage_panel_button_sx_4_text'
            );

            this.fillter_txt.node.color = cc.color(197, 197, 215);

            this.fillter_line.setContentSize(
                cc.size(this.fillter_txt.node.getContentSize().width, this.fillter_line.getContentSize().height)
            );

            this.fillter_line.color = cc.color(197, 197, 215);
        }
    }

    showScrollView() {
        const arr = [this.weiData, this.xiaoData, this.zhongData, this.daData];

        const data = this.isHelpView ? this.helpData : arr[this.viewIndex];

        this.setFillterStatus();

        if (!this.noSeeFull || this._gameType === DiscoverGameType.PLO) {
            this.updateListviewData(data);
        } else {
            const otherData: Array<any> = [];

            const dataLen = data.length;

            for (let i = 0; i < dataLen; i++) {
                if (
                    data[i].player_count < data[i].player_count_max ||
                    cv.roomManager.checkGameIsZoom(data[i].game_id) ||
                    data[i].game_id === cv.Enum.GameId.StarSeat
                ) {
                    otherData.push(data[i]);
                }
            }

            this.updateListviewData(otherData);
        }
    }

    onClickCloseBtn() {
        this.hideQuickEnterView();

        cv.AudioMgr.playButtonSound('close');

        // 跟踪用户行为, 发送事件
        const properties = { item: 'closeFiltersButton' };

        cv.segmentTool.track(
            cv.Enum.CurrentScreen.lobby,
            cv.Enum.segmentEvent.Clicked,
            cv.Enum.Functionality.poker,
            properties
        );
    }

    hideQuickEnterView() {
        if (!this.quickSeatPanelRoot || !this.quickSeatPanelRoot.active) {
            return;
        }

        this.quickSeatPanelRoot.stopAllActions();

        this.quickSeatPanelRoot.runAction(
            cc.sequence(
                cc.moveTo(0.15, this.quickPanelPos.x, this.quickPanelPos.y),
                cc.callFunc(
                    function () {
                        cv.action.removeShieldLayer(this.node, 'shieldLayer-quickEnter');

                        this.quickSeatPanelRoot.active = false;
                    }.bind(this, this.quickSeatPanelRoot)
                )
            )
        );

        this.showMailBtn(true);
    }

    showMailBtn(isView: boolean) {
        // 设置邮件图标显隐
        if (isView) {
            cv.MessageCenter.send('show_mail_entrance');
        } else {
            cv.MessageCenter.send('hide_mail_entrance');
        }

        const rightSideBtns = this.node.parent.getChildByName('rightSideBtns');

        if (rightSideBtns) {
            const switchServer = rightSideBtns.getChildByName('switchServer');

            const kefu = rightSideBtns.getChildByName('kefu');

            if (switchServer) {
                switchServer.active = isView;
            }

            if (kefu) {
                kefu.active = isView;
            }
        }

        const imgLogo = this.node.parent.getChildByName('img_logo');

        if (imgLogo) {
            imgLogo.active = isView;
        }

        const goldView = this.node.parent.getChildByName('goldView_pref');

        if (goldView) {
            goldView.active = isView;
        }
    }

    initBanner(): void {
        this.img_PageView.removeAllPages();

        this.img_PageView.scrollToLeft();

        const whiteCircleLen = this._whiteCircleList.length;

        for (let i = 0; i < whiteCircleLen; i++) {
            this._whiteCircleList[i].removeFromParent(true);

            this._whiteCircleList[i].destroy();
        }

        this._whiteCircleList = [];

        const imgUrlList: BannerInfo[] = cv.dataHandler.getBannerUrlList(this._gameType + 1);

        const len = cv.StringTools.getArrayLength(imgUrlList);

        if (len === 0) {
            // 兼容未配数据的情况
            const default_img = new cc.Node().addComponent(cc.Sprite);

            cv.resMgr.setSpriteFrame(default_img.node, cv.tools.getBackgroundBannerImgPath());

            default_img.node.setPosition(0.5 * this.img_PageView.node.width, 0);

            this.img_PageView.addPage(default_img.node);

            this.setPageCircleimg(0);

            this.unschedule(this.PageViewAction);

            this.img_PageView.scrollToPage(0, 0.1);

            return;
        }

        const hpg = 25;

        const mid = (len + 1) / 2.0;

        for (let i = 0; i < len; i++) {
            if (len > 1) {
                /* const whiteCircle = cv.resMgr.createSprite(this.discover_panel, 'zh_CN/hall/ui/ui_0059_round_yes');

                this._whiteCircleList.push(whiteCircle);

                whiteCircle.setPosition(cc.v2(this.discover_panel.getContentSize().width / 2 + (i + 1 - mid) * hpg,
                    this.img_PageView.node.y - 128)); */

                const whiteCircle = cv.resMgr.createSprite(this.img_PageView.node, 'zh_CN/hall/ui/ui_0059_round_yes');

                this._whiteCircleList.push(whiteCircle);

                whiteCircle.setPosition(
                    cc.v2((i + 1 - mid) * hpg, (-1 * this.img_PageView.node.getContentSize().height) / 2 + 15)
                );
            }

            const id: number = imgUrlList[i].id;
            
            const path: string = imgUrlList[i].imageUrl;

            let webUrl: string = imgUrlList[i].webViewUrl;

            const aid: SportAidEnum = imgUrlList[i].aid;

            const matchID: string = imgUrlList[i].matchID;

            const pkBanner = new cc.Node().addComponent(cc.Sprite);

            this.setSpriteByDownload(pkBanner, path);

            pkBanner.node.setPosition((i + 0.5) * this.img_PageView.node.width, 0);

            this.img_PageView.addPage(pkBanner.node);

            this.img_PageView.content.setContentSize(
                this.img_PageView.content.width * (i + 1),
                this.img_PageView.content.height
            );

            if(this._jumpToSport(aid, matchID)) {
                pkBanner.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event) => {
                    cv.dataHandler.getActivityData().setAid = aid;
                    cv.dataHandler.getActivityData().setMatchID = matchID;
                    cv.MessageCenter.send("jumpToMiniGamesHall", {jumpType: cv.Enum.JUMPGAMETYPE.JUMP_TO_SPORT});
                });
            } else if (webUrl.length > 0) {
                if (imgUrlList[i].is_pkf === 1) {
                    webUrl = encodeURI(webUrl + '&' + cv.dataHandler.getUserData().pkf_add_url);
                }

                pkBanner.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event) => {
                    console.log('!!!!!!' + webUrl);

                    if (webUrl.search('ggjs:') !== -1) {
                        // 跳转到公告页
                        // "User/Article/getDetail?pid=40"
                        let urls = webUrl.substr(5, webUrl.length);

                        urls =
                            cv.domainMgr.getServerInfo().web_server +
                            urls +
                            '#clientType=' +
                            String(cv.config.GET_CLIENT_TYPE()) +
                            '&language=' +
                            cv.config.getCurrentLanguage() +
                            '&uid=' +
                            cv.dataHandler.getUserData().u32Uid;

                        cv.MessageCenter.send('jumpgto_notice', urls);

                        return;
                    }

                    if (cc.sys.isBrowser) {
                        if (!cv.config.isSiyuType()) {
                            window.open(webUrl);
                        } else {
                            const cmdStr = '{"cmd": "1006", "url":"%s", "op":1, "width":%d, "height":%d}';

                            let _offsetY = 0;

                            if (cc.sys.os === cc.sys.OS_IOS && cv.config.IS_IPHONEX_SCREEN) {
                                // iphoneX刘海屏
                                _offsetY = 0; // 2 * cv.config.FULLSCREEN_OFFSETY;
                            }

                            const _cmd = cv.StringTools.formatC(
                                cmdStr,
                                webUrl,
                                cv.config.WIDTH,
                                cv.config.HEIGHT - _offsetY
                            );

                            cv.native.SYwebjsToClient(_cmd);
                        }

                        return;
                    }

                    const web = new cc.Node('banner_webview').addComponent(cc.WebView);

                    web.node.setContentSize(cc.size(cv.config.WIDTH, cv.config.HEIGHT));

                    web.node.setPosition(cv.config.WIDTH * 0.5, cv.config.HEIGHT * 0.5);

                    cc.director.getScene().addChild(web.node);

                    web.url = webUrl;

                    web.setJavascriptInterfaceScheme('ccjs');

                    web.setOnJSCallback((webView: cc.WebView, url: string) => {
                        if (url.search('ccjs://https') !== -1 || url.search('ccjs://http') !== -1) {
                            const banner_webview = cc.director.getScene().getChildByName('banner_webview');

                            if (banner_webview) {
                                banner_webview.removeFromParent(true);

                                banner_webview.destroy();
                            }
                        }
                    });
                });
            }
            
            pkBanner.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event) => {
                cv.httpHandler.requestClickCounter(ClickCounterCategory.BANNER, id);
            });
        }

        this.setPageCircleimg(0);

        this.unschedule(this.PageViewAction);

        this.schedule(this.PageViewAction, 3.0);

        this.img_PageView.scrollToPage(0, 0.1);
    }

    private _jumpToSport(aid: SportAidEnum, matchID : string):boolean {
        if(matchID) return true;
        if(aid == null || aid == undefined || aid === SportAidEnum.Main) {
            return false;
        }
        return true;
    }

    adaptBanner(): void {
        this.unschedule(this.PageViewAction);

        for (let i = 0; i < this.img_PageView.content.children.length; i++) {
            const pkBanner = this.img_PageView.content.children[i];
            pkBanner.setPosition((i + 0.5) * this.img_PageView.node.width, 0);
        }
        this.img_PageView.content.setContentSize(
            this.img_PageView.content.width * this.img_PageView.content.children.length,
            this.img_PageView.content.height
        );
        this.schedule(this.PageViewAction, 3.0);
    }

    setSpriteByDownload(spr: cc.Sprite, path: string) {
        if (!path) {
            console.log('path 不能为空');

            return;
        }

        cv.resMgr.loadRemote(path, (error: Error, resource: cc.Texture2D) => {
            if (!spr || !cc.isValid(spr, true)) {
                return;
            }

            if (error) {
                cv.resMgr.setSpriteFrame(spr.node, cv.tools.getBackgroundBannerImgPath());

                return;
            }

            spr.spriteFrame = new cc.SpriteFrame(resource);
        });
    }

    setPageCircleimg(index: number): void {
        if (this._whiteCircleList.length === 0) {
            return;
        }

        const len = this._whiteCircleList.length;

        if (len <= 1) {
            this._whiteCircleList[0].active = false;
        } else {
            for (let i = 0; i < len; i++) {
                this._whiteCircleList[i].active = true;

                if (i === index) {
                    cv.resMgr.setSpriteFrame(this._whiteCircleList[i], 'zh_CN/hall/ui/ui_0059_round_yes');
                } else {
                    cv.resMgr.setSpriteFrame(this._whiteCircleList[i], 'zh_CN/hall/ui/ui_0060_round_no');
                }
            }
        }
    }

    PageViewAction(): void {
        const len = this._whiteCircleList.length;

        if (len > 1) {
            const index = this.img_PageView.getCurrentPageIndex();

            const next = index < len - 1 ? index + 1 : 0;

            this.img_PageView.scrollToPage(next, 1);
        }
    }

    touchImgPageview(event: cc.Event) {
        const len = this._whiteCircleList.length;

        const index = this.img_PageView.getCurrentPageIndex();

        const curindex = index >= len ? len - 1 : index;

        this.setPageCircleimg(curindex);
    }

    setGameType(type: number) {
        this._gameType = type;
        this.filterPanelComponent._gameType = type;

        this.updatePokerPagePanelActive();

        const checkBoxButton = cc.find('discover_panel/LayoutParent/pokerPage_panel/checkBoxButton', this.node);

        if (type === DiscoverGameType.PLO) {
            cc.find('discover_panel/LayoutParent/pokerPage_panel/button1/text', this.node).color = cc.color(
                102,
                102,
                102
            );

            cc.find('discover_panel/LayoutParent/pokerPage_panel/button2/text', this.node).color = cc.color(
                102,
                102,
                102
            );

            cc.find('discover_panel/LayoutParent/pokerPage_panel/button3/text', this.node).color = cc.color(
                102,
                102,
                102
            );

            checkBoxButton.active = false;

            // this.scrollView.node.getComponent(cc.Widget).top = this.scrollViewTop - this.pokerPage_panel.height;
        } else {
            cc.find('discover_panel/LayoutParent/pokerPage_panel/button1/text', this.node).color = cc.Color.WHITE;

            cc.find('discover_panel/LayoutParent/pokerPage_panel/button2/text', this.node).color = cc.Color.WHITE;

            cc.find('discover_panel/LayoutParent/pokerPage_panel/button3/text', this.node).color = cc.Color.WHITE;

            checkBoxButton.active = true;

            // this.scrollView.node.getComponent(cc.Widget).top = this.scrollViewTop;

            this.changeView(this.viewIndex, true);
        }

        cv.resMgr.adaptWidget(this.scrollView.node, true);
    }

    updatePokerPagePanelActive() {
        const isShow =
            this._gameType !== DiscoverGameType.PLO &&
            this._gameType !== this.MTT_NUM &&
            this._gameType !== this.JSNG_NUM &&
            this._gameType !== this.BLACKJACK_NUM;

        this.pokerPage_panel.active = isShow;

        this.setSafeAreaAndScrollViewPart();
    }

    setViewGametype(type: number, isRunAction: boolean): void {
        const isMTT: boolean = (type == this.MTT_NUM || type == this.JSNG_NUM);

        const isBJPVP: boolean = type === this.BLACKJACK_NUM;

        const isScrollViewActive: boolean = !isMTT && !isBJPVP;

        if (this.mtt_img) {
            this.mtt_img.active = isBJPVP;
        }

        this.scrollView.node.active = isScrollViewActive;

        if (this.isHelpView) {
            this.isHelpView = false;

            this.changeView(4, false);

            this.changeView(this.viewIndex, true);
        }

        if (type === DiscoverGameType.PLO && this.viewIndex !== 0) {
            this.setViewIndex(0);
        }

        this.setGameType(type);

        const str: string = cv.String(this._gameType);

        cv.tools.SaveStringByCCFile(this.SAVE_gameType, str);

        this.updateGameBtn();
    }

    initPage(): void {
        const pageV = cv.tools.GetStringByCCFile('last_pageview');

        if (cv.StringTools.getArrayLength(pageV) > 0) {
        }
    }

    initGameType(): void {
        const str: string = cv.tools.GetStringByCCFile(this.SAVE_gameType);

        if (cv.StringTools.getArrayLength(str) > 0) {
            this.setGameType(cv.Number(str));
        } else {
            this.setGameType(DiscoverGameType.DZPK);
        }

        let list;

        let isFind = false;

        if (cv.config.isOverSeas()) {
            list = this.PKC_GAME_TYPE;
        } else {
            list = this.PKW_GAME_TYPE;
        }

        for (let index = 0; index < list.length; index++) {
            if (this._gameType === list[index]) {
                isFind = true;

                break;
            }
        }

        if (!isFind && list.length > 0) {
            this.setGameType(list[0]);

            const str: string = cv.String(this._gameType);

            cv.tools.SaveStringByCCFile(this.SAVE_gameType, str);
        }

        this.updateGameBtn();
    }

    // 刷新二级菜单按钮
    updateGameBtn(): void {
        this.resetButton();
        const len = this._gamebuttonList.length;

        for (let i = 0; i < len; i++) {
            let tag;

            if (cv.config.isOverSeas()) {
                tag = this.PKC_GAME_TYPE[i];
            } else {
                tag = this.PKW_GAME_TYPE[i];
            }

            const text = this._gamebuttonList[i].getChildByName('group').getChildByName('text');

            const newyear_icon_left = this._gamebuttonList[i].getChildByName('newyear_icon_left');

            const newyear_icon_right = this._gamebuttonList[i].getChildByName('newyear_icon_right');

            const selectButton=this._gamebuttonList[i].getChildByName("selectimgBTN");
            selectButton.active=false;

            if (tag !== this._gameType) {
                // new cc.Color().fromHEX("#ADAEC2");
                text.color = cc.color(197, 197, 214);

                newyear_icon_left.active = false;

                newyear_icon_right.active = false;
            } else {
                const isNewYear: boolean = cv.config.isShowNewYear();
                selectButton.active=true;

                // new cc.Color().fromHEX("#FFFFFF");
                text.color = cc.color(39, 39, 50);

                newyear_icon_left.active = isNewYear;

                newyear_icon_right.active = isNewYear;
            }
        }
        
        this.initBanner();
    }

    sortByBlindorAnte(a: any, b: any): number {
        if (a.game_id > b.game_id) {
            return -1;
        }

        if (a.game_id < b.game_id) {
            return 1;
        }

        if (a.game_id === cv.Enum.GameId.Allin && b.game_id === cv.Enum.GameId.Allin) {
            return a.big_blind < b.big_blind ? 1 : -1;
        }

        // 牌局从大到小
        let num1;

        let num2;

        if (a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            num1 = a.small_blind;
        } else {
            num1 = a.ante;
        }

        if (b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            num2 = b.small_blind;
        } else {
            num2 = b.ante;
        }

        // 有straddle的优先
        if (
            a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal &&
            b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal
        ) {
            if (a.big_blind === b.big_blind) {
                if (a.straddle && !b.straddle) {
                    return -1;
                }

                if (!a.straddle && b.straddle) {
                    return 1;
                }
            }
        }

        if (num1 === num2) {
            // 优先短牌局
            if (
                a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal &&
                b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal
            ) {
                // 前注从大到小
                if (a.ante !== b.ante) {
                    return a.ante < b.ante ? 1 : -1;
                }
            } else if (a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
                return 1;
            } else if (b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
                return -1;
            }

            // 再按是否开始 已开始的优先
            if (a.start_time !== 0 && b.start_time !== 0) {
                // 按照开始时间最晚的排上面
                return a.start_time < b.start_time ? 1 : -1;
            }

            if (a.start_time !== 0) {
                return -1;
            }

            if (b.start_time !== 0) {
                return 1;
            }

            // 按照创建时间最晚的排上面
            return a.create_time < b.create_time ? 1 : -1;
        }

        return num1 < num2 ? 1 : -1;
    }

    sortByMixed(a: any, b: any): number {
        // 牌局从大到小
        let num1;

        let num2;

        if (a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            num1 = a.small_blind;
        } else {
            num1 = a.ante;
        }

        if (b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            num2 = b.small_blind;
        } else {
            num2 = b.ante;
        }

        if (
            a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal &&
            b.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal
        ) {
            if (a.big_blind === 200 * 100 && a.big_blind === b.big_blind) {
                if (a.straddle && !b.straddle) {
                    return -1;
                }

                if (!a.straddle && b.straddle) {
                    return 1;
                }
            }
        }

        // 开局时间从早到晚
        if (num1 === num2) {
            return a.start_time < b.start_time ? -1 : 1;
        }

        return num1 > num2 ? -1 : 1;
    }

    sortByAof(a: any, b: any): number {
        if (a.game_id === cv.Enum.GameId.Allin && b.game_id !== cv.Enum.GameId.Allin) {
            return -1;
        }

        if (a.game_id !== cv.Enum.GameId.Allin && b.game_id === cv.Enum.GameId.Allin) {
            return 1;
        }

        return 1;
    }

    // 优先已买入  其次已结算（同为买入或结算的局 优先新创建）优先未开始的局  （未开始的局优先新创建的局， 已开始的局优先最新开始的局）
    sortByCreatime(a: any, b: any): number {
        // 急速游戏不在这里排序
        if (a.game_id > b.game_id) {
            return -1;
        }

        if (a.game_id < b.game_id) {
            return 1;
        }

        if (a.has_buyin === 1 && b.has_buyin !== 1) {
            // 1:买入 2：结算
            return -1;
        }

        if (a.has_buyin !== 1 && b.has_buyin === 1) {
            return 1;
        }

        if (a.has_buyin === 1 && b.has_buyin === 1) {
            return a.create_time > b.create_time ? -1 : 1;
        }

        if (a.has_buyin === 2 && b.has_buyin !== 2) {
            return -1;
        }

        if (a.has_buyin !== 2 && b.has_buyin === 2) {
            return 1;
        }

        if (a.has_buyin === 2 && b.has_buyin === 2) {
            return a.create_time > b.create_time ? -1 : 1;
        }

        if (a.start_time === 0 && b.start_time !== 0) {
            return -1;
        }

        if (a.start_time !== 0 && b.start_time === 0) {
            return 1;
        }

        if (a.start_time === 0 && b.start_time === 0) {
            return a.create_time > b.create_time ? -1 : 1;
        }

        return a.start_time > b.start_time ? -1 : 1;
    }

    // 发现列表排序 凌晨3 -7点和平时按不同规则来排
    sortListByTime(list: any[]) {
        const hours = new Date().getHours();

        if (hours >= 3 && hours < 7) {
            list.sort(this.sortByCreatime);
        } else {
            list.sort(this.sortByLevel.bind(this));
        }
    }

    screenByGame(): world_pb.ClubGameSnapshotV3[] {
        const list: world_pb.ClubGameSnapshotV3[] = [];

        const game_mode: number[] = [];

        const game_type: number[] = [];

        const itemLen = this.itemData.length;

        for (let i = 0; i < itemLen; i++) {
            list.push(this.itemData[i]);
        }

        switch (this._gameType) {
            case DiscoverGameType.ALL: {
                return this.cleanJspkData(list);
            }

            case DiscoverGameType.DZPK:
                game_mode.push(cv.Enum.CreateGameMode.CreateGame_Mode_Normal);

                // optimization list
                game_mode.push(cv.Enum.CreateGameMode.CreateGame_Mode_Short);

                game_type.push(cv.Enum.GameId.Texas);

                game_type.push(cv.Enum.GameId.StarSeat);

                game_type.push(cv.Enum.GameId.Bet);

                game_type.push(cv.Enum.GameId.Squid);

                break;

            case DiscoverGameType.DZPK_SHORT:
                game_mode.push(cv.Enum.CreateGameMode.CreateGame_Mode_Short);

                game_type.push(cv.Enum.GameId.Texas);

                game_type.push(cv.Enum.GameId.StarSeat);

                break;

            case DiscoverGameType.AOF:
                game_type.push(cv.Enum.GameId.Allin);

                break;

            case DiscoverGameType.ZOOM_TEXAS:
                for (let i = cv.Enum.GameId.ZoomTexas; i <= cv.Enum.GameId.ZoomTexasMax; i++) {
                    game_type.push(i);
                }

                break;

            case DiscoverGameType.BET:
                game_mode.push(cv.Enum.CreateGameMode.CreateGame_Mode_Normal);

                game_type.push(cv.Enum.GameId.Bet);

                break;

            case DiscoverGameType.JACKFRUIT:
                game_type.push(cv.Enum.GameId.Jackfruit);

                break;

            case DiscoverGameType.PLO:
                game_type.push(world_pb.GameId.PLO);

                break;
        }

        const result: any[] = [];

        const listLen = list.length;

        const typeLen = game_type.length;

        const modeLen = game_mode.length;

        for (let i = 0; i < listLen; i++) {
            if (typeLen === 0) {
                if (modeLen === 0) {
                    result.push(list[i]);
                } else {
                    for (let s = 0; s < modeLen; s++) {
                        if (list[i].game_mode === game_mode[s]) {
                            result.push(list[i]);
                        }
                    }
                }
            } else {
                for (let j = 0; j < typeLen; j++) {
                    if (list[i].game_id === game_type[j]) {
                        if (modeLen === 0) {
                            result.push(list[i]);
                        } else {
                            for (let s = 0; s < modeLen; s++) {
                                if (list[i].game_mode === game_mode[s]) {
                                    if (this._gameType === DiscoverGameType.DZPK && this.needToExcludeFromNLHE(list[i])) {
                                        continue;
                                    }
                                    result.push(list[i]);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (this._gameType === DiscoverGameType.DZPK) {
            // For NLHE, the shortdeck mode && Splash tables can be visible as per FeatureFlags from backend
            return this.filterByFeatureFlags(result);
        }

        return result;
    }

    needToExcludeFromNLHE(item: any): boolean {

        if (cv.roomManager.checkGameIsZoomShortDeck(item.game_id, item.game_mode)) {
            // Exclude Short Deck - Zoom games for NLHE
            return true;
        }

        if ((item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short || item.game_id === cv.Enum.GameId.Bet) && item.player_count === 0) {
            // Exclude Short Deck - Splash games with 0 online player for NLHE tab
            return true;
        }

        return false;
    }

    filterByFeatureFlags(tableList: any[]): any[] {
        const result: any[] = [];
        const flags: world_pb.FeatureFlags = cv.dataHandler.getUserData().featureFlags;
        for (let i = 0; i < tableList.length; i++) {
            if (flags != null && tableList[i].game_id === cv.Enum.GameId.Bet) {
                const gameSizeType = this.splash_GameSizeType(tableList[i]);

                if (flags.splash_visble_micro && gameSizeType === cv.Enum.GameSizeType.Micro) {
                    result.push(tableList[i]);
                }

                if (flags.splash_visble_small && gameSizeType === cv.Enum.GameSizeType.Small) {
                    result.push(tableList[i]);
                }

                if (flags.splash_visble_medium && gameSizeType === cv.Enum.GameSizeType.Medium) {
                    result.push(tableList[i]);
                }

                if (flags.splash_visble_big && gameSizeType === cv.Enum.GameSizeType.High) {
                    result.push(tableList[i]);
                }
            }
            else if (flags != null && tableList[i].game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
                const gameSizeType = this.shortDeck_NLHE_GameSizeType(tableList[i]);

                if (flags.shortdeck_visible_micro && gameSizeType === cv.Enum.GameSizeType.Micro) {
                    result.push(tableList[i]);
                }
                if (flags.shortdeck_visible_small && gameSizeType === cv.Enum.GameSizeType.Small) {
                    result.push(tableList[i]);
                }
                if (flags.shortdeck_visible_medium && gameSizeType === cv.Enum.GameSizeType.Medium) {
                    result.push(tableList[i]);
                }
                if (flags.shortdeck_visible_big && gameSizeType === cv.Enum.GameSizeType.High) {
                    result.push(tableList[i]);
                }
            } else {
                result.push(tableList[i]);
            }
        }

        return result;
    }

    splash_GameSizeType(item: any): GameSizeType {  // Determine Splash (Bet) game size type base on ante config settings
        let gameSizeType: GameSizeType = GameSizeType.None;
        if (item.is_mirco === 1) {
            gameSizeType = GameSizeType.Micro;
        } else if (item.ante <= 500) {
            gameSizeType = GameSizeType.Small;
        } else if (item.ante >= 1000 && item.ante <= 80000) {
            gameSizeType = GameSizeType.Medium;
        } else if (item.ante > 80000) {
            gameSizeType = GameSizeType.High;
        }
        return gameSizeType;
    }

    shortDeck_NLHE_GameSizeType(item: any): GameSizeType {
        let gameSizeType: GameSizeType = GameSizeType.None;

        if (item.currencyType === CurrencyType.USDT) {
            if (item.ante <= 20) {
                gameSizeType = GameSizeType.Micro;
            } else if (item.ante <= 200) {
                gameSizeType = GameSizeType.Small;
            } else if (item.ante <= 1000) {
                gameSizeType = GameSizeType.Medium;
            } else {
                gameSizeType = GameSizeType.High;
            }
        } else if (item.is_mirco === 1) {
            gameSizeType = GameSizeType.Micro;
        } else if (item.ante <= 3 * 100) {
            gameSizeType = GameSizeType.Small;
        } else if (item.ante >= 5 * 100 && item.ante <= 100 * 100) {
            gameSizeType = GameSizeType.Medium;
        } else {
            gameSizeType = GameSizeType.High;
        }

        return gameSizeType;
    }

    cleanJspkData(arr: any[]): any[] {
        const len = cv.StringTools.getArrayLength(arr);

        for (let i = len - 1; i >= 0; i--) {
            if (cv.roomManager.checkGameIsZoom(arr[i].game_id)) {
                arr.splice(i, 1);
            }
        }

        return arr;
    }

    sortZoomByAnte(a: any, b: any): number {
        // 优先盲注小的在前
        if (a.ante !== b.ante) {
            return a.ante < b.ante ? -1 : 1;
        }

        // 人数少的在前
        if (a.player_count_max !== b.player_count_max) {
            return a.player_count_max < b.player_count_max ? -1 : 1;
        }

        // 新创建的在前
        return a.create_time > b.create_time ? -1 : 1;
    }

    sortZoomByBlind(a: any, b: any): number {
        // 优先盲注小的在前
        if (a.big_blind !== b.big_blind) {
            return a.big_blind < b.big_blind ? -1 : 1;
        }

        // 人数少的在前
        if (a.player_count_max !== b.player_count_max) {
            return a.player_count_max < b.player_count_max ? -1 : 1;
        }

        // 新创建的在前
        return a.create_time > b.create_time ? -1 : 1;
    }

    initJspkList() {
        const tempGameType = this._gameType;

        this.setGameType(DiscoverGameType.ZOOM_TEXAS);

        this._jspkArr = this.screenByGame();

        this.setGameType(tempGameType);

        if (cv.StringTools.getArrayLength(this._jspkArr) > 1) {
            this._jspkArr.sort(this.sortJspk.bind(this));
        }
    }

    sortJspk(a: any, b: any): number {
        if (a.game_mode === b.game_mode) {
            if (a.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
                return this.sortZoomByBlind(a, b);
            }

            return this.sortZoomByAnte(a, b);
        }

        return a.game_mode > b.game_mode ? 1 : -1;
    }

    sortByVacancy(a: any, b: any): number {
        if (a.game_id === cv.Enum.GameId.Allin && b.game_id !== cv.Enum.GameId.Allin) {
            return -1;
        }

        if (a.game_id !== cv.Enum.GameId.Allin && b.game_id === cv.Enum.GameId.Allin) {
            return 1;
        }

        const aIsempty = a.left_seatnum === a.player_count_max;

        const bIsempty = b.left_seatnum === b.player_count_max;

        if (aIsempty && !bIsempty) {
            return -1;
        }

        if (!aIsempty && bIsempty) {
            return 1;
        }

        if (a.left_seatnum === b.left_seatnum) {
            return a.player_count_max < b.player_count_max ? 1 : -1;
        }

        return a.left_seatnum < b.left_seatnum ? 1 : -1;
    }


    sortByTimeAndPeople(a: any, b: any): number {
        // 游戏划分
        if (a.game_id > b.game_id) {
            return -1;
        }

        if (a.game_id < b.game_id) {
            return 1;
        }

        // 优先已开始的局
        if (a.start_time === 0 && b.start_time !== 0) {
            return 1;
        }

        if (a.start_time !== 0 && b.start_time === 0) {
            return -1;
        }

        return this.sortByMixed(a, b);
    }

    // 明星桌永远排第一位
    sortByStar(a: any, b: any): number {
        // 明星桌排序在前面
        if (a.game_id === cv.Enum.GameId.StarSeat && b.game_id === cv.Enum.GameId.StarSeat) {
            return 1;
        }

        if (a.game_id === cv.Enum.GameId.StarSeat) {
            return -1;
        }

        if (b.game_id === cv.Enum.GameId.StarSeat) {
            return 1;
        }

        return 1;
    }

    // If Squid Game squid_stick_top flag true 
    sortSquidGameToTop(a: any, b: any): number {
        // Move 'Squid' games to the top
        const isSquidA = a.game_id === cv.Enum.GameId.Squid;
        const isSquidB = b.game_id === cv.Enum.GameId.Squid;

        if (isSquidA && !isSquidB) return -1; // a is Squid, b is not
        if (!isSquidA && isSquidB) return 1;  // b is Squid, a is not

        return 0; // both are Squid or both are not
    }


    // 短牌优先 计时桌空桌排最上  非计时空桌排第二 满桌最后
    // 急速游戏不在这里排序
    sortByLevel(a: any, b: any): number {
        // 优先游戏
        if (a.game_id > b.game_id) {
            return -1;
        }

        if (a.game_id < b.game_id) {
            return 1;
        }

        // 其次按类型  短牌优先
        if (a.game_mode > b.game_mode) {
            return -1;
        }

        if (a.game_mode < b.game_mode) {
            return 1;
        }

        // 按排序级别划分 新版排序没有计时桌  所以排序是（非空非满， 空桌，满桌）
        const alevel = this.getSortLevel(a);

        const blevel = this.getSortLevel(b);

        if (alevel > blevel) {
            return -1;
        }

        if (alevel < blevel) {
            return 1;
        }

        // 空桌排序逻辑 小桌靠前
        if (a.player_count === 0) {
            return a.player_count_max < b.player_count_max ? -1 : 1;
        }

        // 满桌排序逻辑 大桌靠前
        if (a.player_count === a.player_count_max) {
            return a.player_count_max > b.player_count_max ? -1 : 1;
        }

        // 非空非满 牌桌所需坐满人数越少，排名越靠前 相同的大桌靠前
        if (this.getNeededNum(a) < this.getNeededNum(b)) {
            return -1;
        }

        if (this.getNeededNum(a) > this.getNeededNum(b)) {
            return 1;
        }

        return a.player_count_max > b.player_count_max ? -1 : 1;
    }

    sortByLooseMode(a: any, b: any): number {
        return (
            (b.room_mode === world_pb.RoomMode.RoomModeLoose ? 1 : 0) -
            (a.room_mode === world_pb.RoomMode.RoomModeLoose ? 1 : 0)
        );
    }

    // check loose mode stick on top or not
    private _checkLooseModeOnTop(): boolean {
        const vClubCurrentBoardList: world_pb.ClubGameSnapshotV3[] = cv.clubDataMgr.getClubCurrentBoardList();
        return vClubCurrentBoardList.some(board => board.is_loose_mode_stick_on_top);
    }

    // 坐满需要的人数
    getNeededNum(data: any): number {
        return data.player_count_max - data.player_count;
    }

    // 新版排序没有计时桌  所以排序是（非空非满， 空桌，满桌）
    getSortLevel(data: any): number {
        // // 计时桌空桌排最上  非计时空桌排第二
        if (data.player_count === data.player_count_max) {
            return 0;
        }

        if (data.player_count === 0) {
            return 1;
        }

        return 2;
    }

    // 获取多级别允许的混合数量
    getLevelProNumber(data: any): number {
        let num = 0;

        for (let i = 0; i < data.proDatas.length; i++) {
            if (data.proDatas[i].levelLimit !== 0) {
                num++;
            }
        }

        return num;
    }

    // 玩家所属级别还能坐下的人数-玩家所属级别已有人数，按由大到小降序排序
    // 玩家当前级别不能进的房间服务器会屏蔽,因此不会出现当前级别levelLimit === 0的情况
    getSortLevelByNewPro(data: any): number {
        if (data.proLevel <= 0 || data.proLevel > data.proDatas.length) {
            return 0;
        }
        const info = data.proDatas[data.proLevel - 1];

        const maxnum = info.levelLimit > 0 ? info.levelLimit : data.player_count_max;

        return maxnum - info.nowNum * 2;
    }

    destroyMTTView() {
        if (this.mtt && cc.isValid(this.mtt.node)) {
            this.mtt.node.targetOff(this);

            this.mtt.node.removeFromParent(true);

            this.mtt.node.destroy();
        }

        if (cc.vv && cc.vv.AssetsManager) {
            cc.vv.AssetsManager.hideAllDialog();
        }

        if (WorldWebSocket.hasInstance()) {
            WorldWebSocket.getInstance().close(true);

            WorldWebSocket.dropInstance();
        }

        this.mtt = null;
    }

    reInitMttView() {
        this.destroyMTTView();

        this.initMTT();
    }

    openMttView(url: string): void {
        if (!cv.config.HAVE_MTT || this.matchUrl === url) {
            return;
        }

        this.matchUrl = url;

        if (this.mttUrl !== url) {
            this.mttUrl = this.matchUrl;

            this.reInitMttView();
        }

        this.setMatchWebPos(this._gameType === this.MTT_NUM && this.node.active);
    }

    openMatchWebview(url: string): void {
        if (!this.mttUseWebView) {
            if (this.matchWeb) {
                this.matchWeb.node.active = false;
            }

            return;
        }

        if (!cv.config.HAVE_MTT || this.matchUrl === url) {
            return;
        }

        this.matchUrl = url + '&isFullscreen=' + 'false';

        if (this.matchWeb) {
            if (this.matchWeb.url !== url) {
                this.matchWeb.url = this.matchUrl;
            }

            this.setMatchWebPos(this._gameType === this.MTT_NUM && this.node.active);
        }
    }

    HandleCheckMTT(isView?: boolean) {
        isView = isView === true;

        if (this.node.active && isView && this._gameType === this.MTT_NUM) {
            this.initMTT();
        }

        if (cc.isValid(this.mtt)) {
            const isMttView = (this._gameType == this.MTT_NUM || this._gameType == this.JSNG_NUM) && this.node.active;
            isView = isView && this._gameType == this.MTT_NUM && this.node.active;

            this.setMatchWebPos(isMttView);
            MTTConnector.instance.setFindViewShowType(isView? FindViewShowType.MTT : FindViewShowType.NONE);
        }
    }

    HandleCheckWebView(isView?: boolean) {
        if (!this.mttUseWebView) {
            switch (this._gameType) {
                case this.MTT_NUM:
                    this.HandleCheckMTT(isView);
                    break;
                case this.JSNG_NUM:
                    this.handleCheckJSNG(isView);
                    break;
                default:
                    this.setMatchWebPos(false);
                    MTTConnector.instance.setFindViewShowType(FindViewShowType.NONE);
                    break;
            }

            return;
        }

        isView = isView === true;

        if (this.node.active && isView && (this._gameType == this.MTT_NUM || this._gameType == this.JSNG_NUM)) {
            this.initMTTWeb();
        }

        if (this.matchWeb) {
            if (this.matchUrl !== '' && this.matchWeb.url !== this.matchUrl) {
                this.matchWeb.url = this.matchUrl;
            }

            isView = isView && (this._gameType == this.MTT_NUM || this._gameType == this.JSNG_NUM) && this.node.active;

            this.setMatchWebPos(isView);
        }
    }

    setMatchWebPos(isView: boolean): void {
        if (!this.mttUseWebView) {
            this.setMttPos(isView);

            return;
        }

        this.webIsView = isView;

        if (this.matchWeb) {
            const pos = cc.v2(cc.winSize.width * 4, cc.winSize.height * 0.5);

            this.matchWeb.node.setPosition(isView === false ? pos : this.matchWeb_pos);
        }
    }

    setMttPos(isView: boolean): void {
        this.webIsView = isView;

        if (this.mtt) {
            const pos = cc.v2(cc.winSize.width * 4, this.mtt.node.y);

            this.mtt.widget.top = this.matchWeb_pos.x;

            this.mtt.widget.bottom = this.matchWeb_pos.y;

            this.mtt.node.active = isView;
        }
    }

    updateMTTState(): void {
        const isView = cv.config.HAVE_MTT;
        const cell = this.gameType_panel.getChildByName('button_0');

        if (!isView) {
            this.destroyMTTView();
            ImpokerHall.instance?._mttPage?.getComponent(MttPrefabPkw)?._filterPopup?.getComponent(GameListFilter)?.onHide();
            this.matchUrl = '';

            // 更新按钮选项
         
                this._gamebuttonList[0].removeFromParent(true);
                this._gamebuttonList[0].destroy();
                this._gamebuttonList.shift();
                this.PKW_GAME_TYPE.shift();
       

            const len = this._gamebuttonList.length;

            const x: number = this.gameType_scrollView.content.width * (0 - this.gameType_scrollView.content.anchorX);
            const y: number = this.gameType_scrollView.content.height * (0.5 - this.gameType_scrollView.content.anchorY);
            const split: number = this.gameType_scrollView.content.width / len;
            const cellSize = cc.size(this.gameType_panel.width / len - this._button_margin, cell.height);

            for (let i = 0; i < len; i++) {
                const button = this._gamebuttonList[i];
                const btn_x: number = x + split * i + split / 2 + button.width * (button.anchorX - 0.5);
                const btn_y: number = y;
                button.setContentSize(cellSize);
                button.setPosition(btn_x, btn_y);
            }
            this.gameTypeClickHandler(this.getNextGameTypeTag());
        } else {
            this.PKW_GAME_TYPE.unshift(this.MTT_NUM);

            const len = this.PKW_GAME_TYPE.length;

            const x: number = this.gameType_scrollView.content.width * (0 - this.gameType_scrollView.content.anchorX);
            const y: number = this.gameType_scrollView.content.height * (0.5 - this.gameType_scrollView.content.anchorY);
            const split: number = this.gameType_scrollView.content.width / len;
            const cellSize = cc.size((this.gameType_panel.width -  (2 * this._button_panel_side_margin))/ len - this._button_margin, cell.height);

            for (let i = 0; i < len; i++) {
                let tag:number;
                if (cv.config.isOverSeas()) {
                    tag = this.PKC_GAME_TYPE[i];
                } else {
                    tag = this.PKW_GAME_TYPE[i];
                }
                
                if (tag === this.MTT_NUM && cv.config.HAVE_MTT) {
                    const button = cc.instantiate(cell);

                    button.active = true;

                    button.name = 'btn_' + tag;

                    button.on(
                        'click',
                        (event: cc.Event) => {
                            cv.AudioMgr.playButtonSound('tab');
                            this.gameTypeClickHandler(tag);
                        },
                        this
                    );
                    if(tag === this.MTT_NUM && !this._gamebuttonList.find(x=>x.name === "btn_6")){
                        this._gamebuttonList.splice(0,0,button);
                        this.gameType_scrollView.content.addChild(button);
                    }
                        
                  
                }

                const button = this._gamebuttonList[i];
                let btn_x: number = x + split * i + split / 2 + button.width * (button.anchorX - 0.5);
                const btn_y: number = y;
                if(i === 0){
                    btn_x += this._button_panel_side_margin;
                } else if(i === len - 1){
                    btn_x -= this._button_panel_side_margin;
                }
                button.setContentSize(cellSize);
                button.setPosition(btn_x, btn_y);
            }
        }

        this.initLanguage();
        this.updateGameBtn();
    }

    updateJSNGState(): void {
        const isView = cv.config.HAVE_SPIN;
        if(!this.PKW_GAME_TYPE) return;

        const cell = this.gameType_panel.getChildByName('button_0');
     
        this.showJSNG = cv.config.HAVE_SPIN;
        if (!isView) {
            // 更新按钮选项
            const idx = cv.config.HAVE_MTT? 1 : 0; // 1:remove MTT tab only 2: remove MTT and JSNG tab
                this._gamebuttonList[idx].removeFromParent(true);
                this._gamebuttonList[idx].destroy();
                this._gamebuttonList.splice(idx, 1);
                this.PKW_GAME_TYPE.splice(idx, 1);
            const len = this._gamebuttonList.length;
            const cellSize = cc.size((this.gameType_panel.width - (2 * this._button_panel_side_margin))/ len - this._button_margin, cell.height);

            const x: number = this.gameType_scrollView.content.width * (0 - this.gameType_scrollView.content.anchorX);
            const y: number = this.gameType_scrollView.content.height * (0.5 - this.gameType_scrollView.content.anchorY);
            const split: number = this.gameType_scrollView.content.width / len;

            for (let i = 0; i < len; i++) {
                const button = this._gamebuttonList[i];
                const btn_x: number = x + split * i + split / 2 + button.width * (button.anchorX - 0.5);
                const btn_y: number = y;
                button.setContentSize(cellSize);
                button.setPosition(btn_x, btn_y);
            }
            this.gameTypeClickHandler(this.getNextGameTypeTag());
            
        } else {
            if(cv.config.HAVE_SPIN && !this.PKW_GAME_TYPE.includes(this.JSNG_NUM)){
                const idx = cv.config.HAVE_MTT ? 1: 0;
                this.PKW_GAME_TYPE.splice(idx, 0, this.JSNG_NUM);
            }
            const len = this.PKW_GAME_TYPE.length;
            const x: number = this.gameType_scrollView.content.width * (0 - this.gameType_scrollView.content.anchorX);
            const y: number = this.gameType_scrollView.content.height * (0.5 - this.gameType_scrollView.content.anchorY);
            const split: number = this.gameType_scrollView.content.width / len;
            const cellSize = cc.size(this.gameType_panel.width / len - this._button_margin, cell.height);

            for (let i = 0; i < len; i++) {
                let tag:number;
                if (cv.config.isOverSeas()) {
                    tag = this.PKC_GAME_TYPE[i];
                } else {
                    tag = this.PKW_GAME_TYPE[i];
                }
                
                if (tag === this.JSNG_NUM && this.showJSNG) {
                    const button = cc.instantiate(cell);

                    button.active = true;

                    button.name = 'btn_' + tag;

                    button.on(
                        'click',
                        (event: cc.Event) => {
                            cv.AudioMgr.playButtonSound('tab');
                            this.gameTypeClickHandler(tag);
                        },
                        this
                    );
                  
                     if(tag === this.JSNG_NUM && this.showJSNG && !this._gamebuttonList.find(x=>x.name === "btn_10"))
                        this._gamebuttonList.splice(cv.config.HAVE_MTT ? 1: 0,0,button);

                    this.gameType_scrollView.content.addChild(button);
                }

                const button = this._gamebuttonList[i];
                let btn_x: number = x + split * i + split / 2 + button.width * (button.anchorX - 0.5);
                const btn_y: number = y;
                if(i === 0){
                    btn_x += this._button_panel_side_margin;
                } else if(i === len - 1){
                    btn_x -= this._button_panel_side_margin;
                }
                button.setContentSize(cellSize); 
                button.setPosition(btn_x, btn_y);
            }
        }

        this.initLanguage();
        this.updateGameBtn();
    }
   
    getNextGameTypeTag(): number {
        const btn = [1, 2, 4, 8]; // These are the tags of NLHE, shortdeck.
        for(let i =0; i < btn.length; i++) {
            const btnObj = this._gamebuttonList.find(x=>x.name === `btn_${btn[i]}`);
            if(btnObj) return btn[i];
        }
       return this.PKW_GAME_TYPE[0];
    }

    gameTypeClickHandler ( tag: number) {
        this.setViewGametype(tag, true);
        if (tag === this.MTT_NUM && cv.config.HAVE_MTT) {
            this.onShowMttTab();
        } else if (tag === this.BLACKJACK_NUM && cv.config.HAVE_BLACKJACK) {
            this.onShowBJPVPTab();
        } else if (tag === this.JSNG_NUM && cv.config.HAVE_SPIN) {
            this.onShowJSNGTab();
        } else {
            this.HandleCheckMTT(false);

            this.handleCheckBJPVP(false);

            this.handleCheckJSNG(false);

            this.updateGameTypeDataAndView();
        }
    }

    // 21点开关状态
    // cv.config.HAVE_BLACKJACK: true为显示  false为关闭
    updateBlackJackState() {
        const isView = cv.config.HAVE_BLACKJACK;

        let haveBlackJack: boolean = false;

        let _indexBlackJack = -1;

        for (let i = 0; i < this.PKW_GAME_TYPE.length; i++) {
            if (this.PKW_GAME_TYPE[i] === this.BLACKJACK_NUM) {
                haveBlackJack = true;

                _indexBlackJack = i;

                break;
            }
        }

        if ((!isView && !haveBlackJack) || (isView && haveBlackJack)) {
            return;
        }

        const cell = this.gameType_panel.getChildByName('button_0');

        if (!isView) {
            // 关闭
            // 21点按钮现在所在的位置
            let curBjIndex: number = -1;

            let btnBJ_Node: cc.Node = null;

            const btnBJ_name = 'btn_' + this.BLACKJACK_NUM;

            for (let i = 0; i < this._gamebuttonList.length; i++) {
                if (this._gamebuttonList[i].name === btnBJ_name) {
                    curBjIndex = i;

                    btnBJ_Node = this._gamebuttonList[i];
                }
            }

            // 更新按钮选项,将21点按钮从 this._gamebuttonList数组中移除
            if (btnBJ_Node) {
                btnBJ_Node.removeFromParent(true);

                btnBJ_Node.destroy();

                this._gamebuttonList.splice(curBjIndex, 1);
            }

            // 将21点索引从 this.PKW_GAME_TYPE数组中移除
            if (_indexBlackJack !== -1) {
                this.PKW_GAME_TYPE.splice(_indexBlackJack, 1);
            }

            const len = this._gamebuttonList.length;

            const x: number = this.gameType_scrollView.content.width * (0 - this.gameType_scrollView.content.anchorX);
            const y: number = this.gameType_scrollView.content.height * (0.5 - this.gameType_scrollView.content.anchorY);
            const split: number = this.gameType_scrollView.content.width / len;

            for (let i = 0; i < len; i++) {
                const button = this._gamebuttonList[i];
                let btn_x: number = x + split * i + split / 2 + button.width * (button.anchorX - 0.5);
                const btn_y: number = y;
                if(i == 0){
                    btn_x = btn_x + 8;
                } else if(i == len - 1){
                    btn_x = btn_x - 8;
                }
                button.setPosition(btn_x, btn_y);
            }

            if (this._gameType === this.BLACKJACK_NUM) {
                this.setViewGametype(1, true);

                this.updateGameTypeDataAndView();
            }
        } else {
            // 重新打开21点
            let shortIndex = -1;

            for (let i = 0; i < this.PKW_GAME_TYPE.length; i++) {
                if (this.PKW_GAME_TYPE[i] === 2) {
                    // 获取短牌的位置
                    shortIndex = i;

                    break;
                }
            }

            if (cv.config.HAVE_BLACKJACK) {
                // 21点按钮添加到短牌按钮后面
                this.PKW_GAME_TYPE.splice(shortIndex + 1, 0, this.BLACKJACK_NUM);
            }

            const len = this.PKW_GAME_TYPE.length;

            const x: number = this.gameType_scrollView.content.width * (0 - this.gameType_scrollView.content.anchorX);
            const y: number = this.gameType_scrollView.content.height * (0.5 - this.gameType_scrollView.content.anchorY);
            const split: number = this.gameType_scrollView.content.width / len;

            for (let i = 0; i < len; i++) {
                if (this.PKW_GAME_TYPE[i] === this.BLACKJACK_NUM) {
                    const button = cc.instantiate(cell);

                    let tag;

                    if (cv.config.isOverSeas()) {
                        tag = this.PKC_GAME_TYPE[i];
                    } else {
                        tag = this.PKW_GAME_TYPE[i];
                    }

                    button.active = true;

                    button.name = 'btn_' + tag;

                    button.on(
                        'click',
                        (event: cc.Event) => {
                            cv.AudioMgr.playButtonSound('tab');
                            this.gameTypeClickHandler(tag);
                        },
                        this
                    );

                    this._gamebuttonList.splice(shortIndex + 1, 0, button);

                    this.gameType_scrollView.content.addChild(button);
                }

                const button = this._gamebuttonList[i];
                let btn_x: number = x + split * i + split / 2 + button.width * (button.anchorX - 0.5);
                if(i === 0){
                    btn_x += this._button_panel_side_margin;
                } else if(i === len - 1){
                    btn_x -= this._button_panel_side_margin;
                }
                const btn_y: number = y;
                
                button.setPosition(btn_x, btn_y);
            }
        }

        this.updateGameBtn();
        this.initLanguage();
    }

    initMTT(): void {
        if ((cv.config.HAVE_SPIN || cv.config.HAVE_MTT) && !cc.isValid(this.mtt)) {
            this.createMTT();
        }
    }

    createMTT() {
        MTTConnector.instance.initCCVV();

        if ((cv.config.HAVE_SPIN || cv.config.HAVE_MTT) && !cc.isValid(this.mtt)) {
            const prefab = this.mttPrefab;

            this.initMTTParam(prefab);
        }
    }

    initMTTParam(prefab: cc.Prefab) {
        MTTConnector.instance.initMTTParams();

        const temp = cc.instantiate(prefab);

        this.mtt = temp.getComponent(ImpokerHallFeature);

        temp.setContentSize(this.scrollView.node.width, this.scrollView.node.height + this.pokerPage_panel.height - 20);

        this.matchWeb_pos = this.mttInitPos;

        this.mtt.node.setPosition(cc.v2(0, this.mtt.node.y));

        this.setMatchWebPos(this.webIsView);

        temp.parent = cc.director.getScene().getComponentInChildren(HallScene).node;

        this.mtt.dialogLayer = temp.parent;

        if (this._gameType == this.MTT_NUM || this._gameType == this.JSNG_NUM) {
            this.setMatchWebPos(this.node.active);
        } else {
            this.setMatchWebPos(false);
        }

        if (cv.roomManager.isEnterMTT) {
            this.enterMTTGame(cv.roomManager.mtt_id);
        }
    }

    onShowMttTab() {
        if (cv.config.HAVE_BLACKJACK) {
            this.handleCheckBJPVP(false);
        }

        this.handleCheckJSNG(false);
        this.HandleCheckMTT(true);
    }

    onAuthMttSucc(msg: any) {
        MTTConnector.instance.onAuthMttSucc(msg);
    }

    onAuthMttError(msg: any) {
        MTTConnector.instance.onAuthMttError(msg);
    }

    public updateListviewData(data) {
        if (!this.isBoardListChanged(data) && !this.isPKFStarLiveChanged()) {
            return;
        }
        const dataList = [];

        for (let index = 0; index < data.length; index++) {
            // TOFIX:
            if (data[index].game_id === cv.Enum.GameId.StarSeat) {
                // 明星桌
                dataList.push({ type: 1, data: data[index] });
                // if(!cc.sys.isBrowser || CC_PREVIEW) {
                //     dataList.push({ type: 1, data: data[index] });
                // }
            } else if (data[index].currencyType === 101) {
                dataList.push({ type: 2, data: data[index] });
            } else {
                dataList.push({ type: 0, data: data[index] });
            }
        }

        // add pkf star live
        const pkfStarLiveDataList = PKFConnector.instance.StarLiveManager.getPKFStarLiveDataList(
            this._gameType,
            this.viewIndex
        );

        for (let i = pkfStarLiveDataList.length - 1; i >= 0; i--) {
            dataList.unshift({ type: 3, data: pkfStarLiveDataList[i] });
        }
        this.refreshTop.resetUI();
        this.scrollView.getComponent(ListView).notifyDataSetChanged(dataList);
    }

    public bindcallfunc(node: cc.Node, info, i) {
        if (info.type === 0) {
            node.getComponent(FindItem).updateItemData(this._gameType, info.data, this._onItemCallback.bind(this));
        } else if (info.type === 1) {
            node.getComponent(FindItemStar).updateItemData(info.data, this._onItemCallback.bind(this));
        } else if (info.type === 2) {
            node.getComponent(FindItem).updateItemData(this._gameType, info.data, this._onItemCallback.bind(this));
        } else if (info.type === 3) {
            node.getComponent(FindItemPKFStarLive).updateItemData(info.data, this._onItemCallback.bind(this));
        }
    }

    private _onItemCallback(itemClicked: any) {
        this._selectedItem = itemClicked;
        cv.worldNet.requestCheckSquidGameStatus(itemClicked.roomId ?? 0, itemClicked.gameId ?? 0);
        cv.SwitchLoadingView.show(cv.config.getStringData('Loading_resource'));
        this.scheduleOnce(this._joinSelectedRoom, cv.config.CHECK_SQUID_STATUS_TIME_OUT);
    }
  
    private _joinSelectedRoom(){
        this._selectedItem?.funcJoinRoom?.();
    }

    private _onSquidGameBlockStatusResponse(data: any): void {
        console.log('SquidGameBlockStatusResponse', data);
        cv.SwitchLoadingView.hide();
        this.unschedule(this._joinSelectedRoom);
        const joinRoom = (data) =>{
            cv.roomManager.RequestJoinRoom(cv.Enum.GameId.Squid, data.squidRoomId, false, false);
        };
        if (data.squidRoomId === this._selectedItem?.roomId) {
            joinRoom(data);
            return;
        }
        switch (data.error) {
            case 1:
                this._joinSelectedRoom();
                break;
            case 31123:
            // won an squid in another game and wants to join new game
                cv.TP.showMsg(cv.config.getStringData("Squid_Player_Joining_New_Room_After_Winning_Squid"), cv.Enum.ButtonStyle.TWO_BUTTON, (obj: any): void => {
                    joinRoom(data); // Go back to exisitng game
                },()=>{
                    // Leaving the current roome before join new room
                    cv.roomManager.setCurrentGameID(cv.Enum.GameId.Squid);
                    cv.worldNet.requestSquidGameLeaveExistingRoom(data.squidRoomId);
                });
                cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_SQUID_JOIN);
            break;
            case 31124: 
                // not won any squid wants to join this. Do force join to old game.
                cv.TP.showMsg(cv.config.getStringData("Squid_Return_To_Existing_Game"), cv.Enum.ButtonStyle.TWO_BUTTON, () => {
                    joinRoom(data);
                });
            break;
            case 22:
                cv.worldNet.requestCurrentBoardList();
                PKFConnector.instance.StarLiveManager.requestPKFStarLiveList();
                cv.ToastError(data.error);
                this.resetAutoFetchBoardList();
            break;
        }
    }
  
    private _onSquidGameLeavingResponse(data: any): void {
        if (data.error === 1){
            this._joinSelectedRoom();
        }
    }

    public getItemType(data, index) {
        return data.type;
    }

    initBJPVP(): void {
        if (!cc.isValid(this.bjpvp)) {
            this.bjpvp = BJPVPConnector.instance.createGameList(
                this.bjpvpPrefab,
                this.mtt_img,
                cc.director.getScene().getComponentInChildren(HallScene).node
            );
        }
    }

    handleCheckBJPVP(isView?: boolean): void {
        isView = isView === true;

        if (this.node.active && isView && this._gameType === this.BLACKJACK_NUM) {
            this.initBJPVP();
        }

        if (cc.isValid(this.bjpvp)) {
            isView = isView && this._gameType === this.BLACKJACK_NUM && this.node.active;

            this.bjpvp.node.active = isView;
        }
    }

    // level: 0-micro微, 1-small小, 2-mid中, 3-Da (big) 大
    switchBJPVPTab(level: number) {
        this.bjpvp.gameListFilter.switchBJPVPTab(level);
    }

    onShowBJPVPTab() {
        if (cv.config.HAVE_MTT) {
            this.HandleCheckMTT(false);
            this.handleCheckJSNG(false);
        }

        this.handleCheckBJPVP(true);
    }

    onAuthBlackJackSucc(msg: any) {
        BJPVPConnector.instance.onAuthBlackJackSucc(msg);
    }

    onAuthBlackJackError(msg: any) {
        BJPVPConnector.instance.onAuthBlackJackError(msg);
    }

    initMTTWeb(): void {
        if (!this.mttUseWebView) {
            this.initMTT();

            return;
        }

        if (cv.config.HAVE_MTT && !this.matchWeb && cv.config.CAN_USE_WEBGL && cv.config.getMTTWebIndex() === 0) {
            this.matchWebNode = new cc.Node();

            this.matchWeb = this.matchWebNode.addComponent(cc.WebView);

            const scrollPos = this.scrollView_mtt.node.parent.convertToWorldSpaceAR(
                this.scrollView_mtt.node.getPosition()
            );

            this.matchWeb.node.setContentSize(
                this.scrollView_mtt.node.width,
                this.scrollView_mtt.node.height + this.pokerPage_panel.height - 20
            );

            this.matchWeb_pos = cc.v2(scrollPos.x, scrollPos.y + this.pokerPage_panel.height * 0.5 + 10);

            this.setMatchWebPos(this.webIsView);

            cc.director.getScene().addChild(this.matchWeb.node);

            const self = this;

            this.matchWeb.node.on(
                'error',
                () => {
                    console.log('this.matchWeb error');
                    cv.TP.showMsg(cv.config.getStringData('MTT_Load_failed'), cv.Enum.ButtonStyle.TWO_BUTTON, () => {
                        if (self.matchWeb) {
                            self.matchWeb.url = '';
                        }

                        cv.worldNet.RequestAuthApi();
                    });
                },
                this
            );

            if (cc.sys.isNative) {
                this.matchWeb.setJavascriptInterfaceScheme('mttjs');

                this.matchWeb.setOnJSCallback((webView: cc.WebView, url: string) => {
                    if (url.search('mttjs://goto_game') !== -1) {
                        if (self.matchWeb) {
                            self.matchWeb.node.setContentSize(cc.winSize);
                        }

                        self.matchWeb_pos = cc.v2(cc.winSize.width * 0.5, cc.winSize.height * 0.5);

                        self.setMatchWebPos(self.webIsView);
                    } else if (url.search('mttjs://goto_list') !== -1) {
                        if (self.matchWeb) {
                            self.matchWeb.node.setContentSize(
                                self.scrollView_mtt.node.width,
                                self.scrollView_mtt.node.height /*+ self.pokerPage_panel.height - 20*/
                            );
                        }

                        self.matchWeb_pos = cc.v2(scrollPos.x, scrollPos.y + self.pokerPage_panel.height * 0.5 + 10);

                        self.setMatchWebPos(self.webIsView);
                    } else if (url.search('mttjs://back-normal') !== -1 || url.search('mttjs://back-abnormal') !== -1) {
                        self.matchUrl = '';

                        if (self.matchWeb) {
                            self.matchWeb.url = '';
                        }

                        cv.worldNet.RequestAuthApi();
                    } else if (url.search('mttjs://WebGL') !== -1) {
                        cv.config.CAN_USE_WEBGL = false;

                        if (self.matchWeb) {
                            self.matchWeb.destroy();

                            self.matchWeb = null;

                            self.matchWebNode.targetOff(this);

                            self.matchWebNode.removeFromParent(true);

                            self.matchWebNode.destroy();

                            self.matchWebNode = null;
                        }

                        self.matchWeb = null;

                        self.matchUrl = '';

                        cv.worldNet.RequestAuthApi();
                    }
                });
            }

            if (this._gameType === this.MTT_NUM && this.matchUrl.length > 0) {
                this.matchWeb.url = this.matchUrl;

                this.setMatchWebPos(this.node.active);
            } else {
                this.setMatchWebPos(false);
            }
        }
    }

    mttNotify(url: string) {
        const scrollPos = this.scrollView.node.parent.convertToWorldSpaceAR(this.scrollView.node.getPosition());

        if (url.search('mttjs://goto_game') !== -1) {
            if (cc.isValid(this.mtt)) {
                this.mtt.node.setContentSize(cc.winSize);
            }

            MTTConnector.instance.sendMessageCenter('hide_mail_entrance');

            this.matchWeb_pos = cc.v2(0, 0);

            this.setMatchWebPos(this.webIsView);

            cv.resMgr.adaptWidget(this.mtt.node, true);
        } else if (url.search('mttjs://goto_list') !== -1) {
            if (cc.isValid(this.mtt)) {
                this.mtt.node.setContentSize(
                    this.scrollView.node.width,
                    this.scrollView.node.height + this.pokerPage_panel.height - 20
                );
            }

            MTTConnector.instance.sendMessageCenter('show_mail_entrance');

            this.matchWeb_pos = this.mttInitPos;

            this.setMatchWebPos(this.webIsView);

            cv.resMgr.adaptWidget(this.mtt.node, true);
        } else if (url.search('mttjs://back-normal') !== -1) {
            MTTConnector.instance.sendMessageCenter(
                MTTConnector.instance.config.BroadCast.MTT_TOKEN_ERROR,
                MTTConnector.instance.config.tokenErrorMsg.BACK_NORMAL
            );
        } else if (url.search('mttjs://back-abnormal') !== -1) {
            MTTConnector.instance.sendMessageCenter(
                MTTConnector.instance.config.BroadCast.MTT_TOKEN_ERROR,
                MTTConnector.instance.config.tokenErrorMsg.BACK_ABNORMAL
            );
        }
    }

    enterMTTHall(mttId: number) {
        if (mttId && this.mtt && cc.isValid(this.mtt.node)) {
            cv.roomManager.isEnterMTT = false;

            this.mtt.callMTTHall(mttId);
        }
    }

    enterMTTGame(mttId: number, callback?: Function) {
        if (mttId) {
            cv.roomManager.isEnterMTT = false;

            MTTConnector.instance.enterMTTGame(mttId, callback);
        }
    }

    showMttError(msg: string): void {
        if (cv.config.HAVE_MTT && this.node.active && this._gameType === this.MTT_NUM) {
            cv.TT.showMsg(msg, cv.Enum.ToastType.ToastTypeError);
        }
    }

    setSafeAreaAndScrollViewPart() {
        this.top.height = this.topOriginal.height;

        this.bottom.height = this.bottomOriginal.height;

        const offsetY = cv.SafeAreaWithDifferentDevices.getSafeAreaHall();

        this.safearea.height = offsetY;

        // Quick Seat
        this.quickSeatSafearea.height = offsetY;

        let scrollViewPartHeight =
            this.findView.height -
            offsetY -
            this.top.height -
            this.img_PageView.node.height -
            this.gameType_panel.height -
            this.bottom.height;

        if (this.pokerPage_panel.active) {
            scrollViewPartHeight -= this.pokerPage_panel.height;
        }

        this.scrollviewPart.height = scrollViewPartHeight;

        this.layout.updateLayout();

        this.quickSeatlayout.updateLayout();

        this.fillter_line.setPosition(this.fillter_line_Pos);
    }

    initJSNG() {
        if (!cc.isValid(this.jsngGameList)) {
            //make sure ImpokerHall exist
            this.initMTT();

            const temp = cc.instantiate(this.jsngPrefab);
            temp.parent = this.mtt.layers[0];
            this.jsngGameList = temp.getComponent(JackpotSngGameList);
            if (MTTConnector.instance.isPKW) {
                temp.getComponent(cc.Widget).bottom = 80;
                this.jsngGameList.popUpParent = cc.director.getScene().getComponentInChildren(HallScene).node;
            }
            cc.vv.ConsoleLog.log("initJSNG");
        }
    }

    handleCheckJSNG(isView?: boolean) {
        isView = isView == true;

        if (this.node.active && isView && this._gameType == this.JSNG_NUM) {
            this.initJSNG();
        }

        if (cc.isValid(this.jsngGameList)) {
            const isMttView = (this._gameType == this.MTT_NUM || this._gameType == this.JSNG_NUM) && this.node.active;
            isView = isView && this._gameType == this.JSNG_NUM && this.node.active;

            cc.vv.ConsoleLog.log("handleCheckJSNG", this._gameType, isMttView, isView);

            this.setMttPos(isMttView);
            MTTConnector.instance.setFindViewShowType(isView? FindViewShowType.JSNG : FindViewShowType.NONE);
        }
    }

    onShowJSNGTab() {
        if (cv.config.HAVE_BLACKJACK) {
            this.handleCheckBJPVP(false);
        }

        this.HandleCheckMTT(true);
        this.handleCheckJSNG(true);
    }
    
    refreshBoardList() {
        cv.worldNet.requestCurrentBoardList();
        PKFConnector.instance.StarLiveManager.requestPKFStarLiveList();
        this.resetAutoFetchBoardList();
    }

    enableFetchBoardList() {
      this.isBlockFetchBoardList = false;
    }

    fetchBoardList() {
        const currentTime = Date.now();
        if (currentTime - this.lastFetchTime < REQUEST_BOARD_LIST_TIME_LIMIT * 1000) {
            return;
        }

        this.isBlockFetchBoardList = true;
        this.lastFetchTime = currentTime;
        cv.worldNet.requestCurrentBoardList();
        PKFConnector.instance.StarLiveManager.requestPKFStarLiveList();
        this.scheduleOnce(this.enableFetchBoardList.bind(this), REQUEST_BOARD_LIST_TIME_LIMIT);
    }

    resetAutoFetchBoardList(interval: number = 30) {
        this.unschedule(this.fetchBoardList);
        this.schedule(this.fetchBoardList, interval);
    }

    isBoardListChanged(newData: any[]): boolean {
        const normalizedData = this.normalizeData(newData);
        const newDataMd5 = cv.md5.md5(JSON.stringify(normalizedData));
        if (this.lastBoardListMd5 !== newDataMd5) {
            this.lastBoardListMd5 = newDataMd5;
            return true;
        }
        
        return false;
    }

    isPKFStarLiveChanged(): boolean {
        const currentPKFData = PKFConnector.instance.StarLiveManager.getPKFStarLiveDataList(
            this._gameType,
            this.viewIndex
        );
        
        const newPKFDataMd5 = cv.md5.md5(JSON.stringify(currentPKFData));
        
        if (this.lastPKFStarLiveMd5 !== newPKFDataMd5) {
            this.lastPKFStarLiveMd5 = newPKFDataMd5;
            return true;
        }
        
        return false;
    }

    normalizeData(data: any[]): any[] {
        return data.map(item => {
            if (!item.room_sub_title) return item;
          
            return {
                ...item,
                room_sub_title: item.room_sub_title
                    ? Object.fromEntries(
                        Object.entries(item.room_sub_title).sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
                    )
                    : {}
            };
        });
    }
}
