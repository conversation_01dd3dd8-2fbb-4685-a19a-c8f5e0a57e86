import { pb } from "../../../common/pb/ws_protocol";
import { LANGUAGE_TYPE } from "../../../common/tools/Enum";
import cv from "../cv";
import EarnView from "./EarnView";
import PointItem from "./PointItem";
import SwitchPoint from "./SwitchPoint";
import HallScene from './HallScene';

const { ccclass, property } = cc._decorator;

@ccclass
export class RewardsView extends cc.Component {
    @property(cc.Label) casinoChipsAmount: cc.Label = null;

    @property(cc.Label) balanceAmount: cc.Label = null;

    @property(cc.Prefab) switchPoint_prefab: cc.Prefab = null;

    @property(cc.Node) scroll_content: cc.Node = null;

    @property(cc.Node) item_node: cc.Node = null;

    @property(cc.Prefab) pointItem_prefab: cc.Prefab = null;

    @property(cc.Label) lab_tips: cc.Label = null;

    @property(cc.Node) backBtn: cc.Node = null;

    @property(cc.Node) rewardsView: cc.Node = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) top: cc.Node = null;

    @property(cc.Node) backGroundBg: cc.Node = null;

    @property(cc.Layout) backGroundBgLayout: cc.Layout = null;

    @property(cc.Node) title: cc.Node = null;

    @property(cc.Node) newNode: cc.Node = null;

    @property(cc.Layout) newNodeLayout: cc.Layout = null;

    @property(cc.Node) casinoChips: cc.Node = null;

    @property(cc.Node) balance: cc.Node = null;

    @property(cc.Node) dummyHieght39: cc.Node = null;

    @property(cc.Node) rootScrollView: cc.Node = null;

    @property(cc.Layout) contentLayout: cc.Layout = null;

    protected switchPoint_node: cc.Node = null;

    protected getGoodList: boolean = false;

    public earnView: EarnView = null;

    protected onLoad(): void {
        this.registerMsg();

        this.switchPoint_node = cc.instantiate(this.switchPoint_prefab);

        cv.action.addChildToSceneOnce(this.switchPoint_node);

        this.switchPoint_node.active = false;

        this.setSafeArea();
    }

    protected onEnable(): void {
        this.balanceAmount.string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().user_points);

        let scroll = this.scroll_content.parent.getComponent(cc.ScrollView);

        scroll.scrollToTop(0.01);

        this.GoodsListRequest();
    }

    protected onDestroy(): void {
        this.unregisterMsg();
    }

    protected registerMsg(): void {
        cv.MessageCenter.register("EarnView_ExchangeUserPoints", this.ExchangeUserPointsResponse.bind(this), this.node);

        cv.MessageCenter.register("update_info", this.noticeUpdateData.bind(this), this.node);

        cv.MessageCenter.register("update_gold", this.noticeUpdateData.bind(this), this.node);

        cv.MessageCenter.register("EarnView_GoodsListResponse", this.GoodsListResponse.bind(this), this.node);
    }

    protected unregisterMsg(): void {
        cv.MessageCenter.unregister("EarnView_ExchangeUserPoints", this.node);

        cv.MessageCenter.unregister("update_info", this.node);

        cv.MessageCenter.unregister("update_gold", this.node);

        cv.MessageCenter.unregister("EarnView_GoodsListResponse", this.node);
    }

    protected ExchangeUserPointsResponse(msg: pb.ExchangeUserPointsResponse): void {
        let isSucc = msg.error == 1;

        let tips = "";

        if (isSucc) {
            tips = "SwitchPoint_lab_4";
        }
        else if (msg.error == 236) {
            tips = "ServerErrorCode" + msg.error;
        }
        else {
            cv.ToastError(msg.error);

            return;
        }

        this.switchPoint_node.getComponent(SwitchPoint).showSucc(cv.config.getStringData(tips), isSucc);

        this.noticeUpdateData();
    }

    protected noticeUpdateData(): void {
        this.casinoChipsAmount.string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().game_coin);

        this.balanceAmount.string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().user_points);

        this.setLabTips();
    }

    protected GoodsListRequest(): void {
        this.noticeUpdateData();

        if (!cv.dataHandler.getUserData().m_bIsLoginServerSucc) return;

        if (!this.getGoodList) {
            cv.worldNet.GoodsListRequest();
        }
        else {
            this.setLabTips();
        }
    }

    protected GoodsListResponse(msg: pb.Goods[]): void {
        let msglen = cv.StringTools.getArrayLength(msg);

        if (!this.getGoodList && msglen > 0) {
            this.setLabTips();

            let len = Math.ceil(msglen / 2);

            for (let i = 0; i < len; i++) {
                let node = cc.instantiate(this.item_node);

                node.active = true;

                this.scroll_content.addChild(node);

                let item_0 = node.getChildByName("item_0");

                let item_1 = node.getChildByName("item_1");

                let item0 = cc.instantiate(this.pointItem_prefab);

                item_0.addChild(item0);

                item0.getComponent(PointItem).show(msg[2 * i], 2 * i);

                let item1 = cc.instantiate(this.pointItem_prefab);

                item_1.addChild(item1);

                item1.getComponent(PointItem).show(msg[2 * i + 1], 2 * i + 1);
            }
        }

        this.getGoodList = true;
    }

    protected setLabTips(): void {
        let point = cv.StringTools.serverGoldToShowNumber(cv.dataHandler.getUserData().user_points);

        if (point < 200) {
            this.lab_tips.string = "";

            this.lab_tips.node.active = false;

            return;
        }
        else {
            this.lab_tips.node.active = true;
        }

        let num_0 = Math.floor(point / 200);

        let num_1 = Math.floor(point / 1000);

        if (point < 1000) {
            this.lab_tips.string = cv.StringTools.formatC(cv.config.getStringData("Earnings_lab_use1"), num_0);
        }
        else {
            this.lab_tips.string = cv.StringTools.formatC(cv.config.getStringData("Earnings_lab_use0"), num_0, num_1);
        }
    }

    protected goBack(): void {
        cv.action.showAction(this.node, cv.action.eMoveActionDir.EMAD_TO_RIGHT, cv.action.eMoveActionType.EMAT_FADE_OUT);
    }

    public onBtnBack(event: cc.Event): void {
        cv.AudioMgr.playButtonSound('back_button');

        this.hideBackBtn();

        this.goBack();
    }

    public onBtnExplainCasinoChips(event: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        this.earnView.onTotalCoinExplainBtnClicked();
    }

    showBackBtn() {
        this.backBtn.opacity = 255;
    }

    hideBackBtn() {
        this.backBtn.opacity = 0;
    }

    setSafeArea() {
        cv.resMgr.adaptWidget(this.node, true);

        let hallScene = cc.director.getScene().getComponentInChildren(HallScene);

        this.top.height = hallScene.getTopHeight();

        let offsetY = cv.SafeAreaWithDifferentDevices.getSafeAreaHall();

        this.safearea.height = offsetY;

        let backGroundBgHeight = this.rewardsView.height - offsetY - this.top.height;

        this.backGroundBg.height = backGroundBgHeight;

        this.layout.updateLayout();

        let newNodeHeight = this.backGroundBg.height - this.title.height;

        this.newNode.height = newNodeHeight;

        this.backGroundBgLayout.updateLayout();

        let rootScrollViewHeight = this.newNode.height - this.casinoChips.height - this.balance.height -this.dummyHieght39.height;

        this.rootScrollView.height = rootScrollViewHeight;

        this.newNodeLayout.updateLayout();
        
        this.contentLayout.paddingBottom += offsetY;
        this.contentLayout.updateLayout();

        cv.resMgr.adaptWidget(this.node, true);
    }
}
