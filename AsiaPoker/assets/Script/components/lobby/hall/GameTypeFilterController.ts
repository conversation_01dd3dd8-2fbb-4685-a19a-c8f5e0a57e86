import BaseFilterController from './BaseFilterController';
import cv from '../cv';
import GameTypeFilterButton from './GameTypeFilterButton';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import { DiscoverGameType } from './FindView';

const { ccclass } = cc._decorator;

@ccclass
export default class GameTypeFilterController extends BaseFilterController {
    private _filterData: string[] = [];

    public get filterData() {
        return this._filterData;
    }

    public get SAVE_KEY(): string {
        return 'FILTER_GAMETYPE_PKW_' + this.keyIndex;
    }

    public initialize(
        gameTypes: string[],
        index: number
    ): void {
        if (index !== -1) this.keyIndex = index;
        this.titleLabel.string = cv.config.getStringData('Filter_GameType_Title_Text');
        for (let a = 0; a < gameTypes.length; a++) {
            const gameTypebutton = this.getNewButton();
            const buttonComponent: GameTypeFilterButton = gameTypebutton.getComponent(GameTypeFilterButton);
            const gameType = gameTypes[a];
            const savedState = this._filterData.indexOf(gameType) !== -1;
            buttonComponent.initialize(gameType, savedState, this.onFilterClicked.bind(this));
        }

        this.node.active = true;
        this.layoutContainer.updateLayout();
        this._initData();
    }

    public refreshByData(): void {
        this.activeButtons.forEach((element) => {
            const btn = element.getComponent(GameTypeFilterButton);
            btn.setNewState(this._filterData.indexOf(btn.metadata) !== -1);
        });
    }

    public filter(data: world_pb.ClubGameSnapshotV3[], gameType: Number): world_pb.ClubGameSnapshotV3[] {
        let tempData = data;
        if (this.isApplicable(gameType) && this._filterData.length > 0 && this.isActive) {
            tempData = tempData.filter(
                (x) =>
                    (this._filterData.includes('texas') && ([cv.Enum.GameId.Texas, cv.Enum.GameId.Squid].includes(x.game_id))) ||
                    (this._filterData.includes('zoom') && cv.roomManager.checkGameIsZoom(x.game_id)) ||
                    x.game_id === cv.Enum.GameId.StarSeat
            );
        }
        return tempData;
    }

    public isQualified(
        item: world_pb.ClubGameSnapshotV3,
        gameType: Number,
        isSubset: boolean
    ): boolean {
        if (this._filterData.length > 0 && this.isApplicable(gameType) && isSubset) {
            return (
                (this._filterData.includes('texas') && ([cv.Enum.GameId.Texas, cv.Enum.GameId.Squid].includes(item.game_id))) ||
                (this._filterData.includes('zoom') && cv.roomManager.checkGameIsZoom(item.game_id))
            );
        }
        return true;
    }

    public isApplicable(gameType: Number): boolean {
        return gameType === DiscoverGameType.DZPK || gameType === DiscoverGameType.DZPK_SHORT;
    }

    public override saveFilterDataToFile(): void {
        this._saveGameTypeDataToFile();
    }

    public override initLanguage(): void {
        super.initLanguage();
        this.titleLabel.string = cv.config.getStringData('Filter_GameType_Title_Text');
    }

    protected override onFilterClicked(Component: GameTypeFilterButton): void {
        const newState = Component.currentState;
        const gameType = Component.metadata;
        if (newState) {
            this._filterData.push(Component.metadata);
        } else {
            const index = this._filterData.findIndex((x) => x.includes(gameType));
            this._filterData.splice(index, 1);
        }
        this.emitClickEvent();
    }

    protected override emitClickEvent() {
        cc.game.emit('OnFilterButtonClicked', true);
    }

    private _initData(): void {
        const data = this._fetchSavedGameTypeDataFromFile();
        if (data) {
            this._filterData = data;
            this.refreshByData();
        }
    }

    private _saveGameTypeDataToFile() {
        const value = this.filterData.join(this.saveSeparatorChar);
        cv.tools.SaveStringByCCFile(this.SAVE_KEY, value);
    }

    private _fetchSavedGameTypeDataFromFile() {
        let stringArray: string[] = [];
        const savedData = cv.tools.GetStringByCCFile(this.SAVE_KEY);
        if (savedData) {
            stringArray = savedData.split(this.saveSeparatorChar);
        }
        return stringArray;
    }
}
