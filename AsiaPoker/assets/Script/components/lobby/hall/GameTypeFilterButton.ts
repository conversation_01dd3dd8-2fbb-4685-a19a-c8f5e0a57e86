
import cv from "../cv";
import LobbyFilterButtonBase from "./LobbyFilterButtonBase";

const {ccclass} = cc._decorator;

@ccclass
export default class GameTypeFilterButton extends LobbyFilterButtonBase {

    protected metaData: string;
    
    public get metadata()
    {
        return this.metaData;
    }

    protected onLoad(): void 
    {
        super.onLoad();
    }

    public initialize(type: string, newState: boolean, buttonCallback: (btn: GameTypeFilterButton)=>void)
    {
        this.metaData = type;
        this.setNewState(newState);
        this.initLanguage();
        this.onButtonClickDelegate = buttonCallback;
    }

    public override initLanguage():void{
        let localisedString = "";
        switch (this.metaData) {
            case 'texas':
                localisedString = cv.config.getStringData('Filtrate_filtrate_panel_normal_game_button_2');
                break;

            case 'zoom':
                localisedString = cv.config.getStringData('DataView_data_panel_dataInfo_panel_zoomGame_button');
                break;
        }
        this.setContent(localisedString);
    }

    protected onUnuse(): void {
        super.onUnuse();
        this.metaData = "";
    }

    protected onClickListener(event: cc.Event): void 
    {
        super.onClickListener(event);
        this.onButtonClickDelegate(this);
    }

}
