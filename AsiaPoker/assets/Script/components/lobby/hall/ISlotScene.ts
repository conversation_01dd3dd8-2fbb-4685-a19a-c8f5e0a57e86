import cv from "../cv";
import { Safe<PERSON>reaHelper } from "../../../../poker-framework/scripts/core/system/view/safe-area-helper";


const { ccclass, property } = cc._decorator;
@ccclass
export class ISlotScene extends cc.Component {
    @property(cc.WebView) web: cc.WebView = null;
    @property(cc.Node) webViewContainer: cc.Node = null;
    @property(cc.Node) headerContainer: cc.Node = null;
    @property(cc.Button) btExit: cc.Button = null;

    onLoad() {
        if (cv.config.needLandscape(cv.Enum.SCENE.ISLOT_SCENE)){
            this.adaptLandscapeView();
        }
        cv.config.setCurrentScene(cv.Enum.SCENE.ISLOT_SCENE);
        cv.config.adaptScreenHen(this.node);

        cv.resMgr.adaptWidget(this.node, true);
        this.adaptWebContainner();
    }

    protected onEnable(): void {
        cv.MessageCenter.register("HideWebview_ShowWindows", this.HandleSwitchServer.bind(this), this.node);
    }

    protected onDisable(): void {
        cv.MessageCenter.unregister("HideWebview_ShowWindows", this.node);
    }

    start() {

        this.setWebUrl();
        this.showISlotScene();
        this.web.setJavascriptInterfaceScheme("ccjs");
        this.web.setOnJSCallback((webView: cc.WebView, url: string) => {
            console.error("ISlot ccjs ------ " + url);
            if (url.search("ccjs://back-normal") !== -1) {
                this.exitGame();
                return;
            }
        });

        this.initText();
        this.btExit.node.on("click", this.onExitBtnClicked, this);
        cv.StatusView.show(false);
     
        this._tempFixViewIssue();
    }

    /* this is to force refresh the web view, without this there will be a resolution issue in small screen devices (can only test on native app), 
    this is just a temporarily fix due to constraint timeline, please revisit in the future */
    private _tempFixViewIssue() {
        this.scheduleOnce(() => {
            this.setWebActive(null);
        }, 1);

        this.scheduleOnce(() => {
            this.setWebActive(true);
        }, 2);
    }

    private onExitBtnClicked(){
        cv.TP.showMsg(
            cv.config.getStringData('ExitMinigameConfirm'),
            cv.Enum.ButtonStyle.TWO_BUTTON,
            this.exitGame.bind(this),
            ()=>{
                this.setWebActive(true)
            }
        );
    }

    HandleSwitchServer(isView?: boolean) {
        isView = (!cv.TP.getVisible()) && isView;
        this.setWebActive(isView);
    }

    initText() {
        cv.StringTools.setLabelString(this.btExit.node, "Label", "MiniGames_Exit");
    }

    exitGame() {
        cv.worldNet.requestISlotLogout();
        this.setWebActive(false);
        cv.roomManager.reset();
        cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene): void => {
            cv.MessageCenter.send("switchSceneToMiniGame");
        });
       
    }

    adaptLandscapeView() {
        var size = this.node.getComponent(cc.Canvas).designResolution;
        this.node.getComponent(cc.Canvas).designResolution = cc.size(size.height, size.width);
    }

    showISlotScene() {
        const isView = cv.TP.getVisible();
        this.setWebActive(!isView);
    }

    setWebUrl() {
        this.web.url = cv.roomManager.getISlotnUrl();
    }

    setWebActive(isView: boolean) {
        const size = this.node.getComponent(cc.Canvas).designResolution;

        if (isView) {
            this.web.node.setPosition(cc.v2(0, 0));
        }
        else {
            this.web.node.setPosition(cc.v2(0, size.height * 2));
        }
    }
    
    adaptWebContainner() {
      if (cc.sys.isNative) {
        const yOffset = SafeAreaHelper.getUpperDangerZoneYOffset();
        const containerWidget = this.webViewContainer.getComponent(cc.Widget);

        if (yOffset > 100) {
          this.headerContainer.height = yOffset;
          this.headerContainer.getComponent(cc.Widget).updateAlignment();
          containerWidget.top = yOffset;  
        }

        containerWidget.updateAlignment();
        this.web.node.getComponent(cc.Widget).updateAlignment();
      }
    }
}
