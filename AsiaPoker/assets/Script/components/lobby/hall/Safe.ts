import cv from '../../lobby/cv';
import ListView from '../../../common/tools/ListView';
import ModifyPassword from './ModifyPassword';
import SecurityBoxRecordDateItem from './SecurityBoxRecordDateItem';
import SecurityBoxRecordItem from './SecurityBoxRecordItem';
import HallScene from './HallScene';

enum eSafeListType {
    SAFE_TYPE_NONE = 0,
    SAFE_TYPE_DEPOSIT,
    SAFE_TYPE_TAKEOUT,
    SAFE_TYPE_DETAIL
}

export enum safeType {
    GOLD = 0,
    USDT
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class Safe extends cc.Component {
    @property(cc.Button) gold_btn: cc.Button = null;

    @property(cc.Button) usdt_btn: cc.Button = null;

    @property(cc.Label) gold_lab: cc.Label = null;

    @property(cc.Label) usdt_lab: cc.Label = null;

    @property(cc.Node) goldBackground: cc.Node = null;

    @property(cc.Node) usdtBackground: cc.Node = null;

    @property(cc.Node) goldGroup: cc.Node = null;

    @property(cc.Node) usdtGroup: cc.Node = null;

    @property(cc.Sprite) deposit_img_gold: cc.Sprite = null;

    @property(cc.Sprite) deposit_img_usdt: cc.Sprite = null;

    @property(cc.Sprite) inputIconGold: cc.Sprite = null;

    @property(cc.Sprite) inputIconUsdt: cc.Sprite = null;

    @property(cc.Button) helpButton: cc.Button = null;

    @property(cc.Node) helpDetail: cc.Node = null;

    @property(cc.Button) helpDetailCloseButton: cc.Button = null;

    @property(cc.Node) bg_twoPsd: cc.Node = null;

    @property(cc.Node) withdrawBackground: cc.Node = null;

    @property(cc.Button) deposit_btn: cc.Button = null;

    @property(cc.Button) takeout_btn: cc.Button = null;

    @property(cc.Button) detail_btn: cc.Button = null;

    @property(cc.Node) deposit_panel: cc.Node = null;

    @property(cc.Node) detail_panel: cc.Node = null;

    @property(cc.Node) depositbg: cc.Node = null;

    @property(cc.Node) detailbg: cc.Node = null;

    @property(cc.Node) takeoutbg: cc.Node = null;

    @property(cc.Button) threeCloseBtn: cc.Button = null;

    @property(cc.Node) accountgold_text: cc.Node = null;

    @property(cc.Node) textCoinAmount: cc.Node = null;

    @property(cc.Node) textUSDTAmount: cc.Node = null;

    @property(cc.Label) deposit_txt: cc.Label = null;

    @property(cc.Node) deposit_text: cc.Node = null;

    @property(cc.Button) ok_btn1: cc.Button = null;

    @property(cc.Node) deposit_slider: cc.Node = null;

    @property(cc.Sprite) deposit_progress: cc.Sprite = null;

    @property(cc.EditBox) deposit_editbox: cc.EditBox = null;

    @property(cc.Sprite) editoBoxGoldIcon: cc.Sprite = null;

    @property(cc.Sprite) editoBoxUSDTIcon: cc.Sprite = null;

    @property(cc.Button) editMaxButton: cc.Button = null;

    @property(cc.EditBox) pwd_editbox: cc.EditBox = null;

    @property(cc.Node) des_txt: cc.Node = null;

    @property(cc.Node) hyperlink_text: cc.Node = null;

    @property(cc.Button) ok3_btn: cc.Button = null;

    @property(ListView) listView: ListView = null;

    @property(cc.Label) title1_txt: cc.Label = null;

    @property(cc.Label) title_1: cc.Label = null;

    @property(cc.Label) title_2: cc.Label = null;

    @property(cc.Label) title_3: cc.Label = null;

    @property(cc.Label) box_hold: cc.Label = null;

    @property(cc.Label) box_txt: cc.Label = null;

    @property(cc.Button) secondPasswordFoldButton: cc.Button = null;

    @property(cc.Button) secondPasswordShowHideButton: cc.Button = null;

    @property(cc.Sprite) secondPasswordShowIcon: cc.Sprite = null;

    @property(cc.Sprite) secondPasswordHideIcon: cc.Sprite = null;

    @property(cc.Node) secondPasswordSectionHint: cc.Node = null;

    @property(cc.Node) successfulNotification: cc.Node = null;

    @property(cc.Label) successfulNotificationLabel: cc.Label = null;

    @property(cc.Node) failurefulNotification: cc.Node = null;

    @property(cc.Label) failurefulNotificationLabel: cc.Label = null;

    @property(cc.RichText) instruct_rich: cc.RichText[] = [];

    @property(cc.Label) instruct_lab: cc.Label[] = [];

    @property(cc.Button) ExBtn: cc.Button = null;

    @property(cc.Node) explainNode: cc.Node = null;

    @property(cc.Sprite) ExContentBg: cc.Sprite = null;

    @property(cc.Label) ExContent: cc.Label = null;

    @property(cc.Node) backBtn: cc.Node = null;

    @property(cc.Node) wholeLayout: cc.Node = null;

    _closeCallback: Function = null;

    once: boolean = true;

    @property(cc.Prefab) prefab_modifyPW: cc.Prefab = null; // 二级密码预制件

    @property(cc.Node) safe: cc.Node = null;

    @property(cc.Layout) safeLayout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) top: cc.Node = null;

    @property(cc.Node) layout: cc.Node = null;

    @property(cc.Layout) layoutLayout: cc.Layout = null;

    @property(cc.Node) sectionTitle: cc.Node = null;

    @property(cc.Node) sectionTabs: cc.Node = null;

    @property(cc.Node) sectionTabsContainer: cc.Node = null;

    @property(cc.Layout) sectionTabsContainerLayout: cc.Layout = null;

    @property(cc.Node) labTitleParent: cc.Node = null;

    @property(cc.Node) sectionSectionTabs: cc.Node = null;

    @property(cc.Node) sectionSectionTabsContainer: cc.Node = null;

    @property(cc.Node) sectionSectionAmount: cc.Node = null;

    @property(cc.Node) sectionSectionActionParent: cc.Node = null;

    private _inst_modifyPW: cc.Node = null;

    private _viewType: eSafeListType = eSafeListType.SAFE_TYPE_DEPOSIT;

    private _selectType: number = safeType.GOLD;

    private _instructHeight: number = 0;

    private _btWidth: number = 0;

    private _showSecondPassword = false;

    private _recordButtonCooldown = false;

    private _queuedResponsedLists = {
        count: 0,
        gold: [],
        usdt: []
    };

    private _sliderWidth = 894;

    protected onLoad(): void {
        this._instructHeight = this.helpDetail.height;

        //cv.resMgr.adaptWidget(this.node, true);


        this.gold_btn.node.on(
            'click',
            () => {
                cv.AudioMgr.playButtonSound('tab');

                if (safeType.GOLD == this._selectType) return;

                this.changeSelectTypeView(safeType.GOLD);
            },
            this
        );

        this.usdt_btn.node.on(
            'click',
            () => {
                cv.AudioMgr.playButtonSound('tab');

                if (safeType.USDT == this._selectType) return;

                this.changeSelectTypeView(safeType.USDT);
            },
            this
        );

        this.deposit_btn.node.on(
            'click',
            () => {
                this._recordButtonCooldown = false;

                this.unschedule(this.restoreRecordButtonCooldown);

                this.onClickSelected(1);
            },
            this
        );

        this.takeout_btn.node.on(
            'click',
            () => {
                this._recordButtonCooldown = false;

                this.unschedule(this.restoreRecordButtonCooldown);

                this.onClickSelected(2);
            },
            this
        );

        this.detail_btn.node.on(
            'click',
            () => {
                if (!this._recordButtonCooldown) {
                    this.onClickSelected(3);

                    this.retrieveRecord();

                    this._recordButtonCooldown = true;
                } else {
                    cv.AudioMgr.playButtonSound('tab');
                }
            },
            this
        );

        this.threeCloseBtn.node.on(
            'click',
            () => {
                cv.AudioMgr.playButtonSound('back_button');

                cv.action.showAction(
                    this.node,
                    cv.action.eMoveActionDir.EMAD_TO_RIGHT,
                    cv.action.eMoveActionType.EMAT_FADE_OUT,
                    cv.action.delay_type.NORMAL,
                    (target: cc.Node, actIO: number): void => {
                        // Just before the tween starts, hide back button
                        this.helpDetail.active = false;

                        this.withdrawBackground.active = false;

                        this._showSecondPassword = false;

                        this.pwd_editbox.getComponent(cc.EditBox).string = '';

                        this.restoreRecordButtonCooldown();

                        this.hideBackBtn();
                    },
                    (target: cc.Node, actIO: number): void => {
                        this.closeView();
                    }
                );
            },
            this
        );

        this.editMaxButton.node.on(
            'click',
            () => {
                cv.AudioMgr.playButtonSound('button_click');

                let slider = this.deposit_slider.getComponent(cc.Slider);

                slider.progress = 1;

                let tempData1: number = this.getSliderData();

                if (tempData1 === 0) {
                    slider.progress = 0;
                }

                this.deposit_progress.node.width = slider.progress * this._sliderWidth;

                let xx = tempData1 * slider.progress;

                let bb = Math.floor(cv.StringTools.clientGoldByServer(xx));

                if (slider.progress == 1) {
                    bb = cv.StringTools.clientGoldByServer(tempData1);
                }

                this.deposit_editbox.string = cv.StringTools.numberToShowString(bb);
            },
            this
        );

        this.ok_btn1.node.on(
            'click',
            () => {
                cv.AudioMgr.playButtonSound('button_click');

                if (this._viewType == eSafeListType.SAFE_TYPE_TAKEOUT) {
                    this.takeoutClick();

                    return;
                }

                if (this.deposit_editbox.string == '') {
                    this.showNotificationBar(cv.config.getStringData('Safe_input_account'), false);

                    return;
                }

                let depostNum = cv.StringTools.showStringToNumber(this.deposit_editbox.string);

                var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;

                if (!reg.test(this.deposit_editbox.string)) {
                    this.showNotificationBar(cv.config.getStringData('Safe_correct_number'), false);

                    return;
                }

                if (depostNum <= cv.StringTools.clientGoldByServer(this.getGoldOrUsdtData())) {
                    if (depostNum > 0) {
                        cv.worldNet.RequestDeposit(depostNum, this._selectType);
                    } else {
                        this.showNotificationBar(cv.config.getStringData('Safe_input_account'), false);
                    }
                } else {
                    let tempStr = this._selectType == safeType.GOLD ? '' : '_usdt';

                    this.showNotificationBar(cv.config.getStringData('Safe_account_not_enough_money' + tempStr), false);
                }
            },
            this
        );

        this.ok3_btn.node.on(
            'click',
            () => {
                cv.AudioMgr.playButtonSound('button_click');

                //取出请求
                let pwd = this.pwd_editbox.getComponent(cc.EditBox).string;

                if (pwd.length == 0) {
                    this.showNotificationBar(cv.config.getStringData('ErrorCode2'), false);

                    return;
                }

                cv.worldNet.RequestTakeOut(
                    cv.StringTools.showStringToNumber(this.deposit_editbox.string),
                    pwd,
                    this._selectType
                );
            },
            this
        );

        this.deposit_editbox.node.on(
            'text-changed',
            () => {
                this.deposit_editbox.string = this.deposit_editbox.string;

                let slider = this.deposit_slider.getComponent(cc.Slider);

                if (slider == null || this.deposit_progress == null) {
                    return;
                }

                if (this.deposit_editbox.string === '') {
                    slider.progress = 0;

                    this.deposit_progress.node.width = slider.progress * this._sliderWidth;
                }

                let currentAmount = 0;

                if (this._viewType === eSafeListType.SAFE_TYPE_DEPOSIT) {
                    if (safeType.GOLD === this._selectType) {
                        currentAmount = cv.dataHandler.getUserData().u32Chips;
                    } else if (safeType.USDT === this._selectType) {
                        currentAmount = cv.dataHandler.getUserData().usdt;
                    }
                } else if (this._viewType === eSafeListType.SAFE_TYPE_TAKEOUT) {
                    if (safeType.GOLD === this._selectType) {
                        currentAmount = cv.dataHandler.getUserData().u32Deposit_gold;
                    } else if (safeType.USDT === this._selectType) {
                        currentAmount = cv.dataHandler.getUserData().deposit_usdt;
                    }
                }

                let editBoxNumber = parseFloat(this.deposit_editbox.string);

                if (isNaN(editBoxNumber)) {
                    slider.progress = 0;

                    this.deposit_progress.node.width = slider.progress * this._sliderWidth;
                } else {
                    editBoxNumber = editBoxNumber * 100;

                    let ratio = 0;

                    if (currentAmount > 0) {
                        ratio = editBoxNumber / currentAmount;

                        if (ratio > 1.0) {
                            ratio = 1.0;

                            if (this._viewType === eSafeListType.SAFE_TYPE_DEPOSIT) {
                                if (safeType.GOLD === this._selectType) {
                                    this.deposit_editbox.string = cv.StringTools.numToFloatString(
                                        cv.dataHandler.getUserData().u32Chips
                                    );
                                } else if (safeType.USDT === this._selectType) {
                                    this.deposit_editbox.string = cv.StringTools.numToFloatString(
                                        cv.dataHandler.getUserData().usdt
                                    );
                                }
                            } else if (this._viewType === eSafeListType.SAFE_TYPE_TAKEOUT) {
                                if (safeType.GOLD === this._selectType) {
                                    this.deposit_editbox.string = cv.StringTools.numToFloatString(
                                        cv.dataHandler.getUserData().u32Deposit_gold
                                    );
                                } else if (safeType.USDT === this._selectType) {
                                    this.deposit_editbox.string = cv.StringTools.numToFloatString(
                                        cv.dataHandler.getUserData().deposit_usdt
                                    );
                                }
                            }
                        }

                        slider.progress = ratio;

                        this.deposit_progress.node.width = slider.progress * this._sliderWidth;
                    } else {
                        this.deposit_editbox.string = '';

                        slider.progress = 0;

                        this.deposit_progress.node.width = slider.progress * this._sliderWidth;
                    }
                }
            },
            this
        );

        this.secondPasswordFoldButton.node.on(
            'click',
            () => {
                console.log(`viewType: ${this._viewType}`);

                this.setViewType(eSafeListType.SAFE_TYPE_TAKEOUT);
            },
            this
        );

        this.secondPasswordShowHideButton.node.on(
            'click',
            () => {
                this._showSecondPassword = !this._showSecondPassword;

                this.updateShowHideIcon();
            },
            this
        );

        this.helpButton.node.on(
            'click',
            () => {
                cv.AudioMgr.playButtonSound('button_click');

                this.withdrawBackground.active = true;

                this.initInstructView();

                this.helpDetail.active = true;
            },
            this
        );

        this.helpDetailCloseButton.node.on(
            'click',
            () => {
                this.withdrawBackground.active = false;

                this.helpDetail.active = false;
            },
            this
        );

        this.onChangeLanguage();

        this.updateSlider();

        this.addRegister();

        this.setSafeArea();
    }

    private updateShowHideIcon() {
        if (this._showSecondPassword) {
            this.secondPasswordHideIcon.node.active = false;

            this.secondPasswordShowIcon.node.active = true;
        } else {
            this.secondPasswordHideIcon.node.active = true;

            this.secondPasswordShowIcon.node.active = false;
        }

        if (this._showSecondPassword) {
            this.pwd_editbox.inputFlag = cc.EditBox.InputFlag.DEFAULT;
        } else {
            this.pwd_editbox.inputFlag = cc.EditBox.InputFlag.PASSWORD;
        }
    }

    start() {
        this._sliderWidth = this.deposit_slider.width;
        
        if (this.successfulNotification) {
            this.successfulNotification.active = false;
        }

        if (this.failurefulNotification) {
            this.failurefulNotification.active = false;
        }

        if (this.textCoinAmount) {
            const labelComp = this.textCoinAmount.getComponent(cc.Label);

            labelComp.string = cv.config.getStringData('Safe_accountgold');
        }
        if (this.textUSDTAmount) {
            const labelComp = this.textUSDTAmount.getComponent(cc.Label);

            labelComp.string = cv.config.getStringData('Safe_accountUsdt');
        }

        this.bg_twoPsd.active = false;

        this.withdrawBackground.active = false;

        this._showSecondPassword = false;

        this.pwd_editbox.inputFlag = cc.EditBox.InputFlag.PASSWORD;
        this.updateShowHideIcon();

        this.helpDetail.active = false;

        // Need to force update usdt and gold amount
        this.changeSelectTypeView(safeType.USDT);

        this.changeSelectTypeView(safeType.GOLD);

        this.listView.bindScrollEventTarget(this);

        this.listView.init(this.bindCallFunc.bind(this), this.getItemType.bind(this));
    }

    private updateGroupPos(amount: string, baseOn: cc.Node, whichGroup: cc.Node, whichAmount: cc.Node) {
        const labelComp = whichAmount.getComponent(cc.Label);

        let amountSize = cv.resMgr.getLabelStringSize(labelComp, amount);

        const widgetComp = whichGroup.getComponent(cc.Widget);

        if (widgetComp) {
            widgetComp.enabled = false;
        }

        const halfSize = amountSize.width / 2 + 20;

        console.log(
            `updateGroupPos - baseOn: ${baseOn} whichGroup: ${whichGroup.name} - amountSize: ${amountSize} halfSize: ${halfSize}`
        );

        whichGroup.setPosition(baseOn.position.x - halfSize, baseOn.position.y);
    }

    public bindCallFunc(node: cc.Node, info, i) {
        console.log(`bindCallFunc - i: ${i} ${info}`);

        if (info.type == 0) {
            node.getComponent(SecurityBoxRecordItem).updateItemData(i, info.data);
        } else if (info.type == 1) {
            node.getComponent(SecurityBoxRecordDateItem).updateItemData(i, info.data);
        }
    }

    public getItemType(data, index) {
        return data.type;
    }

    private reloadView(dataArray: any[], resetPos?: boolean): void {
        if (resetPos) {
            if (this.listView.sv) {
                this.listView.sv.scrollToTop(0);
            }
        }

        dataArray.forEach((el) => {
            el.dayEpoch = cv.config.getTimeWithTimeZone(el.time * 1000).setHours(0, 0, 0, 0);
        });

        let dataList = [];

        let daysAdded: number[] = [];

        for (let index: number = 0; index < dataArray.length; index++) {
            if (!daysAdded.includes(dataArray[index].dayEpoch)) {
                dataList.push({ type: 1, data: dataArray[index] });

                daysAdded.push(dataArray[index].dayEpoch);
            }

            dataList.push({ type: 0, data: dataArray[index] });
        }

        if (dataList.length > 0) {
            this.listView.notifyDataSetChanged(dataList);
        }
    }

    public chk(num: string) {
        //var patrn = /^\d+(\.\d+)?$/;
        //验证是数字或者两位小数，或者一位小数，整数
        var patrn = /^\d+(\.\d{0,2})?$/;

        var result = true;

        if (!patrn.exec(num)) {
            if (num.length == 0) {
                return;
            }

            this.showNotificationBar(cv.config.getStringData('Safe_correct_number'), false);

            result = false;
        }

        return result;
    }

    public limitDepositXiaoshu(): void {
        if (isNaN(Number(this.deposit_editbox.string))) {
            this.showNotificationBar(cv.config.getStringData('Safe_correct_number'), false);

            return;
        }

        //判断小数位
        let num = Number(this.deposit_editbox.string);

        let digits = num.toString().split('.');

        if (digits.length > 1) {
            if (digits[1].length == 1) {
                this.setMid(this.deposit_txt.string, num.toString());

                return;
            }

            this.setMid(this.deposit_txt.string, num.toFixed(3).slice(0, -1));

            return;
        }

        if (!this.chk(num.toString())) return;
    }

    public limitTakeOutXiaoshu(): void {
        if (isNaN(Number(this.deposit_editbox.string))) {
            this.showNotificationBar(cv.config.getStringData('Safe_correct_number'), false);

            return;
        }

        let num = Number(this.deposit_editbox.string);

        let digits = num.toString().split('.');

        if (digits.length > 1) {
            if (digits[1].length == 1) {
                this.setMid(this.deposit_txt.string, num.toString());

                return;
            }

            this.setMid(this.deposit_txt.string, num.toFixed(3).slice(0, -1));

            return;
        }

        if (!this.chk(num.toString())) return;
    }

    public onChangeLanguage(): void {
        let desSize = cv.resMgr.getLabelStringSize(
            this.des_txt.getComponent(cc.Label),
            cv.config.getStringData('SecondaryPassword_des_text')
        );

        this.des_txt.getComponent(cc.Label).string = cv.config.getStringData('SecondaryPassword_des_text');

        let linkSize = cv.resMgr.getLabelStringSize(
            this.hyperlink_text.getComponent(cc.Label),
            cv.config.getStringData('SecondaryPassword_hyperlink_text')
        );

        this.hyperlink_text.getComponent(cc.Label).string = cv.config.getStringData('SecondaryPassword_hyperlink_text');

        let tempW = this.secondPasswordSectionHint.width - (desSize.width + linkSize.width);

        let diff = 0.5 * tempW;

        let offset = -(this.withdrawBackground.width * 0.5);

        this.des_txt.setPosition(offset + 60.0 + diff, this.des_txt.y);

        this.hyperlink_text.setPosition(offset + 60.0 + diff + desSize.width, this.hyperlink_text.y);

        this.pwd_editbox.getComponent(cc.EditBox).placeholderLabel.string = cv.config.getStringData(
            'SecondaryPassword_editBox_bg_editBox_text'
        );

        // Change this from confirm to deposit or withdraw
        if (this.ok_btn1) {
            let labelComp = this.ok_btn1.getComponentInChildren(cc.Label);

            if (labelComp) {
                labelComp.string = cv.config.getStringData('Safe_deposit');
            }
        }

        this.title_1.getComponent(cc.Label).string = cv.config.getStringData('Safe_deposit');

        this.title_2.getComponent(cc.Label).string = cv.config.getStringData('Safe_takeout');

        this.title_3.getComponent(cc.Label).string = cv.config.getStringData('Safe_detail');
    }

    private gotoSecondaryPwd(): void {
        cv.AudioMgr.playButtonSound('button_click');

        //跳到二级密码设置界面
        this._inst_modifyPW = cc.instantiate(this.prefab_modifyPW);

        let modifyPW: ModifyPassword = this._inst_modifyPW.getComponent(ModifyPassword);

        //先将this._inst_modifyPW的size根据当前父节点适配，ModifyPassword类的onload适配是异步执行，下方init()时还未生效
        let scene = cc.director.getScene();

        scene.addChild(this._inst_modifyPW, cv.Enum.ZORDER_TYPE.ZORDER_7);

        cv.resMgr.adaptWidget(this._inst_modifyPW);

        modifyPW.init(false, () => {
            this._inst_modifyPW.removeFromParent(true);

            this._inst_modifyPW.destroy();
        });

        this._inst_modifyPW.runAction(cc.moveTo(0, cc.v2(cv.config.WIDTH * 0.5, 0)));

        this._inst_modifyPW.name = 'ModifyPsd';
    }

    private updateSlider(): void {
        let slider = this.deposit_slider.getComponent(cc.Slider);

        if (slider == null || this.deposit_progress == null) {
            return;
        }

        let tempData: number = this.getSliderData();

        slider.progress = 0;

        this.deposit_progress.node.width = 0;

        if (tempData == 0) {
            slider.enabled = false;
        } else {
            slider.enabled = true;
        }

        slider.node.targetOff(this);

        slider.node.on(
            'slide',
            (event) => {
                let tempData1: number = this.getSliderData();

                this.deposit_progress.node.width = slider.progress * this._sliderWidth;

                let xx = tempData1 * slider.progress;

                let bb = Math.floor(cv.StringTools.clientGoldByServer(xx));

                if (slider.progress == 1) {
                    bb = cv.StringTools.clientGoldByServer(tempData1);
                }

                if (slider.progress == 0) {
                    this.deposit_editbox.string = '';
                } else {
                    this.deposit_editbox.string = cv.StringTools.numberToShowString(bb);
                }
            },
            this
        );
    }

    private onSafeDetailList(): void {
        // Guess that need to take out SafeDetailList and process for date and actual record
        let arr = cv.dataHandler.getUserData().SafeDetailList;

        let isView = true;

        if (this._queuedResponsedLists.count === 0) {
            this._queuedResponsedLists.gold = arr;
        } else if (this._queuedResponsedLists.count === 1) {
            this._queuedResponsedLists.usdt = arr;
        }

        this._queuedResponsedLists.count += 1;

        if (this._queuedResponsedLists.count == 2) {
            // Combine both before sending to list
            let recordItems = [];

            for (let i = 0; i < this._queuedResponsedLists.gold.length; ++i) {
                const item = this._queuedResponsedLists.gold[i];

                let recordItem = {
                    amount: item.amount,
                    balance: item.balance,
                    // This type indicates withdraw or deposit
                    type: item.type,
                    time: item.time,
                    kind: 0
                };

                recordItems.push(recordItem);
            }

            for (let i = 0; i < this._queuedResponsedLists.usdt.length; ++i) {
                const item = this._queuedResponsedLists.usdt[i];
                let recordItem = {
                    amount: item.amount,
                    balance: item.balance,
                    // This type indicates withdraw or deposit
                    type: item.type,
                    time: item.time,
                    kind: 1
                };

                recordItems.push(recordItem);
            }

            recordItems.sort(function (a, b) {
                return b.time - a.time;
            });

            this.reloadView(recordItems, isView);
        }
    }

    private onUpdateDepositAndGold(): void {
        let tempData = this.getSliderData();

        this.accountgold_text.getComponent(cc.Label).string = cv.StringTools.numToFloatString(tempData);

        if (this.textCoinAmount) {
            let label = this.textCoinAmount.getComponent(cc.Label);

            if (label) {
                label.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().u32Deposit_gold);

                this.updateGroupPos(label.string, this.goldBackground, this.goldGroup, this.textCoinAmount);
            }
        }

        if (this.textUSDTAmount) {
            let label = this.textUSDTAmount.getComponent(cc.Label);

            if (label) {
                label.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().deposit_usdt);

                this.updateGroupPos(label.string, this.usdtBackground, this.usdtGroup, this.textUSDTAmount);
            }
        }

        // The successful notication is showing else location
        this.showNotificationBar(cv.config.getStringData('Safe_deposit_succeed_no_type'), true);

        //判断剩余金币是否可以滑动 重置滑动条
        let slider1 = this.deposit_slider.getComponent(cc.Slider);

        slider1.progress = 0;

        this.deposit_progress.node.width = 0;

        // If no more amount to scroll, reset edit box as well
        // this.deposit_editbox.string = "0";
        this.deposit_editbox.string = '';

        if (tempData == 0) {
            slider1.enabled = false;
        } else {
            slider1.enabled = true;
        }

        this.onClickSelected(3);

        this._recordButtonCooldown = false;

        this.retrieveRecord();
    }

    private showNotificationBar(str: string, isSucc: boolean) {
        if (isSucc) {
            this.successfulNotification.active = true;

            this.successfulNotification.stopAllActions();

            this.successfulNotificationLabel.string = str;

            this.successfulNotification.runAction(
                cc.sequence(
                    cc.fadeIn(0.1),
                    cc.delayTime(0.7),
                    cc.fadeOut(0.3),
                    cc.callFunc(() => {
                        this.successfulNotification.active = false;
                    }, this)
                )
            );
        } else {
            this.failurefulNotification.active = true;

            this.failurefulNotification.stopAllActions();

            this.failurefulNotificationLabel.string = str;

            this.failurefulNotification.runAction(
                cc.sequence(
                    cc.fadeIn(0.1),
                    cc.delayTime(0.7),
                    cc.fadeOut(0.3),
                    cc.callFunc(() => {
                        this.failurefulNotification.active = false;
                    }, this)
                )
            );
        }
    }

    private onUpdateTakeOutBalance(): void {
        let tempData = this.getGoldOrUsdtData();

        let DepositData = this.getDepositData();

        if (this.textCoinAmount) {
            let label = this.textCoinAmount.getComponent(cc.Label);

            if (label) {
                label.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().u32Deposit_gold);

                this.updateGroupPos(label.string, this.goldBackground, this.goldGroup, this.textCoinAmount);
            }
        }

        if (this.textUSDTAmount) {
            let label = this.textUSDTAmount.getComponent(cc.Label);

            if (label) {
                label.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().deposit_usdt);

                this.updateGroupPos(label.string, this.usdtBackground, this.usdtGroup, this.textUSDTAmount);
            }
        }

        // The successful notication is showing else location
        this.showNotificationBar(cv.config.getStringData('Safe_takeout_succeed_no_type'), true);

        //成功后调到取出界面
        this.pwd_editbox.getComponent(cc.EditBox).string = '';

        this.bg_twoPsd.active = false;

        this.withdrawBackground.active = false;

        //判断剩余金币是否可以滑动
        let slider2 = this.deposit_slider.getComponent(cc.Slider);

        slider2.progress = 0;

        this.deposit_progress.node.width = 0;

        // If no more amount to scroll, reset edit box as well
        // this.deposit_editbox.string = "0";
        this.deposit_editbox.string = '';

        if (DepositData == 0) {
            slider2.enabled = false;
        } else {
            slider2.enabled = true;
        }

        this.onClickSelected(3);

        this._recordButtonCooldown = false;

        this.retrieveRecord();
    }

    private addRegister(): void {
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.onChangeLanguage.bind(this), this.node);

        cv.MessageCenter.register('SafeDetailList', this.onSafeDetailList.bind(this), this.node);

        cv.MessageCenter.register('update_deposit_and_gold', this.onUpdateDepositAndGold.bind(this), this.node);

        cv.MessageCenter.register('update_takeout_balance', this.onUpdateTakeOutBalance.bind(this), this.node);

        cv.MessageCenter.register('update_slider_state', this.updateSlider.bind(this), this.node);

        this.pwd_editbox.node.on('editing-did-began', this.secondPasswordEditingDidBegan, this);

        this.pwd_editbox.node.on('editing-did-ended', this.secondPasswordEditingDidEnded, this);
    }

    private removeRegister(): void {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);

        cv.MessageCenter.unregister('SafeDetailList', this.node);

        cv.MessageCenter.unregister('update_deposit_and_gold', this.node);

        cv.MessageCenter.unregister('update_takeout_balance', this.node);

        cv.MessageCenter.unregister('update_slider_state', this.node);
    }

    onDestroy(): void {
        this.removeRegister();
    }

    private onClickSelected(selected: number, isplaySound: boolean = true): void {
        if (isplaySound) {
            cv.AudioMgr.playButtonSound('tab');
        }

        if (selected == this._viewType) return;

        this.setViewType(selected);

        this.resetSliderAndBox();
    }

    private retrieveRecord() {
        if (this._recordButtonCooldown) {
            return;
        }

        this._queuedResponsedLists.count = 0;

        this._queuedResponsedLists.gold = [];

        this._queuedResponsedLists.usdt = [];

        // Need to request both gold and usdt but usdt request need to delay a bit
        cv.worldNet.RequestStrongboxInfo(safeType.GOLD);

        this.schedule(
            () => {
                cv.worldNet.RequestStrongboxInfo(safeType.USDT);
            },
            0.1,
            1
        );

        this.schedule(this.restoreRecordButtonCooldown, 15, 1);
    }

    private restoreRecordButtonCooldown() {
        this._recordButtonCooldown = false;
    }

    setViewType(eType: eSafeListType): void {
        this._viewType = eType;

        this.updateView();
    }

    getViewType(): eSafeListType {
        return this._viewType;
    }

    public updateView(): void {
        // Either type will need to reset
        let slider = this.deposit_slider.getComponent(cc.Slider);

        slider.progress = 0;

        this.deposit_progress.node.width = 0;

        this.deposit_editbox.string = '';

        this.bg_twoPsd.active = false;

        this.withdrawBackground.active = false;

        if (this._viewType == eSafeListType.SAFE_TYPE_DEPOSIT) {
            if (this.ok_btn1) {
                let labelComp = this.ok_btn1.getComponentInChildren(cc.Label);

                if (labelComp) {
                    labelComp.string = cv.config.getStringData('Safe_deposit');
                }
            }

            this.deposit_txt.string = cv.config.getStringData('Safe_input_account');
        } else if (this._viewType == eSafeListType.SAFE_TYPE_TAKEOUT) {
            if (this.ok_btn1) {
                let labelComp = this.ok_btn1.getComponentInChildren(cc.Label);

                if (labelComp) {
                    labelComp.string = cv.config.getStringData('Safe_takeout');
                }
            }

            this.deposit_txt.string = cv.config.getStringData('Safe_output_account');
        } else if (this._viewType == eSafeListType.SAFE_TYPE_DETAIL) {
            if (this.listView.sv) {
                this.listView.sv.scrollToTop(0.1);
            } else {
            }
        }

        switch (this._viewType) {
            case eSafeListType.SAFE_TYPE_DEPOSIT:
                {
                    this.deposit_panel.active = true;
                    this.detail_panel.active = false;
                    this.depositbg.active = true;
                    this.takeoutbg.active = false;
                    this.detailbg.active = false;
                    this.title_1.node.color = cc.color(240, 213, 170);
                    this.title_2.node.color = cc.color(255, 255, 255);
                    this.title_3.node.color = cc.color(255, 255, 255);
                }
                break;

            case eSafeListType.SAFE_TYPE_TAKEOUT:
                {
                    this.deposit_panel.active = true;
                    this.detail_panel.active = false;
                    this.depositbg.active = false;
                    this.takeoutbg.active = true;
                    this.detailbg.active = false;
                    this.title_1.node.color = cc.color(255, 255, 255);
                    this.title_2.node.color = cc.color(240, 213, 170);
                    this.title_3.node.color = cc.color(255, 255, 255);
                }
                break;

            case eSafeListType.SAFE_TYPE_DETAIL:
                {
                    this.deposit_panel.active = false;
                    this.detail_panel.active = true;
                    this.depositbg.active = false;
                    this.takeoutbg.active = false;
                    this.detailbg.active = true;
                    this.title_1.node.color = cc.color(255, 255, 255);
                    this.title_2.node.color = cc.color(255, 255, 255);
                    this.title_3.node.color = cc.color(240, 213, 170);
                }
                break;

            default:
                break;
        }
    }

    setCloseCallBack(callback: Function) {
        this._closeCallback = callback;
    }

    takeoutClick() {
        //弹出对话框
        let takeoutNum = cv.StringTools.showStringToNumber(this.deposit_editbox.string);

        if (this.deposit_editbox.string == '') {
            this.showNotificationBar(cv.config.getStringData('Safe_output_account'), false);

            return;
        }

        if (takeoutNum > cv.StringTools.clientGoldByServer(this.getDepositData())) {
            let tempStr = this._selectType == safeType.GOLD ? '' : '_usdt';

            this.showNotificationBar(cv.config.getStringData('Safe_box_not_enough_money'), false);

            return;
        }

        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;

        if (!reg.test(takeoutNum.toString())) {
            this.showNotificationBar(cv.config.getStringData('Safe_correct_number'), false);

            return;
        }

        if (takeoutNum == 0) {
            this.showNotificationBar(cv.config.getStringData('Safe_output_account'), false);

            return;
        }

        this.bg_twoPsd.active = true;

        this.withdrawBackground.active = true;

        this.updateShowHideIcon();

        this.onChangeLanguage();
    }

    initInstructView() {
        let str = '';

        switch (this._selectType) {
            case safeType.USDT:
                str = '_usdt';
                break;
        }

        let instructData: string[] = [];

        instructData.push(
            cv.config.getStringData('Safe_content_1' + str) +
                '，<color=#D0AB6E>' +
                cv.config.getStringData('Safe_title_1') +
                '</color>'
        );

        instructData.push(
            cv.config.getStringData('Safe_content_2' + str) +
                '，<color=#D0AB6E>' +
                cv.config.getStringData('Safe_title_2' + str) +
                '</color>'
        );

        instructData.push(
            cv.config.getStringData('Safe_content_3' + str) +
                '，<color=#D0AB6E>' +
                cv.config.getStringData('Safe_title_3' + str) +
                '</color>'
        );

        let totalLineNum: number = 0;

        let len = this.instruct_rich.length;

        let sizeArr: number[] = [];

        for (let i = 0; i < len; i++) {
            let currentSize = cv.resMgr.getRichTextStringSize(this.instruct_rich[i], instructData[i]);

            sizeArr.push(currentSize.height);

            if (i > 0) {
                let tempLineNum = Math.floor(sizeArr[i - 1] / 56);

                totalLineNum += tempLineNum;

                this.instruct_rich[i].node.setPosition(
                    this.instruct_rich[i].node.x,
                    this.instruct_rich[i - 1].node.y - tempLineNum * 56 - 40
                );
            }

            this.instruct_lab[i].node.setPosition(this.instruct_lab[i].node.x, this.instruct_rich[i].node.y);
        }

        totalLineNum += Math.floor(sizeArr[len - 1] / 56);

        this.helpDetail.setContentSize(cc.size(this.helpDetail.width, this._instructHeight));

        if (totalLineNum > 6) {
            this.helpDetail.setContentSize(
                cc.size(this.helpDetail.width, this.helpDetail.height + (totalLineNum - 6) * 56)
            );

            cv.resMgr.adaptWidget(this.helpDetail);
        }
    }

    changeSelectTypeView(num: number) {
        this._selectType = num;

        this.bg_twoPsd.active = false;

        this.withdrawBackground.active = false;

        // Either type will need to reset
        let slider = this.deposit_slider.getComponent(cc.Slider);

        slider.progress = 0;

        this.deposit_editbox.string = '';

        switch (this._selectType) {
            case safeType.GOLD:
                if (this.goldBackground) {
                    this.goldBackground.active = true;
                }

                if (this.usdtBackground) {
                    this.usdtBackground.active = false;
                }

                if (this.textCoinAmount) {
                    let label = this.textCoinAmount.getComponent(cc.Label);

                    if (label) {
                        label.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().u32Deposit_gold);

                        this.updateGroupPos(label.string, this.goldBackground, this.goldGroup, this.textCoinAmount);
                    }
                }

                if (this.textUSDTAmount) {
                    let label = this.textUSDTAmount.getComponent(cc.Label);

                    if (label) {
                        label.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().deposit_usdt);

                        this.updateGroupPos(label.string, this.usdtBackground, this.usdtGroup, this.textUSDTAmount);
                    }
                }

                this.deposit_img_gold.node.active = true;

                this.deposit_img_usdt.node.active = false;

                if (this.inputIconGold) {
                    this.inputIconGold.node.active = true;
                }

                if (this.inputIconUsdt) {
                    this.inputIconUsdt.node.active = false;
                }
                break;

            case safeType.USDT:
                if (this.goldBackground) {
                    this.goldBackground.active = false;
                }

                if (this.usdtBackground) {
                    this.usdtBackground.active = true;
                }

                if (this.textCoinAmount) {
                    let label = this.textCoinAmount.getComponent(cc.Label);

                    if (label) {
                        label.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().u32Deposit_gold);

                        this.updateGroupPos(label.string, this.goldBackground, this.goldGroup, this.textCoinAmount);
                    }
                }

                if (this.textUSDTAmount) {
                    let label = this.textUSDTAmount.getComponent(cc.Label);

                    if (label) {
                        label.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().deposit_usdt);

                        this.updateGroupPos(label.string, this.usdtBackground, this.usdtGroup, this.textUSDTAmount);
                    }
                }

                this.deposit_img_gold.node.active = false;

                this.deposit_img_usdt.node.active = true;

                if (this.inputIconGold) {
                    this.inputIconGold.node.active = false;
                }

                if (this.inputIconUsdt) {
                    this.inputIconUsdt.node.active = true;
                }
                break;
        }

        this.setViewType(this._viewType);

        this.resetSliderAndBox();
    }

    getGoldOrUsdtData(): number {
        let result: number = 0;

        switch (this._selectType) {
            case safeType.GOLD:
                result = cv.dataHandler.getUserData().u32Chips;
                break;

            case safeType.USDT:
                result = cv.dataHandler.getUserData().usdt;
                break;
        }
        return result;
    }

    getDepositData(): number {
        let result: number = 0;

        switch (this._selectType) {
            case safeType.GOLD:
                result = cv.dataHandler.getUserData().u32Deposit_gold;
                break;

            case safeType.USDT:
                result = cv.dataHandler.getUserData().deposit_usdt;
                break;
        }

        return result;
    }

    resetSliderAndBox() {
        let tempData = this.getSliderData();

        //判断剩余金币是否可以滑动 重置滑动条
        let slider1 = this.deposit_slider.getComponent(cc.Slider);

        slider1.progress = 0;

        this.deposit_progress.node.width = 0;

        if (tempData == 0) {
            slider1.enabled = false;
        } else {
            slider1.enabled = true;
        }
    }

    getSliderData(): number {
        if (this._viewType == eSafeListType.SAFE_TYPE_DEPOSIT) {
            return this.getGoldOrUsdtData();
        } else {
            return this.getDepositData();
        }
    }

    reset() {
        this.node.active = true;

        this.bg_twoPsd.active = false;

        this.withdrawBackground.active = false;

        this.helpDetail.active = false;

        this._viewType = eSafeListType.SAFE_TYPE_DEPOSIT;

        this.changeSelectTypeView(safeType.GOLD);
    }

    setMid(txtStr: string, boxStr: string) {
        let txtW = cv.resMgr.getLabelStringSize(this.deposit_txt, txtStr).width;

        let boxW = 0;

        if (boxStr == '') {
            boxW = cv.resMgr.getLabelStringSize(this.box_hold, boxStr).width;
        } else {
            boxW = cv.resMgr.getLabelStringSize(this.box_txt, boxStr).width;
        }

        let posW = (txtW + boxW + 4) * 0.5;

        this.deposit_txt.node.setPosition(-posW, this.deposit_txt.node.y);

        this.deposit_editbox.node.setPosition(posW - boxW, this.deposit_editbox.node.y);

        this.deposit_editbox.string = boxStr;
    }

    forceCloseView():void{
        this.helpDetail.active = false;
        this.withdrawBackground.active = false;
        this._showSecondPassword = false;
        this.pwd_editbox.getComponent(cc.EditBox).string = '';
        this.restoreRecordButtonCooldown();
        this.hideBackBtn();
        this.closeView();
        this.node.active = false;
    }

    closeView() {
        if (this._closeCallback) {
            this._closeCallback();
        }
    }

    secondPasswordEditingDidBegan() {
        if (cc.sys.isNative) {
            const widgetComp = this.bg_twoPsd.getComponent(cc.Widget);

            if (widgetComp) {
                widgetComp.isAbsoluteBottom = false;

                widgetComp.bottom = 0.4;

                widgetComp.updateAlignment();
            }
        }
    }

    secondPasswordEditingDidEnded() {
        if (cc.sys.isNative) {
            const widgetComp = this.bg_twoPsd.getComponent(cc.Widget);

            if (widgetComp) {
                widgetComp.isAbsoluteBottom = false;

                widgetComp.bottom = 0;

                widgetComp.updateAlignment();
            }
        }
    }

    showBackBtn() {
        this.backBtn.opacity = 255;
    }

    hideBackBtn() {
        this.backBtn.opacity = 0;
    }

    public showingView() {
        // While showing view, reset by calling deposit tab but makes no sound
        this.showBackBtn();

        this.onClickSelected(1, false);

        if (safeType.GOLD == this._selectType) return;

        this.changeSelectTypeView(safeType.GOLD);
    }

    setSafeArea() {
        cv.resMgr.adaptWidget(this.node, true);

        let hallScene = cc.director.getScene().getComponentInChildren(HallScene);

        this.top.height = hallScene.getTopHeight();

        let offsetY = cv.SafeAreaWithDifferentDevices.getSafeAreaHall();

        this.safearea.height = offsetY;

        let layoutHeight = this.safe.height - offsetY - this.top.height;

        this.layout.height = layoutHeight;

        this.safeLayout.updateLayout();

        let sectionTabsContainerHeight = this.layout.height - this.sectionTitle.height - this.sectionTabs.height;

        this.sectionTabsContainer.height = sectionTabsContainerHeight;

        this.layoutLayout.updateLayout();

        let sectionSectionActionParentHeight =
            this.sectionTabsContainer.height -
            this.labTitleParent.height -
            this.sectionSectionTabs.height -
            this.sectionSectionTabsContainer.height -
            this.sectionSectionAmount.height;

        this.sectionSectionActionParent.height = sectionSectionActionParentHeight;

        this.sectionTabsContainerLayout.updateLayout();

        cv.resMgr.adaptWidget(this.node, true);
    }
}
