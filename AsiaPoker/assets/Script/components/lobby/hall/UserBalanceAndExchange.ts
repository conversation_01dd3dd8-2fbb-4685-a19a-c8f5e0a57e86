// Learn TypeScript:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html

import { CurrencyType } from "../../../common/tools/Enum";
import cv from "../cv";

const { ccclass} = cc._decorator;

@ccclass
export default class UserBalanceAndExchange extends cc.Object{

    private static _instance: UserBalanceAndExchange = null;

    public static getInstance(): UserBalanceAndExchange {
        if (!this._instance) {
            this._instance = new UserBalanceAndExchange();
        }
        return this._instance;
    }

    private constructor() {
        super();
        cv.MessageCenter.register("onGetScalerQuoteResponse", this.requestExRateResponse.bind(this), this);
    }

    protected usdt_2_coin_ex_val: number = 0;

    protected coin_2_usdt_ex_val: number = 0;

    
    requestLatestExchange () {
        const hlType: number = cv.GameDataManager.tRoomData.currency === CurrencyType.USDT ? 1 : 0;
        cv.worldNet.GetScalerQuoteRequest(hlType);
    }

    onDestroy() {
        cv.MessageCenter.unregister("onGetScalerQuoteResponse", this);
    }

    getUSDT2CoinExRate(): number {
        if (isNaN(this.usdt_2_coin_ex_val) || this.usdt_2_coin_ex_val <= 0) {
            return 0;
        }

        return this.usdt_2_coin_ex_val;
    }

    getCoin2USDTExRate(): number {
        if (isNaN(this.coin_2_usdt_ex_val) || this.coin_2_usdt_ex_val <= 0) {
            return 0;
        }

        return this.coin_2_usdt_ex_val;
    }

    requestExRateResponse(msg: any): void {
        const rateNum = Number(msg.rate);

        if (isNaN(rateNum) || rateNum <= 0 || (msg.op_type !== 1 && msg.op_type !== 0)) {
            return;
        }

        if (msg.op_type === 0) {
            this.coin_2_usdt_ex_val = rateNum;
        }
        else {
            this.usdt_2_coin_ex_val = rateNum;
        }
    }

    public checkIsSufficientFund(amount :number):boolean {
        const bringChips = cv.StringTools.clientGoldByServer(this.bringChips());
        if (amount >= bringChips) {
            let remainingAmount = amount - bringChips;
            const exchangeRate = this._getExchangeRate();
            remainingAmount *=  exchangeRate; // Converted to other currency
            const exchangeChips = cv.StringTools.clientGoldByServer(this.exchangeChips());
            return (remainingAmount <= exchangeChips);
        }
        return true;
    }

    public isSufficientGoldFund(amount :number=0):boolean {
        const bringChips = cv.StringTools.clientGoldByServer(cv.dataHandler.getUserData().u32Chips);
        return amount > bringChips;
         
    }

    public isSufficientUSDFund(amount :number=0):boolean {
        const bringChips = cv.StringTools.clientGoldByServer(cv.dataHandler.getUserData().usdt);
        return amount > bringChips;
    }

    _getExchangeRate(): number {
        return cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.USDT ? this.usdt_2_coin_ex_val : this.coin_2_usdt_ex_val;
    }

     // The chips brought into the USDT table are USDT, and the exchange chips are COINS. The opposite is true for the gold coin table.
     bringChips(): number {
        return cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.USDT
            ? cv.dataHandler.getUserData().usdt
            : cv.dataHandler.getUserData().u32Chips;
    }

    exchangeChips(): number {
        return cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.USDT
            ? cv.dataHandler.getUserData().u32Chips
            : cv.dataHandler.getUserData().usdt;
    }
}
