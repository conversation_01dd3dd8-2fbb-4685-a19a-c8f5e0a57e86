import cv from '../cv';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import FastEnterSearch from './FastEnterSearch';
import FilterGroupController from './FilterGroupController';
import StakesFilterController from './StakesFilterController';
import LobbyFilterGroupBase from './LobbyFilterGroupBase';

const { ccclass, property } = cc._decorator;

@ccclass
export default class JackfruitFilterGroup extends FilterGroupController {
    @property(FastEnterSearch) protected jackfruitSearchbarInstance: FastEnterSearch = undefined;

    public initialize(): void {
        this.jackfruitSearchbarInstance.searchBtn.node.on(cc.Node.EventType.TOUCH_END, this._onSearchBtnClicked,this);
        this.stakesTitle.setSiblingIndex(this.layout.node.childrenCount-1);
        let controller;
        for (let j = world_pb.GameSizeType.GameSizeTypeMicro; j <= world_pb.GameSizeType.GameSizeTypeHigh; j++) {
            // Instantiating Stakes content and populating them with data:
            const fastEnter_content = cc.instantiate(this.fastEnter_content);
            fastEnter_content.setParent(this.layout.node);

            controller = fastEnter_content.getComponent(StakesFilterController);
            controller.SetStakeSize(j);
            this.filterControllers.push(controller);
        }

        this.filterControllers.forEach((x) => {
            x.node.active = false;
        });
    }

    public initControllers(gamdID: number, gameType: number, currencies: number[], gameTypes: string[], gameModes: string[]): void {
        // Do nothing
    }

    public setData(
        key:number,
        value: LobbyFilterGroupBase
    ): void {
        this.title.string = cv.config.getStringData('MainScene_Scene_gameType_panel_button7_text');
        this.filterGroupsData = value;
        this.stakesTitle.getChildByName('StakeTitle').getComponent(cc.Label).string =
            cv.config.getStringData('Filter_Stake_Title_Text');

        for (let j = world_pb.GameSizeType.GameSizeTypeMicro; j <= world_pb.GameSizeType.GameSizeTypeHigh; j++) {
            const dataArr = value.GetStakesArray(j);
            const len = cv.StringTools.getArrayLength(dataArr);
            const controller = this.getStakesFilterController(j);
            if (len > 0) {
                controller.initialize(j, dataArr, this.gametypeEntries[value.ID], true, key);
            } else {
                controller.node.setParent(null);
                controller.node.active = false;
            }
        }
        this.layout.updateLayout();
    }

    public refreshData(key:number, value: any): void {
        this.filterGroupsData = value;

        for (let j = world_pb.GameSizeType.GameSizeTypeMicro; j <= world_pb.GameSizeType.GameSizeTypeHigh; j++) {
            const dataArr = this.filterGroupsData.GetStakesArray(j);
            const len = cv.StringTools.getArrayLength(dataArr);
            const controller = this.getStakesFilterController(j);
            if (len > 0) {
                controller.initialize(
                    j,
                    dataArr,
                    this.gametypeEntries[this.filterGroupsData.ID],
                    false,
                    key
                );
                controller.refreshByData();
            } else {
                controller.node.setParent(null);
                controller.node.active = false;
            }
        }
        this.layout.updateLayout();
    }

    public isAllFilterEmpty(): boolean {
        return this.isStakeFiltersEmpty();
    }

    public getAllStakes(): string[] {
        const arr: string[] = [];
        const id = this.gametypeEntries[this.filterGroupsData.ID];
        for (let i = world_pb.GameSizeType.GameSizeTypeMicro; i <= world_pb.GameSizeType.GameSizeTypeHigh; i++) {
            const stakeController = this.getStakesFilterController(i);
            stakeController.filterData.forEach((element) => {
                arr.push(element + '+' + id);
            });
        }
        return arr;
    }

    public override initLanguage():void{
        this.title.string = cv.config.getStringData('MainScene_Scene_gameType_panel_button7_text');
    }

    private _onSearchBtnClicked():void{
        const str = this.jackfruitSearchbarInstance.search.string;
        if (str.length !== 4) {
            cv.TT.showMsg(
                cv.config.getStringData('jackfruit_find_view_search_tips'),
                cv.Enum.ToastType.ToastTypeInfo
            );
            return;
        }
        cc.game.emit('OnFilterTablesButtonClicked', str);
    }
}
