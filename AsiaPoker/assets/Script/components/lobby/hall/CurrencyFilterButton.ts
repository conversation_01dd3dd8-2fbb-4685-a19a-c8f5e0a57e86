
import { CurrencyType } from "../../../common/tools/Enum";
import cv from "../cv";
import LobbyFilterButtonBase from "./LobbyFilterButtonBase";

const {ccclass} = cc._decorator;

@ccclass
export default class CurrencyFilterButton extends LobbyFilterButtonBase {

    protected metaData: CurrencyType;

    public get metadata()
    {
        return this.metaData;
    }

    public initialize(type: CurrencyType, newState: boolean, buttonCallback: (btn: CurrencyFilterButton)=>void)
    {
        this.metaData = type;
        this.setNewState(newState);
        this.initLanguage();
        this.onButtonClickDelegate = buttonCallback;
    }

    public override initLanguage():void{
        let localisedString = "";
        switch (this.metaData) {
            case cv.Enum.CurrencyType.GOLD:
                localisedString = cv.config.getStringData('Coins_Label_Display');
                break;
            case cv.Enum.CurrencyType.USDT:
                localisedString = cv.config.getStringData('USD_Label_Display');
                break;
        }
        this.setContent(localisedString);
    }

    protected onUnuse(): void {
        super.onUnuse();
        this.metaData = 0;
    }
    
    protected onClickListener(event: cc.Event): void 
    {
        super.onClickListener(event);
        this.onButtonClickDelegate(this);
    }
}
