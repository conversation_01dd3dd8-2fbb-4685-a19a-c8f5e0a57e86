import cv from '../cv';
import { HashMap } from '../../../common/tools/HashMap';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import LobbyFilterGroupBase from './LobbyFilterGroupBase';
import JackfruitFilterGroup from './JackfruitFilterGroup';
import FilterGroupController from './FilterGroupController';
import { DiscoverGameType } from './FindView';
import BaseFilterController from './BaseFilterController';
import StakesFilterController from './StakesFilterController';

const { ccclass, property } = cc._decorator;

@ccclass
export default class FilterPanel extends cc.Component {
    @property(cc.Node) quickSeatPanel: cc.Node = null;
    @property(cc.Node) topBG: cc.Node = null;
    @property(cc.Label) title: cc.Label = null;
    @property(cc.Node) filterButtonsScrollContainer: cc.Node = null;
    @property(cc.Prefab) fastEnter_search: cc.Prefab = null;
    @property(cc.Prefab) filterGroupPrefab: cc.Prefab = null;
    @property(cc.Prefab) jackfruitGroupPrefab: cc.Prefab = null;
    @property(cc.Node) bottom: cc.Node = null;
    @property(cc.Node) quickSeatPanelRoot: cc.Node = null;
    @property(cc.Node) quickSeatButtonNode: cc.Node = null;
    @property(cc.Node) filterTablesButtonNode: cc.Node = null;

    private readonly blindSaveDataDelimeter: string = '_blind';

    public _gameType: number = 0;

    public masterTableList: world_pb.ClubGameSnapshotV3[] = [];

    protected filterGroupsData: Map<number, LobbyFilterGroupBase> = new Map();
    protected filterGroups: HashMap<number, FilterGroupController> = new HashMap();

    private topScrollSize: cc.Size = new cc.Size(0, 0);
    // To limit the number of filter criteria.
    private _maxFilterCount: number = 5;
    private _filterCache: [FilterGroupController, BaseFilterController, any][] = [];

    protected onLoad(): void {
        this.topScrollSize = this.filterButtonsScrollContainer.parent.getContentSize();
        this.quickSeatButtonNode.on(cc.Node.EventType.TOUCH_END, this._onBtnQuickEntryClicked, this);
        this.filterTablesButtonNode.on(cc.Node.EventType.TOUCH_END, this._onBtnFilterTablesClicked, this);
        cc.game.on('OnFilterButtonClicked', this._onFilterButtonClicked, this);

        this.filterButtonsScrollContainer.on(cc.Node.EventType.SIZE_CHANGED,()=>{
         this._setContentSize();   
        },this);
    }

    protected onEnable(): void {
        this._initLanguage();
    }

    public reset(): void {
        this.filterGroups.forEach((key: number, value: FilterGroupController)=>{
            value.node.destroy();
        });
        this.filterGroups.clear();
    }

    // Refreshes the blind levels scroll list in Quick seat panel. This block was earlier part of onBtnFastEnterClick, but now it's separated out
    // because of AT-636, where the blind levels have to be dynamic based on currency type. That's already happening in this.getFastEnterData();
    public refreshFilterWindowContent(): void {
        this._onNLHETabSkipSDAndBetGames();
        this.filterButtonsScrollContainer.parent.getComponent(cc.ScrollView).scrollToTop();
        const gameIDs = this._getUniqueGameID();
        gameIDs.forEach((value: number) => {
            this._addFilterGroup(value);
        });
        
        this._fetchFilteredTableList();
        this.filterGroupsData.forEach((value: LobbyFilterGroupBase, key: number) => {
            this._updateFilterGroup(key, value);
        });

        this._updateGameModeBtnState();
        this.filterButtonsScrollContainer.getComponent(cc.Layout).updateLayout();
        this.quickSeatPanelRoot.active = true;
    }

    // Returns a list of entries unique by currency. This will help determine no of currencies from current list of tables.
    public getUniqueListForCurrency(list: world_pb.ClubGameSnapshotV3[]) {
        const uniqueList = list
            .filter((obj, index) => index === list.findIndex((o) => obj.currencyType === o.currencyType))
            .map((x) => x.currencyType);

        return uniqueList;
    }

    public getUniqueListForCurrencyForKey(list: world_pb.ClubGameSnapshotV3[], keyIndex: number) {
        const newList = list.filter((x) => keyIndex === this._getKeyIndexByItem(x));
        const uniqueList = this.getUniqueListForCurrency(newList);

        return uniqueList;
    }

    public getUniqueListOfGameTypes(list: world_pb.ClubGameSnapshotV3[]) {
        const result: string[] = [];

        if (list.filter((x) => x.game_id === cv.Enum.GameId.Texas || x.game_id === cv.Enum.GameId.Squid).length > 0) {
            result.push('texas');
        }
        if (list.filter((x) => cv.roomManager.checkGameIsZoom(x.game_id)).length > 0) {
            result.push('zoom');
        }

        return result;
    }

    public getUniqueListOfGameTypesForKey(list: world_pb.ClubGameSnapshotV3[], keyIndex: number) {
        const newList = list.filter((x) => keyIndex === this._getKeyIndexByItem(x));
        const result = this.getUniqueListOfGameTypes(newList);

        return result;
    }

    public getUniqueListOfGameModes(list: world_pb.ClubGameSnapshotV3[]) {
        const modes = {
            classic: false,
            loose: false,
            critical: false,
            squid: false
        };
        
        for (let i = 0; i < list.length; i++) {
            const x = list[i];
            if (x.room_mode === world_pb.RoomMode.RoomModeNone && x.game_id !== cv.Enum.GameId.Squid) {
                modes.classic = true;
            }
            if (x.room_mode === world_pb.RoomMode.RoomModeLoose) {
                modes.loose = true;
            }
            if (x.room_mode === world_pb.RoomMode.RoomModeBomb) {
                modes.critical = true;
            }
            if (x.game_id === cv.Enum.GameId.Squid) {
                modes.squid = true;
            }
        }
       return Object.keys(modes).filter(key => modes[key]);  
    }

    public getUniqueListOfGameModesForKey(list: world_pb.ClubGameSnapshotV3[], keyIndex: number) {
        const newList = list.filter((x) => keyIndex === this._getKeyIndexByItem(x));
        const result = this.getUniqueListOfGameModes(newList);
        return result;
    }

    public filterMasterTableList(name: string = ''): world_pb.ClubGameSnapshotV3[] {
        this._onNLHETabSkipSDAndBetGames();
        if (name !== '') {
            return this._filterByRoomName(name);
        }

        let helpData: world_pb.ClubGameSnapshotV3[] = [];
        let tempData = this.masterTableList;
        this.filterGroups.forEach((key: number, controller: FilterGroupController) => {
            // Ignore Jackruit because jackfruit filter group doesn't compose currency / mode / gametype fitlers.
            if(key !== 6)
            {
                tempData = controller.getCurrencyFilterController().filter(tempData, this._gameType);
                tempData = controller.getGameTypeFilterController().filter(tempData, this._gameType);
                tempData = controller.getModeTypeFilterController().filter(tempData, this._gameType);
            }
        });

        const keys = this._getAllStakes();
        
        const blindTabsSelected = keys.filter((x) => x.includes(this.blindSaveDataDelimeter));

        if (blindTabsSelected.length <= 0) {
            helpData = tempData;
        } else {
            for (let i = 0; i < tempData.length; i++) {
                const item = tempData[i];

                if (item.game_id === cv.Enum.GameId.StarSeat) {
                    // Add all star tables regardsless filters selected
                    helpData.push(item);
                } else {
                    const manzhu = this._getManZhuString(item);

                    for (let j = 0; j < blindTabsSelected.length; j++) {
                        let savedEntry = blindTabsSelected[j];
                        savedEntry = savedEntry.replace(this.blindSaveDataDelimeter, '');
                        const pos = savedEntry.indexOf('+');

                        const saveManzhu = savedEntry.slice(0, pos);

                        const saveType = cv.Number(savedEntry.slice(pos + 1));

                        if (
                            (manzhu === saveManzhu || item.game_id === cv.Enum.GameId.StarSeat) &&
                            this._compareType(item, saveType)
                        ) {
                            helpData.push(item);
                            break;
                        }
                    }
                }
            }
        }

        return helpData;
    }

    public FiltereTableListForQuickEntry(isSimulator: boolean): world_pb.ClubGameSnapshotV3[] {
        const arr: world_pb.ClubGameSnapshotV3[] = [];
        this._onNLHETabSkipSDAndBetGames();
        let tempData = this.masterTableList;

        this.filterGroups.forEach((key: number, controller: FilterGroupController) => {
            // Ignore Jackruit because jackfruit filter group doesn't compose currency / mode / gametype fitlers.
            if(key !== 6)
            {
                tempData = controller.getCurrencyFilterController().filter(tempData, this._gameType);
                tempData = controller.getGameTypeFilterController().filter(tempData, this._gameType);
                tempData = controller.getModeTypeFilterController().filter(tempData, this._gameType);
            }
        });

        const itemLen = cv.StringTools.getArrayLength(tempData);
        const keys = this._getAllStakes();

        for (let i = 0; i < itemLen; i++) {
            const item = tempData[i];

            const manzhu = this._getManZhuString(item);

            for (let j = 0; j < keys.length; j++) {
                let savedEntry = keys[j];
                savedEntry = savedEntry.replace(this.blindSaveDataDelimeter, '');
                const pos = savedEntry.indexOf('+');

                const saveManzhu = savedEntry.slice(0, pos);

                const saveType = cv.Number(savedEntry.slice(pos + 1));

                if (item.game_id !== cv.Enum.GameId.StarSeat && manzhu === saveManzhu && this._compareType(item, saveType)) {
                    const currentTime = new Date().getTime() / 1000;

                    // 普通局排除超过开放时间超过24小时的牌局
                    if (
                        (item.is_mirco === 1 ||
                            item.has_buyin !== 2 ||
                            item.IscalcIncomePerhand ||
                            item.game_id === cv.Enum.GameId.Bet ||
                            item.game_id === cv.Enum.GameId.Jackfruit ||
                            item.game_id === cv.Enum.GameId.Plo) &&
                        // item.join_password.length <= 0 &&
                        // item.buyin_password.length <= 0 &&
                        (currentTime - item.create_time) / 3600 < 24
                    ) {
                        let hasPlayTime = 0.0;

                        if (item.start_time === 0) {
                            hasPlayTime = 0;
                        } else {
                            hasPlayTime = (currentTime - item.start_time) / 3600.0;
                        }

                        const timeLimit = cv.Number(
                            cv.config.getStringData(cv.StringTools.formatC('UITime%d', item.rule_time_limit - 1))
                        );

                        const yanshiTime = item.extra_time / 3600.0;

                        const lastTimes = timeLimit - hasPlayTime + yanshiTime;

                        // 排除时长不够2小时房间(微牌局不排除)
                        if (
                            lastTimes >= 2 ||
                            item.is_mirco === 1 ||
                            item.game_id === cv.Enum.GameId.Bet ||
                            item.IscalcIncomePerhand ||
                            item.game_id === cv.Enum.GameId.Jackfruit ||
                            item.game_id === cv.Enum.GameId.Plo
                        ) {
                            //
                            // 排除满座
                            if (item.left_seatnum > 0 || item.game_id === cv.Enum.GameId.Plo) {
                                // 模拟器过滤
                                if (!isSimulator || item.anti_simulator) {
                                    arr.push(item);
                                }
                            }
                        }
                    }
                }
            }
        }
        return arr;
    }

    private _setFastEnterSaveRecord(): void {
        this.filterGroups.forEach((key: number, value: FilterGroupController)=>{
            value.SaveDataToFile();
        });
    }

    private _onBtnFilterTablesClicked(event: cc.Event) {
        cv.AudioMgr.playButtonSound('button_click');

        event.stopPropagation();

        this._setFastEnterSaveRecord();

        if (this._isAllFilterEmpty()) {
            cv.TT.showMsg(cv.config.getStringData('UIMainTips01'), cv.Enum.ToastType.ToastTypeError);

            return;
        }

        // 跟踪用户行为, 发送事件
        const stakeSizes: string[] = this._getAllStakes();
        stakeSizes.sort();
        const properties = { stakeSizes, skipLobby: false };

        cv.segmentTool.track(
            cv.Enum.CurrentScreen.lobby,
            cv.Enum.segmentEvent.LobbyFilterApplied,
            cv.Enum.Functionality.poker,
            properties
        );

        cc.game.emit('OnFilterTablesButtonClicked');
    }

    private _onBtnQuickEntryClicked(event: cc.Event) { 
        cv.AudioMgr.playButtonSound('button_click');

        event.stopPropagation();

        this._setFastEnterSaveRecord();

        if (this._isAllStakesEmpty()) {
            cv.TT.showMsg(cv.config.getStringData('UIMainTips01'), cv.Enum.ToastType.ToastTypeError);
            return;
        }

        if (cv.dataHandler.getUserData().isban) {
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode501'), cv.Enum.ToastType.ToastTypeInfo);
            return;
        }

        cv.reCaptcha.checkRecaptcha(this._onCaptchaResult.bind(this));

        // 跟踪用户行为, 发送事件
        const stakeSizes: string[] = this._getAllStakes();
        stakeSizes.sort();

        const properties = { stakeSizes, skipLobby: true };

        cv.segmentTool.track(
            cv.Enum.CurrentScreen.lobby,
            cv.Enum.segmentEvent.LobbyFilterApplied,
            cv.Enum.Functionality.poker,
            properties
        );
    }

    private _onCaptchaResult(captchaPassed: boolean) {
        cc.game.emit('OnJoinQuickSeatCaptchaResult', captchaPassed);
    }
    
    private _getUniqueKeyIndices(list: world_pb.ClubGameSnapshotV3[]) {
        const result: number[] = [];

        list.forEach((table) => {
            const xValue = this._getKeyIndexByItem(table);
            result.push(xValue);
        });

        // Filter for unique entries
        const uniqueResult = Array.from(new Set(result));

        return uniqueResult;
    }

    private _refreshFilters() {
        // record filter data
        const filterCache = this._filterCache;
        const still = new Set<number>();

        this.filterGroups.forEach((gameID, fg)=>{ 
            const controllers = fg.getFilterControllers();
            for(let controller of controllers) {
                for(let i = 0; i < controller.filterData.length; i++) {
                    const val = controller.filterData[i];
                    let index = filterCache.findIndex((x) => x[0] === fg && x[1] === controller && x[2] === val);
                    if(index === -1) {
                        filterCache.push([fg, controller, val]);
                        index = filterCache.length - 1;
                    }
                    still.add(index);
                }
                if(controller instanceof StakesFilterController) {
                    controller.filterData.length = 0;
                    controller.saveFilterDataToFile();
                }
            }
        });

        const newFilterCache = [] as typeof filterCache
        for(let i = 0; i < filterCache.length; i++) {
            if(!still.has(i)) continue;
            newFilterCache.push(filterCache[i]);
        }

        this._onNLHETabSkipSDAndBetGames();
        const gameIDs = this._getUniqueGameID();
        gameIDs.forEach((value: number) => {
            this._addFilterGroup(value);
        });
        
        this._fetchFilteredTableList();
        this.filterGroupsData.forEach((value: LobbyFilterGroupBase, key: number) => {
            this._updateFilterGroup(key, value);
        });

        // record the index of valid filters selected
        const confirmIdx = new Set;
        this.filterGroups.forEach((key, fg) => { 
            for (let i = world_pb.GameSizeType.GameSizeTypeMicro; i <= world_pb.GameSizeType.GameSizeTypeHigh; i++) {
                const controller = fg.getStakesFilterController(i);
                const stakes = controller.stakes;
                for(let stake of stakes) {
                    let index = newFilterCache.findIndex((x) => x[0] === fg && x[1] === controller && x[2] === stake);
                    if(index !== -1) {
                        confirmIdx.add(index);
                    }
                }
            }
        });

        // record the valid filters
        const validFilterCache: typeof newFilterCache = [];
        for(let i = 0; i < newFilterCache.length; i++) {
            const [fg, controller, val] = newFilterCache[i];
            if(!(controller instanceof StakesFilterController) || confirmIdx.has(i)) {
                validFilterCache.push([fg, controller, val]);
            }
        }
        this._filterCache = validFilterCache;
        
        // apply is selected to valid filters
        for(let i = 0; i < validFilterCache.length; i++) {
            const [fg, controller, val]= validFilterCache[i];
            if(controller.filterData.indexOf(val) !== -1) continue;
            controller.filterData.push(val);
        }

         // limit 5 stake filters
        const stakeFilters = validFilterCache.filter((x) => x[1] instanceof StakesFilterController);
        if(stakeFilters.length > 5) {
            let index = validFilterCache.findIndex((x) => x[1] === stakeFilters[0][1]);
            if(index !== -1) {
                const filter = validFilterCache[index];
                filter[1].filterData.splice(0, 1);
                //To handle changes caused by multi-level linkage, recursion is used to process them in the same way.
                this._refreshFilters();
                return;
            }
        }

        this._setFastEnterSaveRecord();

        // update view
        this.filterGroupsData.forEach((value: LobbyFilterGroupBase, key: number) => {
            this._updateFilterGroup(key, value);
        })
        this._updateGameModeBtnState();
        this.filterButtonsScrollContainer.getComponent(cc.Layout).updateLayout();
    }

    // Common behaviour when filters other than stakes are clicked.
    private _onFilterButtonClicked(refreshContentFlag: boolean)
    {
        cv.AudioMgr.playButtonSound('tab'); 

        this._refreshFilters();

        if(refreshContentFlag) {
            this.filterButtonsScrollContainer.parent.getComponent(cc.ScrollView).scrollToTop();
        }
    }

    private _updateGameModeBtnState() {
        if (!this._isAllFilterEmpty()) {
            this._btnNormal(this.filterTablesButtonNode, cc.color(80, 66, 38));
        } else {
            this._btnDisabled(this.filterTablesButtonNode, cc.color(143, 144, 156));
        }

        if (!this._isAllStakesEmpty()) {
            this._btnNormal(this.quickSeatButtonNode, cc.Color.WHITE);
        } else {
            this._btnDisabled(this.quickSeatButtonNode, cc.color(143, 144, 156));
        }
    }

    private _btnNormal(btn: cc.Node, color: cc.Color) {
        const btnLabel = btn.getComponentInChildren(cc.Label);
        btnLabel.node.color = color;
        btnLabel.node.opacity = 255;
        btn.getComponent(cc.Button).interactable = true;
        btn.getComponent(cc.Button).enabled = true;
    }

    private _btnDisabled(btn: cc.Node, color: cc.Color) {
        const btnLabel = btn.getComponentInChildren(cc.Label);
        btnLabel.node.color = color;
        btnLabel.node.opacity = 102;
        btn.getComponent(cc.Button).interactable = false;
        btn.getComponent(cc.Button).enabled = false;
    }

    private _getUniqueGameID():number[]
    {
        const set = new Set<number>();
        const itemLen = cv.StringTools.getArrayLength(this.masterTableList);
        for (let i = 0; i < itemLen; i++) {
            const item = this.masterTableList[i];
            set.add(this._getKeyIndexByItem(item));
        }
        return Array.from(set.values());;
    }

    private _fetchFilteredTableList() {
        this.filterGroupsData.clear();

        const uniqueIndex = this._getUniqueKeyIndices(this.masterTableList);

        let uniqueGameTypes = this.getUniqueListOfGameTypes(this.masterTableList);
        let uniqueModeTypes = this.getUniqueListOfGameModes(this.masterTableList);

        const itemLen = cv.StringTools.getArrayLength(this.masterTableList);

        for (let i = 0; i < itemLen; i++) {
            const item = this.masterTableList[i];
            const manzhu = this._getManZhuString(item);
            const gameID = this._getKeyIndexByItem(item);

            if (gameID !== -1)
            {
                let filterGroup: LobbyFilterGroupBase = this.filterGroupsData.get(gameID);
                if (!filterGroup) {
                    this.filterGroupsData.set(gameID, new LobbyFilterGroupBase());
                    filterGroup = this.filterGroupsData.get(gameID);
                }
                filterGroup.SetID(gameID);

                if (uniqueIndex.length > 1) {
                    uniqueGameTypes = this.getUniqueListOfGameTypesForKey(this.masterTableList, gameID);
                    uniqueModeTypes = this.getUniqueListOfGameModesForKey(this.masterTableList, gameID);
                }
                const sIndex: number = filterGroup.getStakeIndexByItem(item);
                let stakeGroup: string[] = [];
                stakeGroup = filterGroup.GetStakesArray(sIndex);

                const isJackfruit = gameID === 6;
                let filterPassed = true;
                if(!isJackfruit)
                {
                    const group = this.filterGroups.get(gameID);
                    const qualifiedForGameTypeFilter = group
                    .getGameTypeFilterController()
                    .isQualified(
                        item,
                        this._gameType,
                        this._checkIfSubset(group.getGameTypeFilterController().filterData, uniqueGameTypes)
                    );
                    const qualifiedForModeTypeFilter = group
                            .getModeTypeFilterController()
                            .isQualified(
                                item,
                                this._gameType,
                                this._checkIfSubset(group.getModeTypeFilterController().filterData, uniqueModeTypes)
                            );
                    const qualifiedForCurrencyFilter = group.getCurrencyFilterController().isQualified(item, this._gameType);
                    filterPassed = qualifiedForCurrencyFilter && qualifiedForModeTypeFilter && qualifiedForGameTypeFilter;
                }

                if (
                    (stakeGroup.indexOf(manzhu) === -1 && filterPassed) || isJackfruit
                ) {
                    filterGroup.addEntry(sIndex, manzhu);
                }
            }
        }

        if (this._gameType === DiscoverGameType.DZPK) {
            this._sortNLHEGroupData(uniqueIndex);
        }

        this.filterGroupsData.forEach((value: LobbyFilterGroupBase, key: number) => {
            value.SortAllStakeLevels();
        });
    }

    private _addFilterGroup(id:number): void {
        const group = this.filterGroups.get(id);
        if (!group) {
            let filterGroup: FilterGroupController;
            if(id === 6)
            {
                filterGroup = cc.instantiate(this.jackfruitGroupPrefab).getComponent(JackfruitFilterGroup);
            }
            else
            {
                filterGroup = cc.instantiate(this.filterGroupPrefab).getComponent(FilterGroupController);
            }
            filterGroup.node.setParent(this.filterButtonsScrollContainer);
            this.filterGroups.add(id, filterGroup);
            filterGroup.initialize();
            const currencies = this.getUniqueListForCurrencyForKey(this.masterTableList,id);
            const gameModes = this.getUniqueListOfGameModesForKey(this.masterTableList,id);
            const gameTypes = this.getUniqueListOfGameTypesForKey(this.masterTableList,id);
            filterGroup.initControllers(id, this._gameType, currencies,gameTypes,gameModes);
        }
    }

    private _updateFilterGroup(key: number, value: LobbyFilterGroupBase): void {
        const group = this.filterGroups.get(key);
        if (group) {
            group.setData(key, value);
            group.refreshData(key, value);
        }
    }

    private _setContentSize(): void {
        let height: number = 0;
        const maxHeight = this.quickSeatPanel.height - this.topBG.height - this.bottom.height - 120;
        height = this.filterButtonsScrollContainer.height;
        const resizeTarget = this.filterButtonsScrollContainer.parent;
        if (this.topScrollSize.height > height) {
            resizeTarget.setContentSize(this.topScrollSize);
        } else if (maxHeight > height) {
            this.filterButtonsScrollContainer
                .getParent()
                .setContentSize(resizeTarget.getContentSize().width, height);
        } else {
            this.filterButtonsScrollContainer
                .getParent()
                .setContentSize(resizeTarget.getContentSize().width, maxHeight);
        }
    }

    private _getAllStakes():string[]{
        const str :string[] = [];
        this.filterGroups.forEach((key,value) => {
            const strArr = value.getAllStakes();
            strArr.forEach(s => { str.push(s+this.blindSaveDataDelimeter)});
        });
        return str;
    }

    private _isAllFilterEmpty():boolean{
        let empty = true;
        this.filterGroups.forEach((key,value) => {
            if(!value.isAllFilterEmpty()) {empty = false;}
        });
        return empty;
    }

    private _isAllStakesEmpty():boolean{
        let empty = true;
        this.filterGroups.forEach((key,value) => {
            if(!value.isStakeFiltersEmpty()) {empty = false;}
        });
        return empty;
    }

    // Sort the filterGroupsData so that NLHE data will be first, rest order can remain the same.
    private _sortNLHEGroupData(uniqueIndex: number[]) {
        if (this.filterGroupsData.size > 1) {
            // Sorting index such that NLHE is first. Kindly refer this._getKeyIndexByItem() for why 3 is used for NLHE.
            uniqueIndex.sort((a, b) => {
                if (a === 3) {
                    return -1;
                }
                if (b === 3) {
                    return 1;
                }
                return 0;
                
            });

            const tempMap: Map<number, LobbyFilterGroupBase> = new Map(this.filterGroupsData);
            this.filterGroupsData.clear();
            uniqueIndex.forEach((index) => {
                this.filterGroupsData.set(index, tempMap.get(index));
            });
        }
    }

    private _getKeyIndexByItem(item: world_pb.ClubGameSnapshotV3): number {
        let index: number = -1;
        if (item.game_id === cv.Enum.GameId.Bet) {
            index = 0;
        } else if (item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            if (item.game_id === cv.Enum.GameId.Allin) {
                index = 1;
            } else if (item.game_id === cv.Enum.GameId.Texas || item.game_id === cv.Enum.GameId.Squid  || item.game_id === cv.Enum.GameId.StarSeat || cv.roomManager.checkGameIsZoom(item.game_id)) {
                index = 3;
            } else if (item.game_id === cv.Enum.GameId.Jackfruit) {
                index = 6;
            } else if (item.game_id === world_pb.GameId.PLO) {
                index = 7;
            }
        } else if (item.game_id === cv.Enum.GameId.Allin) {
            index = 2;
        } else if (item.game_id === cv.Enum.GameId.Texas || item.game_id === cv.Enum.GameId.Squid ||item.game_id === cv.Enum.GameId.StarSeat || cv.roomManager.checkGameIsZoom(item.game_id)) {
            index = 4;
        } else if (item.game_id === cv.Enum.GameId.Jackfruit) {
            index = 6;
        }
        return index;
    }
    
    // Checks if <targetToCheck> is a subset of referenceArr.
    private _checkIfSubset(targetToCheck: string[], referenceArr: string[]): boolean {
        return targetToCheck.every((x) => referenceArr.includes(x));
    }

    private _compareType(item: world_pb.ClubGameSnapshotV3, type: number): boolean {
        switch (type) {
            case 1:
                return (
                    (item.game_id === cv.Enum.GameId.Texas || item.game_id === cv.Enum.GameId.Squid || item.game_id === cv.Enum.GameId.StarSeat || cv.roomManager.checkGameIsZoom(item.game_id)) &&
                    item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal
                );
            case 2:
                return (
                    (item.game_id === cv.Enum.GameId.Texas || item.game_id === cv.Enum.GameId.Squid || item.game_id === cv.Enum.GameId.StarSeat || cv.roomManager.checkGameIsZoom(item.game_id)) &&
                    item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short
                );
            case 3:
                return (
                    item.game_id === cv.Enum.GameId.Allin &&
                    item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal
                );
            case 4:
                return item.game_id === cv.Enum.GameId.Bet;
            case 30:
                return (
                    item.game_id === cv.Enum.GameId.Allin &&
                    item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short
                );
            case 7: // 暴击德州
                return item.game_id === cv.Enum.GameId.Jackfruit;
            case 8: // 奥马哈
                return item.game_id === world_pb.GameId.PLO;
        }
        return false;
    }

    private _filterByRoomName(name: string): any[] {
        const helpData: any[] = [];
        const tempData = this.masterTableList;
        const itemLen = cv.StringTools.getArrayLength(tempData);
        for (let i = 0; i < itemLen; i++) {
            const item = tempData[i];
            const str: string = item.room_name;
            if (str.indexOf(name) !== -1) {
                helpData.push(item);
            }
        }
        return helpData;
    }

    private _getManZhuString(msg: world_pb.ClubGameSnapshotV3): string {
        let manzhu = '';

        if (msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short || msg.game_id === cv.Enum.GameId.Bet) {
            const ante: number = cv.StringTools.serverGoldToShowNumber(msg.ante);
            manzhu = cv.StringTools.formatC(
                '%s%s',
                (ante >= 1000 ? ante / 1000.0 : ante).toString(),
                ante >= 1000 ? 'K' : ''
            );
        } else if (msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            const cbBigBlind: number = cv.StringTools.serverGoldToShowNumber(msg.big_blind);
            const cbSmallBlind: number = cv.StringTools.serverGoldToShowNumber(msg.small_blind);
            const bigBlind: string = cv.StringTools.formatC(
                '%s%s',
                (cbBigBlind >= 1000 ? cbBigBlind / 1000.0 : cbBigBlind).toString(),
                cbBigBlind >= 1000 ? 'K' : ''
            );

            const smallBlind: string = cv.StringTools.formatC(
                '%s%s',
                (cbSmallBlind >= 1000 ? cbSmallBlind / 1000.0 : cbSmallBlind).toString(),
                cbSmallBlind >= 1000 ? 'K' : ''
            );

            manzhu = cv.StringTools.formatC('%s/%s', smallBlind.toString(), bigBlind.toString());

            if (msg.straddle) {
                const cbstaraddle: number = cv.StringTools.times(cbBigBlind, 2);
                manzhu = cv.StringTools.formatC(
                    '%s/%s',
                    manzhu,
                    cbBigBlind >= 1000 ? cv.StringTools.div(cbstaraddle, 1000).toString() + 'K' : cbstaraddle.toString()
                );
            }
        }

        if (msg.player_count_max === 2 && msg.game_id !== cv.Enum.GameId.Jackfruit) {
            manzhu += ' (HU)';
        }

        return manzhu;
    }

    private _initLanguage():void{
        this.filterGroups.forEach((key, value)=>{value.initLanguage();});
        this.filterTablesButtonNode.getComponentInChildren(cc.Label).string = cv.config.getStringData('MainScene_Scene_pokerPage_panel_screen_button_text');
        this.quickSeatButtonNode.getComponentInChildren(cc.Label).string = cv.config.getStringData('MainScene_Scene_pokerPage_panel_seat_button_text');
        this.title.string = cv.config.getStringData('MainScene_Scene_pokerPage_panel_seat_button_text');
    }

    // this fucntion is use only when NLHE tab filter will not show all shortDeck and Bet games.
    private _onNLHETabSkipSDAndBetGames(){
        if(this._gameType=== DiscoverGameType.DZPK){
            this.masterTableList = this.masterTableList.filter(
                item =>  item.game_id !== cv.Enum.GameId.Bet && item.game_mode !== cv.Enum.CreateGameMode.CreateGame_Mode_Short
            );
        }
    }

}
