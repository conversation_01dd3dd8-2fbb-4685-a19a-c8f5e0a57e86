 
const {ccclass, property} = cc._decorator;

@ccclass
export default class LobbyFilterButtonBase extends cc.Component {

    // Holds reference to label where content to display is set.
    @property(cc.Label)
    protected displayContent: cc.Label = null;

    // Holds reference to sprite-renderer node upon active state.
    @property(cc.Node)
    protected activeSpriteNode: cc.Node = null;

    // Holds reference to sprite-renderer node upon inactive state.
    @property(cc.Node)
    protected inactiveSpriteNode: cc.Node = null;

    @property(cc.Color)
    protected displayLabelActiveColor: cc.Color = cc.Color.WHITE;

    @property(cc.Color)
    protected displayLabelInactiveColor: cc.Color = cc.Color.WHITE;

    // Current state: true means active, false means inactive. Active meaning this filter will get applied.
    protected _currentState: boolean;

    public get currentState()
    {
        return this._currentState;
    }

    public onButtonClickDelegate: Function;

    public setNewState(newState: boolean):void
    {
        this._currentState = newState;
        this.activeSpriteNode.active = newState;
        this.inactiveSpriteNode.active = !newState;

        const newColor: cc.Color = newState? this.displayLabelActiveColor : this.displayLabelInactiveColor;
        this.displayContent.node.color = newColor;
    }

    public initLanguage():void{}

    protected onLoad(): void 
    {
        this.node.on(cc.Node.EventType.TOUCH_END, this.onClickListener.bind(this));
    }

    protected onUnuse()
    {
        this.node.active = false;
        this.onButtonClickDelegate = null;
        this.displayContent.string = "";
        this._currentState = false;
    }

    protected onReuse()
    {
        this.node.active = true;
    }
    
    // Override this function and call the super.OnClickListener() from child class
    protected onClickListener(event: cc.Event) 
    {
        this.setNewState(!this.currentState);
    }

    protected setContent(content: string)
    {
        this.displayContent.string = content;
    }
}
