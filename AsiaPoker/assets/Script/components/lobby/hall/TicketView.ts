/* eslint-disable max-classes-per-file */
// Learn TypeScript:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html

import { pb } from "../../../../Script/common/pb/ws_protocol";
import { LANGUAGE_TYPE } from "../../../../Script/common/tools/Enum";
import { HashMap } from "../../../../Script/common/tools/HashMap";
import cv from "../../../../Script/components/lobby/cv";

enum DesSpriteType {
    giftForYou = 0,
    gotAGift,
    tapToOpen,
    willBePlaced,
    claim,
}
class NODE_SATE {
    position: cc.Vec2;
    rotation: number;
    scale: cc.Vec2;
    opacity: number;
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class TicketView extends cc.Component {

    @property(cc.Animation)
    pk: cc.Animation = null;

    @property(cc.Node)
    bg: cc.Node = null;

    @property(cc.Sprite)
    tik: cc.Sprite = null;

    @property(cc.Button)
    sure: cc.Button = null;

    @property(cc.Label)
    txt_desc: cc.Label = null;

    @property(cc.Label)
    txt_count: cc.Label = null;

    @property(cc.SpriteFrame)
    defaultImg: cc.SpriteFrame[] = [];

    @property(cc.Node)
    closeBtnNode: cc.Node = null;

    //#region Des Sprite Nodes
    @property(cc.Node)
    giftForYou: cc.Node = null;

    @property(cc.Node)
    gotAGift: cc.Node = null;

    @property(cc.Node)
    tapToOpen: cc.Node = null;

    @property(cc.Node)
    willBePlaced: cc.Node = null;

    @property(cc.Node)
    claim: cc.Node = null;
    //#endregion Des Sprite Nodes

    private _desSpriteMap: Map <DesSpriteType, cc.Node>  = new Map();

    aniName: string[] = ["pk_donghua_01", "pk_donghua_02", "pk_donghua_03"];
    canClick: boolean = false;
    isClick: boolean = false;
    canSure: boolean = false;

    msg: pb.IToolsInfo[] = [];
    currentIndex: number = 0;
    downloadIndex: number = 0;
    downloadSpr: HashMap<number, cc.SpriteFrame> = new HashMap();
    stateMap: HashMap<string, NODE_SATE> = null;

    static IS_VIEW: boolean = false;
    static NAME: string = "ticketView";
    getData: boolean = false;
    isClose: boolean = false;
    private _forceHide = false;
    private _claimBtnClickCb: Function = null;

    private _closeBtnY = 0;


    onLoad() {
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this._setDesNodeSprite.bind(this), this.node);
        cv.MessageCenter.register("ReceiveToolsNotice", this.ReceiveToolsNotice.bind(this), this.node);
        cv.MessageCenter.register("ReceiveToolsResponse", this.ReceiveToolsResponse.bind(this), this.node);
        cv.MessageCenter.register("MockTicketGiftShow", this._mockShow.bind(this), this.node);

        this._initDesSpriteMap();
        this.stateMap = new HashMap();
        this.getNodeStateData(this.pk.node);

        this.bg.on(cc.Node.EventType.TOUCH_END, () => {
            cv.AudioMgr.playButtonSound('button_click');
            if (!this.canClick) return;
            this.isClick = true;
            if (this.downloadIndex <= this.currentIndex) return;
            this.playAni();
        }, this);

        this._setDesNodeSprite();

        
        if(cv.SafeAreaWithDifferentDevices.isShortScreen()) {
            this._closeBtnY = this.closeBtnNode.y + 80;
        } else {
            this._closeBtnY = this.closeBtnNode.y;
        }
    }

    private _initDesSpriteMap() {
        this._desSpriteMap.clear();
        this._desSpriteMap.set(DesSpriteType.giftForYou, this.giftForYou);
        this._desSpriteMap.set(DesSpriteType.gotAGift, this.gotAGift);
        this._desSpriteMap.set(DesSpriteType.tapToOpen, this.tapToOpen);
        this._desSpriteMap.set(DesSpriteType.willBePlaced, this.willBePlaced);
        this._desSpriteMap.set(DesSpriteType.claim, this.claim);
    }

    hide() {
        this._forceHide = true;
        this.node.active = false;
    }

    onDestroy() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
        cv.MessageCenter.unregister("ReceiveToolsNotice", this.node);
        cv.MessageCenter.unregister("ReceiveToolsResponse", this.node);
        cv.MessageCenter.unregister("MockTicketGiftShow", this.node);
    }

    show() {
        if(this._forceHide) return;
        if (this.msg.length <= this.currentIndex || !TicketView.IS_VIEW) {
            TicketView.IS_VIEW = false;
            this.reset();
            cv.MessageCenter.send("sortATLView");
            return;
        }
       
        this._setDesNodeSprite();
        this.setNodeStateData(this.pk.node);
        this.sure.node.active = false;
        this.closeBtnNode.active = false;
        this.node.active = true;
        this.canClick = false;
        this.canSure = false;
        this.txt_desc.node.active = false;
        this.txt_count.node.active = false;
        this.pk.play(this.aniName[0]);
        this.pk.on("finished", (event: cc.Event): void => {
            this.pk.off("finished");
            TicketView.IS_VIEW = true;
            //this.sure.node.active = false;
            this.pk.play(this.aniName[1]);
            this.canClick = true;
            this.downloadTimeOut();
        }, this);
    }

    playAni() {
        let info: pb.IToolsInfo = null;
        if (this.currentIndex >= 0 && this.currentIndex < this.msg.length) {
            info = this.msg[this.currentIndex];
        }

        // 道具类型不是门票, 则隐藏门票闪光节点
        if (info) {
            let blink: cc.Node = this.tik.node.getChildByName("ticket_blick_00000");
            blink.active = info.currency_type === 0;
        }

        this.isClick = false;
        this.canClick = false;
        this.tik.spriteFrame = this.downloadSpr.get(this.currentIndex);
        this.pk.stop();
        this.closeBtnNode.active = false;
        this.sure.node.active = false;
        this._claimBtnClickCb = null;
        this.closeBtnNode.y = this._closeBtnY;
        this.pk.play(this.aniName[2]);
        this.pk.on("finished", (event: cc.Event): void => {
            this.pk.off("finished");
            this.canSure = true;
            this.closeBtnNode.active = true;
            this.sure.node.active = true;
            // 显示数量
            if (info) {
                let count: number = info.qty;
                switch (info.currency_type) {
                    case 0: // 门票
                        this._claimBtnClickCb = () => {
                            //redirect to back page
                            cv.MessageCenter.send("jumpToBackPack");
                        }                         
                        break;
                    case 1:         // 积分
                    case 2:         // 金币
                    case 3:         // 小游戏币
                    case 4:         // usdt
                    case 6:         // Trial coins
                        count = cv.StringTools.clientGoldByServer(count);
                        count = cv.StringTools.toFixed(count);
                        break;
                    case 7:         // PG免費小遊戲 (次 單位)
                    case 8:         // PG紅利小遊戲(紅利 單位)
                        count = Math.floor(count);
                        break;
                    default: break;
                }

                if (count > 0) {
                    // 描述
                    let desc: string = cv.config.getStringData(`ticketview_txt_desc_${info.currency_type}`);
                    if (desc.length > 0) {
                        this.txt_desc.string = desc;
                        this.txt_desc.node.active = true;
                    }

                    // 数量
                    this.txt_count.string = `✕${count}`;
                    this.txt_count.node.active = true;
                }
            }
        }, this);
    }

    private _setDesNodeSprite() {
        
        let isTicket:boolean = false;
        if(this.msg && this.msg[this.currentIndex]) {
            // currency_type 0 is ticket
            isTicket = this.msg[this.currentIndex].currency_type === 0? true : false;
        }

        this._desSpriteMap.forEach( (spriteNode:cc.Node, desSpriteType:DesSpriteType ) => {
            let spritePath = "hall/ticketView/wz_";
            if (isTicket) {
                
                //gift type is mtt ticket, change the text sprite
                switch (desSpriteType) {
                    case DesSpriteType.gotAGift:
                    case DesSpriteType.willBePlaced:
                    {
                        spritePath += "ticket_";
                        break;
                    }
                }
            }
            cv.resMgr.setSpriteFrame(spriteNode, cv.config.getLanguagePath(spritePath + desSpriteType));
        });
    }

    ReceiveToolsNotice(result: pb.ReceiveToolsNotice) {
        cv.dataHandler.getUserData().giftToolNotice = null;
        if(this._forceHide) return;
        let len = cv.StringTools.getArrayLength(result.toolsInfos);
        if (len <= 0) return;
        this.getData = true;
        TicketView.IS_VIEW = true;
        if (len <= 0) {
            TicketView.IS_VIEW = false;
            cv.MessageCenter.send("sortATLView");
            return;
        }
        this.reset(false);
        this.msg = result.toolsInfos.slice(0);

        this.download();
        cv.MessageCenter.send("sortATLView");
    }

    ReceiveToolsResponse(result: pb.ReceiveToolsResponse) {
        if(this._forceHide) return;
        if (result.error == 1) {
            this.currentIndex = this.currentIndex + 1;
            this.show();
        }
        else {
            TicketView.IS_VIEW = false;
            this.reset();
            cv.MessageCenter.send("sortATLView");
        }
    }

    download() {
        if (this.msg.length <= this.downloadIndex) return;
        let info = this.msg[this.downloadIndex];
        let url = cv.domainMgr.getServerInfo().image_server + (cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? info.imgAddr : info.imgAddrEn);
        let index = this.downloadIndex;

        let that = this;
        cv.resMgr.loadRemote(url, (error: Error, resource: cc.Texture2D) => {
            console.log("TicketView url = " + url);
            if (index < that.downloadIndex) return;
            if (error) {
                let index = cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? 0 : 1;

                that.downloadSpr.add(that.downloadIndex, that.defaultImg[index]);
            }
            else {
                that.downloadSpr.add(that.downloadIndex, new cc.SpriteFrame(resource));
            }

            if (that.currentIndex == that.downloadIndex && that.isClick) {
                that.playAni();
            }
            that.downloadIndex = that.downloadIndex + 1;
            that.download();
        });
    }

    downloadTimeOut() {
        if (this.currentIndex === this.downloadIndex) {
            let index = cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? 0 : 1;
            this.downloadSpr.add(this.downloadIndex, this.defaultImg[index]);
            this.downloadIndex = this.downloadIndex + 1;
            this.download();
        }
    }

    reset(isClose: boolean = true) {
        this.isClose = (isClose != false);
        this.pk.off("finished");
        this.pk.stop();
        this.node.active = false;
        this.canClick = false;
        this.isClick = false;
        this.canSure = false;
        this.downloadIndex = 0;
        this.currentIndex = 0;
        this.downloadSpr.clear();
        this.msg = [];
        cv.dataHandler.getUserData().giftToolNotice = null;
    }

    getNodeStateData(node: cc.Node) {
        let info = new NODE_SATE();
        info.position = node.position;
        info.rotation = node.angle;
        info.scale = cc.v2(node.scaleX, node.scaleY);
        info.opacity = node.opacity;
        this.stateMap.add(node.name, info);

        let len = node.childrenCount;
        for (let i = 0; i < len; i++) {
            this.getNodeStateData(node.children[i]);
        }
    }

    setNodeStateData(node: cc.Node) {
        let info = this.stateMap.get(node.name);
        if (info) {
            node.setPosition(info.position);
            node.angle = info.rotation;
            node.setScale(info.scale);
            node.opacity = info.opacity;
        }
        let len = node.childrenCount;
        for (let i = 0; i < len; i++) {
            this.setNodeStateData(node.children[i]);
        }
    }

    confirmTicket() {
        cv.AudioMgr.playButtonSound('button_click');
        if (!this.canSure) return;

        cv.worldNet.ReceiveToolsRequest(this.msg[this.currentIndex]);
    }

    onCloseBtnClicked() {
        this.confirmTicket();
    }

    onClaimBtnClicked() {
        this.confirmTicket();
        this._claimBtnClickCb?.();
    }

    //for mockTool use only
    private _mockShow(msg) {
        TicketView.IS_VIEW = true;
        this.getData = false;
        this.ReceiveToolsNotice(msg);
    }
}
