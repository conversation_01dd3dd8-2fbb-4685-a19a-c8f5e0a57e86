import LobbyFilterButtonBase from './LobbyFilterButtonBase';

const { ccclass, property } = cc._decorator;
// prefab:FastEnterNewFilters
@ccclass
export default abstract class BaseFilterController extends cc.Component {
    @property(cc.Label) titleLabel: cc.Label = null;
    @property(cc.Layout) layoutContainer: cc.Layout = null;
    @property(cc.Prefab) FilterButtonPrefab: cc.Prefab = null;
    public isActive: boolean = false;
    protected keyIndex = -1;
    protected readonly saveSeparatorChar = '|';

    public get KeyIndex()
    {
        return this.keyIndex;
    }

    public abstract get SAVE_KEY(): string;
    public abstract saveFilterDataToFile(): void;
    protected abstract onFilterClicked(Component: LobbyFilterButtonBase): void;

    private buttonsPool: cc.NodePool = new cc.NodePool();
    protected activeButtons: cc.Node[] = [];

    public abstract get filterData(): any[];

    protected onDestroy(): void {
        this.buttonsPool.clear();
    }
    
    public initLanguage():void{
        this.activeButtons.forEach(btn =>{
            const comp = btn.getComponent(LobbyFilterButtonBase);
            if(comp){
                comp.initLanguage();
            }
        })
    }

    protected emitClickEvent()
    {
        cc.game.emit('OnFilterButtonClicked');
    }

    protected getNewButton(): cc.Node
    {
        let object: cc.Node = null;

        if (this.buttonsPool.size() > 0) {
            object = this.buttonsPool.get();
        } else {
            object = cc.instantiate(this.FilterButtonPrefab);
        }
        object.setParent(this.layoutContainer.node);
        this.activeButtons.push(object);
        return object;
    }
}
