import borderGraphic from "../../../common/tools/borderGraphic";
import { CircleSprite } from "../../../common/tools/CircleSprite";
import { CurrencyType } from "../../../common/tools/Enum";
import { Tools } from "../../../common/tools/Tools";
import JackfruitRule from "../../../components/game/jackfruit/JackfruitRule";
import { RemarkData } from "../../../data/userData";
import cv from "../cv";
import { default as criticism, default as CriticismTips } from "./CriticismTips";
import { DiscoverGameType } from "./FindView";
import ws_protocol = require("../../../../Script/common/pb/ws_protocol");
import world_pb = ws_protocol.pb;

const { ccclass, property } = cc._decorator;
@ccclass
export default class FindItem extends cc.Component {

    msg: world_pb.ClubGameSnapshotV3 = null;
    name_text: cc.Label = null;
    needPassword: boolean = false;
    typename: string = "";
    @property(cc.Node) redEnvelope_node: cc.Node = null;
    @property(cc.Sprite) prize_festival_sprite: cc.Sprite = null;
    @property(cc.SpriteFrame) prize_festival_text_icon_English: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) prize_festival_text_icon_Chinese: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) prize_festival_text_icon_Vietnamese: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) prize_festival_text_icon_Hindi: cc.SpriteFrame = null;
    @property(cc.Prefab) criticism_prefab: cc.Prefab = null;
    @property(cc.Prefab) jackfruit_rule_prefab: cc.Prefab = null;
    @property(cc.Node) non_nlhe_item_holder: cc.Node = null;
    @property(cc.Label) non_nlhe_item_label: cc.Label = null;
    @property(cc.Node) tag_label_holder: cc.Node = null;
    @property(cc.Label) tag_label: cc.Label = null;
    @property(cc.Node) num_text_splash: cc.Node = null;
    @property(cc.Node) word_text_splash: cc.Node = null;
    @property(cc.Node) squid_icon: cc.Node = null;

    @property([cc.Sprite]) icon_Sprites: cc.Sprite[] = [];
    @property([cc.SpriteFrame]) golden_usd_Icon: cc.SpriteFrame[] = [];
    @property([cc.SpriteFrame]) white_usd_Icon: cc.SpriteFrame[] = [];
    @property(cc.Sprite) wpt_icon: cc.Sprite = null;
    @property([cc.SpriteFrame]) wpt_sf_list: cc.SpriteFrame[] = [];

    private  buy_in_holder : cc.Node = null;

    private _gameType: DiscoverGameType = 0 // DiscoverGameType.ALL;

    private _onCallback: Function = null;

    onLoad() {
        cv.resMgr.adaptWidget(this.node, true);
        this.buy_in_holder =  cc.find("icon_holder/buy_in_holder", this.node);
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);
        cv.MessageCenter.register("update_remarks", this.onUpdateRemark.bind(this), this.node);
    }

    start() {

    }

    onDestroy() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
        cv.MessageCenter.unregister("update_remarks", this.node);
    }

    private switchRedFestivalTextIconByLanguage(): void {

        switch (cv.config.getCurrentLanguage()) {
            case cv.Enum.LANGUAGE_TYPE.zh_CN:
                this.prize_festival_sprite.spriteFrame = this.prize_festival_text_icon_Chinese;
                break;
            case cv.Enum.LANGUAGE_TYPE.en_US:
                this.prize_festival_sprite.spriteFrame = this.prize_festival_text_icon_English;
                break;
            case cv.Enum.LANGUAGE_TYPE.yn_TH:
                this.prize_festival_sprite.spriteFrame = this.prize_festival_text_icon_Vietnamese;
                break;
            case cv.Enum.LANGUAGE_TYPE.hi_IN:
                this.prize_festival_sprite.spriteFrame = this.prize_festival_text_icon_Hindi;
                break;
            default:
                this.prize_festival_sprite.spriteFrame = this.prize_festival_text_icon_English;
                break;
        }
    }

    initLanguage() {
        if (!this.msg) return;
        const date: Date = new Date();
        const curTime: number = cv.StringTools.toFixed(date.getTime() / 1000.0, 0);
        let hasPlayTime: number = 0;
        if (this.msg.start_time !== 0) {
            hasPlayTime = (curTime - this.msg.start_time) / 3600.0;
        }

        this.name_text = cc.find("top/name_text", this.node).getComponent(cc.Label);
        const isLoose = this.msg.room_mode === world_pb.RoomMode.RoomModeLoose;
        if (this.tag_label) {
            this.tag_label.string = cv.config.getStringData(this.getLabelString());
        }

        const maxPlayer: number = this.msg.forceWithdrawMode ? this.msg.player_count_max : 0;
        if (isLoose) this.msg.room_name = this.msg.room_name.replace("LSHL", "HL");
        this.typename = cv.tools.displayChineseName(this.msg.room_name, maxPlayer);


        this.switchRedFestivalTextIconByLanguage();

        this.non_nlhe_item_label.fontSize = cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? 40 : 27 ; 

        if (this.wpt_icon !== null) {
            this.wpt_icon.spriteFrame = cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? this.wpt_sf_list[1] : this.wpt_sf_list[0];
        }

        if (this.msg.currencyType === CurrencyType.USDT) {
            cc.find("top/game_type/label", this.node).getComponent(cc.Label).string = cv.config.getStringData("USD_Cash_Game_Card");
        }

        const isZoom: boolean = cv.roomManager.checkGameIsZoom(this.msg.game_id);
        const timeLimit: number = cv.config.timeArr[this.msg.rule_time_limit - 1];
        const yanshiTime: number = cv.StringTools.toFixed(this.msg.extra_time / 3600.0, 1);
        const lastTimes = timeLimit - hasPlayTime + yanshiTime;

        const time_text = cc.find("time_text", this.node).getComponent(cc.Label);
        const time_img = cc.find("time_img", this.node);
        time_text.string = "";
        time_img.active = false;

        if (!isZoom && this.msg.game_id !== cv.Enum.GameId.Allin && this.msg.game_id !== cv.Enum.GameId.Bet && this.msg.game_id !== cv.Enum.GameId.Jackfruit) {//
            // 短牌不再显示时间
            const isShort = (this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short);
            if (this.msg.is_mirco === 1
                || this.msg.IscalcIncomePerhand
                || isShort) {
                time_text.string = "";
            }
            else if (lastTimes < 0) {
                time_text.string = cv.config.getStringData("UIfindlistview");
            }
            else if (lastTimes < 1.0 && (timeLimit + yanshiTime) < 1.0) {
                time_text.string =
                    cv.StringTools.formatC(cv.config.getStringData("UITitle128"), lastTimes * 60, (cv.config.timeArr[this.msg.rule_time_limit - 1] + yanshiTime) * 60);
            }
            else if (lastTimes < 1.0 && (timeLimit + yanshiTime) >= 1.0) {
                time_text.string =
                    cv.StringTools.formatC(cv.config.getStringData("UITitle127"), lastTimes * 60, cv.config.timeArr[this.msg.rule_time_limit - 1] + yanshiTime);
            }
            else {
                time_text.string =
                    cv.StringTools.formatC(cv.config.getStringData("UITitle126"), lastTimes, cv.config.timeArr[this.msg.rule_time_limit - 1] + yanshiTime);
            }
        }

        time_text.node.active = time_text.string !== "";
        time_img.active = time_text.node.active;
        if (time_img.active) {
            cv.resMgr.getLabelStringSize(time_text, time_text.string);// 这里设置一下，以获得当前帧文本的真实宽高
            time_img.x = time_text.node.x - time_text.node.getContentSize().width - 5;
        }

        let name: string = "";
        const zoom_table_people_num_text = cc.find("icon_holder/zoom_table_holder/zoom_table_people_num_text", this.node).getComponent(cc.Label);
        zoom_table_people_num_text.node.active = isZoom || this.msg.game_id === world_pb.GameId.PLO;
        cc.find("icon_holder/zoom_table_holder", this.node).active = isZoom || this.msg.game_id === world_pb.GameId.PLO;
        this.num_text_splash.active = false;
        this.word_text_splash.active = false;
        if (isZoom || this.msg.game_id === world_pb.GameId.PLO) {
            if (this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
                name = "DataView_data_panel_dataInfo_panel_zoomShortGame_button";
            } else {
                name = "DataView_data_panel_dataInfo_panel_zoomGame_button";
            }

            zoom_table_people_num_text.string = this.msg.player_count_max.toString();
        }
        else if (this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            if (this.msg.game_id === cv.Enum.GameId.Allin) {
                name = "DataView_data_panel_dataInfo_panel_aofGame_button";
            }
            else if (this.msg.game_id === cv.Enum.GameId.Bet) {
              
                this.updateView();
                return;
            }
            else {
                name = "DataView_data_panel_dataInfo_panel_normalGame_button";
                // 爆击德州
                // if(this.msg.isCriticismField == true){
                //     name = "DataView_data_panel_dataInfo_panel_BaoJi_button";
                // }
            }
        }
        else if (this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            if (this.msg.game_id === cv.Enum.GameId.Allin) {
                name = "DataView_data_panel_dataInfo_panel_aofGameShort_button";
            }
            else {
                name = "DataView_data_panel_dataInfo_panel_short_button";
            }

            // 爆击短牌
            // if(this.msg.isCriticismField == true){
            //     name = "DataView_data_panel_dataInfo_panel_BaoJi_Short";
            // }
        }
        if(!this.buy_in_holder)
            this.buy_in_holder =  cc.find("icon_holder/buy_in_holder", this.node);
        if (this._gameType === DiscoverGameType.DZPK && this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            this.buy_in_holder.active = true;
            const buy_in_text = cc.find("buy_in_text", this.buy_in_holder).getComponent(cc.Label);

            const cbBuyinMin: number = parseFloat(cv.StringTools.numToFloatString(this.msg.buyin_min));
            const buyinMin: string = cbBuyinMin >= 1000 ? cv.StringTools.formatC("%sK", (cbBuyinMin / 1000.0).toString()) : cbBuyinMin.toString();
            buy_in_text.string = cv.StringTools.formatC(cv.config.getStringData("FindItem_bet_mangZhu_text"), buyinMin);
        } else {
            this.buy_in_holder.active = false;
        }


        this.word_text_splash.getComponent(cc.Label).string = cv.config.getStringData(name);
        this.updateView();
    }

    updateSVReuseData(index: number, dataArray: Array<any>): void {
        // console.log("updateSVReuseData - " + index);
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;

        // let alpha: number = index % 2 == 0 ? 170 : 255;
        cc.find("bg_image", this.node).opacity = 255;

        this.msg = dataArray[index];

        this.initLanguage();
        this._updateRedEnvelope();
    }

    updateItemData(gameType: number, data: any, callBack?: Function): void {
        this._onCallback = callBack;
        // let alpha: number = index % 2 == 0 ? 170 : 255;
        cc.find("bg_image", this.node).opacity = 255;
        this._gameType = gameType;
        this.msg = data;
        this.non_nlhe_item_holder.active = false; 
        if(this.squid_icon) this.squid_icon.active = data.game_id === cv.Enum.GameId.Squid;

        if (gameType === DiscoverGameType.DZPK) {
            if (data.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short || data.game_id === cv.Enum.GameId.Bet) {
                this.non_nlhe_item_holder.active = true;
                const shortDeck_Color: cc.Color = cc.color(14, 111, 103);
                const splash_Color: cc.Color = cc.color(145, 38, 148);
                this.non_nlhe_item_holder.getComponent(borderGraphic).fillColor = data.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? shortDeck_Color : splash_Color;
                this.non_nlhe_item_holder.getComponent(borderGraphic).strokesColor = data.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? shortDeck_Color : splash_Color;
                this.non_nlhe_item_label.node.color = data.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? cc.color(130, 241, 207) : cc.color(255, 188, 224);
            }
        }
        this.tag_label_holder.active = this.msg.room_mode === world_pb.RoomMode.RoomModeLoose || this.msg.game_id === cv.Enum.GameId.Squid;
        
        if (this.msg.currencyType === CurrencyType.USDT) {
            this.wpt_icon.node.parent.active = true;
        }
        
        this.initLanguage();
        this._updateRedEnvelope();
    }

    updatecommunityImg() {
        const name_text = cc.find("top/name_text", this.node).getComponent(cc.Label);
        cv.resMgr.getLabelStringSize(name_text, name_text.string);
        const image_suo = cc.find("image_suo", this.node);
        const community_img = cc.find("top/community_img", this.node);
        if (image_suo.active) {
            community_img.setPosition(cc.v2(image_suo.x + 20 + community_img.width / 2, image_suo.y));
        } else {
            console.log("community_img.widht::::" + community_img.width);
            console.log("name_text.node.x" + name_text.node.x + "name_text.node.width" + name_text.node.width);
            community_img.setPosition(cc.v2(name_text.node.x + name_text.node.width + community_img.width * 0.5 + 20, name_text.node.y));
        }
    }


    onBtnRedEnvelopeClick(event: cc.Component.EventHandler) {

        cv.MessageCenter.send("onRedEnvelopeButtonClick");

    }

    updateView() {

        const isZoom: boolean = cv.roomManager.checkGameIsZoom(this.msg.game_id);

        this.name_text = cc.find("top/name_text", this.node).getComponent(cc.Label);
        const mangZhu_text = cc.find("icon_holder/chip_holder/mangZhu_text", this.node);

       

        if (this.msg.game_id === cv.Enum.GameId.Bet) {  // 如果是必下

            const roomInfo = cv.tools.getSplitRoominfo(this.msg.room_name);

            
            const roomType: string = roomInfo[0]; // 房间类型名称


            const roomNo: string = roomInfo[1];   // 房间号
          
            const cbAnte: number = cv.StringTools.serverGoldToShowNumber(this.msg.ante);
            const anteStr: string = cbAnte >= 1000 ? cv.StringTools.formatC("%sK", (cbAnte / 1000.0).toString()) : cbAnte.toString();

            let non_nlhe_string : string = "";
            if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
                this.typename = anteStr + " " + roomType + " " + roomNo;
                non_nlhe_string = anteStr+roomType;
            } else {
                this.typename = roomType + " " + cv.StringTools.formatC(cv.config.getStringData("FindItem_bet_text"), anteStr) + " " + roomNo;
                non_nlhe_string = roomType+" "+ (cv.StringTools.formatC(cv.config.getStringData("FindItem_bet_text"), anteStr)).replace(":"," ");
            }
            this.non_nlhe_item_label.string = non_nlhe_string;
            if (this._gameType === DiscoverGameType.DZPK) { // For Splash table name into NLHE tab
                this.typename = cv.tools.displayChineseName(this.msg.room_name.replace("AN","HL"));
            }
        }
        else {
            const maxPlayer: number = this.msg.forceWithdrawMode ? this.msg.player_count_max : 0;
            this.typename = cv.tools.displayChineseName(this.msg.room_name, maxPlayer);
        }


        const memberNum_text = cc.find("icon_holder/member_holder/memberNum_text", this.node).getComponent(cc.Label);
        if (isZoom || this.msg.game_id === world_pb.GameId.PLO) {
            memberNum_text.string = this.msg.player_count.toString();
            cv.resMgr.getLabelStringSize(memberNum_text, memberNum_text.string);// 这里设置一下，以获得当前帧文本的真实宽高
        }
        else {
            memberNum_text.string = this.msg.player_count + "/" + this.msg.player_count_max;
        }

        let mangZhu: string = "";
        this.word_text_splash.color = cc.Color.WHITE;

        const bombFlag = cc.find("bg_img/bombFlag", this.node);
        bombFlag.active = false;

        cc.find("jackfruit_node", this.node).active = this.msg.game_id === cv.Enum.GameId.Jackfruit;
        const cbMininumAmount: number = parseFloat(cv.StringTools.numToFloatString(this.msg.buyin_min));

        const bg_img = cc.find("bg_img", this.node).getComponent(borderGraphic);
        if (this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            mangZhu = "(%d)";
            const cbBigBlind: number = parseFloat(cv.StringTools.numToFloatString(this.msg.big_blind));
            const cbSmallBlind: number = parseFloat(cv.StringTools.numToFloatString(this.msg.small_blind));
            const cbBuyinMin: number = parseFloat(cv.StringTools.numToFloatString(this.msg.buyin_min));
            const cbStraddle = cbBigBlind * (2.0);

            const bigBlind: string = cbBigBlind >= 1000 ? cv.StringTools.formatC("%sK", (cbBigBlind / 1000.0).toString()) : cbBigBlind.toString();
            const smallBlind: string = cbSmallBlind >= 1000 ? cv.StringTools.formatC("%sK", (cbSmallBlind / 1000.0).toString()) : cbSmallBlind.toString();
            mangZhu_text.getComponent(cc.Label).string = (cv.StringTools.formatC("%s/%s", smallBlind.toString(), bigBlind.toString()));
            if (this.msg.straddle) {
                mangZhu_text.getComponent(cc.Label).string = mangZhu_text.getComponent(cc.Label).string + "/" + (cbStraddle >= 1000 ? cv.StringTools.formatC("%sK", (cbStraddle / 1000.0).toString()) : cbStraddle.toString());
            }

            if (this.msg.game_id === world_pb.GameId.PLO) {
                bg_img.fillColor = cc.color(104, 172, 81);
            } else if (this.msg.game_id === cv.Enum.GameId.Allin) {
                bg_img.fillColor = cc.color(145, 71, 67);
            } else if (this.msg.game_id === cv.Enum.GameId.Squid) {
                bg_img.fillColor = cc.color(245, 49, 115);
            } else if (this.msg.game_id === cv.Enum.GameId.Bet) {
                const buyinMin: string = cbBuyinMin >= 1000 ? cv.StringTools.formatC("%sK", (cbBuyinMin / 1000.0).toString()) : cbBuyinMin.toString();
                mangZhu_text.getComponent(cc.Label).string = cv.StringTools.formatC(cv.config.getStringData("FindItem_bet_mangZhu_text"), buyinMin);
                bg_img.fillColor = cc.color(158, 70, 119);
            } else if (this.msg.game_id === cv.Enum.GameId.Jackfruit) {
                const img = cc.find("jackfruit_node/minimum_img", this.node);
                const label = cc.find("jackfruit_node/minimum_label", this.node)
                label.getComponent(cc.Label).string = cbMininumAmount >= 1000 ? cv.StringTools.formatC("%sK", (cbMininumAmount / 1000.0).toString()) : cbMininumAmount.toString();
                const size = cv.resMgr.getLabelStringSize(label.getComponent(cc.Label));
                const pos = label.getPosition();
                img.setPosition(cc.v2(pos.x - size.width - 24, img.getPosition().y));
                bg_img.fillColor = cc.color(73, 98, 199);
            } else if (this.msg.room_mode === world_pb.RoomMode.RoomModeLoose) {
                bg_img.fillColor = cc.color(255,198,50); // #FFC632
            } else {

                bg_img.fillColor  = this.msg.room_mode === world_pb.RoomMode.RoomModeBomb? cc.color(174, 117, 64) : cc.color(63, 137, 209);
            }

        }
        else if (this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            mangZhu_text.getComponent(cc.Label).string = ("");
            if (this._gameType === DiscoverGameType.DZPK) {
                bg_img.fillColor = cc.color(149, 149, 194);
                this.non_nlhe_item_label.string = cv.config.getStringData('DataView_gameType_panel_button_1_text');
            }
            else {
                mangZhu = "%d";
                if (this.msg.game_id === cv.Enum.GameId.Allin) {
                    bg_img.fillColor = cc.color(145, 71, 67);
                }else if (this.msg.game_id === cv.Enum.GameId.Squid) {
                    bg_img.fillColor = cc.color(245, 49, 115);
                }  else if (this.msg.game_id === cv.Enum.GameId.Jackfruit) {
                    const img = cc.find("jackfruit_node/minimum_img", this.node);
                    const label = cc.find("jackfruit_node/minimum_label", this.node)
                    label.getComponent(cc.Label).string = cbMininumAmount >= 1000 ? cv.StringTools.formatC("%sK", (cbMininumAmount / 1000.0).toString()) : cbMininumAmount.toString();
                    const size = cv.resMgr.getLabelStringSize(label.getComponent(cc.Label));
                    const pos = label.getPosition();
                    img.setPosition(cc.v2(pos.x - size.width - 24, img.getPosition().y));
                    bg_img.fillColor = cc.color(73, 98, 199);
                }
                else {
                    // 当前是爆击场
                    // eslint-disable-next-line no-lonely-if
                    if (this.msg.room_mode === world_pb.RoomMode.RoomModeBomb) {
                        bg_img.fillColor = cc.color(174, 117, 64);
                        // bombFlag.active = true;
                    } else {
                        bg_img.fillColor = cc.color(49, 109, 82);
                    }
                }
            }
        }

        if (isZoom) {
            if (this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
                bg_img.fillColor = cc.color(122, 69, 168);
            } else {
                bg_img.fillColor = cc.color(122, 69, 168);
            }
        }

        this.icon_Sprites[0].spriteFrame =this.white_usd_Icon[0]; // Icon "Image_4_1" set to chips icon normally 
        if(this.msg.game_id === world_pb.GameId.Bet) 
        {
            this.buy_in_holder.active = false;
            if(this.msg.currencyType === CurrencyType.GOLD)
            {
                this.icon_Sprites[0].spriteFrame = this.golden_usd_Icon[0];  // Icon "Image_4_1" set to Buy_in icon for Splash CNY games 
            }
        }
        if ( this.msg.currencyType === CurrencyType.USDT) {

            // AT-3735-> For all USD tables, use white icons same as NLHE.
            this.icon_Sprites[0].spriteFrame =this.white_usd_Icon[0];
            this.icon_Sprites[1].spriteFrame =this.white_usd_Icon[1]; 
            this.icon_Sprites[2].spriteFrame = this.white_usd_Icon[2];
            this.icon_Sprites[3].spriteFrame =this.white_usd_Icon[3]; 
            
        }


        /*
                let antiImg = cc.find("antiImg", this.node);
                let stardlleImg = cc.find("stardlleImg", this.node);
                let insuranceImg = cc.find("insuranceImg", this.node);
                let gpsImg = cc.find("gpsImg", this.node);
                let jackpotImg = cc.find("jackpotImg", this.node);
                let aofImg = cc.find("aofImg", this.node);
                let fullHouseImg = cc.find("fullHouseImg", this.node);
                let recallImg = cc.find("recallImg", this.node);
                let showCardImg = cc.find("showCardImg", this.node); */
        const image_suo = cc.find("image_suo", this.node);

        // let maximum_length_allowed =   (cv.config.getCurrentLanguage() == LANGUAGE_TYPE.zh_CN) ? 6 : 12;
        // this.typename = this.typename.substring(0,maximum_length_allowed);

        cv.resMgr.getLabelStringSize(this.name_text, this.typename);
        // const posX = this.name_text.node.x + name_text_size.width;

        // if (cv.StringTools.getArrayLength(this.msg.join_password) > 0 || cv.StringTools.getArrayLength(this.msg.buyin_password) > 0) {
        //     image_suo.active = true;
        //     image_suo.setPosition(cc.v2(posX + image_suo.width * 0.5 + 20, this.name_text.node.y));
        //     // cv.resMgr.setSpriteFrame(image_suo, "hall/common_paizhuo");
        // }
        // else {
        // }
        image_suo.active = false;



        cv.resMgr.getLabelStringSize(this.name_text, this.name_text.string);// 这里设置一下，以获得当前帧文本的真实宽高

        if (this.msg.ante && this.msg.game_id !== cv.Enum.GameId.Bet) {
            /* antiImg.active = true;
            posX += 30;
            antiImg.setPosition(posX, this.name_text.getPosition().y);
*/
            // if (this.msg.ante < 100) {
            //     mangZhu = mangZhu.replace("%d", this.getStringFormat(this.msg.ante * 0.01));
            // }
            if (mangZhu_text.getComponent(cc.Label).string !== "") {
                mangZhu_text.getComponent(cc.Label).string += ("(" + cv.StringTools.numberToString(this.msg.ante * 0.01) + ")");
            }
            else {
                mangZhu_text.getComponent(cc.Label).string += cv.StringTools.numberToString(this.msg.ante * 0.01);
            }
            // let x = mangZhu_text.getPositionX() + mangZhu_text.getContentSize().width + 30;
            // if (x > )

        }
        else {
            // antiImg.active = false;
        }

        cc.find("icon_holder/icon_hot", this.node).active = isZoom && this.msg.player_count > 40;

        /* let isShortMode = (this.msg.game_mode == cv.Enum.CreateGameMode.CreateGame_Mode_Short);
        posX = this.showGamePropty(this.msg.straddle, stardlleImg, posX);
        posX = this.showGamePropty(this.msg.insurance, insuranceImg, posX);
        posX = this.showGamePropty(this.msg.anti_cheating, gpsImg, posX);
        posX = this.showGamePropty(this.msg.jackpot_isopen, jackpotImg, posX);
        posX = this.showGamePropty(this.msg.is_allin_allfold, aofImg, posX);
        posX = this.showGamePropty(this.msg.straddle, stardlleImg, posX);
        posX = this.showGamePropty(this.msg.straddle, stardlleImg, posX);
        posX = this.showGamePropty(isShortMode, fullHouseImg, posX);
        posX = this.showGamePropty(this.msg.is_opened_drawback, recallImg, posX);
        posX = this.showGamePropty(this.msg.is_force_showcard, showCardImg, posX);
        if (isShortMode && this.msg.short_fullhouse_flush_straight_three) {
            cv.resMgr.setSpriteFrame(fullHouseImg, "zh_CN/hall/ui/common_icon_hu");
        }

        antiImg.active = false;
        stardlleImg.active = false;
        insuranceImg.active = false;
        gpsImg.active = false;
        jackpotImg.active = false;
        aofImg.active = false;
        fullHouseImg.active = false;
        recallImg.active = false;
        showCardImg.active = false; */

        const community_img = cc.find("top/community_img", this.node);
        console.log("has_buyin" + this.msg.has_buyin);
        community_img.active = (this.msg.has_buyin !== 0 && this.msg.has_buyin != undefined);

        const isShort = (this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short);
        if (this.msg.is_mirco === 1
            || this.msg.IscalcIncomePerhand
            || isZoom
            || isShort) {
            community_img.active = false;
        } else {
            community_img.active = this.msg.has_buyin !== 0;
        }
        if (community_img.active) {
            if (this.msg.has_buyin === 1) {
                cv.resMgr.setSpriteFrame(community_img, cv.config.getLanguagePath("hall/ui/common_biaoqian1"), this.updatecommunityImg.bind(this));
            }
            else if (this.msg.has_buyin === 2) {
                cv.resMgr.setSpriteFrame(community_img, cv.config.getLanguagePath("hall/ui/common_biaoqian2"), this.updatecommunityImg.bind(this));
            }
        }

        // 设置mvp头像  这里需要判断是否有数据，因为mvp有开关，当开关关闭时，没有此字段
        // 微局长牌，所有短牌，必下，急速，这就是所有把抽的
        if ((this.msg.is_mirco
            || this.msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short
            || this.msg.game_id === cv.Enum.GameId.Bet
            || this.msg.IscalcIncomePerhand
            || isZoom) && this.msg.mvp_data) {
            const mvpdata = this.msg.mvp_data;
            const mvpImg = cc.find("mvpbg", this.node);
            mvpImg.active = mvpdata.uid > 0;
            if (mvpImg.active) {
                const mvpName: cc.Label = cc.find("mvpbg/mvpName", this.node).getComponent(cc.Label);
                // 设置名称，有备注则显示备注
                const remarkData: RemarkData = cv.dataHandler.getUserData().getRemarkData(mvpdata.uid)
                let mvpStr = "";
                if (remarkData.nUid == 0) {
                    mvpStr = mvpdata.nickname;
                }
                else if (remarkData.sRemark.length == 0) {
                    mvpStr = mvpdata.nickname;
                }
                else {
                    mvpStr = remarkData.sRemark;
                }
                cv.StringTools.setShrinkString(mvpName.node, mvpStr, true);
                // 设置头像
                CircleSprite.setCircleSprite(cc.find("mvpbg/mvpbg", this.node), Tools.checkAvatar(mvpdata.thumb), this.msg.mvp_data.plat, false);
            }
        } else {
            cc.find("mvpbg", this.node).active = false;
        }
    }


    onEntryRoom() {
        cv.GameDataManager.tRoomData.pkRoomParam.currencyType = this.msg.currencyType;
        cv.roomManager.RequestJoinRoom(this.msg.game_id, this.msg.room_id, false, this.needPassword);
    }

    showCriticismGuide(msg: any) {
        const _gameNode: cc.Node = criticism.getSinglePrefabInst(this.criticism_prefab);
        const _criticism = _gameNode.getComponent(CriticismTips);
        _criticism.autoShow(cc.director.getScene(), msg, cv.Enum.ZORDER_TYPE.ZORDER_TOP);
        _criticism.setSureFunc(this.onEntryRoom.bind(this));

    }

    onBtnItemClick(event: cc.Component.EventHandler) {
        if (cv.dataHandler.getUserData().isban) {
            cv.TT.showMsg(cv.config.getStringData("ServerErrorCode501"), cv.Enum.ToastType.ToastTypeInfo);
            return;
        }
        
        if (cv.native.IsSimulator() && !this.msg.anti_simulator) {
            cv.TT.showMsg(cv.config.getStringData("UIEmulatorErrorText"), cv.Enum.ToastType.ToastTypeInfo);
            return;
        }

        if (!this._onCallback){
            cv.reCaptcha.checkRecaptcha(this._joinRoom.bind(this));
            return;
        }

        const itemData = {
            gameId: this.msg.game_id,
            roomId: this.msg.room_id,
            funcJoinRoom: () => cv.reCaptcha.checkRecaptcha(this._joinRoom.bind(this))
        };
        
        this._onCallback?.(itemData);
    }

    private _joinRoom(captchaPassed: boolean) {
        // did not pass captcha test
        if (captchaPassed == false) {
            return;
        }
        cc.find("bg_image", this.node).opacity = 170;
        // let bgNode = cc.find("bg_image", this.node);
        // cv.resMgr.setSpriteFrame(bgNode, "zh_CN/common/icon/common_selected_bg");
        // bgNode.setContentSize(this.node.getContentSize());

        const str = cv.tools.GetStringByCCFile("hideJackfruitRule");
        const isOpen = str === "" || str == null;
        if (this.msg.game_id === cv.Enum.GameId.Jackfruit && isOpen) {
            const _jackfruitRule = JackfruitRule.getSinglePrefabInst(this.jackfruit_rule_prefab).getComponent(JackfruitRule);
            _jackfruitRule.setData(this.msg);
            _jackfruitRule.setNeedPassword(false);
            _jackfruitRule.show();
        } else {
            cv.GameDataManager.tRoomData.u32GameID = this.msg.game_id;
            cv.GameDataManager.tRoomData.pkRoomParam.currencyType = this.msg.currencyType;
            this.needPassword = false;
            cv.roomManager.RequestJoinRoom(this.msg.game_id, this.msg.room_id, false, false);
        }
    }

    showGamePropty(isShow: boolean, node: cc.Node, posX: number): number {
        if (isShow) {
            node.active = true;
            posX += 50;
            node.setPosition(posX, this.name_text.node.getPosition().y);
        }
        else {
            node.active = false;
        }
        return posX;
    }

    // Toggle red Envelope node as per backend response
    _updateRedEnvelope(): void {
        this.redEnvelope_node.active = this.msg.red_envelope_switch;
    }
    
    onUpdateRemark() {
        const mvpImg = cc.find("mvpbg", this.node);
        if (mvpImg && mvpImg.active) {
            const mvpdata = this.msg.mvp_data;
            if (this.msg.mvp_data) {
                const mvpName: cc.Label = cc.find("mvpbg/mvpName", this.node).getComponent(cc.Label);
                // 设置名称，有备注则显示备注
                const remarkData: RemarkData = cv.dataHandler.getUserData().getRemarkData(mvpdata.uid)
                let mvpStr = "";
                if (remarkData.nUid === 0) {
                    mvpStr = mvpdata.nickname;
                }
                else if (remarkData.sRemark.length === 0) {
                    mvpStr = mvpdata.nickname;
                }
                else {
                    mvpStr = remarkData.sRemark;
                }
                cv.StringTools.setShrinkString(mvpName.node, mvpStr, true);
            }
        }
    }

    getLabelString(): string {
        if (this.msg.game_id === cv.Enum.GameId.Squid) {
            return this.msg.squidHuntGameData?.mode === world_pb.SquidHuntGameData.Mode.MULTIPLIER_MODE 
                ? "Double_squid_title" 
                : "Squid_hunt_title";
        }
        return "Filter_Mode_Loose_Text"; // Default & for this.msg.room_mode === world_pb.RoomMode.RoomModeLoose
    }

}