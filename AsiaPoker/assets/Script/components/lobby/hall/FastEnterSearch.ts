import cv from "../cv";

const {ccclass, property} = cc._decorator;

@ccclass
export default class FastEnterSearch extends cc.Component {
    @property(cc.EditBox) search: cc.EditBox = null;
    @property(cc.Button) searchBtn: cc.Button = null;

    protected onEnable():void{
        this._setText();
    }

    onTextChanged()
    {
        let str = this.search.string;
        str = cv.StringTools.earseNoNumber(str);
        this.search.string = str;
    }

    private _setText():void{
        this.search.placeholder =  cv.config.getStringData('jackfruit_find_view_search_label');
    }
}
