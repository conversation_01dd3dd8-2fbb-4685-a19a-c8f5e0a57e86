import { CircleSprite } from "../../../common/tools/CircleSprite";
import cv from '../cv';
import starDetails from './starDetail';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import borderGraphic from '../../../common/tools/borderGraphic';

const { ccclass, property } = cc._decorator;
@ccclass
export default class FindItemStar extends cc.Component {
    @property(cc.Prefab)
    starDetail_prefab: cc.Prefab = null;

    @property(cc.Sprite)
    wpt_icon_sprite: cc.Sprite = null;

    @property(cc.SpriteFrame)
    wpt_icons: cc.SpriteFrame[] = [];

    @property(cc.SpriteFrame)
    goldCoinPeopleSpriteFrame: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    usdPeopleSpriteFrame: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    goldCoinCurrencySpriteFrame: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    usdCurrencySpriteFrame: cc.SpriteFrame = null;

    @property(cc.Sprite)
    peopleSprite: cc.Sprite = null;

    @property(cc.Sprite)
    currencySprite: cc.Sprite = null;

    @property(cc.Node)
    countdownHolder: cc.Node = null;

    @property(cc.RichText)
    countdownTimer: cc.RichText = null;

    @property(cc.Node)
    starInfoRoot: cc.Node = null;

    @property(cc.Label)
    starDescription: cc.Label = null;

    @property(cc.Node)
    starDescBG: cc.Node = null;

    _msg: world_pb.ClubGameSnapshotV3 = null;
    _needPassword: boolean = false;

    _timeLeft: number = 0;
    _intervalCallback = null;

    private _onCallback: Function = null;

    onLoad() {
        cv.resMgr.adaptWidget(this.node, true);
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this._initLanguage.bind(this), this.node);
        cv.MessageCenter.register('onCheckStarRoomResponse', this._onCheckStatus.bind(this), this.node);
    }

    onDestroy() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
        cv.MessageCenter.unregister('onCheckStarRoomResponse', this.node);
    }

    onEnable(): void {
        this._initLanguage();
    }

    protected onDisable(): void {
        clearInterval(this._intervalCallback);
        this._intervalCallback = null;
    }

    updateItemData(data: any, callBack?: Function): void {
        this._onCallback = callBack;
        cc.find('bg_image', this.node).opacity = 255;
        this._msg = data;
        this._initLanguage();
    }

    onBtnItemClick(event: cc.Component.EventHandler) {
        if (cv.native.IsSimulator() && !this._msg.anti_simulator) {
            cv.TT.showMsg(cv.config.getStringData("UIEmulatorErrorText"), cv.Enum.ToastType.ToastTypeInfo);
            return;
        }
        
        if (!this._onCallback){
            cv.reCaptcha.checkRecaptcha(this._joinRoom.bind(this));
            return;
        }
        
        const itemData = {
            gameId: this._msg.game_id,
            roomId: this._msg.room_id,
            funcJoinRoom: () => cv.reCaptcha.checkRecaptcha(this._joinRoom.bind(this))
        };
        
        this._onCallback?.(itemData);
    }

    // 点击明星头像
    onBtnClickStarHead1(event: cc.Component.EventHandler) {
        const starData = this._msg.starData;
        const star_uids: number[] = [];
        if (starData && starData.length > 0) {
            // 所有明星的UID都需要发给服务器，点击哪个明星，哪个明星排在前面
            if (starData[0] && starData[0].uid) {
                star_uids.push(starData[0].uid);
            }
            if (starData[1] && starData[1].uid) {
                star_uids.push(starData[1].uid);
            }

            cv.worldNet.StarDetailInfoRequest(star_uids);
            const _starDetail = starDetails.getSinglePrefabInst(this.starDetail_prefab).getComponent(starDetails);
            _starDetail.setData(this.onBtnItemClick.bind(this), 1);
        }
    }

    // 点击明星头像
    onBtnClickStarHead2(event: cc.Component.EventHandler) {
        const starData = this._msg.starData;
        const star_uids: number[] = [];
        if (starData && starData.length > 0) {
            // 所有明星的UID都需要发给服务器，点击哪个明星，哪个明星排在前面
            if (starData[1] && starData[1].uid) {
                star_uids.push(starData[1].uid);
            } else {
                // 第二个明星头像为空
                return;
            }
            if (starData[0] && starData[0].uid) {
                star_uids.push(starData[0].uid);
            }

            const _starDetail = starDetails.getSinglePrefabInst(this.starDetail_prefab).getComponent(starDetails);
            _starDetail.setData(this.onBtnItemClick.bind(this), 2);
            cv.worldNet.StarDetailInfoRequest(star_uids);
        }
    }

    _initLanguage() {
        if (!this._msg) return;
        this._updateView();
    }

    _updateView() {
        const isZoom: boolean = cv.roomManager.checkGameIsZoom(this._msg.game_id);

        //  room_name就是明星桌提示
        const room_name = cc.find('roomStatus/room_name', this.node).getComponent(cc.Label);

        const _roomName = this._msg.room_name;
        this.countdownHolder.getComponent(cc.Layout).paddingRight =
            cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? 20 : 40;

        const roomArray = _roomName.split('#');
        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            //  中文
            room_name.string = roomArray[0];
        } else if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.yn_TH) {
            //  越南文
            room_name.string = roomArray[2];
        } else {
            //  其它默认英文
            room_name.string = roomArray[1];
        }

        cc.find("bg_img", this.node).getComponent(borderGraphic).fillColor = cc.color(226, 212, 150);

        const mangZhu_text = cc.find('manzhu/mangZhu_text', this.node);

        const memberNum_text = cc.find('member_img/memberNum_text', this.node).getComponent(cc.Label);
        if (isZoom) {
            memberNum_text.string = this._msg.player_count.toString();
            cv.resMgr.getLabelStringSize(memberNum_text, memberNum_text.string); // 这里设置一下，以获得当前帧文本的真实宽高
        } else {
            cc.find('member_img/memberNum_text', this.node).getComponent(cc.Label).string =
                this._msg.player_count + '/' + this._msg.player_count_max;
        }

        let mangZhu: string = '';
        const cbMininumAmount: number = parseFloat(cv.StringTools.numToFloatString(this._msg.buyin_min));

        if (this._msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            mangZhu = '(%d)';
            const cbBigBlind: number = parseFloat(cv.StringTools.numToFloatString(this._msg.big_blind));
            const cbSmallBlind: number = parseFloat(cv.StringTools.numToFloatString(this._msg.small_blind));
            const cbBuyinMin: number = parseFloat(cv.StringTools.numToFloatString(this._msg.buyin_min));
            const cbStraddle = cbBigBlind * 2.0;

            const bigBlind: string =
                cbBigBlind >= 1000
                    ? cv.StringTools.formatC('%sK', (cbBigBlind / 1000.0).toString())
                    : cbBigBlind.toString();
            const smallBlind: string =
                cbSmallBlind >= 1000
                    ? cv.StringTools.formatC('%sK', (cbSmallBlind / 1000.0).toString())
                    : cbSmallBlind.toString();
            mangZhu_text.getComponent(cc.Label).string = cv.StringTools.formatC(
                '%s/%s',
                smallBlind.toString(),
                bigBlind.toString()
            );
            if (this._msg.straddle) {
                mangZhu_text.getComponent(cc.Label).string =
                    mangZhu_text.getComponent(cc.Label).string +
                    '/' +
                    (cbStraddle >= 1000
                        ? cv.StringTools.formatC('%sK', (cbStraddle / 1000.0).toString())
                        : cbStraddle.toString());
            }

            if (this._msg.game_id === cv.Enum.GameId.Allin) {
                //  cc.find("bg_img", this.node).getComponent(borderGraphic).fillColor = cc.color(145, 71, 67);
                //  cv.resMgr.setSpriteFrame(cc.find("bg_img", this.node), "zh_CN/hall/lobby/common_aof");
            } else if (this._msg.game_id === cv.Enum.GameId.Bet) {
                const buyinMin: string =
                    cbBuyinMin >= 1000
                        ? cv.StringTools.formatC('%sK', (cbBuyinMin / 1000.0).toString())
                        : cbBuyinMin.toString();
                mangZhu_text.getComponent(cc.Label).string = cv.StringTools.formatC(
                    cv.config.getStringData('FindItem_bet_mangZhu_text'),
                    buyinMin
                );
            } else if (this._msg.game_id === cv.Enum.GameId.Jackfruit) {
                const img = cc.find('jackfruit_node/minimum_img', this.node);
                const label = cc.find('jackfruit_node/minimum_label', this.node);
                label.getComponent(cc.Label).string =
                    cbMininumAmount >= 1000
                        ? cv.StringTools.formatC('%sK', (cbMininumAmount / 1000.0).toString())
                        : cbMininumAmount.toString();
                const size = cv.resMgr.getLabelStringSize(label.getComponent(cc.Label));
                const pos = label.getPosition();
                img.setPosition(cc.v2(pos.x - size.width - 24, img.getPosition().y));
            }
        } else if (this._msg.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            mangZhu = '%d';
            mangZhu_text.getComponent(cc.Label).string = '';
            if (this._msg.game_id === cv.Enum.GameId.Allin) {
                cc.find("bg_img", this.node).getComponent(borderGraphic).fillColor = cc.color(145, 71, 67);
            } else if (this._msg.game_id === cv.Enum.GameId.Jackfruit) {
                const img = cc.find('jackfruit_node/minimum_img', this.node);
                const label = cc.find('jackfruit_node/minimum_label', this.node);
                label.getComponent(cc.Label).string =
                    cbMininumAmount >= 1000
                        ? cv.StringTools.formatC('%sK', (cbMininumAmount / 1000.0).toString())
                        : cbMininumAmount.toString();
                const size = cv.resMgr.getLabelStringSize(label.getComponent(cc.Label));
                const pos = label.getPosition();
                img.setPosition(cc.v2(pos.x - size.width - 24, img.getPosition().y));
            }
        }

        if (this._msg.ante && this._msg.game_id !== cv.Enum.GameId.Bet) {
            if (mangZhu_text.getComponent(cc.Label).string !== '') {
                mangZhu_text.getComponent(cc.Label).string +=
                    '(' + cv.StringTools.numberToString(this._msg.ante * 0.01) + ')';
            } else {
                mangZhu_text.getComponent(cc.Label).string += cv.StringTools.numberToString(this._msg.ante * 0.01);
            }
        }

        const member_img = cc.find('member_img', this.node);
        const begin_x = mangZhu_text.parent.getPosition().x; // mangZhu_text.getPosition().x
        member_img.setPosition(
            cc.v2(
                begin_x + cv.resMgr.getLabelStringSize(mangZhu_text.getComponent(cc.Label)).width + 40,
                member_img.getPosition().y
            )
        );

        // 观战人数
        const viewsNumber = cc.find('number', this.node).getComponent(cc.Label);
        viewsNumber.string = this._msg.bystanderNum.toString();
        const viewsPerson = cc.find('viewsPerson', this.node);
        viewsPerson.setPosition(
            cc.v2(
                viewsNumber.node.x - cv.resMgr.getLabelStringSize(viewsNumber.getComponent(cc.Label)).width - 5,
                viewsPerson.getPosition().y
            )
        );

        const starData = this._msg.starData; // 明星数据
        const len = starData.length > 2 ? 2 : starData.length;

        this._resetStarItem();

        let width = 0;      // This is the total width occupied by stars info. Will be later used to determine width of star tag.

        const startInfoLayout = this.starInfoRoot.getComponent(cc.Layout);

        let _StarOnline = false; // 是否还有明星在线

        width += startInfoLayout.paddingLeft;

        for (let i = 0; i < len; i++) {
            this.starInfoRoot.children[i].active = true;
            const headNode = cc.find(cv.StringTools.formatC('starHead%d', i + 1), this.starInfoRoot.children[i]);
            const nameNode = cc.find(cv.StringTools.formatC('starName%d', i + 1), this.starInfoRoot.children[i]);

            headNode.active = true;

            const starHead = headNode.getChildByName('starHead'); // 头像
            const starName = nameNode.getChildByName('starName'); // 名称

            const CelebrityFX = headNode.getChildByName('CelebrityFX'); // 头像动画
            const avatar = starData[i].thumb;
            if (avatar) {
                CircleSprite.setCircleSprite(starHead, avatar, 0, false);
            }

            // Commented because no longer required as it is all handled by layout in prefab.
            // 判断第二个头像的位置, 最少与第一个明星信息间距40个像素
            // if (i === 1) {
            //     headNode.x = this._headNodeX; // 防止item是clone的，使用上个item的x坐标
            //     const headBeginX = headNode.x - headNode.getContentSize().width / 2; // 第二个头像初始化位置
            //     const starName1 = cc.find('starName1', this.starInfoRoot);
            //     const name1EndX = starName1.x + starName1.getContentSize().width;
            //     if (headBeginX < name1EndX + 40) {
            //         // 第二个头像，与第一个明星间隔小于40个像素
            //         headNode.x = name1EndX + headNode.getContentSize().width / 2 + 40;
            //     }
            // } else {
            //     cv.resMgr.adaptWidget(headNode, true);
            // }

            if (starData[i].nickname.length > 0) {
                nameNode.active = true;
                const nameLabel = starName.getComponent(cc.Label);
                nameLabel.string = starData[i].nickname;
                const labelW = cv.resMgr.getLabelStringSize(nameLabel).width;
                nameNode.width = labelW + 40;
                nameNode.x = headNode.getContentSize().width - 10;
                const nameRect = nameNode.getBoundingBox();
                const headRect = headNode.getBoundingBox();
                const newSize = new cc.Rect();
                headRect.union(newSize, nameRect);
                this.starInfoRoot.children[i].setContentSize(newSize.size);
                width += newSize.width;
            } else {
                nameNode.active = false;
                this.starInfoRoot.children[i].setContentSize(headNode.getContentSize());
            }

            const status = starData[i].status; // 0.未开播  1. 在线 2.已下播
            if (status !== 2) {
                // 有明星桌未开播，明星在线，Live icon状态设置为true
                _StarOnline = true;
            }

            const txtStatus = headNode.getChildByName('txtStatus');
            txtStatus.getComponent(cc.Label).string = cv.config.getStringData('Star_live_offline');
            const maskStatus = headNode.getChildByName('maskDark');

            txtStatus.active = false;
            maskStatus.active = false;
            CelebrityFX.active = true;

            if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
                txtStatus.getComponent(cc.Label).fontSize = 36;
            } else {
                txtStatus.getComponent(cc.Label).fontSize = 26;
            }
            width += startInfoLayout.spacingX;
        }

        let subtitle: string = "";
        const length = Object.keys(this._msg.room_sub_title).length;

        if(length > 0)
        {
            const langKey = (cv.config.getCurrentLanguage() in this._msg.room_sub_title) ? cv.config.getCurrentLanguage() : cv.Enum.LANGUAGE_TYPE.en_US;
            subtitle = this._msg.room_sub_title[langKey];
        }
        
        this.setStarTag(subtitle, len, width, startInfoLayout.paddingRight);

        const live_icon = cc.find('roomStatus/live_icon', this.node);
        if (!_StarOnline) {
            // 该桌子明星已经下播，房间名称前面的live标识消失
            live_icon.active = false;
            room_name.node.setPosition(cc.v2(live_icon.x - live_icon.getContentSize().width / 2, room_name.node.y));
        } else {
            room_name.node.setPosition(
                cc.v2(live_icon.x + live_icon.getContentSize().width / 2 + 11, room_name.node.y)
            );
            live_icon.active = true;
        }

        //  check if its usd table
        if (this._msg.currencyType === 101) {
            this.wpt_icon_sprite.node.active = true;
            this.wpt_icon_sprite.spriteFrame =
                this.wpt_icons[cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? 0 : 1];

            this.currencySprite.spriteFrame = this.usdCurrencySpriteFrame;
            this.peopleSprite.spriteFrame = this.usdPeopleSpriteFrame;
        } else {
            this.wpt_icon_sprite.node.active = false;

            this.currencySprite.spriteFrame = this.goldCoinCurrencySpriteFrame;
            this.peopleSprite.spriteFrame = this.goldCoinPeopleSpriteFrame;
        }

        this._showCountdownTime();
    }

    // AT-4516: New Tag object in Star Seat.
    // NOTE: All constants here are derived from Figma. They are NOT magic numbers! 
    setStarTag(tagString: string, noOfStars: number, widthOfStars: number, rightPadding: number)
    {
        if(tagString && tagString.trim())
        {
            this.starDescription.node.parent.active = true;
            this.starDescription.node.parent.color = cc.Color.TRANSPARENT;
            this.starDescription.string = tagString;
            const desiredWidth = cv.resMgr.getLabelStringSize(this.starDescription).width + 48;     // Desired width of Tag-BG
            const maxWidth = this.starInfoRoot.width - widthOfStars - rightPadding;
            
            this.starDescBG.width = cv.StringTools.clamp(desiredWidth, 73, maxWidth);
            const maxLabelWidth = cv.StringTools.clamp(desiredWidth-48, 25, maxWidth - 48);
            cv.StringTools.truncateLabelText(this.starDescription, maxLabelWidth);
            this.starDescBG.setPosition(this.starDescBG.width * 0.5, 0);
            this.starDescription.node.setPosition(this.starDescBG.width * 0.5, 0);      // This will ensure that text is in center of the bg image.
            
            this.starInfoRoot.getComponent(cc.Layout).updateLayout();

            this.scheduleOnce(()=>{
                this.starDescription.node.parent.color = cc.Color.WHITE;
            });
        }
        else        // If tag string is null or empty
        {
            this.starDescription.node.parent.active = false;
        }

        this.starInfoRoot.getComponent(cc.Layout).updateLayout();
    }

    _onCheckStatus(data: any) {
        const _error = data.error; // error等于1得时候表示正常进入明星座
        const _roomId = data.roomId;
        const _notifyTime = data.notifyTime; // 开启时间
        
        if (_roomId !== this._msg.room_id) {
            // 不是当前房间的消息
            console.log('_onCheckStatus  star roomID is error _roomId=' + _roomId);
            return;
        }

        cv.SwitchLoadingView.hide();

        if (_error !== 1) {
            if (_error === 255) {
                //  255表示提示状态是xx:xx时间准时开启
                const dateTime = new Date(_notifyTime * 1000); // 服务器返回的时间戳单位是秒，此处要x1000
                const tips = cv.StringTools.formatC(
                    '%02d:%02d%s',
                    dateTime.getHours(),
                    dateTime.getMinutes(),
                    cv.config.getStringData('ServerErrorCode255')
                );
                cv.TT.showMsg(tips, cv.Enum.ToastType.ToastTypeInfo);
            } else if (_error === 256) {
                // 房间已解散
                cv.TT.showMsg(cv.config.getStringData('ServerErrorCode256'), cv.Enum.ToastType.ToastTypeInfo);
            }
            return;
        }

        if (cv.dataHandler.getUserData().isban) {
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode501'), cv.Enum.ToastType.ToastTypeInfo);
            return;
        }

        cc.find('bg_image', this.node).opacity = 170;

        const str = cv.tools.GetStringByCCFile('hideJackfruitRule');
        const isOpen = str === '' || str === null;
        if (!(this._msg.game_id === cv.Enum.GameId.Jackfruit && isOpen)) {
            cv.GameDataManager.tRoomData.u32GameID = this._msg.game_id;
            this._needPassword = false;
            cv.GameDataManager.tRoomData.pkRoomParam.currencyType = this._msg.currencyType;
            cv.roomManager.RequestJoinRoom(this._msg.game_id, this._msg.room_id, false, false);
        }
    }

    _joinRoom(captchaPassed: boolean) {
        //  did not pass captcha test
        if (captchaPassed === false) {
            return;
        }

        cv.SwitchLoadingView.show(cv.config.getStringData('Loading_resource'));
        // 进入明星桌之前，先检测当前明星桌是否开放
        cv.worldNet.CheckStarRoomRequest(this._msg.room_id);
    }

    // 清空头像线性
    _resetStarItem() {
        for (let i = 0; i < 2; i++) {
            this.starInfoRoot.children[i].active = false;
            const headNode = cc.find(cv.StringTools.formatC('starHead%d', i + 1), this.starInfoRoot.children[i]);

            const starHead = headNode.getChildByName('starHead'); // 头像
            CircleSprite.cleanHeadNode(starHead);

            const CelebrityFX = headNode.getChildByName('CelebrityFX'); // 头像动画
            CelebrityFX.active = false;
        }
    }

    _showCountdownTime() {
        const now = Date.now();
        const startTime = this._msg.starseatStartTime * 1000;
        this._timeLeft = startTime - now;
        this.countdownHolder.active = this._timeLeft > 0;

        if( this._intervalCallback !== null) return;
        
        if (this._timeLeft > 0) {
            this._updateCountdownTime(this._timeLeft);
            this._intervalCallback = setInterval(() => {
                this._timeLeft -= 1000;
                this._updateCountdownTime(this._timeLeft);

                if (this._timeLeft <= 0) {
                    clearInterval(this._intervalCallback);
                    this.countdownHolder.active = false;
                }
            }, 1000)
        }
    }

    _updateCountdownTime(milliseconds: number) {
        let seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        seconds %= 60;
        if (minutes >= 10) {
            this.countdownTimer.string =
                cv.StringTools.formatC(
                    cv.config.getStringData('Star_Table_Countdown_1'),
                    minutes
                );
        }
        else if (minutes <= 0) {
            this.countdownTimer.string =
                cv.StringTools.formatC(
                    cv.config.getStringData('Star_Table_Countdown_3'),
                    seconds
                );
        }
        else {
            this.countdownTimer.string =
                cv.StringTools.formatC(
                    cv.config.getStringData('Star_Table_Countdown_2'),
                    minutes,
                    seconds
                );
        }
    }
}
