import { pb } from '../../../common/pb/ws_protocol';
import { eTimeType } from '../../../common/tools/Enum';
import cv from '../cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class InquireItem extends cc.Component {
    @property(cc.Label)
    lab_name: cc.Label = null;

    @property(cc.Label)
    lab_num: cc.Label = null;

    @property(cc.Label)
    lab_time: cc.Label = null;

    @property(cc.Sprite)
    lab_num_bg: cc.Sprite = null;

    @property(cc.Sprite)
    lab_icon_sprite: cc.Sprite = null;

    @property(cc.SpriteFrame)
    lab_icons: cc.SpriteFrame[] = [];

    color_lab_plus: cc.Color = cc.color(240, 213, 170);
    color_lab_less: cc.Color = cc.color(122, 121, 122);

    color_lab_bg_plus: cc.Color = cc.color(49, 46, 44);
    color_lab_bg_less: cc.Color = cc.color(41, 41, 44);
    msg: pb.BankDetailsSnapshot = null;

    onLoad() {
        cv.resMgr.adaptWidget(this.node, true);
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);
    }

    onDestroy() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
    }

    initLanguage() {
        if (!this.msg) return;
        const isPlus = this.msg.amount > 0;
        this.lab_name.string = cv.config.getStringData('InquireView_content_' + this.msg.source_type);
        this.lab_num.string = (isPlus ? '+' : '') + cv.StringTools.serverGoldToShowString(this.msg.amount);
        this.lab_time.string = cv.StringTools.formatTime(this.msg.create_time, eTimeType.Month_Day_Hour_Min_Sec);
        this.lab_num.node.color = isPlus ? this.color_lab_plus : this.color_lab_less;
        this.lab_num_bg.node.color = isPlus ? this.color_lab_bg_plus : this.color_lab_bg_less;
        this.lab_icon_sprite.spriteFrame = this.lab_icons[isPlus ? 1 : 0];

        const rightPadding = 20, leftPadding = 20, minWidth = 50;
        const currWidth = cv.resMgr.getLabelStringSize(this.lab_name).width;
        
        this.scheduleOnce(() => {
            const maxWidth = this.node.width - (this.lab_num_bg.node.width + rightPadding + leftPadding);
            const maxLabelWidth = cv.StringTools.clamp(currWidth, minWidth, maxWidth);
            this.lab_name.node.width = maxLabelWidth;
        }, 0);
    }

    updateSVReuseData(index: number, dataArray: Array<any>): void {
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;
        this.msg = dataArray[index];
        this.initLanguage();
    }

    updateItemData(index: number, data: any): void {
        this.msg = data;
        this.initLanguage();
    }
}
