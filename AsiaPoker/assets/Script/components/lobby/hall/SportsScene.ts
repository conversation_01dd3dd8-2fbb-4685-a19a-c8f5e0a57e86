import ws_protocol = require('../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;

import cv from '../cv';

const { ccclass, property } = cc._decorator;
@ccclass
export class SportsScene extends cc.Component {
    @property(cc.WebView) web: cc.WebView = null;
    private webPos: cc.Vec2 = null;
    private needRecharge : boolean = false;

    onLoad() {
        console.warn('onload');
        if (cv.roomManager.getCurrentGameID() === world_pb.GameId.TopMatches) {
            cv.config.adaptScreenHen(this.node);
        } else {
            cv.config.adaptScreen(this.node);
        }

        // 这行要注释掉, ipad上会引起webview视图紊乱放大, 解决方案要么注释, 要么widget后动态添加webview
        // cv.resMgr.adaptWidget(this.node, true);

        cv.MessageCenter.register('startSportsScene', this.startSportsScene.bind(this), this.node);

        cv.MessageCenter.register('HideWebview_ShowWindows', this.HandleSwitchServer.bind(this), this.node);
        cv.MessageCenter.register('showSportsScene', this.showSportsScene.bind(this), this.node);
        cv.MessageCenter.register('Exit_click', this.exitGame.bind(this), this.node);
        cv.MessageCenter.register('OnLoginServer', this._onReconnection.bind(this), this.node);
    }

    start() {
        console.warn('start');
        this.needRecharge = false;
        cv.viewAdaptive.isselfchange = false;

        this.webPos = this.web.node.getPosition();
        this.setWebUrl();
        this.web.setJavascriptInterfaceScheme('ccjs');
        this.web.setOnJSCallback((webView: cc.WebView, url: string) => {
            console.log('sports ccjs ------ ' + url);
            if (url.search("ccjs://back-normal-recharge") !== -1) {
                this.needRecharge = true;
                this.exitGame();
                return;
            }
            if (url.search('ccjs://back-normal') !== -1) {
                this.exitGame();
            }
        });

        this.showSportsScene();
    }

    onDestroy() {
        cv.MessageCenter.unregister('HideWebview_ShowWindows', this.node);
        cv.MessageCenter.unregister('startSportsScene', this.node);
        cv.MessageCenter.unregister('showSportsScene', this.node);
        cv.MessageCenter.unregister('Exit_click', this.node);
        cv.MessageCenter.unregister('OnLoginServer', this.node);
    }

    HandleSwitchServer(isView: boolean = false) {
        const active = !cv.TP.getVisible() && isView;
        this.setWebActive(active);
    }

    exitGame() {
        console.error('exit game');
        const gameId = cv.roomManager.getCurrentGameID();
        cv.roomManager.reset();


        if(this.needRecharge && gameId === world_pb.GameId.Sports) // Set properties to redirect to recharge on Hall-Scene
        {
            cv.viewAdaptive.isselfchange = true;
            cv.viewAdaptive.sportsGameId = gameId;
            this.needRecharge = false;
        }

        cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene): void => {
            if (!cv.roomManager.isEnterMTT) {
                cv.MessageCenter.send('switchSceneToMiniGame');
            }

            switch (gameId) {
                case world_pb.GameId.Sports:
                case world_pb.GameId.TopMatches:
                    cv.worldNet.SportsLeaveRequest();
                    break;
                default:
                    break;
            }
        });
    }

    startSportsScene(gameid: number) {
        this.setWebUrl();
        this.showSportsScene();
    }

    showSportsScene() {
        const active = cv.TP.getVisible();
        this.setWebActive(!active);
    }

    setWebUrl() {
        switch (cv.roomManager.getCurrentGameID()) {
            case world_pb.GameId.Sports:
            case world_pb.GameId.TopMatches:
                this.web.url = cv.roomManager.getSportsUrl();
                break;        
            case world_pb.GameId.BlackJack:
                this.web.url = cv.roomManager.getBlackJackUrl();
                break;
            default:
                break;
        }
    }

    setWebActive(isView: boolean) {
        switch (cv.roomManager.getCurrentGameID()) {
            case world_pb.GameId.BlackJack:
            case world_pb.GameId.Sports:
            case world_pb.GameId.TopMatches:
                    this.web.node.active = isView;
                break;
            case world_pb.GameId.PocketGames:
                {
                    const posX = isView ? this.webPos.x : this.webPos.x + cc.winSize.width * 1.5;
                    this.web.node.setPosition(posX, this.webPos.y);
                }
                break;
            default:
                break;
        }
    }

    // on reconnection it will trigger sport leave request on backend side, hence we need to perform sport login upon reconnection
    _onReconnection() {
        console.error('reconnection');
        const gameId = cv.roomManager.getCurrentGameID();

        switch (gameId) {
            case world_pb.GameId.Sports:
            case world_pb.GameId.TopMatches:
                cv.roomManager.RequestJoinSportsRoom(gameId);
                break;
            case world_pb.GameId.PocketGames:
                break;
            default:
                break;
        }
    }
}
