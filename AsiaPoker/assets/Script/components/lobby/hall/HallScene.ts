import BJPVPConnector from '../../../../blackjackpvp/script/common/BJPVPConnector';
import { ecdhHandler } from '../../../common/ecdh/ecdhHandler';
import { PushNoticeType } from '../../../common/prefab/PushNotice';
import { ActivityType } from '../../../data/activityData';
import cb from '../../game/cowboy/cb';
import { GoldViewNew } from '../../game/dzPoker/GoldViewNew';
import humanboyDataMgr from '../../game/humanboy/HumanboyDataMgr';
import PokerMasterDataMgr from '../../game/pokerMaster/PokerMasterDataMgr';
import VideoCowboyManager from '../../game/videoCowboy/VideoCowboyManager';
import { MailEntrance } from '../../globalMsg/MailEntrance';
import { SliderVerify } from '../../lobby/sliderVerify/SliderVerify';
import UpgradeView from '../login/UpgradeView';
import ActivityView from './ActivityView';
import { ChooseVerity } from './ChooseVerity';
import EarnView from './EarnView';
import FindView, { DiscoverGameType } from './FindView';
import { NoticeView } from './NoticeView';
import Safe from './Safe';
import TicketView from './TicketView';
import Color = cc.Color;
import cv from '../cv';
import LabaView from './LabaView';
import { SelfView } from './SelfView';
import * as pf from '../../../../poker-framework/scripts/pf';
import LineSwitcher from './LineSwitcher';


const { ccclass, property } = cc._decorator;

@ccclass
export default class HallScene extends cc.Component {
    @property(cc.Prefab) selfviewPref: cc.Prefab = null;

    @property(cc.Prefab) activityPref: cc.Prefab = null;

    @property(cc.Prefab) ticketPref: cc.Prefab = null;

    @property(cc.Prefab) goldViewPref: cc.Prefab = null;

    @property(cc.Prefab) labaViewPref: cc.Prefab = null;

    @property(cc.Prefab) safePref: cc.Prefab = null;

    @property(cc.Prefab) upgradePref: cc.Prefab = null;

    @property(cc.Prefab) earnView_Pref: cc.Prefab = null;

    @property(cc.Prefab) prefab_chooseVerity: cc.Prefab = null;

    @property(cc.Prefab) sliderVerify_prefab: cc.Prefab = null;

    @property(cc.Prefab) redEnvelope_prefab: cc.Prefab = null;

    @property(LineSwitcher)
    lineSwitcher: LineSwitcher = null;

    // switch line scrollview, content, lintitem prefab
    @property(cc.Node)
    lineBox: cc.Node = null;
    
    // right-bottom number of switch server icon
    @property(cc.Label)
    currentLineNumberLabel: cc.Label = null;

    // for switch server info
    private _allServer: any[] = [];
    private _currentServer: any = null;
    private _specificLineIndex: number = 0;

    // 普通版本
    @property(cc.Node) bottomView: cc.Node = null;

    @property(cc.Node) findView: cc.Node = null;

    @property(cc.Node) miniGamesView: cc.Node = null;

    @property(cc.Node) noticeView: cc.Node = null;

    @property(cc.Node) findBtn: cc.Node = null;

    @property(cc.Node) minigameBtn: cc.Node = null;

    @property(cc.Node) earnBtn: cc.Node = null;

    @property(cc.Node) noticeBtn: cc.Node = null;

    @property(cc.Node) selfBtn: cc.Node = null;

    //海外版按钮
    @property(cc.Node) findBtn_en: cc.Node = null;

    @property(cc.Node) earnBtn_en: cc.Node = null;

    @property(cc.Node) dataBtn_en: cc.Node = null;

    @property(cc.Node) selfBtn_en: cc.Node = null;

    @property(cc.Node) safeBtn: cc.Node = null;

    @property(cc.Node) gold_Panel: cc.Node = null;

    @property(cc.Node) bankBtnDot: cc.Node = null;

    @property(cc.Node) bottomViewBg: cc.Node = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) top: cc.Node = null;

    protected redEnvelopePopup: cc.Node = null;

    public safe: cc.Node = null;

    public earnView: cc.Node = null;

    public silenceMusic: string = 'zh_CN/game/dzpoker/audio/silence';

    public goldView: cc.Node = null;

    public selfView: cc.Node = null;

    public upgradeView: cc.Node = null;

    public _isNoticeView: boolean = false;

    public _isFindView: boolean = false;

    public isATLRun: boolean = false;

    private normalTextColor: Color = new Color(255, 255, 255, 255);

    //#F0D5AA
    private pressedTextColor: Color = new Color(240, 213, 170, 255);

    _isJumpingToNotice: boolean = false;

    private activityViewInstance : cc.Node;

    private isNoticeViewOpen : boolean;

    onLoad() {
        
        window.addEventListener("orientationchange", this.onScreenOrientationChanged);

        if(cc.sys.isBrowser) {
            this._adaptUI();
        } else {
            cv.config.adaptScreen(this.node);
            cv.resMgr.adaptWidget(this.node, true);
        }

        this.isNoticeViewOpen = false;
        
        cv.config.setCurrentScene(cv.Enum.SCENE.HALL_SCENE);

        cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_LOBBY);

        //显示广告页的时候，电量信息隐藏，此处重新显示。
        cv.StatusView.show(true);

        this.registerMsg();

        this.initGoldView();

        // 登录请求
        if (cv.netWork.isConnect()) {
            this.updateChipNum();
        }

        cv.StatusView.updateSystemTimePos(true);

        this.selfView = cc.instantiate(this.selfviewPref);

        this.earnView = cc.instantiate(this.earnView_Pref);

        this.findView.getParent().addChild(this.earnView);

        this.earnView.active = false;

        let earnViewComp = this.earnView.getComponent(EarnView);

        if (earnViewComp) {
            earnViewComp.refHallSceneNode = this.node;
        }

        if (cv.config.isOverSeas()) {
            this.safeBtn.active = false;
        }

        //防止selfView中的元素层级盖过，大厅顶端按钮（保险箱，切换线路，客服等按钮），selfView的层级要比safeBtn等低。
        this.selfView.zIndex = this.safeBtn.zIndex - 1;

        cv.netWorkManager.SceneOnLoad();

        ecdhHandler.getInstance().ecdh_init();

        cv.StatusView.getBatteryInfo();

        if (cc.sys.os == cc.sys.OS_IOS) {
            cv.native.AuthLocation();
        }

        this.bottomView.active = true;

        this.bottomView.getParent().getParent().addChild(this.selfView);

        this.selfView.active = false;

        this.bottomView.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_1;

        this.swithView(this.findView, this.findBtn.children[0]);

        this.bottomViewBg.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_1;

        // 切到默认界面之前要激活"findView"界面(因为其"onload"中有些蛋疼流程的请求错综复杂, 只能按原来套路来)
        // 沿用上面的逻辑, 这里的暂时注释
        // this.findView.active = true;
        if (cv.dataHandler.getUserData().default_hall_view_enabled) {
            cv.dataHandler.getUserData().default_hall_view_enabled = false;

            this.swithViewByViewIdx(cv.dataHandler.getUserData().default_hall_view);
        }

        //获取常驻节点
        cv.MessageCenter.send('hallEnterMTT');

        cv.httpHandler.getKefuUrl(); // get customer service url on load to prevent double click to show

        this.setSafeArea();



    }


    adaptWidgetBottom(node: cc.Node, num: number) {
        let widget: cc.Widget = node.getComponent(cc.Widget);

        widget.bottom += num;
    }

    //声道变换
    routeChange(jsonCmd: any) {
        let str: string = jsonCmd.ret;

        if (str == 'Speaker') {
            cv.TT.showMsg('Speaker', cv.Enum.ToastType.ToastTypeSuccess);
        } else if (str == 'Headphones') {
            cv.TT.showMsg('Headphones', cv.Enum.ToastType.ToastTypeSuccess);
        } else {
            cv.TT.showMsg(str, cv.Enum.ToastType.ToastTypeSuccess);
        }
    }

    start() {

        if(cc.sys.isBrowser) {
            this.scheduleOnce(this._adaptUI);
        }

        cv.MessageCenter.register('routeChange', this.routeChange.bind(this), this.node);
        let isNewYear = cv.config.isShowNewYear();

        cc.find('newyear_down_img', this.bottomView).active = isNewYear;

        cc.find('newyear_left_img', this.bottomView).active = isNewYear;

        cc.find('newyear_right_img', this.bottomView).active = isNewYear;

        // 写在这里是处理邮件按钮换图
        if (this._isFindView) {
            // 新年活动
            this.initNewYear(cv.config.isShowNewYear());
        }

        if (cc.sys.isNative) {
            cv.httpHandler.sendNativeError();
        }

        this.initLanguage();

        let node = cv.action.addChildToScene(this, this.activityPref, [], cv.Enum.ZORDER_TYPE.ZORDER_ACTIVITY, true);

        node.name = 'activityView';

        this.activityViewInstance = node;

        this.firstShowActivityPanel();

        let ticketNode = cv.action.addChildToScene(this, this.ticketPref, [], cv.Enum.ZORDER_TYPE.ZORDER_7, true);

        ticketNode.name = TicketView.NAME;
        this.isATLRun = TicketView.IS_VIEW;
        this.showLabaPanel();

        this.sortATLView();

        if (cv.dataHandler.getUserData().is_goto_myredpacket) {
            cv.dataHandler.getUserData().is_goto_myredpacket = false;

            this._onMsgSwitchSceneToSelfView();

            cv.MessageCenter.send('open_myredpackets');
        }

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();

        // 检查退出牛仔房间后的提示
        if (cv.StringTools.getArrayLength(cb.getCowboyRoom().backToMainTips) > 0) {
            let showTips: string = cb.getCowboyRoom().backToMainTips;

            cb.getCowboyRoom().backToMainTips = '';

            this.scheduleOnce((dt: number) => {
                cv.TP.showMsg(showTips, cv.Enum.ButtonStyle.GOLD_BUTTON, null);
            }, 0.3);
        }
        // 检查退出百人房间后的提示
        else if (cv.StringTools.getArrayLength(humanboyDataMgr.getHumanboyRoom().sBackToMainTips) > 0) {
            let showTips: string = humanboyDataMgr.getHumanboyRoom().sBackToMainTips;

            humanboyDataMgr.getHumanboyRoom().sBackToMainTips = '';

            this.scheduleOnce((dt: number) => {
                cv.TP.showMsg(showTips, cv.Enum.ButtonStyle.GOLD_BUTTON, null);
            }, 0.3);
        }
        // 检查退出视频牛仔房间后的提示
        else if (cv.StringTools.getArrayLength(VideoCowboyManager.getVideoCowboyRoom().backToMainTips) > 0) {
            let showTips: string = VideoCowboyManager.getVideoCowboyRoom().backToMainTips;

            VideoCowboyManager.getVideoCowboyRoom().backToMainTips = '';

            this.scheduleOnce((dt: number) => {
                cv.TP.showMsg(showTips, cv.Enum.ButtonStyle.GOLD_BUTTON, null);
            }, 0.3);
        }
        // 检查退出扑克大师房间后的提示
        else if (cv.StringTools.getArrayLength(PokerMasterDataMgr.getPokerMasterRoom().sBackToMainTips) > 0) {
            let showTips: string = PokerMasterDataMgr.getPokerMasterRoom().sBackToMainTips;

            PokerMasterDataMgr.getPokerMasterRoom().sBackToMainTips = '';

            this.scheduleOnce((dt: number) => {
                cv.TP.showMsg(showTips, cv.Enum.ButtonStyle.GOLD_BUTTON, null);
            }, 0.3);
        } else if(context && cv.StringTools.getArrayLength(context.backToMainTips) > 0) {
            let showTips: string = context.backToMainTips;

            context.backToMainTips = '';

            this.scheduleOnce((dt: number) => {
                cv.TP.showMsg(showTips, cv.Enum.ButtonStyle.GOLD_BUTTON, null);
            }, 0.3);
        }
        else {
            if (cv.viewAdaptive.isselfchange) {
                cv.SHOP.setExitCallFunc(() => {
                    if (cv.viewAdaptive.cowboyroomid > 0) {
                        cv.roomManager.RequestJoinRoom(cv.Enum.GameId.CowBoy, cv.viewAdaptive.cowboyroomid, false);
                    } else if (cv.viewAdaptive.videoCowboyRoomId > 0) {
                        cv.roomManager.RequestJoinRoom(
                            cv.Enum.GameId.VideoCowboy,
                            cv.viewAdaptive.videoCowboyRoomId,
                            false
                        );
                    } else if (cv.viewAdaptive.humanboyroomid > 0) {
                        cv.roomManager.RequestJoinRoom(cv.Enum.GameId.HumanBoy, cv.viewAdaptive.humanboyroomid, false);
                    } else if (cv.viewAdaptive.pokerMasterRoomID > 0) {
                        cv.roomManager.RequestJoinRoom(
                            cv.Enum.GameId.PokerMaster,
                            cv.viewAdaptive.pokerMasterRoomID,
                            false
                        );
                    } else if (cv.viewAdaptive.bjpvpLevelId > 0) {
                        cv.MessageCenter.send(
                            BJPVPConnector.instance.keyConfig.BroadCast.BJPVP_ENTER_GAMESCENE,
                            cv.viewAdaptive.bjpvpLevelId
                        );
                    } else if (cv.viewAdaptive.blackjackLevelId > 0) {
                        cv.roomManager.RequestJoinBlackJackRoom(
                            cv.viewAdaptive.blackjackRoomId,
                            cv.viewAdaptive.blackjackLevelId,
                            cv.viewAdaptive.blackjackGameId
                        );
                    } else if (cv.viewAdaptive.sportsGameId > 0) {
                        cv.roomManager.RequestJoinSportsRoom(cv.viewAdaptive.sportsGameId);
                    }
                });

                if (cv.roomManager.isPlayingGame()) {
                    cv.viewAdaptive.isselfchange = false;
                }

                cv.SHOP.show();

                cv.SHOP.RechargeClick();
            } else if(context && context.isSelfRecharge) {                
                cv.SHOP.setExitCallFunc(() => { 
                    cv.roomManager.RequestJoinRoom(
                        context.gameId,
                        context.roomId,
                        false
                    );                    
                });

                context.isSelfRecharge =  false;
                cv.SHOP.show();
                cv.SHOP.RechargeClick();
            }
            else{
                cv.SHOP.setExitCallFunc(() => {});
            }
       
        }

        //根据php返回的is_alert_sl判断是否弹首次登录引导
        if (cv.config.isOpenSiyuVerify() && cv.dataHandler.getUserData().is_alert_sl) {
            cv.dataHandler.getUserData().is_alert_sl = false;

            cv.TP.showMsg(
                cv.config.getStringData('siyu_toast_guide'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                this._onGotoVerityMessage.bind(this),
                null,
                false,
                cv.config.getStringData('siyu_dlg_title')
            );

            cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_SILIAO_TIPS);
        }

        cv.action.addNodeForAcMap(cv.SHOP.msgNode);

        this.onBonusAndFreeResponse();

        this.earnView.getComponent(EarnView).onSecurityBoxBtnClicked = this.showSafe.bind(this);
        const giftNotice = cv.dataHandler.getUserData().giftToolNotice;
        if(giftNotice && !ticketNode.active) cv.MessageCenter.send("ReceiveToolsNotice", giftNotice);
        
        this.lineSwitcher.init(this.getTopHeight.bind(this));

        // event from network and lineitem emit
        cv.MessageCenter.register('HallScene-SwitchSuccess', this.onSwitchSuccess.bind(this), this);
        cv.MessageCenter.register('HallScene-ResetDomainIndex', this.onResetDomainIndex.bind(this), this);

        // current line number for switch server button
        this.currentLineNumberLabel.string = `${this.checkCurrentLineIndex()+1}`;
    }

    //跳转到短信验证界面
    private _onGotoVerityMessage() {
        let inst_chooseVerity: cc.Node = ChooseVerity.getSinglePrefabInst(this.prefab_chooseVerity);

        cv.action.addChildToSceneOnce(inst_chooseVerity);

        cv.action.showAction(
            inst_chooseVerity,
            cv.action.eMoveActionDir.EMAD_TO_LEFT,
            cv.action.eMoveActionType.EMAT_FADE_IN
        );
    }

    initLanguage() {
        cv.resMgr.setSpriteFrame(cc.find('img_logo', this.node), cv.config.getLogoPath(cv.Enum.SCENE.HALL_SCENE));
    }

    private registerMsg() {
        cv.MessageCenter.register('update_info', this.onGetPlayerInfoSuccess.bind(this), this.node);

        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);

        cv.MessageCenter.register('showLabaPanel', this.showLabaPanel.bind(this), this.node);

        cv.MessageCenter.register('HideWebview_ShowWindows', this.HandleSwitchServer.bind(this), this.node);

        cv.MessageCenter.register('switchSceneToMiniGame', this._onMsgSwitchSceneToMiniGame.bind(this), this.node);

        cv.MessageCenter.register('switchSceneToSelfView', this._onMsgSwitchSceneToSelfView.bind(this), this.node);

        // 真人验证消息
        cv.MessageCenter.register('on_need_slider_verify', this._onMsgNeedSliderVerify.bind(this), this.node);

        cv.MessageCenter.register('jumpgto_notice', this._onjumpgto_notice.bind(this), this.node);

        cv.MessageCenter.register('showUpgradeView', this.showUpgradeView.bind(this), this.node);

        cv.MessageCenter.register('sortATLView', this.sortATLView.bind(this), this.node);
        cv.MessageCenter.register('updata_my_redpackets_pos', this.sortATLView.bind(this), this.node);


        cv.MessageCenter.register('jumpToMtt', this.jumpToMtt.bind(this), this.node);

        cv.MessageCenter.register('jumpToBlackJack', this.jumpToBlackJack.bind(this), this.node);

        cv.MessageCenter.register('jumpToBlackJackMicro', this.jumpToBlackJackMicro.bind(this), this.node);

        cv.MessageCenter.register('jumpToGlobalSpin', this.jumpToGlobalSpin.bind(this), this.node);

        cv.MessageCenter.register('hallEnterMTT', this.hallEnterMTT.bind(this), this.node);

        cv.MessageCenter.register('jumpToMiniGamesHall', this.hallShowMiniGames.bind(this), this.node);

        cv.MessageCenter.register('jumpToHallBank', this.hallShowBank.bind(this), this.node);

        cv.MessageCenter.register('sendBonusAndFreeGamesMsg', this.onBonusAndFreeResponse.bind(this), this.node);

        cv.MessageCenter.register('onC2CPaymentNotice', this.onC2CPaymentNotice.bind(this), this.node);

        cv.MessageCenter.register('onC2CWithdrawNotice', this.onC2CWithdrawNotice.bind(this), this.node);

        cv.MessageCenter.register('onCustomerServiceClick', this.openKeFu.bind(this), this.node);

        cv.MessageCenter.register('jumpToBackPack', this.jumpToBackPack.bind(this), this.node);

        //私语版本，走私语切换后台注册
        if (cv.config.isSiyuType()) {
            cv.MessageCenter.register('on_syOnEnterBackground', this.OnAppEnterBackground.bind(this), this.node);
            cv.MessageCenter.register('on_syOnEnterForeground', this.OnAppEnterForeground.bind(this), this.node);
        }
        cv.MessageCenter.register('onLogoutMiniGame', this.onLogoutMiniGame.bind(this), this.node);
        cv.MessageCenter.register('closeActivityView', this._closeActivityView.bind(this), this.node);
        cc.game.on(cc.game.EVENT_SHOW, this.gameShow.bind(this), this.node);
    }

    onDestroy() {
        if (cv.config.isSiyuType()) {
            cv.MessageCenter.unregister('on_syOnEnterBackground', this.node);

            cv.MessageCenter.unregister('on_syOnEnterForeground', this.node);
        }

        // Network line switch
        cv.MessageCenter.unregister('HallScene-SwitchSuccess', this);
        cv.MessageCenter.unregister('HallScene-ResetDomainIndex', this);

        cv.MessageCenter.unregister('routeChange', this.routeChange.bind(this));

        cv.MessageCenter.unregister('update_info', this.node);

        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);

        cv.MessageCenter.unregister('showLabaPanel', this.node);

        cv.MessageCenter.unregister('HideWebview_ShowWindows', this.node);

        cv.MessageCenter.unregister('switchSceneToMiniGame', this.node);

        cv.MessageCenter.unregister('switchSceneToSelfView', this.node);

        cv.MessageCenter.unregister('on_need_slider_verify', this.node);

        cv.MessageCenter.unregister('jumpgto_notice', this.node);

        cv.MessageCenter.unregister('showUpgradeView', this.node);

        cv.MessageCenter.unregister('sortATLView', this.node);
        cv.MessageCenter.unregister('updata_my_redpackets_pos', this.node);

        cv.MessageCenter.unregister('jumpToMtt', this.node);

        cv.MessageCenter.unregister('jumpToBlackJack', this.node);

        cv.MessageCenter.unregister('jumpToBlackJackMicro', this.node);

        cv.MessageCenter.unregister('jumpToGlobalSpin', this.node);

        cv.MessageCenter.unregister('hallEnterMTT', this.node);

        cv.MessageCenter.unregister('jumpToMiniGamesHall', this.node);

        cv.MessageCenter.unregister('sendBonusAndFreeGamesMsg', this.node);

        cv.MessageCenter.unregister('jumpToHallBank', this.node);

        cv.MessageCenter.unregister('jumpToBackPack', this.node);

        cv.MessageCenter.unregister('openKeFu', this.node);

        cv.MessageCenter.unregister('onCustomerServiceClick', this.node);
        cv.MessageCenter.unregister('onLogoutMiniGame', this.node);
        cv.MessageCenter.unregister('closeActivityView', this.node);
        cc.game.off(cc.game.EVENT_SHOW, this.gameShow);
        window.removeEventListener("orientationchange", this.onScreenOrientationChanged);
    }

    /**
     * 显示升级账号界面
     */
    public showUpgradeView() {
        let inst: cc.Node = UpgradeView.getSinglePrefabInst(this.upgradePref);

        inst.active = true;

        inst.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_7;

        cv.action.addChildToSceneOnce(inst);
    }

    private onLogoutMiniGame(gameId: number): void {
        switch (gameId) {
            case cv.Enum.GameId.ISlot:
                if (cv.config.alllowMultipWindow()){
                    cv.worldNet.requestGetUserData();
                }
            break;
            case cv.Enum.GameId.OBGames:
                cv.worldNet.requestGetUserData();
            break;
        }
    }

    private gameShow() {
        if (!cv.config.alllowMultipWindow()){
            return;
        }

        const gameId = cv.roomManager.getCurrentGameID();
        switch(gameId){
            case cv.Enum.GameId.ISlot:
                this._logoutMiniGame(()=>cv.worldNet.requestISlotLogout());
                break;
            case cv.Enum.GameId.Sport:
                this._logoutMiniGame(()=>cv.worldNet.SportsLeaveRequest());
                break;
            case cv.Enum.GameId.CaribbeanStud:
            case cv.Enum.GameId.CaribbeanTexasHold:
            case cv.Enum.GameId.WealthTrio:
            case cv.Enum.GameId.BitMasterGoldCoin:
            case cv.Enum.GameId.BitMasterUSD:
                this._logoutMiniGame(()=>cv.worldNet.requestHabaLogout(cv.roomManager.getCurrentGameID()));
                break;
            case cv.Enum.GameId.PG:
                this._logoutMiniGame(()=>{
                    cv.worldNet.PgLeaveRequest();
                    cv.worldNet.PgBonusAndFreeGamesRequest();
                });
                break;
            case cv.Enum.GameId.PP:
                if (cc.sys.os === cc.sys.OS_IOS) {
                    cv.worldNet.PpLeaveRequest(cv.roomManager.getPpEntryName());
                    return;
                }
                this._logoutMiniGame(()=>{
                    cv.worldNet.PpLeaveRequest(cv.roomManager.getPpEntryName());
                });
                break;
            default:
                break;
        }
    }

    private _logoutMiniGame(func:() => void){
        if (cv.config.gameWindow){
            this.scheduleOnce(() => {
                if (cv.config.gameWindow.closed)
                {
                    func();
                    cv.config.gameWindow = null;
                }
                else{
                    cv.worldNet.requestGetUserData();
                }
            }, 1);
        }
    }

    /**
     * 游戏进入后台时触发的事件
     * 请注意，在 WEB 平台，这个事件不一定会 100% 触发，这完全取决于浏览器的回调行为
     * 在原生平台，它对应的是应用被切换到后台事件，下拉菜单和上拉状态栏等不一定会触发这个事件，这取决于系统行为
     */
    OnAppEnterBackground(): void {
        //私语版本, 切回后台后，将所有音频暂停
        if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.H5WebPage) {
            if (cc.sys.os == cc.sys.OS_IOS) {
                //creator IOS平台的网页web版，切换后台，再切回游戏后。会出现所有音乐、音效播放没有声音的bug。
                //在切回后播放一个无声的mp3背景音乐，保持ios音效处于激活状态。在切回前台后，再stop背景音乐，可以临时解决这个问题。
                cv.AudioMgr.play(this.silenceMusic, true, 0.1, true);
            }
        }
    }

    /**
     * 游戏进入前台运行时触发的事件
     * 请注意，在 WEB 平台，这个事件不一定会 100% 触发，这完全取决于浏览器的回调行为
     * 在原生平台，它对应的是应用被切换到前台事件
     */
    OnAppEnterForeground(): void {
        if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.H5WebPage) {
            if (cc.sys.os == cc.sys.OS_IOS) {
                cv.AudioMgr.stop(cv.AudioMgr.getAudioID(this.silenceMusic));
            }
        }
        if (this._isFindView) {
            this.sortATLView();
        }
       
    }

    public onGetPlayerInfoSuccess() {
        if (cv.dataHandler.getUserData().HeadPath.length == 0) {
            let path = Math.floor(
                cv.StringTools.randomRange(cv.config.HEAD_LENGTH / 2, cv.config.HEAD_LENGTH) + 1
            ).toString();

            let strUrl: string = cv.dataHandler.getUserData().headUrl;

            let u64Key = strUrl.lastIndexOf('/') + 1;

            let name = strUrl.substr(u64Key);

            if (name.length == 0) {
                cv.dataHandler.getUserData().HeadPath = path;

                cv.httpHandler.setDefaultHead(path);
            }
        }

        this.updateChipNum();
    }

    /**
     * 点击发现按钮
     * @param event
     */
    onBtnFindClick(event: cc.Component.EventHandler) {
        this.swithView(this.findView, event.target);

        cv.AudioMgr.playButtonSound('hall_bottom_button');

        // 活动弹框
        let activityNode = cc.director.getScene().getChildByName('activityView');

        if (activityNode && activityNode.getComponent(ActivityView)) {
            activityNode.getComponent(ActivityView).reOpenActivity();
        }
        // 检测拉霸
        else {
            this.showLabaPanel();
        }

        // "发现列表"事件采集
        this.trackFindView();
    }

    /**
     * 点击小游戏按钮
     * @param event
     */
    onBtnMiniGamesClick(event: cc.Component.EventHandler) {
        cv.AudioMgr.playButtonSound('hall_bottom_button');

        this.swithView(this.miniGamesView, event.target);

        // 跟踪用户行为, 发送事件
        cv.segmentTool.track(
            cv.Enum.CurrentScreen.casinoGameSelection,
            cv.Enum.segmentEvent.ScreenOpened,
            cv.Enum.Functionality.casino
        );
    }

    /**
     * 点击中间充值界面按钮
     * @param event
     */
    onBtnEarnClick(event: cc.Component.EventHandler) {
        cv.AudioMgr.playButtonSound('hall_bottom_button');

        this.swithView(this.earnView, event.target);

        cv.tools.SaveStringByCCFile('welfareNew', 'false');

        this.bankBtnDot.active = false;
    }

    /**
     * 点击公告按钮
     * @param event
     */
    onBtnNoticeClick(event: cc.Component.EventHandler, callback?: () => void) {
        cv.AudioMgr.playButtonSound('hall_bottom_button');
        
        if (cc.sys.isBrowser) {
            if (!event.customEventData) {
                event.customEventData = this.noticeView.getComponent(NoticeView).getNoticeWebUrl();
            }
            cv.native.openUrl(event.customEventData);
            callback && callback();
        } else {
            this.swithView(this.noticeView, event.target);
            if(callback === null)
            {
                const hideActivity = () => {
                    this.OnFirstActivityShownCallback();
                };
                callback = hideActivity;
            }
            this.noticeView.getComponent(NoticeView).setWebView(event.customEventData, callback);
            this.isNoticeViewOpen = true;
        }
    }

    /**
     * 点击我的空间按钮
     * @param event
     */
    onBtnSelfClick(event: cc.Component.EventHandler) {
        cv.AudioMgr.playButtonSound('hall_bottom_button');

        this.swithView(this.selfView, event.target);

        cv.config.getLog();

        // 跟踪用户行为, 发送事件
        cv.segmentTool.track(
            cv.Enum.CurrentScreen.profileSettings,
            cv.Enum.segmentEvent.ScreenOpened,
            cv.Enum.Functionality.invite
        );
    }

    showLabaPanel(): void {
        let lababViewNode = this.node.getChildByName('labaViewNode');

        if (!lababViewNode) {
            lababViewNode = cc.instantiate(this.labaViewPref);

            lababViewNode.name = 'labaViewNode';

            this.node.addChild(lababViewNode, 2);

            // 适配widget
            cv.resMgr.adaptWidget(lababViewNode);

            lababViewNode.setPosition(cc.v2(0, 0));

            lababViewNode.active = false;
        }

        if (cv.dataHandler.getUserData().luckindex < cv.dataHandler.getUserData().lucks.length) {
            this.sortATLView();
        } else {
            lababViewNode.active = false;
        }
    }

    swithView(node: cc.Node, btnNode: cc.Node) {
        this._isNoticeView = node == this.noticeView;

        this._isFindView = node == this.findView;

        this.findView.active = false;

        this.selfView.active = false;

        this.earnView.active = false;

        this.noticeView.active = false;

        this.miniGamesView.active = false;

        this.showImg(this.findBtn, false);

        this.showImg(this.noticeBtn, false);

        this.showImg(this.selfBtn, false);

        this.showImg(this.earnBtn, false);

        this.showImg(this.minigameBtn, false);

        if (cv.config.isOverSeas() || cv.config.isVietnam()) {
            //海外版和越南版
            this.showImg(this.findBtn_en, false);

            this.showImg(this.dataBtn_en, false);

            this.showImg(this.earnBtn_en, false);

            this.showImg(this.selfBtn_en, false);
        }

        if (!this._isNoticeView) {
            this.noticeView.getComponent(NoticeView).cleanWebview();
        }

        node.active = true;

        if (node != this.findView) {
            this.findView.getComponent(FindView).HandleCheckWebView(false);

            this.findView.getComponent(FindView).hideQuickEnterView();

            // 新年活动
            this.initNewYear(false);
        } else {
            this.findView.getComponent(FindView).HandleCheckWebView(true);

            // 新年活动
            this.initNewYear(cv.config.isShowNewYear());
        }

        this.showImg(btnNode.getParent(), true);
    }

    swithViewByViewIdx(viewIdx: number): void {
        let view: cc.Node = null;

        let view_btn: cc.Node = null;

        // 海外版和越南版
        let isOverSeas: boolean = cv.config.isOverSeas() || cv.config.isVietnam();

        switch (viewIdx) {
            // 小游戏
            case 2:
                {
                    view = this.miniGamesView;

                    view_btn = this.minigameBtn.getChildByName('btn');

                    if (isOverSeas) {
                        view = this.findView;

                        view_btn = this.findBtn_en.getChildByName('btn');
                    }
                }
                break;

            // 存取款
            case 3:
                {
                    view = this.earnView;

                    view_btn = this.earnBtn.getChildByName('btn');

                    if (isOverSeas) {
                        view_btn = this.earnBtn_en.getChildByName('btn');
                    }
                }
                break;

            // 我
            case 5:
                {
                    view = this.selfView;

                    view_btn = this.selfBtn.getChildByName('btn');

                    if (isOverSeas) {
                        view_btn = this.selfBtn_en.getChildByName('btn');
                    }
                }
                break;

            case 1: // 发现列表
            case 4: // 公告(网页和活动弹窗网页重叠, 层级都是置顶, 会遮罩底部按钮, 引擎原生层bug, 要解决改动很大, 不稳, 暂时禁用处理)
            default:
                {
                    view = this.findView;

                    view_btn = this.findBtn.getChildByName('btn');

                    if (isOverSeas) {
                        view_btn = this.findBtn_en.getChildByName('btn');
                    }
                }
                break;
        }

        // 切换至目标面板
        this.swithView(view, view_btn);

        // "发现列表"事件采集
        this.trackFindView();
    }

    /**
     * 采集每次激活"发现列表"时的用户数据
     */
    trackFindView(): void {
        if (!this._isFindView) {
            return;
        }
        let gameType: number = DiscoverGameType.DZPK;

        let gameIndex: number = 0;

        let findView: FindView = this.findView.getComponent(FindView);

        let saveStr: string = cv.tools.GetStringByCCFile(findView.SAVE_gameType);

        if (cv.StringTools.getArrayLength(saveStr) > 0) {
            gameType = cv.Number(saveStr);
        }

        gameIndex = cv.Number(cv.tools.GetStringByCCFile(findView.SAVE_wxzd));

        let format_type: string = `MainScene_Scene_gameType_panel_button${gameType}_text`;

        let format_index: string = `MainScene_Scene_pokerPage_panel_button${gameIndex}_text`;

        let selectedGameType: string = cv.config.getStringData(format_type);

        let selectedStakes: string = cv.config.getStringData(format_index);

        // 奥马哈没有"微/小/中/大"
        if (gameType === DiscoverGameType.PLO) {
            selectedStakes = '';
        }

        // 跟踪用户行为, 发送事件
        let properties = { selectedGameType: selectedGameType, selectedStakes: selectedStakes };

        cv.segmentTool.track(
            cv.Enum.CurrentScreen.lobby,
            cv.Enum.segmentEvent.ScreenOpened,
            cv.Enum.Functionality.poker,
            properties
        );
    }

    showImg(node: cc.Node, isView: boolean) {
        var img = node.getChildByName('img');

        if (img) {
            img.active = isView;
        }

        let text = node.getChildByName('text');

        if (text) {
            text.color = isView ? this.pressedTextColor : this.normalTextColor;
        }
    }

    updateChipNum() {
        this.goldView.getComponent(GoldViewNew).UpdateUserInfo();
    }

    firstShowActivityPanel() {
        //获取当次登录后，是否还有没显示到的活动弹窗
        let haveNoShowActivity = cv.dataHandler.getActivityData().getActivityHaveNotShow();

        // 被动切换到助力红包界面时  不显示
        if (haveNoShowActivity && !cv.dataHandler.getUserData().is_goto_myredpacket) {
            cc.director.getScene().getChildByName('activityView').getComponent(ActivityView).init();

            if (cv.dataHandler.getActivityData().showType != ActivityType.NONE) {
                cc.director
                    .getScene()
                    .getChildByName('activityView')
                    .getComponent(ActivityView)
                    .showActivity(cv.dataHandler.getActivityData().showType, this.OnFirstActivityShownCallback);

                cv.dataHandler.getActivityData().showType = ActivityType.NONE;
            } else {
                cc.director.getScene().getChildByName('activityView').getComponent(ActivityView).showActivity(undefined, this.OnFirstActivityShownCallback);
            }
        } else {
            cc.director.getScene().getChildByName('activityView').getComponent(ActivityView).removeWebview();
        }
    }

    OnFirstActivityShownCallback()
    {
        if(this.isNoticeViewOpen)
        {
            this.activityViewInstance.getComponent(ActivityView).closeActivity(true);
        }
    }

    onBtnAddCoinClick() {
        cv.SHOP.RechargeClick();
    }

    //open customer service
    openKeFu() {
        this.HandleSwitchServer(false);
        let url = '';
        url = cv.httpHandler.getKefuUrl();
        if (cc.sys.isBrowser) {
            window.open(url);
        } else {
            cv.httpHandler.getCustom();
        }
    }

    onBtnKeFuClick() {
        cv.AudioMgr.playButtonSound('button_click');

        this.HandleSwitchServer(false);

        let url = '';
        url = cv.httpHandler.getKefuUrl();
        if (cc.sys.isBrowser) {
            if (url) {
                cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                    window.open(url);
                });
            }
        } else {
            cv.httpHandler.getCustom();
        }

        let fuc: Function = (): void => {
            this.HandleSwitchServer(true);

            // cv.httpHandler.getCustom();

            cc.director.getScene().getChildByName('activityView').getComponent(ActivityView).clearCallBack();
        };

        cc.director.getScene().getChildByName('activityView').getComponent(ActivityView).init();

        cc.director.getScene().getChildByName('activityView').getComponent(ActivityView).showActivity(1, fuc);
    }

    HandleSwitchServer(isView?: boolean) {
        isView = isView == true && this._isNoticeView ? true : false;

        this.noticeView.active = isView;

        if (isView) {
            this.noticeView.getComponent(NoticeView).setWebView(undefined);
        } else {
            this.noticeView.getComponent(NoticeView).cleanWebview();
        }
    }

    private onClickSwitchServer() {
        if (this.lineSwitcher) {
            this.lineSwitcher.toggleLineBox();
        } else {
            cc.warn('LineSwitcher is not initialized');
        }
        cv.AudioMgr.playButtonSound('button_click');
    }


    checkCurrentLineIndex() {
        this._allServer = cv.domainMgr.getAllServerInfo();
        this._currentServer = cv.domainMgr.getServerInfo();
        cc.log('this._allServer =');
        cc.log(this._allServer);
    
        const currentServerKeys = Object.keys(this._currentServer);
    
        const matchIndex = this._allServer.findIndex(server => 
            currentServerKeys.every(key => this._currentServer[key] === server[key])
        );
    
        // if ALL KEY MATCH return index and also set the Line number
        if (matchIndex !== -1) {
            this.currentLineNumberLabel.string = (matchIndex + 1).toString();
            cc.log('isMatch, i = ' + matchIndex);
        }
    
        return matchIndex; // if none match server will return -1
    }


    showingSafe(): void {
        this.showSafe();
    }

    private showSafe(): void {
        cv.AudioMgr.playButtonSound('button_click');

        cv.MessageCenter.send('HideWebview_ShowWindows', false);

        if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(
                cv.config.getStringData('roleInfoSet_tips_useSafe_barntips_text'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                cv.dataHandler.upgradeAccount.bind(cv.dataHandler),
                cv.dataHandler.cancleUpgradeAccount.bind(cv.dataHandler)
            );

            return;
        }

        cv.worldNet.RequestGetStrongboxInfo();

        if (this.earnView) {
            const earnViewComp = this.earnView.getComponent(EarnView);

            if (earnViewComp) {
                earnViewComp.createSecurityBox(this._isNoticeView);
            }
        }
    }

    private onGoReconnect() {
        cv.MessageCenter.send('HideWebview_ShowWindows', true);

        cv.netWorkManager.onGoReconnect(true);
    }

    public onResetDomainIndex(domain_index){
        cc.log(`onResetDomainIndex, domain_index = ${domain_index}`);
        this._specificLineIndex = domain_index;

        let bol: boolean = cv.domainMgr.isHaveNextServer();

        cv.MessageCenter.send('HideWebview_ShowWindows', false);

        cv.TP.showMsg(
            cv.config.getStringData('UIWitchServer2'),
            cv.Enum.ButtonStyle.TWO_BUTTON,
            this.onGoReconnectSpecific.bind(this),
            this.onNoReconnect.bind(this)
        );

    }

    private onGoReconnectSpecific(){
        cc.log(`onGoReconnectSpecific, this._specificLineIndex = ${this._specificLineIndex}`);
        cv.MessageCenter.send('HideWebview_ShowWindows', true);
        cv.netWorkManager.onReconnectToSpecificLine(this._specificLineIndex, true);
        if (this.lineBox.active){
            this.lineBox.active = false;

        }
    }

    public onSwitchSuccess(){
        // make sure current line
        this.currentLineNumberLabel.string = (this.checkCurrentLineIndex()+1).toString();
    }

    private onNoReconnect() {
        cv.MessageCenter.send('HideWebview_ShowWindows', true);
    }

    private _onMsgSwitchSceneToMiniGame() {
        this.swithView(this.miniGamesView, this.minigameBtn.getChildByName('btn'));
    }

    private _onMsgSwitchSceneToSelfView() {
        this.swithView(this.selfView, this.selfBtn.getChildByName('btn'));
    }

    /**
     * 真人验证
     * 大厅这里的验证消息目前是针对急速"JoinRoom"失败后抛出的, 所以没带参数,
     * 如果是其他验证则需要重新修改逻辑, 加入参数去区分了
     */
    private _onMsgNeedSliderVerify(): void {
        let sliderVerify: SliderVerify = SliderVerify.initSingleInst(
            this.sliderVerify_prefab,
            this.node,
            cv.Enum.ZORDER_TYPE.ZORDER_TOP
        );

        sliderVerify.autoShow((): void => {
            // do something
        });
    }

    private _onjumpgto_notice(url) {
        this._isJumpingToNotice = true;

        let lababViewNode = this.node.getChildByName('labaViewNode');
        const labaView = lababViewNode ? lababViewNode.getComponent(LabaView) : undefined;
        // 跳转公告面板时如果已经显示拉霸, 则隐藏拉霸
        const hideLaba = () => {
            this._isJumpingToNotice = false;
            if (labaView) {
                console.error('hide laba');
                lababViewNode.active && labaView.closeView(false);
            } 
        };

        let event = new cc.Component.EventHandler();

        event.customEventData = url;

        event.target = cc.find('btn', this.noticeBtn);

        this.onBtnNoticeClick(event, hideLaba);
    }

    initNewYear(isShowNewYear) {
        let path = isShowNewYear ? 'zh_CN/festival/newyear/newyear_bg_newyear' : 'zh_CN/common/icon/bg_top_02';

        cv.resMgr.setSpriteFrame(cc.find('bg_top_img', this.node), path);

        cc.find('newyear_up_node', this.node).active = isShowNewYear;

        path = isShowNewYear ? 'zh_CN/festival/newyear/newyear_icon_kefu' : 'zh_CN/hall/safe/icon_kefu';

        cv.resMgr.setSpriteFrame(cc.find('mainView/kefu', this.node), path);

        if (MailEntrance.getInstance() != null) {
            path = isShowNewYear ? 'zh_CN/festival/newyear/newyear_icon_bell' : 'zh_CN/common/icon/icon_bell';

            cv.resMgr.setSpriteFrame(MailEntrance.getInstance().node, path);
        }
    }

    initGoldView() {
        this.goldView = cc.instantiate(this.goldViewPref);

        this.noticeView.parent.addChild(this.goldView);

        if (!cv.config.IS_FULLSCREEN) {
            this.goldView.getComponent(GoldViewNew).setViewStyle(2);
        }

        this.goldView.name = 'goldView_pref';

        this.goldView.parent = this.gold_Panel;

        const goldViewWidget = this.goldView.getComponent(cc.Widget);

        goldViewWidget.top = 0;

        goldViewWidget.bottom = 0;

        goldViewWidget.right = 0;

        goldViewWidget.left = 0;

        const goldViewScript = this.goldView.getComponent(GoldViewNew);

        goldViewScript.setNoticeViewNode(this.noticeView);

        /*let goldPos = this.gold_Panel.getPosition();

        let goldSize = this.goldView.getContentSize();

        let offSet_y = 0;

        if (cc.sys.os == cc.sys.OS_IOS && cv.config.IS_FULLSCREEN) 
        {
            //防止刘海屏遮挡金币框
            offSet_y = 6;
        }

        this.goldView.setPosition(cc.v2(goldPos.x - goldSize.width / 2, goldPos.y - goldSize.height / 2 - offSet_y));*/
    }

    sortATLView(): void {
        // 玩家从游戏中切换到助力红包界面时  不弹其它的活动弹框
        if (cv.dataHandler.getUserData().is_goto_myredpacket) {
            return;
        }
        const activityNode = cc.director.getScene().getChildByName('activityView');
        const lababViewNode = this.node.getChildByName('labaViewNode');
        const ticketNode = cc.director.getScene().getChildByName(TicketView.NAME);

        if (activityNode && activityNode.active) {
            if (ticketNode) {
                ticketNode.active = false;
            }
        } else if (ticketNode && TicketView.IS_VIEW) {
            const ticketView = ticketNode.getComponent(TicketView);
                if (!ticketView.isClose && ticketView.getData) {
                    ticketView.show();
                    TicketView.IS_VIEW = false;
                } else if (
                    lababViewNode &&
                        !this._isJumpingToNotice &&
                        cv.dataHandler.getUserData().luckindex < cv.dataHandler.getUserData().lucks.length
                    ) {
                        lababViewNode.active = true;
                        lababViewNode.getComponent(LabaView).init();
                    } else if (cv.dataHandler.getUserData().getHelpWarpList().length > 0 && 
                    cv.dataHandler.getUserData().isShow_help_warp !== false ) {
                            cv.dataHandler.getUserData().isShow_help_warp = false;
                            cv.TP.showMsg(
                                cv.config.getStringData('MyRedPackets_login_notice'),
                                cv.Enum.ButtonStyle.TWO_BUTTON,
                                () => {
                                    this._onMsgSwitchSceneToSelfView();
                                    cv.MessageCenter.send('open_myredpackets');
                                }
                            );
                            cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_MY_RED_PACKETS);
                            cv.TP.setHideTipsCallBack(cv.worldNet.MTTGameStartNoticeCb);
                        } else {
                            cv.worldNet.MTTGameStartNoticeCb?.();
                            cv.worldNet.MTTGameStartNoticeCb = null;
                        }
        } else if (lababViewNode &&
                !this._isJumpingToNotice &&
                cv.dataHandler.getUserData().luckindex < cv.dataHandler.getUserData().lucks.length
            ) {
                lababViewNode.active = true;
                lababViewNode.getComponent(LabaView).init();
            } else if (cv.dataHandler.getUserData().getHelpWarpList().length > 0 && 
            cv.dataHandler.getUserData().isShow_help_warp !== false ) {
                    cv.dataHandler.getUserData().isShow_help_warp = false;
                    cv.TP.showMsg(
                        cv.config.getStringData('MyRedPackets_login_notice'),
                        cv.Enum.ButtonStyle.TWO_BUTTON,
                        () => {
                            this._onMsgSwitchSceneToSelfView();
                            cv.MessageCenter.send('open_myredpackets');
                        }
                    );
                    cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_MY_RED_PACKETS);
                    cv.TP.setHideTipsCallBack(cv.worldNet.MTTGameStartNoticeCb);
                } else {
                    cv.worldNet.MTTGameStartNoticeCb?.();
                    cv.worldNet.MTTGameStartNoticeCb = null;
                    cv.MessageCenter.send("ShowRebateRankingNoticePopup");
                }
    }

    jumpToMtt() {
        let tag = this.findView.getComponent(FindView).MTT_NUM;

        this.findView.getComponent(FindView).setViewGametype(tag, true);

        this.swithView(this.findView, this.findBtn.children[0]);
    }

    jumpToBlackJack() {
        let tag = this.findView.getComponent(FindView).BLACKJACK_NUM;

        this.findView.getComponent(FindView).setViewGametype(tag, true);

        this.findView.getComponent(FindView).onShowBJPVPTab();

        this.swithView(this.findView, this.findBtn.children[0]);
    }

    jumpToBlackJackMicro() {
        this.swithView(this.findView, cc.find('btn', this.findBtn));

        this.scheduleOnce(() => {
            this.jumpToBlackJack();

            this.findView.getComponent(FindView).switchBJPVPTab(0);
        });
    }

    jumpToGlobalSpin():void{
        const tag = this.findView.getComponent(FindView).JSNG_NUM;
        this.findView.getComponent(FindView).setViewGametype(tag, true);
        this.findView.getComponent(FindView).onShowJSNGTab();
        this.swithView(this.findView, this.findBtn.children[0]);
    }

    hallEnterMTT() {
        if (cv.roomManager.isEnterMTT) {
            cv.action.setAcMapHide();

            let safe_pref = cc.director.getScene().getChildByName('SecurityBox');

            if (cc.isValid(safe_pref, true)) {
                safe_pref.getComponent(Safe).closeView();
            }

            cv.MessageCenter.send('jumpToMtt');

            this.findView.getComponent(FindView).enterMTTGame(cv.roomManager.mtt_id);
        }
    }

    // 跳转到电子游戏列表
    hallShowMiniGames(data: any) {
        this.swithView(this.miniGamesView, this.minigameBtn.getChildByName('btn'));

        cv.MessageCenter.send('enterMiniGameEvent', data);
    }

    //跳转到银行
    hallShowBank(data: any) {
        this.swithView(this.earnView, this.earnBtn.getChildByName('btn'));

        cv.tools.SaveStringByCCFile('welfareNew', 'false');

        this.bankBtnDot.active = false;
    }

    jumpToBackPack(toBackPackMainPage:boolean = false) {
        
        const safe_pref = cc.director.getScene().getChildByName('SecurityBox');
        if (cc.isValid(safe_pref, true)) {
            safe_pref.getComponent(Safe).forceCloseView();
        }

        this.swithView(this.selfView, this.selfBtn.getChildByName('btn'));

        if(toBackPackMainPage) {
            this.selfView.getComponent(SelfView).onBackPackBtnClick();
        } else {
            if (!this.selfView.getComponent(SelfView).isBackpackPageActive()) {
                this.selfView.getComponent(SelfView).onBackPackBtnClick();
            }
        }
    }

    //银行按钮上面的红点
    onBonusAndFreeResponse() {
        let _bShowGuidDot = cv.tools.GetStringByCCFile('welfareNew');

        if (_bShowGuidDot === 'true') {
            this.bankBtnDot.active = true;
        } else {
            this.bankBtnDot.active = false;
        }
    }

    /*
    bill_no: "300002"
    deadline: 0
    product_id: 2
    status: 3
    time: 0
    */
    onC2CWithdrawNotice(msg: any) {}

    /*
    coin: 0
    product_id: 2
    status: 0
    time: **********
    */
    onC2CPaymentNotice(msg: any) {}

    setSafeArea() {
        // SafeArea
        let offsetY = cv.SafeAreaWithDifferentDevices.getSafeAreaHall();

        this.safearea.height = offsetY;

        // Mini Games
        let widget = this.miniGamesView.getComponent(cc.Widget);

        widget.top = offsetY + this.top.height - 230;

        this.layout.updateLayout();
    }

    getTopHeight(): number {
        return this.top.height;
    }

    getBottomHeight(): number {
        return this.bottomViewBg.height;
    }

    private onScreenOrientationChanged = () =>{
        if(cc.sys.isBrowser) {
            this.scheduleOnce(this._adaptUI, 0.2);
        } else {
            return;
        }
    }

    private _adaptUI(): void {
        cv.config.adaptScreen(this.node);
        cv.resMgr.adaptWidget(this.node, true);
        cv.TP.adaptUI();
        this.findView.getComponent(FindView).adaptBanner();
    }

    private _closeActivityView() {
        this.activityViewInstance?.getComponent(ActivityView)?.closeActivity(true);
    }
}
