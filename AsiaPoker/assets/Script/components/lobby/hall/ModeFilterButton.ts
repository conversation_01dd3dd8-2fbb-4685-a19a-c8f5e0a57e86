
import cv from "../cv";
import LobbyFilterButtonBase from "./LobbyFilterButtonBase";

const {ccclass} = cc._decorator;

@ccclass
export default class ModeFilterButton extends LobbyFilterButtonBase {
    
    protected metaData: string;

    public get metadata()
    {
        return this.metaData;
    }

    public initialize(type: string, newState: boolean, buttonCallback: (btn: ModeFilterButton)=>void)
    {
        this.metaData = type;
        this.setNewState(newState);
        this.initLanguage();
        this.onButtonClickDelegate = buttonCallback;
    }

    public override initLanguage():void{
        let localisedString = "";
        switch (this.metaData) {
            case 'classic':
                    localisedString = cv.config.getStringData('Filter_Mode_Classic_Text');
                    break;

                case 'loose':
                    localisedString = cv.config.getStringData('Filter_Mode_Loose_Text');
                    break;

                case 'critical':
                    localisedString = cv.config.getStringData('Filter_Mode_Critical_Text');
                    break;
                case 'squid':
                    localisedString = cv.config.getStringData('Filter_Mode_Squid_Text');
                    break;
        }
        this.setContent(localisedString);
    }

    protected onUnuse(): void {
        super.onUnuse();
        this.metaData = "";
    }

    protected onClickListener(event: cc.Event): void 
    {
        super.onClickListener(event);
        this.onButtonClickDelegate(this);
    }
}
