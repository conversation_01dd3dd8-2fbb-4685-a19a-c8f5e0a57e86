import { SafeAreaHelper } from '../../../../default/shared/safe_area_helper/SafeAreaHelper';
import cv from '../cv';

const { ccclass, property } = cc._decorator;
@ccclass
export abstract class GenericWebviewScene extends cc.Component {
    @property(cc.WebView) web: cc.WebView = null;
    @property(cc.Button) btExit: cc.Button = null;
    @property(cc.Node) headerNode: cc.Node = null;

    protected abstract setCurrentScene():void;
    protected abstract setWebUrl():void;

    protected onLoad() {
        this.setCurrentScene();
        cv.config.adaptScreenHen(this.node);
        cv.resMgr.adaptWidget(this.node, true);
    }

    protected onEnable(): void {
        cv.MessageCenter.register('HideWebview_ShowWindows', this.HandleSwitchServer.bind(this), this.node);
    }

    protected onDisable(): void {
        cv.MessageCenter.unregister('HideWebview_ShowWindows', this.node);
    }

    protected start() {
        this.setWebUrl();
        this.showWebviewGameScene();
        this.web.setJavascriptInterfaceScheme('ccjs');
        this.web.setOnJSCallback((webView: cc.WebView, url: string) => {
            if (url.search('ccjs://back-normal') !== -1) {
                this.exitGame();
            }
        });
        this.btExit.node.on(cc.Node.EventType.TOUCH_END, this.onExitBtnClicked, this);
        cv.StatusView.show(false);

        this.headerNode.height = SafeAreaHelper.getUpperDangerZoneYOffset() || 1; // yes we need at least 1 pixel for this webview resolution scale to work, i cant remove this magic number, at least for now
        this.web.node.height -= SafeAreaHelper.getUpperDangerZoneYOffset();
        this._tempFixViewIssue();
    }

    protected onDestroy() {
        cv.MessageCenter.unregister('HideWebview_ShowWindows', this.node);
    }

    protected HandleSwitchServer(isView?: boolean) {
        isView = !cv.TP.getVisible() && isView;
        this.setWebActive(isView);
    }

    private onExitBtnClicked() {
        cv.TP.showMsg(
            cv.config.getStringData('ExitMinigameConfirm'),
            cv.Enum.ButtonStyle.TWO_BUTTON,
            this.exitGame.bind(this),
            () => {
                this.setWebActive(true);
            }
        );
    }

    protected exitGame() {
        cv.worldNet.requestHabaLogout(cv.roomManager.getCurrentGameID());
        this.setWebActive(false);
        cv.roomManager.reset();
        cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene): void => {
            cv.MessageCenter.send('switchSceneToMiniGame');
        });
    }

    protected showWebviewGameScene() {
        const isView = cv.TP.getVisible();
        this.setWebActive(!isView);
    }

    protected setWebActive(isView: boolean) {
        const size = this.node.getComponent(cc.Canvas).designResolution;
        if (isView) {
            this.web.node.setPosition(cc.v2(0, 0));
        } else {
            this.web.node.setPosition(cc.v2(0, size.height * 2));
        }
    }

    /* Temp solution same as iSlot scene */
    protected _tempFixViewIssue() {
        this.scheduleOnce(() => {
            this.setWebActive(null);
        }, 1);

        this.scheduleOnce(() => {
            this.setWebActive(true);
        }, 2);
    }
}
