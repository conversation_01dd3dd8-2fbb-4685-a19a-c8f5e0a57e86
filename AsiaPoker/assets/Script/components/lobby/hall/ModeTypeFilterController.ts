import BaseFilterController from './BaseFilterController';
import cv from '../cv';
import ModeFilterButton from './ModeFilterButton';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import { DiscoverGameType } from './FindView';

const { ccclass } = cc._decorator;

@ccclass
export default class ModeTypeFilterController extends BaseFilterController {
    private _filterData: string[] = [];

    public get filterData() {
        return this._filterData;
    }

    public get SAVE_KEY(): string {
        return 'FILTER_MODETYPE_PKW_' + this.keyIndex;
    }

    public initialize(
        modes: string[],
        index: number
    ): void {
        if (index !== -1) this.keyIndex = index;
        this.titleLabel.string = cv.config.getStringData('Filter_ModeType_Title_Text');
        for (let a = 0; a < modes.length; a++) {
            const modeTypeButton = this.getNewButton();
            const buttonComponent: ModeFilterButton = modeTypeButton.getComponent(ModeFilterButton);
            const modeType = modes[a];
            const savedState = this._filterData.indexOf(modeType) !== -1;
            buttonComponent.initialize(modeType, savedState, this.onFilterClicked.bind(this));
        }
        this.node.active = true;
        this.layoutContainer.updateLayout();
        this._initData();
    }

    public refreshByData(): void {
        this.activeButtons.forEach((element) => {
            const btn = element.getComponent(ModeFilterButton);
            btn.setNewState(this._filterData.indexOf(btn.metadata) !== -1);
        });
    }

    public filter(data: world_pb.ClubGameSnapshotV3[], gameType: Number): world_pb.ClubGameSnapshotV3[] {
        let tempData = data;
        if (this.isApplicable(gameType) && this._filterData.length > 0 && this.isActive) {
            tempData = tempData.filter(
                (x) =>
                    ((this._filterData.includes( cv.Enum.GameModeType.Classic) && x.room_mode === world_pb.RoomMode.RoomModeNone && (x.game_id !== cv.Enum.GameId.Squid)|| // In WPK, 6532 reported to exclude Splash and SD games from Mode filter when Classic mode is selected.
                    (this._filterData.includes( cv.Enum.GameModeType.Squid)  && x.game_id === cv.Enum.GameId.Squid) ||
                    (this._filterData.includes( cv.Enum.GameModeType.Loose) && x.room_mode === world_pb.RoomMode.RoomModeLoose) ||
                    (this._filterData.includes( cv.Enum.GameModeType.Critical) && x.room_mode === world_pb.RoomMode.RoomModeBomb) ||
                    x.game_id === cv.Enum.GameId.StarSeat
                )
            ));
        }
        return tempData;
    }

    public isQualified(
        item: world_pb.ClubGameSnapshotV3,
        gameType: Number,
        isSubset: boolean
    ): boolean {
        if (this._filterData.length > 0 && this.isApplicable(gameType) && isSubset) {
            return (
                (this._filterData.includes( cv.Enum.GameModeType.Classic) && item.room_mode === world_pb.RoomMode.RoomModeNone && 
                (item.game_id !== cv.Enum.GameId.Squid)) ||
                (this._filterData.includes( cv.Enum.GameModeType.Squid) && item.game_id === cv.Enum.GameId.Squid) ||
                (this._filterData.includes( cv.Enum.GameModeType.Loose) && item.room_mode === world_pb.RoomMode.RoomModeLoose) ||
                (this._filterData.includes( cv.Enum.GameModeType.Critical) && item.room_mode === world_pb.RoomMode.RoomModeBomb)
            );
        }
        return true;
    }

    public isApplicable(gameType: Number): boolean {
        return gameType === DiscoverGameType.DZPK || gameType === DiscoverGameType.DZPK_SHORT;
    }

    public override saveFilterDataToFile(): void {
        this._saveModeTypeDataToFile();
    }

    public override initLanguage(): void {
        super.initLanguage();
        this.titleLabel.string = cv.config.getStringData('Filter_ModeType_Title_Text');
    }

    protected override onFilterClicked(Component: ModeFilterButton): void {
        const newState = Component.currentState;
        const modeType = Component.metadata;

        if (newState) {
            this._filterData.push(modeType);
        } else {
            const index = this._filterData.findIndex((x) => x.includes(modeType));
            this._filterData.splice(index, 1);
        }
        this.emitClickEvent();
    }

    protected override emitClickEvent() {
        cc.game.emit('OnFilterButtonClicked', true);
    }

    private _initData(): void {
        const data = this._fetchSavedModeTypeDataFromFile();
        if (data) {
            this._filterData = data;
            this.refreshByData();
        }
    }

    private _saveModeTypeDataToFile() {
        const value = this.filterData.join(this.saveSeparatorChar);
        cv.tools.SaveStringByCCFile(this.SAVE_KEY, value);
    }

    private _fetchSavedModeTypeDataFromFile() {
        let stringArray: string[] = [];
        const savedData = cv.tools.GetStringByCCFile(this.SAVE_KEY);
        if (savedData) {
            stringArray = savedData.split(this.saveSeparatorChar);
        }
        return stringArray;
    }
}
