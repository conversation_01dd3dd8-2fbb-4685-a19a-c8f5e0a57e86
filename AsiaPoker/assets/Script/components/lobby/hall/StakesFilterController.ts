import BaseFilterController from './BaseFilterController';
import StakeSelectorButton from './StakeSelectorButton';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import cv from '../cv';
import world_pb = ws_protocol.pb;

const { ccclass } = cc._decorator;

@ccclass
export default class StakesFilterController extends BaseFilterController {
    protected stakeSize: world_pb.GameSizeType;
    private _filterData: string[] = [];

    // AT-2571:- 90 is the regular default width as set in the prefab. 
    protected readonly REGULAR_TITLE_BG_WIDTH: number = 90;
    protected readonly STRETCHED_TITLE_BG_WIDTH: number = 140;

    public get SAVE_KEY(): string {
        return 'FILTER_STAKES_PKW_' + this.keyIndex + '_' + this.stakeSize;
    }

    public get StakeSize() {
        return this.stakeSize;
    }

    public get filterData() {
        return this._filterData;
    }

    protected onLoad(): void {
        this.initializeFilterDataFromSavedData();
    }

    public SetStakeSize(size: world_pb.GameSizeType): void {
        this.stakeSize = size;
    }

    public stakes: string[] = [];

    public initialize(
        size: world_pb.GameSizeType,
        stakesData: string[],
        gameId: number,
        isInit:boolean,
        index:number
    ): void {
        if (index !== -1) this.keyIndex = index;
        this.stakes = stakesData;
        this.stakeSize = size;
        this.initLanguage();
        this.activeButtons.forEach((element) => {
            if (element.isValid) element.destroy();
        });
        this.activeButtons = [];
        for (let d = 0; d < stakesData.length; d++) {
            const btn = this.getNewButton();
            const buttonComponent = btn.getComponent(StakeSelectorButton);
            this._initializeBlindsFilterButton(buttonComponent, stakesData[d], gameId);
        }

        this.node.active = true;
        this.layoutContainer.updateLayout();
        if(isInit){
            this._initData();
        }
    }

    public refreshByData(): void {
        this.activeButtons.forEach((element) => {
            const btn = element.getComponent(StakeSelectorButton);
            btn.setNewState(this._filterData.indexOf(btn.metadata) !== -1);
        });
    }

    public override onFilterClicked(Component: StakeSelectorButton): void {
        const newState = Component.currentState;
        const gameType = Component.metadata;
        if (newState) {
            this._filterData.push(Component.metadata);
        } else {
            const index = this._filterData.findIndex(x => x === gameType);
            if(index !== -1) {
                this._filterData.splice(index, 1);
            }
        }
        this.emitClickEvent();
    }

    public override saveFilterDataToFile(): void {
        this._saveStakesDataToFile();
    }

    public override initLanguage(): void {
        const contentName: string[] = [
        cv.config.getStringData('MainScene_Scene_pokerPage_panel_button0_text'), // 微
        cv.config.getStringData('MainScene_Scene_pokerPage_panel_button1_text'), // 小
        cv.config.getStringData('MainScene_Scene_pokerPage_panel_button2_text'), // 中
        cv.config.getStringData('MainScene_Scene_pokerPage_panel_button3_text') // 大
        ];

        // AT-2571:- For these languages, the word 'Micro' is too bug so need to stretch size of BG which titleLabel is parented to.
        if(cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.yn_TH 
            || cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.en_US
            || cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.hi_IN)
        {
            this.titleLabel.node.parent.width = this.STRETCHED_TITLE_BG_WIDTH;
        }
        else
        {
            this.titleLabel.node.parent.width = this.REGULAR_TITLE_BG_WIDTH;
        }

        this.titleLabel.string = contentName[this.stakeSize - 1];
    }

    protected initializeFilterDataFromSavedData(): void {}

    private _initData(): void {
        const data = this._fetchSavedStakesDataFromFile();
        if (data) {
            this._filterData = data;
            this.refreshByData();
        }
    }

    private _initializeBlindsFilterButton(buttonComponent: StakeSelectorButton, str: string, gameID: number): void {
        const savedState: boolean = false;
        buttonComponent.initialize(str, savedState, str, gameID, this.onFilterClicked.bind(this));
    }

    private _saveStakesDataToFile(): void {
        const value = this.filterData.join(this.saveSeparatorChar);
        cv.tools.SaveStringByCCFile(this.SAVE_KEY, value);
    }

    private _fetchSavedStakesDataFromFile(): string[] {
        let stringArray: string[] = [];
        const savedData = cv.tools.GetStringByCCFile(this.SAVE_KEY);
        if (savedData) {
            stringArray = savedData.split(this.saveSeparatorChar);
        }
        return stringArray;
    }
}
