
import LobbyFilterButtonBase from "./LobbyFilterButtonBase";

const {ccclass} = cc._decorator;

@ccclass
export default class StakeSelectorButton extends LobbyFilterButtonBase {

    protected metaData: string;

    protected thisGameType: number;

    public get metadata()
    {
        return this.metaData;
    }

    public set metadata(value: string)
    {
        this.metaData = value;
    }

    public initialize(type: string, newState: boolean, content: string, gameType: number,buttonCallback: (btn: StakeSelectorButton, currentType: number)=> void)
    {
        this.metaData = type;
        this.thisGameType = gameType;
        this.setNewState(newState);
        this.setContent(content)
        this.onButtonClickDelegate = buttonCallback;
    }

     protected onUnuse(): void {
        super.onUnuse();
        this.metaData = "";
        this.thisGameType = -1;
    }

    protected onClickListener(event: cc.Event): void 
    {
        super.onClickListener(event);
        this.onButtonClickDelegate(this);
        event.stopPropagation();
    }
}
