import { set } from "mobx";
import cv from "../cv";

const { ccclass, property } = cc._decorator;
@ccclass
export class FishingKingScene extends cc.Component {
    @property(cc.WebView) web: cc.WebView = null;
    private isBlocked = false;
    onLoad() {

        cv.config.setCurrentScene(cv.Enum.SCENE.FISHINGKING_SCENE);
        cv.config.adaptScreenHen(this.node);
        cv.resMgr.adaptWidget(this.node, true);
        // 监听 WebView 事件
        cv.MessageCenter.register("startFishingKingScene", this.startFishingKingScene.bind(this), this.node);
        cv.MessageCenter.register("HideWebview_ShowWindows", this.HandleSwitchServer.bind(this), this.node);
        cv.MessageCenter.register("fishingKingCloseWindowNotice", this.exitGame.bind(this), this.node);
    }

    start() {
        this.initFishingKing();
    }

    async initFishingKing() {
        const url = cv.roomManager.getFishingKingUrl();
        this.isBlocked = await cv.roomManager.checkUrlBefore(url);
        console.log("Initial URL check result:", this.isBlocked);
        
        if (!this.isBlocked) {
            this.web.url = url;
            this.showFishingKingScene();
            this.web.setJavascriptInterfaceScheme("ccjs");
            this.web.setOnJSCallback((webView: cc.WebView, url: string) => {
                console.log("fishing king ccjs ------ " + url);
                if (url.search("ccjs://back-normal") != -1) {
                    this.exitGame();
                    return;
                }
            });
        } else {
            this.exitGame();
        }
    }

    onDestroy() {
        cv.MessageCenter.unregister("HideWebview_ShowWindows", this.node);
        cv.MessageCenter.unregister("startFishingKingScene", this.node);
        cv.MessageCenter.unregister("fishingKingCloseWindowNotice", this.node);
    }

    HandleSwitchServer(isView?: boolean) {
        isView = (!cv.TP.getVisible()) && isView;
        this.setWebActive(isView);
    }


    exitGame() {
        cv.roomManager.reset();
        cv.worldNet.requestFishingKingLogout();
        this.setWebActive(false);
        cv.roomManager.reset();
        cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene): void => {
            cv.MessageCenter.send("switchSceneToMiniGame");
            if(this.isBlocked) {
                cv.TP.showMsg(
                    cv.config.getStringData('ExitMinigameDueToError'),
                    cv.Enum.ButtonStyle.GOLD_BUTTON,
                    ()=>{
                       
                    }
                );
            }
        
        });
    }

    startFishingKingScene(gameid: number) {
        this.setWebUrl();
        this.showFishingKingScene();
    }

    showFishingKingScene() {
        let isView = cv.TP.getVisible();
        this.setWebActive(!isView);
    }

    setWebUrl() {
        this.web.url = cv.roomManager.getFishingKingUrl();
    }

    setWebActive(isView: boolean) {
        this.web.node.active = isView;
    }

}
