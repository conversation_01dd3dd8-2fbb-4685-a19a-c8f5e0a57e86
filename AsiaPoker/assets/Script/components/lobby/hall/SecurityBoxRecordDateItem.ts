import cv from "../cv";

const { ccclass, property } = cc._decorator;

@ccclass
export default class SecurityBoxRecordDateItem extends cc.Component {
    @property(cc.Label) dateLabel: cc.Label = null;

    private msg: any = null;


    updateSVReuseData(index: number, dataArray: Array<any>): void {
        if (dataArray.length <= 0 || dataArray.length - 1 < index) return;
        this.msg = dataArray[index];
        this.updateUI();
    }

    updateItemData(index: number, data: any): void {
        console.log(`SecurityBoxRecordDateItems - updateItemData`);
        this.msg = data;
        this.updateUI();
    }

    private updateUI(): void {
        let createdAt: Date = new Date(Number(this.msg.time * 1000));
        let dateString: string = cv.StringTools.formatTime(createdAt.getTime(), cv.Enum.eTimeType.DD_MMMM, true);
       
        let time  = new Date().getTime();
        let today = new Date(new Date(time).getTime())

        if (today === this.msg.dayEpoch) {
            dateString = cv.config.getStringDataNew("Today") + ", " + dateString;
        }
        this.dateLabel.string = dateString;
    }
}
/*

 let createdAt: Date = new Date();
        if (this.msg.createdAt) {
            createdAt = new Date(this.msg.createdAt);
        }
        else {
            createdAt = new Date(this.msg.create_time * 1000);
        }

        let dateString: string = cv.StringTools.formatTime(createdAt.getTime(), cv.Enum.eTimeType.DD_MMM_YYYY, true);
        let today: number = cv.config.getTimeWithTimeZone().setHours(0, 0, 0, 0);
        if (today === this.msg.dayEpoch) {
            dateString = cv.config.getStringData("Today", false, "Today") + ", " + dateString;
        }

        this.dateLabel.string = dateString;
*/