// Learn TypeScript:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html

import { Gradient } from "../../../common/tools/Gradient";
import cv from "../cv";
import USDTAndCoin from "./USDTAndCoin";
import HallScene from "./HallScene";

const { ccclass, property } = cc._decorator;

@ccclass
export default class USDTView extends USDTAndCoin {

    @property(cc.Label) titleText: cc.Label = null;

    @property(cc.Label) txt_freeTimes: cc.Label = null;  //usdt免费兑换次数

    @property(cc.Node) feeTips: cc.Node = null;  //usdt费用提示

    @property(cc.Label) txt_feeTips: cc.Label = null;  //usdt费用提示

    @property(cc.Node) bgExplanation: cc.Node = null;  //提示背景弹框

    @property(cc.Node) usdtExplain: cc.Node = null;  //提示背景弹框

    @property(cc.SpriteFrame) borderSpriteFrame: cc.SpriteFrame[] = [];

    @property(cc.Node) ItemNode: cc.Node = null;

    @property(cc.Node) backBtn: cc.Node = null;

    @property(cc.Node) usdtView: cc.Node = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) top: cc.Node = null;

    @property(cc.Node) imgBg: cc.Node = null;

    @property(cc.Layout) imgBgLayout: cc.Layout = null;

    @property(cc.Node) title: cc.Node = null;

    @property(cc.Node) usdtCoinParent: cc.Node = null;

    protected coin_2_usdt_TotalFreeTime: number = 10; //总共免费兑换次数

    protected coin_2_usdt_freeTime: number = 0; //剩余免费兑换次数

    protected coin_2_usdt_feeRatio: number = 0.005; //coin转usdt费率

    protected coin_2_usdt_pointRatio: number = 0; //积分折扣兑换比

    protected usdt_exchange_interval: number = 0; //兑换时间间隔

    color_lab_select: cc.Color = cc.color(208, 171, 110);

    color_lab_noSelect: cc.Color = cc.color(148, 149, 149);

    exchange_button_deselect: cc.Color = cc.color(34, 34, 36);

    exchange_button_gradient1: cc.Color = cc.color(245, 225, 191);

    exchange_button_gradient2: cc.Color = cc.color(195, 157, 97);

    exchnage_text_enable: cc.Color = cc.color(23, 25, 35);

    exchnage_text_disable: cc.Color = cc.color(122, 121, 122);

    onLoad() {
        //cv.resMgr.adaptWidget(this.node, true);

        this.setSafeArea();
    }

    start() {
        super.start();

        this.switchTab(1);

        this.open_use_point = false;

        this.bgExplanation.active = false;

        cv.MessageCenter.register("onExchangeGetConfigResponse", this.onExhangeConfigResponse.bind(this), this.node);

        cv.MessageCenter.register("onExchangeGetConfigNotice", this.onExhangeConfigChangeNotice.bind(this), this.node);

        cv.MessageCenter.register("onExchangeTimeLimitError", this.onExchangeTimeLimitError.bind(this), this.node);

        this.disableExchnageButton(this.coinNode);

        this.disableExchnageButton(this.usdtNode);
    }

    onEnable() {
        super.OnEnable();

        this.disableExchnageButton(this.coinNode);

        this.disableExchnageButton(this.usdtNode);
    }

    onDestroy() {
        super.onDestroy();

        cv.MessageCenter.unregister("onExchangeGetConfigResponse", this.node);

        cv.MessageCenter.unregister("onExchangeGetConfigNotice", this.node);

        cv.MessageCenter.unregister("onExchangeTimeLimitError", this.node);
    }

    initLanguage() {
        this.titleText.string = cv.config.getStringData("conversion_usdt_goldcoins");

        this.updateExhangeFreeTimesTips();

        super.initLanguage();
    }

    enableExchnageButton(activeNode: cc.Node) {
        var gt = cc.find("exchange_button", activeNode).getComponent(Gradient);

        var button_level_node = cc.find("exchange_button/level", activeNode);

        gt.startColor = this.exchange_button_gradient1;

        gt.endColor = this.exchange_button_gradient2;

        button_level_node.color = this.exchnage_text_enable;

        cc.find("exchange_button", activeNode).getComponent(cc.Button).enabled = true;
    }

    disableExchnageButton(activeNode: cc.Node) {
        var gt = cc.find("exchange_button", activeNode).getComponent(Gradient);

        var button_level_node = cc.find("exchange_button/level", activeNode);

        gt.startColor = this.exchange_button_deselect;

        gt.endColor = this.exchange_button_deselect;

        button_level_node.color = this.exchnage_text_disable;

        cc.find("exchange_button", activeNode).getComponent(cc.Button).enabled = false;
    }

    updateTabExHuilv(index: number) {
        let tabNode: cc.Node = this.getTabNode(index);

        if (tabNode) {
            let str = cv.StringTools.formatC(cv.config.getStringData("USDTView_usdt_coin_ex_label_" + index), this.getTabExHuilv(index));
            
            let txt_huiLv = null;
            
            if (tabNode == this.usdtNode) {
                txt_huiLv = cc.find("layoutTips/huilvNode/txt_huilv", tabNode);
            } 
            else if (tabNode == this.coinNode) {
                txt_huiLv = cc.find("txt_huilv", tabNode);
            }

            if (txt_huiLv) {
                txt_huiLv.getComponent(cc.Label).string = str;
            }
        }

        super.updateTabExHuilv(index);
    }

    initExplanTxtLanguage(content: string): void {
        if (content == null) {
            return;
        }

        this.bgExplanation.destroyAllChildren()
        
        this.bgExplanation.removeAllChildren(true);
        let explanTxt: string = content;
        
        let explanTxtArr: string[] = explanTxt.split('\n');
        
        if (explanTxtArr.length == 0) {
            return;
        }

        for (let i = 0; i < explanTxtArr.length; ++i) {
            let itemNode: cc.Node = cc.instantiate(this.ItemNode);

            let size: cc.Size = cv.resMgr.getLabelStringSize(itemNode.children[1].getComponent(cc.Label), explanTxtArr[i]);
            
            itemNode.height = size.height;
            
            itemNode.active = true;
            
            //背景图片bgExplanation的anchor y是1  所以子节点itemNode要向左边偏移
            itemNode.x = -(this.bgExplanation.getContentSize().width - (this.bgExplanation.getContentSize().width - itemNode.width) * 0.5);

            this.bgExplanation.addChild(itemNode);
        }
    }

    showToolTip(): void {
        this.bgExplanation.active = true;
        
        var content = cv.StringTools.formatC(cv.config.getStringData("USDTView_exchange_tips_label"), this.coin_2_usdt_TotalFreeTime, this.usdt_exchange_interval);
        
        if (content == null) {
            return;
        }

        this.bgExplanation.destroyAllChildren()
        
        this.bgExplanation.removeAllChildren(true);

        let explanTxt: string = content;

        let explanTxtArr: string[] = explanTxt.split('\n');

        if (explanTxtArr.length == 0) {
            return;
        }

        let maxWidth = 0;

        for (let i = 0; i < explanTxtArr.length; ++i) {
            let itemNode: cc.Node = cc.instantiate(this.ItemNode);

            let size: cc.Size = cv.resMgr.getLabelStringSize(itemNode.children[1].getComponent(cc.Label), explanTxtArr[i]);
            
            itemNode.height = size.height;
            
            itemNode.active = true;
            
            itemNode.children[1].getComponent(cc.Label).string = "• " + explanTxtArr[i];
            
            this.bgExplanation.addChild(itemNode);

            if (size.width > maxWidth) {
                maxWidth = size.width;
            }
        }

        let newItemNodeX = Math.max((this.bgExplanation.width - maxWidth) / 2, 0)

        for (let i = 0; i < this.bgExplanation.children.length; ++i) {
            if (newItemNodeX == 0) {
                this.bgExplanation.children[i].children[1].getComponent(cc.Label).overflow = cc.Label.Overflow.SHRINK;
            }

            cv.resMgr.adaptWidget(this.bgExplanation.children[i], true);
            
            this.bgExplanation.children[i].getComponent(cc.Widget).left = newItemNodeX;
            
            this.bgExplanation.children[i].getComponent(cc.Widget).updateAlignment();
        }
    }

    updateCoinAndUSDT() {

    }

    onBack(evt: cc.Event) {
        this.resetTabTestExNum(this.select_index);

        this.disableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);

        cv.AudioMgr.playButtonSound('back_button');

        cv.action.showAction(this.node
            , cv.action.eMoveActionDir.EMAD_TO_RIGHT
            , cv.action.eMoveActionType.EMAT_FADE_OUT
            , cv.action.delay_type.NORMAL
            , (target: cc.Node, actIO: number): void => {
                this.backBtn.opacity = 0;
            }
            , (target: cc.Node, actIO: number): void => {

            });
    }

    onUSDT2Coin(evt: cc.Event) {
        cv.AudioMgr.playButtonSound('button_click');

        this.titleText.string = cv.config.getStringData("conversion_goldcoins_usdt");

        this.switchTab(0);
    }

    onCoin2USDT(evt: cc.Event) {
        cv.AudioMgr.playButtonSound('button_click');

        this.titleText.string = cv.config.getStringData("conversion_usdt_goldcoins");

        this.switchTab(1);
    }

    onTestCoin2USDT(text: string, editbox: cc.EditBox, customEventData: string) {
        super.onTestCoin2USDT(text, editbox, customEventData);

        if (parseInt(text) > 0)
            this.enableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);
        else
            this.disableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);

        this.onUpdateUsdtInput();
    }

    onTestUSDT2Coin(text: string, editbox: cc.EditBox, customEventData: string) {
        super.onTestUSDT2Coin(text, editbox, customEventData);

        if (parseInt(text) > 0)
            this.enableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);
        else
            this.disableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);

        this.onUpdateUsdtInput();
    }

    //从输入框里面获取usdt值
    onUpdateUsdtInput() {
        if (!this.usdtNode || !this.usdtNode.active) {
            return;
        }

        let usdtEditBox: cc.EditBox = this.getCoinEditBoxNode(this.usdtNode).getComponent(cc.EditBox);

        let inputVal: string = usdtEditBox.string;

        if (!this.checkTestExNumber(inputVal) || inputVal == "") {
            this.updateExchangeFeeTips(0);

            this.updateExchangePointTips(0);

            return;
        }

        let exNum: number = parseFloat(inputVal);

        if (isNaN(exNum) || exNum <= 0) {
            this.updateExchangeFeeTips(0);

            this.updateExchangePointTips(0);
        } 
        else {
            this.updateExchangeFeeTips(exNum);

            this.updateExchangePointTips(exNum);
        }
    }

    switchTab(index: number) {
        if ((index != 0 && index != 1) || this.select_index == index) {
            return;
        }

        this.disableExchnageButton(this.coinNode);

        this.disableExchnageButton(this.usdtNode);

        super.switchTab(index);

        this.bgExplanation.active = false;

        if (this.usdtNode && this.usdtNode.active) {
            //当前是金币兑换usdt选项
            cv.worldNet.ExchangeGetUsdtConfigRequest(); //

            this.updateExhangeFreeTimesTips();
        } 
        else if (this.coinNode && this.coinNode.active) {
            this.hideExchangeTxtTips();
        }
    }

    formatExRate(rateNum: number): string {
        if (isNaN(rateNum) || rateNum <= 0) {
            return "";
        }

        return cv.StringTools.handleNumberByFloor(rateNum, 4).toString();
    }

    onTabExResponse(msg: any) {
        super.onTabExResponse(msg);

        this.hideExchangeTxtTips();

        this.disableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);

        this.bgExplanation.active = false;
    }

    onCoin2USDTAll(evt: cc.Event) {
        super.onCoin2USDTAll(evt);

        this.onUpdateUsdtInput();

        let srcEditBox: cc.EditBox = this.getCoinEditBoxNode(this.usdtNode).getComponent(cc.EditBox);

        if (parseInt(srcEditBox.string) > 0)
            this.enableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);
        else
            this.disableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);

        this.bgExplanation.active = false;
    }

    onUSDT2CoinAll(evt: cc.Event) {
        super.onUSDT2CoinAll(evt);

        let srcEditBox: cc.EditBox = this.getUSDTEditBoxNode(this.coinNode).getComponent(cc.EditBox);

        if (parseInt(srcEditBox.string) > 0)
            this.enableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);
        else
            this.disableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);

        this.bgExplanation.active = false;
    }

    //积分提示
    onBtnPointQuestion(event: cc.Event) {
        var node = event.target;

        let btnWorldPos = node.convertToWorldSpaceAR(cc.v2(0.5, 0.5));

        let pos = this.bgExplanation.parent.convertToNodeSpaceAR(btnWorldPos);

        this.bgExplanation.setPosition(pos.x + 40, pos.y - node.getContentSize().height / 2 + 10);

        cv.AudioMgr.playButtonSound('button_click');

        this.bgExplanation.active = true;

        let str = cv.StringTools.formatC(cv.config.getStringData("USDTView_exchange_tips_label"), this.coin_2_usdt_TotalFreeTime, this.usdt_exchange_interval)
        
        this.initExplanTxtLanguage(str);
    }

    onEditBegin(text: any, editbox: cc.EditBox, customEventData: string) {
        super.onEditBegin(text, editbox, customEventData);
        
        this.setEditboxActive(text as cc.EditBox);
        
        this.bgExplanation.active = false;
    }

    onEditEnd(text: string, editbox: cc.EditBox, customEventData: string) {
        this.setEditBoxInactiveSprite();
    }

    setEditboxActive(editbox: cc.EditBox) {
        this.setEditBoxInactiveSprite();

        if (this.select_index == 0) {

            if (editbox.node.name == "editbox_usdt")
                cc.find("input_node/from/input", this.coinNode).getComponent(cc.Sprite).spriteFrame = this.borderSpriteFrame[1];
            else
                cc.find("input_node/to/input", this.coinNode).getComponent(cc.Sprite).spriteFrame = this.borderSpriteFrame[1];
        } 
        else if (this.select_index == 1) {
            if (editbox.node.name == "editbox_usdt")
                cc.find("input_node/to/input", this.usdtNode).getComponent(cc.Sprite).spriteFrame = this.borderSpriteFrame[1];
            else
                cc.find("input_node/from/input", this.usdtNode).getComponent(cc.Sprite).spriteFrame = this.borderSpriteFrame[1];
        }
    }

    setEditBoxInactiveSprite() {
        cc.find("input_node/from/input", this.coinNode).getComponent(cc.Sprite).spriteFrame = this.borderSpriteFrame[0];

        cc.find("input_node/to/input", this.coinNode).getComponent(cc.Sprite).spriteFrame = this.borderSpriteFrame[0];

        cc.find("input_node/from/input", this.usdtNode).getComponent(cc.Sprite).spriteFrame = this.borderSpriteFrame[0];

        cc.find("input_node/to/input", this.usdtNode).getComponent(cc.Sprite).spriteFrame = this.borderSpriteFrame[0];
    }

    resetTabTestExNum(index: number) {
        super.resetTabTestExNum(index);

        this.hideExchangeTxtTips();

        this.feeTips.active = true;
    }

    //隐藏兑换提示
    hideExchangeTxtTips() {
        this.txt_feeTips.node.active = false;

        this.open_use_point = false;

        this.setShowPointStatus(false);

        this.setBtnSpriteStatus(this.open_use_point);
    }

    //刷新免费剩余次数
    updateExhangeFreeTimesTips() {
        this.feeTips.active = true;

        let _freeCount = this.coin_2_usdt_freeTime >= 0 ? this.coin_2_usdt_freeTime : 0;

        this.txt_freeTimes.string = _freeCount.toString();
    }

    //刷新费率提示 
    //dstCoinNum：当前要转换的目标金币值
    //pointFreeCoin ： 积分折扣的金币值
    updateExchangeFeeTips(dstCoinNum: number = 0, pointFreeCoin: number = 0) {
        //当前剩余次数大于0，或者目标兑换usdt <=0 隐藏提示
        if (this.coin_2_usdt_freeTime > 0 || dstCoinNum <= 0) {
            this.txt_feeTips.node.active = false;

            return;
        }

        this.txt_feeTips.node.active = true;

        let _fee = this.coin_2_usdt_feeRatio;

        let _curCoin: number = dstCoinNum - dstCoinNum * _fee + pointFreeCoin;

        let hlNum = this.getCoin2USDTExRate(); //当前汇率

        //因为汇率有6位小数，所以先转换成分，再做汇率运算
        _curCoin = cv.StringTools.serverGoldByClient(_curCoin);

        _curCoin = Math.floor(_curCoin);

        let _realUSDT: number = hlNum * _curCoin;

        _realUSDT = Math.floor(_realUSDT);

        let _realUSDTStr = cv.StringTools.numToFloatString(_realUSDT);  //再换算成客户端数值

        this.txt_feeTips.string = cv.StringTools.formatC(cv.config.getStringData("USDTView_usdt_change_fee_tips"), _fee * 100 + "%", _realUSDTStr);
    }

    //设置是否显示积分栏
    setShowPointStatus(status: boolean = false) {

    }

    //刷新积分提示
    updateExchangePointTips(dstCoinNum: number = 0) {
        let _curUserPoint = cv.dataHandler.getUserData().user_points;

        if (this.coin_2_usdt_freeTime > 0 || _curUserPoint <= 0 || dstCoinNum <= 0) {
            //剩余免费次数大于0   或者  用户积分为0，不显示积分兑换提示  或者 需要兑换的coin为0   那么，隐藏提示
            this.setShowPointStatus(false);

            return;
        }

        if (this.coin_2_usdt_pointRatio <= 0 || _curUserPoint < this.coin_2_usdt_pointRatio) {
            //积分兑换比为0，表示服务器没有配置  , 如果积分折扣的兑换的_usdt小于1个coin，此时也不显示
            this.setShowPointStatus(false);

            return;
        }

        this.setShowPointStatus(true);

        let _curShowUsePoint = "";

        //手续费, 相上兼容
        let _fee = cv.StringTools.toFixed(dstCoinNum * this.coin_2_usdt_feeRatio,
            2, cv.StringTools.RoundingMode.ROUND_UP);

        let _feeNeedPoints = _fee * this.coin_2_usdt_pointRatio;   //手续费对应的积分，保留到分

        let _pointDiscountCoin = 0;      //积分折扣的金币

        if (_curUserPoint > _feeNeedPoints) {
            //如果自己的积分，大于手续费所需要的积分
            _curShowUsePoint = cv.StringTools.numToFloatString(_feeNeedPoints);

            _pointDiscountCoin = _fee;
        } 
        else {
            //如果自己的积分，小于等于手续费所需要的积分。 自己全部积分将用上
            _curShowUsePoint = cv.StringTools.numToFloatString(_curUserPoint);

            //积分不足向下取
            _pointDiscountCoin = cv.StringTools.handleNumberByFloor(_curUserPoint / this.coin_2_usdt_pointRatio, 2);
        }

        if (this.open_use_point) {  //开启了可用开关
            this.updateExchangeFeeTips(dstCoinNum, _pointDiscountCoin);
        }
    }

    //开启/关闭使用积分
    onBtnUsePoint() {
        cv.AudioMgr.playButtonSound('button_click');

        this.open_use_point = !this.open_use_point;

        this.setBtnSpriteStatus(this.open_use_point);

        let srcEditBox: cc.EditBox = this.getCoinEditBoxNode(this.usdtNode).getComponent(cc.EditBox);

        let inputVal = srcEditBox.string;

        let dstNum = parseFloat(inputVal);

        if (isNaN(dstNum) || dstNum <= 0) {
            this.updateExchangeFeeTips(0);

            this.updateExchangePointTips(0);
        } 
        else {
            this.updateExchangeFeeTips(dstNum);

            this.updateExchangePointTips(dstNum);
        }
    }

    setBtnSpriteStatus(btnStatus: boolean = false) {

    }

    //获取兑换配置信息
    onExhangeConfigResponse(msg: any): void {
        this.coin_2_usdt_TotalFreeTime = msg.max_usdt_exchange_count;

        this.coin_2_usdt_freeTime = msg.left_usdt_exchange_count;

        this.coin_2_usdt_feeRatio = parseFloat(msg.usdt_fee_ratio);

        this.coin_2_usdt_pointRatio = msg.point_to_usd_deduction;

        this.usdt_exchange_interval = cv.StringTools.handleNumberByFloor(msg.usdt_exchange_interval / 60, 2);

        this.updateExhangeFreeTimesTips();
    }

    //兑换配置改变通知
    onExhangeConfigChangeNotice(msg: any): void {
        cv.worldNet.ExchangeGetUsdtConfigRequest();
    }

    onExchangeTimeLimitError(msg: any): void {
        this.bgExplanation.active = false;

        this.disableExchnageButton(this.select_index == 0 ? this.coinNode : this.usdtNode);

        cv.TT.showMsg(cv.StringTools.formatC(cv.config.getStringData("ServerErrorCode257"), this.usdt_exchange_interval), cv.Enum.ToastType.ToastTypeError);
    }

    onExplainClosed() {
        cv.AudioMgr.playButtonSound('close');

        this.usdtExplain.active = false;
    }

    onUsdtExplainBtnClicked() {
        cv.AudioMgr.playButtonSound('button_click');

        this.usdtExplain.active = true;

        this.showToolTip();
    }

    showBackBtn() {
        this.backBtn.opacity = 255;
    }

    setSafeArea() {
        cv.resMgr.adaptWidget(this.node, true);

        let hallScene = cc.director.getScene().getComponentInChildren(HallScene);

        this.top.height = hallScene.getTopHeight();

        // SafeArea
        let offsetY = cv.SafeAreaWithDifferentDevices.getSafeAreaHall();

        this.safearea.height = offsetY;

        let imgBgHeight = this.usdtView.height - offsetY - this.top.height;

        this.imgBg.height = imgBgHeight;

        this.layout.updateLayout();

        let usdtCoinParentHeight = this.imgBg.height - this.title.height;

        this.usdtCoinParent.height = usdtCoinParentHeight;

        this.imgBgLayout.updateLayout();

        cv.resMgr.adaptWidget(this.node, true);
    }
}
