
import { HashMap } from '../../../common/tools/HashMap';
import ListView from '../../../common/tools/ListView';
import cv from '../cv';
import InquireItem from './InquireItem';
import { InquireItemDate } from './InquireItemDate';
import HallScene from './HallScene';
import { pb } from '../../../common/pb/ws_protocol';

const { ccclass, property } = cc._decorator;

@ccclass
export default class InquireView extends cc.Component {
    @property(cc.Node) ndContent: cc.Node = null;
    @property(cc.Node) btn_arr: cc.Node[] = [];
    @property(ListView) scrollView: ListView = null;
    @property(cc.Prefab) item_prefab: cc.Prefab = null;
    @property(cc.Prefab) item_date_prefab: cc.Prefab = null;
    @property(cc.Label) lab_arr: cc.Label[] = [];
    @property(cc.Node) noList_icon: cc.Node = null;
    @property(cc.Node) bottom_node: cc.Node = null;
    @property(cc.Node) backBtn: cc.Node = null;
    @property(cc.Node) inquireView: cc.Node = null;
    @property(cc.Layout) layout: cc.Layout = null;
    @property(cc.Node) safearea: cc.Node = null;
    @property(cc.Node) top: cc.Node = null;
    @property(cc.Node) imgBg: cc.Node = null;
    @property(cc.Layout) imgBgLayout: cc.Layout = null;
    @property(cc.Node) title: cc.Node = null;
    @property(cc.Node) inquireIcon: cc.Node = null;
    @property(cc.Node) scrollView1: cc.Node = null;

    color_lab_select: cc.Color = cc.color(240, 213, 170);
    color_lab_noSelect: cc.Color = cc.color(255, 255, 255);
    list_arr: HashMap<pb.BankDetailsType, pb.BankDetailsSnapshot[]> = new HashMap();
    pre_pull: HashMap<pb.BankDetailsType, pb.BankDetailsQueryResponse> = new HashMap();
    no_pre_pull: HashMap<pb.BankDetailsType, pb.BankDetailsQueryResponse> = new HashMap();
    select_index: number = 0;
    isView: boolean = false; // 用于列表刷新

    onLoad() {
        this.setSafeArea();
    }

    onEnable() {
        if (this.select_index >= 0) {
            this.setBtnState(this.select_index);
            this.show(this.select_index);
        }
    }

    start() {
        this.registerMsg();
        cv.resMgr.adaptWidget(this.scrollView.node, true);
        this.scrollView.bindScrollEventTarget(this);
        this.scrollView.init(this.bindcallfunc.bind(this), this.getItemType.bind(this));
        this.noList_icon.active = false;
        this.setBtnState(0);
        const len = this.btn_arr.length;
        for (let i = 0; i < len; i++) {
            this.btn_arr[i].on(
                'click',
                () => {
                    cv.AudioMgr.playButtonSound('button_click');
                    this.select_index = i;
                    this.setNolistIcon();
                    this.setBtnState(i);
                    this.show(i);
                },
                this
            );
        }

        cc.find('LayoutParent/top/btn_back', this.node).on(
            'click',
            () => {
                cv.AudioMgr.playButtonSound('back_button');
                cv.action.showAction(
                    this.node,
                    cv.action.eMoveActionDir.EMAD_TO_RIGHT,
                    cv.action.eMoveActionType.EMAT_FADE_OUT,
                    cv.action.delay_type.NORMAL,
                    (target: cc.Node, actIO: number): void => {
                        this.backBtn.opacity = 0;
                    },
                    (target: cc.Node, actIO: number): void => {}
                );
            },
            this
        );
        this.initLanguage();
    }

    registerMsg() {
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);
        cv.MessageCenter.register('InquireView_QueryResponse', this.BankDetailsQueryResponse.bind(this), this.node);
    }

    onDestroy() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
        cv.MessageCenter.unregister('InquireView_QueryResponse', this.node);
    }

    initLanguage() {
        for (let i = 0; i < this.lab_arr.length - 1; i++) {
            this.lab_arr[i].string = cv.config.getStringData('InquireView_lab_' + i);
        }
        this.setNolistIcon();
    }

    setNolistIcon() {
        const index = this.lab_arr.length - 1;
        const temp_select = this.select_index + 1;
        let str = cv.config.getStringData('InquireView_lab_' + temp_select);
        str = this.select_index === 3 ? 'USD' : str;
        this.lab_arr[index].string = cv.StringTools.formatC(cv.config.getStringData('InquireView_lab_' + index), str);
    }

    onSVEventScrollToBottom(arg: cc.ScrollView): void {
        this.BankDetailsQueryRequest(this.select_index, true);
    }

    onSVEventScrollToTop(arg: cc.ScrollView): void {
        this.BankDetailsQueryRequest(this.select_index, false);
    }

    setBtnState(index: number) {
        for (let i = 0; i < this.btn_arr.length; i++) {
            const btn = this.btn_arr[i];
            if (i === index) {
                btn.getChildByName('Label').color = this.color_lab_select;
                btn.getChildByName('Background').active = true;
            } else {
                btn.getChildByName('Label').color = this.color_lab_noSelect;
                btn.getChildByName('Background').active = false;
            }
        }
    }

    show(index: number) {
        this.isView = false;
        const msg = this.list_arr.get(index);
        if (!msg) {
            this.BankDetailsQueryRequest(index, false);
        } else {
            this.reloadView(msg, true);
            this.updateViewState(msg);
            this.isView = msg.length > 0;
            this.noList_icon.active = !this.isView;
            this.bottom_node.active = this.isView;
            if (msg.length <= 0) {
                this.BankDetailsQueryRequest(index, false);
            }
        }
    }

    BankDetailsQueryRequest(type: pb.BankDetailsType, is_prev_pull: boolean) {
        const hash = is_prev_pull ? this.pre_pull : this.no_pre_pull;
        const pull_count = 20;
        const msg = hash.get(type);
        let data = null;

        if (!msg) {
            data = {
                detail_type: type,
                is_prev_pull,
                pull_count,
                pull_pos: 0,
                begin_time: 0,
                end_time: 0,
                table_suffix_time: 0
            };
        } else {
            data = {
                detail_type: type,
                is_prev_pull,
                pull_count,
                pull_pos: is_prev_pull ? msg.first_inc_id : msg.last_inc_id,
                begin_time: msg.begin_time,
                end_time: msg.end_time,
                table_suffix_time: msg.table_suffix_time
            };
        }

        cv.worldNet.BankDetailsQueryRequest(data);
    }

    BankDetailsQueryResponse(data: pb.BankDetailsQueryResponse) {
        const len = cv.StringTools.getArrayLength(data.snapshots);
        if (len > 0) {
            const hash = data.is_prev_pull ? this.pre_pull : this.no_pre_pull;
            const msg = pb.BankDetailsQueryResponse.create(data);
            hash.add(data.detail_type, msg);
            if (data.is_prev_pull) {
                let item = this.no_pre_pull.get(data.detail_type);
                if (!item) {
                    item = pb.BankDetailsQueryResponse.create(data);
                    item.is_prev_pull = false;
                    this.no_pre_pull.add(data.detail_type, item);
                }
            } else {
                let item = this.pre_pull.get(data.detail_type);
                if (!item) {
                    item = pb.BankDetailsQueryResponse.create(data);
                    item.is_prev_pull = true;
                    this.pre_pull.add(data.detail_type, item);
                }
            }

            let sno = this.list_arr.get(data.detail_type);
            if (!sno) {
                sno = [];
            }

            if (data.is_prev_pull) {
                for (let i = len - 1; i >= 0; i--) {
                    sno.unshift(pb.BankDetailsSnapshot.create(data.snapshots[i]));
                }
            } else {
                for (let i = 0; i < len; i++) {
                    sno.push(pb.BankDetailsSnapshot.create(data.snapshots[i]));
                }
            }
            this.list_arr.add(data.detail_type, sno);
        }

        let arr = this.list_arr.get(data.detail_type);
        if (!arr) {
            arr = [];
        }
        this.reloadView(arr, !this.isView);
        this.updateViewState(arr);
        const result = cv.StringTools.getArrayLength(arr) <= 0;
        this.isView = !result;
        this.noList_icon.active = result;
        this.bottom_node.active = !result;
    }

    private reloadView(dataArray: any[], resetPos?: boolean): void {
        if (resetPos) this.scrollView.sv.scrollToTop(0);

        dataArray.forEach((el) => {
            el.dayEpoch = cv.config.getTimeWithTimeZone(el.create_time * 1000).setHours(0, 0, 0, 0);
        });
        let dataList = [];
        let daysAdded: number[] = [];

        for (let index: number = 0; index < dataArray.length; index++) {
            if (!daysAdded.includes(dataArray[index].dayEpoch)) {
                dataList.push({ type: 1, data: dataArray[index] });
                daysAdded.push(dataArray[index].dayEpoch);
            }
            dataList.push({ type: 0, data: dataArray[index] });
        }
        this.scrollView.notifyDataSetChanged(dataList);
    }

    private updateViewState(dataArray: any[]): void {
        this.isView = cv.StringTools.getArrayLength(dataArray) > 0;

        if (this.isView) {
            this.noList_icon.active = false;
            this.scrollView.node.active = true;
        } else {
            this.scrollView.node.active = false;
            this.noList_icon.active = true;
        }
    }

    public bindcallfunc(node: cc.Node, info, i) {
        if (info.type === 0) {
            node.getComponent(InquireItem).updateItemData(i, info.data);
        } else if (info.type === 1) {
            node.getComponent(InquireItemDate).updateItemData(i, info.data);
        }
    }

    public getItemType(data, index) {
        return data.type;
    }

    showBackBtn() {
        this.backBtn.opacity = 255;
    }

    setSafeArea() {
        cv.resMgr.adaptWidget(this.node, true);
        const hallScene = cc.director.getScene().getComponentInChildren(HallScene);
        this.top.height = hallScene.getTopHeight();

        // SafeArea
        const offsetY = cv.SafeAreaWithDifferentDevices.getSafeAreaHall();
        this.safearea.height = offsetY;
        const imgBgHeight = this.inquireView.height - offsetY - this.top.height;
        this.imgBg.height = imgBgHeight;
        this.layout.updateLayout();

        const scrollView1Height = this.imgBg.height - this.title.height - this.inquireIcon.height;
        this.scrollView1.height = scrollView1Height;
        this.imgBgLayout.updateLayout();

        cv.resMgr.adaptWidget(this.node, true);
    }
}
