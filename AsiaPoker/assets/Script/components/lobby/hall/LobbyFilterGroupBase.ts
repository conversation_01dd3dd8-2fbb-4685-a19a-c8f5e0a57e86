import cv from '../cv';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;

export default class LobbyFilterGroupBase {
    protected stakes: Map<world_pb.GameSizeType, Set<string>> = new Map([
        [world_pb.GameSizeType.GameSizeTypeMicro, new Set<string>()],
        [world_pb.GameSizeType.GameSizeTypeSmall, new Set<string>()],
        [world_pb.GameSizeType.GameSizeTypeMedium, new Set<string>()],
        [world_pb.GameSizeType.GameSizeTypeHigh, new Set<string>()],
    ]);

    _id: number;

    public get ID(): number {
        return this._id;
    }

    public SetID(id: number): void {
        this._id = id;
    }

    constructor() {
        this.stakes.forEach(set=>set.clear());
    }

    public addEntry(size: world_pb.GameSizeType, value: string): void {
        this.stakes.get(size).add(value)
    }

    public GetStakesArray(size: number): string[] {
        return Array.from(this.stakes.get(size));
    }

    public SortAllStakeLevels(): void {
        for (let i = world_pb.GameSizeType.GameSizeTypeMicro; i <= world_pb.GameSizeType.GameSizeTypeHigh; i++) {
            let dataSet: Set<string> = this.stakes.get(i);
            const sortedArray = Array.from(dataSet).sort((a, b): number => {
                let c = a.replace(/K/i, '000');
                let d = b.replace(/K/i, '000');
                let isHuc = false;
                let isHud = false;

                const cIndex = c.indexOf(' (HU)');
                const dIndex = d.indexOf(' (HU)');

                if (cIndex !== -1) {
                    c = c.slice(0, cIndex);
                    isHuc = true;
                }

                if (dIndex !== -1) {
                    d = d.slice(0, dIndex);
                    isHud = true;
                }

                const e = c.split('/');
                const f = d.split('/');

                if (cv.Number(e[0]) === cv.Number(f[0])) {
                    if (e.length === f.length) {
                        if (isHuc === isHud) {
                            return -1;
                        }
                        return isHuc ? 1 : -1;
                    }
                    return e.length - f.length;
                }
                return cv.Number(e[0]) - cv.Number(f[0]);
            });

            // Update the set with sorted values
            dataSet.clear();
            sortedArray.forEach(value => dataSet.add(value));
        }
    }

    public getStakeIndexByItem(item: world_pb.ClubGameSnapshotV3): number {
        let sIndex: number = 0;
        if (item.game_id === cv.Enum.GameId.Bet) {
            const ante = item.ante;
            if (item.is_mirco === 1) {
                sIndex = world_pb.GameSizeType.GameSizeTypeMicro;
            } else if (ante <= 500) {
                sIndex = world_pb.GameSizeType.GameSizeTypeSmall;
            } else if (ante >= 1000 && ante <= 80000) {
                sIndex = world_pb.GameSizeType.GameSizeTypeMedium;
            } else if (ante > 80000) {
                sIndex = world_pb.GameSizeType.GameSizeTypeHigh;
            }
        } else if (item.game_id === cv.Enum.GameId.Jackfruit) {
            const ante = item.ante;
            if (item.is_mirco === 1) {
                sIndex = world_pb.GameSizeType.GameSizeTypeMicro;
            } else if (ante <= 200) {
                sIndex = world_pb.GameSizeType.GameSizeTypeSmall;
            } else if (ante >= 500 && ante <= 2000) {
                sIndex = world_pb.GameSizeType.GameSizeTypeMedium;
            } else if (ante > 2000) {
                sIndex = world_pb.GameSizeType.GameSizeTypeHigh;
            }
        } else {
            //  微: < 1， 小: < 5，中: <=100，大 : >100
            const small_blind =
                item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? item.ante : item.small_blind;
            if (small_blind < 100) {
                sIndex = world_pb.GameSizeType.GameSizeTypeMicro;
            } else if (small_blind < 500) {
                sIndex = world_pb.GameSizeType.GameSizeTypeSmall;
            } else if (small_blind <= 10000) {
                if (
                    item.big_blind === 20000 &&
                    item.straddle &&
                    item.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal &&
                    !cv.roomManager.checkGameIsZoom(item.game_id)
                ) {
                    sIndex = world_pb.GameSizeType.GameSizeTypeHigh;
                } else {
                    sIndex = world_pb.GameSizeType.GameSizeTypeMedium;
                }
            } else {
                sIndex = world_pb.GameSizeType.GameSizeTypeHigh;
            }
        }

        if (item.stickOnLevelTab === world_pb.GameLevelEnum.GameLevelEnumMicro) {
            sIndex = world_pb.GameSizeType.GameSizeTypeMicro;
        } else if (item.stickOnLevelTab === world_pb.GameLevelEnum.GameLevelEnumSmall) {
            sIndex = world_pb.GameSizeType.GameSizeTypeSmall;
        } else if (item.stickOnLevelTab === world_pb.GameLevelEnum.GameLevelEnumMedium) {
            sIndex = world_pb.GameSizeType.GameSizeTypeMedium;
        } else if (item.stickOnLevelTab === world_pb.GameLevelEnum.GameLevelEnumHigh) {
            sIndex = world_pb.GameSizeType.GameSizeTypeHigh;
        }
        return sIndex;
    }
}