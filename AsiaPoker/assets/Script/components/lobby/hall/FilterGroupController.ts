import BaseFilterController from './BaseFilterController';
import CurrencyFilterController from './CurrencyFilterController';
import GameTypeFilterController from './GameTypeFilterController';
import ModeTypeFilterController from './ModeTypeFilterController';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import StakesFilterController from './StakesFilterController';
import LobbyFilterGroupBase from './LobbyFilterGroupBase';
import cv from '../cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class FilterGroupController extends cc.Component {
    @property(cc.Label) title: cc.Label = null;
    @property(cc.Layout) layout: cc.Layout = null;
    @property(cc.Node) stakesTitle: cc.Node = null;
    @property(cc.Prefab) currencyFilterContentPrefab: cc.Prefab = null;
    @property(cc.Prefab) gameTypeFilterContentPrefab: cc.Prefab = null;
    @property(cc.Prefab) modeTypeFilterContentPrefab: cc.Prefab = null;
    @property(cc.Prefab) fastEnterStakesTitlePrefab: cc.Prefab = null;
    @property(cc.Prefab) fastEnter_content: cc.Prefab = null;

    protected filterControllers: BaseFilterController[] = [];
    protected filterGroupsData: LobbyFilterGroupBase;

    protected readonly gametypeEntries: number[] = [4, 3, 30, 1, 2, 5, 7, 8];
    protected readonly saveSeparatorChar = '|';

    public initialize() {
        const currencyFilterContentInstance = cc.instantiate(this.currencyFilterContentPrefab);
        currencyFilterContentInstance.setParent(this.layout.node);
        const gameTypeFilterContentInstance = cc.instantiate(this.gameTypeFilterContentPrefab);
        gameTypeFilterContentInstance.setParent(this.layout.node);
        const modeTypeFilterContentInstance = cc.instantiate(this.modeTypeFilterContentPrefab);
        modeTypeFilterContentInstance.setParent(this.layout.node);

        let controller;
        controller = currencyFilterContentInstance.getComponent(CurrencyFilterController);
        this.filterControllers.push(controller);

        controller = gameTypeFilterContentInstance.getComponent(GameTypeFilterController);
        this.filterControllers.push(controller);

        controller = modeTypeFilterContentInstance.getComponent(ModeTypeFilterController);
        this.filterControllers.push(controller);

        this.stakesTitle.setParent(this.layout.node);

        this.stakesTitle.setSiblingIndex(this.layout.node.childrenCount-1);

        for (let j = world_pb.GameSizeType.GameSizeTypeMicro; j <= world_pb.GameSizeType.GameSizeTypeHigh; j++) {
            // Instantiating Stakes content and populating them with data:
            const fastEnter_content = cc.instantiate(this.fastEnter_content);
            fastEnter_content.setParent(this.layout.node);

            controller = fastEnter_content.getComponent(StakesFilterController);
            controller.SetStakeSize(j);
            this.filterControllers.push(controller);
        }

        this.filterControllers.forEach((x) => {
            x.node.active = false;
        });
    }

    public initControllers(gamdID:number, gameType:number, currencies:number[], gameTypes:string[], gameModes: string[]): void {
        const currencyController = this.getCurrencyFilterController();
        if (currencies.length > 1) {
            // For any mode if more than 1 currencies are detected in table-list then spawn currency filter buttons.
            this.getCurrencyFilterController().isActive = true;
            currencyController.initialize(currencies, gamdID);
        }

        if (this.getGameTypeFilterController().isApplicable(gameType)) {
            const gameTypeController = this.getGameTypeFilterController();
            if (gameTypes.length > 1) {
                this.getGameTypeFilterController().isActive = true;
                gameTypeController.initialize(gameTypes, gamdID);
            }
        }

        if (this.getModeTypeFilterController().isApplicable(gameType)) {
            // Instantiate Mode-type filter buttons if needed:
            const modeTypeController = this.getModeTypeFilterController();
            if (gameModes.length > 1) {
                this.getModeTypeFilterController().isActive = true;
                modeTypeController.initialize(gameModes, gamdID);
            }
        }
    }

    public setData(
        key:number,
        value: LobbyFilterGroupBase
    ): void {
        this.filterGroupsData = value;
        this.initLanguage();
        this.layout.updateLayout();
    }

    public getCurrencyFilterController() {
        return this.filterControllers.find((x) => x instanceof CurrencyFilterController) as CurrencyFilterController;
    }

    public getGameTypeFilterController() {
        return this.filterControllers.find((x) => x instanceof GameTypeFilterController) as GameTypeFilterController;
    }

    public getModeTypeFilterController() {
        return this.filterControllers.find((x) => x instanceof ModeTypeFilterController) as ModeTypeFilterController;
    }

    public getStakesFilterController(size: world_pb.GameSizeType) {
        return this.filterControllers.find(
            (x) => x instanceof StakesFilterController && (<StakesFilterController>x).StakeSize === size
        ) as StakesFilterController;
    }

    public getFilterControllers() {
        return this.filterControllers;
    }

    public refreshData(key:number, value): void {
        this.filterGroupsData = value;
        this.getCurrencyFilterController().refreshByData();
        this.getGameTypeFilterController().refreshByData();
        this.getModeTypeFilterController().refreshByData();
        this._updateStakeControllers(key);
        this.layout.updateLayout();
    }

    public getAllStakes(): string[] {
        const arr: string[] = [];
        const id = this.gametypeEntries[this.filterGroupsData.ID];
        for (let i = world_pb.GameSizeType.GameSizeTypeMicro; i <= world_pb.GameSizeType.GameSizeTypeHigh; i++) {
            const stakeController = this.getStakesFilterController(i);
            stakeController.filterData.forEach((element) => {
                arr.push(element + '+' + id);
            });
        }
        return arr;
    }

    public isCommonFiltersEmpty(): boolean {
        return (
            this.getCurrencyFilterController().filterData.length === 0 &&
            this.getGameTypeFilterController().filterData.length === 0 &&
            this.getModeTypeFilterController().filterData.length === 0
        );
    }

    public isStakeFiltersEmpty(): boolean {
        for (let i = world_pb.GameSizeType.GameSizeTypeMicro; i <= world_pb.GameSizeType.GameSizeTypeHigh; i++) {
            const stakeController = this.getStakesFilterController(i);
            if (stakeController.filterData.length > 0) {
                return false;
            }
        }
        return true;
    }

    public isAllFilterEmpty(): boolean {
        return this.isCommonFiltersEmpty() && this.isStakeFiltersEmpty();
    }

    public SaveDataToFile() {
        this.filterControllers.forEach((x) => {
            x.saveFilterDataToFile();
        });
    }

    public initLanguage():void{
        const titleName: string[] = [
            cv.config.getStringData('DataView_data_panel_dataInfo_panel_bet_button'), // 必下
            cv.config.getStringData('DataView_data_panel_dataInfo_panel_aofGame_button'), // aof
            cv.config.getStringData('DataView_data_panel_dataInfo_panel_aofGameShort_button'), // aof短牌
            cv.config.getStringData('Filtrate_filtrate_panel_normal_game_button_2'), // 德州
            cv.config.getStringData('DataView_gameType_panel_button_1_text'), // 短牌
            cv.config.getStringData('DataView_data_panel_dataInfo_panel_zoomGame_button'), // 急速扑克
            cv.config.getStringData('MainScene_Scene_gameType_panel_button7_text'), // 菠萝蜜
            cv.config.getStringData('MainScene_Scene_gameType_panel_button8_text') // 奥马哈
        ];
        this.title.string = titleName[ this.filterGroupsData.ID];
        this.stakesTitle.getChildByName('StakeTitle').getComponent(cc.Label).string =
            cv.config.getStringData('Filter_Stake_Title_Text');

        this.filterControllers.forEach(controller => controller.initLanguage());
    }

    private _updateStakeControllers(gameID:number): void {
        for (let j = world_pb.GameSizeType.GameSizeTypeMicro; j <= world_pb.GameSizeType.GameSizeTypeHigh; j++) {
            const dataArr = this.filterGroupsData.GetStakesArray(j);
            const len = cv.StringTools.getArrayLength(dataArr);
            const controller = this.getStakesFilterController(j);
            if (len > 0) {
                controller.initialize(
                    j,
                    dataArr,
                    this.gametypeEntries[this.filterGroupsData.ID],
                    true,
                    gameID
                );
                controller.refreshByData();
            } else {
                controller.node.active = false;
            }
        }
    }
}
