
import cv from "../cv";
import LoginScene from "../login/LoginScene";
const { ccclass, property } = cc._decorator;

@ccclass
export default class RessetPassword extends cc.Component {

    @property(cc.Node) ResetPassword_panel: cc.Node = null;

    @property(cc.Node) inputBg: cc.Node[] = [];  
    @property(cc.Node) inputBgChoice: cc.Node[] = [];  
    @property(cc.Node) inputBgError: cc.Node[] = [];  
    @property(cc.Node) inputTipsNode: cc.Node[] = [];  

    @property(cc.Node) pass_input_node1: cc.Node = null;
    @property(cc.Node) pass_input_node2: cc.Node = null;

    @property(cc.Node) btnShowPass1: cc.Node = null;
    @property(cc.Node) btnShowPass2: cc.Node = null;

    @property(cc.Node) txtBtnback: cc.Node = null;  
    @property(cc.Node) okButton: cc.Node = null;  
    @property(cc.Node) resultDlg: cc.Node = null;  

    private _passInputNode_y:number = 0;
    private _areaCode:number = 0;
    private _phoneNum:number = 0;

    onLoad() {
        this.registerMsg();
        this.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event) => { event.stopPropagation(); }, this);
        cv.resMgr.adaptWidget(this.node);
        this.initText();
    }

    start() {
        cv.resMgr.adaptWidget(this.pass_input_node2);
        this._passInputNode_y = this.pass_input_node2.y;
       
    }
    onDestroy() {
        cv.MessageCenter.unregister("onCheckNewPsdSuccess", this.node);
    }

    onEnable(){
        this.resultDlg.active = false;
        this.OnClear();
        this.okButton.getComponent(cc.Button).interactable = true;
        this.resetPageStatus();
    }

    setData(areaCode, phoneNum) {
        this._areaCode = areaCode;
        this._phoneNum = phoneNum;
    }

    registerMsg() {
        cv.MessageCenter.register("onCheckNewPsdSuccess", this.onResetSuccess.bind(this), this.node);
    }

    initText() {
        this.txtBtnback.getComponent(cc.Label).string = cv.config.getStringData("BackBtnTitle");
        cv.StringTools.setLabelString(this.resultDlg, "dlg/tips", "Login_Scene_register_tips_sucess");

        cc.find("code_panel/password_text", this.ResetPassword_panel).getComponent(cc.EditBox).string = "";
        cc.find("code_panel_0/password_text", this.ResetPassword_panel).getComponent(cc.EditBox).string = "";

        cc.find("code_panel/password_text", this.ResetPassword_panel).getComponent(cc.EditBox).placeholder = cv.config.getStringData("ModifyPassword_ModifyPassword_panel_code_panel_password_text");
        cc.find("code_panel_0/password_text", this.ResetPassword_panel).getComponent(cc.EditBox).placeholder = cv.config.getStringData("ModifyPassword_ModifyPassword_panel_code_panel_0_password_text");

        cc.find("fok_button/Label", this.ResetPassword_panel).getComponent(cc.Label).string = cv.config.getStringData("TipsPanel_sure0_button");
        cc.find("title_text", this.ResetPassword_panel).getComponent(cc.Label).string = cv.config.getStringData("reset_password");

        cc.find("tipNode/txt", this.pass_input_node1).getComponent(cc.Label).string = cv.config.getStringData("Login_Scene_login_panel_password_Panel_password_text1");;
        cc.find("tipNode/txt", this.pass_input_node2).getComponent(cc.Label).string = cv.config.getStringData("Login_Scene_register_panel_passwd_diff");
    }

    onBtnBackClick() {
        cv.AudioMgr.playButtonSound('back_button');
        this.node.getParent().getComponent(LoginScene).exitResetPsdView();
    }

    onBtnSureClick() {
        cv.AudioMgr.playButtonSound('button_click');

        let kPassword1 = cc.find("code_panel/password_text", this.ResetPassword_panel).getComponent(cc.EditBox).string;
        let kPassword2 = cc.find("code_panel_0/password_text", this.ResetPassword_panel).getComponent(cc.EditBox).string;

        if (kPassword1 != kPassword2) {
            cv.TT.showMsg(cv.config.getStringData("ErrorToast17"), cv.Enum.ToastType.ToastTypeError);
            return;
        }

        if (cv.StringTools.getArrayLength(kPassword1) < 6 || cv.StringTools.getArrayLength(kPassword1) > 14) {
            cv.TT.showMsg(cv.config.getStringData("ErrorCode7"), cv.Enum.ToastType.ToastTypeError);
            return;
        }

        if (kPassword1.search(" ") != -1) {
            cv.TT.showMsg(cv.config.getStringData("recetPassWord_recetPassWord_panel_des_text"), cv.Enum.ToastType.ToastTypeError);
            return;
        }

        cv.httpHandler.requestCheckNewPsd(this._phoneNum, this._areaCode, kPassword1);
    }

    OnClear() {
        this.setInputStatus("passwordInput", 0);
        this.setInputStatus("passwordInput2", 0);

        cc.find("code_panel/password_text", this.ResetPassword_panel).getComponent(cc.EditBox).string = "";
        cc.find("code_panel_0/password_text", this.ResetPassword_panel).getComponent(cc.EditBox).string = "";
    }

    onResetSuccess(msg:any){
        this.okButton.getComponent(cc.Button).interactable = false;
        this.resultDlg.active = true;
        this.scheduleOnce(() => {
            this.node.getParent().getComponent(LoginScene).exitResetPsdView();
        },2);

        return;
    }

    //点击输入框
    public onBeginInputEdit(event: cc.Event, CustomEventData){

        let index = Number(CustomEventData) - 1;

        this.setTipsInputBg(index, 1);
        if(index == 0){
            this.setTipsNodeShow(index, true);
        }
    }

     //离开输入框
     public onEndInputEdit(event: cc.Event, CustomEventData){

        let index = Number(CustomEventData) - 1;

        let password_text1 = this.pass_input_node1.getChildByName("password_text").getComponent(cc.EditBox).string;
        let password_text2 = this.pass_input_node2.getChildByName("password_text").getComponent(cc.EditBox).string;

        this.setTipsNodeShow(0, false);

        if(password_text1.length > 0 && password_text2.length > 0 && password_text1 != password_text2){
            this.setTipsNodeShow(1, true); //密码是否匹配
        }else{
            this.setTipsNodeShow(1, false); //密码是否匹配
        }

         if(index == 0){  //密码输入框1
            let isValid = cv.tools.showError({
                password0: password_text1,
            }, false);
    
            if(password_text1.length <= 0){
                this.setInputStatus("passwordInput", 0);
            }else{
                this.setInputStatus("passwordInput", isValid?2:1);
            }

            if(password_text2.length > 0){
                if(password_text1 === password_text2 ){
                    this.setTipsInputBg(1, 0);
                    this.setInputStatus("passwordInput2", 1);
                }else{
                    this.setTipsInputBg(1, 2);
                    this.setInputStatus("passwordInput2", 2);
                }
            }

        }else if(index == 1){  //密码输入框2
    
            if(password_text2.length <= 0){
                this.setInputStatus("passwordInput2", 0);
            }else{
                if(password_text1 != password_text2){
                    this.setInputStatus("passwordInput2", 2);
                }else{
                    this.setInputStatus("passwordInput2", 1);
                }
            }
        }
    }

    //显示输入框的背景
    //index: 0: 密码输入框  1: 确认密码输入框
    //type:  0：正常背景（灰色） 1: 选中背景(黄色)  2: 错误背景（红色）
    private setTipsInputBg(index:number = 0, type:number = 0){

        for(let i = 0; i < this.inputBg.length; i++){
            this.inputBg[i].active = true;
        }
        for(let i = 0; i < this.inputBgChoice.length; i++){
            this.inputBgChoice[i].active = false;
        }
      
        if(type == 0){
            this.inputBgError[index].active = false;
            this.inputBg[index].active = true;
        }else if(type == 1){
            this.inputBgChoice[index].active = true;
            this.inputBg[index].active = false;
            this.inputBgError[index].active = false;
        }
    }

    //显示输入框的提示
    // 0: 密码输入框
    // 1: 确认密码输入框
    private setTipsNodeShow(index:number = 0, bShowTips:boolean = true){

        for(let i = 0; i < this.inputTipsNode.length; i++){
            this.inputTipsNode[i].active = false;
        }

        this.inputTipsNode[index].active = bShowTips;
    
        this.pass_input_node2.y = this._passInputNode_y;
        if(index == 0 && bShowTips){
            this.pass_input_node2.y =  this._passInputNode_y - 80;
        }
    }
    
    //密码 明文/密文 切换
    private onBtnShowPasswd(event: cc.Event, CustomEventData){
        cv.AudioMgr.playButtonSound('button_click');
        let index = Number(CustomEventData);
        let spHide = null;
        let spShow = null;
        let passEditBox = null;
        if(index == 0){
            passEditBox = this.pass_input_node1.getChildByName("password_text").getComponent(cc.EditBox);
            spHide =  this.btnShowPass1.getChildByName("spHide");
            spShow =  this.btnShowPass1.getChildByName("spShow");
        }else if(index == 1){
            passEditBox = this.pass_input_node2.getChildByName("password_text").getComponent(cc.EditBox);
            spHide =  this.btnShowPass2.getChildByName("spHide");
            spShow =  this.btnShowPass2.getChildByName("spShow");
        }

        if(spHide.active == true){
              spHide.active = false;
              spShow.active = true;
              passEditBox.inputFlag = cc.EditBox.InputFlag.SENSITIVE;
        }else{
              spHide.active = true;
              spShow.active = false;
              passEditBox.inputFlag = cc.EditBox.InputFlag.PASSWORD;
        }
    }

    private resetPageStatus(){
        let  passEditBox = this.pass_input_node1.getChildByName("password_text").getComponent(cc.EditBox);
        this.btnShowPass1.getChildByName("spHide").active = true;
        this.btnShowPass1.getChildByName("spShow").active = false;
        passEditBox.inputFlag = cc.EditBox.InputFlag.PASSWORD;

        let passEditBox2 = this.pass_input_node2.getChildByName("password_text").getComponent(cc.EditBox);
        this.btnShowPass2.getChildByName("spHide").active = true;
        this.btnShowPass2.getChildByName("spShow").active = false;
        passEditBox2.inputFlag = cc.EditBox.InputFlag.PASSWORD;


        this.setInputStatus("passwordInput", 0);
        this.setInputStatus("passwordInput2", 0);

        this.setTipsInputBg(0, 0);
        this.setTipsInputBg(1, 0);


        this.setTipsNodeShow(0, false); 
        this.setTipsNodeShow(1, false); 
    }

    //输入框状态标志显示
    //inputType:  "usernameInput" :用户名输入
    //             "passwordInput" : 密码输入
    //             "passwordInput2": 密码确认输入
    //statusType:  输入状态
    //             0： 状态不显示  1 输入检测正常  2 输入检测不通过
    private setInputStatus(inputType:string, statusType:number){

        let spCheckOk:cc.Node = null;
        let spCheckWarn:cc.Node = null;
        let index = 0;
         if("passwordInput" === inputType){
            spCheckOk = this.pass_input_node1.getChildByName("spCheckOk");
            spCheckWarn = this.pass_input_node1.getChildByName("spCheckWarn");
            index = 0;
        }else if("passwordInput2" === inputType){
            spCheckOk = this.pass_input_node2.getChildByName("spCheckOk");
            spCheckWarn = this.pass_input_node2.getChildByName("spCheckWarn");
            index = 1;
        }

        if(spCheckOk == null || spCheckWarn == null){
            return;
        }

        if(statusType == 0){
            spCheckOk.active = false;
            spCheckWarn.active = false;
        }else if(statusType == 1){
            spCheckOk.active = true;
            spCheckWarn.active = false;
            this.setTipsInputBg(index, 1)
        }else if(statusType == 2){
            spCheckOk.active = false;
            spCheckWarn.active = true;
            this.setTipsInputBg(index, 2)
        }
    }
}