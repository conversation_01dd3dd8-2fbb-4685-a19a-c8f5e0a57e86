import cv from '../cv';
import { GenericWebviewScene } from './GenericWebviewScene';

const { ccclass, property } = cc._decorator;
@ccclass
export class BitMasterScene extends GenericWebviewScene {
    protected setCurrentScene() {
        cv.config.setCurrentScene(cv.Enum.SCENE.BIT_MASTER_SCENE);
    }

    protected setWebUrl() {
        this.web.url = cv.roomManager.getBitMasterUrl();
        console.log("[bitMaster] url:"+ this.web.url);
    }
}
