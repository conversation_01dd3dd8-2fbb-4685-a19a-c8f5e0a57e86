import ws_protocol = require("../../../common/pb/ws_protocol");
import world_pb = ws_protocol.pb;

import cv from "../cv";

const { ccclass, property } = cc._decorator;
@ccclass
export class BlackJackScene extends cc.Component {
    @property(cc.WebView) web: cc.WebView = null;
    private webPos: cc.Vec2 = null;
    private needRecharge : boolean = false;
    onLoad() {
        
        cv.config.setCurrentScene(cv.Enum.SCENE.BLACKJACK_SCENE);
        cv.config.adaptScreenHen(this.node);

        cv.resMgr.adaptWidget(this.node, true);

        // 这行要注释掉, ipad上会引起webview视图紊乱放大, 解决方案要么注释, 要么widget后动态添加webview
        cv.MessageCenter.register("startBlackJackScene", this.startBlackJackScene.bind(this), this.node);
        cv.MessageCenter.register("HideWebview_ShowWindows", this.HandleSwitchServer.bind(this), this.node);
        cv.MessageCenter.register("showSportsScene", this.showBlackJackScene.bind(this), this.node);
        cv.MessageCenter.register("Exit_click", this.exitGame.bind(this), this.node);
        cv.MessageCenter.register("LogoutBlackJackScene", this.onBlackJackLogout.bind(this), this.node);
    }

    start() {
        this.webPos = this.web.node.getPosition();
        this.needRecharge = false;
        cv.viewAdaptive.isselfchange = false;
        cv.viewAdaptive.blackjackGameId = 0;
        cv.viewAdaptive.blackjackRoomId = 0;
        cv.viewAdaptive.blackjackLevelId = 0;
        this.setWebUrl();
        this.web.setJavascriptInterfaceScheme("ccjs");
        this.web.setOnJSCallback((webView: cc.WebView, url: string) => {
            console.log("sports ccjs ------ " + url);
            if (url.search("ccjs://back-normal-recharge") != -1) {
                this.needRecharge = true;
                this.exitGame();
                return;
            }
            if (url.search("ccjs://back-normal") != -1) {
                this.exitGame();
                return;
            }
        });

        this.showBlackJackScene();
    }

    onDestroy() {
        cv.MessageCenter.unregister("HideWebview_ShowWindows", this.node);
        cv.MessageCenter.unregister("startBlackJackScene", this.node);
        cv.MessageCenter.unregister("showBlackJackScene", this.node);
        cv.MessageCenter.unregister("Exit_click", this.node);
        cv.MessageCenter.unregister("LogoutBlackJackScene", this.node);
    }

    HandleSwitchServer(isView?: boolean) {
        isView = (!cv.TP.getVisible()) && isView;
        this.setWebActive(isView);
    }

    
    exitGame() {
        cv.MessageCenter.unregister("HideWebview_ShowWindows", this.node);
        let gameId = cv.roomManager.getCurrentBlackjackGameID();
        let token = cv.roomManager.getCurrentToken();
        
        if(this.needRecharge)//Set properties to redirect to recharge on Hall-Scene
        {
            cv.viewAdaptive.isselfchange = true;
            cv.viewAdaptive.blackjackGameId = gameId;
            cv.viewAdaptive.blackjackRoomId = cv.roomManager.getCurrentRoomID();
            cv.viewAdaptive.blackjackLevelId = cv.roomManager.getCurrentLevelID();
            this.needRecharge = false;
        }

        cv.roomManager.reset();
        cv.worldNet.requestBlackJackLogout(gameId,token);
        cv.worldNet.requestBlackJackAuthApi();
       
    }


    onBlackJackLogout(){
        // this.setWebActive(false);
        this.web.url = "";
        this.web.node.removeFromParent(false);
        this.web.destroy();
        cv.roomManager.reset();
        setTimeout(() => {
            cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene): void => {
                    cv.MessageCenter.send("switchSceneToMiniGame");
            });
        }, 100);
    }

    startBlackJackScene(gameid: number) {
        this.setWebUrl();
        this.showBlackJackScene();
    }

    showBlackJackScene() {
        let isView = cv.TP.getVisible();
        this.setWebActive(!isView);
    }

    setWebUrl() {
        this.web.url = cv.roomManager.getBlackJackUrl();
    }

    setWebActive(isView: boolean) {
        if(cc.isValid(this.web))
            this.web.node.active = isView;
    }
}
