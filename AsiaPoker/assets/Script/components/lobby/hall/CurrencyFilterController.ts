import cv from '../cv';
import BaseFilterController from './BaseFilterController';
import CurrencyFilterButton from './CurrencyFilterButton';
import ws_protocol = require('../../../../Script/common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import { DiscoverGameType } from './FindView';

const { ccclass } = cc._decorator;

@ccclass
export default class CurrencyFilterController extends BaseFilterController {
    private _filterData: number[] = [];

    public get filterData() {
        return this._filterData;
    }

    public get SAVE_KEY(): string {
        return 'FILTER_CURRENCY_PKW_' + this.keyIndex;
    }

    public initialize(
        currencies: number[],
        index: number
    ): void {
        if (index !== -1) this.keyIndex = index;
        this.titleLabel.string =  cv.config.getStringData('Filter_Currency_Title_Text');
        for (let a = 0; a < currencies.length; a++) {
            const currencyButton = this.getNewButton();
            const buttonComponent: CurrencyFilterButton = currencyButton.getComponent(CurrencyFilterButton);
            const currencyType = currencies[a];
            const savedState = this._filterData.indexOf(currencyType) !== -1;
            buttonComponent.initialize(currencyType, savedState, this.onFilterClicked.bind(this));
        }

        this.node.active = true;

        this.layoutContainer.updateLayout();
        this._initData();
    }

    public refreshByData(): void {
        this.activeButtons.forEach((element) => {
            const btn = element.getComponent(CurrencyFilterButton);
            btn.setNewState(this._filterData.indexOf(btn.metadata) !== -1);
        });
    }

    public filter(data: world_pb.ClubGameSnapshotV3[], gameType: Number): world_pb.ClubGameSnapshotV3[] {
        let tempData = data;
        if (this._isApplicable(gameType) && this._filterData.length > 0 && this.isActive) {
            tempData = tempData.filter(
                (x) => this._filterData.includes(x.currencyType) || x.game_id === cv.Enum.GameId.StarSeat
            );
        }
        return tempData;
    }

    public isQualified(item: world_pb.ClubGameSnapshotV3, gameType: Number): boolean {
        return (
            this._isApplicable(gameType) &&
            (this._filterData.length === 0 || this._filterData.includes(item.currencyType))
        );
    }

    public override saveFilterDataToFile(): void {
        this.saveCurrencyDataToFile();
    }

    public saveCurrencyDataToFile() {
        const value = this.filterData.join(this.saveSeparatorChar);
        cv.tools.SaveStringByCCFile(this.SAVE_KEY, value);
    }
    
    public override initLanguage(): void {
        super.initLanguage();
        this.titleLabel.string =  cv.config.getStringData('Filter_Currency_Title_Text');
    }

    protected override onFilterClicked(Component: CurrencyFilterButton): void {
        const newState = Component.currentState;
        const currencyType: number = Component.metadata;
        if (newState) {
            this._filterData.push(Component.metadata);
        } else {
            const index = this._filterData.indexOf(currencyType);
            this._filterData.splice(index, 1);
        }
        this.emitClickEvent();
    }

    protected override emitClickEvent() {
        cc.game.emit('OnFilterButtonClicked', true);
    }

    private _fetchSavedCurrencyDataFromFile() {
        let numberArray: number[] = [];
        const savedData = cv.tools.GetStringByCCFile(this.SAVE_KEY);
        if (savedData) {
            const stringArray: string[] = savedData.split(this.saveSeparatorChar);
            numberArray = stringArray.map(Number);
        }
        return numberArray;
    }

    private _initData(): void {
        const data = this._fetchSavedCurrencyDataFromFile();
        if (data) {
            this._filterData = data;
            this.refreshByData();
        }
    }

    private _isApplicable(gameType: Number): boolean {
        return (
            gameType === DiscoverGameType.DZPK ||
            gameType === DiscoverGameType.DZPK_SHORT ||
            gameType === DiscoverGameType.BET
        );
    }
}
