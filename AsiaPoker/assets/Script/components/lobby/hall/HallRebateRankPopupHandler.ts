
import { MiniGamePopupId } from "../../../../../assets/minigames/Script/components/game/common/DialogManager";
import RebateRankingNoticePopup from "../../../../../assets/minigames/Script/components/game/common/rebatePromotion/RebateRankingNoticePopup";
import { pb } from '../../../common/pb/ws_protocol';
import cv from '../cv';


const { ccclass, property } = cc._decorator;

@ccclass
export default class HallRebateRankPopupHandler extends cc.Component {

    onLoad() {
        cv.MessageCenter.register('onPopupMessageNotice', this.onPopupMessageNotice.bind(this), this.node);
        cv.MessageCenter.register("ShowRebateRankingNoticePopup", this.showRebateRankingNoticePopup.bind(this), this.node);
    }

    onDestroy(): void {
        cv.MessageCenter.unregister('onPopupMessageNotice', this.node);
        cv.MessageCenter.unregister("ShowRebateRankingNoticePopup", this.node);
    }

    showRebateRankingNoticePopup(){
        if (cv.dataHandler.getUserData().rebateRankingAwards.length > 0) {
            let rebate_ranking_award = [];
            cv.StringTools.deepCopy(cv.dataHandler.getUserData().rebateRankingAwards, rebate_ranking_award);
            cv.dataHandler.getUserData().rebateRankingAwards = [];
            this.showPopup([...rebate_ranking_award]);
        }
    }

    start() {}

    // update (dt) {}

    onPopupMessageNotice(data : pb.PopupMessageNotice) {
        this.showPopup(data.minigame_rebate_ranking_awards);              
    }

    showPopup(data) {
        if (data.length === 0) {
            return;
        }
        cv.dialogMager
        .onInit((node) => {
            const controller = node.getComponent(RebateRankingNoticePopup);
            if (controller) {
                controller.init();
                controller.updateData(data[0]);
            }
        })
        .showPopup({
            popupId: MiniGamePopupId.RebateRankingNoticePopup,
            content: '',
            sureCallback: () => {
                cv.dialogMager.processClose();
                
                this.showPopup(data.slice(1));
            }
        });

    }
}
