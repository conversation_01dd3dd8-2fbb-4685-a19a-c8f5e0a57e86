import LineItem from './LineItem';
import cv from '../cv';
import { SafeAreaHelper } from '../../../../default/shared/safe_area_helper/SafeAreaHelper';

const { ccclass, property } = cc._decorator;

@ccclass
export default class LineSwitcher extends cc.Component {
    @property(cc.Node)
    lineBox: cc.Node = null;

    @property(cc.Node)
    lineBoxContent: cc.Node = null;

    @property(cc.Node)
    lineBoxView: cc.Node = null;

    @property(cc.Prefab)
    lineItemPrefab: cc.Prefab = null;

    @property(cc.Label)
    currentLineNumberLabel: cc.Label = null;

    private _allServer: any[];
    private _currentServer: any;
    private _showAllLine: boolean;
    private _checkCurrentLineIndexResult: number;
    private _hasShownAllLines: boolean = false;
    private _getTopHeight: () => number;

    private lineSwitchRootNode: cc.Node = null;     // AT-6975: Reference to root panel of lineBox.

    public init(getTopHeightFunc: () => number) {
        this.lineSwitchRootNode = this.lineBox.parent;
        this.lineSwitchRootNode.parent = null;

        // AT-6975: since earn panel is child of the scene root node but not the canvas, it causes the lineBox to be hidden behind the earn panel.
        // To fix this, we add the lineSwitchRootNode to the scene root node.
        if(cv.action.addChildToSceneOnce(this.lineSwitchRootNode))
        {
            this.lineSwitchRootNode.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TOP;
        }
        // AT-6975: Added linebox node to a parent panel and set it as a persist root node.
        // cc.game.addPersistRootNode(this.lineBox.parent);
        // this.lineSwitchrootNode.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TT;
        this._getTopHeight = getTopHeightFunc;
        this._updateServerInfo();
        this.lineSwitchRootNode.active = false;
    }

    public toggleLineBox() {
        if (this.lineSwitchRootNode.active) {
            this.lineSwitchRootNode.active = false;
        } else {
            this._prepareLineBox();
        }
    }

    private _prepareLineBox() {
        // set invisible till position and size update
        this.lineSwitchRootNode.active = true;
        this.lineBox.opacity = 0;

        // update position and lines data
        this._updateLineBoxPosition();
        this._updateServerInfo();
        this._setLineData();

        // next frame show lineBox
        cc.director.once(
            cc.Director.EVENT_AFTER_UPDATE,
            () => {
                this._adjustScrollViewHeight();
                this.lineBox.opacity = 255;
            },
            this
        );
    }

    private _updateLineBoxPosition() {
        const offsetY = SafeAreaHelper.getUpperDangerZoneYOffset();
        const widget = this.lineBox.getComponent(cc.Widget);
        widget.top = offsetY + this._getTopHeight() + 35;
        widget.updateAlignment();
    }

    private _updateServerInfo() {
        this._allServer = cv.domainMgr.getAllServerInfo();
        this._currentServer = cv.domainMgr.getServerInfo();
        cc.log(`this._allServer = `);
        cc.log(this._allServer);
        cc.log(`_currentServer = `);
        cc.log(this._currentServer);
        // if current line already to Line4, then always show L1~L4
        this._showAllLine = cv.domainMgr.isPreserveDomain();
        cc.log(`_showAllLine = ${this._showAllLine}`);
        // if _showAllLine = true
        // _hasShownAllLines will be true always, for show L1~L4
        if (this._showAllLine) {
            this._hasShownAllLines = true;
        }
    }

    private _setLineData() {
        this.lineBoxContent.removeAllChildren();
        this._initLineItems();
    }

    private _initLineItems() {
        this._checkCurrentLineIndexResult = this._checkCurrentLineIndex();
        cc.log(`_checkCurrentLineIndexResult = ${this._checkCurrentLineIndexResult}`);

        // using _hasShownAllLines to decide show 4 or 3 Line
        const showLength = this._hasShownAllLines ? 4 : 3;
        cc.log(`showLength = ${showLength}`);

        const lineNamePrefix = cv.config.getStringData('UISwitchLineNameDisplay');

        for (let i = 0; i < showLength; i++) {
            this._createLineItem(i, lineNamePrefix, showLength);
        }

        this._adjustScrollViewHeight();
    }

    private _createLineItem(index: number, namePrefix: string, itemLength: number) {
        if(this._allServer.length < index + 1){
            return;
        }
        
        const item = cc.instantiate(this.lineItemPrefab);
        const lineItem = item.getComponent(LineItem);
        // serious error
        if (!lineItem) {
            throw new Error(`LineItem component is missing on the prefab for index ${index}`);
        }

        lineItem.setLineName(`${namePrefix}${index + 1}`);
        lineItem.setLineDomainIndex(index);
        lineItem.setLineDomain(this._allServer[index].gate_server || '');
        lineItem.setLineActive('');

        if (index === this._checkCurrentLineIndexResult) {
            lineItem.setCurrentLineNameColor();
        }
        if (index === itemLength - 1) {
            lineItem.hideSeparator();
        }
        this.lineBoxContent.addChild(item);
    }

    private _adjustScrollViewHeight() {
        this.lineBox.height = this.lineBoxContent.height;
        this.lineBoxView.height = this.lineBoxContent.height;
    }

    private _checkCurrentLineIndex(): number {
        const currentServerKeys = Object.keys(this._currentServer);
        const matchIndex = this._allServer.findIndex((server) =>
            currentServerKeys.every((key) => this._currentServer[key] === server[key])
        );

        if (matchIndex !== -1) {
            this._updateCurrentLineNumber(matchIndex + 1);
        }

        return matchIndex;
    }

    private _updateCurrentLineNumber(number: number) {
        this.currentLineNumberLabel.string = number.toString();
    }
}
