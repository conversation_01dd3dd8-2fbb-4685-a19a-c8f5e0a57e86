import cv from '../cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class LineItem extends cc.Component {
    @property(cc.Label)
    lineLabel: cc.Label = null;

    @property(cc.Label)
    statusLabel: cc.Label = null;

    @property(cc.Node)
    separator: cc.Node = null;

    private _lineDomain: string = '';

    private _lineDomainIndex: number = 0;

    public itemBtn() {
        cc.log('Line, itemBtn, _lineDomain = ', this._lineDomain);
        cc.log(`Line, this._lineDomainIndex = ${this._lineDomainIndex}`);
        cv.MessageCenter.send('HallScene-ResetDomainIndex', this._lineDomainIndex);
    }

    public setLineName(linename) {
        this.lineLabel.string = linename;
    }

    public setCurrentLineNameColor() {
        this.lineLabel.node.color = new cc.Color(231, 206, 121);
    }

    // for ping value or other status
    public setLineActive(status) {
        this.statusLabel.string = status;
    }

    public setLineDomain(domain_name) {
        this._lineDomain = domain_name;
    }

    public setLineDomainIndex(domain_index) {
        cc.log(`setLineDomainIndex, domain_index = ${domain_index}`);
        this._lineDomainIndex = domain_index;
    }

    public hideSeparator() {
        cc.log(`hideSeparator`);
        this.separator.active = false;
    }
}
