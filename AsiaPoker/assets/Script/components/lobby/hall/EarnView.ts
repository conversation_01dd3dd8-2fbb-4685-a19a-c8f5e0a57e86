import { pb } from "../../../common/pb/ws_protocol";
import cv from "../cv";
import ExplainView from "./ExplainView";
import HallScene from "./HallScene";
import InquireView from "./InquireView";
import { RewardsView } from "./RewardsView";
import Safe from "./Safe";
import USDTView from "./USDTView";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EarnView extends cc.Component {

    @property(cc.Prefab) SecondaryPassword: cc.Prefab = null;

    secondPsdView: cc.Node = null;

    @property(cc.Prefab) safePref: cc.Prefab = null;

    @property(cc.Node) RechargeBtn: cc.Node = null;

    @property(cc.Node) EarnBtn: cc.Node = null;

    @property(cc.Node) ExchangeBtn: cc.Node = null;

    @property(cc.Node) QueryBtn: cc.Node = null;

    @property(cc.Button) explain_btn_mini: cc.Button = null;

    @property(cc.Button) explain_btn_jifeng: cc.Button = null;

    @property(cc.Label) labArr: cc.Label[] = [];

    @property(cc.Layout) totalNumNode: cc.Layout = null;

    @property(cc.Label) coinNum: cc.Label = null;

    @property(cc.Label) usdtNum: cc.Label = null;

    @property(cc.Label) lab_gold: cc.Label = null;

    @property(cc.Label) lab_minigold: cc.Label = null;

    @property(cc.Label) lab_usdt: cc.Label = null;

    @property(cc.Label) lab_jifeng: cc.Label = null;

    @property(cc.Prefab) explainView_prefab: cc.Prefab = null;

    @property(cc.Prefab) InquireView_prefab: cc.Prefab = null;

    @property(cc.Prefab) UsdtView_prefab: cc.Prefab = null;

    @property(cc.Prefab) RewardsView_prefab: cc.Prefab = null;

    @property(cc.Prefab) securityBoxPrefab: cc.Prefab = null;

    @property(cc.Node) root_scroll_content: cc.Node = null;

    // 积分信息区域
    @property(cc.Node) poinNode: cc.Node = null;

    // 电子福利标题
    @property(cc.Label) welfareTitle: cc.Label = null;

    // 电子福利显示收缩按钮
    @property(cc.Button) btn_welfareCtrlView: cc.Button = null;

    // 电子福利信息区域
    @property(cc.Node) welfareInfoNode: cc.Node = null;

    // 免费福利金额
    @property(cc.Label) freeBonusTitle: cc.Label = null;

    // 免费福利金额提示按钮
    @property(cc.Button) btn_BonusQues: cc.Button = null;

    // 免费游戏次数
    @property(cc.Label) freeCountTitle: cc.Label = null;

    // 免费游戏次数按钮
    @property(cc.Button) btn_FreeQues: cc.Button = null;

    // 免费红利描述
    @property(cc.RichText) bonusDesc: cc.RichText = null;

    // 免费红利描述icon
    @property(cc.Node) freeBonusIcon: cc.Node = null;

    // 免费次数描述
    @property(cc.RichText) FreeCountDesc: cc.RichText = null;

    // 免费次数描述icon
    @property(cc.Node) freeGamesIcon: cc.Node = null;

    // 免费红利金额
    @property(cc.Label) txtWelfareAmount: cc.Label = null;

    // 免费次数
    @property(cc.Label) txtFreeCount: cc.Label = null;

    // 免费红利金额
    @property(cc.Label) txtWelfareAmount2: cc.Label = null;

    // 免费次数
    @property(cc.Label) txtFreeCount2: cc.Label = null;

    // 红利红点
    @property(cc.Node) bonusRedDot: cc.Node = null;

    // 免费次数红点
    @property(cc.Node) freeRedDot: cc.Node = null;

    @property(cc.Node) totalCoinExplain: cc.Node = null;

    @property(cc.Node) usdtExplain: cc.Node = null;

    @property(cc.Node) eGameExplain: cc.Node = null;

    @property(cc.Node) rewardExplain: cc.Node = null;

    @property(cc.Node) securityBoxBtn: cc.Node = null;

    @property(cc.Node) trailCoinExplain: cc.Node = null;

    @property(cc.Label) freeBonusDesc: cc.Label = null;

    @property(cc.Label) freeGamesDesc: cc.Label = null;

    @property(cc.Label) usdLabel: cc.Label = null;

    @property(cc.Label) trailCointitle: cc.Label = null;

    @property(cc.Label) trailCoinBalance: cc.Label = null;

    @property(cc.Node) trailCoinExpiry: cc.Node = null;

    @property(cc.Node) earnView: cc.Node = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) top: cc.Node = null;

    @property(cc.Node) topPart: cc.Node = null;

    @property(cc.Node) rootScrollView: cc.Node = null;

    @property(cc.Widget) viewWidget: cc.Widget = null;

    @property(cc.Node) bottom: cc.Node = null;

    @property(cc.Widget) backgroundBg: cc.Widget = null;

    @property(cc.Layout) contentLayout: cc.Layout = null;

    @property(cc.Layout) welfareNodeLayout: cc.Layout = null;

    @property(cc.Node) rewardNodeParent: cc.Node = null;

    refHallSceneNode: cc.Node = null;

    inquire_node: cc.Node = null;

    usdtView_node: cc.Node = null;

    rewardsView_node: cc.Node = null;

    explainIndex: number = -1;

    securityBoxNode: cc.Node = null;

    // 是否显示福利区域
    private isShowWelfare: boolean = false;

    // 存在红利奖励的游戏
    private bonusGameIDs: number[] = [];

    // 存在免费次数的游戏
    private freeGameIDs: number[] = [];

    private oriPoinNodeY: number;

    private initExplainViews() {
        this.totalCoinExplain.parent = cc.director.getScene();

        this.usdtExplain.parent = cc.director.getScene();

        this.eGameExplain.parent = cc.director.getScene();

        this.rewardExplain.parent = cc.director.getScene();

        this.trailCoinExplain.parent = cc.director.getScene();

        this.totalCoinExplain.zIndex = this.trailCoinExplain.zIndex = this.usdtExplain.zIndex = this.eGameExplain.zIndex = this.rewardExplain.zIndex = 1;
    }

    onLoad() {
        this.initExplainViews();

        this.oriPoinNodeY = this.poinNode.y;

        this.registerMsg();

        this.secondPsdView = cv.action.addChildToScene(this, this.SecondaryPassword, []);

        // 福利收缩按钮
        this.btn_welfareCtrlView.node.on("click", () => {
            if (cv.dataHandler.getUserData().checkHaveWelfare()) {
                cv.AudioMgr.playButtonSound('button_click');

                this.showWelfareSwitchView(!this.isShowWelfare);
            }

        }, this);

        // 小游戏福利信息
        if (!cv.dataHandler.getUserData().checkHaveWelfare()) {
            // 没有下游戏福利
            this.showWelfareSwitchView(false);

            this.txtWelfareAmount2.string = "0";

            this.txtFreeCount2.string = "0";
        }
        else {
            this.onBonusAndFreeResponse(null);
        }
    };

    // AT-3589: Potential fix for the auto-rotate issue. This combined with changes in earnView prefab root object's widget component
    // resize mode is now set to 'ALWAYS' instead of 'WINDOW_RESIZE' because the instance of this panel gets instantiated and 
    // disabled instantly in HallScene.ts->onLoad() function. Working hypothesis is that the widget of earnView instance 
    // might not be able to account for the canvas resizing when switching from landscape to portrait in H5.
    protected onEnable(): void {
        this.setSafeAreaAndRootScrollView();
    }

    start() {
        this.initLanguage();

        this.setSafeAreaAndRootScrollView();
    }

    onDestroy() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);

        cv.MessageCenter.unregister("earn_open", this.node);

        cv.MessageCenter.unregister("update_info", this.node);

        cv.MessageCenter.unregister("update_gold", this.node);

        cv.MessageCenter.unregister("sendBonusAndFreeGamesMsg", this.node);
        cv.MessageCenter.unregister("jump_to_conversation_page", this.node);
    }

    onDisable() {
        const mini_explainView = this.node.getChildByName("mini_explainView");

        if (mini_explainView) {
            if (mini_explainView.getComponent(ExplainView).isShow) {
                mini_explainView.getComponent(ExplainView).hide();
            }
        }
    }

    registerMsg() {
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);

        cv.MessageCenter.register("earn_open", this.onSafeTakeOutClick.bind(this), this.node);

        cv.MessageCenter.register("update_info", this.noticeUpdateData.bind(this), this.node);

        cv.MessageCenter.register("update_gold", this.noticeUpdateData.bind(this), this.node);

        cv.MessageCenter.register("sendBonusAndFreeGamesMsg", this.onBonusAndFreeResponse.bind(this), this.node);
        cv.MessageCenter.register("jump_to_conversation_page", this.onClickUSDT.bind(this), this.node);
    }

    onRechargeHandDown(evt) {
        console.log("onRechargeHandDown", evt);
    }

    onRechargeHandUp(evt) {
        cc.find("Label", evt.currentTarget).getComponent(cc.Label).node.color = new cc.Color(0, 143, 240);
    }

    onRechargeHandCancel(evt) {
        cc.find("Label", evt.currentTarget).getComponent(cc.Label).node.color = new cc.Color(0, 143, 240);
    }

    initLanguage() {
        cc.find("Label", this.RechargeBtn).getComponent(cc.Label).string = cv.config.getStringData("Earnings_recharge_button_label");

        cc.find("Label", this.EarnBtn).getComponent(cc.Label).string = cv.config.getStringData("Earnings_earnings_button_label");

        cc.find("Label", this.ExchangeBtn).getComponent(cc.Label).string = cv.config.getStringData("Earnings_exchange_button_label");

        cc.find("Label", this.QueryBtn).getComponent(cc.Label).string = cv.config.getStringData("Safe_detail");

        this.usdLabel.string = cv.config.getStringData("USD_Cash_Game_Card");

        const len = this.labArr.length;

        for (let i = 0; i < len; i++) {
            const tempLab = this.labArr[i];

            tempLab.string = cv.config.getStringData("Earnings_lab_" + i);
        }

        // 免费红利金额
        this.freeBonusTitle.string = cv.config.getStringData("Earnings_bonus");

        // 免费次数
        this.freeCountTitle.string = cv.config.getStringData("Earnings_freeCount");

        this.trailCointitle.string = cv.config.getStringData("TRAIL_COIN_TITLE");

        cc.find("expire_label", this.trailCoinExpiry).getComponent(cc.Label).string = cv.config.getStringData("TRAIL_COIN_EXPIRY");

        const jSize = cv.resMgr.getLabelStringSize(this.explain_btn_jifeng.node.parent.getComponent(cc.Label));

        const mSize = cv.resMgr.getLabelStringSize(this.explain_btn_mini.node.parent.getComponent(cc.Label));

        this.explain_btn_jifeng.node.setPosition(jSize.width, this.explain_btn_jifeng.node.y);

        this.explain_btn_mini.node.setPosition(mSize.width, this.explain_btn_mini.node.y);

        const isthai: boolean = cv.config.isThai();

        this.RechargeBtn.active = !isthai;

        this.EarnBtn.active = !isthai;

        this.ExchangeBtn.active = !isthai;

        this.QueryBtn.active = !isthai;

        const mini_explainView = this.node.getChildByName("mini_explainView");

        if (mini_explainView) {
            if (mini_explainView.getComponent(ExplainView).isShow) {
                if (this.explainIndex == 0) {
                    this.showExplainView(this.explain_btn_jifeng.node, "Earnings_point_tips_", 3);
                }
                else if (this.explainIndex == 1) {
                    this.showExplainView(this.explain_btn_mini.node, "Earnings_minigold_tips_", 3);
                } else if (this.explainIndex == 2 || this.explainIndex == 3) {
                    mini_explainView.getComponent(ExplainView).hide();
                }
            }
        }
    }

    onBtnRechargeClick() {
        cv.AudioMgr.playButtonSound('tab');
        const properties = { item: "headerDepositButton" };
        cv.segmentTool.track(cv.Enum.CurrentScreen.store, cv.Enum.segmentEvent.DepositInitiated, cv.Enum.Functionality.payments, properties);
        cv.SHOP.RechargeClick();
    };

    onBtnEarnClick() {
        cv.AudioMgr.playButtonSound('tab');

        if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(cv.config.getStringData("roleInfoSet_tips_updateGrade_earntips_text"), cv.Enum.ButtonStyle.TWO_BUTTON, cv.dataHandler.upgradeAccount.bind(cv.dataHandler), cv.dataHandler.cancleUpgradeAccount.bind(cv.dataHandler));

            return;
        }

        if (cv.config.isOverSeas()) {
            cv.StatusView.show(false);

            cv.action.moveToAction(this.secondPsdView, cv.Enum.action_FuncType.to_left, cv.Enum.action_FuncType.enter,
                cv.Enum.action_FuncType.dt_NORMAL,
                cc.v2(cv.config.WIDTH * 1.5, 0), cc.v2(cv.config.WIDTH * 0.5, 0));

            return;
        }

        cv.TP.showMsg(cv.config.getStringData("Safe_tips_content"), cv.Enum.ButtonStyle.TWO_BUTTON, this.safeSureBtn.bind(this), this.safeCancelBtn.bind(this), false, cv.config.getStringData("Safe_tips"));

        cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_OPEN_Security_Box);

        if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.zh_CN) {
            cv.TP.getMessageText().fontSize = 47;
        }
    };

    onSafeTakeOutClick() {
        cv.StatusView.show(false);

        cv.action.moveToAction(this.secondPsdView, cv.Enum.action_FuncType.to_left, cv.Enum.action_FuncType.enter,
            cv.Enum.action_FuncType.dt_NORMAL,
            cc.v2(cv.config.WIDTH * 1.5, 0), cc.v2(cv.config.WIDTH * 0.5, 0));
    }

    onBtnQueryClick() {
        cv.AudioMgr.playButtonSound('tab');

        if (!this.inquire_node) {
            this.inquire_node = cc.instantiate(this.InquireView_prefab);

            cv.action.addChildToSceneOnce(this.inquire_node);
        }
        cv.action.showAction(this.inquire_node, cv.action.eMoveActionDir.EMAD_TO_LEFT, cv.action.eMoveActionType.EMAT_FADE_IN,
            cv.action.delay_type.NORMAL, null, () => this.inquire_node.getComponent(InquireView).showBackBtn());
    };

    noticeUpdateData() {
        this.coinNum.string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().total_amount);

        const usdtStr: string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().usdt);

        this.usdtNum.string = usdtStr;

        this.lab_usdt.string = usdtStr;

        this.lab_gold.string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().u32Chips);

        this.lab_minigold.string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().game_coin);

        this.lab_jifeng.string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().user_points);

        this.trailCoinBalance.string = cv.StringTools.serverGoldToShowString(cv.dataHandler.getUserData().trail_coin);

        const date = new Date(cv.dataHandler.getUserData().trail_coin_expiry * 1000);

        const year = date.getFullYear();

        const month = String(date.getMonth() + 1).padStart(2, '0');

        const day = String(date.getDate()).padStart(2, '0');

        const hour = String(date.getHours()).padStart(2, '0');

        const minute = String(date.getMinutes()).padStart(2, '0');
        const dateString = `${year}/${month}/${day} ${hour}:${minute}`;

        cc.find("expire_date", this.trailCoinExpiry).getComponent(cc.Label).string = dateString;

        this.trailCoinExpiry.active = cv.dataHandler.getUserData().trail_coin_expiry != 0;
    }

    /**
     * 
     * @param type 1: free credits, 0: free games
     * @returns Welfare desc string
     */
    private getWelfareTips(type: number): string {
        let str: string = ""
        // 如果不支持的游戏数量小于5个（列表中显示的所有游戏中）
        // ——则后半部分提示：适用除xxx、xxx、xxx之外的所有游戏。
        // 如果不支持的游戏数量大于5个
        // ——则后半部分提示：“适用游戏：xxx、xxx、xxx.....。”
        // 如果列表中的游戏都支持
        // ——则后半部分提示：适用所有游戏。

        const totalShowMinis: pb.PgGameData[] = cv.dataHandler.getUserData().welfarePgGames;

        const totalGameLen: number = totalShowMinis.length;

        if (totalGameLen <= 0) {
            return "";
        }

        let _curSurporGameIDs: number[] = [];

        let GameNames: string = "";

        if (type == 1) {
            // 红利处理
            _curSurporGameIDs = this.bonusGameIDs;
        }
        else {
            _curSurporGameIDs = this.freeGameIDs;
        }

        const GameNameArr: string[] = [];

        const suportLen = _curSurporGameIDs.length;

        const lineCount = cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? 4 : 2;

        if (totalGameLen == suportLen) {
            // 所有的游戏都支持
            str += (cv.config.getStringData("Earnings_welfare_tips6"));

        }
        else if ((totalGameLen - _curSurporGameIDs.length) <= 5) {
            // 如果不支持的游戏小于5个, 找出不支持的游戏来支持
            let _countLine = 0;

            let _curTotal = lineCount - 1;

            for (let i = 0; i < totalShowMinis.length; i++) {
                let bSuport = false;

                for (let j = 0; j < _curSurporGameIDs.length; j++) {
                    if (totalShowMinis[i].gameId === _curSurporGameIDs[j]) {
                        bSuport = true;

                        break;
                    }
                }

                if (!bSuport) {
                    const name: string = totalShowMinis[i].gameName;

                    if (name.length > 0) {
                        _countLine++;

                        if (_countLine == _curTotal && i != totalShowMinis.length - 1) {
                            GameNameArr.push(name + "\n");

                            _curTotal = lineCount;

                            _countLine = 0;
                        }
                        else {
                            GameNameArr.push(name);
                        }
                    }
                }
            }

            GameNames = GameNameArr.join("、");

            if (GameNameArr.length > 0) {
                str += (cv.StringTools.formatC(cv.config.getStringData("Earnings_welfare_tips3"), GameNames))
            }

        }
        else {
            // 如果不支持的游戏大于5个，显示支持的游戏
            let _countLine = 0;

            for (let i = 0; i < _curSurporGameIDs.length; i++) {
                for (let j = 0; j < totalShowMinis.length; j++) {
                    if (_curSurporGameIDs[i] === totalShowMinis[j].gameId) {
                        const name: string = totalShowMinis[j].gameName;

                        if (name.length > 0) {
                            _countLine++;

                            if (_countLine == lineCount && i != _curSurporGameIDs.length - 1) {
                                // GameNameArr.push(name + "\n");
                                _countLine = 0;
                            }
                            else {
                                GameNameArr.push(name);
                            }
                        }

                        break;
                    }
                }

                if (i == 19) {
                    // 最多显示20个
                    GameNameArr.push("...");

                    break;
                }
            }

            GameNames = GameNameArr.join("、");

            let tips = cv.StringTools.formatC(cv.config.getStringData("Earnings_welfare_tips5"), GameNames);

            if (GameNameArr.length <= 0) {
                // 没有游戏 后面句号去掉
                tips = tips.substring(0, tips.lastIndexOf('。'));
            }
            else {
                str += (tips);
            }
        }

        return str;
    }


    onExplainViewCloseBtnClicked() {
        cv.AudioMgr.playButtonSound('close');

        this.totalCoinExplain.active = false;

        this.usdtExplain.active = false;

        this.eGameExplain.active = false;

        this.rewardExplain.active = false;

        this.trailCoinExplain.active = false;
    }

    onTotalCoinExplainBtnClicked() {
        cv.AudioMgr.playButtonSound('button_click');

        this.totalCoinExplain.active = true;
    }

    onUsdtExplainBtnClicked() {
        cv.AudioMgr.playButtonSound('button_click');

        this.usdtExplain.active = true;
    }

    onEGameExplainBtnClicked() {
        this.freeBonusDesc.string = this.getWelfareTips(1);

        this.freeGamesDesc.string = this.getWelfareTips(0);

        cv.AudioMgr.playButtonSound('button_click');

        this.eGameExplain.active = true;
    }

    onRewardExplainBtnClicked() {
        cv.AudioMgr.playButtonSound('button_click');

        this.rewardExplain.active = true;
    }


    onTrailCoinExplainBtnClicked() {
        cv.AudioMgr.playButtonSound('button_click');

        this.trailCoinExplain.active = true;
    }

    onSecurityBoxBtnClicked() {
    }

    createSecurityBox(isNoticeView) {
        cv.AudioMgr.playButtonSound('button_click');

        if (!this.securityBoxNode) {
            this.securityBoxNode = cc.instantiate(this.securityBoxPrefab);

            if (isNoticeView) {
                isNoticeView = false;

                this.securityBoxNode.getComponent(Safe).setCloseCallBack(() => {
                    isNoticeView = true;
                });
            }
            else {
                this.securityBoxNode.getComponent(Safe).setCloseCallBack(null);
            }
            cv.action.addChildToSceneOnce(this.securityBoxNode);
        }

        cv.action.showAction(
            this.securityBoxNode,
            cv.action.eMoveActionDir.EMAD_TO_LEFT,
            cv.action.eMoveActionType.EMAT_FADE_IN,
            cv.action.delay_type.NORMAL,
            () => {
            },
            () => {
                this.securityBoxNode.getComponent(Safe).showingView();
            }
        );
    }

    // 存储存在红利的游戏gameID
    private addToBounsArray(gameID: number) {
        const curShowPgGames = cv.dataHandler.getUserData().welfarePgGames;

        let bShowNow = false;

        for (let j = 0; j < curShowPgGames.length; j++) {
            if (gameID === curShowPgGames[j].gameId) {
                bShowNow = true;

                break;
            }
        }

        if (!bShowNow) {
            // 不在当前显示中
            return;
        }

        for (let i = 0; i < this.bonusGameIDs.length; i++) {
            if (this.bonusGameIDs[i] == gameID) {
                // 已经在存储中了
                return;
            }
        }

        this.bonusGameIDs.push(gameID);
    }

    private addToFreeArray(gameID: number) {

        const curShowPgGames = cv.dataHandler.getUserData().welfarePgGames;

        let bShowNow = false;

        for (let j = 0; j < curShowPgGames.length; j++) {
            if (gameID === curShowPgGames[j].gameId) {
                bShowNow = true;

                break;
            }
        }

        if (!bShowNow) {
            // 不在当前显示中
            return;
        }

        for (let i = 0; i < this.freeGameIDs.length; i++) {
            if (this.freeGameIDs[i] == gameID) {
                // 已经在存储中了
                return;
            }
        }

        this.freeGameIDs.push(gameID);
    }


    // 红点显示处理
    private showWelfareRedDot() {
        const _showFreeDot = cv.tools.GetStringByCCFile("welfareFreeNew");

        const _showBounDot = cv.tools.GetStringByCCFile("welfareBounsNew");
    }

    // 红利隐藏处理
    private hideWelfareRedDot(type: number = 0) {

    }

    // 电子游戏福利返回
    onBonusAndFreeResponse(data: any) {
        this.FreeCountDesc.node.active = false;

        this.bonusDesc.node.active = false;

        this.freeGamesIcon.active = false;

        this.freeBonusIcon.active = false;

        this.showWelfareRedDot();

        if (cv.dataHandler.getUserData().checkHaveWelfare()) {
            this.showWelfareSwitchView(true);
        }
        else {
            // 没有数据
            this.showWelfareSwitchView(false);

            return;
        }

        const bonus = cv.dataHandler.getUserData().getWelfareBouns();

        const freeGames = cv.dataHandler.getUserData().getWelfareFrees();

        this.bonusGameIDs = [];

        // 红利总额度
        let balanceAmount = 0;

        // 最近过期的红利金额
        let balanceAmount_exp = 0;

        // 记录最近的过期时间
        let bonusExpTimes = 0;

        // 所有过期时间都一样
        let bSameExpTimes = true;

        for (let i = 0; i < bonus.length; i++) {
            
            if(bonus[i].expiredDate < Date.now()){//AT-2413 前端过滤掉已经过期的时间
                continue;
            }

            const gameIds = bonus[i].gameIds;

            if (bonusExpTimes == 0) {
                bonusExpTimes = bonus[i].expiredDate;

                balanceAmount_exp = bonus[i].balanceAmount;
            }

            balanceAmount += bonus[i].balanceAmount;

            if (bonus[i].expiredDate < bonusExpTimes) {
                // 记录最近的过期时间
                bonusExpTimes = bonus[i].expiredDate;

                balanceAmount_exp = bonus[i].balanceAmount;
            }

            // 判断是否所有过期时间都一样
            if (i != 0 && bonus[i].expiredDate != bonus[i - 1].expiredDate) {
                bSameExpTimes = false;
            }

            for (let j = 0; j < gameIds.length; j++) {
                this.addToBounsArray(gameIds[j]);
            }
        }

        // 免费红利总金额
        this.txtWelfareAmount.string = balanceAmount.toFixed(2).toString();

        // 免费红利总金额
        this.txtWelfareAmount2.string = balanceAmount.toFixed(0).toString();

        let timeStr = cv.StringTools.formatTime(bonusExpTimes / 1000, cv.Enum.eTimeType.Year_Month_Day_Hour_Min);

        if (balanceAmount_exp > 0) {
            this.bonusDesc.node.active = true;

            this.freeBonusIcon.active = true;

            if (bSameExpTimes) {
                this.bonusDesc.string = cv.StringTools.formatC(cv.config.getStringData("Earnings_expirationDesc2"), timeStr);
            }
            else {
                this.bonusDesc.string = cv.StringTools.formatC(cv.config.getStringData("Earnings_expirationDesc1"), balanceAmount_exp.toFixed(2), timeStr);
            }
        }

        // 免费游戏次数
        let freeGameAmount = 0;

        // 最近过期的免费次数
        let freeGameAmount_exp = 0;

        // 记录最近的过期时间
        let FreeExpTimes = 0;

        bSameExpTimes = true;

        this.freeGameIDs = [];

        for (let i = 0; i < freeGames.length; i++) {

            if(freeGames[i].expiredDate < Date.now()){//AT-2413 前端过滤掉已经过期的时间
                continue;
            }

            const gameIds = freeGames[i].gameIds;

            freeGameAmount += freeGames[i].gameCount;

            if (FreeExpTimes == 0) {
                FreeExpTimes = freeGames[i].expiredDate;

                freeGameAmount_exp = freeGames[i].gameCount;
            }

            if (freeGames[i].expiredDate < FreeExpTimes) {
                FreeExpTimes = freeGames[i].expiredDate;

                freeGameAmount_exp = freeGames[i].gameCount;
            }

            // 判断是否所有过期时间都一样
            if (i != 0 && freeGames[i].expiredDate != freeGames[i - 1].expiredDate) {
                bSameExpTimes = false;
            }

            for (let j = 0; j < gameIds.length; j++) {
                this.addToFreeArray(gameIds[j]);
            }

        }

        this.txtFreeCount.string = cv.StringTools.formatC(cv.config.getStringData("Earnings_freeNum"), freeGameAmount);   // 免费次数

        this.txtFreeCount2.string = freeGameAmount.toString();

        timeStr = cv.StringTools.formatTime(FreeExpTimes / 1000, cv.Enum.eTimeType.Year_Month_Day_Hour_Min);

        if (freeGameAmount_exp > 0) {
            this.FreeCountDesc.node.active = true;

            this.freeGamesIcon.active = true;

            if (bSameExpTimes) {
                this.FreeCountDesc.string = cv.StringTools.formatC(cv.config.getStringData("Earnings_expirationDesc2"), timeStr);
            }
            else {
                const _tempstr = cv.StringTools.formatC(cv.config.getStringData("Earnings_freeNum"), freeGameAmount_exp);   // 过期免费次数

                this.FreeCountDesc.string = cv.StringTools.formatC(cv.config.getStringData("Earnings_expirationDesc1"), _tempstr, timeStr);
            }
        }
    }

    showExplainView(node: cc.Node, str: string, len: number) {
        const bg = node.getChildByName("Background");

        const worldPos = node.convertToWorldSpaceAR(cc.v2(bg.x + bg.width * 0.5, bg.y - bg.height));

        let mini_explainView = this.node.getChildByName("mini_explainView");

        if (!mini_explainView) {
            mini_explainView = cc.instantiate(this.explainView_prefab);

            this.node.addChild(mini_explainView);

            mini_explainView.name = "mini_explainView";
        }
    }


    showExplainCommonView(node: cc.Node, str: string[], len: number) {
        const bg = node.getChildByName("Background");

        const worldPos = node.convertToWorldSpaceAR(cc.v2(bg.x + bg.width * 0.5, bg.y - bg.height));

        let mini_explainView = this.node.getChildByName("mini_explainView");

        if (!mini_explainView) {
            mini_explainView = cc.instantiate(this.explainView_prefab);

            this.node.addChild(mini_explainView);

            mini_explainView.name = "mini_explainView";
        }
    }

    showWelfareSwitchView(isShowWelfare: boolean = false) {

        this.isShowWelfare = isShowWelfare;

        this.welfareInfoNode.active = this.isShowWelfare

        cc.find("checkIcon", this.btn_welfareCtrlView.node).active = this.isShowWelfare;

        cc.find("uncheckIcon", this.btn_welfareCtrlView.node).active = !this.isShowWelfare;

        /* if (this.isShowWelfare) {
            //福利显示
            //this.poinNode.y = this.oriPoinNodeY;
            this.rewardNodeParent.active = true;
        }
        else {
            //福利隐藏
            //this.poinNode.y = this.oriPoinNodeY + (this.welfareInfoNode.getContentSize().height - 100);
            this.rewardNodeParent.active = false;
        } */

        this.setSafeAreaAndRootScrollView();
    }

    // show usdt
    onClickUSDT() {
        // 0-获取人民币到Usdt汇率 1-获取usdt到人民币的汇率
        // cv.worldNet.GetScalerQuoteRequest(1);
        // 兑换操作类型, 0-人民币到Usdt的兑换 1-usdt到人民币的兑换
        // 要兑换货币的金额  以分为单位
        // cv.worldNet.ExchangeCurrencyRequest(1, 680);

        cv.AudioMgr.playButtonSound('button_click');

        if (!this.usdtView_node) {
            this.usdtView_node = cc.instantiate(this.UsdtView_prefab);

            cv.action.addChildToSceneOnce(this.usdtView_node);
        }
        cv.action.showAction(this.usdtView_node,
            cv.action.eMoveActionDir.EMAD_TO_LEFT,
            cv.action.eMoveActionType.EMAT_FADE_IN,
            cv.action.delay_type.NORMAL,
            null,
            () => this.usdtView_node.getComponent(USDTView).showBackBtn());
    }

    safeSureBtn(): void {
        if (this.refHallSceneNode) {
            const hallSceneComp = this.refHallSceneNode.getComponent(HallScene);

            if (hallSceneComp) {
                hallSceneComp.showingSafe();
            }
        }
    }

    safeCancelBtn(): void {
        // 跳转到取款页面
        cv.MessageCenter.send("earn_open");
    }

    onClickRewards() {
        cv.AudioMgr.playButtonSound('button_click');

        if (!this.rewardsView_node) {
            this.rewardsView_node = cc.instantiate(this.RewardsView_prefab);

            cv.action.addChildToSceneOnce(this.rewardsView_node);

            this.rewardsView_node.getComponent(RewardsView).earnView = this;
        }

        cv.action.showAction(this.rewardsView_node,
            cv.action.eMoveActionDir.EMAD_TO_LEFT,
            cv.action.eMoveActionType.EMAT_FADE_IN,
            cv.action.delay_type.NORMAL,
            null,
            () => this.rewardsView_node.getComponent(RewardsView).showBackBtn());
    }

    setSafeAreaAndRootScrollView() {
        if(this.refHallSceneNode) {
            const hallScene = this.refHallSceneNode.getComponent(HallScene);

            this.top.height = hallScene.getTopHeight();
    
            this.bottom.height = hallScene.getBottomHeight();
        }

        // SafeArea
        const offsetY = cv.SafeAreaWithDifferentDevices.getSafeAreaHall();

        this.safearea.height = offsetY;

        const rootScrollViewHeight = this.earnView.height - offsetY - this.top.height - this.topPart.height -
            this.bottom.height;
            
        this.rootScrollView.height = rootScrollViewHeight;

        this.welfareNodeLayout.updateLayout();

        this.contentLayout.updateLayout();

        this.layout.updateLayout();

        this.backgroundBg.top = offsetY + this.top.height;

        this.backgroundBg.bottom = this.bottom.height;

        cv.resMgr.adaptWidget(this.node, true);
    }
}
