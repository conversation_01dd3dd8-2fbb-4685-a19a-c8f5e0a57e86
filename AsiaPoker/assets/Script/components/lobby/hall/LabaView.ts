// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import cv from '../cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class LabaView extends cc.Component {
    @property(cc.Animation) laba_animation: cc.Animation = null;
    @property(cc.Animation) button_hand_animation: cc.Animation = null;
    @property(cc.Label) cells: cc.Label[] = [];
    @property(cc.Label) winning_cell_effect: cc.Label = null;
    @property(cc.Label) result: cc.Label = null;
    @property(cc.RichText) des_num_text: cc.RichText = null;
    @property(cc.Label) des_text: cc.Label = null;
    @property(cc.Node) sprite_title: cc.Node = null;
    @property(cc.Node) result_title: cc.Node = null;
    @property(cc.Node) laba_gold: cc.Node = null;
    @property(cc.Node) gold_target_position: cc.Node = null;

    private readonly _winning_index: number = 7;
    private readonly _goldImgYOffest = 70;
    private _action_running: boolean = false;
    private _rewardList: number[] = [];
    private _luckindex: number = 0;

    //#region For calculating neighboring winning numbers
    private readonly _upperBonds = [1, 10, 100, 1000, 10000];
    private readonly _minAdjNum = 0.01;
    private readonly _maxAdjNum = 9999.99;
    private readonly _diffs = [
        {
            // 0.xx
            pBias: 200,
            nBias: 0,
            maxDigit: 4,
            minDigit: 2,
        },
        {
            // x.xx
            pBias: 200,
            nBias: 0,
            maxDigit: 4,
            minDigit: 2,
        },
        {
            //  xx.xx
            pBias: 400,
            nBias: 0,
            maxDigit: 4,
            minDigit: 3,
        },
        {
            // xxx.xx
            pBias: 800,
            nBias: 80,
            maxDigit: 4,
            minDigit: 0,
        },
        {
            // xxxx.xx
            pBias: 0,
            nBias: 800,
            maxDigit: 3,
            minDigit: 0,
        },
    ];
    //#endregion For calculating neighboring winning numbers

    public init(isNeedResetRewards: Boolean = true) {
        const userData = cv.dataHandler.getUserData(); 
        if (!this._action_running && userData.luckindex >= userData.lucks.length) {
            this.closeView();
            return;
        } 

        this._action_running = false;
        this._rewardList = userData.lucks[userData.luckindex].amount_ranges;
        this._luckindex = userData.luckindex;

        this.laba_gold.setPosition(cc.v2(0, 0)); // fix gold position flickering issue due to not resetting to center

        if (isNeedResetRewards) {
            this._setRewards();
        }

        this._setRemainNumber();
        this._playDefaultAnimation();
    }

    public closeView(isShowSortATLView: Boolean = true) {
        cv.AudioMgr.stopAll();
        this.node.active = false;
        if (isShowSortATLView) {
            cv.MessageCenter.send('sortATLView');
        }
    }

    // Button Event, for "LabaView.prefab -> Button_Hand (Anim)"
    public onTouchLabaPanel() {
        if (this._action_running) {
            return;
        }

        this._action_running = true;
        this.laba_animation.play('SpinWheel');
        this.laba_animation.once('finished', this._onAnimationFinished, this);
        cv.AudioMgr.playEffect('zh_CN/hall/laba/audio/LabaSlot');
        this._luckindex++;
        this._setRemainNumber();
    }

    // Animation Event, for "LabaView.prefab -> Node_LabaSlot -> Animation (SpinWheel) at 3:00"
    public changeRewardEvent() {
        const userData = cv.dataHandler.getUserData(); 
        const index = userData.lucks[userData.luckindex].index;
        const winningNum = cv.StringTools.clientGoldByServer(this._rewardList[index]);
        const winningNumberStr = cv.StringTools.numberToString(winningNum);
        
        this._setRewards();

        const winning_index2 = this._winning_index - this.cells.length / 2 ;
        this.cells[this._winning_index].string = winningNumberStr;
        this.cells[winning_index2].string = winningNumberStr;

        const adjentNums: number[] = this._genAdjentNum(winningNum);
        this.cells[this._winning_index + 1].string =  this.cells[winning_index2 + 1].string = cv.StringTools.numberToString(adjentNums[0]);
        this.cells[this._winning_index - 1].string =  this.cells[winning_index2 - 1].string = cv.StringTools.numberToString(adjentNums[1]);

        this.winning_cell_effect.string = winningNumberStr;
        this.result.string = winningNumberStr;
        const id = userData.lucks[userData.luckindex].id;
        cv.worldNet.RequestLuckDrawDone(id);
    }

    // Animation Event, for "LabaView.prefab -> Node_LabaSlot -> Animation (SpinWheel) at 11:50"
    public goldEvent() {
        this.laba_gold.setPosition(cc.v2(0, 0));
        let adaptIndex = cc.winSize.height / cv.config.DESIGN_HEIGHT;
        if (cv.config.IS_FULLSCREEN) {
            adaptIndex = (cc.winSize.height - cv.config.FULLSCREEN_OFFSETY) / cv.config.DESIGN_HEIGHT;
        }

        const position = this.gold_target_position.getPosition();
        if (cc.sys.isBrowser) {
            this.laba_gold.runAction(cc.moveTo(0.83, cc.v2(position.x, (position.y + this._goldImgYOffest) * adaptIndex)));
        } else {
            this.laba_gold.runAction(cc.moveTo(0.83, cc.v2(position.x, position.y * adaptIndex)));
        }

        cv.AudioMgr.playEffect('zh_CN/hall/laba/audio/laba_chipfly');
        cv.dataHandler.getUserData().luckindex++;
    }

    protected onLoad(): void {
        // Set images based on language
        this._initImages();
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this._initImages.bind(this), this.node);
    }

    protected onDestroy() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
        cv.AudioMgr.stopAll();
    }

    private _initImages() {
        this._setImages();
        cv.resMgr.adaptWidget(this.node);
    }

    private _setRewards() {
        const randomIndexArray: number[] = [];
        const randomCount: number = this.cells.length / 2;
        for (let i = 0; i < randomCount; ++i) {
            randomIndexArray[i] = Math.floor(Math.random() * this._rewardList.length);
            for (let j = 0; j < i; ++j) {
                if (randomIndexArray[i] === randomIndexArray[j]) {
                    --i;
                    break;
                }
            }

            this.cells[i].string = cv.StringTools.numberToString(
                cv.StringTools.clientGoldByServer(this._rewardList[randomIndexArray[i]])
            );
            this.cells[i + randomCount].string = cv.StringTools.numberToString(
                cv.StringTools.clientGoldByServer(this._rewardList[randomIndexArray[i]])
            );
        }
    }

    private _setRemainNumber() {
        const userData = cv.dataHandler.getUserData();
        let remainNumber = userData.lucks.length - this._luckindex;
        if (remainNumber < 0) {
            remainNumber = 0;
        }

        cv.StringTools.setRichTextString(
            this.des_num_text.node,
            cv.StringTools.formatC(cv.config.getStringData('Laba_laba_panel_des_num_text'), remainNumber)
        );
        this.des_text.string = cv.config.getStringData('Laba_laba_panel_des_text');
    }

    private _playDefaultAnimation() {
        this.laba_animation.play('LabaSlot_Init');
        this.button_hand_animation.playAdditive('Hand_Loop');
        this.button_hand_animation.playAdditive('Stars_Loop');
    }

    private _onAnimationFinished() {
        const userData = cv.dataHandler.getUserData(); 
        if (userData.luckindex < userData.lucks.length) {
            if (this._action_running) {
                this._action_running = false;
                this.init(false);
            }
        } else {
            this.closeView();
        }

        cv.worldNet.requestGetUserData();
    }

    private _setImages() {
        cv.resMgr.setSpriteFrame(this.sprite_title, cv.config.getLanguagePath('hall/laba/title'));
        cv.resMgr.setSpriteFrame(this.result_title, cv.config.getLanguagePath('hall/laba/jieguozhansd'));
    }

    //#region For Generating neighboring winning numbers
    private _genAdjentNum(winNum: number): number[] {

        let section = this._upperBonds.length - 1;
        for(let i=0; i < this._upperBonds.length; ++i) {
            if(winNum < this._upperBonds[i]) {
                section = i;
                break;
            }
        }

        let excludeDigits = [];

        //define: digit of 0.xx is 0
        const winNumDigit = Math.floor(Math.log10(Math.abs(winNum))) +1 
        excludeDigits.push(winNumDigit)
        const adjNumDigit1 = this._genExpectDigit(excludeDigits, this._diffs[section].minDigit, this._diffs[section].maxDigit);
        let adjentNum1 = this._genNumWithDigit(adjNumDigit1);
        adjentNum1 = this._fixAdjNumber(adjentNum1, winNum, section);

        const adjNumDigit = Math.floor(Math.log10(Math.abs(adjentNum1))) +1 ;
        excludeDigits.push(adjNumDigit);
        const adjNumDigit2 = this._genExpectDigit(excludeDigits, this._diffs[section].minDigit, this._diffs[section].maxDigit);
        let adjentNum2 = this._genNumWithDigit(adjNumDigit2);
        adjentNum2 = this._fixAdjNumber(adjentNum2, winNum, section);

        return [adjentNum1,adjentNum2];
    }

    private _fixAdjNumber(adjentNum: number, winNum: number, section: number) {

      // check the `diff` of adjentNum and winNum is big enough
      if(adjentNum >= winNum) {
          // adjentNum bigger than winNum, but not big enough
          if(adjentNum < winNum + this._diffs[section].pBias) {

              //re-gen adjentNum, such it is at least `pBias` bigger than winNum
              const rangeMin = winNum + this._diffs[section].pBias;
              const rangeMax = this._maxAdjNum;
              adjentNum = Math.random() * (rangeMax - rangeMin) + rangeMin;
          }

      } else {
           // adjentNum smaller than winNum, but not small enough
          if(adjentNum > winNum - this._diffs[section].nBias) {

              //re-gen adjentNum, such it is at least `nBias` smaller than winNum
              const rangeMin = this._minAdjNum;
              const rangeMax = winNum - this._diffs[section].nBias
              adjentNum = Math.random() * (rangeMax - rangeMin) + rangeMin;
          }
      }

      if(adjentNum < this._minAdjNum) {
        //prun adjentNum = 0.00xxx case
        adjentNum = this._minAdjNum;
      } else if(adjentNum > this._maxAdjNum) {
        adjentNum = this._maxAdjNum;
      }

      return adjentNum;
    }

    private _genExpectDigit(exludeDigits: number[], minDigit: number, maxDigit: number): number {
      let iter = 100;
      let randomDigit = maxDigit;
      while(--iter) {
          randomDigit = Math.floor(Math.random() * (maxDigit - minDigit+1)) + minDigit;
          if(!exludeDigits.includes(randomDigit)) {
            break;
          }
      }

      return randomDigit;
    }

    private _genNumWithDigit(digit: number): number {

        const min = Math.pow(10, digit - 1);
        const max = Math.pow(10, digit) - 1;
      
        return Math.random() * (max - min) + min;
    }
    //#endregion For Generating neighboring winning numbers
}
