import cv from "../cv";

const {ccclass, property} = cc._decorator;

@ccclass
export default class RedEnvelopePopup extends cc.Component {

    @property(cc.Label)
    title: cc.Label = null;

    @property(cc.Label)
    subtitle: cc.Label = null;

    @property(cc.Label)
    desccription: cc.Label = null;



    start () { }

    protected onEnable(){
        
        this.title.string =  cv.config.getStringData("RedEnvelopPopupTitle");
        this.subtitle.string =  cv.config.getStringData("RedEnvelopPopupSubTitle");
        this.desccription.string =  cv.config.getStringData("RedEnvelopPopupMsgText");
    }


    onCloseBtnClick(){
        cv.AudioMgr.playButtonSound('close');
        this.node.active =  false ;
    }

}
