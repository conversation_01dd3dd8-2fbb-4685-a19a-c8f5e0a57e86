import ws_protocol = require('../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;

import cv from '../cv';
import { HTMLWebView } from '../../../common/tools/HTMLWebView';

const { ccclass, property } = cc._decorator;
@ccclass
export class PocketGameScene extends cc.Component {
    @property(HTMLWebView) web: HTMLWebView = null;
    @property(cc.Button) exitBtn: cc.Button = null;
    @property(cc.Widget) webWidget: cc.Widget = null;
    private needRecharge: boolean = false;
    private originalWebPos: cc.Vec2 = null;
    private _currentGameId: number = 0;

    onLoad() {
        if (cv.roomManager.getCurrentGameID() === world_pb.GameId.TopMatches) {
            cv.config.adaptScreenHen(this.node);
        } else {
            cv.config.adaptScreen(this.node);
        }
        // 这行要注释掉, ipad上会引起webview视图紊乱放大, 解决方案要么注释, 要么widget后动态添加webview
        // cv.resMgr.adaptWidget(this.node, true);

        cv.MessageCenter.register('startSlotGame', this.startSlotGameScene.bind(this), this.node);

        cv.MessageCenter.register('HideWebview_ShowWindows', this.HandleSwitchServer.bind(this), this.node);
        cv.MessageCenter.register('showSportsScene', this.showSportsScene.bind(this), this.node);
        cv.MessageCenter.register('Exit_click', this.exitGame.bind(this), this.node);
        cv.MessageCenter.register('OnLoginServer', this._onReconnection.bind(this), this.node);

        this.originalWebPos = this.web.node.position;
        this.exitBtn.node.on(cc.Node.EventType.TOUCH_END, this._onExitButtonClick.bind(this), this);
    }

    start() {
        this.needRecharge = false;
        cv.viewAdaptive.isselfchange = false;
        this.web.setJavascriptInterfaceScheme('ccjs');
        this.web.setOnJSCallback((webView: cc.WebView, url: string) => {
            if (url.search('ccjs://back-normal-recharge') !== -1) {
                this.needRecharge = true;
                this.exitGame();
                return;
            }
            if (url.search('ccjs://back-normal') !== -1) {
                this.exitGame();
            }
        });
        this.showSportsScene();
    }

    onDestroy() {
        cv.MessageCenter.unregister('HideWebview_ShowWindows', this.node);
        cv.MessageCenter.unregister('startSlotGame', this.node);
        cv.MessageCenter.unregister('showSportsScene', this.node);
        cv.MessageCenter.unregister('Exit_click', this.node);
        cv.MessageCenter.unregister('OnLoginServer', this.node);
    }

    HandleSwitchServer(isView: boolean = false) {
        const active = !cv.TP.getVisible() && isView;
        this._setWebviewVisible(active);
    }

    _setWebviewVisible(isView: boolean) {
        if (isView) {
            this.web.node.setPosition(this.originalWebPos);
            this.webWidget.enabled = true;
            this.webWidget.updateAlignment();
        } else {
            this.web.node.setPosition(cc.v2(0, 10000));
            this.webWidget.enabled = false;
        }
    }

    _onExitButtonClick() {
        cv.TP.showMsg(
            cv.config.getStringData('MiniGame_UIconfirmExit'),
            cv.Enum.ButtonStyle.TWO_BUTTON,
            () => {
                this.exitGame();
            },
            () => {
                this._setWebviewVisible(true);
            }
        );
    }

    exitGame() {
        const func =()=>{
            cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene): void => {
                if (!cv.roomManager.isEnterMTT) {
                    cv.MessageCenter.send('switchSceneToMiniGame');
                }
            });
        }

        if (this._currentGameId === world_pb.GameId.PPGames) {
            this.web.url = '';
            cv.worldNet.PpLeaveRequest(cv.roomManager.getPpEntryName());
            this.scheduleOnce(func, 0.3);// especially PP game, need to wait a while before switch scene. If not, it may cause the game to be stuck.
        }
        else{
            cv.worldNet.PgLeaveRequest();
            cv.worldNet.PgBonusAndFreeGamesRequest(); // 退出电子小游戏后，请求电子小游戏福利数据
            func();
        }
        cv.roomManager.reset();
    }

    startSlotGameScene(gameid: number) {
        console.log('startSlotGameScene', gameid, cv.roomManager.getPpGameUrl());
        this._currentGameId = gameid;
        switch(gameid){
            case world_pb.GameId.PocketGames:
                this.loadHtmlContent();
                break;
            case world_pb.GameId.PPGames:
                this._openPpGame();
                break;
        }
        this.showSportsScene();
    }

    private _openPpGame() {
        this.web.url = cv.roomManager.getPpGameUrl();
    }

    showSportsScene() {
        const active = cv.TP.getVisible();
        this.setWebActive(!active);
    }

    loadHtmlContent() {
        this.web.loadHTMLString(cv.roomManager.getPGHtmlContent());
    }


    setWebActive(isView: boolean) {
        switch (cv.roomManager.getCurrentGameID()) {
            case world_pb.GameId.BlackJack:
            case world_pb.GameId.Sports:
            case world_pb.GameId.TopMatches:
                this.web.node.active = isView;
                break;
            case world_pb.GameId.PocketGames:
                this._setWebviewVisible(isView);
                break;
            default:
                break;
        }
    }

    // on reconnection it will trigger sport leave request on backend side, hence we need to perform sport login upon reconnection
    _onReconnection() {
        const gameId = cv.roomManager.getCurrentGameID();

        switch (gameId) {
            case world_pb.GameId.Sports:
            case world_pb.GameId.TopMatches:
                cv.roomManager.RequestJoinSportsRoom(gameId);
                break;
            case world_pb.GameId.PocketGames:
                break;
            default:
                break;
        }
    }
}
