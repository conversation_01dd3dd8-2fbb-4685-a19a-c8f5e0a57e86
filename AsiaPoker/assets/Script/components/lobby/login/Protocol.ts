// Learn TypeScript:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html
import { ENV_KEY } from "../../../common/tools/Config";
import cv from "../cv";
import { SafeAreaHelper } from "../../../../default/shared/safe_area_helper/SafeAreaHelper";

const { ccclass, property } = cc._decorator;

@ccclass
export class Protocol extends cc.Component {
  
    @property(cc.Node)
    safearea: cc.Node = null;

    @property(cc.Node)
    rule: cc.Node = null;
    
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;
    
    @property(cc.Layout)
    headerLayout: cc.Layout = null;
    
    @property(cc.Label)
    title: cc.Label = null;

    @property(cc.WebView)
    web: cc.WebView = null;

    PROTOTAL_RULE = "user/article/getAgreement?unique=register&clientType=%s&language=%s";

    start() {
        this.setSafeArea();
        this.title.string = cv.config.getStringData("Login_Scene_register_panel_Text_4_0_0");
        this.showWebview();
        cc.find("Header/content/back_button", this.node).on("click", (event: cc.Event): void => {
            cv.AudioMgr.playButtonSound('back_button');
            this.node.active = false;
        })
    }
    
    setSafeArea() {
      // 100 is default for all top align without notch
      const offsetY = SafeAreaHelper.getUpperDangerZoneYOffset() || 100;
      this.safearea.height = offsetY; 
      this.headerLayout.updateLayout();
      
      const scrollViewHeight = cc.view.getVisibleSize().height - this.headerLayout.node.height;
      this.scrollView.node.height = scrollViewHeight;
      this.scrollView.node.getChildByName("view").height = scrollViewHeight;
      this.rule.height = scrollViewHeight;
      this.rule.parent.getComponent(cc.Layout).updateLayout();


      const scrollviewWG = this.scrollView.node.getComponent(cc.Widget);
      scrollviewWG.top = this.headerLayout.node.height;
      scrollviewWG.updateAlignment()

    }

    showWebview() {
        const apiHead:string = cv.config.GET_ENV_DATA(ENV_KEY.WEB_API_HEAD) as string;
        this.web.url = apiHead + cv.StringTools.formatC(this.PROTOTAL_RULE, cv.config.GET_CLIENT_TYPE(), cv.config.getCurrentLanguage());    
    }
}