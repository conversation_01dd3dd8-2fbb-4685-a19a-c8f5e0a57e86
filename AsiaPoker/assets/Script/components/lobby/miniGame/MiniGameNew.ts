/* eslint-disable no-lone-blocks */
import ws_protocol = require('./../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;

import { HashMap } from '../../../common/tools/HashMap';
import { TableView } from '../../../common/tools/TableView';
import cv from '../../../components/lobby/cv';

import { BlackjackRoomListItem } from './BlackjackRoomListItem';
import { MiniGameNewItem, MiniGameNewItemData, MiniGameNewItemsInfo } from './MiniGameNewItem';
import { MiniGameNewRoomList } from './MiniGameNewRoomList';
import { MiniGameNewRoomListItem } from './MiniGameNewRoomListItem';
import { MiniGameNewRoomListItemEuropean } from './MiniGameNewRoomListItemEuropean';
import { ClickEvent<PERSON><PERSON><PERSON>, TipsPanelThemeType } from '../../../common/prefab/TipsPanelConfig';
import PocketGameListViewController from './PocketGameListViewController';
import BundleDownloadDialog from './BundleDownloadDialog';

const IPAD_DESIGN_WIDTH = 1536; // H: 2048

const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameNew extends cc.Component {
    @property(cc.Prefab) prefab_roomList: cc.Prefab = null; // 游戏房间预制件
    @property(TableView) tableview: TableView = null;
    @property(cc.Prefab) popSilencePre: cc.Prefab = null; // 冷静预制件
    @property(cc.Prefab) pgGameListPrefab: cc.Prefab = null;

    @property(BundleDownloadDialog)
    private bundleDownloadDialog: BundleDownloadDialog = null;

    static g_class_name: string = 'MiniGameNew'; // 类名
    private static g_instance: MiniGameNew = null; // 伪单例

    private _gameIDs: number[]; // 游戏id有序数组
    private _gameRoomMap: HashMap<number, world_pb.MiniGame[]> = new HashMap(); // 游戏id对应的房间列表
    private _itemsMap: HashMap<number, MiniGameNewItemData> = new HashMap(); // 小游戏界面数据存储
    private _autoEnterMiniGame: boolean = false; // 是否需要自动进入小游戏
    private _autoJumpGameData: any = null; // 自动跳转进入小游戏数据

    // pocket game
    private _pocketGameListNode: cc.Node = null;
    private _pocketGameListViewController: PocketGameListViewController = null;

    /**
     * 静态初始化实例(会"add"到对应父节点且隐藏, 且只生效一次)
     * @brief 若调用层不采用该实例化方法, 也可自行维护
     * @param prefab        该预制件引用
     * @param parentNode    父节点(缺省时默认当前场景节点)
     * @param zorder        节点内部Z序(缺省时默认枚举)
     * @param pos           该节点实例化后的位置(缺省时默认居中)
     */
    static initSingleInst(prefab: cc.Prefab, parentNode?: cc.Node, zorder?: number, pos?: cc.Vec2): MiniGameNew {
        if (!(prefab instanceof cc.Prefab)) return null;

        if (!MiniGameNew.g_instance || !cc.isValid(MiniGameNew.g_instance)) {
            parentNode = parentNode || cc.director.getScene();
            zorder = zorder || 0;

            let inst: cc.Node = cc.instantiate(prefab);
            MiniGameNew.g_instance = inst.getComponent(MiniGameNew);
            if (MiniGameNew.g_instance) {
                const v2_size: cc.Vec2 = cc.v2(inst.width, inst.height);
                const v2_scale: cc.Vec2 = cc.v2(inst.scaleX, inst.scaleY);
                pos =
                    pos ||
                    inst.getAnchorPoint().sub(parentNode.getAnchorPoint()).scaleSelf(v2_size).scaleSelf(v2_scale);
                inst.setPosition(pos);
                inst.active = false;
                parentNode.addChild(inst, zorder);
            } else {
                inst.destroy();
                inst = null;
            }
        }

        return MiniGameNew.g_instance;
    }

    protected onLoad(): void {
        if (!MiniGameNew.g_instance) MiniGameNew.g_instance = this;

        // 适配ios全面屏(底部栏)
        if (cc.sys.os === cc.sys.OS_IOS && cv.config.IS_FULLSCREEN) {
            const widget: cc.Widget = this.tableview.getComponent(cc.Widget);
            widget.bottom += cv.config.FULLSCREEN_OFFSETY_B;
        }

        cv.resMgr.adaptWidget(this.node, true);

        // 游戏id有序映射
        this._gameIDs = [];
        this._gameIDs.push(world_pb.GameId.GameId_Dummy);
        this._gameIDs.push(world_pb.GameId.CowBoy);

        // H5 先暫時屏蔽補魚
        if (!cc.sys.isBrowser) {
            this._gameIDs.push(world_pb.GameId.OBGames);
        }

        this._gameIDs.push(world_pb.GameId.ISlot);
        this._gameIDs.push(world_pb.GameId.HumanBoy);
        // Draft :: remove videocowboy
        // this._gameIDs.push(world_pb.GameId.VideoCowboy);
        this._gameIDs.push(world_pb.GameId.PokerMaster);
        this._gameIDs.push(world_pb.GameId.Sports);
        this._gameIDs.push(world_pb.GameId.TopMatches);

        // H5 先暫時屏蔽21點
        if (!cc.sys.isBrowser) {
            this._gameIDs.push(world_pb.GameId.BlackJack);
        }

        this._gameIDs.push(world_pb.GameId.CaribbeanTexasHold);
        this._gameIDs.push(world_pb.GameId.WealthTrio);
        this._gameIDs.push(world_pb.GameId.BitMasterGoldCoin);
        this._gameIDs.push(world_pb.GameId.PocketGames);
        this._autoEnterMiniGame = false;
        this._autoJumpGameData = null;

        // pocket game list
        this._pocketGameListNode = cc.instantiate(this.pgGameListPrefab);
        this._pocketGameListViewController = this._pocketGameListNode.getComponent(PocketGameListViewController);
        this._pocketGameListNode.active = false;
        const parentNode = cc.director.getScene();
        parentNode.addChild(this._pocketGameListNode);
        if (cc.sys.isBrowser) {
            cc.view.on("canvas-resize", this.onCanvasResize, this);
        }
    }

    onCanvasResize(): void {
        if (!this.node.active) {
            return; 
        }
        const isWide: boolean = cv.native.isWideScreen();
        const scale = isWide ? Math.max(cc.winSize.width / IPAD_DESIGN_WIDTH, IPAD_DESIGN_WIDTH / cc.winSize.width) : 1;
        this.tableview.cellScale = scale;
        this.tableview.reloadView();
    }
    
    protected onEnable(): void {
        this._registerEvent();
        cv.worldNet.MiniGamesListRequest();
    }

    protected onDisable(): void {
        this._unregisterEvent();
        if (this.tableview) this.tableview.clearView();
    }

    protected onDestroy(): void {
        if (cc.sys.isBrowser) {
            cc.view.off("canvas-resize", this.onCanvasResize, this);
        }
        MiniGameNew.g_instance = null;
    }

    private _registerEvent(): void {
        cv.MessageCenter.register('MiniGamesListResponse', this._onMsgRespRoomList.bind(this), this.node);
        cv.MessageCenter.register('startSportsScene', this._onMsgRespStartSportsScene.bind(this), this.node);
        cv.MessageCenter.register('startBlackJackScene', this._onMsgRespStartSportsScene.bind(this), this.node);
        cv.MessageCenter.register('startFishingKingScene', this._onMsgRespStartSportsScene.bind(this), this.node);
        cv.MessageCenter.register('startCaribbeanGame', this._onMsgRespStartSportsScene.bind(this), this.node);
        cv.MessageCenter.register('startWealthTrioGame', this._onMsgRespStartSportsScene.bind(this), this.node);
        cv.MessageCenter.register('startBitMasterGame', this._onMsgRespStartSportsScene.bind(this), this.node);
        cv.MessageCenter.register('startISlotGame', this._onMsgRespStartSportsScene.bind(this), this.node);
        cv.MessageCenter.register('enterMiniGameEvent', this._onMsgEnterMiniGameEvent.bind(this), this.node);
        cv.MessageCenter.register('openBundleDownloadDialog', this.onOpenBundleDownloadDialog.bind(this), this.node);

        cv.MessageCenter.register(
            `${MiniGameNewItem.g_class_name}_click`,
            this._onMsgGameItemClick.bind(this),
            this.node
        );
        cv.MessageCenter.register(
            `${MiniGameNewRoomListItem.g_class_name}_click`,
            this._onMsgRoomListItemClick.bind(this),
            this.node
        );
        cv.MessageCenter.register(
            `${MiniGameNewRoomListItemEuropean.g_class_name}_click`,
            this._onMsgRoomListItemEuropeanClick.bind(this),
            this.node
        );
        cv.MessageCenter.register(
            `${BlackjackRoomListItem.g_class_name}_click`,
            this._onMsgRoomListItemBlackJackClick.bind(this),
            this.node
        );
        cv.MessageCenter.register("onForcedLogout", this.onForcedLogout.bind(this), this.node);
    }

    private onOpenBundleDownloadDialog(retryCallback: () => void): void {
        if (!this.bundleDownloadDialog) {
            return;
        }

        if (retryCallback) {
            this.bundleDownloadDialog.showRetryDialog(retryCallback);
        } else {
            this.bundleDownloadDialog.showFailedDialog();
        }
    }

    private _unregisterEvent(): void {
        cv.MessageCenter.unregister('MiniGamesListResponse', this.node);
        cv.MessageCenter.unregister('startBlackJackScene', this.node);
        cv.MessageCenter.unregister('startSportsScene', this.node);
        cv.MessageCenter.unregister('enterMiniGameEvent', this.node);
        cv.MessageCenter.unregister('startCaribbeanGame', this.node);
        cv.MessageCenter.unregister('startWealthTrioGame', this.node);
        cv.MessageCenter.unregister('startBitMasterGame', this.node);
        cv.MessageCenter.unregister('startFishingKingScene', this.node);
        cv.MessageCenter.unregister('startISlotGame', this.node);
        cv.MessageCenter.unregister(`${MiniGameNewItem.g_class_name}_click`, this.node);
        cv.MessageCenter.unregister(`${MiniGameNewRoomListItem.g_class_name}_click`, this.node);
        cv.MessageCenter.unregister(`${MiniGameNewRoomListItemEuropean.g_class_name}_click`, this.node);
        cv.MessageCenter.unregister(`${BlackjackRoomListItem.g_class_name}_click`, this.node);
        cv.MessageCenter.unregister('bundleDownloadDialog', this.node);
        cv.MessageCenter.unregister('onForcedLogout', this.node);
    }

    private _sortRoomList(a: world_pb.MiniGame, b: world_pb.MiniGame): number {
        if (a.sourceType !== b.sourceType) {
            return a.sourceType - b.sourceType;
        }
        if (a.deskType !== b.deskType) {
            return a.deskType - b.deskType;
        }
        if (a.AmountLevel[0] !== b.AmountLevel[0]) {
            return a.AmountLevel[0] - b.AmountLevel[0];
        }

        return a.roomid - b.roomid;
    }

    private _parseJsonArray(text: string): any[] {
        let json: any[] = [];

        try {
            json = JSON.parse(text);
        } catch (error) {
            json = [];
        }

        return json;
    }

    /**
     * 房间列表消息通知
     * @param msg
     */
    private _onMsgRespRoomList(msg: world_pb.MiniGame[]): void {
        this._gameRoomMap.clear();
        msg.sort(this._sortRoomList);

        // 添加房间列表
        for (let i = 0; i < msg.length; ++i) {
            const gameid: number = msg[i].sourceType;
            let roomList: world_pb.MiniGame[] = this._gameRoomMap.get(gameid);
            if (!roomList) roomList = [];
            roomList.push(msg[i]);
            this._gameRoomMap.add(gameid, roomList);
        }

        // 解析数据
        this._itemsMap.clear();
        for (let i = 0; i < this._gameIDs.length; ++i) {
            const gameid: number = this._gameIDs[i];
            const t: MiniGameNewItemData = new MiniGameNewItemData();
            t.gameid = gameid;

            let roomList: world_pb.MiniGame[] = this._gameRoomMap.get(gameid);
            if (gameid === world_pb.GameId.CaribbeanTexasHold) {
                const studRoomData = this._gameRoomMap.get(world_pb.GameId.CaribbeanStud);
                roomList = roomList ? roomList.concat(studRoomData ?? []) : studRoomData ?? [];
            } else if (gameid === world_pb.GameId.WealthTrio) {
                const roomData = this._gameRoomMap.get(world_pb.GameId.WealthTrio);
                roomList = roomList ? roomList.concat(roomData ?? []) : roomData ?? [];
            } else if (gameid === world_pb.GameId.BitMasterGoldCoin) {
                const roomData = this._gameRoomMap.get(world_pb.GameId.BitMasterUSD);
                roomList = roomList ? roomList.concat(roomData ?? []) : roomData ?? [];
            }

            if (roomList && roomList.length > 0) {
                const bHot: boolean = roomList[0].isHot; // 是否显示hot图标
                const miniLabel: world_pb.MiniLabel = roomList[0].label; // 显示标签类型
                t.bHot = bHot;
                t.label = miniLabel;

                switch (gameid) {
                    // 一起看球
                    case world_pb.GameId.TopMatches:
                        {
                            t.status = 1;
                            const topMatches: any[] = this._parseJsonArray(roomList[0].topMatches);
                            if (Array.isArray(topMatches)) {
                                for (let j = 0; j < topMatches.length; ++j) {
                                    t.online += cv.Number(topMatches[j].count);
                                }
                            }
                        }
                        break;
                    // 电子游戏
                    case world_pb.GameId.PocketGames: {
                        // No need roomlist info and just login PG lobby
                        const pgGameData: world_pb.PgGameData[] = roomList[0].pgGameData as world_pb.PgGameData[];
                        if (pgGameData.length > 0) {
                            t.status = 1;
                            t.online = roomList[0].playerNum;
                        }
                        break;
                    }
                    // Fishing king ob games
                    case world_pb.GameId.OBGames:
                        {
                            if (roomList.length > 0) {
                                t.status = roomList[0].ObGame.status ? 1 : 0;
                                t.online = roomList[0].ObGame.playerNum;
                            }
                        }
                        break;

                    // Haba Caribbean Stud  game
                    case world_pb.GameId.CaribbeanTexasHold:
                    case world_pb.GameId.BitMasterGoldCoin:
                    case world_pb.GameId.WealthTrio:
                        for (let j = 0; j < roomList.length; ++j) {
                            if (t.bHot !== true) t.bHot = roomList[j].isHot; // Show Hot label in either of games send Hot flag
                            t.status = 1;
                            t.online += roomList[j].playerNum;
                        }
                        break;
                    // 其他游戏
                    default:
                        {
                            t.status = 1;
                            for (let j = 0; j < roomList.length; ++j) {
                                t.online += roomList[j].playerNum;
                            }
                        }
                        break;
                }
            }

            t.mini_hotgame = this.isMiniHotgame(gameid);
            t.onDownloadComplete = this._showToastGameReady.bind(this);
            this._itemsMap.add(t.gameid, t);
        }

        // 刷新游戏列表
        const objs: any[] = [];
        let miniGameItemAry: MiniGameNewItemData[] = [];

        if (cc.sys.isBrowser) {
            miniGameItemAry = [
                // row 1
                this._itemsMap.get(world_pb.GameId.CowBoy),
                // this._itemsMap.get(world_pb.GameId.OBGames),
                // row 2
                this._itemsMap.get(world_pb.GameId.Sports),
                this._itemsMap.get(world_pb.GameId.PocketGames),
                // row 3
                this._itemsMap.get(world_pb.GameId.WealthTrio),
                this._itemsMap.get(world_pb.GameId.PokerMaster),
                // row 4
                this._itemsMap.get(world_pb.GameId.HumanBoy),
                // Draft :: remove videocowboy
                // this._itemsMap.get(world_pb.GameId.VideoCowboy),
                // this._itemsMap.get(world_pb.GameId.BlackJack),
                this._itemsMap.get(world_pb.GameId.CaribbeanTexasHold),
                this._itemsMap.get(world_pb.GameId.BitMasterGoldCoin)
                // this._itemsMap.get(world_pb.GameId.ISlot)
            ];
        } else {
            miniGameItemAry = [
                // row 1
                this._itemsMap.get(world_pb.GameId.CowBoy),
                // row 2
                this._itemsMap.get(world_pb.GameId.OBGames),
                this._itemsMap.get(world_pb.GameId.Sports),
                // row 3
                this._itemsMap.get(world_pb.GameId.WealthTrio),
                this._itemsMap.get(world_pb.GameId.PocketGames),
                // row 4
                this._itemsMap.get(world_pb.GameId.PokerMaster),
                this._itemsMap.get(world_pb.GameId.HumanBoy),
                // Draft :: remove videocowboy
                // this._itemsMap.get(world_pb.GameId.VideoCowboy),
                this._itemsMap.get(world_pb.GameId.BlackJack),
                this._itemsMap.get(world_pb.GameId.CaribbeanTexasHold),
                this._itemsMap.get(world_pb.GameId.BitMasterGoldCoin)
                // this._itemsMap.get(world_pb.GameId.ISlot),
                // this._itemsMap.get(world_pb.GameId.GameId_Dummy),
            ];
        }

        let t: MiniGameNewItemsInfo = new MiniGameNewItemsInfo();
        for (let i = 0; i < miniGameItemAry.length; ++i) {
            t.items.push(miniGameItemAry[i]);
            if (i < 1 && t.items.length === 1) {
                objs.push({ prefab_type: 0, prefab_component: MiniGameNewItem, prefab_datas: t });
                t = new MiniGameNewItemsInfo();
            } else if (i < 5 && t.items.length === 2) {
                objs.push({ prefab_type: 1, prefab_component: MiniGameNewItem, prefab_datas: t });
                t = new MiniGameNewItemsInfo();
            } else if (t.items.length === 3 || i === miniGameItemAry.length - 1) {
                objs.push({ prefab_type: i > 3 ? 2 : 1, prefab_component: MiniGameNewItem, prefab_datas: t });
                t = new MiniGameNewItemsInfo();
            }
        }
        const isWide: boolean = cv.native.isWideScreen();
        const scale = isWide ? Math.max(cc.winSize.width / IPAD_DESIGN_WIDTH, IPAD_DESIGN_WIDTH / cc.winSize.width) : 1;

        this.tableview.cellScale = scale;
        this.tableview.clearView(true);
        this.tableview.bindData(objs);
        this.tableview.reloadView();

        // 点击活动弹窗跳转小游戏事件到达后, 小游戏列表数据可能还没返回
        // 所以在返回小游戏列表数据后, 判断下是否要自动进入小游戏
        if (this._autoEnterMiniGame && this._autoJumpGameData) {
            this._onMsgEnterMiniGameEvent(this._autoJumpGameData);
        }
    }

    /**
     * 切换"体育竞猜"游戏场景
     */
    private _onMsgRespStartSportsScene(gameid: number): void {
        switch (gameid) {
            // 一起看球, 横屏
            case world_pb.GameId.TopMatches:
                cv.action.switchScene(cv.Enum.SCENE.TOPMATCHE_SCENE);
                break;
            case world_pb.GameId.BlackJack:
                this._openBJGame();
                break;
            case world_pb.GameId.OBGames:
                this._openFishGame();
                break;
            case world_pb.GameId.CaribbeanTexasHold:
                if (!cv.config.alllowMultipWindow()) {
                    cv.action.switchScene(cv.Enum.SCENE.CARIBBEAN_POKER_SCENE);
                } else {
                    cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                        cv.config.gameWindow = window.open(cv.roomManager.getHabaCaribbeanUrl());
                    });
                }
                break;
            case world_pb.GameId.WealthTrio:
                if (!cv.config.alllowMultipWindow()) {
                    cv.action.switchScene(cv.Enum.SCENE.WEALTHTRIO_SCENE);
                } else {
                    cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                        cv.config.gameWindow = window.open(cv.roomManager.getHabaWealthTrioUrl());
                    });
                }
                break;
            case world_pb.GameId.BitMasterGoldCoin:
                if (!cv.config.alllowMultipWindow()) {
                    cv.action.switchScene(cv.Enum.SCENE.BIT_MASTER_SCENE);
                } else {
                    cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                        cv.config.gameWindow = window.open(cv.roomManager.getBitMasterUrl());
                    });
                }
                break;
            case world_pb.GameId.ISlot:
                if (!cv.config.alllowMultipWindow()) {
                    cv.action.switchScene(cv.Enum.SCENE.ISLOT_SCENE);
                } else {
                    cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                        cv.config.gameWindow = window.open(cv.roomManager.getISlotnUrl());
                    });
                }
                break;
            // 体育赛事/电子游戏, 竖屏
            case world_pb.GameId.Sports:
                if (!CC_PREVIEW && !cv.config.alllowMultipWindow()) {
                    cv.action.switchScene(cv.Enum.SCENE.SPORTS_SCENE);
                } else {
                    cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                        cv.config.gameWindow = window.open(cv.roomManager.getSportsUrl());
                    });
                }
                break;
            case world_pb.GameId.PocketGames:
                {
                    cv.action.switchScene(cv.Enum.SCENE.POCKETGAME_SCENE, (scene: cc.Scene) => {
                        cv.MessageCenter.send('startSlotGame', gameid);
                    });
                }
                break;
            case world_pb.GameId.PPGames:
                {
                    this._openPpGame();
                }
                break;
            default:
                break;
        }
    }

    /**
     * 游戏图标"item"点击事件
     * @param data
     */
    private async _onMsgGameItemClick(data: MiniGameNewItemData): Promise<void> {
        if (data.status === 0) {
            const porfix: string = cv.config.getStringData('ServerErrorCode104');
            cv.TT.showMsg(porfix, cv.Enum.ToastType.ToastTypeWarning);
        } else {
            let roomsData: world_pb.MiniGame[] = this._gameRoomMap.get(data.gameid);
            if (data.gameid === world_pb.GameId.CaribbeanTexasHold) {
                const studRoomData = this._gameRoomMap.get(world_pb.GameId.CaribbeanStud);
                roomsData = roomsData ? roomsData.concat(studRoomData ?? []) : studRoomData ?? [];
            } else if (data.gameid === world_pb.GameId.WealthTrio) {
                const studRoomData = this._gameRoomMap.get(world_pb.GameId.WealthTrio);
                roomsData = roomsData ? roomsData.concat(studRoomData ?? []) : studRoomData ?? [];
            } else if (data.gameid === world_pb.GameId.BitMasterGoldCoin) {
                const studRoomData = this._gameRoomMap.get(world_pb.GameId.BitMasterUSD);
                roomsData = roomsData ? roomsData.concat(studRoomData ?? []) : studRoomData ?? [];
            }
            if (!roomsData || roomsData.length <= 0) {
                console.error(`${MiniGameNew.g_class_name}: error, game ${data.gameid} is not exist!`);
                return;
            }

            switch (data.gameid) {
                // 一起看球
                case world_pb.GameId.TopMatches:
                    {
                        const t: world_pb.MiniGame = roomsData[0];
                        const topMatches: any[] = this._parseJsonArray(t.topMatches);

                        // 有房间
                        if (Array.isArray(topMatches) && topMatches.length > 0) {
                            const inst: MiniGameNewRoomList = MiniGameNewRoomList.initSingleInst(this.prefab_roomList);
                            inst.autoShowEuropean(t);
                        }
                        // 没房间
                        else {
                            const porfix: string = cv.config.getStringData('minigame_no_matches');
                            cv.TT.showMsg(porfix, cv.Enum.ToastType.ToastTypeWarning);
                        }

                        // 跟踪用户行为, 发送事件
                        const properties = { gameType: t.sourceType, lowestBet: 0, people: t.playerNum };
                        cv.segmentTool.track(
                            cv.Enum.CurrentScreen.casinoGameSelection,
                            cv.Enum.segmentEvent.CasinoGameSelected,
                            cv.Enum.Functionality.casino,
                            properties
                        );
                    }
                    break;

                // 电子游戏
                case world_pb.GameId.PocketGames:
                    {
                        // TODO: show pocket game list
                        const iSlotGame = this._gameRoomMap.get(world_pb.GameId.ISlot);
                        const pocketGameroomsData: world_pb.MiniGame[] = this._gameRoomMap.get(
                            world_pb.GameId.PocketGames
                        );
                        const extra = [];
                        if (iSlotGame) {
                            const gameData = {
                                gameId: world_pb.GameId.ISlot,
                                gameIcon: `hall/miniGame/new/itemIcon/native/slot_icon_${world_pb.GameId.ISlot}`,
                                gameName: 'iSlot',
                                gameType: world_pb.GameId.ISlot,
                                label: world_pb.PgGameLabel.LabelNew,
                                isChamPoin: null
                            };

                            extra.push(gameData);
                        }

                        // TODO: hard code embed ISlot game into pocket game

                        if (CC_PREVIEW) console.error(pocketGameroomsData);

                        this._pocketGameListViewController.setOnGameIconCallback((game: world_pb.IPgGameData) => {
                            if (game.gameType === world_pb.GameId.PocketGames) {
                                if (!cv.config.alllowMultipWindow()) {
                                    cv.worldNet.PgLoginRequest(game.gameId);
                                } else {
                                    cv.TP.showMsg(
                                        cv.config.getStringData('UIOpenNewWindow'),
                                        cv.Enum.ButtonStyle.TWO_BUTTON,
                                        () => {
                                            cv.worldNet.PgLoginRequest(game.gameId);
                                        },
                                        null
                                    );
                                }
                            } else if (game.gameType === world_pb.GameId.ISlot) {
                                this._onISlotGameClicked();
                            } else {
                                cv.roomManager.setCurrentGameID(game.gameType);
                                cv.roomManager.setPpEntryName(game.entryName);
                                cv.worldNet.PpLoginRequest(game.entryName, 'ccjs://back-normal');
                                cv.SwitchLoadingView.show(cv.config.getStringData('Loading_resource'));
                            }
                        });
                        this._pocketGameListViewController.showPocketGameListView(pocketGameroomsData, extra);
                    }
                    break;

                // Blackjack
                case world_pb.GameId.BlackJack:
                    if (
                        roomsData.length > 0 &&
                        roomsData[0].blackjackRoomLists.error === 0 &&
                        roomsData[0].blackjackRoomLists.room.length > 0
                    ) {
                        // Check if Error == 0, have data of black jack game with room details in it
                        const blackjackRoomData: world_pb.MiniGame[] = this.organizeBlackJackData(roomsData[0]); // Organize room data as per one MiniGame object into three objects based on levels
                        blackjackRoomData.sort(this._sortRoomList);
                        const inst: MiniGameNewRoomList = MiniGameNewRoomList.initSingleInst(this.prefab_roomList);
                        inst.autoShowCom(blackjackRoomData);

                        // 跟踪用户行为, 发送事件
                        const properties = { gameType: data.gameid };
                        cv.segmentTool.track(
                            cv.Enum.CurrentScreen.casinoGameSelection,
                            cv.Enum.segmentEvent.CasinoGameSelected,
                            cv.Enum.Functionality.casino,
                            properties
                        );
                    }
                    break;

                // Fishing King Mini Game
                case world_pb.GameId.OBGames:
                    this._onHappyFishingGameClicked();
                    cv.segmentTool.track(
                        cv.Enum.CurrentScreen.casinoGameSelection,
                        cv.Enum.segmentEvent.CasinoGameSelected,
                        cv.Enum.Functionality.casino,
                        world_pb.GameId.OBGames
                    );
                    break;

                // Haba Caribbean Stud Game
                case world_pb.GameId.CaribbeanTexasHold:
                    if (roomsData.length > 0) {
                        const inst: MiniGameNewRoomList = MiniGameNewRoomList.initSingleInst(this.prefab_roomList);
                        inst.autoShowCaribbean(roomsData);
                    }
                    cv.segmentTool.track(
                        cv.Enum.CurrentScreen.casinoGameSelection,
                        cv.Enum.segmentEvent.CasinoGameSelected,
                        cv.Enum.Functionality.casino,
                        world_pb.GameId.CaribbeanTexasHold
                    );
                    break;
                case world_pb.GameId.WealthTrio:
                    if (roomsData.length > 0) {
                        this._onWeathTrioClicked(roomsData[0].sourceType);
                    }
                    cv.segmentTool.track(
                        cv.Enum.CurrentScreen.casinoGameSelection,
                        cv.Enum.segmentEvent.CasinoGameSelected,
                        cv.Enum.Functionality.casino,
                        world_pb.GameId.WealthTrio
                    );
                    break;
                case world_pb.GameId.BitMasterGoldCoin:
                    if (roomsData.length > 0) {
                        const inst: MiniGameNewRoomList = MiniGameNewRoomList.initSingleInst(this.prefab_roomList);
                        inst.autoShowBitMaster(roomsData);
                    }
                    cv.segmentTool.track(
                        cv.Enum.CurrentScreen.casinoGameSelection,
                        cv.Enum.segmentEvent.CasinoGameSelected,
                        cv.Enum.Functionality.casino,
                        data.gameid
                    );
                    break;
                // ISlot - Slot Machine Game
                case world_pb.GameId.ISlot:
                    this._onISlotGameClicked();
                    cv.segmentTool.track(
                        cv.Enum.CurrentScreen.casinoGameSelection,
                        cv.Enum.segmentEvent.CasinoGameSelected,
                        cv.Enum.Functionality.casino,
                        world_pb.GameId.ISlot
                    );
                    break;

                // 其他游戏
                default:
                    // 多个房间才显示列表(列表逻辑支持只显示1个房间)
                    if (roomsData.length > 1) {
                        const inst: MiniGameNewRoomList = MiniGameNewRoomList.initSingleInst(this.prefab_roomList);
                        inst.autoShowCom(roomsData);

                        // 跟踪用户行为, 发送事件
                        const properties = { gameType: data.gameid };
                        cv.segmentTool.track(
                            cv.Enum.CurrentScreen.casinoGameSelection,
                            cv.Enum.segmentEvent.CasinoGameSelected,
                            cv.Enum.Functionality.casino,
                            properties
                        );
                    } else {
                        this._onMsgRoomListItemClick(roomsData[0]);
                    }
                    break;
            }
        }
    }

    private organizeBlackJackData(blackJackData: world_pb.MiniGame): world_pb.MiniGame[] {
        const { room } = blackJackData.blackjackRoomLists;
        const bj_roomList: world_pb.blackjackRoomList[] = [];

        for (let i = 0; i < room.length; i++) {
            const gameLevel: number = room[i].gameLevels[0];
            let bj_obj: world_pb.blackjackRoomList = null;
            for (let j = 0; j < bj_roomList.length; j++) {
                if (bj_roomList[j].room.filter((item) => item.gameLevels[0] === gameLevel).length > 0) {
                    bj_obj = new world_pb.blackjackRoomList(bj_roomList[j]);
                }
            }
            if (bj_obj) {
                bj_obj.room.push(room[i]);
            } else {
                bj_obj = new world_pb.blackjackRoomList();
                bj_obj.error = blackJackData.blackjackRoomLists.error;
                bj_obj.playerNum = blackJackData.blackjackRoomLists.playerNum;
                bj_obj.room.push(room[i]);
                bj_roomList.push(bj_obj);
            }
        }

        const roomsData: world_pb.MiniGame[] = [];

        for (let i = 0; i < bj_roomList.length; i++) {
            const obj: world_pb.MiniGame = new world_pb.MiniGame();
            Object.assign(obj, blackJackData);
            const gameLevel = bj_roomList[i].room[0].gameLevels[0];
            if (gameLevel === 1) {
                obj.deskType = 1;
            } else if (gameLevel === 10) {
                obj.deskType = 3;
            } else {
                obj.deskType = 2;
            }
            obj.playerNum = this.getTotalPlayer(bj_roomList[i].room);
            obj.isHot = blackJackData.isHot;
            obj.blackjackRoomLists = bj_roomList[i];
            roomsData.push(obj);
        }
        return roomsData;
    }

    private getTotalPlayer(rooms: any[]): number {
        let playerCount = 0;
        for (let i = 0; i < rooms.length; i++) {
            playerCount += rooms[i].playerCount;
        }
        return playerCount;
    }

    /**
     * 常规房间列表"Item"点击事件
     * @param data
     */
    private _onMsgRoomListItemClick(data: world_pb.MiniGame): void {
        if (!data) return;
        console.log(`${MiniGameNew.g_class_name}: click common-game room:${data.roomid}`);

        if (data.sourceType === world_pb.GameId.BlackJack) {
            if (MiniGameNewRoomList.g_instance) {
                MiniGameNewRoomList.g_instance.switchBlackjackPage(data);
            }
            return;
        }

        const gameid: number = data.sourceType;
        const roomid: number = data.roomid;

        // 禁止"禁止德州人员"
        if (cv.dataHandler.getUserData().isban) {
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode501'), cv.Enum.ToastType.ToastTypeInfo);
            return;
        }

        // 禁止"游客"
        if (gameid === world_pb.GameId.Sports && cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(
                cv.config.getStringData('Sports_enter_tip'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                cv.dataHandler.upgradeAccount.bind(cv.dataHandler)
            );
            return;
        }

        // 进入游戏
        if (gameid === world_pb.GameId.Sports) {
            if (!cv.config.alllowMultipWindow()) {
                cv.roomManager.RequestJoinSportsRoom(gameid);
            } else {
                cv.TP.showMsg(
                    cv.config.getStringData('UIOpenNewWindow'),
                    cv.Enum.ButtonStyle.TWO_BUTTON,
                    () => {
                        cv.roomManager.RequestJoinSportsRoom(gameid);
                    },
                    null
                );
            }
        } else {
            cv.roomManager.RequestJoinRoom(gameid, roomid);
        }

        // 跟踪用户行为, 发送事件
        const properties = { gameType: data.sourceType, lowestBet: data.AmountLevel[0], people: data.playerNum };
        cv.segmentTool.track(
            cv.Enum.CurrentScreen.casinoGameSelection,
            cv.Enum.segmentEvent.CasinoGameSelected,
            cv.Enum.Functionality.casino,
            properties
        );
    }

    /**
     * 一起看球房间列表"Item"点击事件
     * @param data
     */
    private _onMsgRoomListItemEuropeanClick(data: any): void {
        console.log(
            `${MiniGameNew.g_class_name}: click european-game matchId:${cv.String(data.matchId)}, sportId:${cv.String(
                data.sportId
            )}`
        );

        // 禁止"禁止德州人员"
        if (cv.dataHandler.getUserData().isban) {
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode501'), cv.Enum.ToastType.ToastTypeInfo);
            return;
        }

        // 禁止"游客"
        if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(
                cv.config.getStringData('Sports_enter_tip'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                cv.dataHandler.upgradeAccount.bind(cv.dataHandler)
            );
            return;
        }

        // 禁用"Ipad"设备
        if (cv.native.IsPad()) {
            cv.TP.showMsg(
                cv.config.getStringData('minigame_is_ipad_error'),
                cv.Enum.ButtonStyle.GOLD_BUTTON,
                (): void => {}
            );
            return;
        }

        // 进入游戏
        const gameid: number = world_pb.GameId.TopMatches;
        const matchId: string = cv.String(data.matchId);

        cv.roomManager.RequestJoinSportsRoom(gameid, matchId);

        // 跟踪用户行为, 发送事件
        const properties = { gameType: gameid };
        cv.segmentTool.track(
            cv.Enum.CurrentScreen.casinoGameSelection,
            cv.Enum.segmentEvent.CasinoGameSelected,
            cv.Enum.Functionality.casino,
            properties
        );
    }

    /**
     * blackjack game room list item click event
     * @param data
     */
    private _onMsgRoomListItemBlackJackClick(data: world_pb.blackjackRoomInfo): void {
        console.log(
            `${MiniGameNew.g_class_name}: click blackjack-game levelId:${cv.String(data.levelID)}, roomId:${cv.String(
                data.roomID
            )}`
        );

        // If Blackjack is in maintenance
        if (!cv.config.HAVE_BLACKJACK_MiniGame) {
            cv.TP.showMsg(
                cv.config.getStringData('Blackjack_in_maintenance'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                cv.dataHandler.upgradeAccount.bind(cv.dataHandler)
            );
            return;
        }

        // 禁止"游客"
        if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(
                cv.config.getStringData('Sports_enter_tip'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                cv.dataHandler.upgradeAccount.bind(cv.dataHandler)
            );
            return;
        }

        // 禁用"Ipad"设备
        if (cv.native.IsPad()) {
            cv.TP.showMsg(
                cv.config.getStringData('minigame_bj_ipad_error'),
                cv.Enum.ButtonStyle.GOLD_BUTTON,
                (): void => {}
            );
            return;
        }

        // 进入游戏
        const roomId: number = data.roomID;
        const levelId: number = data.levelID;
        const gameId: number = data.gameID;
        // Call for Blackjack H5 version from here
        cv.roomManager.RequestJoinBlackJackRoom(roomId, levelId, gameId);
        cv.SwitchLoadingView.show(cv.config.getStringData('Loading_resource'));
        // 跟踪用户行为, 发送事件
        const properties = { gameType: world_pb.GameId.BlackJack, roomId, levelId };
        cv.segmentTool.track(
            cv.Enum.CurrentScreen.casinoGameSelection,
            cv.Enum.segmentEvent.CasinoGameSelected,
            cv.Enum.Functionality.casino,
            properties
        );
    }

    /**
     * Happy Fishing king game  click event
     * @param data
     */
    private _onHappyFishingGameClicked(): void {
        // 禁止"游客"
        if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(
                cv.config.getStringData('Sports_enter_tip'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                cv.dataHandler.upgradeAccount.bind(cv.dataHandler)
            );
            return;
        }
        // 禁用"Ipad"设备
        if (cv.native.IsPad()) {
            cv.TP.showMsg(
                cv.config.getStringData('minigame_fishing_ipad_error'),
                cv.Enum.ButtonStyle.GOLD_BUTTON,
                (): void => {}
            );
            return;
        }
        cv.roomManager.RequestJoinFishingKing();
    }

    private _onISlotGameClicked(): void {
        if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(
                cv.config.getStringData('Sports_enter_tip'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                cv.dataHandler.upgradeAccount.bind(cv.dataHandler)
            );
            return;
        }
        this._showBuyinUSD(() => cv.roomManager.RequestJoinISlot());
    }

    private _onWeathTrioClicked(gameID: number): void {
        if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(
                cv.config.getStringData('Sports_enter_tip'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                cv.dataHandler.upgradeAccount.bind(cv.dataHandler)
            );
            return;
        }
        cv.roomManager.RequestJoinHabaGame(gameID);
    }

    /**
     *自动进入小游戏事件回调
     * @param data { jumpType: number, gameID: number, gameCode: string, matchID:string }
     */
    private _onMsgEnterMiniGameEvent(data: any) {
        if (data === null || typeof data === 'undefined') {
            console.error(`${MiniGameNew.g_class_name} - _onMsgEnterMiniGameEvent: data is null`);
            return;
        }

        // 此时小游列表数据还没有返回
        if (this._itemsMap.length <= 0) {
            this._autoEnterMiniGame = true;
            this._autoJumpGameData = data;
            console.error(
                `${MiniGameNew.g_class_name} - _onMsgEnterMiniGameEvent: Current miniGame list data _itemsMap is null`
            );
            return;
        }

        this._autoEnterMiniGame = false;
        this._autoJumpGameData = null;

        // 开始跳转
        const jumpType: number = cv.Number(data.jumpType);
        switch (jumpType) {
            case cv.Enum.JUMPGAMETYPE.JUMP_TO_MINI_GAME:
                {
                    const gameID: number = cv.Number(data.gameID);
                    const t: MiniGameNewItemData = this._itemsMap.get(gameID);
                    if (t) {
                        this._autoEnterGame(t);
                    }
                }
                break;

            case cv.Enum.JUMPGAMETYPE.JUMP_TO_SPORT:
                {
                    const gameID: number = world_pb.GameId.Sports;
                    const t: MiniGameNewItemData = this._itemsMap.get(gameID);
                    if (t) {
                        this._autoEnterGame(t);
                    }
                }
                break;

            case cv.Enum.JUMPGAMETYPE.JUMP_TO_ELECT_LIST:
                {
                    const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.PocketGames);
                    if (t) {
                        this._onMsgGameItemClick(t);
                    }
                }
                break;

            case cv.Enum.JUMPGAMETYPE.JUMP_TO_ELECT_GAME:
                {
                    const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.PocketGames);
                    if (t) {
                        const gameCode = data.gameCode;
                        // 防止后台配置的电子小游戏名称有错误(管理后台是手动输入, 名称可能存在错误), 此处客户端进行比对判断
                        const bHave = cv.miniGameConfig.ishaveGame(gameCode);
                        if (bHave) {
                            this._autoEnterGame(t, gameCode);
                        } else {
                            console.error(
                                `${MiniGameNew.g_class_name} - _onMsgEnterMiniGameEvent: Server enter mini elect gamecode = [${gameCode}] is error.`
                            );
                        }
                    }
                }
                break;

            case cv.Enum.JUMPGAMETYPE.JUMP_TO_CARIBBEAN_STUD:
                {
                    const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.CaribbeanTexasHold);
                    if (t) {
                        this._onMsgGameItemClick(t);
                    }
                }
                break;

            case cv.Enum.JUMPGAMETYPE.JUMP_TO_WEALTH_TRIO:
                {
                    const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.WealthTrio);
                    if (t) {
                        this._onMsgGameItemClick(t);
                    }
                }
                break;

            case cv.Enum.JUMPGAMETYPE.JUMP_TO_BIT_MASTER:
                {
                    const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.BitMasterGoldCoin);
                    if (t) {
                        this._onMsgGameItemClick(t);
                    }
                }
                break;
                
            case cv.Enum.JUMPGAMETYPE.JUMP_TO_VIDEO_COWBOY:
                {
                    // const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.VideoCowboy);
                    // if (t) {
                    //     this._onMsgGameItemClick(t);
                    // }
                }
                break;
                
            case cv.Enum.JUMPGAMETYPE.JUMP_TO_OB_GAME:
                {
                    const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.OBGames);
                    if (t) {
                        this._onMsgGameItemClick(t);
                    }
                }
                break;
                
            case cv.Enum.JUMPGAMETYPE.JUMP_TO_BLACK_JACK:
                {
                    const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.BlackJack);
                    if (t) {
                        this._onMsgGameItemClick(t);
                    }
                }
                break;

            case cv.Enum.JUMPGAMETYPE.JUMP_TO_WATCH_MACTCHS:
                {
                    const matchID: string = cv.String(data.matchID);
                    const roomsData: world_pb.MiniGame[] = this._gameRoomMap.get(world_pb.GameId.TopMatches);
                    if (roomsData.length > 0) {
                        const t: world_pb.MiniGame = roomsData[0];
                        const topMatches: any[] = this._parseJsonArray(t.topMatches);

                        // 有比赛
                        if (Array.isArray(topMatches) && topMatches.length > 0) {
                            // 查询现在是否存在对应的matchID比赛
                            let bHave = false;
                            if (Array.isArray(topMatches)) {
                                for (let i = 0; i < topMatches.length; ++i) {
                                    if (matchID === topMatches[i].matchId) {
                                        bHave = true;
                                        break;
                                    }
                                }
                            }

                            // "matchID"存在, 直接跳转具体比赛
                            if (bHave) {
                                // 进入具体某个比赛
                                const _dataMatch: any = { matchId: cv.String(matchID) };
                                this._onMsgRoomListItemEuropeanClick(_dataMatch);
                            }
                            // 不存在, 则跳转到列表
                            else {
                                console.error(
                                    `${MiniGameNew.g_class_name} - _onMsgEnterMiniGameEvent: Server enter top matches matchID = [${matchID}] is error.`
                                );
                                const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.TopMatches);
                                if (t) {
                                    this._onMsgGameItemClick(t);
                                }
                            }
                        }
                        // 暂无比赛
                        else {
                            const porfix: string = cv.config.getStringData('minigame_no_matches');
                            cv.TT.showMsg(porfix, cv.Enum.ToastType.ToastTypeWarning);
                        }
                    }
                }
                break;

            case cv.Enum.JUMPGAMETYPE.JUMP_TO_ISLOT:
                {
                    const t: MiniGameNewItemData = this._itemsMap.get(world_pb.GameId.ISlot);
                    if (t) {
                        this._onMsgGameItemClick(t);
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 自动进入游戏
     * @param data
     */
    private _autoEnterGame(data: MiniGameNewItemData, gameCode: string = null): void {
        console.log(`${MiniGameNew.g_class_name} - _autoEnterGame enter gameid:${data.gameid}`);

        if (data.status === 0) {
            const porfix: string = cv.config.getStringData('ServerErrorCode104');
            cv.TT.showMsg(porfix, cv.Enum.ToastType.ToastTypeWarning);
        } else {
            const roomsData: world_pb.MiniGame[] = this._gameRoomMap.get(data.gameid);
            if (!roomsData || roomsData.length <= 0) {
                console.error(`${MiniGameNew.g_class_name} - _autoEnterGame enter, game ${data.gameid} is not exist!`);
                return;
            }

            // 寻找一个初级房间
            let _roomData: world_pb.MiniGame = roomsData[0];
            for (let i = 0; i < roomsData.length; ++i) {
                // 初级房间
                if (roomsData[i].deskType === 1) {
                    _roomData = roomsData[i];
                    break;
                }
            }
            this._onMsgRoomListItemClick(_roomData);
        }
    }

    private _showBuyinUSD(enterGame: Function): void {
        const richText = cv.TP.getRichTextNode();
        let eventHandle = richText.getComponent(ClickEventHandler);
        if (eventHandle == null) {
            eventHandle = richText.addComponent(ClickEventHandler);
        }
        eventHandle.setCallBack(() => {
            cv.TP.hideTipsPanel();
            cv.MessageCenter.send('jumpToHallBank');
            cv.MessageCenter.send('jump_to_conversation_page');
        });

        cv.TP.showRichText(
            cv.config.getStringData('ReminderBuyUSDContent'),
            cv.Enum.ButtonStyle.TWO_BUTTON,
            () => {
                cv.TP.hideTipsPanel();
                enterGame?.();
            },
            () => {},
            false,
            cv.config.getStringData('ReminderBuyUSDTitleForIslot'),
            cc.Label.HorizontalAlign.LEFT,
            false
        );
        cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_ENTER_GAME);
        cv.TP.setTheme(TipsPanelThemeType.TwoButton_Buyin_USD);
    }

    private isMiniHotgame(gameID: number): boolean {
        if (cc.sys.isBrowser) {
            return false;
        }

        switch (gameID) {
            case world_pb.GameId.PocketGames:
                return true;
            default:
                return false;
        }
    }

    private _openPpGame(): void {
        const gameUrl = cv.roomManager.getPpGameUrl();
        if (gameUrl === '') {
            return;
        }
        if (cc.sys.isBrowser) {
            cv.SwitchLoadingView.hide();
            cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                if (cc.sys.os === cc.sys.OS_IOS) {
                    cv.config.gameWindow = window.open(gameUrl, '_blank', 'noopener');
                } else {
                    cv.config.gameWindow = window.open(gameUrl);
                }
            });
        } else if (cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID && !cv.native.isAndroidSimulator()) {
            const openWebView = () => {
                this.scheduleOnce(() => {
                    cv.SwitchLoadingView.hide();
                }, 0.5);

                (<any>window).HMFJSBridge.openWebView(
                    gameUrl,
                    () => {
                        cv.worldNet.PpLeaveRequest(cv.roomManager.getPpEntryName());
                        cv.roomManager.reset();
                    },
                    0,
                    '0F5E4A',
                    0
                );
            };
            openWebView();
        } else {
            cv.action.switchScene(cv.Enum.SCENE.POCKETGAME_SCENE, (scene: cc.Scene) => {
                cv.MessageCenter.send('startSlotGame', cv.Enum.GameId.PP);
            });
        }
    }

    private _openBJGame(): void {
        this.scheduleOnce(() => {
            cv.SwitchLoadingView.hide();
        }, 0.5);

        cv.roomManager.openBJGame();
    }

    private async _openFishGame(): Promise<void> {
        if (cc.sys.os === cc.sys.OS_ANDROID) {
            cv.action.switchScene(cv.Enum.SCENE.FISHINGKING_SCENE);
            return;
        }
        const exitFunc = () => {
            cv.roomManager.reset();
            cv.worldNet.requestFishingKingLogout();
        };

        const gameUrl = cv.roomManager.getFishingKingUrl();
        const isBlocked = await cv.roomManager.checkUrlBefore(gameUrl);
        if (isBlocked){
            exitFunc();
            cv.TP.showMsg(cv.config.getStringData('ExitMinigameDueToError'), cv.Enum.ButtonStyle.GOLD_BUTTON, null);
            cv.SwitchLoadingView.hide();
            return;
        }

        this.scheduleOnce(() => {
            cv.SwitchLoadingView.hide();
        }, 0.5);

        (<any>window).HMFJSBridge.openWebView(gameUrl,
            (ccjs) => {
                const url = ccjs.url;
                console.log("Fish ccjs ------ " + url);
                
                if (url.search("ccjs://back-normal") != -1) {
                    exitFunc();
                    return;
                }

            }, 
            1, '0F5E4A', 0
        );
        
    }
    
    private _showToastGameReady(gameId: number): void {
        if (cv.config.getCurrentScene() !== cv.Enum.SCENE.HALL_SCENE) return;

        cv.TT.showMsg(
            cv.StringTools.formatC(cv.config.getStringData("Minigame_completion_prompt"), cv.config.getStringData(`MiniGame_Title_${gameId}`)),
            cv.Enum.ToastType.ToastTypeDefault
        );
    }

    private onForcedLogout(): void {
        (<any>window).HMFJSBridge.closeWebView();
    }
}
