/* eslint-disable max-classes-per-file */
import cv from "../../../components/lobby/cv";
import { GameId, LANGUAGE_TYPE } from "../../../common/tools/Enum";
import { TableView } from "../../../common/tools/TableView";
import ws_protocol = require("./../../../common/pb/ws_protocol");
import world_pb = ws_protocol.pb;
import { ProgressBarViewControl } from "../../lobby/miniGame/ProgressBarViewControl";
import BundleDownloadControl from "./BundleDownloadControl";

export class MiniGameNewItemData {
    gameid: number = 0;                                         // 游戏id
    online: number = 0;                                         // 在线总人数
    status: number = 0;                                         // 游戏状态(0 维护, 1 正常)
    bHot:boolean = false;                                       // 是否显示hot图标
    label:world_pb.MiniLabel = 0;                               // 小游戏显示标签 0：正常不显示  1： 显示new标签
    mini_hotgame: boolean = false;                              // pocket game require do extra handle if placed on 3rd row    

    // download
    progressControl: ProgressBarViewControl = null;
    onDownloadComplete?: (gameId) => void;
}

export class MiniGameNewItemsInfo {
    items: MiniGameNewItemData[] = [];
}

const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameNewItem extends cc.Component {
    @property({ type: [cc.Node] }) item: cc.Node[] = [];
    @property({ type: [cc.Sprite] }) img_bg: cc.Sprite[] = [];
    @property({ type: [cc.Sprite] }) img_icon: cc.Sprite[] = [];
    @property({ type: [cc.Sprite] }) img_title: cc.Sprite[] = [];
    @property({ type: [cc.Sprite] }) img_hot: cc.Sprite[] = [];
    @property({ type: [cc.Label] }) txt_online: cc.Label[] = [];
    @property({ type: [cc.Sprite] }) img_new: cc.Sprite[] = [];
    @property({ type: [cc.Node] }) node_onlineHead: cc.Node[] = [];
    @property({ type: [cc.Label] }) txt_maintenance: cc.Label[] = [];
    @property(cc.Material) gray_material: cc.Material = null;
    @property({ type: [cc.Node] }) layout: cc.Node[] = [];

    static g_class_name: string = "MiniGameNewItem";
    private _dataRef: MiniGameNewItemsInfo = null;
    private readonly SUPPORT_MULTIPLE_BG_LIST = [70]; // for games may need 2 or more itemBgs
    private readonly SUPPORT_WIDE_TITLE_CH = [1100]; // for games may need 2 or more itemBgs
    private readonly SUPPORT_WIDE_TITLE_EN = [1100, 1000, 6003]; // for games may need 2 or more itemBgs

    protected onLoad(): void {
        cv.resMgr.adaptWidget(this.node, true);
    }

    protected onDestroy(): void {}

    updateSVReuseData(index: number, info: MiniGameNewItemsInfo, view?: TableView): void {
        cc.log("MiniGameNewItem updateSVReuseData", info, index);

        this._dataRef = info;
        const cellType: number = view.getCellType(index);
        const isWide: boolean = cv.native.isWideScreen();
        const prefixBG = isWide ? "hall/miniGame/new/itemBg_w/" : "hall/miniGame/new/itemBg/";

        for (let i = 0; i < this.img_bg.length; ++i) {
            const img_bg: cc.Sprite = this.img_bg[i];
            const img_icon: cc.Sprite = this.img_icon[i];
            const img_title: cc.Sprite = this.img_title[i];
            const img_hot: cc.Sprite = this.img_hot[i];
            const txt_online: cc.Label = this.txt_online[i];
            const img_new: cc.Sprite = this.img_new[i];
            const node_online_head: cc.Node = this.node_onlineHead[i];
            const txt_maintenance: cc.Label = this.txt_maintenance[i];

            img_bg.node.active = i < info.items.length;
            if (!img_bg.node.active) continue;

            const t: MiniGameNewItemData = info.items[i];
            let path_bg: string = `${prefixBG}bg_${t.gameid}`;
            const path_icon: string = `hall/miniGame/new/itemIcon/native/normal_icon_${t.gameid}`;
            let path_title: string = `hall/miniGame/new/itemTitle/normal_title_${t.gameid}`;

            const node = this.item[i];
            if (node) {
                const progressBarViewControl = node.getComponentInChildren(ProgressBarViewControl);
                if (progressBarViewControl) {
                    t.progressControl = progressBarViewControl;
                }
            } else {
                t.progressControl = this.node.getComponentInChildren(ProgressBarViewControl);
            }

            // 有游戏id
            if (t.gameid > 0) {
                if (this._checkSupportMultiBgs(t.gameid) && cellType === 1) {
                    path_bg += "_m";
                }
                if (this._checkSupportWideTitle(t.gameid) && isWide) {
                    path_title += "_w";
                }
                cv.resMgr.setSpriteFrame(img_bg.node, cv.config.getLanguagePath(path_bg, LANGUAGE_TYPE.zh_CN));
                if (this.checkSupportIconForMultiLanguage(t.gameid)) {
                    cv.resMgr.setSpriteFrame(img_icon.node, cv.config.getLanguagePath(path_icon));
                } else {
                    cv.resMgr.setSpriteFrame(img_icon.node, cv.config.getLanguagePath(path_icon, LANGUAGE_TYPE.zh_CN));
                }
                cv.resMgr.setSpriteFrame(img_title.node, cv.config.getLanguagePath(path_title));

                img_bg.getComponent(cc.Button).enabled = true;
                img_hot.node.active = t.bHot;
                img_new.node.active = t.label === world_pb.MiniLabel.MiniLabelNew;

                if (t.status !== 0) {
                    node_online_head.active = true;
                    txt_online.node.active = true;
                    txt_maintenance.node.active = false;
                    txt_online.string = t.online.toString();
                } else {
                    node_online_head.active = false;
                    txt_online.node.active = false;
                    txt_maintenance.node.active = true;
                    txt_maintenance.string = cv.config.getStringData("minigame_new_maintain");
                    const grayMat = cc.Material.getBuiltinMaterial("2d-gray-sprite");
                    img_bg.setMaterial(0, grayMat);
                    img_icon.setMaterial(0, grayMat);
                    img_title.setMaterial(0, grayMat);
                }

                if (this._checkLinesOfTitle(t.gameid) && cellType === 1 && !isWide) {
                    img_title.node.y = 34.5;
                } else {
                    img_title.node.y = 18.5;
                }
            }
            // 无游戏id, 则认为是待开放游戏... // Dummy game id - 0 =NEW GAME .. Coming Soon
            else {
                cv.resMgr.setSpriteFrame(img_bg.node, cv.config.getLanguagePath(path_bg, LANGUAGE_TYPE.zh_CN));
                cv.resMgr.setSpriteFrame(img_icon.node, cv.config.getLanguagePath(path_icon, LANGUAGE_TYPE.zh_CN));
                cv.resMgr.setSpriteFrame(img_title.node, cv.config.getLanguagePath(path_title, LANGUAGE_TYPE.zh_CN));

                img_icon.node.width = 307; // For Dummy game, New Game Icon"s size & position different than common one so using static
                img_icon.node.height = 77;
                img_icon.node.y = 37;
                img_title.node.width = 214;
                img_title.node.height = 49;
                img_title.node.y = -35;
                img_bg.getComponent(cc.Button).enabled = false;
                txt_online.node.active = false;
                img_hot.node.active = false;
                img_new.node.active = false;
            }

            this.initBundleControl(i, t);
        }

        this._updateWidgetByCellType(cellType);
    }

    private initBundleControl(index: number, data: MiniGameNewItemData): void {
        const node = this.item[index];
        const bundleDownloadControl =
            node.getComponent(BundleDownloadControl) || node.addComponent(BundleDownloadControl);

        bundleDownloadControl.init(data.gameid);
        bundleDownloadControl.setProgressBar(data.progressControl);
        bundleDownloadControl.setCompleteCallback(() => {
            this.layout[index].active = true;
            data.onDownloadComplete?.(data.gameid);
        });
        bundleDownloadControl.setFailedCallback(() => {
            this.layout[index].active = true;
            cv.MessageCenter.send("openBundleDownloadDialog", () => {
                this.layout[index].active = false;
                bundleDownloadControl.download();
            });
        });

        if (bundleDownloadControl.isQueuing) {
            bundleDownloadControl.prepareDownloadUI();
        }

        if (bundleDownloadControl.isUpdating || bundleDownloadControl.isQueuing) {
            this.layout[index].active = false;
        }
    }

    onClickItem(event: cc.Event, index: number): void {
        cv.AudioMgr.playButtonSound("button_click");
        if (!this._dataRef) return;
        if (index < 0 || index >= this._dataRef.items.length) return;

        const miniGameData: MiniGameNewItemData = this._dataRef.items[index];
        const node = this.item[index];
        const bundleDownloadControl = node.getComponent(BundleDownloadControl);
        if (bundleDownloadControl && bundleDownloadControl.isBundle) {
            if (bundleDownloadControl.isUpdating || bundleDownloadControl.isQueuing) {
                return;
            }

            if (!bundleDownloadControl.isBundleValid) {
                cv.MessageCenter.send("openBundleDownloadDialog");
                cc.error("Bundle info is invalid.");
                return;
            }

            if (bundleDownloadControl.shouldDownloadBundle()) {
                this.layout[index].active = false;
                bundleDownloadControl.download();
            } else {
                cv.MessageCenter.send(`${MiniGameNewItem.g_class_name}_click`, miniGameData);
            }
        } else {
            cv.MessageCenter.send(`${MiniGameNewItem.g_class_name}_click`, miniGameData);
        }
    }

    private _updateWidgetByCellType(cellType: number): void {
        switch (cellType) {
            case 0:
                break;
            case 1: {
                const padding = 18;
                const width = this.node.width / 2;
                this.item[0].getComponent(cc.Widget).left = 0;
                this.item[0].getComponent(cc.Widget).right = width + padding;
                this.item[1].getComponent(cc.Widget).left = width + padding;
                this.item[1].getComponent(cc.Widget).right = 0;
                cv.resMgr.adaptWidget(this.item[0], true);
                cv.resMgr.adaptWidget(this.item[1], true);
                break;
            }
            case 2: {
                const padding = 15;
                const width = this.node.width / 3;
                this.item[0].getComponent(cc.Widget).left = 0;
                this.item[0].getComponent(cc.Widget).right = width * 2 + padding;
                this.item[1].getComponent(cc.Widget).left = width + padding;
                this.item[1].getComponent(cc.Widget).right = width + padding;
                this.item[2].getComponent(cc.Widget).left = width * 2 + padding;
                this.item[2].getComponent(cc.Widget).right = 0;

                cv.resMgr.adaptWidget(this.item[0], true);
                cv.resMgr.adaptWidget(this.item[1], true);
                cv.resMgr.adaptWidget(this.item[2], true);
                break;
            }
        }
    }

    private checkSupportIconForMultiLanguage(gameId: number): boolean {
        const array = [6100];
        return array.indexOf(gameId) >= 0;
    }

    private _checkLinesOfTitle(gameId: number): boolean {
        const lang = cv.config.getCurrentLanguage();
        const array = lang === LANGUAGE_TYPE.zh_CN ? [1100] : [1000, 1100, 6003];
        return array.indexOf(gameId) >= 0;
    }

    private _checkSupportMultiBgs(gameId: number): boolean {
        return this.SUPPORT_MULTIPLE_BG_LIST.indexOf(gameId) >= 0;
    }

    private _checkSupportWideTitle(gameId: number): boolean {
        const lang = cv.config.getCurrentLanguage();
        if (lang === LANGUAGE_TYPE.zh_CN) {
            return this.SUPPORT_WIDE_TITLE_CH.indexOf(gameId) >= 0;
        }
        return this.SUPPORT_WIDE_TITLE_EN.indexOf(gameId) >= 0;
    }
}
