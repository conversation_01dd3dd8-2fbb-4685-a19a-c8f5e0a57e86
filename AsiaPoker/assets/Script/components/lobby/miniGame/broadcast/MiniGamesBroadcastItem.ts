import ws_protocol = require('./../../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import cv from '../../cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class MiniGamesBroadcastItem extends cc.Component {
    @property(cc.Node)
    private avatarNode: cc.Node = null;

    @property(cc.Sprite)
    private avatar: cc.Sprite = null;

    @property(cc.Node)
    private content: cc.Node = null;

    @property(cc.Node)
    private detail: cc.Node = null;

    @property(cc.RichText)
    private label1: cc.RichText = null;

    @property(cc.Sprite)
    private gameIcon: cc.Sprite = null;

    @property(cc.RichText)
    private label2: cc.RichText = null;

    @property([cc.SpriteFrame])
    private gameIconFrames: cc.SpriteFrame[] = [];

    private _notifyData = null;

    private readonly AVARTAR_SPACING: number = 24;

    protected onLoad(): void {
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this._onLanguageChange.bind(this), this.node);
    }

    private _onLanguageChange(): void {
        if (this._notifyData) {
            this.setBroadcastItem(this._notifyData);
        }
    }

    public setBroadcastItem(data: world_pb.IMiniGameAtmosphereMessage, callback : Function = null): void {
        this._notifyData = data;
        // cc.warn('setBroadcastItem', data);

        this.detail.position = cc.v2(0, 0);
        const params = data.params;

       this.gameIcon.node.active = false;

        const template = cv.config.getStringData('minigame_notification_template_' + data.template);
        const gameName = cv.config.getStringData('MiniGame_Title_' + params.game_id);

        let target = "";
        if (params.target) {
            target = cv.config.getStringData('minigame_notification_target_' + data.params.game_id + "_" + params.target.toString());
        }

        let currencyType = "";
        if (params.currency_type) {
            currencyType = cv.config.getStringData('minigame_currency_type_' + params.currency_type.toString());
        }
        // game_name : `{"language_list": [{"language_msg": "ppTesty 測試", "language_name": "zh_CN"}, {"language_msg": "ppTesty en", "language_name": "en_US"}, {"language_msg": "ppTesty vn", "language_name": "yn_TH"}, {"language_msg": "ppTesty tai", "language_name": "th_PH"}, {"language_msg": "ppTesty IND", "language_name": "hi_IN"}]}`
        let game_name = "";
        if (params.game_name) {
            const game_names = JSON.parse(params.game_name);
            game_name = game_names.language_list.find((language) => language.language_name === cv.config.getCurrentLanguage())?.language_msg;
            if (!game_name) {
                game_name = game_names.language_list.find((language) => language.language_name === 'en_US')?.language_msg;
            }
        }

        let league_name = "";
        if (params.league_name) {
            const league_names = JSON.parse(params.league_name);
            league_name = league_names[cv.config.getCurrentLanguage()];
            if (!league_name) {
            league_name = league_names.en_US;
            }
        }

        let sports_odds_name = "";
        if (params.sports_odds_name) {
            const sports_odds_names = JSON.parse(params.sports_odds_name);
            sports_odds_name = sports_odds_names[cv.config.getCurrentLanguage()];
            if (!sports_odds_name) {
            sports_odds_name = sports_odds_names.en_US;
            }
        }

        // "{"en_US":"Hubei Istar vs Shandong Taishan B","zh_CN":"湖北青年星 vs 山东泰山B队"}"  

        let team_names = "";
        if (params.team_names) {
            const team_name = JSON.parse(params.team_names);
            team_names = team_name[cv.config.getCurrentLanguage()];
            if (!team_names) {
                team_names = team_name.en_US;
            }
        }

        const formattedMessage = template
            .replace('{player_name}', this._convertToRichText(params.player_name))
            .replace('{game_id}', this._convertToRichText(gameName))
            .replace('{currency_amount}', this._convertToRichText(cv.config.getShortOwnCoinString(params.currency_amount, true)))
            .replace('{win_count}', this._convertToRichText(params.win_count.toString()))
            .replace('{target}', this._convertToRichText(target))
            .replace('{multiplier}', this._convertToRichText(params.multiplier.toString()))
            .replace('{loss}', this._convertToRichText(cv.config.getShortOwnCoinString(params.loss, true)))
            .replace('{count}', this._convertToRichText(params.win_count.toString()))
            .replace('{league_name}', this._convertToRichText(league_name))
            .replace('{team_names}', this._convertToRichText(team_names))
            .replace('{currency_type}', this._convertToRichText(currencyType))
            .replace('{game_name}', this._convertToRichText(game_name))
            .replace('{sports_odds_name}',this._convertToRichText(sports_odds_name))
            .replace('{sports_bets_multi_m}',this._convertToRichText(params.sports_bets_multi_m))
            .replace('{sports_bets_multi_n}',this._convertToRichText(params.sports_bets_multi_n));


        const message = formattedMessage.split('~');
        this.label1.string = message[0];
        this.label2.string = '';
        if (message.length > 1){
            this.label2.string = message[1];
            this._setGameIcon(params.game_id);
        }

        // set avatar
        if (!!params.player_id){
            this._setPlayerAvatar(params.player_head, params.player_plat, callback);
        }
        else{
            this._setAvatarEnabled(false)
        }
    }

    private _convertToRichText(param: any): string {
        return `<color=#EA4E4E>${param.toString()}</c>`;
    }


    private _setGameIcon(gameId: number): void {

        // game id
        // 10 cowboy
        // 30 human boy
        // 70 poker master
        // 1000 sport
        // 1010 PG
        // 1030 PP
        // 1021 blackjack
        // cc.warn('setGameIcon', 'game_' + gameId);
        const frame: cc.SpriteFrame = this.gameIconFrames.find((frame) => frame.name === 'game_' + gameId);
        if (frame) {
            this.gameIcon.node.active = true;
            this.gameIcon.spriteFrame = frame;
        }
    }

    public startTween(onCompleted: Function): void {
        this.detail.position = cc.v2(0, 0);
        const d = this.detail.width - this.content.width;
        if (d > 0) {
            // tween detail to left
            const time = d * 0.01;
            cc.tween(this.detail)
                .delay(1.5)
                .to(time, { position: cc.v2(-d, 0) })
                .delay(1.5)
                .call(() => {
                    onCompleted();
                })
                .start();
        } else {
            // delay 3.5s then complete + tween up time
            cc.tween(this.detail)
                .delay(3.5)
                .call(() => {
                    onCompleted();
                })
                .start();
        }
    }

    public stopTween(): void {
        cc.Tween.stopAllByTarget(this.detail);
    }

    private _setPlayerAvatar(url: string, plat: number, callback : Function = null) {
        const temp = url.lastIndexOf('/');
        const tempStr = url.slice(temp + 1);

        if(url === '') {
            this._setAvatarEnabled(false);
        } else {
            this._setAvatarEnabled(true);
        }
        
        if (cv.StringTools.isNumber(tempStr)) {
            let tempNum = cv.Number(tempStr);
            if (tempNum <= 0 || tempNum > cv.config.HEAD_LENGTH) {
                tempNum = 1;
            }
            const head = 'zh_CN/common/head/head_' + tempNum;
            // cc.warn('setPlayerAvatar', url);
            const self = this;
            cc.resources.load(head, cc.SpriteFrame, (err, frame: cc.SpriteFrame) => {
                if (err) {
                    cc.error(err.message || err);
                    return;
                }
                self._setAvatarSpriteFrame(frame);
            });
            if (callback) {
                callback();
            }
            return;
        }

        const  head_url = cv.dataHandler.getUserData().getImageUrlByPlat(url, plat);
        const texture: cc.Texture2D = cv.resMgr.get(head_url, cc.Texture2D);
        if (texture) {
            this._setAvatarSpriteFrame(texture);
            if (callback) {
                callback();
            }
        } else {
            this._updateSpriteByUrl(head_url, callback);
        }
    }

    private _setAvatarSpriteFrame(asset: cc.SpriteFrame | cc.Texture2D) {
      if (cv.tools.isValidNode(asset) && cv.tools.isValidNode(this.avatar)) {
        if (asset instanceof cc.SpriteFrame) {
          this.avatar.spriteFrame = asset;
        }
        else if (asset instanceof cc.Texture2D) {
          this.avatar.spriteFrame = new cc.SpriteFrame(asset);
        }
      }
    }

    private _updateSpriteByUrl(url: string, callback : Function = null) {
        const name: string = url.substr(url.lastIndexOf('/') + 1);
        // console.log("updateSpriteByUrl ==> name :" + name);
        if (name !== '') {
            this._downloadImage(url, callback);
        } 
    }

    private _downloadImage(url: string, callback : Function = null) {
        cv.resMgr.loadRemote(url, (error: Error, texture: cc.Texture2D): void => {
            if (callback) {
                callback();
            }
            if (error) {
                console.log(error.message || error);
                return;
            }
            this._setAvatarSpriteFrame(texture);
        });
    }

    private _setAvatarEnabled(enabled: boolean): void {
        const layout = this.node.getComponent(cc.Layout);
        this.avatarNode.active = enabled;
        if (enabled) {
            layout.spacingX = this.AVARTAR_SPACING;
            this.content.width = this.node.parent.width - this.avatarNode.width - layout.spacingX;
        }
        else {
            layout.spacingX = 0;
            this.content.width = this.node.parent.width;
        }
    }

    protected onDestroy(): void {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
    }
}
