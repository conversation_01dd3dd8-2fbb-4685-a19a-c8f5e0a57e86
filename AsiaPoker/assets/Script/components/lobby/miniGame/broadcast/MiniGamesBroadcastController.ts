// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html
import ws_protocol = require('./../../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import cv from '../../cv';
import MiniGamesBroadcastItem from './MiniGamesBroadcastItem';

const { ccclass, property } = cc._decorator;

const MAX_QUEUE_SIZE = 50;

@ccclass
export default class MiniGamesBroadcastController extends cc.Component {

    @property([MiniGamesBroadcastItem])
    private broadcastItems: MiniGamesBroadcastItem[] = [];

    @property(cc.Widget)
    scrollViewWidget: cc.Widget = null;

    @property(cc.Node) minigameBlocker: cc.Node = null;
    @property(cc.ScrollView) minigameScrollView: cc.ScrollView = null;
    
    private _fetchBroadcastInterval: NodeJS.Timeout = null;;
    private _currentIndex : number = 0;
    private _nextIndex : number  = 1;
    private _isBoardCasting : boolean = false;
    private _isFirst : boolean = true;

    private readonly MB_STORAGE_PLAYED_SET_KEY = 'PKW_MINIGAME_BROADCAST_PLAYED_SET';

    private _broadcastQueue: world_pb.IMiniGameAtmosphereMessage[] = [];
    private _originBroadCast: world_pb.IMiniGameAtmosphereMessage[] = [];
    private BLOCKER_HEIGHT: number = 80;
    private _currentQueueIndex: number = 0;
    private _playedNotiSet: Set<string> = new Set();

    protected onEnable(): void {
        cv.MessageCenter.register(
            'MiniGamesAtmosphereMessagesResponse',
            this._onMsgUpdateMiniGamesAtmosphere.bind(this),
            this.node
        );

        cv.MessageCenter.register(
            'updateMiniGamesAtmosphere',
            this._onMsgUpdateMiniGamesAtmosphereNotice.bind(this),
            this.node
        );

        this._fetchBroadcastData();
        this._initDynamicBlocker();
        
    }

    private _fetchBroadcastData(): void {
        cv.worldNet.requestMinigamesAtmosphere();
    }

    private _onMsgUpdateMiniGamesAtmosphere(data: world_pb.MiniGamesAtmosphereMessagesResponse): void {
        // cc.warn('MiniGamesBroadcastController _onMsgUpdateMiniGamesAtmosphere', data);

        //update this._broadcastQueue
        this._originBroadCast = data.messages;
        this._processBroadcastQueue(data.messages);
        if (this._broadcastQueue.length === 0 && this._originBroadCast.length > 0) {
            // This case is player close the app while all messages are played but haven't clear cache yet
            this._clearPlayedNotiSet();
            this._processBroadcastQueue(this._originBroadCast);
        }
        this._currentQueueIndex = this._findQueueIndex();
        this._notifyNextBroadcast();
    }

    private _onMsgUpdateMiniGamesAtmosphereNotice(data: world_pb.MiniGamesAtmosphereNotice): void {
        // cc.warn('MiniGamesBroadcastController _onMsgUpdateMiniGamesAtmosphereNotice', data);

        this._originBroadCast.push(data.message);
        this._processBroadcastQueue( this._originBroadCast);

        this._currentQueueIndex = this._findQueueIndex();
        this._notifyNextBroadcast();
    }
 
    private _processBroadcastQueue(oriMsgs:world_pb.IMiniGameAtmosphereMessage[]) {

        // store unplayed msg
        const tmpNewMsgs:world_pb.IMiniGameAtmosphereMessage[] = [];

        // store non-duplicated msg from backend
        const msgs:world_pb.IMiniGameAtmosphereMessage[] = [];

        const msgSet: Set<string> = new Set();
        msgSet.clear();

        // filter duplicated msg from backend
        for(let i=0; i<oriMsgs.length; ++i) {
            const msgMd5 = this._getChecksum(oriMsgs[i]);
            if(!msgSet.has(msgMd5)) {
                msgSet.add(msgMd5);
                msgs.push(oriMsgs[i]);
            }
        }

        const queueNotiSet: Set<string> = new Set();
        queueNotiSet.clear();

        this._playedNotiSet = cv.tools.getSetFromLocalStorage(this.MB_STORAGE_PLAYED_SET_KEY);

        for (let i = msgs.length - 1 ; i >= 0 ; --i) {

            if(tmpNewMsgs.length >= MAX_QUEUE_SIZE) {
                break;
            }

           const msgMd5 = this._getChecksum(msgs[i]);

            if(!this._playedNotiSet.has(msgMd5)) {
                queueNotiSet.add(msgMd5);
                tmpNewMsgs.push(msgs[i]);
            }
        }

        // assume msgs = [m1,m2,...,m50], then tmpNewMsgs could be  [m50, m49, m48, m41,... ]
        for(let i=0; i < this._broadcastQueue.length; ++i) {

            if(tmpNewMsgs.length >= MAX_QUEUE_SIZE) {
                break;
            }
            const msgMd5 = this._getChecksum( this._broadcastQueue[i] ); 
            if(!queueNotiSet.has(msgMd5)) {
                tmpNewMsgs.push(this._broadcastQueue[i]);
                queueNotiSet.add(msgMd5)
            }
        }

        this._broadcastQueue = tmpNewMsgs;

    }

    private _findQueueIndex():number {
        this._playedNotiSet = cv.tools.getSetFromLocalStorage(this.MB_STORAGE_PLAYED_SET_KEY);
        for(let idx=0 ; idx <this._broadcastQueue.length; ++idx ) {
            const noti: world_pb.IMiniGameAtmosphereMessage = this._broadcastQueue[idx];
            if(!this._playedNotiSet.has(this._getChecksum(noti))) {
                return idx;
            }
        }

        this._clearPlayedNotiSet();
        return 0;
    }

    private _getNotiFromQueue(): world_pb.IMiniGameAtmosphereMessage {
        this._playedNotiSet = cv.tools.getSetFromLocalStorage(this.MB_STORAGE_PLAYED_SET_KEY);
        for(let idx=0 ; idx <this._broadcastQueue.length; ++idx ) {
            const noti: world_pb.IMiniGameAtmosphereMessage = this._broadcastQueue[this._currentQueueIndex];
            this._currentQueueIndex = (this._currentQueueIndex + 1) % this._broadcastQueue.length;
            if(!this._playedNotiSet.has(this._getChecksum(noti))) {
                return noti;
            }
        }
     
        this._clearPlayedNotiSet();
        this._processBroadcastQueue(this._originBroadCast);
        this._currentQueueIndex = 0;
        const noti = this._broadcastQueue[this._currentQueueIndex];
        this._currentQueueIndex = (this._currentQueueIndex + 1) % this._broadcastQueue.length;
        return noti;
    }

    private _notifyNextBroadcast(): void {
        
        if (this._isBoardCasting) {
            return;
        }
        
        if (this._broadcastQueue.length === 0) {
            cc.tween(this.broadcastItems[this._currentIndex].node)
            .to(0.2, { position: cc.v2(0, 70), opacity: 0 })
            .start();
            return;
        }
        
        this._isBoardCasting = true;
        
        cc.Tween.stopAllByTarget(this.broadcastItems[this._currentIndex].node);
        cc.Tween.stopAllByTarget(this.broadcastItems[this._nextIndex].node);
        this.broadcastItems[this._nextIndex].stopTween();
        this.broadcastItems[this._currentIndex].stopTween();

        const noti = this._getNotiFromQueue();
     
        this._updatePlayedNotiSet(noti);

        const self = this;
        if (this._isFirst) {
            this._isFirst = false;
            this.broadcastItems[this._currentIndex].setBroadcastItem(noti);
            this.broadcastItems[this._nextIndex].setBroadcastItem(this._broadcastQueue[0]);
            cc.tween(this.scrollViewWidget)
                .to(
                    0.25,
                    { top: 320 },
                    {
                        progress: (start, end, current, ratio) => {
                            self.scrollViewWidget.updateAlignment();
                            self.minigameScrollView.scrollToTop(0.02);
                            return start + (end - start) * ratio;
                        }
                    }
                )
                .call(() => {
                    // self.scrollViewContent.position = cc.v2(0, 889);
                    this.minigameBlocker.active = true;
                    this.minigameBlocker.height = this.BLOCKER_HEIGHT;
                    this.broadcastItems[this._currentIndex].node.active = true;
                    this.broadcastItems[this._currentIndex].startTween(() => {
                        // cc.warn('MiniGameNew first broadcast completed');
                        self._isBoardCasting = false;
                        self._notifyNextBroadcast();
                    });
                })
                .start();
            return;
        }
        

        this.broadcastItems[this._nextIndex].node.setPosition(cc.v2(0, -70));

        this.broadcastItems[this._nextIndex].setBroadcastItem(noti);

        cc.tween(this.broadcastItems[this._currentIndex].node)
            .to(0.2, { position: cc.v2(0, 70), opacity: 0 })
            .call(() => {
                this.broadcastItems[this._currentIndex].setBroadcastItem(this._broadcastQueue[0]);
            })
            .start();

        cc.tween(this.broadcastItems[this._nextIndex].node)
            .to(0.2, { position: cc.v2(0, 0), opacity: 255 })
            .call(() => {
                this.broadcastItems[this._nextIndex].startTween(() => {
                    // cc.warn('MiniGameNew next broadcast completed');
                    self._isBoardCasting = false;
                    self._notifyNextBroadcast();
                });

                this._currentIndex = this._nextIndex;
                this._nextIndex = (this._nextIndex + 1) % this.broadcastItems.length;
            })
            .start();
    }

    private _initDynamicBlocker(): void {
        if(!this.minigameScrollView) return;
        this.minigameBlocker.active = false;
    }

    protected onDisable(): void {
        cv.MessageCenter.unregister('MiniGamesAtmosphereMessagesResponse', this.node);
        cv.MessageCenter.unregister('updateMiniGamesAtmosphere', this.node);
    }

    protected onDestroy(): void {
        clearInterval(this._fetchBroadcastInterval);
    }

    private _getChecksum(noti: world_pb.IMiniGameAtmosphereMessage){
        const content = JSON.stringify(noti);
        return cv.md5.md5(content);
    }

    private _updatePlayedNotiSet(noti: world_pb.IMiniGameAtmosphereMessage) {

        if(this._playedNotiSet.size >= MAX_QUEUE_SIZE * 2) {
            const oldestElement = this._playedNotiSet.values().next().value;
            this._playedNotiSet.delete(oldestElement);
        }

        this._playedNotiSet.add(this._getChecksum(noti));
        cv.tools.SaveStringByCCFile(this.MB_STORAGE_PLAYED_SET_KEY, JSON.stringify(Array.from(this._playedNotiSet)) );
    }

    private _clearPlayedNotiSet() {
        this._playedNotiSet.clear();
        cv.tools.SaveStringByCCFile(this.MB_STORAGE_PLAYED_SET_KEY, JSON.stringify(Array.from(this._playedNotiSet)) );
    }

}
