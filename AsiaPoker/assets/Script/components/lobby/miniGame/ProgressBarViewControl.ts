import { ResourceManager } from '../../../common/tools/ResourceManager';
import cv from '../../../components/lobby/cv';

const { ccclass, property } = cc._decorator;
@ccclass
export class ProgressBarViewControl extends cc.Component {
    @property(cc.ProgressBar) progressBar: cc.ProgressBar = null;
    @property(cc.Label) barText: cc.Label = null;
    @property(cc.Node) loadingPanel: cc.Node = null;

    private _show = false;

    protected onLoad(): void {
        const resMgr: ResourceManager = ResourceManager.getInstance();
        resMgr.adaptWidget(this.node);
        resMgr.adaptWidget(this.loadingPanel);
        resMgr.adaptWidget(this.progressBar.node);
        resMgr.adaptWidget(this.progressBar.barSprite.node);
        this.progressBar.progress = 0;
        this.progressBar.totalLength = this.progressBar.node.width;
        this.progressBar.barSprite.node.width = 0;
        this.node.active = false;
        this.progressBar.barSprite.node.active = false;
        this._show = false;
    }

    show = () => {
        if (!this._show) {
            this.node.active = true;
            this.progressBar.barSprite.node.active = true;
            this._show = true;
        }
    };

    hide = () => {
        if (this._show) {
            this.node.active = false;
            this.progressBar.barSprite.node.active = false;
            this._show = false;
        }
    };

    onLoadingProgress(percentage: number): void {
        const progress = Math.trunc(100 * percentage);

        this.barText.string = cv.config.getStringData('download_minigame') + progress.toString() + '%';
        this.progressBar.progress = percentage;
    }

}
