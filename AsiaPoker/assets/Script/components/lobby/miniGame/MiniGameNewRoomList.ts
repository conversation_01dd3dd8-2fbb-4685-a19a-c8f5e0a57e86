import ws_protocol = require("./../../../common/pb/ws_protocol");
import world_pb = ws_protocol.pb;
import cv from "../../../components/lobby/cv";
import { TableView } from "../../../common/tools/TableView";
import { MiniGameNewRoomListItem } from "./MiniGameNewRoomListItem";
import { MiniGameNewRoomListItemEuropean } from "./MiniGameNewRoomListItemEuropean";
import { BlackjackRoomListItem } from "./BlackjackRoomListItem";
import { MiniGameListItemCaribbean } from "./MiniGameListItemCaribbean";
import { MiniGameListItemWealthTrio } from "./MiniGameListItemWealthTrio";
import { MiniGameListItemBitMaster } from "./MiniGameListItemBitMaster";

const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameNewRoomList extends cc.Component {
    @property(cc.Node) img_bg: cc.Node = null;
    @property(cc.Node) img_fade: cc.Node = null;
    @property(cc.Node) img_shield: cc.Node = null;
    
    // blackjack stage2 panel properties
    @property(cc.Label) lbTxtback: cc.Label = null;     
    @property(cc.Label) lbTxtOnePeople: cc.Label = null;     
    @property(cc.Label) lbTxtThreePeople: cc.Label = null;     
    @property(cc.Label) lbTxtQuickGame: cc.Label = null;   
    @property(cc.Sprite) btnSingle: cc.Sprite = null;
    @property(cc.Sprite) btnThree: cc.Sprite = null;
    @property(cc.SpriteFrame) picSpriteDef: cc.SpriteFrame = null;
    @property(cc.Node) onepeopleContent : cc.Node = null;
    @property(cc.Node) multipeopleContent : cc.Node = null;
    @property(cc.Node) panelCom: cc.Node = null;
    @property(cc.Node) panelCaribbean: cc.Node = null;
    @property(cc.Node) panelWealthTrio: cc.Node = null;
    @property(cc.Node) panelBitMaster: cc.Node = null;
    @property(cc.Node) panelBlackjack1: cc.Node = null;
    @property(cc.Node) panelBlackjack2: cc.Node = null;
    @property(cc.Node) panelEuro: cc.Node = null;
    private _tableView_onePeopleMode: TableView = null;
    private _tableView_threePeopleMode: TableView = null;


    static g_class_name: string = "MiniGameNewRoomList";                                    // 类名
    public static g_instance: MiniGameNewRoomList = null;                                  // 伪单例
    public gameType  :number = null;
    private _bj_levelId_1Player  :number = null;
    private _bj_levelId_3Player  :number = null;
    private _bj_isSingleMode : boolean = false;
    private _panel: cc.Node = null;
    private _panel_blackjack: cc.Node = null;
    private _panel_src_pos: cc.Vec2 = cc.Vec2.ZERO;                                         // 主面板原位置
    private _panel_src_widget_top: number = 0;                                              // 主面板原顶部对齐值
    private _blackjack_panel_src_widget_top: number = 0;                                              // 主面板原顶部对齐值

    private _img_title: cc.Node = null;
    private _img_title_blackjack2: cc.Node = null;
    private _btn_close: cc.Node = null;
    private _btn_close2: cc.Node = null;
    private _tableView: TableView = null;
   

    private _isFadingIn: boolean = false;
    private _isFadingOut: boolean = false;
    private _isScrollEnd: boolean = false;

    /**
     * 静态初始化实例(会"add"到对应父节点且隐藏, 且只生效一次)
     * @brief 若调用层不采用该实例化方法, 也可自行维护
     * @param prefab        该预制件引用
     * @param parentNode    父节点(缺省时默认当前场景节点)
     * @param zorder        节点内部Z序(缺省时默认枚举)
     * @param pos           该节点实例化后的位置(缺省时默认居中)
     */
    static initSingleInst(prefab: cc.Prefab, parentNode?: cc.Node, zorder?: number, pos?: cc.Vec2): MiniGameNewRoomList {
        if (!(prefab instanceof cc.Prefab)) return null;

        if (!MiniGameNewRoomList.g_instance || !cc.isValid(MiniGameNewRoomList.g_instance)) {
            parentNode = parentNode || cc.director.getScene();
            zorder = zorder || 0;

            let inst: cc.Node = cc.instantiate(prefab);
            MiniGameNewRoomList.g_instance = inst.getComponent(MiniGameNewRoomList);
            if (MiniGameNewRoomList.g_instance) {
                const v2_size: cc.Vec2 = cc.v2(inst.width, inst.height);
                const v2_scale: cc.Vec2 = cc.v2(inst.scaleX, inst.scaleY);
                pos = pos || (inst.getAnchorPoint().sub(parentNode.getAnchorPoint())).scaleSelf(v2_size).scaleSelf(v2_scale);
                inst.setPosition(pos);
                inst.active = false;
                parentNode.addChild(inst, zorder);
            }
            else {
                inst.destroy();
                inst = null;
            }
        }

        return MiniGameNewRoomList.g_instance;
    }

     /**
     * 自动显示"常规游戏"
     * @param datas 
     * @param isAnim 
     */
     autoShowCaribbean(datas: world_pb.MiniGame[], isAnim: boolean = true): void {
        this._init(world_pb.GameId.CaribbeanTexasHold)
        this.node.active = true;

        this._adaptPanel(datas.length, 216);
        // 计算视图大小后保存最新的视图原始位置
        this._panel_src_pos = cc.v2(this._panel.position);

        this._img_title.getComponent(cc.Label).string = cv.config.getStringData("Caribbean_Holdem");
        
        // 更新列表
        const objs: any[] = [];
        objs.push({ prefab_type: 0, prefab_component: MiniGameListItemCaribbean, prefab_datas: datas });
        this._tableView.bindData(objs);
        this._tableView.reloadView();

        // 动画
        if (isAnim) this._autoAnimFunc(true, isAnim);
    }

    autoShowWealthTrio(datas: world_pb.MiniGame[], isAnim: boolean = true): void {
        this._init(world_pb.GameId.WealthTrio)
        this.node.active = true;

        this._adaptPanel(datas.length, 216);
        // 计算视图大小后保存最新的视图原始位置
        this._panel_src_pos = cc.v2(this._panel.position);

        this._img_title.getComponent(cc.Label).string = cv.config.getStringData("Wealth_Trio");
        
        // 更新列表
        const objs: any[] = [];
        objs.push({ prefab_type: 0, prefab_component: MiniGameListItemWealthTrio, prefab_datas: datas });
        this._tableView.bindData(objs);
        this._tableView.reloadView();

        // 动画
        if (isAnim) this._autoAnimFunc(true, isAnim);
    }

    autoShowBitMaster(datas: world_pb.MiniGame[], isAnim: boolean = true): void {
        this._init(world_pb.GameId.BitMasterGoldCoin)
        this.node.active = true;

        this._adaptPanel(datas.length, 216);
        // 计算视图大小后保存最新的视图原始位置
        this._panel_src_pos = cc.v2(this._panel.position);

        this._img_title.getComponent(cc.Label).string = cv.config.getStringData("bitMaster");
        
        // 更新列表
        const objs: any[] = [];
        objs.push({ prefab_type: 0, prefab_component: MiniGameListItemBitMaster, prefab_datas: datas });
        this._tableView.bindData(objs);
        this._tableView.reloadView();

        // 动画
        if (isAnim) this._autoAnimFunc(true, isAnim);
    }

    /**
     * 自动显示"常规游戏"
     * @param datas 
     * @param isAnim 
     */
    autoShowCom(datas: world_pb.MiniGame[], isAnim: boolean = true): void {
        this._init(datas[0].sourceType === world_pb.GameId.BlackJack?world_pb.GameId.BlackJack:world_pb.GameId.GameId_Dummy)
        this.node.active = true;

        this._adaptPanel(datas.length, 216)
        // 计算视图大小后保存最新的视图原始位置
        this._panel_src_pos = cc.v2(this._panel.position);

        // 设置标题
        this._img_title.getComponent(cc.Label).string = cv.config.getStringData(`MiniGame_Title_${datas[0].sourceType}`);

        // 更新列表
        const objs: any[] = [];
        objs.push({ prefab_type: 0, prefab_component: MiniGameNewRoomListItem, prefab_datas: datas });
        this._tableView.bindData(objs);
        this._tableView.reloadView();

        // 动画
        if (isAnim) this._autoAnimFunc(true, isAnim);
    }

    /**
     * 自动显示"一起看球"
     * @param data 
     * @param isAnim 
     */
    autoShowEuropean(data: world_pb.MiniGame, isAnim: boolean = true): void {
        this._init(data.sourceType);
        this.node.active = true;

        // 解析"json"数据
        let topMatches: any[] = [];
        try {
            topMatches = JSON.parse(data.topMatches);
        }
        catch (error) {
            topMatches = [];
            console.error(`${MiniGameNewRoomList.g_class_name}: error, parse gameid=${data.deskType} json data failed - ${error}!`);
        }
        finally {
        }
        
        this._adaptPanel(topMatches.length, 216);
        // 计算视图大小后保存最新的视图原始位置
        this._panel_src_pos = cc.v2(this._panel.position);

        // 设置标题
        this._img_title.getComponent(cc.Label).string = cv.config.getStringData(`MiniGame_Title_${data.sourceType}`);

        // 更新列表
        const objs: any[] = [];
        objs.push({ prefab_type: 0, prefab_component: MiniGameNewRoomListItemEuropean, prefab_datas: topMatches });
        this._tableView.bindData(objs);
        this._tableView.reloadView();

        // 动画
        if (isAnim) this._autoAnimFunc(true, isAnim);
    }

    /**
     * 自动隐藏
     * @param isAnim 
     * @param isDestory 
     */
    autoHide(isAnim: boolean = true, isDestory: boolean = true): void {
        this._autoAnimFunc(false, isAnim, () => {
            this.img_fade.stopAllActions();
            this.img_fade.opacity = 0xFF;
            this.img_fade.active = false;

            this._tableView.clearView();
            if (isDestory) this.node.destroy();
        });
    }
    
     /**
     * 自动显示"常规游戏"
     * @param datas 
     * @param isAnim 
     */
      autoShowBlackJack(datas: world_pb.blackjackRoomInfo[], tableView : TableView, isAnim: boolean = true): void {
        this.node.active = true;

        // 适配滚动视图大小
        const prefab: cc.Prefab = tableView.prefabTypes[0];
        const offset_h: number = this._getViewOffsetHeight(prefab,4, 216);
        const panel_widget: cc.Widget = this._panel_blackjack.getComponent(cc.Widget);
        panel_widget.top = this._blackjack_panel_src_widget_top + offset_h;
        cv.resMgr.adaptWidget(this.node, true);

        // // 计算视图大小后保存最新的视图原始位置
       //  this._panel_src_pos = cc.v2(this._panel_blackjack.position);

        // 设置标题
        this._img_title.getComponent(cc.Label).string = cv.config.getStringData(`MiniGame_Title_1021`);
        if(this._img_title_blackjack2){
            this._img_title_blackjack2.getComponent(cc.Label).string = cv.config.getStringData(`MiniGame_Title_1021`);
        }
        // 更新列表
        const objs: any[] = [];
        objs.push({ prefab_type: 0, prefab_component: BlackjackRoomListItem, prefab_datas: datas });
        tableView.bindData(objs);
        tableView.reloadView();

        // 动画
        if (isAnim) this._autoAnimFunc(true, isAnim);
    }

 
  clickBlackBack() {
    cv.AudioMgr.playButtonSound("button_click");
    this._panel_blackjack.active = false;
    this._panel.active = true;

  }

 switchBlackjackPage(_blackJackGame: world_pb.MiniGame):void {
    this.clickBlackGame(null,"");
    this._panel.active = false;
    this._panel_blackjack.active = true;
    this.getBlackJackDataStage2(_blackJackGame);
  }

  private getBlackJackDataStage2(_blackJackGame:world_pb.MiniGame):void
  {
    const blackjackRoomsFor1Player : world_pb.blackjackRoomInfo[] = _blackJackGame.blackjackRoomLists.room.filter(item => item.gameType === 1);
    const blackjackRoomsFor3Player : world_pb.blackjackRoomInfo[] = _blackJackGame.blackjackRoomLists.room.filter(item => item.gameType === 0);
    this.gameType = _blackJackGame.deskType;
    if(blackjackRoomsFor1Player.length>0)
        this._bj_levelId_1Player = blackjackRoomsFor1Player[0].levelID;
    this.autoShowBlackJack(blackjackRoomsFor1Player,this._tableView_onePeopleMode);
    if(blackjackRoomsFor3Player.length>0)
        this._bj_levelId_3Player = blackjackRoomsFor3Player[0].levelID;
    
    this.autoShowBlackJack(blackjackRoomsFor3Player,this._tableView_threePeopleMode);

  }

    protected onLoad(): void {
        if (!MiniGameNewRoomList.g_instance) MiniGameNewRoomList.g_instance = this;

        cv.resMgr.adaptWidget(this.node, true);
        this._btn_close.on("click", this._onClickBtnClose, this);
        if(this._btn_close2)
        this._btn_close2.on("click", this._onClickBtnClose, this);
        this.img_bg.on(cc.Node.EventType.TOUCH_END, (sender: cc.Node) => { this.autoHide(); });
    }

    protected onDestroy(): void {
        MiniGameNewRoomList.g_instance = null;
        console.log(`${MiniGameNewRoomList.g_class_name}: onDestroy`);
    }

    onSVEventScrollingBegan(scrollView: cc.ScrollView, tableView: TableView): void {
        this._isScrollEnd = false;
    }

    onSVEventScrolling(scrollView: cc.ScrollView, tableView: TableView): void {
        if (this._isScrollEnd) return;
        if (scrollView.content.height > scrollView.node.height) this._showFadingAct(true);
    }

    onSVEventScrollEndedWithThreshold(scrollView: cc.ScrollView, tableView: TableView): void {
        this._isScrollEnd = true;
        this._showFadingAct(false);
    }

    onSVEventScrollEnded(scrollView: cc.ScrollView, tableView: TableView): void {
        this._isScrollEnd = true;
        this._showFadingAct(false);
    }

    /**
     * 初始化房间列表面板
     * @param gameid
     */
    private _init(gameid: number): void {
        switch (gameid) {
            // 一起看球
            case world_pb.GameId.TopMatches: 
                this.panelCom.destroy();
                this.panelCaribbean.destroy();
                this.panelWealthTrio.destroy();
                this.panelBitMaster.destroy();
                this.panelBlackjack1.destroy();
                this.panelBlackjack2.destroy();
                this._panel = this.panelEuro;
            break;
            // 电子游戏
            case world_pb.GameId.PocketGames:
                this.panelCom.destroy();
                this.panelEuro.destroy();
                this.panelCaribbean.destroy();
                this.panelWealthTrio.destroy();
                this.panelBitMaster.destroy();
                this.panelBlackjack1.destroy();
                this.panelBlackjack2.destroy();
            break;
            // blackjack
            case world_pb.GameId.BlackJack: 
                this.panelEuro.destroy();
                this.panelCom.destroy();
                this.panelCaribbean.destroy();
                this.panelWealthTrio.destroy();
                this.panelBitMaster.destroy();
                this._panel = this.panelBlackjack1;
                this._panel_blackjack = this.panelBlackjack2;
                this.setBlackjackPanelProperties();
                this._panel_blackjack.active = false;
            break;
            case world_pb.GameId.CaribbeanTexasHold: 
                this.panelCom.destroy();
                this.panelEuro.destroy();
                this.panelBlackjack1.destroy();
                this.panelBlackjack2.destroy();
                this.panelWealthTrio.destroy();
                this.panelBitMaster.destroy();
                this._panel = this.panelCaribbean;
             break;
             case world_pb.GameId.WealthTrio: 
                this.panelCom.destroy();
                this.panelEuro.destroy();
                this.panelBlackjack1.destroy();
                this.panelBlackjack2.destroy();
                this.panelCaribbean.destroy();
                this.panelBitMaster.destroy();
                this._panel = this.panelWealthTrio;
             break;
             case world_pb.GameId.BitMasterGoldCoin: 
                this.panelCom.destroy();
                this.panelEuro.destroy();
                this.panelBlackjack1.destroy();
                this.panelBlackjack2.destroy();
                this.panelCaribbean.destroy();
                this.panelWealthTrio.destroy();
                this._panel = this.panelBitMaster;
             break;
            // 其他游戏
            default: 
                this.panelCaribbean.destroy();
                this.panelWealthTrio.destroy();
                this.panelBitMaster.destroy();
                this.panelEuro.destroy();
                this.panelBlackjack1.destroy();
                this.panelBlackjack2.destroy();
                this._panel = this.panelCom;
             break;
        }

        this._panel.active = true;
        this._panel_src_widget_top = this._panel.getComponent(cc.Widget).top;
        this._img_title = this._panel.getChildByName("img_title");
        this._btn_close = this._panel.getChildByName("btn_close");
        if(this._panel_blackjack)
        {
            this._img_title_blackjack2 = this._panel_blackjack.getChildByName("img_title");
            this._btn_close2 = this._panel_blackjack.getChildByName("btn_close");
            this._blackjack_panel_src_widget_top =  this._panel_blackjack.getComponent(cc.Widget).top;
        }
        this._tableView = this._panel.getChildByName("scrollView").getComponent(TableView);
        this._tableView.bindScrollEventTarget(this);

        this.img_fade.active = false;
        this.img_fade.opacity = 0;
    }

    private setBlackjackPanelProperties()
    {
        this.lbTxtback.string = cv.config.getStringData("BackBtnTitle");
        this.lbTxtOnePeople.string = cv.config.getStringData("minigame_one_player");
        this.lbTxtThreePeople.string = cv.config.getStringData("minigame_three_player");
        this.lbTxtQuickGame.string = cv.config.getStringData("minigame_quick_game");

        this._tableView_onePeopleMode = this.onepeopleContent.getChildByName("scrollView").getComponent(TableView);
        this._tableView_threePeopleMode = this.multipeopleContent.getChildByName("scrollView").getComponent(TableView);
        this._blackjack_panel_src_widget_top = this._panel_blackjack.getComponent(cc.Widget).top;
    }

    clickBlackGame(event,data) {
        cv.AudioMgr.playButtonSound("button_click");
        if (data === "more") {
          this.btnThree.spriteFrame = this.picSpriteDef;
          this.btnSingle.spriteFrame = null;
          this.lbTxtOnePeople.node.color = new cc.Color().fromHEX("#787099");
          this.lbTxtThreePeople.node.color = new cc.Color().fromHEX("#3B2B7A");
          this.onepeopleContent.active = false;  
          this.multipeopleContent.active = true;
          this._bj_isSingleMode = false;
        } else {
          this.btnSingle.spriteFrame = this.picSpriteDef;
          this.btnThree.spriteFrame = null;
          this.lbTxtOnePeople.node.color =  new cc.Color().fromHEX("#3B2B7A");
          this.lbTxtThreePeople.node.color =  new cc.Color().fromHEX("#787099");
          this.onepeopleContent.active = true;
          this.multipeopleContent.active = false; 
          this._bj_isSingleMode = true; 
        }
    }

      /**
   * @function 快速游戏
   */
  clickFastGame() {
    cv.AudioMgr.playButtonSound("button_click");
    const _dataRef: world_pb.blackjackRoomInfo = new world_pb.blackjackRoomInfo();
    _dataRef.levelID = this._bj_isSingleMode ? this._bj_levelId_1Player : this._bj_levelId_3Player;
    _dataRef.gameID = 0;
    cv.MessageCenter.send(`${BlackjackRoomListItem.g_class_name}_click`,_dataRef);
  
  }

    private _showFadingAct(isFadeIn: boolean, duration: number = 0.5): void {
        if (isFadeIn) {
            this.img_fade.active = true;

            if (this._isFadingOut) {
                this._isFadingOut = false;
                this.img_fade.stopAllActions();
            }

            if (!this._isFadingIn) {
                this._isFadingIn = true;
                this.img_fade.runAction(cc.fadeIn(duration));
            }
        }
        else {
            if (!this.img_fade.active) return;

            if (this._isFadingIn) {
                this._isFadingIn = false;
                this.img_fade.stopAllActions();
            }

            if (!this._isFadingOut) {
                this._isFadingOut = true;
                this.img_fade.runAction(cc.sequence(cc.fadeOut(duration), cc.callFunc((): void => {
                    this._isFadingOut = false;
                    this.img_fade.opacity = 0xFF;
                    this.img_fade.active = false;
                })));
            }
        }
    }

    /**
     * 获取视图片高度偏移差值
     * @param prefab 指定预制件引用
     * @param rowLen 数据数组长度(即视图"item"行数)
     * @param rowLimitShow 规定显示的行数(默认0, 如果为0则按计算结果显示真实行数)
     * @returns 
     */
    private _getViewOffsetHeight(prefab: cc.Prefab, rowLen: number, offset_h: number = 0): number {
        let value: number = 0;
        if (rowLen >= 1) {
            const minLimitLen: number = 1;        // 规定的下限行数
            const maxLimitLen: number = 5;        // 规定的上限行数(不能超出屏幕)
            let rowMaxLen: number = 0;          // 不同设备自动适配后的实际最大行数
            const content_h: number = this._tableView.node.height - this._tableView.paddingStart - this._tableView.paddingEnd;

            // 适配后实际最大行数
            rowMaxLen = (content_h + this._tableView.spacing) / (prefab.data.height + this._tableView.spacing);
            rowMaxLen = Math.max(minLimitLen, rowMaxLen);
            rowMaxLen = Math.min(maxLimitLen, rowMaxLen);

            // 小于实际行数, 计算缩减差值(为了达到设计效果, 补上一个底部间距值)
            if (rowLen < rowMaxLen) {
                const left_h: number = rowLen * prefab.data.height + (rowLen - 1) * this._tableView.spacing + offset_h;
                value = content_h - left_h;
            }
            // 大于等于比实际行数, 校准视图大小(若适配设备后视图大小不足以整除"item", 这里做个整除校准)
            else {
                rowMaxLen = cv.StringTools.toFixed(rowMaxLen, 0);
                const view_h: number = rowMaxLen * prefab.data.height + (rowMaxLen - 1) * this._tableView.spacing;
                if (content_h < view_h) {
                    value = content_h - view_h;
                }
            }
        }

        return value;
    }

    private _onClickBtnClose(event: cc.Event): void {
        cv.AudioMgr.playButtonSound("close");
        this.autoHide();
    }

    private _autoAnimFunc(actionIn: boolean, isAnim: boolean, callback: () => void = null): void {
        this.node.active = true;
        this._panel.active = true;
        this._panel.stopAllActions();

        const duration: number = 0.1;
        let seq: cc.Action = null;
        const src_pos: cc.Vec2 = cc.v2(this._panel_src_pos);
        const tmp_pos: cc.Vec2 = cc.v2(this._panel_src_pos);

        if (actionIn) {
            tmp_pos.y -= this._panel.height * this._panel.scaleY * (1 - this._panel.anchorY);
            this._panel.setPosition(tmp_pos);

            const cb: cc.ActionInstant = cc.callFunc((): void => {
                this.img_shield.getComponent(cc.BlockInputEvents).enabled = false;
                this.img_shield.active = false;
                this._panel.setPosition(src_pos);
                if (callback) callback();
            });

            if (isAnim) {
                const mt: cc.ActionInterval = cc.moveTo(duration, src_pos);
                const ebo: cc.ActionInterval = mt.easing(cc.easeOut(3));
                seq = cc.sequence(ebo, cb);
            }
            else {
                seq = cb;
            }
        }
        else {
            this._panel.setPosition(src_pos);
            tmp_pos.y -= this._panel.height * this._panel.scaleY * (1 - this._panel.anchorY);

            const cb: cc.ActionInstant = cc.callFunc((): void => {
                this.img_shield.getComponent(cc.BlockInputEvents).enabled = false;
                this.img_shield.active = false;
                this._panel.setPosition(src_pos);
                if (callback) callback();
                this.node.active = false;
            });

            if (isAnim) {
                const mt: cc.ActionInterval = cc.moveTo(duration, tmp_pos);
                const ebi: cc.ActionInterval = mt.easing(cc.easeIn(3));
                seq = cc.sequence(ebi, cb);
            }
            else {
                seq = cb;
            }
        }

        if (seq) {
            this._panel.runAction(seq);
            this.img_shield.active = true;
            this.img_shield.getComponent(cc.BlockInputEvents).enabled = true;
        }
    }

    private _adaptPanel(rowLen, offset):void{
        // 适配滚动视图大小
        const prefab: cc.Prefab = this._tableView.prefabTypes[0];
        const offset_h: number = this._getViewOffsetHeight(prefab,rowLen, offset);
        const panel_widget: cc.Widget = this._panel.getComponent(cc.Widget);
        panel_widget.top = this._panel_src_widget_top + offset_h;
        cv.resMgr.adaptWidget(this.node, true);
    }
}
