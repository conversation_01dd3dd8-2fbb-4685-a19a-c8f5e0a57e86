import cv from '../cv';
import ActivityView from '../hall/ActivityView';

const { ccclass, property } = cc._decorator;

@ccclass
export default class BundleDownloadDialog extends cc.Component {
    @property(cc.Node)
    private mask: cc.Node = null;

    @property(cc.Label)
    private content: cc.Label = null;

    @property(cc.Button)
    private leftButton: cc.Button = null;

    @property(cc.Label)
    private leftButtonLabel: cc.Label = null;

    @property(cc.Button)
    private rightButton: cc.Button = null;

    @property(cc.Label)
    private rightButtonLabel: cc.Label = null;

    private _leftButtonCallback: () => void = null;
    private _rightButtonCallback: () => void = null;

    // LIFE-CYCLE CALLBACKS:

    protected onLoad() {
        this.node.active = false;
    }

    protected onEnable() {
        this.registerEvent();
    }

    protected onDisable(): void {
        this.unregisterEvent();
    }

    // update (dt) {}

    // CUSTOM METHODS:

    private registerEvent(): void {
        this.mask.on('click', this.hide, this);
        this.leftButton.node.on('click', this.onClickLeftButton, this);
        this.rightButton.node.on('click', this.onClickRightButton, this);
    }

    private unregisterEvent(): void {
        this.mask.off('click', this.hide, this);
        this.leftButton.node.off('click', this.onClickLeftButton, this);
        this.rightButton.node.off('click', this.onClickRightButton, this);
    }

    private onClickLeftButton(): void {
        this.hide();
        if (this._leftButtonCallback) {
            this._leftButtonCallback();
        }
    }

    private onClickRightButton(): void {
        this.hide();
        if (this._rightButtonCallback) {
            this._rightButtonCallback();
        }
    }

    private show() {
        this.node.active = true;
    }

    private hide() {
        this.node.active = false;
    }

    showRetryDialog(retryCallback: () => void): void {
        if (this.node.active) {
            return;
        }
        this.setRetryDialogLabels();
        this.setRetryDialogCallback(retryCallback);
        this.show();
    }

    private setRetryDialogLabels(): void {
        this.content.string = cv.config.getStringData('failed_to_download_bundle_with_retry');
        this.leftButtonLabel.string = cv.config.getStringData('bundle_download_dialog_contact_button');
        this.rightButtonLabel.string = cv.config.getStringData('bundle_download_dialog_retry_button');
    }

    private setRetryDialogCallback(retryCallback: () => void): void {
        this._leftButtonCallback = this.contactCustomerService;
        this._rightButtonCallback = retryCallback;
    }

    showFailedDialog(): void {
        if (this.node.active) {
            return;
        }
        this.setFailedDialogLabels();
        this.setFailedDialogCallback();
        this.show();
    }

    private setFailedDialogLabels(): void {
        this.content.string = cv.config.getStringData('failed_to_download_bundle');
        this.leftButtonLabel.string = cv.config.getStringData('Cancel');
        this.rightButtonLabel.string = cv.config.getStringData('bundle_download_dialog_contact_button');
    }

    private setFailedDialogCallback(): void {
        this._leftButtonCallback = () => {};
        this._rightButtonCallback = this.contactCustomerService;
    }

    private contactCustomerService = (): void => {
        const url = cv.httpHandler.getKefuUrl();
        if (cc.sys.isBrowser) {
            if (url) {
                cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                    window.open(url);
                });
            }
        } else {
            cv.httpHandler.getCustom();
        }

        const activityViewNode = cc.director.getScene().getChildByName('activityView');
        if (activityViewNode) {
            const activityView = activityViewNode.getComponent(ActivityView);
            if (activityView) {
                activityView.init();
                activityView.showActivity(1, () => {
                    activityView.clearCallBack();
                });
            }
        }
    };
}
