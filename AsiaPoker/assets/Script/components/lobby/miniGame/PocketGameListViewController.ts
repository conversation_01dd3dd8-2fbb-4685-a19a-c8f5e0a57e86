import { SafeAreaHelper } from '../../../../default/shared/safe_area_helper/SafeAreaHelper';
import cv from '../cv';
import PocketGameListViewItemController from './PocketGameListViewItemController';
import PocketGameScrollView from './PocketGameScrollView';
import ws_protocol = require('./../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;

const { ccclass, property } = cc._decorator;

@ccclass
export default class PocketGameListViewController extends cc.Component {
    @property(cc.Node)
    public backButtonNode: cc.Node = null;

    @property(PocketGameScrollView) public pocketGameItemPool: PocketGameScrollView = null;

    @property(cc.Label)
    public backButtonLabel: cc.Label = null;

    @property(cc.Label)
    public headerTitleLabel: cc.Label = null;

    @property(cc.Node)
    public headerNode: cc.Node = null;

    @property(cc.Node)
    public contentNode: cc.Node = null;

    @property(cc.Node)
    public viewContentNode: cc.Node = null;

    private _onGameIconCallback: (value: world_pb.IPgGameData) => void;

    public showPocketGameListView(data: world_pb.MiniGame[], extra?: world_pb.MiniGame[], extra_start_index?: number): void {
        this.backButtonLabel.string = cv.config.getStringData('BackBtnTitle');
        this.headerTitleLabel.string = cv.config.getStringData('Earnings_lab_4');

        const pocketGameData = [].concat(...data.map((e) => e.pgGameData)).sort((a, b) => a.sortId - b.sortId);
        if(extra) {
            if (!extra_start_index || extra_start_index < 0) {
                pocketGameData.push(...extra);
            }
            else{
                pocketGameData.splice(extra_start_index, 0, ...extra);
            }
        }
        
        if(CC_PREVIEW) console.error(pocketGameData);
        this.node.active = true;
        this._updateGameList(pocketGameData);
    }

    public setOnGameIconCallback(callback: (value: world_pb.IPgGameData) => void): void {
        this._onGameIconCallback = callback;
    }

    protected onLoad(): void {
        this._registerEvents();
        this._adjustSafeArea();
    }

    protected onDestroy(): void {
        this._unregisterEvents();
    }

    private _adjustSafeArea(): void {
        const headerSize = this.headerNode.getContentSize();
        headerSize.height += SafeAreaHelper.getUpperDangerZoneYOffset();
        this.headerNode.setContentSize(headerSize);

        const contentSize = this.node.getContentSize();
        contentSize.height -= headerSize.height;
        this.contentNode.setContentSize(contentSize);
        this.viewContentNode.setContentSize(contentSize);
    }

    private _registerEvents(): void {
        this.backButtonNode.on(cc.Node.EventType.TOUCH_END, this._onBackButtonEvent.bind(this));
        cv.MessageCenter.register('jumpToHallBank', this._onBackButtonEvent.bind(this), this.node);
    }

    private _unregisterEvents(): void {
        this.backButtonNode.off(cc.Node.EventType.TOUCH_END, this._onBackButtonEvent.bind(this));
        cv.MessageCenter.unregister('jumpToHallBank', this.node);
        
    }

    private _onBackButtonEvent(): void {
        this.node.active = false;
    }

    private _updateGameList(data: world_pb.IPgGameData[]) {
        const itemCount = data.length;
        this.pocketGameItemPool.init({ anchorX: 0.5, anchorY: 0.5, itemCount });

        const pocketGameItem = this.pocketGameItemPool.getItems();
        if (!pocketGameItem) {
            console.error('PocketGameListViewController :: _updateGameList : sign items is empty!');
            return;
        }

        pocketGameItem.forEach((item, index) => {
            const pocketGameItemController = item.getComponent(PocketGameListViewItemController);
            if (pocketGameItemController) {
                pocketGameItemController.init(data[index]);
                pocketGameItemController.setOnIconClickCallback(this._onGameIconCallback.bind(this));
            } else {
                console.error(`Failed to get pocketGameItemController script from item ${index}.`);
            }
        });
    }
}
