import ws_protocol = require("../../../common/pb/ws_protocol");
import world_pb = ws_protocol.pb;

import cv from "../../../components/lobby/cv";
import { TableView } from "../../../common/tools/TableView";
import { LANGUAGE_TYPE } from "../../../common/tools/Enum";
import { MiniGameNewRoomList } from "./MiniGameNewRoomList";

const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameListItemBitMaster extends cc.Component {
    @property(cc.Label) currency_type: cc.Label = null;
    @property(cc.Label) balance: cc.Label = null;
    @property(cc.Sprite) main_icon: cc.Sprite = null;
    @property(cc.Label) summary_text: cc.Label = null;
    @property(cc.Label) summary_val: cc.Label = null;
    @property(cc.Button) btn_enter: cc.Button = null;

    @property(cc.SpriteFrame) main_coin_icon: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) main_usd_icon: cc.SpriteFrame = null;

    private readonly _blockZeroBalance : boolean = false;
    private _gameId: world_pb.GameId = null;

    protected onLoad(): void {
        cv.resMgr.adaptWidget(this.node, true);
        this.node.on("click", this._onClickBtnEnter, this);
        this.btn_enter.node.on("click", this._onClickBtnEnter, this);
    }

    updateSVReuseData(index: number, data: world_pb.MiniGame, view?: TableView): void {
        this._gameId = data.sourceType;
        if(this._gameId === world_pb.GameId.BitMasterGoldCoin) {
            this.main_icon.spriteFrame = this.main_coin_icon;
            this.currency_type.string =  cv.config.getStringData("bitMaster_currency_coin"); 
            this.balance.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().total_amount);
            
        } else if (this._gameId === world_pb.GameId.BitMasterUSD) {
            this.main_icon.spriteFrame = this.main_usd_icon;
            this.currency_type.string =  cv.config.getStringData("bitMaster_currency_usdt");
            this.balance.string = cv.StringTools.numToFloatString(cv.dataHandler.getUserData().usdt); 
        }
        this.summary_text.string =  cv.config.getStringData("bitMaster_not_yet_summary");
        this.summary_val.string = cv.StringTools.numToFloatString(data.CFDGameData.unsettled_amount);
        this.btn_enter.node.getComponentInChildren(cc.Label).fontSize = (cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN) ? 40 : 32;
    }


    private _onClickBtnEnter(event: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        if (!this._gameId) return;
          // 禁止"游客"
        if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(cv.config.getStringData("Sports_enter_tip"), cv.Enum.ButtonStyle.TWO_BUTTON, cv.dataHandler.upgradeAccount.bind(cv.dataHandler));
            return;
        }

        let hasSufficientFund:boolean = true;
        if(this._gameId === world_pb.GameId.BitMasterGoldCoin) {
            if(cv.dataHandler.getUserData().u32Chips <= 0) {
                hasSufficientFund = false;
            }
        } else if (this._gameId === world_pb.GameId.BitMasterUSD) {
            if(cv.dataHandler.getUserData().usdt <= 0) {
                hasSufficientFund = false;
            } 
        }

        if (this._blockZeroBalance && !hasSufficientFund) {
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode42'), cv.Enum.ToastType.ToastTypeError);
            return;
        }

        if (!cv.config.alllowMultipWindow()) {
            cv.roomManager.RequestJoinHabaGame(this._gameId);
        }
        else {
            cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.TWO_BUTTON,
                ()=>{ cv.roomManager.RequestJoinHabaGame(this._gameId);  
                      MiniGameNewRoomList?.g_instance?.autoHide(); }, 
                null );
        }
    }
}
