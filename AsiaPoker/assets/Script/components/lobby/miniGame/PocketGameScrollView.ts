
import ScrollViewItemPool, { ScrollViewItemPoolConfig } from '../../../common/tools/ScrollViewItemPool';

const { ccclass } = cc._decorator;

@ccclass
export default class PocketGameScrollView extends ScrollViewItemPool {
    private _initWith = 0;

    public init(config: ScrollViewItemPoolConfig) {
        super.init(config);
        this._initWith = this.scrollView.content.width;
        this.scrollView.content.on(cc.Node.EventType.SIZE_CHANGED, this._adaptLayout, this);
    }

    private _adaptLayout() {
        // calculate column count and adjust padding
        const width = this.scrollView.content.width;
        if (width === this._initWith) {
            return;
        }
        let columnCount = Math.floor(
            (width - (this._scrollViewContentLayout.paddingLeft + this._scrollViewContentLayout.paddingRight)) /
                this.itemPrefab.data.width
        );
        let tempWidth =
            columnCount * this.itemPrefab.data.width +
            (columnCount - 1) * this._scrollViewContentLayout.spacingX +
            this._scrollViewContentLayout.paddingLeft +
            this._scrollViewContentLayout.paddingRight;
        if (tempWidth > width) {
            columnCount -= 1;
        }
        tempWidth =
            columnCount * this.itemPrefab.data.width +
            (columnCount - 1) * this._scrollViewContentLayout.spacingX +
            this._scrollViewContentLayout.paddingLeft +
            this._scrollViewContentLayout.paddingRight;
        const padding = (width - tempWidth) / 2;
        this._scrollViewContentLayout.paddingLeft += padding;
        this.scrollView.content.off(cc.Node.EventType.SIZE_CHANGED, this._adaptLayout, this);
    }
}
