import ws_protocol = require("../../../common/pb/ws_protocol");
import world_pb = ws_protocol.pb;

import cv from "../../../components/lobby/cv";
import { TableView } from "../../../common/tools/TableView";
import { LANGUAGE_TYPE } from "../../../common/tools/Enum";
import { MiniGameNewRoomList } from "./MiniGameNewRoomList";

const { ccclass, property } = cc._decorator;
@ccclass
export class MiniGameListItemCaribbean extends cc.Component {
    @property(cc.Label) txt_title: cc.Label = null;
    @property(cc.Sprite) img_icon: cc.Sprite = null;
    @property(cc.Label) txt_online: cc.Label = null;
    @property(cc.Button) btn_enter: cc.Button = null;

    private  _gameId: world_pb.GameId = null;

    private readonly _blockZeroBalance : boolean = false;

    protected onLoad(): void {
        cv.resMgr.adaptWidget(this.node, true);
        this.node.on("click", this._onClickBtnEnter, this);
        this.btn_enter.node.on("click", this._onClickBtnEnter, this);
    }

    protected start(): void {
    }

    protected onEnable(): void {
    }

    protected onDisable(): void {
    }

    protected onDestroy(): void {
    }

    
    updateSVReuseData(index: number, data: world_pb.MiniGame, view?: TableView): void {
        this._gameId = data.sourceType;
        this.txt_title.string = data.sourceType === world_pb.GameId.CaribbeanStud ?  cv.config.getStringData("Caribbean_Stud") : cv.config.getStringData("Caribbean_Holdem");
        this.txt_online.string = `${data.playerNum}`;
        const path_icon: string = `hall/miniGame/new/itemIcon/listIcon/icon_${data.sourceType}`;
        cv.resMgr.setSpriteFrame(this.img_icon.node, cv.config.getLanguagePath(path_icon, LANGUAGE_TYPE.zh_CN));
        this.btn_enter.node.getComponentInChildren(cc.Label).fontSize = (cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN) ? 40 : 32;
    }


    private _onClickBtnEnter(event: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
    
        if (!this._gameId) return;
    
    
          // 禁止"游客"
          if (cv.dataHandler.getUserData().isTouristUser) {
            cv.TP.showMsg(cv.config.getStringData("Sports_enter_tip"), cv.Enum.ButtonStyle.TWO_BUTTON, cv.dataHandler.upgradeAccount.bind(cv.dataHandler));
            return;
        }
        
        if (this._blockZeroBalance && cv.dataHandler.getUserData().total_amount < 1 ) { // Checks if it has not GOLD-COINS balance
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode42'), cv.Enum.ToastType.ToastTypeError);
            return;
        }

        if (!cv.config.alllowMultipWindow()) {
            cv.roomManager.RequestJoinHabaGame(this._gameId);
        }
        else {
            cv.TP.showMsg(cv.config.getStringData('UIOpenNewWindow'), cv.Enum.ButtonStyle.TWO_BUTTON,
                ()=>{ cv.roomManager.RequestJoinHabaGame(this._gameId);  
                      MiniGameNewRoomList?.g_instance?.autoHide(); }, 
                null );
        }
    }
}
