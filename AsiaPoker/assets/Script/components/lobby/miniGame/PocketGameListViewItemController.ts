import ws_protocol = require('./../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import cv from '../cv';

enum ChampionShipStatus {
    INACTIVE = 1,
    ACTIVE = 2
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class PocketGameListViewItemController extends cc.Component {
    @property(cc.Sprite)
    public pocketGameSprite: cc.Sprite = null;

    // 游戏标签
    @property(cc.Sprite)
    public labelIcon: cc.Sprite = null;

    @property(cc.Sprite)
    public isChampionShip: cc.Sprite = null;

    @property(cc.Label)
    public pocketGameLabel: cc.Label = null;

    private _onIconClickCallback: (value: world_pb.IPgGameData) => void;

    private _gameId = 0;

    private _labelType: world_pb.PgGameLabel = world_pb.PgGameLabel.LabelNormal;
    private _isChampionship: number = 1;
    private _data : world_pb.IPgGameData;

    public init(data: world_pb.IPgGameData) {
        // load spriteframe texture
        if (data.gameIcon && data.gameIcon !== '') {
            let gameUrl = data.gameIcon;
            if (!this._allowGetLocalGameIcon(data.gameId))  {
                gameUrl = cv.domainMgr.getImageURL(data.gameIcon, 0);
            }

            if (this._isUrl(gameUrl)) {
                cc.loader.load(gameUrl, (error, texture) => {
                    if (error) {
                        console.error('Failed to load texture:', error);
                        return;
                    }
                    const spriteFrame = new cc.SpriteFrame(texture);
                    if (cc.isValid(this.pocketGameSprite)) this.pocketGameSprite.spriteFrame = spriteFrame; // async delay which might result this component to be destroyed
                });
            } else {
                cv.resMgr.setSpriteFrame(this.pocketGameSprite.node, cv.config.getLanguagePath(gameUrl));
            }
        }

        // load name
        this._data = data;
        this.pocketGameLabel.string = data.gameName;
        this._gameId = data.gameId;
        this._labelType = data.label;
        this._isChampionship = data.isChamPoin;

        this._unregisterEvents();
        this._registerEvents();
        this.initLanguage();
    }

    protected onDestroy(): void {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
    }

    public setOnIconClickCallback(callback: (value: world_pb.IPgGameData) => void) {
        this._onIconClickCallback = callback;
    }

    private _onIconClick(): void {
        cv.AudioMgr.playButtonSound('button_click');
        if (this._onIconClickCallback) {
            this._onIconClickCallback(this._data);
        }
    }

    private _registerEvents(): void {
        this.node.on(cc.Node.EventType.TOUCH_END, this._onIconClick, this);
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);
    }

    private _unregisterEvents(): void {
        this.node.off(cc.Node.EventType.TOUCH_END, this._onIconClick, this);
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
    }

    private initLanguage() {
        this._updateChampionshipLabel();
        this._updateLabel();
    }

    private _updateLabel() {
        switch (this._labelType) {
            case world_pb.PgGameLabel.LabelNew: // 最新
                cv.resMgr.setSpriteFrame(
                    this.labelIcon.node,
                    cv.config.getLanguagePath('hall/miniGame/new/itemFlag/ic_slots_new')
                );
                break;
            case world_pb.PgGameLabel.LabelMost: // 最多人玩
                cv.resMgr.setSpriteFrame(
                    this.labelIcon.node,
                    cv.config.getLanguagePath('hall/miniGame/new/itemFlag/ic_slots_trend')
                );
                break;
            case world_pb.PgGameLabel.LabelPopular: // 最受欢迎
                cv.resMgr.setSpriteFrame(
                    this.labelIcon.node,
                    cv.config.getLanguagePath('hall/miniGame/new/itemFlag/ic_slots_popular')
                );
                break;
            case world_pb.PgGameLabel.LabelRecommend: // 人工推荐
                cv.resMgr.setSpriteFrame(
                    this.labelIcon.node,
                    cv.config.getLanguagePath('hall/miniGame/new/itemFlag/ic_slots_featured')
                );
                break;
            default:
                this.labelIcon.spriteFrame = null;
                break;
        }
    }

    private _updateChampionshipLabel() {
        const championShipNode = this.isChampionShip.node;
        if (this._isChampionship === ChampionShipStatus.ACTIVE) {
            championShipNode.active = true;
            cv.resMgr.setSpriteFrame(
                championShipNode,
                cv.config.getLanguagePath('hall/miniGame/new/itemFlag/ic_slots_championship')
            );
        } else {
            championShipNode.active = false;
        }
    }

    private _isUrl(url: string): boolean {
        const reg = /[.]/;
        return reg.test(url);
    }

    private _allowGetLocalGameIcon(gameId: number): boolean {
        const GAME_LIST = [world_pb.GameId.ISlot];
        return GAME_LIST.includes(gameId);
    }
}
