import { ISBMarketData, ISBSelectionData } from './SportsBettingPopupPanelManager';

const { ccclass, property } = cc._decorator;

export interface SBContentBetActionPayload {
    marketId: number;
    price: number;
    point: number;
    key: string;
}

export enum SBBetActionButton {
    WIN,
    DRAW,
    LOSE,
    HOME,
    AWAY,
    BIG,
    SMALL
}

@ccclass
export default class SBContentBetActionController extends cc.Component {
    @property(cc.SpriteFrame)
    onSelectedSpriteFrame: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    onUnselectedSpriteFrame: cc.SpriteFrame = null;

    @property(cc.Color)
    onSelectedColor: cc.Color = null;

    @property(cc.Color)
    onUnselectedColor: cc.Color = null;

    _buttonsAreDisabled = false;

    // data
    _selectionData: ISBSelectionData[] = [];

    // payload
    _selectedBetType: number;
    _selectedMarketId: number;
    _selectedKey: string;
    _selectedPrice: number;
    _selectedPoint: number;
    _selectedMatchId: number;
    _selectedSportsType: number;

    mapData(data: ISBMarketData): void {
        const { betType, marketId, selections } = data;
        this._selectedBetType = betType;
        this._selectedMarketId = marketId;
        this._selectionData = selections;
    }

    setupOnBetActionButtonClicked(actionButton: SBBetActionButton): void {
        this.onBetActionButtonClicked(actionButton);
    }

    toggleButtonInteractivity(value: boolean, useEffect: boolean = false): void {
        throw new Error('SBContentBetActionController :: toggleButtonInteractivity must be defined!');
    }

    getSelectedPayload(): SBContentBetActionPayload {
        return {
            marketId: this._selectedMarketId,
            price: this._selectedPrice,
            point: this._selectedPoint,
            key: this._selectedKey
        };
    }

    resetState(): void {
        this.resetActionButtonVisualState();
        this.resetPayload();
    }

    protected onBetActionButtonClicked(actionButton: SBBetActionButton): void {
        if (this._buttonsAreDisabled) return;
        this.resetActionButtonVisualState();
    }

    protected resetActionButtonVisualState(): void {
        throw new Error('SBContentBetActionController :: resetActionButtonVisualState must be defined!');
    }

    protected resetPayload(): void {
        this._selectedPrice = undefined;
        this._selectedPoint = undefined;
        this._selectedKey = undefined;
    }
}
