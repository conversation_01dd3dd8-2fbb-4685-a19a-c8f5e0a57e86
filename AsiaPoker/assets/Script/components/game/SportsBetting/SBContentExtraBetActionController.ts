import cv from '../../lobby/cv';
import SBContentBetActionController, { SBBetActionButton } from './SBContentBetActionController';
import { ISBMarketData, ISBSelectionData } from './SportsBettingPopupPanelManager';

const { ccclass, property } = cc._decorator;

const BET_ACTION_BUTTON_INACTIVE_OPACITY = 127.5;
const ON_PLACING_BET_OPACITY = 127.5;

@ccclass
export default class SBContentExtraBetActionController extends SBContentBetActionController {
    @property(cc.Label)
    titleLabel: cc.Label = null;

    @property(cc.String)
    titleStringId = '';

    @property(cc.String)
    firstStringId = '';

    @property(cc.String)
    secondStringId = '';

    @property(cc.Node)
    firstActionNode: cc.Node = null;

    @property(cc.Node)
    secondActionNode: cc.Node = null;

    @property(cc.Sprite)
    firstActionSprite: cc.Sprite = null;

    @property(cc.Sprite)
    secondActionSprite: cc.Sprite = null;

    @property(cc.Node)
    firstActionLabelNode: cc.Node = null;

    @property(cc.Node)
    secondActionLabelNode: cc.Node = null;

    @property(cc.Node)
    firstActionBetMultiplierLabelNode: cc.Node = null;

    @property(cc.Node)
    secondActionBetMultiplierLabelNode: cc.Node = null;

    @property(cc.Label)
    firstActionLabel: cc.Label = null;

    @property(cc.Label)
    secondActionLabel: cc.Label = null;

    @property(cc.Label)
    firstActionBetMultiplierLabel: cc.Label = null;

    @property(cc.Label)
    secondActionBetMultiplierLabel: cc.Label = null;

    mapData(data: ISBMarketData): void {
        super.mapData(data);
        this.titleLabel.string = cv.config.getStringData(this.titleStringId);

        const homeData = this._selectionData.filter((e) => e.key === 'h');
        this.setupFirstActionLabel(homeData[0].pointStr);
        this.setupFirstActionBetMultiplierLabel(homeData[0].price);

        const awayData = this._selectionData.filter((e) => e.key === 'a');
        this.setupSecondActionLabel(awayData[0].pointStr);
        this.setupSecondActionBetMultiplierLabel(awayData[0].price);
    }

    toggleButtonInteractivity(value: boolean): void {
        this._buttonsAreDisabled = !value;
        if (value) {
            this.firstActionSprite.node.opacity = 255;
            this.secondActionSprite.node.opacity = 255;
        } else {
            this.firstActionSprite.node.opacity = BET_ACTION_BUTTON_INACTIVE_OPACITY;
            this.secondActionSprite.node.opacity = BET_ACTION_BUTTON_INACTIVE_OPACITY;
        }
    }

    public toggleUIOpacityDim(value: boolean) {
        this.node.opacity = value ? ON_PLACING_BET_OPACITY : 255;
    }

    protected setupFirstActionLabel(value: string): void {
        this.firstActionLabel.string = cv.StringTools.formatC(
            cv.config.getStringData(this.firstStringId),
            value.toString()
        );
    }

    protected setupSecondActionLabel(value: string): void {
        this.secondActionLabel.string = cv.StringTools.formatC(
            cv.config.getStringData(this.secondStringId),
            value.toString()
        );
    }

    protected setupFirstActionBetMultiplierLabel(value: number): void {
        this.firstActionBetMultiplierLabel.string = cv.StringTools.formatC(
            cv.config.getStringData('Sports_Bettings_Bet_Action_Multiplier'),
            value.toString()
        );
    }

    protected setupSecondActionBetMultiplierLabel(value: number): void {
        this.secondActionBetMultiplierLabel.string = cv.StringTools.formatC(
            cv.config.getStringData('Sports_Bettings_Bet_Action_Multiplier'),
            value.toString()
        );
    }

    protected onBetActionButtonClicked(actionButton: SBBetActionButton): void {
        super.onBetActionButtonClicked(actionButton);

        if (this._buttonsAreDisabled) return;

        let selectionData: ISBSelectionData;
        switch (actionButton) {
            case SBBetActionButton.HOME:
            case SBBetActionButton.BIG:
                this.firstActionSprite.spriteFrame = this.onSelectedSpriteFrame;
                this.firstActionLabelNode.color = this.onSelectedColor;
                this.firstActionBetMultiplierLabelNode.color = this.onSelectedColor;
                selectionData = this._selectionData.filter((e) => e.key === 'h')[0];
                break;
            case SBBetActionButton.AWAY:
            case SBBetActionButton.SMALL:
                this.secondActionSprite.spriteFrame = this.onSelectedSpriteFrame;
                this.secondActionLabelNode.color = this.onSelectedColor;
                this.secondActionBetMultiplierLabelNode.color = this.onSelectedColor;
                selectionData = this._selectionData.filter((e) => e.key === 'a')[0];
                break;
            default:
                throw new Error('SBContentDualBetActionController :: onBetActionButtonClicked : default case failure!');
        }

        this._selectedKey = selectionData.key;
        this._selectedPrice = selectionData.price;
        this._selectedPoint = selectionData.point;
    }

    protected resetActionButtonVisualState(): void {
        this.firstActionSprite.spriteFrame = this.onUnselectedSpriteFrame;
        this.secondActionSprite.spriteFrame = this.onUnselectedSpriteFrame;

        this.firstActionLabelNode.color = this.onUnselectedColor;
        this.secondActionLabelNode.color = this.onUnselectedColor;

        this.firstActionBetMultiplierLabelNode.color = this.onUnselectedColor;
        this.secondActionBetMultiplierLabelNode.color = this.onUnselectedColor;
    }
}
