import { ScrollIndicatorItem } from './ScrollIndicatorItem';
import { ScrollViewGestureItem } from './ScrollViewGestureItem';

const { ccclass, property } = cc._decorator;

enum SwipeDirection {
    NONE = 0,
    LEFT = 1,
    RIGHT = 2
}

@ccclass
export default class ScrollViewGesture extends cc.Component {
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    @property(cc.Prefab)
    scrollViewGestureItemPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    scrollIndicatorPrefab: cc.Prefab = null;

    @property(cc.Node)
    contentNode: cc.Node = null;

    @property(cc.Node)
    scrollIndicatorHolderNode: cc.Node = null;

    @property(cc.Node)
    gestureCaptureNode: cc.Node = null;

    @property(cc.Layout)
    contentLayout: cc.Layout = null;

    @property
    contentItemWidth: number = 0;

    @property
    canvasDesiredWidth: number = 0;

    _contentItemNodes: cc.Node[] = [];
    _scrollViewGestureItems: ScrollViewGestureItem[] = [];
    _indicatorItemNodes: cc.Node[] = [];
    _scrollIndicatorItems: ScrollIndicatorItem[] = [];

    _prefabWidth: number = 0;
    _contentsWidth: number = 0;

    _contentScrollOffsetPercentageArray: number[] = [];

    _lastScrollOffsetPosition = 0;
    _lastIndexPosition = 0;

    _swipeMinLength = 10;

    _isTouchDisabled = false;

    _touchesStartPosObj: { [key: string]: cc.Vec2 } = Object.create(null);

    protected onLoad(): void {
        this.gestureCaptureNode.on(cc.Node.EventType.TOUCH_START, this.touchStart.bind(this));
        this.gestureCaptureNode.on(cc.Node.EventType.TOUCH_END, this.touchEnd.bind(this));
        this.gestureCaptureNode.on(cc.Node.EventType.TOUCH_CANCEL, this.touchCancel.bind(this));
        this.multiResHandle();
    }

    protected onDestroy(): void {
        this.gestureCaptureNode.off(cc.Node.EventType.TOUCH_START, this.touchStart.bind(this));
        this.gestureCaptureNode.off(cc.Node.EventType.TOUCH_END, this.touchEnd.bind(this));
        this.gestureCaptureNode.off(cc.Node.EventType.TOUCH_CANCEL, this.touchCancel.bind(this));
    }

    protected multiResHandle(): void {
        const canvasWidth = cc.Canvas.instance.node.width;

        this.gestureCaptureNode.width = canvasWidth;

        // assuming 1080 is standard width
        const { spacingX } = this.contentLayout;

        this.contentLayout.spacingX = (canvasWidth / this.canvasDesiredWidth) * spacingX;

        // assuming content item size is 838
        this.contentLayout.paddingLeft = (canvasWidth - this.contentItemWidth) / 2;
        this.contentLayout.paddingRight = (canvasWidth - this.contentItemWidth) / 2;
    }

    protected toggleInteractivity(value: boolean) {
        this._isTouchDisabled = !value;
        this.scrollView.enabled = value;
    }

    protected touchStart(e: cc.Event.EventTouch): void {
        if (this._isTouchDisabled) return;

        this._touchesStartPosObj[e.touch.getID()] = e.getLocation();
    }

    protected touchEnd(e: cc.Event.EventTouch): void {
        if (!this._touchesStartPosObj[e.touch.getID()]) {
            return;
        }

        const start = this._touchesStartPosObj[e.touch.getID()];
        const end = e.getLocation();
        delete this._touchesStartPosObj[e.touch.getID()];

        // e.stopPropagation();

        // get the length and direction of the swipe
        const dx = end.x - start.x;
        const dy = end.y - start.y;
        const length = Math.sqrt(dx * dx + dy * dy);
        const verticalSwipe = Math.abs(dy) > Math.abs(dx);

        if (length < this._swipeMinLength) {
            console.error('reject');
            this.scrollEvent(SwipeDirection.NONE);
            return;
        }

        if (verticalSwipe && dy > 0) {
            // upwards swipe
            // console.log('up ward');
        } else if (verticalSwipe) {
            // downwards swipe
            // console.log('down ward');
        } else if (!verticalSwipe && dx > 0) {
            // swipe right
            // console.log('right');
            this.scrollEvent(SwipeDirection.RIGHT);
        } else {
            // console.log('left');
            this.scrollEvent(SwipeDirection.LEFT);
        }
    }

    protected touchCancel(e: cc.Event.EventTouch): void {
        if (!this._touchesStartPosObj[e.touch.getID()]) {
            return;
        }

        console.log('cancel');

        this.scrollEvent(SwipeDirection.NONE);
    }

    protected scrollEvent(swipeDirection: SwipeDirection, scrollDuration = 0.25) {
        const nextIndexPosition =
            this._lastIndexPosition +
            (swipeDirection === SwipeDirection.NONE ? 0 : swipeDirection === SwipeDirection.LEFT ? 1 : -1);
        const maxLength = this._contentScrollOffsetPercentageArray.length;

        const updatedIndex =
            nextIndexPosition < 0 ? 0 : nextIndexPosition > maxLength - 1 ? maxLength - 1 : nextIndexPosition;

        const scrollToPercentage = this._contentScrollOffsetPercentageArray[updatedIndex];
        this._lastIndexPosition = updatedIndex;

        this.scrollView.scrollToPercentHorizontal(scrollToPercentage, scrollDuration);

        // update item visual
        this.updateItemFocusVisual(updatedIndex);
    }

    protected updateItemFocusVisual(index: number) {
        this._scrollViewGestureItems.forEach((e, i) => {
            if (i !== index) {
                e.onNotInFocus();
            } else {
                e.onFocus();
            }
        });

        if (this._scrollIndicatorItems.length) {
            this._scrollIndicatorItems.forEach((e, i) => {
                if (i !== index) {
                    e.toggleIndicator(false);
                } else {
                    e.toggleIndicator(true);
                }
            });
        }
    }

    protected calculateContentsOffset(count: number) {
        for (let i = 0; i < count; i++) {
            const divider = count ? count - 1 : 0;
            const offset = divider > 0 ? (1 / divider) * i : 0;
            this._contentScrollOffsetPercentageArray.push(offset);
        }
        // console.error(this._contentScrollOffsetPercentageArray);
    }

    protected resetToFirstElement() {
        const scrollToPercentage = this._contentScrollOffsetPercentageArray[0];
        this._lastIndexPosition = 0;

        this.scrollView.scrollToPercentHorizontal(scrollToPercentage);

        // unfocus all item
        this._scrollViewGestureItems.forEach((e) => {
            e.onNotInFocus();
        });
    }

    // #region spawning
    protected spawnItems(count: number) {
        for (let i = 0; i < count; i++) {
            if (this.scrollIndicatorPrefab) this.spawnScrollIndicatorPrefab();

            const itemNode = this.spawnScrollViewGestureItemPrefab();
            this._prefabWidth = itemNode.width;

            if (i !== 0) this._contentsWidth += this.contentLayout.spacingX;
            this._contentsWidth += itemNode.width;
        }

        this._contentsWidth += this.contentLayout.paddingLeft;
        this._contentsWidth += this.contentLayout.paddingRight;
    }

    protected despawnItems(): void {
        this._contentItemNodes.forEach((e) => {
            e.destroy();
        });
        this._contentItemNodes = [];
        this._indicatorItemNodes.forEach((e) => {
            e.destroy();
        });
        this._indicatorItemNodes = [];
        this._scrollViewGestureItems = [];
        this._scrollIndicatorItems = [];
        this._contentScrollOffsetPercentageArray = [];

        this._lastScrollOffsetPosition = 0;
        this._lastIndexPosition = 0;
    }

    protected spawnScrollIndicatorPrefab(): cc.Node {
        const scrollIndicatorNode = cc.instantiate(this.scrollIndicatorPrefab);
        this._indicatorItemNodes.push(scrollIndicatorNode);
        const scrollIndicatorItemScript = scrollIndicatorNode.getComponent(ScrollIndicatorItem);
        this._scrollIndicatorItems.push(scrollIndicatorItemScript);
        this.scrollIndicatorHolderNode.addChild(scrollIndicatorNode);
        scrollIndicatorNode.setPosition(0, 0);
        return scrollIndicatorNode;
    }

    protected spawnScrollViewGestureItemPrefab(): cc.Node {
        const itemNode = cc.instantiate(this.scrollViewGestureItemPrefab);
        this._contentItemNodes.push(itemNode);
        const scrolViewGestureItemScript = itemNode.getComponent(ScrollViewGestureItem);
        this._scrollViewGestureItems.push(scrolViewGestureItemScript);

        this.contentNode.addChild(itemNode);
        itemNode.setPosition(0, 0);
        return itemNode;
    }
    // #endregion
}
