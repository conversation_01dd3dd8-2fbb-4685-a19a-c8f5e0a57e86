const { ccclass, property } = cc._decorator;

@ccclass
export default class TextRunnerView extends cc.Component {
    @property(cc.Label)
    runLaber: cc.Label = null;

    private _mask: cc.Mask = null;
    private _maskWidth: number = 0;
    private _labelWidth: number = 0;
    private _moveDistance: number = 0;
    private _targetPosX: number = 0;

    // LIFE-CYCLE CALLBACKS:

    onLoad() {
        let maskCom = this.node.getComponent(cc.Mask);
        if (maskCom) {
            this._mask = maskCom;
            this._mask.type = cc.Mask.Type.RECT;
            this._mask.enabled = false;
        } else {
            this._mask = this.node.addComponent(cc.Mask);
            this._mask.type = cc.Mask.Type.RECT;
            this._mask.enabled = false;
        }
    }

    start() {
        this._maskWidth = this.node.width;
        if (this.runLaber) {
            this.runLaber.node.setAnchorPoint(this.node.anchorX, this.node.anchorY);
        }
    }

    onDestroy() {
        this.unscheduleAllCallbacks();
    }

    refresh(text: string) {
        if (!this.runLaber || !text) {
            return;
        }
        this.runLaber.string = text;
        this.scheduleOnce(this.viewHandler, 0.1);
    }

    private viewHandler() {
        this._labelWidth = this.runLaber.node.width;
        this._maskWidth = this.node.width;

        if (this._maskWidth >= this._labelWidth) {
            // 没有超出父容器长度的时候不使用跑马灯
            this.refreshNormalView();
        } else {
            // 超出父容器长度，开启跑马灯模式
            this._moveDistance = this._maskWidth + this._labelWidth;
            if (this._mask) this._mask.enabled = true;
            this._targetPosX = this.targetActionPoint();
            // this.resetAcionPoint();
            this.schedule(this.labelMoving, 0.01);
        }
    }

    private refreshNormalView() {
        this.unscheduleAllCallbacks();
        if (this._mask) this._mask.enabled = false;

        // because of the prefabs node structure and layout setting, 
        // reset runLaber.node.y can cause the lable is not in vertical center
        //this.runLaber.node.setPosition(cc.Vec2.ZERO);
        
        this.runLaber.node.x = 0;
    }

    private resetAcionPoint() {
        let anchorX = this.node.getAnchorPoint().x;
        this.runLaber.node.x = this._maskWidth * (1 - anchorX) + this._labelWidth * anchorX;
    }

    private targetActionPoint(): number {
        let anchorX = this.node.getAnchorPoint().x;
        let startPos = this._maskWidth * (1 - anchorX) + this._labelWidth * anchorX;
        return startPos - this._moveDistance;
    }

    private labelMoving() {
        this.runLaber.node.x -= 2;
        if (this.runLaber.node.x < this._targetPosX) {
            this.resetAcionPoint();
        }
    }
}
