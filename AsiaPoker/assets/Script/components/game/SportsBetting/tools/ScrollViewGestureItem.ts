interface IScrollViewGestureItem {
    onFocus(): void;
    onNotInFocus(): void;
}

export abstract class ScrollViewGestureItem extends cc.Component implements IScrollViewGestureItem {
    onFocus(): void {
        throw new Error('ScrollViewGestureItem :: onFocus must be defined!');
    }
    
    onNotInFocus(): void {
        throw new Error('ScrollViewGestureItem :: onNotInFocus must be defined!');
    }

    setupInteractivityToggleCallback(callback: (value: boolean) => void): void {
        throw new Error('ScrollViewGestureItem :: setupInteractivityToggleCallback must be defined!');
    }
}
