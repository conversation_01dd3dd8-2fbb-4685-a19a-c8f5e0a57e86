/* eslint-disable max-classes-per-file */

import { sportsBettingTipsType } from '../../../common/tools/Enum';
import cv from '../../lobby/cv';
import ws_protocol = require('./../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;

const { ccclass, property } = cc._decorator;

export interface ISportNoticeData {
    moreOneHoursStr: string;
    lessOneHoursStr: string;
    startStr: string;
    sportStart: number;
    cdTime: number;
    showTimes: number;
}

class SportNoticeData implements ISportNoticeData {
    moreOneHoursStr: string;
    lessOneHoursStr: string;
    startStr: string;
    sportStart: number;
    cdTime: number;
    showTimes: number;
}

const THIRTY_MINUTES_MS = 30 * 60 * 1000;
const SIX_HOURS_MS = 6 * 60 * 60 * 1000;
const TEN_MINUTES_MS = 10 * 60 * 1000;
const TWENTY_MINUTES_MS = 20 * 60 * 1000;

@ccclass
export default class SportsBettingTipsManager extends cc.Component {
    @property(cc.Node)
    tipsPanel: cc.Node = null;

    @property(cc.Label)
    tipsLabel: cc.Label = null;

    @property(cc.RichText)
    tipsRichText: cc.RichText = null;

    _sportNoticeData: SportNoticeData;

    _displayCount: number = 0;
    _lastTipsShownTime: number = 0;
    _tipType: sportsBettingTipsType = 1;
    _prioritizedMatch: world_pb.ISportsMatchData = null;
    _currentTip: world_pb.TipTemplate = null;
    _currentTips: world_pb.DeskSportNoticeBase = null;

    _isInit: boolean = true;
    
    private _nextTipTime: number = 0;

    setMatchesData(matches: world_pb.ISportsMatchData[]): void {
        this._prioritizedMatch = null;
        matches.forEach((thisMatch) => {
            if (this._prioritizedMatch === null) {
                // Assign first match if the object is null
                this._prioritizedMatch = thisMatch;
            }
            // Check if other match has closet kickofftime than current selected; if yes then prioritize that match data
            if (this._prioritizedMatch.kickOffTime > thisMatch.kickOffTime) {
                this._prioritizedMatch = thisMatch;
            }
        });

        if (this._prioritizedMatch === null) return;

        const matchKickOffTime = this._prioritizedMatch.kickOffTime * 1000;
        if (Date.now() > matchKickOffTime) {
            // Match already started
            this._tipType = sportsBettingTipsType.afterTip;
        } else if (this._getCurrentMinuteDiffernce(matchKickOffTime) > 30) {
            // If match will begin after 30 minutes
            this._tipType = sportsBettingTipsType.loginTip;
        } else {
            this._tipType = sportsBettingTipsType.beforeTip;
        }
        // Request for Sports Betting tips data
        // cv.worldNet.requestSportsbettingTips(); // temporarily disable
        // cv.worldNet.requestSportsbettingTipsV2(); // disable for new requirement
    }

    shouldHideTips(): void {
        this._hideTips();
    }

    protected onEnable(): void {
        this.tipsPanel.active = false;
        this._registerEvent();
    }

    protected onDisable(): void {
        this._unregisterEvent();
        this.unscheduleAllCallbacks();
    }

    _registerEvent(): void {
        // cv.MessageCenter.register(
        //     'OnSportsBettingTipsResponse',
        //     this._onSportsBettingTipsResponse.bind(this),
        //     this.node
        // );

        cv.MessageCenter.register(
            'OnSportsBettingTipsResponseV2',
            this._onSportsBettingTipsResponseV2.bind(this),
            this.node
        );

        cv.MessageCenter.register('on_game_endround_noti', this._onRoundEnd.bind(this), this.node);
        cv.MessageCenter.register('StartGame', this._hideTips.bind(this), this.node);
        cv.MessageCenter.register('ShowSportsTipsOnFold', this._onPlayerHasFold.bind(this), this.node);
        // this.tipsLabel.node.parent.on(cc.Node.EventType.TOUCH_END, this._hideTips.bind(this));
        this.tipsRichText.node.parent.on(cc.Node.EventType.TOUCH_END, this._hideTips.bind(this));
    }

    _unregisterEvent(): void {
        // cv.MessageCenter.unregister('OnSportsBettingTipsResponse', this.node);
        cv.MessageCenter.unregister('OnSportsBettingTipsResponseV2', this.node);
        cv.MessageCenter.unregister('on_game_endround_noti', this.node);
        cv.MessageCenter.unregister('ShowSportsTipsOnFold', this.node);
        cv.MessageCenter.unregister('StartGame', this.node);
        // this.tipsLabel.node.parent.off(cc.Node.EventType.TOUCH_END, this._hideTips.bind(this));
        this.tipsRichText.node.parent.off(cc.Node.EventType.TOUCH_END, this._hideTips.bind(this));
    }

    _onDeskSportNotify(event) {
        const data = event.getUserData().msgBody;
        this._sportNoticeData = new SportNoticeData();

        const { moreOneHoursStr, lessOneHoursStr, startStr, sportStart, cdTime, showTimes } = data;
        this._sportNoticeData.moreOneHoursStr = moreOneHoursStr;
        this._sportNoticeData.lessOneHoursStr = lessOneHoursStr;
        this._sportNoticeData.startStr = startStr;
        this._sportNoticeData.sportStart = sportStart;
        this._sportNoticeData.cdTime = cdTime;
        this._sportNoticeData.showTimes = showTimes;
    }

    // Sets Current tip data based on analysed match & TipType
    _onSportsBettingTipsResponse(msg: world_pb.SportsTipSetting): void {
        if (this._prioritizedMatch === null) return;
        this._currentTip = null;
        if (this._tipType === sportsBettingTipsType.afterTip) {
            this._currentTip = msg.afterStartTemplate as world_pb.TipTemplate;
        } else if (this._tipType === sportsBettingTipsType.loginTip) {
            this._currentTip = msg.loginTemplate as world_pb.TipTemplate;
        } else {
            this._currentTip = msg.beforeStartTemplate as world_pb.TipTemplate;
        }
    }

    _onSportsBettingTipsResponseV2(msg: world_pb.DeskSportNoticeBase): void {
        this._currentTips = msg;

        if (this._isInit) {
            this._isInit = false;
            this._showTipsV2();
        }
    }

    _onRoundEnd() {
        this.scheduleOnce(this._hideTips, 1);
    }

    _onPlayerHasFold() {
        const isTipsEnabled =
            cv.GameDataManager.tRoomData.u32GameID !== cv.Enum.GameId.StarSeat &&
            cv.tools.isSportsBettingToggle() &&
            cv.tools.isSportsBettingBackendEnabled();

        if (isTipsEnabled) {
            if (!this._currentTips){
                this._initTips();
            }
            else{
                this._showTipsV2();
            }
        }
        else this._hideTips();
    }

    _initTips() {
        this._isInit = true;
        cv.worldNet.requestSportsbettingTipsV2();
    }

    _showTips() {
        this._displayCount++;
        this._lastTipsShownTime = Date.now();

        let tipString =
            cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN
                ? this._currentTip.zh_CN
                : this._currentTip.en_US;

        // Processing currentTip with time as per Tip Type
        const kickOffTime = this._prioritizedMatch.kickOffTime * 1000;

        if (this._tipType === sportsBettingTipsType.loginTip) {
            const date = new Date(kickOffTime);
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hour = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const dateString = `${month}/${day}`;
            const timeString = ` ${hour}:${minutes}`;
            tipString = tipString.replace('{$date}', dateString);
            tipString = tipString.replace('{$time}', timeString);
        } else {
            const diff_Minutes = this._getCurrentMinuteDiffernce(kickOffTime);
            tipString = tipString.replace('{$min}', diff_Minutes.toString());
        }

        // Processing currentTip with teams' names.
        tipString = tipString.replace(
            '{$teamA}',
            cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN
                ? this._prioritizedMatch.homeTeamName
                : this._prioritizedMatch.homeTeamNameEn
        );
        tipString = tipString.replace(
            '{$teamB}',
            cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN
                ? this._prioritizedMatch.awayTeamName
                : this._prioritizedMatch.awayTeamNameEn
        );

        this.tipsLabel.string = tipString;
        this.tipsPanel.active = true;
        this.scheduleOnce(this._hideTips, 7);
    }

    _showTipsV2() {
        if (Date.now() < this._nextTipTime) return;
        
        const { sportStart, moreOneHoursStr, lessOneHoursStr, startStr, cdTime } = this._currentTips;
        const time = new Date().getTime();
        const timeLeft = sportStart - time;
        let tipString = '';

        // Convert time intervals to milliseconds for comparison

        const { minutes } = this._getHoursAndMinutesBetween(time, sportStart);

        if (timeLeft >= THIRTY_MINUTES_MS && timeLeft <= SIX_HOURS_MS) {
            // timeLeft is between 30 minutes to 6 hours
            const date = new Date(sportStart);
            const hour = String(date.getHours()).padStart(2, '0');
            const minute = String(date.getMinutes()).padStart(2, '0');
            tipString = moreOneHoursStr.replace('${hours}', `${hour}:${minute}`);
        } else if (timeLeft < TEN_MINUTES_MS && timeLeft >= 0) {
            // timeLeft is less than 10 minutes
            tipString = lessOneHoursStr.replace('${minutes}', minutes.toString());
        } else if (timeLeft < 0 && timeLeft <= -TEN_MINUTES_MS && timeLeft >= -TWENTY_MINUTES_MS) {
            // timeLeft is a negative value between -10 to -20 minutes
            tipString = startStr.replace('${minutes}', Math.abs(minutes).toString());
        } else {
            // Default case if none of the above conditions are met
            tipString = '';
        }

        if (tipString !== '') {
            this.tipsRichText.string = tipString;
            this.tipsPanel.active = true;
            this.scheduleOnce(this._hideTips, 7);
            this._nextTipTime = Date.now() + (cdTime * 1000);
        }
    }

    _hideTips() {
        this.tipsPanel.active = false;
        this.unschedule(this._hideTips);
    }

    _getCurrentMinuteDiffernce(kickOffTime: number): number {
        const difference = Math.abs(Date.now() - kickOffTime);
        let diff_Minutes = difference / 60000; // Converting miliseconds to Minutes
        diff_Minutes = Math.floor(diff_Minutes);
        return diff_Minutes;
    }

    _checkIfTipsCanBeShown(): boolean {
        if (
            this._displayCount < this._currentTip.displayCount &&
            this._currentTip.intervalTime <= this._getCurrentMinuteDiffernce(this._lastTipsShownTime)
        ) {
            return true;
        }
        return false;
    }

    _getHoursAndMinutesBetween(startTimeStamp: number, endTimeStamp: number) {
        const diffMilliSeconds = endTimeStamp - startTimeStamp;

        const diffHours = Math.floor(diffMilliSeconds / (1000 * 60 * 60));
        const diffMinutes = Math.floor((diffMilliSeconds % (1000 * 60 * 60)) / (1000 * 60));

        return { hours: diffHours, minutes: diffMinutes };
    }
}
