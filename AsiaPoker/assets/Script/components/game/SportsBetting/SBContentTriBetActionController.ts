import cv from '../../lobby/cv';
import SBContentBetActionController, { SBBetActionButton } from './SBContentBetActionController';
import { ISBMarketData, ISBSelectionData } from './SportsBettingPopupPanelManager';

const { ccclass, property } = cc._decorator;

const BET_ACTION_BUTTON_INACTIVE_OPACITY = 127.5;

@ccclass
export default class SBContentTriBetActionController extends SBContentBetActionController {
    @property(cc.Node)
    winActionNode: cc.Node = null;

    @property(cc.Node)
    drawActionNode: cc.Node = null;

    @property(cc.Node)
    loseActionNode: cc.Node = null;

    @property(cc.Sprite)
    winActionSprite: cc.Sprite = null;

    @property(cc.Sprite)
    drawActionSprite: cc.Sprite = null;

    @property(cc.Sprite)
    loseActionSprite: cc.Sprite = null;

    @property(cc.Node)
    winActionLabelNode: cc.Node = null;

    @property(cc.Node)
    drawActionLabelNode: cc.Node = null;

    @property(cc.Node)
    loseActionLabelNode: cc.Node = null;

    @property(cc.Node)
    winActionBetMultiplierLabelNode: cc.Node = null;

    @property(cc.Node)
    drawActionBetMultiplierLabelNode: cc.Node = null;

    @property(cc.Node)
    loseActionBetMultiplierLabelNode: cc.Node = null;

    @property(cc.Label)
    winActionBetMultiplierLabel: cc.Label = null;

    @property(cc.Label)
    drawActionBetMultiplierLabel: cc.Label = null;

    @property(cc.Label)
    loseActionBetMultiplierLabel: cc.Label = null;

    mapData(data: ISBMarketData): void {
        super.mapData(data);
        this.setupWinActionBetMultiplierLabel(this._selectionData[0].price);
        this.setupDrawActionBetMultiplierLabel(this._selectionData[1].price);
        this.setupLoseActionBetMultiplierLabel(this._selectionData[2].price);
    }

    toggleButtonInteractivity(value: boolean, useEffect: boolean = false): void {
        this._buttonsAreDisabled = !value;
        if (!useEffect) return;
        if (value) {
            this.winActionSprite.node.opacity = 255;
            this.drawActionSprite.node.opacity = 255;
            this.loseActionSprite.node.opacity = 255;
        } else {
            this.winActionSprite.node.opacity = BET_ACTION_BUTTON_INACTIVE_OPACITY;
            this.drawActionSprite.node.opacity = BET_ACTION_BUTTON_INACTIVE_OPACITY;
            this.loseActionSprite.node.opacity = BET_ACTION_BUTTON_INACTIVE_OPACITY;
        }
    }

    protected setupWinActionBetMultiplierLabel(value: number): void {
        this.winActionBetMultiplierLabel.string = cv.StringTools.formatC(
            cv.config.getStringData('Sports_Bettings_Bet_Action_Multiplier'),
            value.toString()
        );
    }

    protected setupDrawActionBetMultiplierLabel(value: number): void {
        this.drawActionBetMultiplierLabel.string = cv.StringTools.formatC(
            cv.config.getStringData('Sports_Bettings_Bet_Action_Multiplier'),
            value.toString()
        );
    }

    protected setupLoseActionBetMultiplierLabel(value: number): void {
        this.loseActionBetMultiplierLabel.string = cv.StringTools.formatC(
            cv.config.getStringData('Sports_Bettings_Bet_Action_Multiplier'),
            value.toString()
        );
    }

    protected onBetActionButtonClicked(actionButton: SBBetActionButton): void {
        super.onBetActionButtonClicked(actionButton);

        if (this._buttonsAreDisabled) return;

        let selectionData: ISBSelectionData;
        switch (actionButton) {
            case SBBetActionButton.WIN:
                this.winActionSprite.spriteFrame = this.onSelectedSpriteFrame;
                this.winActionLabelNode.color = this.onSelectedColor;
                this.winActionBetMultiplierLabelNode.color = this.onSelectedColor;
                selectionData = this._selectionData[0];
                break;
            case SBBetActionButton.DRAW:
                this.drawActionSprite.spriteFrame = this.onSelectedSpriteFrame;
                this.drawActionLabelNode.color = this.onSelectedColor;
                this.drawActionBetMultiplierLabelNode.color = this.onSelectedColor;
                selectionData = this._selectionData[1];
                break;
            case SBBetActionButton.LOSE:
                this.loseActionSprite.spriteFrame = this.onSelectedSpriteFrame;
                this.loseActionLabelNode.color = this.onSelectedColor;
                this.loseActionBetMultiplierLabelNode.color = this.onSelectedColor;
                selectionData = this._selectionData[2];
                break;
            default:
                throw new Error('SBContentTriBetActionController :: onBetActionButtonClicked : default case failure!');
        }

        this._selectedKey = selectionData.key;
        this._selectedPrice = selectionData.price;
        this._selectedPoint = selectionData.point;
    }

    protected resetActionButtonVisualState(): void {
        this.winActionSprite.spriteFrame = this.onUnselectedSpriteFrame;
        this.drawActionSprite.spriteFrame = this.onUnselectedSpriteFrame;
        this.loseActionSprite.spriteFrame = this.onUnselectedSpriteFrame;

        this.winActionLabelNode.color = this.onUnselectedColor;
        this.drawActionLabelNode.color = this.onUnselectedColor;
        this.loseActionLabelNode.color = this.onUnselectedColor;

        this.winActionBetMultiplierLabelNode.color = this.onUnselectedColor;
        this.drawActionBetMultiplierLabelNode.color = this.onUnselectedColor;
        this.loseActionBetMultiplierLabelNode.color = this.onUnselectedColor;
    }
}
