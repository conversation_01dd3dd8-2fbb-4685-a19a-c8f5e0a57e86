import { LANGUAGE_TYPE } from '../../../common/tools/Enum';
import cv from '../../lobby/cv';
import { ISBTipsContent } from './SportsBettingPopupPanelManager';
import TextRunnerView from './tools/TextRunnerView';

const { ccclass, property } = cc._decorator;

interface ISBContentTipData {
    leagueName: string;
    leagueNameEn: string;
    tipsContent: ISBTipsContent;
}

@ccclass
export default class SBContentTipController extends cc.Component {
    @property(TextRunnerView)
    titleLabelTextRunner: TextRunnerView = null;

    @property(TextRunnerView)
    tipLabelTextRunner: TextRunnerView = null;

    mapData(data: ISBContentTipData) {
        const { leagueName, leagueNameEn, tipsContent } = data;

        const leagueContent = cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? leagueName : leagueNameEn;
        this.titleLabelTextRunner.refresh(`${leagueContent}`);

        const tipsLabel = this._getTipsContent(tipsContent);
        this.tipLabelTextRunner.node.parent.active = !!tipsLabel;
        this.tipLabelTextRunner.refresh(tipsLabel);
    }

    private _getTipsContent(tipsContent: ISBTipsContent) {
        if (!tipsContent) {
            return '';
        }

        const currentLanguage = cv.config.getCurrentLanguage();
        let tipsContentStr = currentLanguage === LANGUAGE_TYPE.zh_CN ? tipsContent.zh_CH : tipsContent.en_US;
        if (!tipsContentStr || tipsContentStr.trim() === '') {
            tipsContentStr = '';
        }
        return tipsContentStr;
    }
}
