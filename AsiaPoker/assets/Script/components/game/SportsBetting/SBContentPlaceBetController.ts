import cv from "../../lobby/cv";

const { ccclass, property } = cc._decorator;

const PLACE_BET_INACTIVE_OPACITY = 127;
const PLACING_BET_OPACITY = 127;

@ccclass
export default class SBContentPlaceBetController extends cc.Component {
    @property(cc.SpriteFrame)
    placeBetSpriteFrame: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    placingBetSpriteFrame: cc.SpriteFrame = null;

    @property(cc.Sprite)
    placeBetSprite: cc.Sprite = null;

    @property(cc.Node)
    placeBetNode: cc.Node = null;

    @property(cc.Label)
    placeBetLabel: cc.Label = null;

    @property(cc.Color)
    onSelectedColor: cc.Color = null;

    @property(cc.Color)
    onUnselectedColor: cc.Color = null;

    togglePlaceBetInteractivity(value: boolean): void {
        this.placeBetLabel.string = cv.config.getStringData('Sports_Bettings_Content_Item_Place_Bet');

        if (value) {
            this.placeBetNode.opacity = 255;
            this.placeBetLabel.node.color = this.onSelectedColor;
            this.placeBetSprite.spriteFrame = this.placeBetSpriteFrame;
        } else {
            this.placeBetNode.opacity = PLACE_BET_INACTIVE_OPACITY;
            this.placeBetSprite.spriteFrame = this.placingBetSpriteFrame;
            this.placeBetLabel.node.color = this.onUnselectedColor;
        }
    }

    switchToPlacingBetStatus(): void {
        this.placeBetLabel.node.color = this.onUnselectedColor;
        this.placeBetSprite.spriteFrame = this.placingBetSpriteFrame;
        this.placeBetNode.opacity = PLACING_BET_OPACITY;
        this.placeBetLabel.string = cv.config.getStringData('Sports_Bettings_Content_Item_Placing_Bet');
    }
}
