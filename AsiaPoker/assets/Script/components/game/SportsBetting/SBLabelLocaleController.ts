import cv from '../../lobby/cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class SBLabelLocaleController extends cc.Component {
    @property([cc.Label])
    winActionLabels: cc.Label[] = [];

    @property(cc.Label)
    drawActionLabel: cc.Label = null;

    @property([cc.Label])
    loseActionLabels: cc.Label[] = [];

    @property(cc.Label)
    clearBetLabel: cc.Label = null;

    @property(cc.Label)
    balanceTextLabel: cc.Label = null;

    @property([cc.Label])
    currencyTextLabels: cc.Label[] = [];

    @property(cc.Label)
    winningTextLabel: cc.Label = null;

    @property(cc.Label)
    placeBetLabel: cc.Label = null;

    @property(cc.Label)
    moreInfoLabel: cc.Label = null;

    start() {
        this.winActionLabels.forEach((e) => {
            e.string = cv.config.getStringData('Sports_Bettings_Content_Item_Action_Win');
        });
        this.drawActionLabel.string = cv.config.getStringData('Sports_Bettings_Content_Item_Action_Draw');
        this.loseActionLabels.forEach((e) => {
            e.string = cv.config.getStringData('Sports_Bettings_Content_Item_Action_Lose');
        });
        this.clearBetLabel.string = cv.config.getStringData('Sports_Bettings_Content_Item_Bet_Clear');
        this.balanceTextLabel.string = cv.config.getStringData('Sports_Bettings_Content_Item_Balance_Fund');
        this.currencyTextLabels.forEach((e) => {
            e.string = cv.config.getStringData('Sports_Bettings_Balance_Currency');
        });
        this.winningTextLabel.string = cv.config.getStringData('Sports_Bettings_Content_Item_Winnings_Fund');
        this.placeBetLabel.string = cv.config.getStringData('Sports_Bettings_Content_Item_Place_Bet');
        this.moreInfoLabel.string = cv.config.getStringData('Sports_Bettings_Content_Item_More_Bet_Options_Info');
    }

}
