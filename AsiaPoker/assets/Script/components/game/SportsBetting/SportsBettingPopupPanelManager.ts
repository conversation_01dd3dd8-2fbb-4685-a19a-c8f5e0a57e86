/* eslint-disable max-classes-per-file */
import SportsBettingContentItem from './SportsBettingContentItem';
import ScrollViewGesture from './tools/ScrollViewGesture';

const { ccclass } = cc._decorator;

export interface ISBTipsContent {
    zh_CH: string;
    en_US: string;
}

export interface ISBContentData {
    sportType: number;
    matchId: number;
    kickOffTime: number;
    leagueName: string;
    leagueNameEn: string;
    homeTeamName: string;
    homeTeamNameEn: string;
    homeTeamIcon: string;
    awayTeamName: string;
    awayTeamNameEn: string;
    awayTeamIcon: string;
    tipsContent: ISBTipsContent;
    markets: ISBMarketData[];
}

export interface ISBMarketData {
    betType: number;
    marketId: number;
    marketStatus: string;
    selections: ISBSelectionData[];
}

export interface ISBSelectionData {
    key: string;
    price: number;
    point: number;
    pointStr: string;
}

class SBSelectionData implements ISBSelectionData {
    key: string;
    price: number;
    point: number;
    pointStr: string;
}
class SBMarketData implements ISBMarketData {
    betType: number;
    marketId: number;
    marketStatus: string;
    selections: SBSelectionData[];
}
class SBContentData implements ISBContentData {
    sportType: number;
    matchId: number;
    kickOffTime: number;
    leagueName: string;
    leagueNameEn: string;
    homeTeamName: string;
    homeTeamNameEn: string;
    homeTeamIcon: string;
    awayTeamName: string;
    awayTeamNameEn: string;
    awayTeamIcon: string;
    tipsContent: ISBTipsContent;
    markets: SBMarketData[];
}
@ccclass
export default class SportsBettingPopupPanelManager extends ScrollViewGesture {
    _isInit = true;

    _sportsBettingContentItems: SportsBettingContentItem[] = [];

    _sbContentDataArr: ISBContentData[] = [];
    _lastMarketIdArr: number[] = [];

    _onRefreshMatchListCallback: () => void;

    onDestroy(): void {
        super.onDestroy();
    }

    onEnter(): void {
        console.warn('on enter sports popup');
        if (this._isInit) {
            this._isInit = false;
            this._populateItems(this._sbContentDataArr.length);
            this._mapItemData();
            this._setupCallbackOnItems();
            // Wait a frame to reset the scrollview in case the it may work before items pupulate in low end CPU
            this.scheduleOnce(() => {
                this.resetToFirstElement();
                this.updateItemFocusVisual(0);
            }, 0);
        } else {
            this._mapItemData();
        }
    }

    onExit(): void {
        console.warn('exit pop up');
    }

    setData(matches): void {
        this._sbContentDataArr = [];

        const marketIdArr = [];

        matches.forEach((e) => {
            const data = new SBContentData();

            data.sportType = e.sportType;
            data.matchId = e.matchId;
            data.kickOffTime = e.kickOffTime;
            data.leagueName = e.leagueName;
            data.leagueNameEn = e.leagueNameEn;
            data.homeTeamName = e.homeTeamName;
            data.homeTeamNameEn = e.homeTeamNameEn;
            data.homeTeamIcon = e.homeTeamIcon;
            data.awayTeamName = e.awayTeamName;
            data.awayTeamNameEn = e.awayTeamNameEn;
            data.awayTeamIcon = e.awayTeamIcon;
            data.tipsContent = e.tipsContent;
            data.markets = [];

            e.markets.forEach((f) => {
                const marketData: SBMarketData = new SBMarketData();
                marketData.betType = f.betType;
                marketData.marketId = f.marketId;
                marketData.marketStatus = f.marketStatus;
                marketData.selections = f.selections as unknown as ISBSelectionData[];

                data.markets.push(marketData);
                marketIdArr.push(marketData.marketId);
            });

            this._sbContentDataArr.push(data);
        });

        // console.error('marketIdArr');
        // console.error(marketIdArr);
        // console.error('this._lastMarketIdArr');
        // console.error(this._lastMarketIdArr);

        if (!this._arraysEqual(marketIdArr, this._lastMarketIdArr)) {
            // console.error('no equal array market id');
            this._isInit = true;
            this.despawnItems();
        }

        this._lastMarketIdArr = marketIdArr.slice();
    }

    setupOnRefreshMatchListCallback(callback: () => void): void {
        this._onRefreshMatchListCallback = callback;
    }

    _callbackOnInteractivityToggle(value: boolean): void {
        this.toggleInteractivity(value);
    }

    _populateItems(count: number): void {
        this.spawnItems(count);
        this.calculateContentsOffset(count);

        this._sportsBettingContentItems = this._contentItemNodes.map((e) => e.getComponent(SportsBettingContentItem));
    }

    _mapItemData(): void {
        this._sportsBettingContentItems.forEach((e, i) => {
            const {
                sportType,
                matchId,
                kickOffTime,
                leagueName,
                leagueNameEn,
                homeTeamName,
                homeTeamNameEn,
                homeTeamIcon,
                awayTeamName,
                awayTeamNameEn,
                awayTeamIcon,
                tipsContent,
                markets
            } = this._sbContentDataArr[i];
            e.mapContentData({
                sportType,
                matchId,
                kickOffTime,
                leagueName,
                leagueNameEn,
                homeTeamName,
                homeTeamNameEn,
                homeTeamIcon,
                awayTeamName,
                awayTeamNameEn,
                awayTeamIcon,
                tipsContent,
                markets
            });
        });
    }

    _setupCallbackOnItems(): void {
        this._sportsBettingContentItems.forEach((e) => {
            e.setupInteractivityToggleCallback(this._callbackOnInteractivityToggle.bind(this));
            e.setupOnRefreshMatchListCallback(this._onRefreshMatchListCallback.bind(this));
        });
    }

    _arraysEqual<T>(a: Array<T>, b: Array<T>) {
        if (a === b) return true;
        if (a == null || b == null) return false;
        if (a.length !== b.length) return false;

        for (let i = 0; i < a.length; ++i) {
            if (a[i] !== b[i]) return false;
        }
        return true;
    }
}
