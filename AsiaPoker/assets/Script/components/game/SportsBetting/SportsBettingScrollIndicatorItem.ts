import borderGraphic from '../../../common/tools/borderGraphic';
import { ScrollIndicatorItem } from './tools/ScrollIndicatorItem';

const { ccclass, property } = cc._decorator;

@ccclass
export default class SportsBettingScrollIndicatorItem extends ScrollIndicatorItem {
    @property(cc.color)
    activeColor: cc.Color = cc.Color.WHITE;

    @property(cc.color)
    inactiveColor: cc.Color = cc.Color.WHITE;

    @property(borderGraphic)
    indicatorBorderGraphic: borderGraphic = null;

    toggleIndicator(value: boolean) {
        this.indicatorBorderGraphic.fillColor = value ? this.activeColor : this.inactiveColor;
    }
}
