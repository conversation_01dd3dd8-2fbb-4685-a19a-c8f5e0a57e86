import cv from '../../lobby/cv';
import ws_protocol = require('./../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import SportsBettingPopupPanelManager from './SportsBettingPopupPanelManager';
import SportsBettingTipsManager from './SportsBettingTipsManager';

const { ccclass, property } = cc._decorator;

const CACHE_VALIDITY_DURATION_SECOND = 20;
@ccclass
export default class SportsBettingManager extends cc.Component {
    @property(cc.Node)
    sportsBettingButtonNode: cc.Node = null;

    @property(cc.Node)
    sportsBettingPopupPanelNode: cc.Node = null;

    @property(cc.Node)
    sportsBettingExitButtonNode: cc.Node = null;

    @property(SportsBettingPopupPanelManager)
    sportBettingsPopUpPanelManager: SportsBettingPopupPanelManager = null;

    @property(SportsBettingTipsManager)
    sportsBettingTipsManager: SportsBettingTipsManager = null;

    @property(cc.Sprite)
    sportsBettingIconTextSprite: cc.Sprite = null;

    @property(cc.SpriteFrame)
    sportsBettingIconENTextSpriteFrame: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    sportsBettingIconZHTextSprite: cc.SpriteFrame = null;

    _isInit = true;
    _isRequesting = false;

    _cacheMatchesData: world_pb.ISportsMatchData[] = [];
    _validCacheTime: number;

    protected onLoad(): void {
        this._init();
    }

    protected onDestroy(): void {
        this._removeEventListener();
    }

    _init() {
        this._toggleSportsBettingNode();
        this.sportsBettingPopupPanelNode.active = false;

        this.sportsBettingIconTextSprite.spriteFrame =
            cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN
                ? this.sportsBettingIconZHTextSprite
                : this.sportsBettingIconENTextSpriteFrame;

        this._updateOverlaySize();
        this._setupEventListener();

        this.sportBettingsPopUpPanelManager.setupOnRefreshMatchListCallback(this._requestSportMatchList.bind(this));
        this._requestSportMatchList();
    }

    _toggleSportsBettingNode(): void {
        this.sportsBettingButtonNode.active =
            cv.GameDataManager.tRoomData.u32GameID !== cv.Enum.GameId.StarSeat &&
            cv.tools.isSportsBettingToggle() &&
            cv.tools.isSportsBettingBackendEnabled();
    }

    _updateOverlaySize(): void {
        this.sportsBettingExitButtonNode.width = cc.Canvas.instance.node.width;
        this.sportsBettingExitButtonNode.height = cc.Canvas.instance.node.height * 2;
    }

    _setupEventListener(): void {
        this.sportsBettingButtonNode.on(cc.Node.EventType.TOUCH_END, this._onSportsBettingButtonClicked.bind(this));
        this.sportsBettingExitButtonNode.on(
            cc.Node.EventType.TOUCH_END,
            this._onExitSportsBettingButtonClicked.bind(this)
        );

        cv.MessageCenter.register('sportMatchListResponse', this._onSportMatchListResponse.bind(this), this.node);
        cv.MessageCenter.register('onSportsBettingSwitch', this._toggleSportsBettingNode.bind(this), this.node);
        cv.MessageCenter.register('onSportsBettingBackendEnabled', this._toggleSportsBettingNode.bind(this), this.node);
    }

    _removeEventListener(): void {
        this.sportsBettingButtonNode.off(cc.Node.EventType.TOUCH_END, this._onSportsBettingButtonClicked.bind(this));

        cv.MessageCenter.unregister('sportMatchListResponse', this.node);
        cv.MessageCenter.unregister('onSportsBettingSwitch', this.node);
        cv.MessageCenter.unregister('onSportsBettingBackendEnabled', this.node);
    }

    _onSportMatchListResponse(msg: any) {
        console.error('_onSportMatchListResponse');
        const matches = msg as world_pb.ISportsMatchData[];

        this._cacheMatchesData = matches;
        this._validCacheTime = Date.now() + CACHE_VALIDITY_DURATION_SECOND * 1000;

        console.warn(matches);
        // map into items
        this.sportBettingsPopUpPanelManager.setData(matches);
        this.sportsBettingTipsManager.setMatchesData(matches);

        // use for initial load to cache data for tips
        if (this._isInit) {
            this._isInit = false;
            return;
        }

        this.sportsBettingPopupPanelNode.active = true;
        this.sportBettingsPopUpPanelManager.node.scale = 0; // Make the animation for the popup a smooth transition
        cc.tween(this.sportBettingsPopUpPanelManager.node)
            .to(0.3, { scale: 1 }, { easing: 'backOut' })
            .start();

        if (matches.length) {
            this.sportBettingsPopUpPanelManager.onEnter();
        } else {
            cv.TT.showMsg(
                cv.config.getStringData('Sports_Bettings_No_Matches_Found'),
                cv.Enum.ToastType.ToastTypeError
            );
            this.sportsBettingPopupPanelNode.active = false;
        }

        this.unschedule(this._clearRequestState.bind(this));
    }

    _onSportsBettingButtonClicked(): void {
        if (this.sportsBettingPopupPanelNode.active) {
            return;
        }
        if (this._isRequesting && !this._cacheMatchesData?.length) {
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode1003'), cv.Enum.ToastType.ToastTypeError);
            return;
        }

        this._requestSportMatchList();
        this.sportsBettingTipsManager.shouldHideTips();
    }

    _onExitSportsBettingButtonClicked(): void {
        if (this.sportsBettingPopupPanelNode.active) {
            this.sportBettingsPopUpPanelManager.onExit();
            this.sportsBettingPopupPanelNode.active = false;
        } else {
            console.error('cant exit');
        }
    }

    _requestSportMatchList(): void {
        if (this._cacheMatchesData && Date.now() < this._validCacheTime) {
            cv.MessageCenter.send('sportMatchListResponse', this._cacheMatchesData);
            return;
        }

        this._isRequesting = true;
        // cv.worldNet.requestSportMatchList(); // temporarily disable
        cv.worldNet.requestSportMatchListV2();

        this.scheduleOnce(this._clearRequestState.bind(this), 3);
    }

    _clearRequestState() {
        this._isRequesting = false;
    }
}
