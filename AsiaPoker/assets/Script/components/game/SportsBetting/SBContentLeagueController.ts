import cv from '../../lobby/cv';
import TextRunnerView from './tools/TextRunnerView';

const { ccclass, property } = cc._decorator;

@ccclass
export default class SBContentLeagueController extends cc.Component {
    @property(cc.Label)
    leagueLabel: cc.Label = null;

    @property(TextRunnerView)
    leagueLabelTextRunner: TextRunnerView = null;

    protected onLoad(): void {
        const leagueContent = cv.config.getStringData('Sports_Bettings_Content_Item_Title');
        this.leagueLabel.string = leagueContent;
        this.leagueLabelTextRunner.refresh(leagueContent);
    }
}
