import ws_protocol = require('./../../../common/pb/ws_protocol');
import world_pb = ws_protocol.pb;
import SBContentBetActionController, { SBBetActionButton } from './SBContentBetActionController';
import SBContentBetAmountController from './SBContentBetAmountController';
import SBContentDualBetActionController from './SBContentDualBetActionController';
import SBContentLeagueController from './SBContentLeagueController';
import SBContentPlaceBetController from './SBContentPlaceBetController';
import SBContentTriBetActionController from './SBContentTriBetActionController';
import SBContentVersusController from './SBContentVersusController';
import SportsBettingPromptPanelManager, {
    SportsBettingFailurePromptInfo,
    SportsBettingPrompt
} from './SportsBettingPromptPanelManager';
import { ScrollViewGestureItem } from './tools/ScrollViewGestureItem';
import { ISBContentData, ISBMarketData } from './SportsBettingPopupPanelManager';
import cv from '../../lobby/cv';
import { LANGUAGE_TYPE } from '../../../common/tools/Enum';
import SBContentTipController from './SBContentTipController';
import SBContentExtraBetActionController from './SBContentExtraBetActionController';

const { ccclass, property } = cc._decorator;

@ccclass
export default class SportsBettingContentItem extends ScrollViewGestureItem {
    @property(SBContentLeagueController)
    sbContentLeagueController: SBContentLeagueController = null;

    @property(SBContentTipController)
    sbContentTipController: SBContentTipController = null;

    @property(SBContentVersusController)
    sbContentVersusController: SBContentVersusController = null;

    @property(SBContentDualBetActionController)
    sbContentDualBetActionController: SBContentDualBetActionController = null;

    @property(SBContentTriBetActionController)
    sbContentTriBetActionController: SBContentTriBetActionController = null;

    @property(SBContentExtraBetActionController)
    sbContentHandicapBetActionController: SBContentExtraBetActionController = null;

    @property(SBContentExtraBetActionController)
    sbContentBigSmallBetActionController: SBContentExtraBetActionController = null;

    @property(SBContentBetAmountController)
    sbContentBetAmountController: SBContentBetAmountController = null;

    @property(SBContentPlaceBetController)
    sbContentPlaceBetController: SBContentPlaceBetController = null;

    @property(cc.Node)
    contentLayoutNode: cc.Node = null;

    @property(cc.Node)
    overlayNode: cc.Node = null;

    @property(cc.Float)
    notInFocusScale = 0;

    @property(cc.Node)
    placeBetNode: cc.Node = null;

    _onRefreshMatchListCallback: () => void;

    _sbContentBetActionController: SBContentBetActionController;

    _callbackOnInteractivityToggle: (value: boolean) => void;
    _callbackOnBetPrompt: (value: SportsBettingPrompt) => void;
    _callbackOnFailurePromptInfo: (config: SportsBettingFailurePromptInfo) => void;

    _contentItemOriginScale = 0;
    _sportBettingContentItem: any;

    _selectedBetAction: SBBetActionButton | undefined = undefined;
    _totalBetAmount: number = 0;

    // payload
    _payLoadSportType: number;
    _payLoadMatchId: number;

    // cache
    _homeTeam: string;
    _awayTeam: string;

    _isPlacingBet: boolean = false;

    onFocus(): void {
        this.node.scale = this._contentItemOriginScale;
        this.overlayNode.active = false;
        this.sbContentBetAmountController.toggleInteractivity(true);
        this._sbContentBetActionController?.toggleButtonInteractivity(true);
        this.sbContentHandicapBetActionController.toggleButtonInteractivity(true);
        this.sbContentBigSmallBetActionController.toggleButtonInteractivity(true);
    }

    onNotInFocus(): void {
        this.node.scale = this.notInFocusScale;
        this.overlayNode.active = true;
        this._resetState();
        this.sbContentBetAmountController.toggleInteractivity(false);
        this._sbContentBetActionController?.toggleButtonInteractivity(false);
        this.sbContentHandicapBetActionController.toggleButtonInteractivity(false);
        this.sbContentBigSmallBetActionController.toggleButtonInteractivity(false);
    }

    setupInteractivityToggleCallback(callback: (value: boolean) => void): void {
        this._callbackOnInteractivityToggle = callback;
    }

    setupBetPromptCallback(callback: (value: SportsBettingPrompt) => void): void {
        this._callbackOnBetPrompt = callback;
    }

    setupFailurePromptInfoCallback(callback: (config: SportsBettingFailurePromptInfo) => void): void {
        this._callbackOnFailurePromptInfo = callback;
    }

    setupOnRefreshMatchListCallback(callback: () => void): void {
        this._onRefreshMatchListCallback = callback;
    }

    mapContentData(data: ISBContentData): void {
        const {
            sportType,
            matchId,
            leagueName,
            leagueNameEn,
            kickOffTime,
            homeTeamName,
            homeTeamNameEn,
            homeTeamIcon,
            awayTeamName,
            awayTeamNameEn,
            awayTeamIcon,
            tipsContent,
            markets
        } = data;
        this.sbContentTipController.mapData({ leagueName, leagueNameEn, tipsContent });
        this.sbContentVersusController.mapData({
            kickOffTime,
            homeTeamName,
            homeTeamNameEn,
            homeTeamIcon,
            awayTeamName,
            awayTeamNameEn,
            awayTeamIcon
        });

        this._mapMarkets(markets);

        // update payload
        this._payLoadSportType = sportType;
        this._payLoadMatchId = matchId;

        // cache
        this._homeTeam = cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? homeTeamName : homeTeamNameEn;
        this._awayTeam = cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? awayTeamName : awayTeamNameEn;
    }

    protected onLoad(): void {
        this._contentItemOriginScale = this.node.scale;
        this._setupListener();
        this.sbContentBetAmountController.setupCallbackOnBetAmountChange(this._onBetAmountChanges.bind(this));
        this._updatePlaceBetButtonVisual();
        this.contentLayoutNode.on(cc.Node.EventType.SIZE_CHANGED, () => {
            this.overlayNode.setContentSize(this.contentLayoutNode.getContentSize());
        });

        this.sbContentDualBetActionController.node.active = false;
        this.sbContentTriBetActionController.node.active = false;
        this.sbContentHandicapBetActionController.node.active = false;
        this.sbContentBigSmallBetActionController.node.active = false;
    }

    protected onDestroy(): void {
        this._removeListener();
    }

    private _mapMarkets(markets: ISBMarketData[]): void {
        markets.forEach((market: ISBMarketData) => {
            const { betType, marketId, selections, marketStatus } = market;
            let sbContentBetActionController = null;
            switch (betType) {
                case 5:
                    sbContentBetActionController = this.sbContentTriBetActionController as SBContentBetActionController;
                    this._sbContentBetActionController = sbContentBetActionController;
                    break;
                case 20:
                    sbContentBetActionController = this
                        .sbContentDualBetActionController as SBContentBetActionController;
                    this._sbContentBetActionController = sbContentBetActionController;
                    break;
                case 1:
                    sbContentBetActionController = this
                        .sbContentHandicapBetActionController as SBContentBetActionController;
                    break;
                case 3:
                    sbContentBetActionController = this
                        .sbContentBigSmallBetActionController as SBContentBetActionController;
                    break;
                default:
                    throw new Error(`SportsBettingContentItem :: _mapMarkets : ${betType} is not supported!`);
            }

            if (sbContentBetActionController) {
                sbContentBetActionController.node.active = true;
                sbContentBetActionController.mapData({ betType, marketId, marketStatus, selections });
            }
        });
    }

    _setupListener(): void {
        this._setupDualBetActionButtonsEvent();
        this._setupTriBetActionButtonsEvent();
        this._setupHandicapBetActionButtonsEvent();
        this._setupBigSmallBetActionButtonsEvent();
        this.placeBetNode.on(cc.Node.EventType.TOUCH_END, this._setupOnPlaceBetButtonClicked.bind(this));
    }

    _removeListener(): void {
        this._clearDualBetActionButtonsEvent();
        this._clearTriBetActionButtonsEvent();
        this._clearHandicapBetActionButtonsEvent();
        this._clearBigSmallBetActionButtonsEvent();
        this.placeBetNode.off(cc.Node.EventType.TOUCH_END, this._setupOnPlaceBetButtonClicked.bind(this));
    }

    // #region click event
    _setupTriBetActionButtonsEvent() {
        this.sbContentTriBetActionController.winActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnWinActionButtonClicked.bind(this)
        );
        this.sbContentTriBetActionController.drawActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnDrawActionButtonClicked.bind(this)
        );
        this.sbContentTriBetActionController.loseActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnLoseActionButtonClicked.bind(this)
        );
    }

    _clearTriBetActionButtonsEvent() {
        const winActionNode = this.sbContentTriBetActionController.winActionNode;

        if (winActionNode)
            winActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnWinActionButtonClicked.bind(this));

        const drawActionNode = this.sbContentTriBetActionController.drawActionNode;

        if (drawActionNode)
            drawActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnDrawActionButtonClicked.bind(this));

        const loseActionNode = this.sbContentTriBetActionController.loseActionNode;

        if (loseActionNode)
            loseActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnLoseActionButtonClicked.bind(this));
    }

    _setupDualBetActionButtonsEvent() {
        this.sbContentDualBetActionController.winActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnWinActionButtonClicked.bind(this)
        );
        this.sbContentDualBetActionController.loseActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnLoseActionButtonClicked.bind(this)
        );
    }

    _clearDualBetActionButtonsEvent() {
        const winActionNode = this.sbContentDualBetActionController.winActionNode;

        if (winActionNode)
            winActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnWinActionButtonClicked.bind(this));

        const loseActionNode = this.sbContentDualBetActionController.loseActionNode;
        if (loseActionNode)
            loseActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnLoseActionButtonClicked.bind(this));
    }

    _setupHandicapBetActionButtonsEvent() {
        this.sbContentHandicapBetActionController.firstActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnHandicapHomeActionButtonClicked.bind(this)
        );
        this.sbContentHandicapBetActionController.secondActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnHandicapAwayActionButtonClicked.bind(this)
        );
    }

    _clearHandicapBetActionButtonsEvent() {
        const firstActionNode = this.sbContentHandicapBetActionController.firstActionNode;
        if (firstActionNode)
            firstActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnHandicapHomeActionButtonClicked.bind(this));

        const secondActionNode = this.sbContentHandicapBetActionController.secondActionNode;
        if (secondActionNode)
            secondActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnHandicapAwayActionButtonClicked.bind(this));
    }

    _setupBigSmallBetActionButtonsEvent() {
        this.sbContentBigSmallBetActionController.firstActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnBetOverActionButtonClicked.bind(this)
        );
        this.sbContentBigSmallBetActionController.secondActionNode.on(
            cc.Node.EventType.TOUCH_END,
            this._setupOnBetUnderActionButtonClicked.bind(this)
        );
    }

    _clearBigSmallBetActionButtonsEvent() {
        const firstActionNode = this.sbContentBigSmallBetActionController.firstActionNode;
        if (firstActionNode)
            firstActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnBetOverActionButtonClicked.bind(this));

        const secondActionNode = this.sbContentBigSmallBetActionController.secondActionNode;
        if (secondActionNode)
            secondActionNode.off(cc.Node.EventType.TOUCH_END, this._setupOnBetUnderActionButtonClicked.bind(this));
    }

    _setupOnWinActionButtonClicked() {
        this._onActionButtonClicked(SBBetActionButton.WIN, this._sbContentBetActionController);
    }

    _setupOnDrawActionButtonClicked() {
        this._onActionButtonClicked(SBBetActionButton.DRAW, this._sbContentBetActionController);
    }

    _setupOnLoseActionButtonClicked() {
        this._onActionButtonClicked(SBBetActionButton.LOSE, this._sbContentBetActionController);
    }

    _setupOnHandicapHomeActionButtonClicked() {
        this._onActionButtonClicked(SBBetActionButton.HOME, this.sbContentHandicapBetActionController);
    }

    _setupOnHandicapAwayActionButtonClicked() {
        this._onActionButtonClicked(SBBetActionButton.AWAY, this.sbContentHandicapBetActionController);
    }

    _setupOnBetOverActionButtonClicked() {
        this._onActionButtonClicked(SBBetActionButton.BIG, this.sbContentBigSmallBetActionController);
    }

    _setupOnBetUnderActionButtonClicked() {
        this._onActionButtonClicked(SBBetActionButton.SMALL, this.sbContentBigSmallBetActionController);
    }

    _onActionButtonClicked(
        sbBetActionButton: SBBetActionButton,
        sbContentBetActionController: SBContentBetActionController
    ) {
        this._resetState();
        sbContentBetActionController.setupOnBetActionButtonClicked(sbBetActionButton);
        this._selectedBetAction = sbBetActionButton;
        const { price } = sbContentBetActionController.getSelectedPayload();
        this.sbContentBetAmountController.setBetMultiplier(price);
        this._updatePlaceBetButtonVisual();
    }

    _setupOnPlaceBetButtonClicked() {
        if (this._selectedBetAction === undefined || this._totalBetAmount === 0) {
            return;
        }

        this.sbContentPlaceBetController.switchToPlacingBetStatus();
        this._dimBetActionButtons(true);

        if (this._callbackOnInteractivityToggle) this._callbackOnInteractivityToggle(false);
        this._requestQuickBet();
    }
    // #endregion

    _onBetAmountChanges(betAmount: number) {
        // check if bet amount is more than 0 before allow bet
        this._totalBetAmount = betAmount;

        // update visual
        this._updatePlaceBetButtonVisual();
    }

    _updatePlaceBetButtonVisual() {
        if (this._selectedBetAction !== undefined && this._totalBetAmount > 0) {
            this.sbContentPlaceBetController.togglePlaceBetInteractivity(true);
        } else {
            this.sbContentPlaceBetController.togglePlaceBetInteractivity(false);
        }
    }

    _resetState(): void {
        this._sbContentBetActionController?.resetState();
        this.sbContentHandicapBetActionController.resetState();
        this.sbContentBigSmallBetActionController.resetState();
        this.sbContentBetAmountController.resetState();
        this._selectedBetAction = undefined;
        this._totalBetAmount = 0;
    }

    // #region payload
    _requestQuickBet() {
        if (this._isPlacingBet) return;
        this._isPlacingBet = true;

        let payload = null;

        switch (this._selectedBetAction) {
            case SBBetActionButton.WIN:
            case SBBetActionButton.LOSE:
            case SBBetActionButton.DRAW:
                payload = this._sbContentBetActionController.getSelectedPayload();
                break;
            case SBBetActionButton.HOME:
            case SBBetActionButton.AWAY:
                payload = this.sbContentHandicapBetActionController.getSelectedPayload();
                break;
            case SBBetActionButton.BIG:
            case SBBetActionButton.SMALL:
                payload = this.sbContentBigSmallBetActionController.getSelectedPayload();
                break;
            default:
                throw new Error(
                    `SportsBettingContentItem :: _requestQuickBet : ${this._selectedBetAction} is not supported!`
                );
        }

        const { marketId, price, point, key } = payload;
        const betAmount = this.sbContentBetAmountController.getBetAmount() * 10;
        // console.warn(`_payLoadSportType: ${this._payLoadSportType}`);
        // console.warn(`_payLoadMatchId: ${this._payLoadMatchId}`);
        // console.warn(`_payLoadMarketId: ${marketId}`);
        // console.warn(`_payLoadPrice: ${price}`);
        // console.warn(`_payLoadPoint: ${point}`);
        // console.warn(`_payLoadKey: ${key}`);
        // console.warn(`_payBetAmount: ${betAmount}`);

        // temporarily disabled due to using v2 (lack of art assets)
        // SportsBettingPromptPanelManager.instance.setupFailureInfo({
        //     homeTeam: this._homeTeam,
        //     awayTeam: this._awayTeam,
        //     betResult: this._selectedBetAction,
        //     multiplierAmount: price,
        //     betAmount
        // });

        cv.MessageCenter.register('responseSportQuickBet', this._onQuickBetResponse.bind(this), this.node);

        cv.worldNet.requestSportQuickBet({
            sportType: this._payLoadSportType,
            matchId: this._payLoadMatchId,
            marketId,
            price,
            point,
            key,
            betAmount
        });
    }

    _onQuickBetResponse(msg: world_pb.SportsQuickBetResponse) {
        this._isPlacingBet = false;

        cv.MessageCenter.unregister('responseSportQuickBet', this.node);
        this._dimBetActionButtons(false);
        if (this._callbackOnInteractivityToggle) this._callbackOnInteractivityToggle(true);
        this._updatePlaceBetButtonVisual();

        console.error('_onQuickBetResponse');
        console.error(msg);
        if (msg.error === 606) {
            console.error('reset card');
            SportsBettingPromptPanelManager.instance.show(SportsBettingPrompt.FAILURE);
            if (this._onRefreshMatchListCallback) this._onRefreshMatchListCallback();
        } else if (msg.error === 1) {
            // restore to current if fail or reset if success
            SportsBettingPromptPanelManager.instance.show(SportsBettingPrompt.SUCCESS);
            // SportsBettingPromptPanelManager.instance.show(SportsBettingPrompt.FAILURE);
            console.error('success bet');
            this._resetState();
        } else {
            console.error('failure');
            SportsBettingPromptPanelManager.instance.show(SportsBettingPrompt.FAILURE);
        }
    }

    private _dimBetActionButtons(value: boolean) {
        this.sbContentHandicapBetActionController.toggleUIOpacityDim(value);
        this.sbContentBigSmallBetActionController.toggleUIOpacityDim(value);

        this.sbContentBetAmountController.toggleButtonInteractivity(!value);
        this._sbContentBetActionController?.toggleButtonInteractivity(!value, true);
        this.sbContentHandicapBetActionController.toggleButtonInteractivity(!value);
        this.sbContentBigSmallBetActionController.toggleButtonInteractivity(!value);
    }
    // #endregion
}
