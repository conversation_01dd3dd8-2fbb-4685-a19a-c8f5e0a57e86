import cv from '../../lobby/cv';

const { ccclass, property } = cc._decorator;

const BET_AMOUNT_1 = 10;
const BET_AMOUNT_2 = 100;
const BET_AMOUNT_3 = 500;

const BET_AMOUNT_BUTTON_INACTIVE_OPACITY = 77;

@ccclass
export default class SBContentBetAmountController extends cc.Component {
    @property(cc.Label)
    totalBetAmountLabel: cc.Label = null;

    @property(cc.Node)
    betAmount1Node: cc.Node = null;

    @property(cc.Node)
    betAmount2Node: cc.Node = null;

    @property(cc.Node)
    betAmount3Node: cc.Node = null;

    @property(cc.Node)
    clearBetNode: cc.Node = null;

    @property(cc.Label)
    balanceAmountLabel: cc.Label = null;

    @property(cc.Label)
    winBetAmountLabel: cc.Label = null;

    @property(cc.Node)
    betAmountPanel: cc.Node = null;

    _totalBetAmount: number = 0;
    _balanceAmount: number = 0;
    _betMultiplier: number = 0;

    _betButton1Clickable = true;
    _betButton2Clickable = true;
    _betButton3Clickable = true;
    _clearButtonClickable = true;
    _enabledInteractivity = true;

    _callbackOnBetAmountChange: (value: number) => void;

    setupCallbackOnBetAmountChange(callback: (value: number) => void): void {
        this._callbackOnBetAmountChange = callback;
    }

    toggleInteractivity(value: boolean): void {
        this._enabledInteractivity = value;
    }

    toggleButtonInteractivity(value: boolean): void {
        if (value) {
            this._clearButtonClickable = true;
            this.clearBetNode.opacity = 255;
            this.betAmountPanel.opacity = 255;
            this._updateBetButtonVisual();
        } else {
            this._betButton1Clickable = false;
            this._betButton2Clickable = false;
            this._betButton3Clickable = false;
            this._clearButtonClickable = false;
            this.betAmount1Node.opacity = BET_AMOUNT_BUTTON_INACTIVE_OPACITY;
            this.betAmount2Node.opacity = BET_AMOUNT_BUTTON_INACTIVE_OPACITY;
            this.betAmount3Node.opacity = BET_AMOUNT_BUTTON_INACTIVE_OPACITY;
            this.clearBetNode.opacity = BET_AMOUNT_BUTTON_INACTIVE_OPACITY;
            this.betAmountPanel.opacity = BET_AMOUNT_BUTTON_INACTIVE_OPACITY;
        }
    }

    setBetMultiplier(value: number): void {
        this._betMultiplier = value;
        this._updateWinBetAmountVisual();
    }

    getBetAmount(): number {
        return this._totalBetAmount;
    }

    resetState(): void {
        this._resetWinAmount();
        this._resetBetAmount();
    }

    protected onLoad(): void {
        // mock
        // console.error(cv.dataHandler.getUserData());
        this._balanceAmount = parseFloat(
            cv.StringTools.numToFloatString(cv.dataHandler.getUserData().sports_betting_balance)
        );

        this._setupListener();
    }

    protected onDestroy(): void {
        this._removeListener();
        this._callbackOnBetAmountChange = undefined;
    }

    _setupListener(): void {
        this.betAmount1Node.on(cc.Node.EventType.TOUCH_END, this._setupOnBetAmountButton1Clicked.bind(this));
        this.betAmount2Node.on(cc.Node.EventType.TOUCH_END, this._setupOnBetAmountButton2Clicked.bind(this));
        this.betAmount3Node.on(cc.Node.EventType.TOUCH_END, this._setupOnBetAmountButton3Clicked.bind(this));
        this.clearBetNode.on(cc.Node.EventType.TOUCH_END, this._setupOnClearBetButtonClicked.bind(this));
        cv.MessageCenter.register('update_gold', this._updateBalance.bind(this), this.node);
    }

    _removeListener(): void {
        this.betAmount1Node.off(cc.Node.EventType.TOUCH_END, this._setupOnBetAmountButton1Clicked.bind(this));
        this.betAmount2Node.off(cc.Node.EventType.TOUCH_END, this._setupOnBetAmountButton2Clicked.bind(this));
        this.betAmount3Node.off(cc.Node.EventType.TOUCH_END, this._setupOnBetAmountButton3Clicked.bind(this));
        this.clearBetNode.off(cc.Node.EventType.TOUCH_END, this._setupOnClearBetButtonClicked.bind(this));
        cv.MessageCenter.unregister('update_gold', this.node);
    }

    _setupOnBetAmountButton1Clicked(): void {
        if (!this._betButton1Clickable || !this._enabledInteractivity) return;
        this._updateBetAmountVisual(BET_AMOUNT_1);
    }

    _setupOnBetAmountButton2Clicked(): void {
        if (!this._betButton2Clickable || !this._enabledInteractivity) return;
        this._updateBetAmountVisual(BET_AMOUNT_2);
    }

    _setupOnBetAmountButton3Clicked(): void {
        if (!this._betButton3Clickable || !this._enabledInteractivity) return;
        this._updateBetAmountVisual(BET_AMOUNT_3);
    }

    _setupOnClearBetButtonClicked(): void {
        if (!this._clearButtonClickable || !this._enabledInteractivity) return;
        this._resetBetAmount();
    }

    _updatedBetButtonClickable(betNode: cc.Node, betAmount: number): boolean {
        const remain = this._balanceAmount - this._totalBetAmount;

        if (remain - betAmount < 0) {
            betNode.opacity = BET_AMOUNT_BUTTON_INACTIVE_OPACITY;
            return false;
        }

        betNode.opacity = 255;
        return true;
    }

    _updateBetButtonVisual(): void {
        this._betButton1Clickable = this._updatedBetButtonClickable(this.betAmount1Node, BET_AMOUNT_1);
        this._betButton2Clickable = this._updatedBetButtonClickable(this.betAmount2Node, BET_AMOUNT_2);
        this._betButton3Clickable = this._updatedBetButtonClickable(this.betAmount3Node, BET_AMOUNT_3);
    }

    _updateBetAmountVisual(value: number): void {
        this._totalBetAmount += value;
        const totalBetAmount = this._totalBetAmount;
        this.totalBetAmountLabel.string = totalBetAmount.toString();

        const remainAmount = this._balanceAmount - totalBetAmount;

        this.balanceAmountLabel.string = cv.StringTools.toFixed(remainAmount, 2).toString();

        this._updateWinBetAmountVisual();
        this._updateBetButtonVisual();
        if (this._callbackOnBetAmountChange) this._callbackOnBetAmountChange(totalBetAmount);
    }

    _updateWinBetAmountVisual(): void {
        const totalWin = this._totalBetAmount * this._betMultiplier;
        this.winBetAmountLabel.string = cv.StringTools.toFixed(totalWin, 2).toString();
    }

    _updateBalance(): void {
        console.error('_updateBalance');
        console.error(cv.dataHandler.getUserData().sports_betting_balance);
        this._balanceAmount = parseFloat(
            cv.StringTools.numToFloatString(cv.dataHandler.getUserData().sports_betting_balance)
        );
    }

    _resetWinAmount(): void {
        this._betMultiplier = 0;
        this.winBetAmountLabel.string = '0';
    }

    _resetBetAmount(): void {
        this._totalBetAmount = 0;
        this.totalBetAmountLabel.string = this._totalBetAmount.toString();
        this._updateBetButtonVisual();
        this._updateBetAmountVisual(0);
    }
}
