import cv from '../../lobby/cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class SportsBettingPromptLabelLocaleController extends cc.Component {
    @property(cc.Label)
    successBetTitleLabel: cc.Label = null;

    @property(cc.Label)
    failureBetTitleLabel: cc.Label = null;

    @property(cc.Label)
    failureBetTitleLabelV2: cc.Label = null;

    @property(cc.Label)
    betResultLabel: cc.Label = null;

    @property(cc.Label)
    teamLabel: cc.Label = null;

    @property(cc.Label)
    multiplierLabel: cc.Label = null;

    protected onLoad(): void {
        this.successBetTitleLabel.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Success_Bet');
        this.failureBetTitleLabel.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Failure_Bet');
        this.failureBetTitleLabelV2.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Failure_Bet');
        this.betResultLabel.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Win');
        this.teamLabel.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Home_Team_Win');
        this.multiplierLabel.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Multiplier');
    }
}
