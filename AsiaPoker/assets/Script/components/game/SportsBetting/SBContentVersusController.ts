import { LANGUAGE_TYPE } from '../../../common/tools/Enum';
import cv from '../../lobby/cv';
import TextRunnerView from './tools/TextRunnerView';

const { ccclass, property } = cc._decorator;

interface ISBContentVersusData {
    kickOffTime: number;
    homeTeamName: string;
    homeTeamNameEn: string;
    homeTeamIcon: string;
    awayTeamName: string;
    awayTeamNameEn: string;
    awayTeamIcon: string;
}

const maxSize = 80;

@ccclass
export default class SBContentVersusController extends cc.Component {
    @property(cc.Sprite)
    homeTeamSprite: cc.Sprite = null;

    @property(cc.Sprite)
    awayTeamSprite: cc.Sprite = null;

    @property(cc.Label)
    versusDateLabel: cc.Label = null;

    @property(cc.Label)
    versusTimeLabel: cc.Label = null;

    @property(TextRunnerView)
    homeLabelTextRunner: TextRunnerView = null;

    @property(TextRunnerView)
    awayLabelTextRunner: TextRunnerView = null;

    mapData(data: ISBContentVersusData): void {
        const { kickOffTime, homeTeamName, homeTeamNameEn, homeTeamIcon, awayTeamName, awayTeamNameEn, awayTeamIcon } =
            data;
        this.homeLabelTextRunner.refresh(
            cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? homeTeamName : homeTeamNameEn
        )
        this.awayLabelTextRunner.refresh(
            cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? awayTeamName : awayTeamNameEn
        );

        // yet to update the date and time format

        const date = new Date(kickOffTime * 1000);
        this.versusDateLabel.string = this._getDateStr(date);
        const hour = String(date.getHours()).padStart(2, '0');
        const minute = String(date.getMinutes()).padStart(2, '0');

        this.versusTimeLabel.string = `${hour}:${minute}`;

        if (homeTeamIcon !== '') {
            cc.vv.AssetsManager.loadWebImage(
                this.homeTeamSprite,
                homeTeamIcon,
                null,
                (tex: any) => {
                    if (cc.isValid(tex)) {
                        const spriteFrame = new cc.SpriteFrame(tex);
                        this.homeTeamSprite.spriteFrame = spriteFrame;
                        const widthRatioScale = maxSize / spriteFrame.getRect().width;
                        const heightRatioScale = maxSize / spriteFrame.getRect().height;
                        this.homeTeamSprite.node.scale =
                            widthRatioScale < heightRatioScale ? widthRatioScale : heightRatioScale;
                    }
                },
                (err: any) => { }
            );
        }

        if (awayTeamIcon !== '') {
            cc.vv.AssetsManager.loadWebImage(
                this.awayTeamSprite,
                awayTeamIcon,
                null,
                (tex: any) => {
                    const spriteFrame = new cc.SpriteFrame(tex);
                    this.awayTeamSprite.spriteFrame = spriteFrame;
                    const widthRatioScale = maxSize / spriteFrame.getRect().width;
                    const heightRatioScale = maxSize / spriteFrame.getRect().height;
                    this.awayTeamSprite.node.scale =
                        widthRatioScale < heightRatioScale ? widthRatioScale : heightRatioScale;
                },
                (err: any) => { }
            );
        }
    }

    private _getDateStr(date): string {
        const clonedDate = new Date(date);
        const ticksPerDay = 86400000;
        const today = new Date().setHours(0, 0, 0, 0);
        const tomorrow = new Date(today + ticksPerDay).setHours(0, 0, 0, 0);
        const afterTomorrow = new Date(today + ticksPerDay * 2).setHours(0, 0, 0, 0);
        const target = clonedDate.setHours(0, 0, 0, 0);

        const year = clonedDate.getFullYear();
        const month = String(clonedDate.getMonth() + 1).padStart(2, '0');
        const day = String(clonedDate.getDate()).padStart(2, '0');
        const dateStr = `${year}/${month}/${day}`;
        
        switch(target){
            case today:
                return cv.config.getStringData('minigame_new_date_today');
            case tomorrow:
                return cv.config.getStringData('minigame_new_date_tomorrow');
            case afterTomorrow:
                return cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN ? cv.config.getStringData('minigame_new_date_acquired') : dateStr;
            default:
                return dateStr;
        }
    }
}
