import cv from '../../lobby/cv';
import { SBBetActionButton } from './SBContentBetActionController';

const { ccclass, property } = cc._decorator;

export enum SportsBettingPrompt {
    SUCCESS,
    FAILURE
}

export interface SportsBettingFailurePromptInfo {
    homeTeam: string;
    awayTeam: string;
    betResult?: SBBetActionButton;
    multiplierAmount: number;
    betAmount: number;
}

@ccclass
export default class SportsBettingPromptPanelManager extends cc.Component {
    static _instance: SportsBettingPromptPanelManager;

    @property(cc.Node)
    overLayNode: cc.Node = null;

    @property(cc.Node)
    successNode: cc.Node = null;

    @property(cc.Node)
    failureNode: cc.Node = null;

    @property(cc.Node)
    failureNodeV2: cc.Node = null;

    @property(cc.Label)
    homeTeamLabel: cc.Label = null;

    @property(cc.Label)
    vsTeamLabel: cc.Label = null;

    @property(cc.Label)
    awayTeamLabel: cc.Label = null;

    @property(cc.Label)
    resultLabel: cc.Label = null;

    @property(cc.Label)
    teamLabel: cc.Label = null;

    @property(cc.Label)
    amountLabel: cc.Label = null;

    @property(cc.Label)
    betLabel: cc.Label = null;

    static get instance() {
        return SportsBettingPromptPanelManager._instance;
    }

    show(sportsBettingPrompt: SportsBettingPrompt): void {
        this.overLayNode.active = true;
        this._togglePromptBox(sportsBettingPrompt);
    }

    hide(): void {
        this.overLayNode.active = false;
        this.successNode.active = false;
        this.failureNode.active = false;
        this.failureNodeV2.active = false;
    }

    setupFailureInfo(config: SportsBettingFailurePromptInfo) {
        const { homeTeam, awayTeam, betResult, multiplierAmount, betAmount } = config;
        this.homeTeamLabel.string = homeTeam;
        this.awayTeamLabel.string = awayTeam;
        this.amountLabel.string = multiplierAmount.toString();
        this.betLabel.string = cv.StringTools.formatC(
            cv.config.getStringData('Sports_Bettings_Prompt_Panel_Bet_Amount_Refunded'),
            betAmount.toString()
        );

        switch (betResult) {
            case SBBetActionButton.WIN:
                this.resultLabel.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Win');
                this.teamLabel.string = ` ${cv.config.getStringData('Sports_Bettings_Prompt_Panel_Home_Team_Win')} `;
                break;
            case SBBetActionButton.DRAW:
                this.resultLabel.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Draw');
                this.teamLabel.string = ` ${cv.config.getStringData('Sports_Bettings_Prompt_Panel_Team_Draw')} `;
                break;
            case SBBetActionButton.LOSE:
                this.resultLabel.string = cv.config.getStringData('Sports_Bettings_Prompt_Panel_Lose');
                this.teamLabel.string = ` ${cv.config.getStringData('Sports_Bettings_Prompt_Panel_Away_Team_Win')} `;
                break;
            case SBBetActionButton.HOME:
                this.resultLabel.string = cv.config.getStringData('Sports_Bettings_Handicap_Home');
                this.teamLabel.string = ``;
                break;
            case SBBetActionButton.AWAY:
                this.resultLabel.string = cv.config.getStringData('Sports_Bettings_Handicap_Away');
                this.teamLabel.string = ``;
                break;
            case SBBetActionButton.BIG:
                this.resultLabel.string = cv.config.getStringData('Sports_Bettings_OU_Over');
                this.teamLabel.string = ``;
                break;
            case SBBetActionButton.SMALL:
                this.resultLabel.string = cv.config.getStringData('Sports_Bettings_OU_Under');
                this.teamLabel.string = ``;
                break;
            default:
                throw new Error('setupFailureInfo :: betResult is invalid!');
        }
    }

    protected onLoad(): void {
        SportsBettingPromptPanelManager._instance = this;
        this.hide();
        this.overLayNode.width = cc.Canvas.instance.node.width;
        this.overLayNode.height = cc.Canvas.instance.node.height * 2;
        this.overLayNode.on(cc.Node.EventType.TOUCH_END, this.hide.bind(this));
    }

    protected onDestroy(): void {
        this.overLayNode.off(cc.Node.EventType.TOUCH_END, this.hide.bind(this));
    }

    _showSuccess(): void {
        this.successNode.active = true;
        // this.failureNode.active = false;
        this.failureNodeV2.active = false;
    }

    _showFailure(): void {
        this.successNode.active = false;
        // this.failureNode.active = true; // temporarily disable
        this.failureNodeV2.active = true;
    }

    _togglePromptBox(sportsBettingPrompt: SportsBettingPrompt): void {
        switch (sportsBettingPrompt) {
            case SportsBettingPrompt.SUCCESS:
                this._showSuccess();
                break;
            case SportsBettingPrompt.FAILURE:
                this._showFailure();
                break;
            default:
                break;
        }
    }
}
