const { ccclass, property } = cc._decorator;

@ccclass
export default class SportsBettingLoader extends cc.Component {
    @property(cc.Prefab)
    sportsBettingPanelPrefab: cc.Prefab = null;

    protected onLoad(): void {
        const sportsBettingNode = cc.instantiate(this.sportsBettingPanelPrefab);
        // this.node.addChild(sportsBettingNode);
        sportsBettingNode.parent = this.node;
        // this.node.addChild(sportsBettingNode);
        // sportsBettingNode.setPosition(cc.Vec2.ZERO);
    }
}
