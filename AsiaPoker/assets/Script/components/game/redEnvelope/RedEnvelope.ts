import { ScrollViewReuse } from '../../../common/tools/ScrollViewReuse';
import cv from '../../lobby/cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class RedEnvelope extends cc.Component {
    @property(cc.Sprite) hb_bg: cc.Sprite = null;
    @property(cc.Label) hb_title: cc.Label = null;
    @property(cc.Label) hb_content: cc.Label = null;
    @property(cc.Label) hb_top: cc.Label = null;
    @property(cc.ScrollView) listView: cc.ScrollView = null;
    @property(cc.Node) curListView: cc.Node = null;
    @property(cc.Prefab) listView_item: cc.Prefab = null;
    @property(cc.Button) close_btn: cc.Button = null;
    @property(cc.Label) hb_date: cc.Label = null;
    @property(cc.Node) tool_tip: cc.Node = null;
    @property(cc.Label) tool_tip_label: cc.Label = null;
    @property(cc.Node) arrow: cc.Node = null;

    public cellIdx = 0;
    public timeCount = 0;
    public lumpCount = 5;
    public txts: cc.Node[] = [];
    private recordListMd5 = undefined;

    private _onCloseView: Function = null;

    onLoad() {
        this.hb_date.string = '';
        cv.MessageCenter.register('showLuckTurnSnaplist', this.updateView.bind(this), this.node);
        cv.MessageCenter.register('showLuckButton', this.setStr.bind(this), this.node);
        cv.MessageCenter.register('showEnvelopeToolTip', this.setEnvelopeToolTipActive.bind(this), this.node);
        this.hb_top.string = cv.StringTools.formatC(cv.config.getStringData('Small_Game_Hongbao_Top'), 50);
        this.tool_tip_label.string = cv.config.getStringData('RedEnvelope_usdt_tool_tip');
        this.setStr();
        cc.find('Panel_3', this.node).on(
            cc.Node.EventType.TOUCH_START,
            () => {
                this.hideView();
            },
            this
        );
    }

    onDisable() {
        this.hideEnvelopeToolTip();
    }

    onDestroy() {
        cv.MessageCenter.unregister('showLuckTurnSnaplist', this.node);
        cv.MessageCenter.unregister('showLuckButton', this.node);
    }

    start() {
        cv.resMgr.setSpriteFrame(this.hb_bg.node, cv.config.getLanguagePath('hall/laba/hb_bg'));
        const sv: ScrollViewReuse = this.listView.getComponent(ScrollViewReuse);
        sv.bindPrefab(this.listView_item, 'RedEnvelopeItem', []);
        sv.generateItemPool();
        sv.bindScrollEventTarget(this);
    }

    getLanguageIndx(): number {
        let indx = 0;
        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            indx = 0;
        } else if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.yn_TH) {
            indx = 2;
        } else if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.th_PH) {
            indx = 3;
        } else {
            indx = 1;
        }
        return indx;
    }

    public setStr() {
        let indx = this.getLanguageIndx();
        const titleArr = cv.dataHandler.getUserData().luckTurntablesInfo.title.split('(*)');
        const contentArr = cv.dataHandler.getUserData().luckTurntablesInfo.content.split('(*)');
        if (titleArr.length < indx + 1 || contentArr.length < indx + 1) {
            indx = titleArr.length >= 2 ? 1 : 0;
        }
        this.hb_title.string = titleArr[indx];
        this.hb_content.string = contentArr[indx];
    }

    public updateView() {
        const newList = cv.dataHandler.getUserData().record_list;
        if(!this.compareObjects(newList)) {
            this.updateListView();
        } else {
            console.log("Record List is same, no action required.")
        }

        if (this.txts.length === 0) {
            this.scorllView();
        }
    }

    public getLampIdx() {
        if (this.cellIdx + 1 === this.lumpCount) {
            this.cellIdx = 0;
        } else {
            this.cellIdx += 1;
        }
    }

    public scorllView() {
        for (let i = 0; i < this.lumpCount; i++) {
            this.cellIdx = i;
            this.setLampData(-1 * this.cellIdx);
        }
    }

    public startUpdate(onClose?: Function) {
        this._onCloseView = onClose;
        this.txts = [];
        this.lumpCount = 5;
        this.curListView.destroyAllChildren();
        this.curListView.removeAllChildren(true);
        this.schedule(this.onUpdate, 0);
        cv.worldNet.RequestLuckTurntableSnaplistResult(8, 50);
        this.node.active = true;
    }

    public hideView() {
        this.unschedule(this.onUpdate);
        this.txts = [];
        this.curListView.destroyAllChildren();
        this.curListView.removeAllChildren(true);
        this.node.active = false;
        this._onCloseView?.();
    }

    public onUpdate(dt: number) {
        this.timeCount += dt;
        if (this.txts[0] !== this.txts[this.txts.length]) {
            if (this.txts[0].y > 450) {
                this.txts[0].removeFromParent(true);
                this.txts[0].destroy();
                this.txts.splice(0, 1);
                this.getLampIdx();
                if (cv.dataHandler.getUserData().lamp_list.length > 0) {
                    this.setLampData(0);
                }
            }
        } else {
            this.scorllView();
        }
        for (let i = 0; i < this.txts.length; i++) {
            this.txts[i].y += 1;
        }
        if (this.timeCount > 5) {
            this.timeCount = 0;
            if (cv.dataHandler.getUserData().luckTurntablesEndTime !== 0) {
                cv.worldNet.RequestLuckTurntableSnaplistResult(5, 50);
            }
        }
    }

    public setLampData(idx: number) {
        const rich = new cc.Node();
        const posY = -20;
        rich.addComponent(cc.RichText);
        rich.setAnchorPoint(0, 0.5);
        rich.getComponent(cc.RichText).fontSize = 28;
        rich.getComponent(cc.RichText).maxWidth = 829;
        rich.setPosition(5, posY + idx * 80);
        this.txts.push(rich);
        this.curListView.addChild(rich);

        if (cv.dataHandler.getUserData().lamp_list.length > this.cellIdx) {
            const lamp = cv.dataHandler.getUserData().lamp_list[this.cellIdx];
            let indx = this.getLanguageIndx();
            const roomArr = lamp.room_name.split('#');
            const desArr = lamp.goods_desc.split('#');
            if (roomArr.length < indx + 1) {
                indx = roomArr.length >= 2 ? 1 : 0;
            }

            if (lamp.game_type === 1) {
                const str = cv.StringTools.formatC('Small_Game_Hongbao_desc_%d', lamp.currency_type);
                if (lamp.currency_type === 3) {
                    cv.StringTools.setRichTextString(
                        rich,
                        cv.StringTools.formatC(
                            cv.config.getStringData(str),
                            lamp.nick_name,
                            roomArr[indx],
                            desArr[indx]
                        )
                    );
                } else {
                    cv.StringTools.setRichTextString(
                        rich,
                        cv.StringTools.formatC(
                            cv.config.getStringData(str),
                            lamp.nick_name,
                            roomArr[indx],
                            cv.StringTools.numToFloatString(lamp.amount)
                        )
                    );
                }
            } else {
                const str = cv.StringTools.formatC('Game_Hongbao_desc_%d', lamp.currency_type);
                if (lamp.currency_type === 3) {
                    cv.StringTools.setRichTextString(
                        rich,
                        cv.StringTools.formatC(
                            cv.config.getStringData(str),
                            roomArr[indx],
                            lamp.nick_name,
                            desArr[indx]
                        )
                    );
                } else {
                    cv.StringTools.setRichTextString(
                        rich,
                        cv.StringTools.formatC(
                            cv.config.getStringData(str),
                            roomArr[indx],
                            lamp.nick_name,
                            cv.StringTools.numToFloatString(lamp.amount)
                        )
                    );
                }
            }
        } else {
            rich.getComponent(cc.RichText).string = '';
        }
    }

    public updateListView() {
        this.hb_top.string = cv.StringTools.formatC(cv.config.getStringData('Small_Game_Hongbao_Top'), 50);
        const sv: ScrollViewReuse = this.listView.getComponent(ScrollViewReuse);
        const list = cv.dataHandler.getUserData().record_list;
        sv.reloadView(list);

        if (list.length > 0) {
            this.hb_date.string = cv.StringTools.formatTime(list[0].lottery_time, cv.Enum.eTimeType.Month_Day);
        } else {
            this.hb_date.string = '';
        }
    }

    public closePael() {
        cv.AudioMgr.playButtonSound('close');
        this.hideView();
    }


    public setEnvelopeToolTipActive(clickDetails: any) {
        if (this.tool_tip.active) {
            this.hideEnvelopeToolTip();
            return;
        }
        const arrowBoarder = 2; // As per figma
        const boxTopShadow = 12.5;// As per figma
        const boxBottomShadow=8.5;// As per figma
        this.tool_tip.active = true;
        // Check if box if going 30px left
        const leftMargin = (this.hb_bg.node.width - this.curListView.width) * 0.5;
        const padding=4;// this static padding between arrow and location pointed as per figma
        let { x, y } = this.tool_tip.convertToNodeSpaceAR(clickDetails.clickPos);
        const box = this.tool_tip_label.node.parent;
        let arrowAngle = 180;
        const totalGapInY = ((clickDetails.iconHeight)*0.5 + this.arrow.height*0.5) + padding;
        if (y - (box.height + this.arrow.height + padding) < 0){
            y += totalGapInY; 
            arrowAngle = 0;
        }else{
            y -= totalGapInY;
        }
        this.arrow.setPosition(new cc.Vec2(x, y));

        let boxX = x;
        if (boxX - box.width * 0.5 < leftMargin) boxX = leftMargin + box.width * 0.5;
        let boxY = y;
        if(arrowAngle === 0){
            boxY = y  + this.arrow.height * 0.5 + box.height - boxTopShadow - arrowBoarder;
        }else{
            boxY = y - this.arrow.height * 0.5 + arrowBoarder + boxBottomShadow; 
        }
        box.setPosition(new cc.Vec2(boxX, boxY));
        this.arrow.rotation = arrowAngle;
        this.tool_tip.on(cc.Node.EventType.TOUCH_END, this.hideEnvelopeToolTip.bind(this), this.node);

    }

    public hideEnvelopeToolTip() {
        this.tool_tip.off(cc.Node.EventType.TOUCH_END, this.hideEnvelopeToolTip.bind(this), this.node);
        this.tool_tip.active = false;
    }

    // Compare an array of objects with the stored objects
    private compareObjects(newObjects) {
        const newSerializeData = cv.md5.md5(JSON.stringify(newObjects));

        const isSame = this.recordListMd5 === newSerializeData;

        if(!isSame) {
            this.recordListMd5 = newSerializeData;
        }

        return isSame;
    }
    
}        

