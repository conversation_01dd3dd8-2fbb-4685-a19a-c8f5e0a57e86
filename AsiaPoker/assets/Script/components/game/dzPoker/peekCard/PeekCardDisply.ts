const { ccclass, property } = cc._decorator;

// peek card on the table
@ccclass
export default class PeekCardDisplay extends cc.Component {

    @property(cc.Sprite)
    cardBack: cc.Sprite = null;

    @property(cc.Sprite)
    cardFace: cc.Sprite = null;

    private _isOpened = false;
    private _isOpening = false;
    private _duration = 0;

    private _startPos: cc.Vec2;
    private _curPos: cc.Vec2;
    private _timer = 0;

    private _matFace: cc.MaterialVariant;
    private _matBack: cc.MaterialVariant;

    onLoad() {
        this._matFace = this.cardFace.getMaterial(0);
        this._matBack = this.cardBack.getMaterial(0);

        this.reset();
    }

    public setSize(width: number, height: number) {
        this.cardBack.node.setContentSize(width, height);
        this.cardFace.node.setContentSize(width, height);
        let shadowWidth = width * 0.1;
        this._matFace.setProperty('shadowWidth', shadowWidth);
    }

    public setCardBack(spf: cc.SpriteFrame) {
        this.cardBack.spriteFrame = spf;
    }

    public setCardFace(spf: cc.SpriteFrame) {
        this.cardFace.spriteFrame = spf;
    }

    public reset() {
        this._isOpening = false;
        this._isOpened = false;
        this._applyUniform(cc.Vec2.ZERO, cc.Vec2.ZERO);
        let shadowWidth = this.cardBack.node.width * 0.1;
        this._matFace.setProperty('shadowWidth', shadowWidth);
        this._matFace.setProperty('width', 0);
        this._matBack.setProperty('width', 0);

        this._startPos = null;
        this._curPos = null;
    }

    public showFace() {
        this._isOpening = false;
        this._isOpened = true;
        this._matFace.setProperty('shadowWidth', 0);

        const rect = this._getWorldBoundingBox();
        const left = rect.xMin;
        const right = rect.xMax;
        this._applyUniform(cc.v2(left, rect.center.y), cc.v2(right, rect.center.y));
    }

    public get isPend() {
        return !this._isOpening;
    }

    public open(duration: number = 1) {
        if(this._isOpened) return;
        if(this._isOpening) {
            if(this._timer > this._duration) {
                return;
            }
            let curRatio = this._timer / this._duration;
            let lastRatio = 1 - curRatio;
            let lastDuration = duration;

            this._duration = lastRatio * lastDuration;
            this._timer = curRatio * this._duration;
            return;
        }
        const rect = this._getWorldBoundingBox();
        this._startPos = cc.v2(rect.xMin, rect.center.y);
        this._curPos = cc.v2(rect.xMin + 16, rect.center.y);
        this._isOpening = true;
        this._duration = duration;
        this._timer = 0;
    }

    private _getWorldBoundingBox(offset?: cc.Size): cc.Rect {
        const pos = cc.Vec3.ZERO;

        const center = this.node.convertToWorldSpaceAR(pos);
        const size = this.cardBack.node.getContentSize();

        if (offset) {
            center.x += offset.width;
            center.y += offset.height;
        }

        return new cc.Rect(center.x - size.width / 2, center.y - size.height / 2, size.width, size.height);
    }

    private _applyUniform(startPos: cc.Vec2, endPos: cc.Vec2) {
        this._matBack.setProperty('startPos', startPos);
        this._matBack.setProperty('endPos', endPos);
        this._matFace.setProperty('startPos', startPos);
        this._matFace.setProperty('endPos', endPos);
    }

    update(dt: number) {
        if (this._isOpening && !this._isOpened) {
            if (!this._startPos || this._duration === 0) {
                this.showFace();
                return;
            }

            this._timer += dt;
            const rect = this._getWorldBoundingBox();

            if (this._timer >= this._duration) {
                let overD = 0.25;
                let t = Math.min(1, (this._timer - this._duration) / overD);
                t = cc.easeSineIn().easing(t);
                this._matFace.setProperty('width', rect.width * .5 * t);
                this._matBack.setProperty('width', rect.width * .5 * t);
                this._applyUniform(cc.v2(rect.xMin, rect.center.y), cc.v2(rect.xMax, rect.center.y));

                if(t >= 1) {
                    this._isOpening = false;
                    this._isOpened = true;
                    this._matFace.setProperty('shadowWidth', 0);
                }
                return;
            }

            let t = Math.min(1, this._timer / this._duration);

            const { _startPos, _curPos } = this

            const center = _startPos.clone().lerp(_curPos, 0.5);

            const offset = center.clone().lerp(rect.center, t).subtract(center);

            const p0 = _startPos.clone().add(offset);
            const p1 = _curPos.clone().add(offset)
            this._applyUniform(p0, p1);

            const asix = cc.v2(Math.sign(p1.x - p0.x), 0);
            const dir = p1.clone().subtract(p0).normalize();

            if (Math.abs(dir.x) < Math.abs(dir.y)) {
                asix.x = 0;
                asix.y = Math.sign(p1.y - p0.y);
            }
            let angle0 = dir.signAngle(asix) * t;
            this._matFace.setProperty('u_angle', -angle0 * 2);
            this._matBack.setProperty('u_angle', -angle0 * 2);
        }
    }
}
