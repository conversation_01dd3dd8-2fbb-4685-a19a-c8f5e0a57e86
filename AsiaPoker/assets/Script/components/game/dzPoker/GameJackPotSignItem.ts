import { JackpotAwardInfo } from '../jackfruit/JackfruitData';
import { GameJackPotSignItemGroup } from './GameJackPotSignItemGroup';
import { GameJackPotSignItemSingle } from './GameJackPotSignItemSingle';
import { GameJackPotRecordItemData } from './JackPot';
import { AwardInfo } from './data/JackpotData';

const { ccclass, property } = cc._decorator;
@ccclass
export class GameJackPotSignItem extends cc.Component {
    @property(cc.Node) public singlePanelNode: cc.Node = null;
    @property(cc.Node) public groupPanelNode: cc.Node = null;

    @property(GameJackPotSignItemSingle) public gameJackPotSignItemSingleScript: GameJackPotSignItemSingle = null;
    @property(GameJackPotSignItemGroup) public gameJackPotSignItemGroupScript: GameJackPotSignItemGroup = null;

    public setdata(award_players: GameJackPotRecordItemData) {
        const { data } = award_players;
        const isGrouped = Array.isArray(data);
        this.singlePanelNode.active = !isGrouped;
        this.groupPanelNode.active = isGrouped;

        if (isGrouped) {
            this.gameJackPotSignItemGroupScript.setdata(data);
        } else {
            this.gameJackPotSignItemSingleScript.setdata(data);
        }
    }

    public setSingleJackFruitData(data: JackpotAwardInfo) {
        const awardInfo = AwardInfo.fromJackpotAwardInfo(data);
        this.gameJackPotSignItemSingleScript.setdata(awardInfo);
    }
}
