// Learn TypeScript:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html
import cv from '../../lobby/cv';

const { ccclass, property } = cc._decorator;

@ccclass
export class GoldViewNew extends cc.Component {
    @property(cc.Node) gold_text: cc.Node = null;
    @property(cc.Node) usdt_text: cc.Node = null;

    protected addCallback: () => void = null;

    private _noticeViewNode: cc.Node = null;

    onLoad() {
        cv.MessageCenter.register('update_info', this.UpdateUserInfo.bind(this), this.node);
        cv.MessageCenter.register('update_gold', this.UpdateUserInfo.bind(this), this.node);

        this.UpdateUserInfo();
    }

    onDestroy() {
        cv.MessageCenter.unregister('update_info', this.node);
        cv.MessageCenter.unregister('update_gold', this.node);
    }

    setNoticeViewNode(node: cc.Node) {
        this._noticeViewNode = node;
    }

    setGoldNumber(num: number) {
        this.gold_text.getComponent(cc.Label).string = cv.StringTools.numToFloatString(num);
    }

    setUSDTNumber(num: number) {
        this.usdt_text.getComponent(cc.Label).string = cv.StringTools.numToFloatString(num);
    }

    setViewStyle(style: number) {}

    UpdateUserInfo() {
        this.setGoldNumber(cv.dataHandler.getUserData().total_amount);
        this.setUSDTNumber(cv.dataHandler.getUserData().usdt);
    }

    setAddCallback(cb: () => void) {
        this.addCallback = cb;
    }

    _rechargeEvent(){

        if (cv.config.getCurrentScene() === cv.Enum.SCENE.HALL_SCENE){
            const properties = { item: "headerDepositButton" };
            cv.segmentTool.track(cv.Enum.CurrentScreen.deposit, cv.Enum.segmentEvent.DepositInitiated, cv.Enum.Functionality.payments, properties);
            return;
        }

        const properties = { item: "leftMenuDepositButton" };
        cv.segmentTool.track(cv.Enum.CurrentScreen.room, cv.Enum.segmentEvent.DepositInitiated, cv.Enum.Functionality.payments, properties);
    }

    onBtnAddClick(event: cc.Event) {
        if (this.addCallback) {
            this.addCallback();
        } else {
            cv.AudioMgr.playButtonSound('button_click');
            const noticeView = this._noticeViewNode;
            if (!cc.sys.isBrowser && cc.sys.os == cc.sys.OS_IOS) {
                if (noticeView && noticeView.active) {
                    noticeView.active = false;
                    cv.SHOP.setExitCallFunc(() => {
                        noticeView.active = true;
                    });
                } else {
                    cv.SHOP.setExitCallFunc(() => {});
                }
            }
            this._rechargeEvent();
            cv.SHOP.RechargeClick();
        }
    }
}
