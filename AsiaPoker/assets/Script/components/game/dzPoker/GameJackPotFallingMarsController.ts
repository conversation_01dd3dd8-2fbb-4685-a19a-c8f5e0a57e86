import MoneyValueComp from '../../../../falling_mars_jackpot/ForGame/MoneyValueComp';
import Particle3D from '../../../../falling_mars_jackpot/Reference/Particle_3D';
import cv from '../../lobby/cv';
import GameJackPotBaseController from './GameJackPotBaseController';
import { AwardInfos, AwardPlayerJackpotType } from './data/JackpotData';

const { ccclass, property } = cc._decorator;

@ccclass
export default class GameJackPotFallingMarsController extends GameJackPotBaseController {
    @property([cc.AnimationClip]) cameraAnimClip: cc.AnimationClip[] = [];
    @property([cc.AudioClip]) effectAudioClip: cc.AudioClip[] = [];
    @property(Particle3D) particle3d: Particle3D = null;
    @property([cc.Label]) lineLabels: cc.Label[] = [];
    @property(cc.Label) titleLabel: cc.Label = null;
    @property(MoneyValueComp) totalWinLabel: MoneyValueComp = null;

    _mainCameraAnim: cc.Animation = null;

    _awardInfos: AwardInfos[] = null;

    _jackpotTotalWin: number = 0;
    _earthsTotalWin: number = 0;

    public init(infos: AwardInfos[]) {
        this._setupLocalization();
        this._awardInfos = infos;
      
        for (let i = 0; i < this.lineLabels.length; i++) {
            this.lineLabels[i].node.parent.active = false;
        }

        this._setupPlanetInfo();
        this._calculateTotalWin();

    }

    public setupMainCameraAnim(mainCamera: cc.Camera) {
        const mainCameraAnim = mainCamera.getComponent(cc.Animation);
        this._mainCameraAnim = mainCameraAnim;
        if (!mainCameraAnim) {
            const anim = mainCamera.addComponent(cc.Animation);
            this._mainCameraAnim = anim;
            anim.addClip(this.cameraAnimClip[0]);
            anim.addClip(this.cameraAnimClip[1]);
            anim.addClip(this.cameraAnimClip[2]);
        }
    }

    public playAnim() {
        this.node.active = true;
        this.particle3d.playParticles3D();

        this.anim.play('BigWinMars');
        this._mainCameraAnim.play('CameraShake');
        cc.audioEngine.play(this.effectAudioClip[0], false, 1);

        cc.Tween.stopAllByTarget(this.totalWinLabel);
        cc.tween(this.totalWinLabel)
            .call(() => {
                this.totalWinLabel.runMoneyTo(cv.StringTools.serverGoldToShowNumber(this._earthsTotalWin));
            })
            .delay(1.63)
            .call(() => {
                this.totalWinLabel.runMoneyTo(cv.StringTools.serverGoldToShowNumber(this._jackpotTotalWin));
            })
            .start();
    }

    private _setupLocalization() {
        this.titleLabel.string = cv.StringTools.formatC(cv.config.getStringData('UIJackpotHitCardPlayer'), '');
    }

    private _setupLineInfo(index: number, info: AwardInfos) {
        const name = info.award_player_name;
        const level = cv.config.getStringData(`UITitle${112 + info.hand_level}`);
        const strAmount = info.award_amount ? ` +${cv.StringTools.serverGoldToShowString(info.award_amount)}` : "";
        this.lineLabels[index].string = `${name} ${cv.StringTools.formatC(
            cv.config.getStringData('UIJackpotHitCardType'),
            level
        )}` + strAmount;

        this.lineLabels[index].node.parent.active = true;
    }

    private _setupPlayers(info: AwardInfos[]) {
        for (let i = 0; i < info.length; i++) {
            const name = info[i].award_player_name;
            const level = cv.config.getStringData(`UITitle${112 + info[i].hand_level}`);
            // let str =
            this.lineLabels[i].string = `${name} ${cv.StringTools.formatC(
                cv.config.getStringData('UIJackpotHitCardType'),
                level
            )} +${cv.StringTools.serverGoldToShowString(info[i].award_amount)}`;
        }
    }

    private _setupPlanetInfo() {
        const marsInfo = this._awardInfos.filter((e) => e.type === AwardPlayerJackpotType.Mars);
        for (let i = 0; i < marsInfo.length; i++) {
            this._setupLineInfo(i, marsInfo[i]);
        }
        const earthInfos = this._awardInfos.filter((e) => e.type === AwardPlayerJackpotType.Earth);
        for (let i = 0; i < earthInfos.length; i++) {
            this._setupLineInfo(i + marsInfo.length, earthInfos[i]);
        }
    }

    private _calculateTotalWin() {
        this.totalWinLabel.moneyValue = 0;

        for (let i = 0; i < this._awardInfos.length; i++) {
            if (this._awardInfos[i].award_amount) {
                this._jackpotTotalWin += this._awardInfos[i].award_amount;
                if (this._awardInfos[i].type === AwardPlayerJackpotType.Earth) {
                    this._earthsTotalWin += this._awardInfos[i].award_amount;
                }
            }    
        }
    }
}
