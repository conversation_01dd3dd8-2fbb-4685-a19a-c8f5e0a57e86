import cv from '../../lobby/cv';

import GameDataManager from './data/GameDataManager';
import BaseJackpotNumberPanelManager from '../../../common/prefab/BaseJackPotNumberPanelManager';

const { ccclass } = cc._decorator;

@ccclass
export default class GameJackpotNumberPanelManager extends BaseJackpotNumberPanelManager {
    public showJackPotNumberPanel() {
        if (cv.GameDataManager.tRoomData.isZoom()) {
            return;
        }

        this.node.active = true;

        cv.worldNet.RequestGetJackpotData(
            cv.GameDataManager.tRoomData.pkRoomParam.club_id,
            cv.GameDataManager.tRoomData.u32RoomId
        );
    }

    public hideJackPotNumberPanel() {
        this.node.active = false;
    }

    public sendNetJackPot() {
        cv.worldNet.RequestCurrentRoomJackpot(
            cv.GameDataManager.tRoomData.pkRoomParam.club_id,
            GameDataManager.tRoomData.u32RoomId,
            cv.GameDataManager.tRoomData.pkRoomParam.rule_blind_enum
        );
        cv.worldNet.RequestGetJackpotData(
            cv.GameDataManager.tRoomData.pkRoomParam.club_id,
            GameDataManager.tRoomData.u32RoomId
        );
        cv.worldNet.RequestJackpotAwardRecord(
            cv.GameDataManager.tRoomData.pkRoomParam.club_id,
            GameDataManager.tRoomData.u32RoomId,
            cv.GameDataManager.tRoomData.pkRoomParam.rule_blind_enum
        );
    }

    protected _updateJackpotNumEvent() {
        if (!cv.GameDataManager.tRoomData.pkRoomParam.is_associated_jackpot) {
            return;
        }

        const num = cv.GameDataManager.tJackPot.getJackpotAmountByBlind(
            cv.GameDataManager.tRoomData.pkRoomParam.rule_blind_enum
        );
        const amount = Math.round(cv.StringTools.clientGoldByServer(num));
        this._updateJackpotNum(amount);
    }

    protected _hasJackpotEnabled(): boolean {
        return cv.GameDataManager.tRoomData.pkRoomParam.is_associated_jackpot;
    }
}
