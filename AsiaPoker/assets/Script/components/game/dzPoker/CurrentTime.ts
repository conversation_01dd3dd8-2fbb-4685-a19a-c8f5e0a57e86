import game_protocol = require("./../../../../Script/common/pb/gs_protocol");
import game_pb = game_protocol.protocol;

import cv from "../../lobby/cv";
import { ObInfo, ObPlayer, PlayerInfo } from "./data/RoomData"
import { CurrentTimeItem } from "./CurrentTimeItem";
import { ObItems } from "./obItems";
import { obManNumber } from "./obManNumber";
import GameDataManager from "./data/GameDataManager";
import ListView from "../../../common/tools/ListView";
import { SafeAreaHelper } from "../../../../default/shared/safe_area_helper/SafeAreaHelper";
import NodeStatusListener from "../../lobby/nodeManager/NodeStatusListener";
import { NodeGroupType } from "../../lobby/nodeManager/NodeStatusCenter";


const { ccclass, property } = cc._decorator;

@ccclass
export class CurrentTime extends cc.Component {
    @property(cc.Widget) currentTime_panel: cc.Widget = null;
    @property(cc.Label) blind_text: cc.Label = null;

    @property(ListView) data_scrollview: ListView = null;
    @property(ListView) data_win_scrollview: ListView = null;

    @property(cc.Node) spCtrlOn: cc.Node = null;
    @property(cc.Node) spCtrlOff: cc.Node = null;

    @property(cc.Label) title_text: cc.Label = null;
    @property(cc.Label) roleName_txt: cc.Label = null;
    @property(cc.Label) buyinLimit_txt: cc.Label = null;
    @property(cc.Label) buyin_txt: cc.Label = null;
    @property(cc.Label) handcount_text: cc.Label = null;
    @property(cc.Label) txtShowCtrl: cc.Label = null;


    private dataListWinAll = []; //win/lose list data
    private dataListWinDesktop = []; //Win/Loss List Data (Table Players Only)

    onLoad(): void {
        this.registerMsg();
        this.setShowCtrlDefault();
        const bShow = (cv.tools.GetStringByCCFile("CURRENTSHOWCTRL") === "TRUE");
        this.spCtrlOn.active = bShow;
        this.spCtrlOff.active = !bShow;
        this.initLanguage();

        const nodeStatusListener = this.node.addComponent(NodeStatusListener);
		nodeStatusListener.init([NodeGroupType.H5LiveStreamWebview]);
    }

    show() {
        this.node.active = true;
        cv.gameNet.RequestSituation(cv.roomManager.getCurrentRoomID());
        this.setSafeArea();
    }

    public setSafeArea() {
        const safeOffset = SafeAreaHelper.getUpperDangerZoneYOffset();
        const seatOffset = 60;

        const offsetY = seatOffset - safeOffset > 0 ? seatOffset - safeOffset : safeOffset;
        this.currentTime_panel.top = offsetY;
        cv.resMgr.adaptWidget(this.currentTime_panel.node, true);
    }

    initLanguage() {
        this.title_text.string = cv.config.getStringData("curentTime_curentTime_panel_title_text");
        this.roleName_txt.string = cv.config.getStringData("curentTime_curentTime_panel_roleName_txt");
        this.buyinLimit_txt.string = cv.config.getStringData("curentTime_curentTime_panel_buyinLimit_txt");
        this.buyin_txt.string = cv.config.getStringData("curentTime_curentTime_panel_buyin_txt");
        this.handcount_text.string = cv.config.getStringData("curentTime_curentTime_panel_shouNum_text");
        this.txtShowCtrl.string = cv.config.getStringData("CurrentTime_ctrl_tips");

    }

    //default set checkbox is true when enter game first time and don't change status of checkbox
    setShowCtrlDefault() {
        if (cv.tools.GetStringByCCFile("CURRENTSHOWCTRL") === null) cv.tools.SaveStringByCCFile("CURRENTSHOWCTRL", "FALSE");
    }

    public outRegion() {
        this.node.active = false;
    }

    public onRoomSituation(msg: any) {
        const binds = cv.config.getblindString(GameDataManager.tRoomData.pkRoomParam.rule_blind_enum - 1);
        this.blind_text.string = cv.config.getStringData("UIGameBlind").replace("%s", binds).toUpperCase();

        const playerBuyIn = msg.buyin_player_list.find((item) => { return item.playerid === cv.dataHandler.getUserData().u32Uid });
        if (playerBuyIn) {
            GameDataManager.tRoomData.isBuyin = true;
            cv.MessageCenter.send("onSetShowAuditState", true);
        }

        const playerCheckout = msg.check_out_list.find((item) => { return item === cv.dataHandler.getUserData().u32Uid });
        if (playerCheckout) {
            GameDataManager.tRoomData.isBuyin = false;
            cv.MessageCenter.send("onSetShowAuditState", true);
        }

        const kObservers: Array<ObPlayer> = [];
        for (let i = 0; i < msg.observer_list.length; ++i) {
            const kPlayer: PlayerInfo = msg.observer_list[i];
            const obPlayer: ObPlayer = new ObPlayer;
            obPlayer.name = kPlayer.name;
            obPlayer.playerid = kPlayer.playerid;
            obPlayer.marks = kPlayer.marks;
            obPlayer.isInroom = kPlayer.is_online;
            obPlayer.data = kPlayer;
            obPlayer.headPath = kPlayer.headurl;
            obPlayer.plat = kPlayer.plat;
            obPlayer.is_online = kPlayer.is_online;
            obPlayer.user_join_room_time = kPlayer.user_join_room_time;
            kObservers.push(obPlayer);
        }

        this.dataListWinAll = []; //win/lose list data
        this.dataListWinDesktop = []; //Win/Loss List Data (Table Players Only)
        const dataList = [];
        const dataListWinNonSeated = [];

        const kRecords: Array<game_pb.PlayerBuyinInfo> = Object.assign([], msg.buyin_player_list);
        kRecords.sort(this.compareRecords);
        for (let index = 0; index < kRecords.length; index++) {
            const player: PlayerInfo = GameDataManager.tRoomData.GetTablePlayer(kRecords[index].playerid);
            if (player != null) {
                this.dataListWinDesktop.push({ type: 0, data: kRecords[index] });
                this.dataListWinAll.push({ type: 0, data: kRecords[index] });
            }
            else {
                dataListWinNonSeated.push({ type: 0, data: kRecords[index] });
            }
        }

        this.dataListWinAll = this.dataListWinAll.concat(dataListWinNonSeated);

        const obInfo = new ObInfo();
        if (msg.observer_info != null) {
            obInfo.onlineNum = msg.observer_info.online_count;
            obInfo.totalNum = msg.observer_info.total_count;
        }
        dataList.push({ type: 1, data: obInfo });

        //spectator list
        const col = 4;
        for (let index = 0; index < kObservers.length; index += col) {
            const obdata = kObservers.slice(index, index + col);
            dataList.push({ type: 2, data: obdata });
        }
        
        const bShow = (cv.tools.GetStringByCCFile("CURRENTSHOWCTRL") === "TRUE");
        this.spCtrlOn.active = bShow;
        this.spCtrlOff.active = !bShow;
        this.data_win_scrollview.init(this.bindcallfuncWin.bind(this), this.getItemTypeWin.bind(this));
        if (bShow) {
            this.data_win_scrollview.notifyDataSetChanged(this.dataListWinDesktop);
        } else {
            this.data_win_scrollview.notifyDataSetChanged(this.dataListWinAll);
        }

        this.data_scrollview.init(this.bindcallfunc.bind(this), this.getItemType.bind(this));
        this.data_scrollview.notifyDataSetChanged(dataList);

    }

    private bindcallfuncWin(node: cc.Node, info, i) {
        if (info.type === 0) {
            node.getComponent(CurrentTimeItem).setdata(info.data);
        }
    }

    private getItemTypeWin(data, index) {
        return data.type;
    }

    private bindcallfunc(node: cc.Node, info, i) {
        if (info.type === 1) {
            node.getComponent(obManNumber).setdata(info.data);
        }
        else if (info.type === 2) {
            node.getComponent(ObItems).setdata(info.data);
        }
    }

    private getItemType(data, index) {
        return data.type;
    }

    private compareRecords(a: any, b: any): number {
        return b.curr_record - a.curr_record;
    }

    onShowListCtrl() {
        cv.AudioMgr.playButtonSound('button_click');
        if (this.spCtrlOn.active === true) {
            this.spCtrlOn.active = false;
            this.spCtrlOff.active = true;
            cv.tools.SaveStringByCCFile("CURRENTSHOWCTRL", "FALSE");
            this.data_win_scrollview.notifyDataSetChanged(this.dataListWinAll);
        } else {
            this.spCtrlOn.active = true;
            this.spCtrlOff.active = false;
            cv.tools.SaveStringByCCFile("CURRENTSHOWCTRL", "TRUE");
            this.data_win_scrollview.notifyDataSetChanged(this.dataListWinDesktop);
        }
    }

    public onStartGame() {
        if (!cv.GameDataManager.tRoomData.hasRecvStartGame) {
            return;
        }
        cv.gameNet.RequestSituation(cv.roomManager.getCurrentRoomID());
    }

    private registerMsg() {
        cv.MessageCenter.register("on_startgame_noti", this.onStartGame.bind(this), this.node);
        cv.MessageCenter.register("on_room_situation", this.onRoomSituation.bind(this), this.node);
    };

    private unregisterMsg() {
        cv.MessageCenter.unregister("on_startgame_noti", this.node);
        cv.MessageCenter.unregister("on_room_situation", this.node);
    };

    onDestroy() {
        this.unregisterMsg();
    };
}