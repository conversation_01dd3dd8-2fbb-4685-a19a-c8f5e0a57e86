import ListView from '../../../common/tools/ListView';
import cv from '../../lobby/cv';
import { GameJackPotSignItem } from './GameJackPotSignItem';
import { dataItem, GameJackpotItem } from './GameJackpotItem';
import { AwardInfo } from './data/JackpotData';
import ScrollViewItemPool from '../../../common/tools/ScrollViewItemPool';
import NodeStatusListener from '../../lobby/nodeManager/NodeStatusListener';
import { NodeGroupType } from '../../lobby/nodeManager/NodeStatusCenter';

const { ccclass, property } = cc._decorator;

export interface GameJackPotRecordItemData {
    data: AwardInfo | AwardInfo[];
}

const TAB_LABEL_FONT_SIZE_ZH = 40;
const TAB_LABEL_FONT_SIZE_EN = 36;
@ccclass
export class JackPot extends cc.Component {
    @property(cc.Node) public jackPot_panel: cc.Node = null;
    @property(cc.Node) public jackPot_Loser_panel: cc.Node = null;
    @property(cc.Node) public allJackpotInfo_panel: cc.Node = null;
    @property(cc.Node) public jackPotSign_panel: cc.Node = null;
    @property(ScrollViewItemPool) public potSignScrollViewItemPool: ScrollViewItemPool = null;
    @property(cc.ScrollView) public infoScorllView: cc.ScrollView = null;
    @property(cc.Node) public allJackpot_img: cc.Node = null;
    @property(cc.Node) public jackpot_img: cc.Node = null;
    @property(cc.Node) public jackpot_loser_img: cc.Node = null;
    @property(cc.Node) public jackpotRecord_img: cc.Node = null;
    @property(cc.Node) public jackpotPotSignInfoNode: cc.Node = null;
    @property(cc.Label) public jackPotInfo_text: cc.Label = null;
    @property(cc.Label) public bigWinnerName_text: cc.Label = null;
    @property(cc.Label) public bigWinnerCard_type_text: cc.Label = null;
    @property(cc.Label) public bigWinnerNumber_text: cc.Label = null;
    @property(cc.Label) public jackpot_blind_text: cc.Label = null;
    @property(cc.Label) public jackpot_date_text: cc.Label = null;
    @property(cc.Sprite) public luckDogAvatarSprite: cc.Sprite = null;

    @property(cc.Label) public clubName_text: cc.Label = null;
    @property(cc.Label) public clubId_text: cc.Label = null;
    @property(cc.Label) public address_text: cc.Label = null;
    @property(cc.Label) public jackpot_level_number_text: cc.Label = null;
    @property(cc.Label) public jackpot_total_number_text: cc.Label = null;
    @property(cc.Label) public jackpot_loser_total_number_text: cc.Label = null;

    @property(cc.Sprite) public jp_hand_loser: cc.Sprite = null;
    @property(cc.SpriteFrame) public jp_hand_loser_cn: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) public jp_hand_loser_en: cc.SpriteFrame = null;
    @property(cc.Sprite) public jp_hand_loser_prize_ratio: cc.Sprite = null;
    @property(cc.SpriteFrame) public jp_hand_loser_prize_ratio_cn: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) public jp_hand_loser_prize_ratio_en: cc.SpriteFrame = null;

    //infos
    @property(cc.Label) public jackpot_loser_hint: cc.Label = null;
    @property(cc.Label) public jackpot_loser_prize_ratio: cc.Label = null;
    @property(cc.Label) public jackpot_loser_prize_tool_tip: cc.Label = null;
    
    //tabs:
    @property(cc.Label) public tab_all_jackpot_label: cc.Label = null;
    @property(cc.Label) public tab_jackpot_label: cc.Label = null;
    @property(cc.Label) public tab_jackpot_smash_label: cc.Label = null;
    @property(cc.Label) public tab_jackpot_record_label: cc.Label = null;

    
    
    //sign panel bind text
    @property(cc.Label) public sign_panel_blind_text: cc.Label = null;


    @property(cc.Node) prompt_info: cc.Node = null;
    

    @property(cc.Prefab) public signItemPrefab: cc.Prefab = null;
    signItem: cc.Node = null;
    @property(cc.Prefab) public jackPotItemPrefab: cc.Prefab = null;
    jackPotItem: cc.Node = null;
    isShort = false;
    blindArr: number[] = [];

    award_type = [];
    isRecord: boolean = false; //处理记录按钮高频率点击卡死
    onLoad() {
        let nodeStatusListener = this.node.addComponent(NodeStatusListener);
        nodeStatusListener.init([NodeGroupType.H5LiveStreamWebview]);

        cv.resMgr.adaptWidget(this.node, true);
        cv.MessageCenter.register('currentRoomJackpot', this.updateJackpot.bind(this), this.node);
        cv.MessageCenter.register('on_jackpot_data', this.updateJackpotInfo.bind(this), this.node);
        cv.MessageCenter.register('jackpotAwardRecord', this._updateBtnJackpotRecord.bind(this), this.node);

        this.signItem = cc.instantiate(this.signItemPrefab);
        this.node.addChild(this.signItem);

        this.jackPotItem = cc.instantiate(this.jackPotItemPrefab);
        this.node.addChild(this.jackPotItem);
        if (cv.GameDataManager.tRoomData.pkRoomParam.game_mode == cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            this.isShort = true;
        }
        this.jackPotItem.active = false;
        this.signItem.active = false;
        this.jackPotInfo_text.node.active = true;
        this.jackpotPotSignInfoNode.active = false;
        this.initLanguage();
    }
    
    start() {
        this.showJackpot();
    }

    onDestroy() {
        cv.MessageCenter.unregister('currentRoomJackpot', this.node);
        cv.MessageCenter.unregister('on_jackpot_data', this.node);
        cv.MessageCenter.unregister('jackpotAwardRecord', this.node);
    }

    initLanguage() {
        this.tab_all_jackpot_label.string = cv.config.getStringData('GameJackPot_button_panel_allJackpotInfo_button');
        this.tab_jackpot_label.string = cv.config.getStringData('GameJackPot_button_panel_jackpot_button');
        this.tab_jackpot_smash_label.string = cv.config.getStringData('GameJackPot_button_panel_planetSmash_button');
        this.tab_jackpot_record_label.string = cv.config.getStringData('GameJackPot_button_panel_jackpotRecord_button');
        
        cv.StringTools.setLabelString(
            this.node,
            'jackPot_panel/awardType_txt',
            'GameJackPot_jackPot_panel_awardType_txt'
        );
        cv.StringTools.setLabelString(
            this.node,
            'jackPot_panel/awardPercent_txt',
            'GameJackPot_jackPot_panel_awardPercent_txt'
        );
        cv.StringTools.setLabelString(this.node, 'jackPot_panel/RoyalFlush_txt', 'GameJackPot_jackPot_panel_Text_65');
        cv.StringTools.setLabelString(this.node, 'jackPot_panel/straight_flush', 'GameJackPot_jackPot_panel_Text_65_0');
        cv.StringTools.setLabelString(this.node, 'jackPot_panel/four_txt', 'GameJackPot_jackPot_panel_Text_65_0_0');
        cv.StringTools.setLabelString(
            this.node,
            'jackPot_panel/jackpotDetail_text',
            'GameJackPot_jackPot_panel_jackpotDetail_text'
        );
        cv.StringTools.setLabelString(
            this.node,
            'jackPot_panel/jackpotDetailTips_text',
            'GameJackPot_jackPot_panel_jackpotDetailTips_text'
        );
        cv.StringTools.setLabelString(
            this.node,
            'allJackpotInfo_panel/totalAmount_txt',
            'GameJackPot_allJackpotInfo_panel_totalAmount_txt'
        );
        cv.StringTools.setLabelString(
            this.node,
            'allJackpotInfo_panel/jackpotlevelAmount_txt',
            'GameJackPot_allJackpotInfo_panel_jackpotlevelAmount_txt'
        );
        cv.StringTools.setLabelString(
            this.node,
            'allJackpotInfo_panel/jackPotSet_button/Label',
            'ClubJackPotSet_jackpotSet_txt'
        );
        cv.StringTools.setLabelString(
            this.node,
            'jackPotSign_panel/Panel_5/jackPotInfo_text',
            'GameJackPot_jackPotSign_panel_Panel_5_jackPotInfo_text'
        );

        let blind: string = this._getJackpotBlindValue();
        this.sign_panel_blind_text.string = cv.StringTools.formatC(
            cv.config.getStringData('UIGameJackPotBlindAmount'),
            blind
        );

        //infos
        this.jackpot_loser_hint.string = cv.config.getStringData('Falling_Mars_Jackpot_Loser_Hint');
        this.jackpot_loser_prize_ratio.string = cv.config.getStringData('Falling_Mars_Jackpot_Loser_Prize_Ratio');
        this.jackpot_loser_prize_tool_tip.string = cv.config.getStringData('Falling_Mars_Jackpot_Loser_Tool_Tip');
    
        //set font size
        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            this.tab_all_jackpot_label.fontSize = TAB_LABEL_FONT_SIZE_ZH;
            this.tab_jackpot_label.fontSize = TAB_LABEL_FONT_SIZE_ZH;
            this.tab_jackpot_record_label.fontSize = TAB_LABEL_FONT_SIZE_ZH;
            this.tab_jackpot_smash_label.fontSize = TAB_LABEL_FONT_SIZE_ZH;
        }else{
            this.tab_all_jackpot_label.fontSize = TAB_LABEL_FONT_SIZE_EN;
            this.tab_jackpot_label.fontSize = TAB_LABEL_FONT_SIZE_EN;
            this.tab_jackpot_record_label.fontSize = TAB_LABEL_FONT_SIZE_EN;
            this.tab_jackpot_smash_label.fontSize = TAB_LABEL_FONT_SIZE_EN;
        }
    }

    public onClickSelf(evt) {
        this.node.active = false;
    }

    public onBtnAllJackpotInfo() {
        this.isRecord = false;
        cv.AudioMgr.playButtonSound('tab');
        this.setDissPanel();
        this.allJackpotInfo_panel.active = true;
        this.allJackpot_img.active = true;

        if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.yn_TH) {
            this.allJackpot_img.setContentSize(cc.size(250, 5.21));
        } else {
            this.allJackpot_img.setContentSize(cc.size(224, 5.21));
        }

        this.tab_all_jackpot_label.node.color = new cc.Color().fromHEX('#FBD888');
    }
    public updateJackpotInfo() {
        let amount: number = 0; //总金额
        let infos = cv.GameDataManager.tJackPot.baseJackpotInfos;

        this.initBlindArr(infos);
        for (let j = 0; j < infos.length; j++) {
            for (let i = 0; i < this.blindArr.length; i++) {
                if (this.blindArr[i] == infos[j].blind_level - 1) {
                    //服务器端从1开始
                    amount += infos[j].amount;
                }
            }
        }
        let zhengshu = Math.round(parseInt(cv.StringTools.numberToShowString(amount)) / 100);

        this.jackpot_total_number_text.string = zhengshu.toString();
        this.clubName_text.string = cv.GameDataManager.tJackPot.club_name;
        this.clubId_text.string = cv.GameDataManager.tJackPot.club_id.toString();
        this.address_text.string = cv.GameDataManager.tJackPot.club_area.toString();
    }

    public onBtnJackpot() {
        this.isRecord = false;
        cv.AudioMgr.playButtonSound('tab');
        this.showJackpot();
    }

    /**
     * 显示jackpot
     */
    public showJackpot() {
        this.setDissPanel();
        this.jackPot_panel.active = true;
        this.jackpot_img.active = true;

        if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.yn_TH) {
            this.jackpot_img.setContentSize(cc.size(250, 5.21));
        } else {
            this.jackpot_img.setContentSize(cc.size(224, 5.21));
        }

        this.tab_jackpot_label.node.color = new cc.Color().fromHEX('#FBD888');
    }

    public onBtnPrompt() {
        this.prompt_info.active = true;
        this.prompt_info.opacity = 0;
        cc.Tween.stopAllByTarget(this.prompt_info);
        cc.tween(this.prompt_info)
            .to(0.2, { opacity: 255 })
            .delay(2)
            .to(1, { opacity: 0 })
            .call(() => {
                this.prompt_info.active = false;
            })
            .start();
    }

    public onBtnLoserJackpot() {
        this.isRecord = false;
        cv.AudioMgr.playButtonSound('tab');
        this.setDissPanel();
        this.jackpot_loser_img.active = true;
        this.jackPot_Loser_panel.active = true;
        this.tab_jackpot_smash_label.node.color = new cc.Color().fromHEX('#FBD888');

        if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.zh_CN){
            this.jp_hand_loser.spriteFrame = this.jp_hand_loser_cn;
            this.jp_hand_loser_prize_ratio.spriteFrame = this.jp_hand_loser_prize_ratio_cn;
        }else{
            this.jp_hand_loser.spriteFrame = this.jp_hand_loser_en;
            this.jp_hand_loser_prize_ratio.spriteFrame = this.jp_hand_loser_prize_ratio_en;
        }
    }


    public updateJackpot() {
        let blinds: string = this._getJackpotBlindValue();

        let blind: cc.Label = this.jackPot_panel.getChildByName('jackPot_blind_text').getComponent(cc.Label);
        let blindSet: cc.Label = this.jackPot_panel.getChildByName('jackPot_blindSet_text').getComponent(cc.Label);
        let blind_loser: cc.Label = this.jackPot_Loser_panel.getChildByName('jackPot_blind_text').getComponent(cc.Label);
        let blindSet_loser: cc.Label = this.jackPot_Loser_panel.getChildByName('jackPot_blindSet_text').getComponent(cc.Label);

        if (cv.GameDataManager.tRoomData.pkRoomParam.game_mode == cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            let blindLv = blinds.substr(0, blinds.indexOf('/'));
            blind.string = cv.StringTools.formatC(
                cv.config.getStringData('Aof_game_short_level_number'),
                blindLv.toString()
            );
            blindSet.string = cv.StringTools.formatC(
                cv.config.getStringData('Aof_game_short_levels_set'),
                blindLv.toString()
            );
            blind_loser.string = cv.StringTools.formatC(
                cv.config.getStringData('Aof_game_short_level_number'),
                blindLv.toString()
            );
            blindSet_loser.string = cv.StringTools.formatC(
                cv.config.getStringData('Aof_game_short_levels_set'),
                blindLv.toString()
            );
        } else {
            blind.string = cv.StringTools.formatC(
                cv.config.getStringData('UIGameJackpotBlindAwardAmount'),
                blinds.toString()
            );
            blindSet.string = cv.StringTools.formatC(
                cv.config.getStringData('UIGameJackpotRecordAwardSet'),
                blinds.toString()
            );
            blind_loser.string = cv.StringTools.formatC(
                cv.config.getStringData('UIGameJackpotBlindAwardAmount'),
                blinds.toString()
            );
            blindSet_loser.string = cv.StringTools.formatC(
                cv.config.getStringData('UIGameJackpotRecordAwardSet'),
                blinds.toString()
            );
        }

        let types = cv.GameDataManager.tJackPot.currentRoomJackpot.CurrentRoomAwardTypes;
        let len: number = cv.StringTools.getArrayLength(types);
        for (let i = 0; i < len; i++) {
            let idx: number = types[i].hand_level;
            let strr: string = `handLevel_${idx}_text`;
            let txts: cc.Label = this.jackPot_panel.getChildByName(strr).getComponent(cc.Label);
            txts.string = types[i].award_percent.toString() + `%`;
            if (types[i].hand_level == 10) {
                let gress: cc.Sprite = this.jackPot_panel.getChildByName('bar_1').getComponent(cc.Sprite);
                gress.fillRange = -1 * types[i].award_percent * 0.01;
            } else if (types[i].hand_level == 9) {
                let gress: cc.Sprite = this.jackPot_panel.getChildByName('bar_2').getComponent(cc.Sprite);
                gress.fillRange = -1 * types[i].award_percent * 0.01;
            } else if (types[i].hand_level == 8) {
                let gress: cc.Sprite = this.jackPot_panel.getChildByName('bar_3').getComponent(cc.Sprite);
                gress.fillRange = -1 * types[i].award_percent * 0.01;
            }
        }

        let amount = cv.GameDataManager.tJackPot.getJackpotAmountByBlind(
            cv.GameDataManager.tRoomData.pkRoomParam.rule_blind_enum
        );
        let n_amount = Math.round(parseInt(cv.StringTools.numberToShowString(amount)) / 100);

        this.jackpot_level_number_text.string = n_amount.toString();
        this.jackpot_loser_total_number_text.string = n_amount.toString();

        let drawin_amout = cv.GameDataManager.tJackPot.currentRoomJackpot.drawin_amout;
        let profit_scale = cv.GameDataManager.tJackPot.currentRoomJackpot.profit_scale;
        let detail: cc.Label = this.jackPot_panel.getChildByName('jackpotDetail_text').getComponent(cc.Label);
        detail.node.active = true;
        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.Allin) {
            detail.node.active = false;
        }
        
        detail.string = cv.StringTools.formatC(
            cv.config.getStringData('UIGameJackpotDetail'),
            profit_scale,
            cv.GameDataManager.tRoomData.u32GameID === cv.Enum.GameId.Plo? 1 : 0.5, // set 0.5 as default and 1 for PLO game type.
            drawin_amout
        );
    }

    public onBtnJackpotRecord() {
        cv.AudioMgr.playButtonSound('tab');
        if (this.isRecord) return;
        this.isRecord = true;
        this.setDissPanel();
        this.jackPotSign_panel.active = true;
        this.jackpotRecord_img.active = true;

        if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.yn_TH) {
            this.jackpotRecord_img.setContentSize(cc.size(250, 5.21));
        } else {
            this.jackpotRecord_img.setContentSize(cc.size(160, 5.21));
        }

        this._updateJackpotSignRecord();

        this.tab_jackpot_record_label.node.color = new cc.Color().fromHEX('#FBD888');
        // cc.find('button_panel/jackpotRecord_button/Label', this.node).getComponent(cc.Label).enableBold = true;
    }

    private _updateJackpotSignRecord() {
        if (!this.potSignScrollViewItemPool) {
            throw new Error('JackPot :: _updateJackpotSignRecord : Pot sign scroll view item pool not found.');
        }

        const itemCount = this.award_type ? this.award_type.length : 0;
        this.potSignScrollViewItemPool.init({ itemCount });

        const signItems = this.potSignScrollViewItemPool.getItems();
        if (!signItems) {
            console.error('JackPot :: _updateJackpotSignRecord : sign items is empty!');
            return;
        }

        signItems.forEach((item, index) => {
            const script = item.getComponent(GameJackPotSignItem);
            if (script) {
                const info = this.award_type[index];
                console.log(info);
                console.log(index);
                script.setdata(info);
            } else {
                console.error(`Failed to get GameJackPotSignItem script from item ${index}.`);
            }
        });
    }

    private _updateBtnJackpotRecord() {
        let award_players = cv.GameDataManager.tJackPot.award_players;

        // perform data massage to cater our visual display due to the data returned from backend is all individual record where falling mars required group display if the gameuuid is the same
        const wrapGameData = (data: AwardInfo[]): GameJackPotRecordItemData => ({
            data: data.length > 1 ? data.slice() : data[0]
        });

        const groupByGameUuid = (data: AwardInfo[]): (GameJackPotRecordItemData | GameJackPotRecordItemData[])[] => {
            const groupedData: { [gameUuid: number]: AwardInfo[] } = {};

            data.forEach((item) => {
                const gameUuid = item.game_uuid;
                const group = groupedData[gameUuid];

                if (group) {
                    group.push(item);
                } else {
                    groupedData[gameUuid] = [item];
                }
            });

            const result: (GameJackPotRecordItemData | GameJackPotRecordItemData[])[] = [];

            Object.values(groupedData).forEach((items) => {
                result.push(wrapGameData(items));
            });

            return result;
        };

        this.award_type = groupByGameUuid(award_players);
        // console.error(this.award_type);

        const luckDog = cv.GameDataManager.tJackPot.luckyDog;

        if (luckDog == null) {
            this.bigWinnerName_text.node.active = false;
            this.bigWinnerNumber_text.node.active = false;
            this.bigWinnerCard_type_text.node.active = false;
            return;
        }

        this.bigWinnerName_text.node.active = true;
        this.bigWinnerNumber_text.node.active = true;
        this.bigWinnerCard_type_text.node.active = true;

        if (luckDog.avatar) {
            const url = cv.dataHandler.getUserData().getImageUrlByPlat(luckDog.avatar, luckDog.platform);
            const texture: cc.Texture2D = cv.resMgr.get(url, cc.Texture2D);
            if (texture) {
                this.luckDogAvatarSprite.spriteFrame = new cc.SpriteFrame(texture);
            }
            else {
                cv.resMgr.loadRemote(url, (error: Error, tex: cc.Texture2D): void => {
                    if (error) {
                        console.log(error.message || error);
                        return;
                    }
                    this.luckDogAvatarSprite.spriteFrame = new cc.SpriteFrame(tex);
                });
            }
        }

        cv.StringTools.setShrinkString(this.bigWinnerName_text.node, luckDog.player_name, true);
        if (luckDog.hand_level >= 8 && luckDog.hand_level <= 10) {
            this.bigWinnerCard_type_text.string = cv.config.getStringData(`UITitle${112 + luckDog.hand_level}`);
        }

        this.jackpot_blind_text.string = this._getJackpotBlindValue();

        this.jackpot_date_text.string =
            luckDog.award_time == 0
                ? ''
                : cv.StringTools.formatTime(luckDog.award_time, cv.Enum.eTimeType.Month_Day, false);

        let amount = cv.StringTools.numberToString(luckDog.award_amount / 100);
        this.bigWinnerNumber_text.string = amount.toString();

        this.jackPotInfo_text.node.active = !(award_players.length > 0);
        this.jackpotPotSignInfoNode.active = !this.jackPotInfo_text.node.active;

        if (this.isRecord) this._updateJackpotSignRecord();
    }

    private _getJackpotBlindValue() {
        const { rule_blind_enum, rule_switch_force_straddle } = cv.GameDataManager.tRoomData.pkRoomParam;

        const mangZhu: string = cv.config.getblindString(rule_blind_enum - 1);
        const splitArr: string[] = mangZhu.split('/');
        const bigBlind =
            Number(splitArr[1]) >= 1000 ? cv.StringTools.formatC('%dk', Number(splitArr[1]) / 1000) : splitArr[1];
        const smallBlind =
            Number(splitArr[0]) >= 1000 ? cv.StringTools.formatC('%dk', Number(splitArr[0]) / 1000) : splitArr[0];

        if (rule_switch_force_straddle === 1) {
            const straddle =
                Number(splitArr[1]) >= 1000
                    ? cv.StringTools.formatC('%dk', (Number(splitArr[1]) * 2) / 1000)
                    : Number(splitArr[1]) * 2;

            return cv.StringTools.formatC('%s/%s/%s', smallBlind, bigBlind, straddle);
        } else {
            return cv.StringTools.formatC('%s/%s', smallBlind, bigBlind);
        }
    }

    private setDissPanel() {
        this.jackPot_panel.active = false;
        this.jackPotSign_panel.active = false;
        this.allJackpotInfo_panel.active = false;
        this.jackPot_Loser_panel.active = false;


        this.allJackpot_img.active = false;
        this.jackpot_img.active = false;
        this.jackpot_loser_img.active = false;
        this.jackpotRecord_img.active = false;

        this.tab_all_jackpot_label.node.color = new cc.Color().fromHEX('#FFFFFF');
        this.tab_jackpot_label.node.color = new cc.Color().fromHEX('#FFFFFF');
        this.tab_jackpot_record_label.node.color = new cc.Color().fromHEX('#FFFFFF');
        this.tab_jackpot_smash_label.node.color = new cc.Color().fromHEX('#FFFFFF');

        this.tab_all_jackpot_label.enableBold = false;
        this.tab_jackpot_label.enableBold = false;
        this.tab_jackpot_record_label.getComponent(cc.Label).enableBold = false;
        this.tab_jackpot_smash_label.enableBold = false;
    }
    private initBlindArr(infos: any[]) {
        this.blindArr = [];
        let list = [];
        let list1 = [];
        for (let index = 0; index < cv.config.getblindArrLen(); index++) {
            let blind = cv.config.getblindString(index);
            blind = blind.substr(0, blind.indexOf('/'));
            list[index] = cv.Number(blind);
            list1[index] = cv.Number(blind);
        }
        list.sort(function (a, b) {
            return a - b;
        });
        for (let i = 0; i < list.length; i++) {
            for (let j = 0; j < list1.length; j++) {
                if (list[i] == list1[j]) {
                    for (let index = 0; index < infos.length; index++) {
                        if (infos[index].blind_level - 1 == j) {
                            this.blindArr.push(j);
                        }
                    }
                    break;
                }
            }
        }

        let dataList = [];
        let line = 0;
        let lineNum = 2;
        for (let index = 0; index < this.blindArr.length;) {
            let obdata = [];
            for (let i = 0; i < lineNum; i++) {
                let curindex = i + line * lineNum;
                if (curindex < this.blindArr.length) {
                    let data = new dataItem();
                    data.blind = this.blindArr[curindex];
                    data.amount = 0;

                    for (let j = 0; j < infos.length; j++) {
                        if (this.blindArr[curindex] == infos[j].blind_level - 1) {
                            //服务器端从1开始
                            data.amount = infos[j].amount;
                        }
                    }

                    obdata.push(data);
                    index++;
                } else {
                    break;
                }
            }

            dataList.push({ type: 0, data: obdata });
            line++;
        }

        const infoListView = this.infoScorllView.getComponent(ListView);

        infoListView.init(this.bindcallfunc.bind(this), this.getItemType.bind(this));
        infoListView.notifyDataSetChanged(dataList);
    }

    public bindcallfunc(node: cc.Node, info, i) {
        console.log(info);
        console.log(i);
        if (info.type == 0) {
            node.getComponent(GameJackpotItem).setdata(info.data);
        }
    }

    public getItemType(data, index) {
        return data.type;
    }
}
