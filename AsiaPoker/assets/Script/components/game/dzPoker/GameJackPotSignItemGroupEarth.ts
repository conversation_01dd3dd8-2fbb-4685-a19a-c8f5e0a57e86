import cv from '../../lobby/cv';
import { AwardInfo } from './data/JackpotData';

const { ccclass, property } = cc._decorator;

@ccclass
export default class GameJackPotSignItemGroupEarth extends cc.Component {
    @property(cc.Label) public earthPlayerText: cc.Label = null;
    // @property(cc.Label) public playerNameText: cc.Label = null;
    @property(cc.Label) public awardText: cc.Label = null;
    @property(cc.Label) public cardTypeNameText: cc.Label = null;
    @property(cc.Label) public awardRatioText: cc.Label = null;

    protected onLoad(): void {
        this.initLanguage();
    }

    initLanguage(): void {
        this.earthPlayerText.string = cv.config.getStringData('Earth_Player');
    }

    setdata(awardPlayer: AwardInfo) {
        const str = `${cv.config.getStringData('Earth_Player')} ${awardPlayer.player_name}`;
        cv.StringTools.setShrinkString(this.earthPlayerText.node, str, true);
        this.awardText.string = awardPlayer.award_amount ? cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(awardPlayer.award_amount)) : '';
        this.cardTypeNameText.string = awardPlayer.hand_level ? cv.config.getStringData(`UITitle${112 + awardPlayer.hand_level}`) : '';
        this.awardRatioText.string = awardPlayer.award_ratio ? `${awardPlayer.award_ratio}%` : '';
    }
}
