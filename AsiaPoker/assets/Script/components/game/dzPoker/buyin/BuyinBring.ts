import cv from '../../../lobby/cv';
import { BuyinStatus, ViewStyleType } from '../BuyInConst';

const { ccclass, property } = cc._decorator;

@ccclass
export default class BuyinBring extends cc.Component {
    @property(cc.SpriteFrame)
    goldCoinIconSpriteFrame: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    usdIconSpriteFrame: cc.SpriteFrame = null;

    @property(cc.Node)
    tackBackPanel: cc.Node = null;

    @property(cc.Node)
    takebackTips_img: cc.Node = null;

    @property(cc.Node)
    takeback_img_0: cc.Node = null;

    @property(cc.Node)
    takebackTips_Arrow: cc.Node = null;

    @property(cc.Node)
    chouMa_slider: cc.Node = null;

    @property(cc.Sprite)
    sliderProgress: cc.Sprite = null;

    @property(cc.Sprite)
    iconSprite: cc.Sprite = null;

    @property(cc.Sprite)
    currentCurrencyIconSprite: cc.Sprite = null;

    @property(cc.Sprite)
    otherCurrencyIconSprite: cc.Sprite = null;

    @property(cc.Label)
    chouMa_txt: cc.Label = null;

    @property(cc.Label)
    min_txt: cc.Label = null;

    @property(cc.Label)
    max_txt: cc.Label = null;

    @property(cc.Label)
    rich_txt: cc.Label = null;

    @property(cc.Label)
    rich_txt_other: cc.Label = null;

    @property(cc.Label)
    rich_word_txt: cc.Label = null;

    @property(cc.Label)
    rich_word_txt_other: cc.Label = null;

    @property(cc.Button)
    confirmButton: cc.Button = null;

    @property(cc.Node)
    confirmLabelNode: cc.Node = null;

    @property(cc.Node)
    currentBuyInNotEnough: cc.Node = null;

    @property(cc.Node)
    bothBuyInNotEnough: cc.Node = null;

    @property([cc.Node])
    rateTipsPanelNodes: cc.Node[] = [];

    @property(cc.Label)
    tackBackAmount_text: cc.Label = null;

    @property(cc.Node)
    tackBackQuestion_button: cc.Node = null;

    @property([cc.Node])
    exchangeRateTipButtonNodes: cc.Node[] = [];

    @property(cc.Label)
    addsign_text: cc.Label = null;

    @property(cc.Node)
    sureBuyInPanel_ok_button: cc.Node = null;

    @property(cc.Node)
    tackbackTip0ItemNode: cc.Node = null;

    @property(cc.Node)
    tackbackTip1ItemNode: cc.Node = null;

    @property(cc.Node)
    tackbackTip2ItemNode: cc.Node = null;

    @property(cc.Slider)
    chouMaSlider: cc.Slider = null;

    @property(cc.Node)
    knobInactiveNode: cc.Node = null;

    @property(cc.Node)
    knobActiveNode: cc.Node = null;

    @property(cc.Node)
    totalBuyInNode: cc.Node = null;

    @property(cc.Node)
    buyInDetailTextNode: cc.Node = null;

    @property(cc.Node)
    insufficientFundTextNode: cc.Node = null;

    @property([cc.Node])
    sliderCurrencySymbolNodes: cc.Node[] = [];

    // localized label
    @property(cc.Label)
    bringTitleLabel: cc.Label = null;

    @property(cc.Label)
    currentCurrencyTextLabel: cc.Label = null;

    @property(cc.Label)
    otherCurrencyTextLabel: cc.Label = null;

    @property(cc.RichText)
    buyInDetailTextLabel: cc.RichText = null;

    @property(cc.Label)
    insufficientFundTextLabel: cc.Label = null; // add localized

    @property(cc.Label)
    balanceTextLabel: cc.Label = null; // add localized

    @property(cc.RichText)
    notEnoughTextLabel: cc.RichText = null;

    @property(cc.Label)
    totalBuyinTextLabel: cc.Label = null;

    @property(cc.Label)
    sureBuyInPanelOkButtonLabel: cc.Label = null;

    @property(cc.Label)
    tackbackTitleTextLabel: cc.Label = null;

    @property(cc.Label)
    tackbackTip0TextLabel: cc.Label = null;

    @property(cc.Label)
    tackbackTip1TextLabel: cc.Label = null;

    @property(cc.Label)
    tackbackTip2TextLabel: cc.Label = null;

    @property([cc.Label])
    exchangeRateTipTextLabels: cc.Label[] = [];

    @property([cc.Label])
    buyinRateTextLabels: cc.Label[] = [];

    @property(cc.RichText)
    buyinNoteRichText: cc.RichText = null;

    @property([cc.Node])
    sliderNodes: cc.Node[] = [];

    @property(cc.Label)
    squid_rules: cc.Label = null;

    @property(cc.Node)
    squid_rebuy_in: cc.Node = null;

    @property(cc.Node)
    loader_animator_node: cc.Node = null;

    @property(cc.Label)
    squid_second_label: cc.Label = null;

    protected _squidBuyInStatus: BuyinStatus = BuyinStatus.Buyin;

    init() {
        this._setupEventListener();
        this.squid_rules.node.active = cv.GameDataManager.tRoomData.u32GameID === cv.Enum.GameId.Squid;

    }

    onDestroy(): void {
        this.tackBackQuestion_button.off(cc.Node.EventType.TOUCH_START, this._tackBackQuestionButtonTouchStart, this);
        this.tackBackQuestion_button.off(cc.Node.EventType.TOUCH_END, this._tackBackQuestionButtonTouchEnd, this);
        this.tackBackQuestion_button.off(cc.Node.EventType.TOUCH_CANCEL, this._tackBackQuestionButtonTouchCancel, this);

        this.exchangeRateTipButtonNodes.forEach((e) => {
            e.off(cc.Node.EventType.TOUCH_START, this._exchangeRateTipButtonTouchStart, this);
            e.off(cc.Node.EventType.TOUCH_END, this._exchangeRateTipButtonTouchEnd, this);
            e.off(cc.Node.EventType.TOUCH_CANCEL, this._exchangeRateTipButtonTouchCancel, this);
        });
    }

    onEnable() : void {
        this._toggleCurrencySymbol(cv.GameDataManager.tRoomData.currency == 101 ? ViewStyleType.USD : ViewStyleType.COIN);
    }


    updateLocalizedText(style: ViewStyleType,isFirstTimeBuyin: boolean = false) {
        this.exchangeRateTipTextLabels.forEach((e) => {
            e.string = cv.config.getStringData('Buyin_exchange_rate_tip');
        });

        this._toggleIcons(style);
        this._toggleCurrencySymbol(style);

        this.insufficientFundTextLabel.string = cv.config.getStringData(
            'GameScene_buyin_panel_insufficient_balance_txt'
        );
        this.balanceTextLabel.string = cv.config.getStringData('GameScene_buyin_panel_balance_funds_txt');
        if (style === ViewStyleType.USD) {
            this.bringTitleLabel.string = isFirstTimeBuyin? cv.config.getStringData("Bringin_usd_title_label") : cv.config.getStringData('Buyin_usd_title_label');
            this.currentCurrencyTextLabel.string = cv.config.getStringData('Buyin_usd_balance_label');
            this.otherCurrencyTextLabel.string = cv.config.getStringData('Buyin_coin_balance_label');
            this.setBuyInDetailTextLabel(0, true, isFirstTimeBuyin);
            this.notEnoughTextLabel.string = cv.config.getStringData('GameScene_buyin_panel_not_enough_both_txt');

            this.totalBuyinTextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_sureBuyInPanel_totalBuyin_txt'
            );
            this.sureBuyInPanelOkButtonLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_sureBuyInPanel_ok_button'
            );
            this.tackbackTitleTextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_tackbackTitle_usd_txt'
            );
            this.tackbackTip0TextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_tackbackTip0_txt_usd'
            );
            this.tackbackTip1TextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_tackbackTip1_txt_usd'
            );
            this.tackbackTip2TextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_tackbackTip2_txt_usd'
            );

            // should move this line since its not localization scope
            cv.worldNet.ExchangeGetUsdtConfigRequest();
        } else {
            this.bringTitleLabel.string = isFirstTimeBuyin? cv.config.getStringData("Bringin_coin_title_label") : cv.config.getStringData('Buyin_coin_title_label');
            this.currentCurrencyTextLabel.string = cv.config.getStringData('Buyin_coin_balance_label');
            this.otherCurrencyTextLabel.string = cv.config.getStringData('Buyin_usd_balance_label');
            this.setBuyInDetailTextLabel(0, false, isFirstTimeBuyin);
            this.notEnoughTextLabel.string = cv.config.getStringData('GameScene_buyin_panel_not_enough_both_txt');

            this.totalBuyinTextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_sureBuyInPanel_totalBuyin_txt'
            );
            this.sureBuyInPanelOkButtonLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_sureBuyInPanel_ok_button'
            );
            this.tackbackTitleTextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_tackbackTitle_txt'
            );
            this.tackbackTip0TextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_tackbackTip0_txt'
            );
            this.tackbackTip1TextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_tackbackTip1_txt'
            );
            this.tackbackTip2TextLabel.string = cv.config.getStringData(
                'GameScene_addSignPoker_panel_tackbackTip2_txt'
            );
        }
    }

    updateTackbackTipItemHeight() {
        this.tackbackTip0ItemNode.height = this.tackbackTip0TextLabel.node.height;
        this.tackbackTip1ItemNode.height = this.tackbackTip1TextLabel.node.height;
        this.tackbackTip2ItemNode.height = this.tackbackTip2TextLabel.node.height;
    }

    getKnobActiveStatus() {
        return this.knobActiveNode.active;
    }

    toggleKnobColor(value: boolean) {
        this.knobActiveNode.active = value;
        this.knobInactiveNode.active = !value;
    }

    _toggleIcons(style: ViewStyleType) {
        if (style === ViewStyleType.USD) {
            this.iconSprite.spriteFrame = this.usdIconSpriteFrame;
            this.currentCurrencyIconSprite.spriteFrame = this.usdIconSpriteFrame;
            this.otherCurrencyIconSprite.spriteFrame = this.goldCoinIconSpriteFrame;
        } else {
            this.iconSprite.spriteFrame = this.goldCoinIconSpriteFrame;
            this.currentCurrencyIconSprite.spriteFrame = this.goldCoinIconSpriteFrame;
            this.otherCurrencyIconSprite.spriteFrame = this.usdIconSpriteFrame;
        }
    }

    _toggleCurrencySymbol(style: ViewStyleType) {
        if (style === ViewStyleType.USD && !cv.tools.showChipAsBBorAnte()) {
            this.sliderCurrencySymbolNodes.forEach((e) => {
                e.active = true;
            });
        } else {
            this.sliderCurrencySymbolNodes.forEach((e) => {
                e.active = false;
            });
        }
    }

    _setupEventListener() {
        this.tackBackQuestion_button.on(cc.Node.EventType.TOUCH_START, this._tackBackQuestionButtonTouchStart, this);
        this.tackBackQuestion_button.on(cc.Node.EventType.TOUCH_END, this._tackBackQuestionButtonTouchEnd, this);
        this.tackBackQuestion_button.on(cc.Node.EventType.TOUCH_CANCEL, this._tackBackQuestionButtonTouchCancel, this);

        this.exchangeRateTipButtonNodes.forEach((e) => {
            e.on(cc.Node.EventType.TOUCH_START, this._exchangeRateTipButtonTouchStart, this);
            e.on(cc.Node.EventType.TOUCH_END, this._exchangeRateTipButtonTouchEnd, this);
            e.on(cc.Node.EventType.TOUCH_CANCEL, this._exchangeRateTipButtonTouchCancel, this);
        });
    }

    _tackBackQuestionButtonTouchStart() {
        this.takebackTips_img.active = true;
        // let worldPos = this.takeback_img_0.convertToWorldSpaceAR(cc.v2(0, 0));
        // let nodePos = this.takebackTips_Arrow.convertToWorldSpaceAR(worldPos);
        // this.takebackTips_Arrow.x = this.takebackTips_Arrow.x - nodePos.x;
        // this.takebackTips_Arrow.y = this.takebackTips_Arrow.y - nodePos.y + 37.1;

        const nodePos = this.takebackTips_img.parent.convertToNodeSpaceAR(this.tackBackPanel.parent.convertToWorldSpaceAR( this.tackBackPanel.position));
        this.takebackTips_img.position = new cc.Vec2(this.takebackTips_img.position.x, nodePos.y + 51);
        const arrowPos = this.takebackTips_Arrow.parent.convertToNodeSpaceAR(this.takeback_img_0.parent.convertToWorldSpaceAR(this.takeback_img_0.position));
        this.takebackTips_Arrow.x = arrowPos.x;
        this.updateTackbackTipItemHeight();  
    }

    _tackBackQuestionButtonTouchEnd() {
        this.takebackTips_img.active = false;
    }

    _tackBackQuestionButtonTouchCancel() {
        this.takebackTips_img.active = false;
    }

    _exchangeRateTipButtonTouchStart() {
        this.rateTipsPanelNodes.forEach((e)=>{
            e.active = true;
        });
    }

    _exchangeRateTipButtonTouchEnd() {
        this.rateTipsPanelNodes.forEach((e)=>{
            e.active = false;
        });
    }

    _exchangeRateTipButtonTouchCancel() {
        this.rateTipsPanelNodes.forEach((e)=>{
            e.active = false;
        });
    }

    setBuyinStatus(buyinStatus: BuyinStatus) {

        if (this._squidBuyInStatus === buyinStatus) return;
        this._squidBuyInStatus = buyinStatus;
        switch (this._squidBuyInStatus) {
            case BuyinStatus.Squid_BuyIn:
                this.squid_rules.node.active = true;
                this.squid_rebuy_in.active = false;
                break;
            case BuyinStatus.Squid_ReBuyIn:
                this.squid_rules.node.active = true;
                this.squid_rebuy_in.active = true;
                break;
            case BuyinStatus.Buyin:
            default:
                this.squid_rules.node.active = false;
                this.squid_rebuy_in.active = false;
                break;
        }
    }

    public setIconShow(isShow: boolean) {
        this.iconSprite.node.active = isShow;
    }

    public setBuyInDetailTextLabel(num: number, isUsdRoom: boolean, isFirstTimeBuyin:boolean) {
        if(cv.tools.showChipAsBBorAnte()) {
            this.buyInDetailTextLabel.string = cv.StringTools.formatC(cv.config.getStringData( isUsdRoom ? (isFirstTimeBuyin? "GameScene_bringin_panel_buyinDetail_usd_txt_bb": "GameScene_buyin_panel_buyinDetail_usd_txt_bb") : (isFirstTimeBuyin? "GameScene_bringin_panel_buyinDetail_gold_txt_bb": "GameScene_buyin_panel_buyinDetail_gold_txt_bb")), num);
        }else{
            this.buyInDetailTextLabel.string = cv.config.getStringData(isUsdRoom ? (isFirstTimeBuyin ? "GameScene_bringin_panel_buyinDetail_usd_txt" : "GameScene_buyin_panel_buyinDetail_usd_txt") : (isFirstTimeBuyin ? "GameScene_bringin_panel_buyinDetail_gold_txt" : "GameScene_buyin_panel_buyinDetail_gold_txt"));
        }
    }
}
