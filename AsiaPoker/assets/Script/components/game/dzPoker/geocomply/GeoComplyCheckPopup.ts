import cv from "../../../lobby/cv";

const {ccclass, property} = cc._decorator;

export enum eGeoComplyCheckPopupIconType {
    IN_PROGRESS = 0,
    VALID,
    INVALID
}
export interface IGCInProgressMsgParams {
    /** Type of icon */
    iconType?: eGeoComplyCheckPopupIconType,

    /** popup title */
    title?: string;

    /** popup message */
    text?: string;

    /** Okay button callback */
    okayCallback?: Function;

    /** Popup close callback */
    closeCallback?: Function;

    /** enable it to auto hide popup after some time */
    enableAutoHide?: boolean;
}

@ccclass
export default class GeoComplyCheckPopup extends cc.Component {
    private static instance: GeoComplyCheckPopup;

    public static getInstance(): GeoComplyCheckPopup {
        if (!this.instance || !this.instance.popUpPrefab) {
            GeoComplyCheckPopup.instance = new GeoComplyCheckPopup();
            GeoComplyCheckPopup.instance.init();
        }

        return GeoComplyCheckPopup.instance;
    };

    /** Load PopUpMsg prefab from resources */
    public init(): void {
        cv.resMgr.load("zh_CN/geocomply/GeoComplyCheckPopup", cc.Prefab, (prefab: cc.Prefab): void => {
            this.popUpPrefab = prefab;
            this.createPopUp();
        });
    }

    @property(cc.Label) titleLabel: cc.Label = null;
    @property(cc.Label) textLabel: cc.Label = null;
    @property(cc.Node) typeIcons: cc.Node[] = [];

    protected popUpPrefab: cc.Prefab = null;
    protected currentParams: IGCInProgressMsgParams = null;
    protected popUpNode: cc.Node = null;
    protected popUp: GeoComplyCheckPopup = null;
    
    /** Popup auto hide time in sec */
    private autoHideDelayTime: number = 5;
    private autoHideTimeOut: any = null;

    protected onLoad(): void {
    }

    protected onDisable(): void {
        // cv.tools.triggerPopupHideEvent();
    }

    private createPopUp(params: IGCInProgressMsgParams = null): void {
        if (this.popUpPrefab && cc.isValid(this.popUpPrefab, true)) {
            this.popUpNode = cc.instantiate(this.popUpPrefab);
            cc.game.addPersistRootNode(this.popUpNode);
            this.popUpNode.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TT;
            this.popUp = this.popUpNode.getComponent(GeoComplyCheckPopup);
            this.popUp.hide();
            if (params) {
                this.popUp.showMsgI18n(params);
            }
        }
        else {
            this.init();
        }
    }

    private show() {
        if(!this.isNodeActive(false)) {
            this.node.active = true;
        }

        this.clearAutoHideTimeOut();
    }

    private hide(forceClose: boolean = false): void {
        if(!forceClose) {
            this.currentParams?.closeCallback?.();
        }
        this.clearAutoHideTimeOut();
        this.node.active = false;
    }

    public hideAllPopup(forceClose: boolean = false) {
        if (this.node == null) { // the singleton one
            if (this.popUp && cc.isValid(this.popUp.node, true)) {
                this.unscheduleAllCallbacks();
                this.popUp.hideAllPopup(forceClose);
            }
        } else {
            this.hide(forceClose);
        }
    }

    public isNodeActive(strict: boolean = true): boolean {
        if (this.node == null) {    // if this is the singleton object (not the rendered one)
            return this.popUp?.isNodeActive(strict) ?? false;
        }
        else {      // if this is the rendered object
            return this.node.active;
        }
    }

    public showMsgI18n(params?: IGCInProgressMsgParams): void {
        if (this.node == null) {    // if this is the singleton object (not the rendered one)
            if (!this.popUp || !cc.isValid(this.popUpNode, true)) {     // if popup was not created yet
                this.createPopUp(params); // created popup renderer
            }
            else {       // if popup was created
                this.scheduleOnce(this.popUp.showMsgI18n.bind(this.popUp, params));
            }
        }
        else {
            this.updatePopUp(params);
        }
    }


    private updatePopUp(params?: IGCInProgressMsgParams) {
        if(!this.node.active) {
            // cv.tools.triggerPopupShowEvent();
        }
        this.currentParams = params;

        params.iconType = params.iconType || eGeoComplyCheckPopupIconType.IN_PROGRESS;

        // Enable given icon node
        for(let i = 0; i < this.typeIcons.length ; i++) {
            this.typeIcons[i].active = params.iconType == i;
        }

        let titleKey = params.title || (params.iconType == eGeoComplyCheckPopupIconType.IN_PROGRESS ? "VerifyingYourAccount" : "VerificationComplete");

        this.titleLabel.string = cv.config.getStringData(titleKey);

        let textKey = params.text || "GeoComplyVerifyingAccountPopupText";

        this.textLabel.string = cv.config.getStringData(textKey);

        this.show();

        if(params.enableAutoHide) {
            this.autoHideTimeOut = setTimeout(this.hide.bind(this), this.autoHideDelayTime * 1000);
        }
    }

    private clearAutoHideTimeOut() {
        if(this.autoHideTimeOut) {
            clearTimeout(this.autoHideTimeOut);
            this.autoHideTimeOut = null;
        }
    }

    onClickOkayButton() {
        this.hide();
        this.currentParams?.okayCallback?.();
    }

    onClickXCloseeButton() {
        this.hide();
    }
}