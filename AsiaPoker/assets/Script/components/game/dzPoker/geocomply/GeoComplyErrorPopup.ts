import cv from "../../../lobby/cv";

const {ccclass, property} = cc._decorator;

export interface IGCErrorMsgParams {
    /** popup title */
    title?: string;

    /** popup message */
    texts?: string[];

    /** instruction steps text to resoleve error */
    instructionSteps: string[];

    /** Confirm But<PERSON> text */
    confirmButtonText: string;

    /** to enable retry button */
    enableConfirmButton?: boolean; 

    /** retry button callback */
    confirmCallback?: Function;

    /** Confirm Button text */
    observeButtonText?: string;

    /** observe button callback */
    observeCallback?: Function;

    /** if observe callback needs to be called on click on close */
    callObserverCallbackOnCloseClick?: boolean;

    /** show pop up on cash game window */
    cashGameDirector?: cc.Director;
}

@ccclass("InstructionStepItem")
class InstructionStepItem {
    @property(cc.Label) text: cc.Label = null;
    @property(cc.Node) line: cc.Node = null;
}

@ccclass
export default class GeoComplyErrorPopup extends cc.Component {
    private static instance: GeoComplyErrorPopup;

    public static getInstance(): GeoComplyErrorPopup {
        if (!this.instance) {
            GeoComplyErrorPopup.instance = new GeoComplyErrorPopup();
            GeoComplyErrorPopup.instance.init();
        }
        return GeoComplyErrorPopup.instance;
    };

    /** Load PopUpMsg prefab from resources */
    public init(): void {
        cv.resMgr.load("zh_CN/geocomply/GeoComplyErrorPopup", cc.Prefab, (prefab: cc.Prefab): void => {
            if (!this.popUpPrefab) {
                this.popUpPrefab = prefab;
                this.createPopUp();
            }
        });
    }

    @property(cc.Node) mainPanel: cc.Node = null;
    @property(cc.Label) titleLabel: cc.Label = null;
    @property(cc.ScrollView) scrollView : cc.ScrollView = null;
    @property(cc.Node) maxSizeNode: cc.Node = null;
    @property(cc.Button) confirmButton: cc.Button = null;
    @property(cc.Label) lblConfirmButton: cc.Label = null;
    @property(cc.Label) lblObserveButton: cc.Label = null;
    @property(cc.Label) textLabels: cc.Label[] = [];
    @property({type: InstructionStepItem}) instructionSteps: InstructionStepItem[] = [];
    
    protected popUpPrefab: cc.Prefab = null;
    protected currentParams: IGCErrorMsgParams = null;
    protected popUpNode: cc.Node = null;
    protected popUp: GeoComplyErrorPopup = null;

    protected onLoad(): void {
    }

    protected onEnable(): void {
    
    }

    protected onDisable(): void {
       
    }

    private createPopUp(params: IGCErrorMsgParams = null): void {
        // avoid creating multiple UI nodes
        if (this.popUpPrefab && cc.isValid(this.popUpPrefab, true) && !cc.isValid(this.popUpNode, true)) {
            this.popUpNode = cc.instantiate(this.popUpPrefab);
            cc.game.addPersistRootNode(this.popUpNode);
            this.popUpNode.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TT;
            this.popUp = this.popUpNode.getComponent(GeoComplyErrorPopup);
            this.popUp.hide();
            if(params) {
                this.popUp.showMsg(params);
            }
            else if (this.currentParams) {
                // use cached params if it exists
                this.popUp.showMsg(this.currentParams);
            }
            else {
                this.popUp.hide();                
            }
        }
        else {
            // cache params if prefab is not yet loaded
            this.currentParams = params;
            this.init();
        }
    }

    public hide(): void {
        this.node.active = false;

        // clear params cache when popup is hidden
        this.currentParams = null;
    }

    public hideAllPopup() {
        if (this.node == null) { // the singleton one
            if (this.popUp && cc.isValid(this.popUp.node, true)) {
                this.unscheduleAllCallbacks();
                this.popUp.hideAllPopup();
            }
        } else {
            this.hide();
        }
    }

    private show() {
        this.node.scale = 1;
    }

    public showMsg(params?: IGCErrorMsgParams): void {
        if (this.node == null) {    // if this is the singleton object (not the rendered one)
            if (!this.popUp || !cc.isValid(this.popUpNode, true)) {     // if popup was not created yet
                this.createPopUp(params); // created popup renderer
            }
            else {       // if popup was created
                this.scheduleOnce(this.popUp.showMsg.bind(this.popUp, params));
            }
        }
        else {
            if (this.node.active == true && 
                this.currentParams.texts.toString() === params.texts.toString() &&
                this.currentParams.instructionSteps.toString() === params.instructionSteps.toString() &&
                this.currentParams.confirmButtonText === params.confirmButtonText) {     
                return;// if a popup is already showing same error then don't show it again
            }
            this.updatePopUp(params);
        }
    }


    private updatePopUp(params?: IGCErrorMsgParams) {
        this.currentParams = params;

        if(params.title) {
            this.titleLabel.string = params.title;
        }
        let stepsLength = params.instructionSteps.length;
        this.instructionSteps.forEach((item, id) => {
            if(id < stepsLength) {
                item.text.node.active = true;
                item.text.string = params.instructionSteps[id];
                item.line.active = (id != stepsLength-1);
            }
            else {
                item.text.node.active = false;
            }
        });

        this.confirmButton.interactable = params.enableConfirmButton != false;

        this.lblConfirmButton.string = cv.StringTools.convertText(params.confirmButtonText, 2);

        params.callObserverCallbackOnCloseClick = !!params.callObserverCallbackOnCloseClick;

        if(this.lblObserveButton) {
            if(!params.observeButtonText) {
                params.observeButtonText = cv.config.getStringData("Observe");
            }
            this.lblObserveButton.string = cv.StringTools.convertText(params.observeButtonText, 2);
        }
       
        let textArrLength = params.texts ? params.texts.length : 0;
        this.textLabels.forEach((item, id) => {
            if(id < textArrLength) {
                item.node.active = true;
                item.string = params.texts[id];
            }
            else {
                item.node.active = false;
            }
        });

        this.node.active = true;
        this.scrollView.node.parent.active = false;
        this.node.scale = 0;
        console.log("GeoComplyCocos ErrorPopup updatePopUp");
        this.unscheduleAllCallbacks();
        this.scheduleOnce(this.resetLayout.bind(this), 0.02);
    }


    resetLayout() {
        console.log("GeoComplyCocos ErrorPopup resetLayout");
        this.scrollView.node.parent.active = true;
        this.scrollView.enabled = false;
        this.scrollView.scrollToTop();

        this.scrollView.node.height = this.scrollView.content.height;
        this.scheduleOnce(this.updateLayout.bind(this), 0.15);
    }

    updateLayout() {
        console.log("GeoComplyCocos ErrorPopup updateLayout");
        let heightDiff = this.mainPanel.height - this.maxSizeNode.height
        if(heightDiff > 0) {
            this.scrollView.enabled = true;
            this.scrollView.node.height = this.scrollView.node.height - heightDiff;
        }
        this.scheduleOnce(this.show.bind(this), 0.03);
    }

    onClickConfirmButton() {
        this.confirmButton.interactable = false;
        this.currentParams.confirmCallback?.();
        setTimeout(() => {
            this.hide();
        }, 15);
    }

    onClickObserveButton() {
        this.currentParams.observeCallback?.();
        this.hide();
    }

    onClickXCloseeButton() {
        if(this.currentParams?.callObserverCallbackOnCloseClick) {
            this.currentParams.observeCallback?.();
        }
        this.hide();
    }
}
