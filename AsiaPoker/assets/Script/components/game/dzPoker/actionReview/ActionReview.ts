import game_protocol = require("./../../../../../Script/common/pb/gs_protocol");
import game_pb = game_protocol.protocol;
import cv from "../../../lobby/cv";
import { LANGUAGE_TYPE } from "../../../../common/tools/Enum";
import IgnoreActionReciewClose from "./IgnoreActionReviewClose";
const {ccclass, property} = cc._decorator;

// Action review panel in game
@ccclass
export default class ActionReview extends cc.Component {

    @property(cc.Node)
    rowTitle: cc.Node = null;

    @property(cc.Node)
    rowAllAnte: cc.Node = null;

    @property(cc.Node)
    rowAction: cc.Node = null;

    @property(cc.Node)
    rowMergeFold: cc.Node = null;

    @property(cc.Node)
    rowPool: cc.Node = null;

    @property(cc.Node)
    rowDivede: cc.Node = null;

    // 下注
    @property(cc.Color)
    colorBet: cc.Color = cc.Color.WHITE;

    // 加注
    @property(cc.Color)
    colorRaise: cc.Color = cc.Color.WHITE;

    @property(cc.Color)
    colorAllin: cc.Color = cc.Color.WHITE;

    // 跟注
    @property(cc.Color)
    colorCall: cc.Color = cc.Color.WHITE;

    // 让牌
    @property(cc.Color)
    colorCheck: cc.Color = cc.Color.WHITE;

    // 弃牌
    @property(cc.Color)
    colorFold: cc.Color = cc.Color.WHITE;

    @property(cc.SpriteFrame)
    tagBB: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    tagBB_en: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    tagSB: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    tagSB_en: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    tagStr: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    tagD: cc.SpriteFrame = null;

    @property(cc.Node)
    content: cc.Node = null;

    @property(cc.ScrollView)
    scroll: cc.ScrollView = null;

    @property(cc.Node)
    bg: cc.Node = null;

    private _maxHeight = 800;
    private _showBB = false;

    private _rowPoolMap: Map<cc.Node, cc.NodePool> = new Map();

    protected onLoad(): void {
        this._assignPool(this.rowTitle);
        this._assignPool(this.rowAllAnte);
        this._assignPool(this.rowAction);
        this._assignPool(this.rowMergeFold);
        this._assignPool(this.rowPool);
        this._assignPool(this.rowDivede);
    }

    protected onEnable(): void {
        if(this._getUpdateFlag()) {
            this._onActionHistoryUpdate();
        }
        cc.Canvas.instance.node.on(cc.Node.EventType.TOUCH_END, this.onGlobalClick, this, true);
        cv.MessageCenter.send("action_review_show")
        cv.MessageCenter.register("on_action_histoty_update", this._onActionHistoryUpdate.bind(this), this.node);
        cv.MessageCenter.register("action_review_hide", this._onMsgHide.bind(this), this.node);
    }

    onGlobalClick(e: cc.Event.EventTouch) {
        const target = e.target as cc.Node;
        if(!target.getComponent(IgnoreActionReciewClose)) {
            this.node.active = false;
        }
    }

    protected onDisable(): void {
        cc.Canvas.instance.node.off(cc.Node.EventType.TOUCH_END, this.onGlobalClick, this);
        cv.MessageCenter.unregister("on_action_histoty_update", this.node);
        cv.MessageCenter.unregister("action_review_hide", this.node);
    }

    private _onMsgHide() {
        this.node.active = false;
    }

    private _assignPool(template: cc.Node) {
        const pool = new cc.NodePool();
        this._rowPoolMap.set(template, pool);
        pool.put(template);
    }

    private _onActionHistoryUpdate() {
        this.updateView();
        this._setUpdateFlag(false);
    }

    private _getHistoryData(): game_pb.IActionHistoyItem[] {
        return [...cv.GameDataManager.tGameData.actionHistory];
    }

    private _getUpdateFlag() {
        const isShowBBNow = cv.tools.showChipAsBBorAnte();
        if(this._showBB !== isShowBBNow) return true;
        return cv.GameDataManager.tGameData.actionHistoryUpdated;
    }

    private _setUpdateFlag(flag: boolean) {
        this._showBB = cv.tools.showChipAsBBorAnte();
        cv.GameDataManager.tGameData.actionHistoryUpdated = flag;
    }

    public setMaxHeight(height: number) {
        this._maxHeight = height;
    }

    updateView() {
        const actionHistoryItems = this._getHistoryData();
        this._recover();

        let contentHeight = 0;
        for(let i = 0; i < actionHistoryItems.length; i++) {
            const actionItem = actionHistoryItems[i];
            const preActionItem = actionHistoryItems[i - 1];
            const nextActionItem = actionHistoryItems[i + 1];

            if(preActionItem && preActionItem.actionStage !== actionItem.actionStage) {
                contentHeight += this._addRowDevide();
            }

            if(!preActionItem || preActionItem.actionStage !== actionItem.actionStage) {
                contentHeight += this._addRowTitle(actionItem.actionStage);
            }

            if(actionItem.players.length > 1) {
                if(actionItem.actionType === game_pb.ActionType.Enum_Action_PostAnte) {
                    contentHeight += this._addRowAllAnte(actionItem);
                }else {
                    contentHeight += this._addRowMergeFold(actionItem);
                }
            }else {
                contentHeight += this._addRowAction(actionItem);
            }

            if(nextActionItem && nextActionItem.actionStage !== actionItem.actionStage) {
                contentHeight += this._addRowPool(actionItem);
            }
        }

        this.scheduleOnce(() => {
            this._updateContentHeight(contentHeight);
        })

        // hide if no action history
        if(actionHistoryItems.length === 0) {
            this.node.active = false;
        }
    }

    private _updateContentHeight(contentHeight: number) {
        const bgPadding = 800 - 718; // is's from design
        const maxContentH = this._maxHeight - bgPadding;

        contentHeight = Math.min(contentHeight, maxContentH);
        this.scroll.node.height = contentHeight;
        this.scroll.verticalScrollBar.node.height = contentHeight;
        
        this.content.parent.height = contentHeight;
        this.bg.height = contentHeight + bgPadding;

        this.scroll.scrollToBottom(0.1);
    }

    private _recover() {
        const children = [...this.content.children];
        while(children.length) {
            const child = children.pop();
            const pool = child['_pool_ref'];
            if(pool) {
                pool.put(child);
            }else {
                child.removeFromParent();
            }
        }
    }

    private _addRowNode(template: cc.Node): cc.Node {
        const pool = this._rowPoolMap.get(template);
        let node = pool.get();
        if(!node) {
            node = cc.instantiate(template);
        }
        node.setParent(this.content);
        node.active = true;
        node['_pool_ref'] = pool;

        return node;
    }

    private _addRowTitle(actionStage: game_pb.ActionHistoryStage) {
        const rowTitle = this._addRowNode(this.rowTitle)

        const keys = {
            [game_pb.ActionHistoryStage.Preflop]: "action_review_round_preflop",
            [game_pb.ActionHistoryStage.Flop]: "action_review_round_flop",
            [game_pb.ActionHistoryStage.Turn]: "action_review_round_turn",
            [game_pb.ActionHistoryStage.River]: "action_review_round_river",
        }

        const label = rowTitle.getComponentInChildren(cc.Label);
        label.string = this._getStringData(keys[actionStage]);

        return rowTitle.height;
    }

    private _addRowDevide() {
        const rowDivide = this._addRowNode(this.rowDivede);
        return rowDivide.height;
    }

    private _addRowPool(actionHistoryItem: game_pb.IActionHistoyItem) {
        const rowPool = this._addRowNode(this.rowPool);

        const label = rowPool.getChildByName("label_action").getComponent(cc.Label);
        const labelAmount = rowPool.getChildByName("label_amount").getComponent(cc.Label);
        this._setAmount(label, labelAmount, actionHistoryItem.potAmount, this._getStringData("GameScene_gameMain_panel_dichiword_text"));
        
        return rowPool.height;
    }

    private _addRowAllAnte(actionHistoryItem: game_pb.IActionHistoyItem) {
        const rowAllAnte = this._addRowNode(this.rowAllAnte);

        const label = rowAllAnte.getChildByName("label_action").getComponent(cc.Label);
        const actionStr = this._getStringData("action_review_all_players_ante");
        label.string = actionStr;

        const amount = actionHistoryItem.amount;
        const labelAmount = rowAllAnte.getChildByName("label_amount").getComponent(cc.Label);
        this._setAmount(label, labelAmount, amount, actionStr);

        return rowAllAnte.height;
    }
    

    private _addRowMergeFold(actionHistoryItem: game_pb.IActionHistoyItem) {
        const rowMergeFold = this._addRowNode(this.rowMergeFold);

        const label = rowMergeFold.getComponentInChildren(cc.Label);
        const nameJoin = actionHistoryItem.players.map((player) => {
            return player.nickname;
        }).join("、");
        const actionStr = this._getStringData("ActionTips1");
        label.string = nameJoin + " " + actionStr;
        //force update size
        label["_forceUpdateRenderData"]();

        rowMergeFold.height = label.node.height;

        return rowMergeFold.height;
    }

    private _addRowAction(actionHistoryItem: game_pb.IActionHistoyItem) {
        const rowAction = this._addRowNode(this.rowAction);

        const nickname = actionHistoryItem.players[0].nickname;
        const lbName = rowAction.getChildByName("label_name").getComponent(cc.Label);
        lbName.string = nickname;
        const maxNameLen = 7;

        cv.StringTools.truncateLabelText(lbName, maxNameLen * lbName.fontSize);

        // set tag
        const playerSeatType = actionHistoryItem.playerSeatType;
        const tagNode = rowAction.getChildByName("tag");
        this._updateTag(tagNode, playerSeatType);
        // lbName._updateRenderData(true);
        this.schedule(() => {
            tagNode.x = lbName.node.x + lbName.node.width + 10 + tagNode.width * tagNode.anchorX;
        })
        // set action and amount
        const actionType = actionHistoryItem.actionType;
        const amount = actionHistoryItem.amount;
        const lbAction = rowAction.getChildByName("label_action").getComponent(cc.Label);
        const lbAmount = rowAction.getChildByName("label_amount")?.getComponent(cc.Label);

        const actionTexts: {[key: number]: string} = {
            [game_pb.ActionType.Enum_Action_PostSB]: "action_review_post_sb",
            [game_pb.ActionType.Enum_Action_PostBB]: "action_review_post_bb",
            [game_pb.ActionType.Enum_Action_PostButtomBlind]: "action_review_post_buttom_blind",
            [game_pb.ActionType.Enum_Action_Straddle]: "action_review_post_straddle",
            [game_pb.ActionType.Enum_Action_PostMissingBlind]: "action_review_post_missed_blind",
            [game_pb.ActionType.Enum_Action_Call]: "action_review_call",
            [game_pb.ActionType.Enum_Action_Bet]: "action_review_bet",
            [game_pb.ActionType.Enum_Action_Raise]: "action_review_raise",
            [game_pb.ActionType.Enum_Action_Allin]: "action_review_all_in",
            [game_pb.ActionType.Enum_Action_Check]: "action_review_check",
            [game_pb.ActionType.Enum_Action_Fold]: "action_review_fold",
            [game_pb.ActionType.Enum_Action_PostAnte]: "action_review_post_ante",
        }

        const actionColors: {[key: number]: cc.Color} = {
            [game_pb.ActionType.Enum_Action_Bet]: this.colorBet,
            [game_pb.ActionType.Enum_Action_Raise]: this.colorRaise,
            [game_pb.ActionType.Enum_Action_Allin]: this.colorAllin,
            [game_pb.ActionType.Enum_Action_Call]: this.colorCall,
            [game_pb.ActionType.Enum_Action_Check]: this.colorCheck,
            [game_pb.ActionType.Enum_Action_Fold]: this.colorFold,
        }

        const actionColor = actionColors[actionType] ?? cc.Color.WHITE;
        const actionText = this._getStringData(actionTexts[actionType]);

        lbAction.node.color = actionColor;
        lbAmount.node.color = actionColor;
        this._setAmount(lbAction, lbAmount, amount, actionText);

        let isSelf = this._isSelf(actionHistoryItem.players[0].playerid);
        rowAction.getChildByName("highlight").active = isSelf;

        return rowAction.height;
    }

    private _updateTag(tag: cc.Node, playerSeatType: game_pb.PlayerSeatType) {
        // playerSeatType = game_pb.PlayerSeatType[playerSeatType];
        const sprite = tag.getComponent(cc.Sprite);

        let spriteFrame: cc.SpriteFrame = null;
        if(playerSeatType === game_pb.PlayerSeatType.Dealer) {
            spriteFrame = this.tagD;
        }else if(playerSeatType === game_pb.PlayerSeatType.BigBlind) {
            spriteFrame = this.tagBB_en;
            if(this._isChinese()) {
                spriteFrame = this.tagBB;
            }
        }else if(playerSeatType === game_pb.PlayerSeatType.SmallBlind) {
            spriteFrame = this.tagSB_en;
            if(this._isChinese()) {
                spriteFrame = this.tagSB;
            }
        }else if(playerSeatType === game_pb.PlayerSeatType.Straddle) {
            spriteFrame = this.tagStr;
        }else {
            tag.active = false;
            return
        }

        sprite.spriteFrame = spriteFrame;
        tag.active = true;
    }

    private _setAmount(label: cc.Label, label_amount: cc.Label, amount: number, prefix: string) {
        const isShowBBMode = cv.tools.showChipAsBBorAnte();
        const amountStr = amount ? cv.StringTools.serverGoldToShowString(amount, true) : "";
        if(isShowBBMode) {
            label.string = prefix + " ";
            label.node.active = true;
            if(amountStr) {
                label_amount.string = amountStr;
                label_amount.node.active = true;
                this.scheduleOnce(() => {
                    label_amount.node.x = label.node.x + label.node.width + label_amount.node.width * label_amount.node.anchorX;
                })
            }else {
                label_amount.node.active = false;
            }
        }else {
            label_amount.node.active = false;
            label.string = prefix + " " + amountStr;
            label.node.active = true;
        }
    }

    private _getStringData(key: string): string {
        return cv.config.getStringData(key) || key;
    }

    private _isChinese() {
        return cv.config.getCurrentLanguage() === LANGUAGE_TYPE.zh_CN;
    }

    private _isSelf(playerId: number) {
        return playerId === Number(cv.dataHandler.getUserData().user_id);
    }

    protected update(dt: number): void {
       const bar = this.scroll.verticalScrollBar;
       bar.node.active = this.content.height > this.scroll.node.height;
    }
}