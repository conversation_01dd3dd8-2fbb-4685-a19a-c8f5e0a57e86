const {ccclass, property} = cc._decorator;

/**
 * Marker component used to exclude nodes from triggering the close behavior of ActionView when clicking outside.
 *
 * Nodes with this component will NOT cause ActionView to close when clicked.
 * 
 * This is a logic-free, marker-only component designed purely for clarity and maintainability.
 *
 * It improves maintainability by avoiding hardcoded exclusions and making the UI logic more declarative.
 */

@ccclass
export default class IgnoreActionReciewClose extends cc.Component {
}
