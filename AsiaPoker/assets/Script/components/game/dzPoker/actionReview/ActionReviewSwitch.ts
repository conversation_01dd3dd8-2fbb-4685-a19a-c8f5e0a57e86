import cv from "../../../lobby/cv";
import ActionReview from "./ActionReview";

const {ccclass, property} = cc._decorator;

@ccclass
export default class ActionReviewSwtich extends cc.Component {

    @property(cc.Prefab)
    actionReviewPrefab: cc.Prefab = null;

    @property(cc.Node)
    container: cc.Node = null;

    // action view panel bottom can't overlap with bottomNode
    @property(cc.Node)
    bottomNode: cc.Node = null;

    private _actionReview: cc.Node = null;

    protected onEnable(): void {
        cv.MessageCenter.register("on_sitdown_succ", this.showActionReviewTip.bind(this), this.node);
        this.showActionReviewTip();
    }

    protected onDisable(): void {
        cv.MessageCenter.unregister("on_sitdown_succ", this.node);
    }

    async showActionReviewTip() {
        if(!cv.GameDataManager.tRoomData.GetTablePlayer(cv.dataHandler.getUserData().u32Uid)) {
            return;
        }
        const showed = cv.tools.GetStringByCCFile("action_review_tip_showed");
        if (showed) {
            return;
        }
        cv.tools.SaveStringByCCFile("action_review_tip_showed", "1");

        await cv.tools.waitFor(1, this);
        const position = cc.v2(this.node.x, this.node.y - this.node.height * this.node.anchorY + 10);
        const coachTip = cv.tooltips.showTip(cv.tooltips.TipType.CoachTip);
        coachTip.show(cv.config.getStringData("action_review_guide"));
        coachTip.addCloseMessage("action_review_show");
        coachTip.node.setPosition(position);
    }

    showActionReview() {
        if(!cv.GameDataManager.tRoomData.GetTablePlayer(cv.dataHandler.getUserData().u32Uid)) {
            return;
        }
        if(cv.GameDataManager.tGameData.actionHistory.length === 0) {
            return;
        }
        let node = this._actionReview;
        if(!node) {
            node = cc.instantiate(this.actionReviewPrefab);
            node.setParent(this.container);
            this._actionReview = node;
        }
        node.active = true;

        const position = this.node.convertToWorldSpaceAR(cc.v2(0, -this.node.height * this.node.anchorY));
        node.setPosition(node.parent.convertToNodeSpaceAR(position));

        const bottomPosition = this.bottomNode.convertToWorldSpaceAR(cc.v2(0, this.bottomNode.height * (1 - this.bottomNode.anchorY)));
        const maxPanelHeight = position.y - bottomPosition.y;
        node.getComponent(ActionReview).setMaxHeight(maxPanelHeight);
    }
}
