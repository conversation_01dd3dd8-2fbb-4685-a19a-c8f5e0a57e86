import { PriorityQueue } from "../../../common/tools/ds";
import cv from "../../lobby/cv";

export enum InGameMessageType {
    NormalText, 
    RichText,
    MessageWithTimer,    
}

export interface InGameMessage {
    type: InGameMessageType,
    duration: number,
    position?: cc.Vec2,
    anchor?: cc.Vec2,
    labelSize?: cc.Size,
    bgSize?: cc.Size,
    content:  string,
    fontSize?:number,
    priority?: number,
}

export enum InGameMessagePriority {
    IMMEDIATE = 1,
    FIRST_IN_QUEUE,
    LAST_IN_QUEUE
}

export const InGameMessageKey  = "inGameMessage"


const { ccclass, property } = cc._decorator;

@ccclass
export abstract class BaseGameTableMessage extends cc.Component {
    @property(cc.Label) remaining_timer_sec: cc.Label = null;
    @property(cc.Label) timer_header: cc.Label = null;
    @property(cc.Node) timerContainer: cc.Node = null;
    @property(cc.RichText) msgLabel: cc.RichText = null;
    @property(cc.Node) msgLabelContainer: cc.Node = null;

    protected _seconds: number = null;
    protected _defaultDuration: number = 2.4;
    protected msgQueue: PriorityQueue<InGameMessage> = new PriorityQueue();
    protected _currMsg: InGameMessage;

    onLoad() {
        cv.MessageCenter.register(InGameMessageKey, this.onMsgReceive.bind(this), this);
    }

    protected onDestroy(): void {
        cv.MessageCenter.unregister(InGameMessageKey, this);
    }

    onMsgReceive(msg: InGameMessage) {
        this.msgQueue.enqueue(msg, msg.priority ||= InGameMessagePriority.LAST_IN_QUEUE);
        if (msg.priority === InGameMessagePriority.IMMEDIATE ||
            (!this.timerContainer.active && !this.msgLabelContainer.active)) {
            this.showMessage();
        }
    }

    showMessage() {
        this.resetFlow();
        this._currMsg = this.msgQueue.dequeue();
        if (!this._currMsg) return;

        switch (this._currMsg.type) {
            case InGameMessageType.NormalText:
            case InGameMessageType.RichText:
                this.showInFloorMsg();
                break;
            case InGameMessageType.MessageWithTimer:
                this.showTimerForSeconds();
                break;
        }

        this.scheduleOnce(this.showMessage.bind(this), this._currMsg.duration || 3);
    }

    protected resetFlow() {
        this.unscheduleAllCallbacks();
        this.timerContainer.active = false;
        this.msgLabelContainer.active = false;
    }

    protected showTimerForSeconds() {
        if (this._currMsg?.duration <= 0) return;

        const pos = this.getTimerPosition();
        if (!pos) return;

        this.timer_header.string = this._currMsg.content;
        this.timerContainer.position = pos;
        this._seconds = this._currMsg.duration;

        this.timerContainer.active = true;
        this.updateCountdown();
        this.schedule(this.updateCountdown.bind(this), 1, this._currMsg.duration - 1, 1);
    }

    protected updateCountdown() {
        if (this._seconds <= 1) {
            this.unschedule(this.updateCountdown);
            this.removeTimer();
        } else {
            this._seconds--;
            this.remaining_timer_sec.string = this._seconds + "s";
        }
    }

    protected removeTimer() {
        this.unschedule(this.updateCountdown);
        this.timerContainer.active = false;
    }

    protected showInFloorMsg() {
        if (!this._currMsg?.content) return;

        this._currMsg.position ||= this.getMessagePosition();
        this.showInFloorMsgWithPos();
    }

    protected showInFloorMsgWithPos() {
        this.msgLabel.string = this._currMsg.content;
        this.msgLabel.fontSize = this._currMsg.fontSize || 40;
        this.msgLabelContainer.active = true;
        this.msgLabelContainer.anchorY = 0;
        this.msgLabelContainer.stopAllActions();
        if (this._currMsg.bgSize) this.msgLabelContainer.setContentSize(this._currMsg.bgSize);
        if (this._currMsg.labelSize) this.msgLabel.node.setContentSize(this._currMsg.labelSize);
        if(this._currMsg.anchor) this.msgLabelContainer.setAnchorPoint(this._currMsg.anchor);
        this.msgLabelContainer.position = this._currMsg.position;
         this.msgLabelContainer.getComponent(cc.Layout)?.updateLayout();
        this.msgLabelContainer.runAction(cc.sequence(
            cc.show(),
            cc.scaleTo(0.1, 1.0),
            cc.delayTime(this._currMsg.duration || this._defaultDuration),
            cc.callFunc(() => {
                this.msgLabelContainer.active = false;
                this.msgLabelContainer.anchorY = 0;
            })
        ));
    }

    protected abstract getTimerPosition(): cc.Vec2;
    protected abstract getMessagePosition(): cc.Vec2;
}