import cv from "../../lobby/cv";
import { menu } from "./menu";
import GameDataManager from "./data/GameDataManager";
import { PlayerInfo } from "./data/RoomData";
import { GameMain } from "./GameMain";
import { GameReview } from "./gameReview/GameReview";
import FaceView from "./danmu/FaceView";
import FaceBarrage from "./danmu/FaceBarrage";
import { RoleInfoSet } from "../../lobby/hall/RoleInfoSet";
import { SliderVerify } from "../../lobby/sliderVerify/SliderVerify";
import { GiftEntrance } from "./gift/GiftEntrance";
import { DiscoverGameType } from "../../lobby/hall/FindView";
import GameJackpotNumberPanelManager from "./GameJackpotNumberPanelManager";
import EmojiLoader from "../../../../emoji/script/EmojiLoader";
import FriendLinesLoader, { Tabletype } from "../../../common/friendlines/FriendLinesLoader";
import { CashGameFeatureCreator } from"../../../features/cashGames/CashGameFeature";
import game_protocol = require("./../../../../Script/common/pb/gs_protocol");
import { registerCashGameFeatrueData } from '../../../features/Feature';
import RuleDiscription from "./RuleDiscription";
import { ChangeCard } from "./ChangeCard";

const { ccclass, property } = cc._decorator;

@ccclass
export class GameScene extends cc.Component {
    @property(cc.Node) gameMain_panel: cc.Node = null;
    @property(cc.Node) card_panel: cc.Node = null;

    @property(cc.Node) voice_panel: cc.Node = null;
    @property(cc.Sprite) recordComplete_img: cc.Sprite = null;
    @property(cc.Sprite) recording_img: cc.Sprite = null;

    @property(cc.Prefab) face_Panel_prefab: cc.Prefab = null;
    @property(cc.Node) emoji_fullscreen: cc.Node = null;
    @property(cc.Prefab) face_barrage_prefab: cc.Prefab = null;
    face_Panel: cc.Node = null;
    @property(cc.Prefab) danmu_Panel_prefab: cc.Prefab = null;
    danmu_view: cc.Node = null;
    @property(cc.Node) danmu_Panel: cc.Node = null;

    @property(cc.Prefab) changeCard_panel_prefab: cc.Prefab = null;
    changeCard: ChangeCard = null;

    @property(cc.Prefab) menu_Panel_prefab: cc.Prefab = null;
    menu_Panel: cc.Node = null;

    @property(FriendLinesLoader) friendLinesLoader: FriendLinesLoader = null;

    @property(cc.Node) gameIcon_img: cc.Node = null;
    @property(cc.Node) gameIcon_img_star: cc.Node = null;
    @property(cc.Node) game_bg: cc.Node = null;
    @property(cc.Node) publicCard_panel: cc.Node = null;

    @property(cc.Prefab) currentTime_prefab: cc.Prefab = null;
    @property(cc.Prefab) buyinList_prefab: cc.Prefab = null;
    currentTime_panel: cc.Node = null;

    dairu_panel: cc.Node = null;

    @property(cc.Prefab) ruleDiscription_panel_prefab: cc.Prefab = null;
    ruleDiscription: RuleDiscription = null;
    @property(cc.Prefab) allreview_prefab: cc.Prefab = null;
    allreview_panel: cc.Node = null;

    @property(cc.Prefab) buyin_prefab: cc.Prefab = null;
    buyin_panel: cc.Node = null;

    @property(cc.Prefab) gift_prefab: cc.Prefab = null;
    giftEntrance: GiftEntrance = null;

    @property(cc.Prefab) sliderVerify_prefab: cc.Prefab = null;
    sliderVerify_panel: SliderVerify = null;

    @property(cc.Prefab) insuranceEntrance_prefab: cc.Prefab = null;
    insurance_panel: cc.Node = null;

    @property(cc.Prefab) insuranceHitOutsTips_prefab: cc.Prefab = null;
    insuranceHitOutsTips: cc.Node = null;

    @property(cc.Prefab) recallbuyin_prefab: cc.Prefab = null;
    recallbuyin_panel: cc.Node = null;

    @property(cc.Prefab) autorecallbuyin_prefab: cc.Prefab = null;
    autorecallbuyin_panel: cc.Node = null;

    @property(cc.Prefab) card: cc.Prefab = null;

    @property(cc.Prefab) ChipsMoveprefab: cc.Prefab = null;

    @property(cc.Prefab) hoseOwer_prefab: cc.Prefab = null;
    hoseOwer_panel: cc.Node = null;

    @property(cc.Prefab) pause_prefab: cc.Prefab = null;
    pausePoker_panel: cc.Node = null;

    @property(cc.Prefab) faceSpine_prefab: cc.Prefab = null;

    @property(cc.Prefab) starInfo: cc.Prefab = null;

    @property(cc.Button) buttonFace: cc.Button = null;

    @property(cc.Label) recordTxt: cc.Label = null;
    @property(cc.Label) recordTimeTxt: cc.Label = null;

    @property(cc.Prefab) guess_hand_card_prefab: cc.Prefab = null;
    guess_hand_card: cc.Node = null;

    @property(cc.Prefab) activityPref: cc.Prefab = null;
    @property(cc.Prefab) prefab_roleInfoSet: cc.Prefab = null;
    @property(GameJackpotNumberPanelManager) gameJackPotNumberPanelManager: GameJackpotNumberPanelManager = null;
    @property(cc.Node) img_star_LS_EN: cc.Node = null;
    @property(cc.Node) img_star_LS_CH: cc.Node = null;

    public gameSeatIsTurning: boolean = false;
    public roomHasEnd: boolean = false;

    gameMain: GameMain = null;

    @registerCashGameFeatrueData
    @property(cc.Prefab) prefeb_feature: cc.Prefab = null;
   
    constructor() {
        super();
    }

    onLoad() {
        cv.config.setCurrentScene(cv.Enum.SCENE.GAME_SCENE);
        cv.config.adaptScreen(this.node);
        cv.GameDataManager.tRoomData.resetVoice();

        this.gameMain = this.gameMain_panel.getComponent(GameMain);

        this.guess_hand_card = cc.instantiate(this.guess_hand_card_prefab);
        this.node.addChild(this.guess_hand_card, cv.Enum.ZORDER_TYPE.ZORDER_6);
        this.guess_hand_card.active = false;

        if (this.isGameStarSeat()) {
            // 实例化"礼物模块入口"(层级要低于弹幕)
            let gift_inst: cc.Node = cc.instantiate(this.gift_prefab);
            this.giftEntrance = gift_inst.getComponent(GiftEntrance);
            this.gameMain_panel.addChild(this.giftEntrance.node, cv.Enum.ZORDER_TYPE.ZORDER_4);
            this.giftEntrance.node.active = false;

            // 弹幕
            this.face_Panel = cc.instantiate(this.face_barrage_prefab);
            this.node.addChild(this.face_Panel, cv.Enum.ZORDER_TYPE.ZORDER_7);
            this.face_Panel.name = "facepanel";
            const faceBarrage: FaceBarrage = this.face_Panel.getComponent(FaceBarrage);
            faceBarrage?.danmuController.setParentNode(this.danmu_Panel);
            faceBarrage?.setGameScene(this);
            faceBarrage?.emojiController.setupEmoji(this.emoji_fullscreen);
        }
        else {
            this.face_Panel = cc.instantiate(this.face_Panel_prefab);
            this.node.addChild(this.face_Panel, cv.Enum.ZORDER_TYPE.ZORDER_7);
            this.face_Panel.name = "facepanel";
            const faceView: FaceView = this.face_Panel.getComponent(FaceView);
            faceView?.danmuController.setParentNode(this.danmu_Panel);
            faceView?.setGameScene(this);
            faceView?.emojiController.setupEmoji(this.emoji_fullscreen);
        }

        this.pausePoker_panel = cc.instantiate(this.pause_prefab);
        this.node.addChild(this.pausePoker_panel);

        this.menu_Panel = cc.instantiate(this.menu_Panel_prefab);
        this.node.addChild(this.menu_Panel);

        this.friendLinesLoader.init(this.gameMain, Tabletype.NormalGame);

        this.buyin_panel = cc.instantiate(this.buyin_prefab);
        this.node.addChild(this.buyin_panel, cv.Enum.ZORDER_TYPE.ZORDER_7);

        this.recallbuyin_panel = cc.instantiate(this.recallbuyin_prefab);
        this.node.addChild(this.recallbuyin_panel);

        this.autorecallbuyin_panel = cc.instantiate(this.autorecallbuyin_prefab);
        this.node.addChild(this.autorecallbuyin_panel);

        this.menu_Panel.getComponent(menu).setGameScene(this);
    
        this.gameMain.menu_btn_red_alert.active = this.menu_Panel.getComponent(menu).isShowPos();

        // 实例化"真人验证"模块
        this.sliderVerify_panel = SliderVerify.initSingleInst(this.sliderVerify_prefab, this.node, cv.Enum.ZORDER_TYPE.ZORDER_TOP);

        // 实例化"保险"模块
        this.insurance_panel = cc.instantiate(this.insuranceEntrance_prefab);
        this.node.addChild(this.insurance_panel, cv.Enum.ZORDER_TYPE.ZORDER_5);

        // 实例化"保险提示动画"模块
        this.insuranceHitOutsTips = cc.instantiate(this.insuranceHitOutsTips_prefab);
        this.node.addChild(this.insuranceHitOutsTips, cv.Enum.ZORDER_TYPE.ZORDER_4);

        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.Allin) {

        }
        else {
            this.gameMain.setGameScene(this);
            this.recallbuyin_panel.getComponent("RecallBuyin").setGameMain(this.gameMain);
            this.autorecallbuyin_panel.getComponent("AutoRecallBuyin").setGameMain(this.gameMain);
            this.changeCard = cc.instantiate(this.changeCard_panel_prefab).getComponent(ChangeCard);
            this.changeCard.setGameMain(this.gameMain);
            this.node.addChild(this.changeCard.node, cv.Enum.ZORDER_TYPE.ZORDER_5);
            this.changeCard.node.active = false;
        }

        this.allreview_panel = GameReview.getSinglePrefabInst(this.allreview_prefab);
        this.hoseOwer_panel = cc.instantiate(this.hoseOwer_prefab);
        this.node.addChild(this.hoseOwer_panel);
        this.hoseOwer_panel.getComponent("HouseOwer").setGameScene(this)

        this.gameJackPotNumberPanelManager.init(this.node);

        this.currentTime_panel = cv.action.addChildToScene(this, this.currentTime_prefab, [], cv.Enum.ZORDER_TYPE.ZORDER_6, true);
        this.dairu_panel = cc.instantiate(this.buyinList_prefab);
        this.node.addChild(this.dairu_panel);

        this.gameIcon_img_star.active = false;
        this.gameIcon_img.getComponent(cc.Sprite).sizeMode = cc.Sprite.SizeMode.RAW;
        this.buyin_panel.active = false;
        this.recallbuyin_panel.active = false;
        this.autorecallbuyin_panel.active = false;
        this.hoseOwer_panel.active = false;
        this.currentTime_panel.active = false;
        this.pausePoker_panel.active = false;
        this.dairu_panel.active = false;

        const isNewYear: boolean = cv.config.isShowNewYear();
        cc.find("newyear_panel", this.node).active = isNewYear;

        this.menu_Panel.active = false;
        this.game_bg.active = true;

        cv.MessageCenter.register("on_NoticeBuyin", this.onNoticeBuyin.bind(this), this.node);

        this.onNoticeBuyin(null);
        this.AdaptiveExpand();
        this.buttonFace.node.on("click", (event: cc.Event): void => { this.onbtnFaceClick(event); }, this);
        this.gameJackPotNumberPanelManager.setSafeArea(this.node);

        EmojiLoader.instance.initialize();
        // add game feature
        CashGameFeatureCreator.addFeatureByGameId(cv.roomManager.getCurrentGameID(),this.node);
        cv.worldNet.requestBackpack();
    }

    public start() {
        if (!cv.dataHandler.getActivityData().isAvatar()) {
            const node = cv.action.addChildToScene(this, this.activityPref, [], cv.Enum.ZORDER_TYPE.ZORDER_ACTIVITY, true);
            node.name = "activityView";
            const roleInfoSet = cv.action.addChildToScene(this, this.prefab_roleInfoSet, [], undefined, true);
            roleInfoSet.name = "roleInfoSet";
            cv.MessageCenter.register("roleInfoSet_setAvatar", this.setAvatar.bind(this), this.node);
        }

        if (cv.GameDataManager.bIsAuthMicphone == false) {
            cv.native.AuthMicphone();
            cv.GameDataManager.bIsAuthMicphone = true;
        }
        this.initLanguage();

        const str: string = cv.tools.GetStringByCCFile('last_gameType');
        const gameType = cv.StringTools.getArrayLength(str) > 0 ? cv.Number(str) : DiscoverGameType.DZPK;
        if (gameType === DiscoverGameType.DZPK && (cv.GameDataManager.tRoomData.pkRoomParam.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short || cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Bet ) ) {
            this._showGameEnteredToast();
        }
    }

    public onDestroy() {
        cv.gameNet.stopVoice();
        // 取消明星桌动画定时器
        this.unscheduleAllCallbacks();
        cv.MessageCenter.unregister("on_NoticeBuyin", this.node);
        cv.MessageCenter.unregister("roleInfoSet_setAvatar", this.node);
        cv.GameDataManager.tRoomData.resetVoice();
    }

    public initLanguage() {
        let gameMain_panel = cc.find("gameMain_panel", this.node);
        let actionButton_panel = gameMain_panel.getChildByName("actionButton_panel");
        cv.StringTools.setLabelString(gameMain_panel, "waitForStart_img/waiting_txt", "GameScene_gameMain_panel_waitForStart_img_waiting_txt");
        cv.StringTools.setLabelString(actionButton_panel, "dichi_button0/dichi_wordText0", "GameScene_gameMain_panel_controlButton_panel_dichi_button0_dichi_wordText0");
        cv.StringTools.setLabelString(actionButton_panel, "dichi_button1/dichi_wordText1", "GameScene_gameMain_panel_controlButton_panel_dichi_button1_dichi_wordText1");
        cv.StringTools.setLabelString(actionButton_panel, "dichi_button2/dichi_wordText2", "GameScene_gameMain_panel_controlButton_panel_dichi_button2_dichi_wordText2");
        cv.StringTools.setLabelString(actionButton_panel, "dichi_button3/dichi_wordText3", "GameScene_gameMain_panel_controlButton_panel_dichi_button3_dichi_wordText3");
        cv.StringTools.setLabelString(actionButton_panel, "dichi_button4/dichi_wordText4", "GameScene_gameMain_panel_controlButton_panel_dichi_button4_dichi_wordText4");

        let actionButton_panel2 = gameMain_panel.getChildByName("actionButton_panel_2");
        cv.StringTools.setLabelString(actionButton_panel2, "dichi_button1/dichi_wordText1", "GameScene_gameMain_panel_controlButton_panel_dichi_button1_dichi_wordText1");

        let freeFill_button_path = cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_Button_big_01");
        cv.resMgr.setButtonFrame(actionButton_panel.getChildByName("freeFill_button"), freeFill_button_path, freeFill_button_path, freeFill_button_path, freeFill_button_path);
        cv.resMgr.setSpriteFrame(actionButton_panel.getChildByName("giveUpRed"), cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_Button_big_03"));
        cv.resMgr.setSpriteFrame(actionButton_panel.getChildByName("followFl_Blue"), cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_Button_big_02x2"));

        cv.resMgr.setButtonFrame(gameMain_panel.getChildByName("deal_button"),
            cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_Button_big_14"),
            cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_Button_big_141"),
            cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_Button_big_14"),
            cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_Button_big_14"));
        cv.resMgr.setButtonFrame(gameMain_panel.getChildByName("forceShowCard_button"),
            cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_force_showCardOn"),
            cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_force_showCardDown"),
            cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_force_showCardOn"),
            cv.config.getLanguagePath("game/dzpoker/ui/gameMain/game_force_showCardOn"));
        cv.StringTools.setLabelString(gameMain_panel, "backGame_button/Label", "GameScene_gameMain_panel_backGame_button");

        this.recordTxt.string = cv.config.getStringData("GameScene_voice_panel_cdTime_recording_img_record_txt");
        this.recordTimeTxt.string = cv.config.getStringData("GameScene_voice_panel_cdTime_recording_img_recordTime_txt");

        let guess_panel = this.guess_hand_card.getChildByName("guess_panel");
        let continue_panel = this.guess_hand_card.getChildByName("continue_panel");
        cv.StringTools.setLabelString(guess_panel, "guess_button_1/title_label", "GameScene_gameMain_panel_guess_hand_card_guess_panel_guess_button_title_label");
        cv.StringTools.setLabelString(guess_panel, "guess_button_2/title_label", "GameScene_gameMain_panel_guess_hand_card_guess_panel_guess_button_title_label");
        cv.StringTools.setLabelString(guess_panel, "close_button/label", "GameScene_gameMain_panel_guess_hand_card_guess_panel_guess_close_button_label");
        cv.StringTools.setLabelString(continue_panel, "continue_button/label", "GameScene_gameMain_panel_guess_hand_card_continue_panel_continue_button_Label");

        let guess_hand_card_button = gameMain_panel.getChildByName("guess_hand_card_button");
        let guess_tips = guess_hand_card_button.getChildByName("guess_tips");
        let guess_tips_label = guess_tips.getChildByName("guess_tips_label");
        guess_tips_label.getComponent(cc.Label).string = cv.StringTools.formatC(cv.config.getStringData("UIGuesstips"));
        let tipsWidth = cv.resMgr.getLabelStringSize(guess_tips_label.getComponent(cc.Label)).width;
        guess_tips.setContentSize(cc.size(tipsWidth + 45, guess_tips.getContentSize().height))
    }

    public onNoticeBuyin(data) {
        if (!cv.GameDataManager.tRoomData.hasRecvBuyinNotice) {
            return;
        }

        if (data == null) {
            data = cv.GameDataManager.tRoomData.recNeedBuyNoticeData;
        }

        if (data != null) {
            if (data.usdt_subtract != 0 && data.gold_add != 0) {
                if (!data.is_auto) {
                    cv.TT.showMsg(cv.StringTools.formatC(data.next_hand ? cv.config.getStringData("ErrorToast48") : cv.config.getStringData("ErrorToast49"), cv.StringTools.numToFloatString(data.usdt_subtract), cv.StringTools.numToFloatString(data.gold_add), cv.StringTools.numToFloatString(data.buyin_amount)), cv.Enum.ToastType.ToastTypeSuccess);
                }
                else {
                    if (data.next_hand) {
                        cv.TT.showMsg(cv.StringTools.formatC(cv.config.getStringData("ErrorToast48"), cv.StringTools.numToFloatString(data.usdt_subtract), cv.StringTools.numToFloatString(data.gold_add), cv.StringTools.numToFloatString(data.buyin_amount)), cv.Enum.ToastType.ToastTypeSuccess);
                    }
                }
            }
            else {
                if (!data.is_auto) {
                    cv.TT.showMsg(cv.StringTools.formatC(data.next_hand ? cv.config.getStringData("ErrorToast45") : cv.config.getStringData("ErrorToast46"), cv.StringTools.numToFloatString(data.buyin_amount, true)), cv.Enum.ToastType.ToastTypeSuccess);

                }
                else {
                    if (data.next_hand) {
                        cv.TT.showMsg(cv.StringTools.formatC(cv.config.getStringData("ErrorToast45"), cv.StringTools.numToFloatString(data.buyin_amount, true)), cv.Enum.ToastType.ToastTypeSuccess);

                    }
                }
            }
        }

        let player: PlayerInfo = GameDataManager.tRoomData.GetTablePlayer(cv.dataHandler.getUserData().u32Uid)

        if (player != null) {
            if (data.is_auto) {
                if (data.next_hand) {
                    cv.MessageCenter.send("on_update_self_buyin_stake");
                }
                else {
                    let curGameId = cv.roomManager.getCurrentGameID();
                    let game_mode: number = cv.GameDataManager.tRoomData.pkRoomParam.game_mode;
                    let auto_withdraw: boolean = cv.GameDataManager.tRoomData.pkRoomParam.auto_withdraw;

                    if (curGameId === cv.Enum.GameId.StarSeat
                        || cv.Enum.GameId.Plo
                        || curGameId === cv.Enum.GameId.Bet
                        || (curGameId === cv.Enum.GameId.Texas && game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short && auto_withdraw)) {
                        cv.MessageCenter.send("on_auto_buyin_eff");
                    }
                }
            }
        }
    }

    //国王表情按钮
    public onbtnFaceClick(event) {
        cv.AudioMgr.playButtonSound('button_click');
        if (this.isGameStarSeat()) {
            this.face_Panel.getComponent(FaceBarrage).showUi();
        }
        else {
            this.face_Panel.getComponent(FaceView).showUi();
        }
    }

    public isGameStarSeat(): boolean {
        return cv.GameDataManager.tRoomData.u32GameID == cv.Enum.GameId.StarSeat;
    }

    public OnBuyinNow(num: number) {
        for (let i = 0; i < GameDataManager.tRoomData.kTablePlayerList.length; ++i) {
            let pkPlayer: PlayerInfo = GameDataManager.tRoomData.kTablePlayerList[i];

            if ((pkPlayer.name == cv.dataHandler.getUserData().nick_name) && pkPlayer.playerid != cv.dataHandler.getUserData().u32Uid) {
                cv.TT.showMsg(cv.config.getStringData("SitDownErrorToast1"), cv.Enum.ToastType.ToastTypeError);
                return;
            }
        }

        cv.gameNet.RequestBuyin(GameDataManager.tRoomData.u32RoomId, num, false);
    }

    public AdaptiveExpand() {
        if (cv.config.IS_FULLSCREEN) {
            const menu_layout = cc.find('menu_layout', this.menu_Panel).getComponent(cc.Layout);
            menu_layout.paddingTop = menu_layout.paddingTop + cc.sys.getSafeAreaRect().y;

            const sliderHolder = cc.find("gameMain_panel/slider_holder", this.node);
            const sliderBg = cc.find("gameMain_panel/slider_holder/sliderBg", this.node);
            const freeSlider = sliderBg.getChildByName("freeSlider");
            let sliderSize = sliderHolder.getContentSize();
            let newSize = cc.size(sliderSize.width / cv.config.DESIGN_HEIGHT * cv.config.HEIGHT, sliderSize.height);
            sliderHolder.setContentSize(newSize);
            let len = sliderHolder.childrenCount;
            let list = sliderHolder.children;

            for (let i = 0; i < len; i++) {
                list[i].setPosition(list[i].position.x / sliderSize.width * newSize.width, list[i].position.y);
            }

            let freeSliderSize = freeSlider.getContentSize();
            freeSlider.setContentSize(cc.size(freeSliderSize.width / sliderSize.width * newSize.width, freeSliderSize.height));
        }
    }

    setAvatar() {
        let roleInfoSet = cc.director.getScene().getChildByName("roleInfoSet").getComponent(RoleInfoSet);
        roleInfoSet.node.active = true;
        roleInfoSet.updateView();
        roleInfoSet.openPhoto();
    }

    _showGameEnteredToast()
    {
            const pkRoomParam = cv.GameDataManager.tRoomData.pkRoomParam;
            let minSignNum = 0;
            let game_name = "";

            if(pkRoomParam.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short)
            {
                minSignNum = cv.StringTools.clientGoldByServer(pkRoomParam.rule_ante_amount) * 100;
                game_name =   cv.config.getStringData("DataView_gameType_panel_button_1_text");
            }
            else if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Bet ) // On Entering to Splash Game from NLHE list
            {
                const blindNum = pkRoomParam.rule_blind_enum;
                minSignNum = cv.config.GetUIBuyInScoreValue(blindNum - 1); 
                game_name =   cv.config.getStringData("DataView_gameType_panel_button_4_text");
            }
            
            const minBuyInAmount = (minSignNum * pkRoomParam.rule_buyin_min_enum) / 100;
           
            cv.TT.showToast(cv.StringTools.formatC(cv.config.getStringData("ToastMessage10"),
                cv.StringTools.numToFloatString(pkRoomParam.rule_ante_amount),
                minBuyInAmount.toString(),
                game_name),
                cv.Enum.ToastPosition.MIDDLE);
    }

    public createRuleDiscriptionPanel() {
        if (!this.ruleDiscription) {
            this.ruleDiscription = cc.instantiate(this.ruleDiscription_panel_prefab).getComponent(RuleDiscription);
            // 因为动画表情要高于原先设置的快捷加注，所以设置成了120,造成点开规则界面有部分在上面有部分在下面，现在把规则zindex调高了，都在动画上面
            this.ruleDiscription.node.zIndex = cc.macro.MAX_ZINDEX;
            cc.director.getScene().addChild(this.ruleDiscription.node, cc.macro.MAX_ZINDEX);
        }
        return this.ruleDiscription;
    }

    public releaseRuleDescriptionPanel() {
        if (this.ruleDiscription) {
            this.ruleDiscription.node.destroy();
            this.ruleDiscription = null;
        }
    }

}
