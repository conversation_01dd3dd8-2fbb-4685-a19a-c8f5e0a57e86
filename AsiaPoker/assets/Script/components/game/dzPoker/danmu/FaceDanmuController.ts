import DanmuHeadItem from './DanmuHeadItem';
import { <PERSON><PERSON><PERSON><PERSON>roller } from './DanmuController';
import cv from '../../../lobby/cv';
import { PlayerInfo } from '../data/RoomData';
import Tag from '../../../../common/tools/Tag';
import FaceView from './FaceView';
import game_protocol = require('../../../../../Script/common/pb/gs_protocol');
import game_pb = game_protocol.protocol;

const { ccclass, property } = cc._decorator;

@ccclass
export class FaceDanmuController extends DanmuController {

    // @玩家头像显示节点
    @property(cc.Node) roleHeadBg: cc.Node = null;
    // @玩家名称背景
    @property(cc.Node) roleNameBg: cc.Node = null;
    // @玩家名称
    @property(cc.Node) roleNameText: cc.Node = null;
    // 弹幕选择面板
    @property(cc.Node) danmu_panel: cc.Node = null;
    @property(cc.Node) buttonOnNode: cc.Node = null;
    @property(cc.Label) atTagLabel: cc.Label = null;

    private atTagStartColor:cc.Color = new cc.Color(251, 216, 136)
    private roleHeadItemList: DanmuHeadItem[] = [];

    public active() {
        this.roleHeadBg.active = !this.isJackfruit;
        this.atTagLabel.node.active= !this.isJackfruit;
        if (this.isJackfruit) {
            this.scrollView.node.setContentSize(this.scrollView.node.getContentSize().width, 530 + 74);
        } else {
            this.scrollView.node.setContentSize(this.scrollView.node.getContentSize().width, 488);
        }
        this.showRoleHead();
    }

    public show() {
        this.danmu_panel.active = true;
        this.atTagLabel.node.color = cc.Color.WHITE;
    }

    public hide() {
        this.danmu_panel.active = false;
    }

    /**
     * name
     */
    public onResetCdtime() {
        // 设备cd状态
        this._isInCd = true;
        this._cdTime = 5;
        cv.MessageCenter.send('updateCdStatus', this._isInCd || !cv.tools.isShowBarrage());
        this.schedule(this.updateDanmuCd, 1);
    }

    /**
     * 点击@玩家头像
     *
     */
    public onclickRoleHead(playerid: number, isSelect: boolean) {
        for (let i = 0; i < this.roleHeadItemList.length; i++) {
            if (this.roleHeadItemList[i].getPlayerId() !== playerid) {
                this.roleHeadItemList[i].hideSelectImg();
            } else {
                if (isSelect) {
                    this.atTagLabel.node.color = this.atTagStartColor;
                    const name = '@ ' + this.roleHeadItemList[i].getRoleName();
                    const size = cv.resMgr.getLabelStringSize(this.roleNameText.getComponent(cc.Label), name);
                    this.roleNameText.getComponent(Tag).setTag(playerid);
                    this.roleNameBg.setContentSize(cc.size(size.width + 40, this.roleNameBg.getContentSize().height));
                    // this.scrollView设置setContentSize的时候会改变this.scrollView.content的坐标，这里先记录下原始的坐标
                    if (!this.roleNameBg.active) {
                        const contentY = this.scrollView.content.getPosition().y;
                        this.scrollView.node.setContentSize(
                            cc.size(this.scrollView.node.getContentSize().width, 530 - 100)
                        );
                        this.scrollView.node.setPosition(
                            this.scrollView.node.x,
                            this.scrollView.node.getContentSize().height / 2 +
                                (this.faceView as FaceView).face_button.getContentSize().height
                        );
                        this.scrollView.content.setPosition(this.scrollView.content.getPosition().x, contentY - 50);
                    }
                } else {
                    this.atTagLabel.node.color = cc.Color.WHITE;
                    const contentY = this.scrollView.content.getPosition().y;
                    this.scrollView.node.setContentSize(cc.size(this.scrollView.node.getContentSize().width, 488));
                    this.scrollView.node.setPosition(
                        this.scrollView.node.x,
                        this.scrollView.node.getContentSize().height / 2 +
                            (this.faceView as FaceView).face_button.getContentSize().height
                    );
                    this.scrollView.content.setPosition(this.scrollView.content.getPosition().x, contentY + 50);
                }
                this.roleNameBg.active = isSelect;
            }
        }
    }

    public onselect(playerid: number) {
        for (let i = 0; i < this.roleHeadItemList.length; i++) {
            if (this.roleHeadItemList[i].getPlayerId() !== playerid) {
                this.roleHeadItemList[i].hideSelectImg();
            } else {
                this.roleHeadItemList[i].showSelectImg();
            }
        }
    }

    /**
     * 点击弹幕
     * @param event
     */
    public danmuItemClick(params: any) {
        console.log(params);
        cv.AudioMgr.playEffect('zh_CN/game/dzpoker/audio/danmu');
        if (!cv.tools.isShowBarrage()) {
            cv.TT.showMsg(
                cv.config.getStringData('Faceview_danmu_button_onOff_Tips'),
                cv.Enum.ToastType.ToastTypeWarning
            );
            return;
        }
        if (this._isInCd) {
            const msg = cv.StringTools.formatC(cv.config.getStringData('Faceview_danmu_cd_tips'), this._cdTime);
            cv.TT.showMsg(msg, cv.Enum.ToastType.ToastTypeWarning);
            return;
        }
        // 设备cd状态
        this._isInCd = true;
        // this.updateCdStatus();
        cv.MessageCenter.send('updateCdStatus', this._isInCd || !cv.tools.isShowBarrage());
        this.schedule(this.updateDanmuCd, 1);

        // let msg = cv.config.getStringData("Faceview_danmu_text_" + params);
        // 自已
        const data: game_pb.NoticeSendBarrage = new game_pb.NoticeSendBarrage();
        data.content = cv.String(params);
        data.nickname = cv.dataHandler.getUserData().nick_name;
        data.playerid = cv.Number(cv.dataHandler.getUserData().user_id);
        data.thump_up_status = 1;
        const at_list: string[] = [];
        const at_uid_list: number[] = [];
        if (this.roleNameBg.active) {
            at_list.push(this.roleNameText.getComponent(cc.Label).string);
            at_uid_list.push(this.roleNameText.getComponent(Tag).getTag());
        }
        data.at_list = at_list;
        // cv.GameDataManager.addDanmuMsg(data);
        this.faceView.hideUi();
        // 发送给服务器
        if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
            cv.jackfruitNet.requestSendBarrage(cv.String(params));
        } else {
            cv.gameNet.requestSendBarrage(cv.String(params), at_list, at_uid_list);
        }
    }

    /**
     * 点击弹幕开关
     */
    public clikcOnOff() {
        // cv.AudioMgr.playButtonSound('button_click');
        // this.onOff = !this.onOff;
        cv.MessageCenter.send('danmu_onOff', cv.tools.isShowBarrage());
        if (cv.tools.isShowBarrage()) {
            cv.TT.showMsg(cv.config.getStringData('Faceview_danmu_button_on'), cv.Enum.ToastType.ToastTypeWarning);
        } else {
            cv.TT.showMsg(cv.config.getStringData('Faceview_danmu_button_off'), cv.Enum.ToastType.ToastTypeWarning);
        }
        // this.updateCdStatus();
        cv.MessageCenter.send('updateCdStatus', this._isInCd || !cv.tools.isShowBarrage());
        // cv.tools.SaveStringByCCFile("danmu_onOff", this.onOff ? "1" : "0");
        // cc.find("danmu_ui/danmu_panel/on_off/button_On", this.node).active = this.onOff;
    }

    /**
     * 设置弹幕开关状态
     */
    setOnOff() {
        // let num = cv.tools.GetStringByCCFile("danmu_onOff");
        // this.onOff = !(num == "0");
        this.buttonOnNode.active = cv.tools.isShowBarrage();
        cv.MessageCenter.send('updateCdStatus', this._isInCd || !cv.tools.isShowBarrage());
        // this.updateCdStatus();
    }

    /**
     * 根据是否处于cd中更新ui 或者弹幕功能是否已关闭
     */
    public updateCdStatus() {
        // let len = this.danmuUItexts.length;
        // for (let index = 0; index < len; index++) {
        //     if (this._isInCd || !this.onOff) {
        //         this.danmuUItexts[index].node.color = cc.Color.GRAY;
        //     } else {
        //         this.danmuUItexts[index].node.color = cc.color(229, 211, 141);
        //     }
        // }
    }

    /**
     * 刷新cd时间
     */
    public updateDanmuCd() {
        this._cdTime -= 1;
        if (this._cdTime <= 0) {
            this._cdTime = 5;
            this._isInCd = false;
            // this.updateCdStatus();
            cv.MessageCenter.send('updateCdStatus', this._isInCd || !cv.tools.isShowBarrage());
            this.unschedule(this.updateDanmuCd);
        }
    }

    /**
     * 显示头像
     */
    public showRoleHead() {
        const playerData: PlayerInfo[] = [];
        const len = cv.GameDataManager.tRoomData.kTablePlayerList.length;
        for (let i = 0; i < len; i++) {
            if (cv.GameDataManager.tRoomData.kTablePlayerList[i].playerid !== cv.dataHandler.getUserData().u32Uid) {
                playerData.push(cv.GameDataManager.tRoomData.kTablePlayerList[i]);
            }
        }
        for (let index = 0; index < this.roleHeadItemList.length; index++) {
            if (index < playerData.length) {
                this.roleHeadItemList[index].setData(playerData[index]);
                this.roleHeadItemList[index].node.active = true;
            } else {
                this.roleHeadItemList[index].clearData();
                this.roleHeadItemList[index].node.active = false;
            }
        }

        this.roleNameBg.active = false;
    }

    /**
     * 显示弹幕UI
     */
    public showScrollview() {
        super.showScrollview();
        this.scrollView.scrollToTop(0);
        cv.MessageCenter.send('updateCdStatus', this._isInCd || !cv.tools.isShowBarrage());
    }

    protected onLoad() {
        super.onLoad();
        this.danmu_panel.active = true;
        this.initScrollviewData();
    }

    protected start() {
        this.initRoleHead();
    }

    /**
     * 初始化头像
     */
    private initRoleHead() {
        // 最多显示8个，默认初始化8个，够用.
        const len = 8;
        for (let index = 0; index < len; index++) {
            const roleHead = cc.instantiate(this.roleHeadItemPrefab);
            roleHead.getComponent(DanmuHeadItem).setFaceView(this.faceView);
            roleHead.setPosition(84 + index * (roleHead.getContentSize().width + 25), 0);
            this.roleHeadBg.addChild(roleHead);
            this.roleHeadItemList.push(roleHead.getComponent(DanmuHeadItem));
        }
    }
}
