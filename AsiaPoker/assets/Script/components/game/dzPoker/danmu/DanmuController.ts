import FaceView from './FaceView';
import FaceBarrage from './FaceBarrage';
import cv from '../../../lobby/cv';
import { BarrageCountData } from '../data/GameDataManager';
import DanmuView from './DanmuView';
import BarrageHistoryView from './BarrageHistoryView';
import DanmuItem from './DanmuItem';

const { ccclass, property } = cc._decorator;

@ccclass
export abstract class DanmuController extends cc.Component {
    // 弹幕滚动层界面
    @property(cc.ScrollView) scrollView: cc.ScrollView = null;
     // 弹幕item
     @property(cc.Prefab) danmuItem: cc.Prefab = null;
    // 头像预制体
    @property(cc.Prefab) roleHeadItemPrefab: cc.Prefab = null;
    // 飘弹幕的界面
    @property(cc.Prefab) danmu_Panel_prefab: cc.Prefab = null;
    public faceView: FaceView | FaceBarrage = null;
    // 发送弹幕的冷却时间
    protected _cdTime: number = 5;
    protected _isInCd: boolean = false;
    protected _danmu_view: cc.Node = null;

    // 菠萝蜜弹幕数量
    protected JACKFRUIT_DANMU_COUNT = 22;
    protected DZPOKER_DANMU_COUNT = 24;
    protected DZPOKER_SQUID_DANMU_COUNT = 24;

    @property(BarrageHistoryView)
    protected historyView: BarrageHistoryView;

    protected get isJackfruit(): boolean {
        return cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit;
    }
    
    private barrageNodeItems: DanmuItem[] = [];

    public abstract danmuItemClick(params: any);

    public abstract clikcOnOff();

    public abstract onResetCdtime();

    public abstract active();

    public abstract show();

    public abstract hide();

    /**
     * 初始化数据
     */
    public initScrollviewData() {
        const isJackfruit = this.isJackfruit;
        const len = isJackfruit ? this.JACKFRUIT_DANMU_COUNT : this._isSquidGame() ? this.DZPOKER_SQUID_DANMU_COUNT : this.DZPOKER_DANMU_COUNT;
        for (let index = 0; index < len; index++) {
            if (isJackfruit) {
                const data: BarrageCountData = new BarrageCountData();
                data.content = cv.config.getStringData(cv.StringTools.formatC('jackfruit_danmu_label%d', index));
                data.BarrageId = index;
                data.count = 0;
                data.lastSendTime = 0;
                cv.GameDataManager.addBarrageData(data);
            } else {
                const data: BarrageCountData = new BarrageCountData();
                const contentKey = this._isSquidGame() ? 'Squid_danmu_text_%d' : 'Faceview_danmu_text_%d';
                data.content = cv.config.getStringData(cv.StringTools.formatC(contentKey, index));
                data.BarrageId = index;
                data.count = 0;
                data.lastSendTime = 0;
                cv.GameDataManager.addBarrageData(data);
            }
        }
    }

    public showScrollview()
    {
        const barrageData = cv.GameDataManager.getBarrageData();
        const existingItemsCount = this.barrageNodeItems.length;
        const barrageLength = barrageData.length;

        for (let i = 0; i < barrageLength; i++) {
            if (i < existingItemsCount) {
                if (barrageData[i].BarrageId != this.barrageNodeItems[i]._BarrageId) this.barrageNodeItems[i].updateItemData(barrageData[i]);
            } else {
                const newItem = cc.instantiate(this.danmuItem);
                const itemComponent = newItem.getComponent(DanmuItem);
                this.scrollView.content.addChild(newItem); // Add to container
                itemComponent.updateItemData(barrageData[i]);
                this.barrageNodeItems.push(itemComponent);
            }
        }
        // Update layout  once
        this.scrollView.content.getComponent(cc.Layout).updateLayout(); 
    }

    /**
     * 设置弹幕的父节点()
     * @param node
     */
    public setParentNode(node: cc.Node) {
        if (node) {
            this._danmu_view = cc.instantiate(this.danmu_Panel_prefab);
            this._danmu_view.getComponent(DanmuView).setParentNode(node);
        }
    }

    public setDanmuChanel(pos: any) {
        if (this._danmu_view) {
            this._danmu_view.getComponent(DanmuView).setDanmuChanel(pos);
        }
    }

    public adjustDanmuMaxNumber(max: number) {
        if (this._danmu_view) {
            this._danmu_view.getComponent(DanmuView).adjustDanmuMaxNumber(max);
        }
    }

    protected onLoad() {
        cv.MessageCenter.register('danmuItemClick', this.danmuItemClick.bind(this), this.node);
        cv.MessageCenter.register('onClickDanmuSwitch', this.clikcOnOff.bind(this), this.node);
        cv.MessageCenter.register('resetCdTime', this.onResetCdtime.bind(this), this.node);
        
    }

    protected onDestroy() {
        cv.MessageCenter.unregister('danmuItemClick', this.node);
        cv.MessageCenter.unregister('onClickDanmuSwitch', this.node);
        cv.MessageCenter.unregister('resetCdTime', this.node);
    }

    protected onEnable(): void {
        cv.MessageCenter.register("on_table_barrage_msg_recieved", this.onTableBarrageMsgReceived.bind(this), this.node);
    }

    protected onDisable(): void {
        cv.MessageCenter.unregister("on_table_barrage_msg_recieved", this.node);
    }

    public onTableBarrageMsgReceived(msg: any)
    {
        this.historyView.onTableBarrageResponse(msg);
    }

    protected _isSquidGame() {
        return cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Squid;
    }
}
