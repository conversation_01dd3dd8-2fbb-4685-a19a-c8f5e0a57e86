import FaceView from './FaceView';
import { PlayerInfo } from '../data/RoomData';
import FaceBarrage from './FaceBarrage';
import { CircleSprite } from '../../../../common/tools/CircleSprite';
import cv from '../../../lobby/cv';

const { ccclass, property } = cc._decorator;

@ccclass
export class FaceHeadItem extends cc.Component {
    @property(cc.Node) select_img: cc.Node = null;
    @property(cc.Node) roleHead: cc.Node = null;
    protected _playerData: PlayerInfo = null;
    protected _faceView: FaceView | FaceBarrage = null;

    /**
     * setFaceView
     */
    public setFaceView(faceView: FaceView | FaceBarrage) {
        this._faceView = faceView;
    }

    public onClick() {
        cv.AudioMgr.playButtonSound('button_click');
    }

    /**
     * getPlayerId
     */
    public getPlayerId(): number {
        if (this._playerData) {
            return this._playerData.playerid;
        }
        return 0;
    }

    /**
     * 设置数据
     */
    public setData(data: PlayerInfo) {
        this.clearData();
        this._playerData = data;
    }

    /**
     * clearData
     */
    public clearData() {
        this._playerData = null;
        this.hideSelectImg();
        CircleSprite.cleanHeadNode(this.roleHead);
    }

    /**
     * getRoleName
     */
    public getRoleName(): string {
        return this._playerData.name;
    }

    /**
     * 隐藏选择图片
     */
    public hideSelectImg() {
        this.select_img.active = false;
    }

    /**
     * 显示
     */
    public showSelectImg() {
        this.select_img.active = true;
    }
}
