import BarrageHistoryItem from "./BarrageHistoryItem";
import game_protocol = require("../../../../../Script/common/pb/gs_protocol");
import jackfruit = require("../../../../../Script/common/pb/jackfruit");
import game_pb = game_protocol.protocol;
import jackfruit_proto = jackfruit.jackfruit_proto;
import cv from "../../../../components/lobby/cv";

const {ccclass, property} = cc._decorator;

@ccclass
export default class BarrageHistoryView extends cc.Component {

    @property(cc.Node)
    protected rootPanel: cc.Node = null;

    @property(cc.Node)
    protected container: cc.Node = null;

    @property(cc.Prefab)
    protected historyItemPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    protected tableBarrageItemPrefab: cc.Prefab = null;

    @property(cc.ScrollView)
    protected sv: cc.ScrollView = null;

    @property(cc.Widget)
    protected svAdjest: cc.Widget = null;

    protected currentData: game_pb.Barrage[] | jackfruit_proto.Barrage[] = [];
    protected latestMsgTimestamp = 0;
    // protected currentMsgsDisplayedCount = 0;
    private _lastOffset: cc.Vec2= cc.Vec2.ZERO;
    private _newEntries: game_pb.Barrage[] | jackfruit_proto.Barrage[] = [];
    private isWelcomeMessageAdded: boolean = false;
  
    
    protected onLoad(): void {
        this.isWelcomeMessageAdded = false;
        cv.MessageCenter.register('Reset_Barrage_History_Data', this.resetHistoryView.bind(this), this.node);
    }

    protected onDestroy(): void {
        cv.MessageCenter.unregister('Reset_Barrage_History_Data', this.node);
    }

    protected onEnable(): void {
        this.sv.content.opacity = 0;
        this.sv.node.on('scrolling', this.onSVEventScrolling, this);
        this.resetScrollView();
         this.scheduleOnce(() => {
            this.sv.setContentPosition(this._lastOffset);
            this.sv.content.opacity = 255;
            cv.gameNet.requestBarrageHistory();
         },1/30);
        
    }

    protected onDisable(): void {
        this.sv.node.off('scrolling', this.onSVEventScrolling, this);
        this._lastOffset = this.sv.getContentPosition();
        this.sv.content.opacity = 0;
    }

    public resetHistoryView()
    {
        this.sv.content.removeAllChildren();
        this.currentData = [];
        this._newEntries = [];
        this.isWelcomeMessageAdded = false;
        this.latestMsgTimestamp = 0;
    }

    public onTableBarrageResponse(data: any) {
        if(data.tableBarrageMessage && data.barrages.length <= 100 && !this.isWelcomeMessageAdded && this.sv.content.children.length === 0)
        {
            const contentToDisplay = this.getTableBarrageContentInCurrLang(data.tableBarrageMessage.content);
            if(typeof contentToDisplay !== 'undefined' && contentToDisplay )
            {
                this.sv.content.opacity = 0;
                const contentLayout = this.sv.content.getComponent(cc.Layout);
                contentLayout.enabled = false;
                const newItem = cc.instantiate(this.tableBarrageItemPrefab);
                const itemComponent = newItem.getComponent(BarrageHistoryItem);
                itemComponent.updateItemAsTableMessage(data, contentToDisplay);
                this.sv.content.addChild(newItem); // Add to container
                newItem.setSiblingIndex(0);
                contentLayout.enabled = true;
                this.isWelcomeMessageAdded = true;
                this.sv.scrollTo(new cc.Vec2(0, 0));
                this._lastOffset = this.sv.getContentPosition();
                
                this.sv.content.opacity = 255;
                if(data.barrages.length>0)
                    this.onBarrageHistoryResponse(data);
            }
        } else{
            this.onBarrageHistoryResponse(data);
        }
        this.resetScrollView();
    }

    private onBarrageHistoryResponse(data: any) {
        if (data.barrages.length === 0 || !cc.isValid(this.node)) {
            return;
        }
        if(this.sv.content.children.length >= 100 && this.isWelcomeMessageAdded)
        {
            this.sv.content.children[this.sv.content.children.length - 1].destroy();
        }
        const newEntries = data.barrages.sort((a, b) => b.send_time - a.send_time).filter(entry => entry.send_time > this.latestMsgTimestamp);
        if (newEntries && newEntries.length > 0) {
            this._newEntries = newEntries;
            if(Math.abs(Math.floor(this._lastOffset.y)) < 10 || this.sv.content.children.length === 0) {
                this.checkForNewMsgs();
            }
        }
    }
   
    onSVEventScrolling(arg: cc.ScrollView): void {
        this.checkForNewMsgs();
    }

    addMsg() {
        const contentLayout = this.sv.content.getComponent(cc.Layout);
        contentLayout.enabled = false;
        for (let i = this._newEntries.length - 1; i >= 0; i--) {
            const newItem = cc.instantiate(this.historyItemPrefab);
            const itemComponent = newItem.getComponent(BarrageHistoryItem);
            itemComponent.updateItemData(this._newEntries[i]);
            this.sv.content.addChild(newItem); // Add to container
            newItem.setSiblingIndex(0);
        }
        contentLayout.enabled = true;
        this.currentData.unshift(...this._newEntries);
        this.latestMsgTimestamp = this.currentData[0].send_time;
        contentLayout.enabled = true;
        this._newEntries.length = 0;
    }


    private checkForNewMsgs(): void {
        if(this.rootPanel.active && !this.sv.isAutoScrolling() && Math.abs(Math.floor(this._lastOffset.y)) < 10 && this._newEntries.length> 0){
            this.addMsg();
            this.sv.scrollTo(new cc.Vec2(0, 0));
            this._lastOffset = this.sv.getContentPosition();
        }
        if(!this.sv.isAutoScrolling()){
            this._lastOffset = this.sv.getContentPosition();
        }
    }

    private resetScrollView(){
        if(this.sv.content.getContentSize().height>this.sv.node.height && this.svAdjest.getComponent(cc.Layout).enabled){
            this.svAdjest.getComponent(cc.Layout).enabled=false;
            this.svAdjest.enabled=true;
            this.svAdjest.node.height=this.sv.node.height;
            this.svAdjest.top=0;
            this.svAdjest.bottom=0;
            this.svAdjest.left=0;
            this.svAdjest.right=0;
        }
        this.sv.setContentPosition(this._lastOffset);
    }

    public getTableBarrageContentInCurrLang(contentData: game_pb.LocalizedContent | jackfruit_proto.LocalizedContent): string
    {
        let result = "";
        switch(cv.config.getCurrentLanguage())
        {
            case cv.Enum.LANGUAGE_TYPE.en_US:
                result = contentData.en_US;
                break;

            case cv.Enum.LANGUAGE_TYPE.ar_SA:
                result = contentData.ar_SA;
                break;

            case cv.Enum.LANGUAGE_TYPE.hi_IN:
                result = contentData.hi_IN;
                break;

            case cv.Enum.LANGUAGE_TYPE.th_PH:
                result = contentData.th_PH;
                break;

            case cv.Enum.LANGUAGE_TYPE.yn_TH:
                result = contentData.yn_TH;
                break;

            case cv.Enum.LANGUAGE_TYPE.zh_CN:
                result = contentData.zh_CN;
                break;
        }

        return result;
    }
}
