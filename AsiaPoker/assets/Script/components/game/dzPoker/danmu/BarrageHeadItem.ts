import { FaceHeadItem } from './FaceHeadItem';
import { PlayerInfo } from '../data/RoomData';
import FaceBarrage from './FaceBarrage';
import { CircleSprite } from '../../../../common/tools/CircleSprite';

const { ccclass, property } = cc._decorator;

@ccclass
export default class BarrageHeadItem extends FaceHeadItem {
    @property(cc.Label) roleName: cc.Label = null;
    @property(cc.Node) starBorder: cc.Node = null;

    public onClick() {
        super.onClick();
        const faceView = this._faceView as FaceBarrage;
        if (faceView.danmuController.currentCdStatus() || this._playerData == null) {
            return;
        }
        this.select_img.active = !this.select_img.active;
        faceView.danmuController.onclickRoleHead(this._playerData.playerid, this.select_img.active);
    }

    /**
     * 设置数据
     */
    public setData(data: PlayerInfo) {
        super.setData(data);
        CircleSprite.setCircleSprite(this.roleHead, data.headurl, data.plat, true);
        this.roleName.string = this._playerData.name;
        if (this._playerData.identity === 1) {
            this.roleName.node.color = cc.color(208, 171, 110);
            this.starBorder.active = true;
        } else {
            this.roleName.node.color = cc.color(255, 255, 255);
            this.starBorder.active = false;
        }
    }
}
