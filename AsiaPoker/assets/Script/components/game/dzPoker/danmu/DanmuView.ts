import game_protocol = require('../../../../../Script/common/pb/gs_protocol');
import jackfruit_proto = require('../../../../../Script/common/pb/jackfruit');
import game_pb = game_protocol.protocol;
import jackfruit = jackfruit_proto.jackfruit_proto;

import cv from '../../../lobby/cv';
import Tag from '../../../../common/tools/Tag';
import { GiftData } from '../gift/GiftData';
import BulltScreenBg from './BulltScreenBg';


/**
 * 弹幕层
 */
const { ccclass, property } = cc._decorator;
@ccclass
export default class DanmuView extends cc.Component {
    @property(cc.SpriteFrame) frame_bulltScreenBg_normal: cc.SpriteFrame = null;

    // 用做飘弹幕的节点
    @property([BulltScreenBg]) danmuNodes: BulltScreenBg[] = [];
    // 弹幕运行轨道
    private danmuChanel: any[] = [
        { pos: 318, idle: true },
        { pos: 178, idle: true },
        { pos: -262, idle: true }
    ];

    // 弹幕飘过的时间
    private _actionTime: number = 4;
    //
    private _maxActiveNumber: number = 5;

    private _isallin: boolean = false;
    private _danmuCaches: game_pb.NoticeSendBarrage[] = [];

    // 管理员封禁弹幕(该功能取消了)
    // private _disBarrageBox: cc.Node = null;
    // private _disBarrageTouch: cc.Node = null;
    // private _disBarragePlayerId: number = 0;
    // private _disBarrageUpdate: boolean = true;
    onLoad() {
        for (let index = 0; index < this.danmuNodes.length; index++) {
            this.danmuNodes[index].node.active = false;
        }

        cv.MessageCenter.register('forbid_chat', this.onIsAllin.bind(this), this.node);
        cv.MessageCenter.register('danmu_onOff', this.onDanmu_onOff.bind(this), this.node);
        this.schedule(this.updateDanmuMsg, this._actionTime / this._maxActiveNumber);
        this.node.opacity = cv.tools.isShowBarrage() ? 255 : 0;

        // this._disBarrageBox = cc.find("disBarrageBox", this.node);
        // this._disBarrageTouch = cc.find("disBarrageTouch", this.node);
        // this.hideDisableBarrageBox();
        // if (this.isGameStarSeat()) {
        //     this._disBarrageTouch.on(cc.Node.EventType.TOUCH_END, function(event) {
        //         this.hideDisableBarrageBox();
        //     }.bind(this));
        //     this._disBarrageTouch._touchListener.setSwallowTouches(false); //touch事件穿透
        // }
    }

    start() {
        cc.game.on(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
        cc.game.on(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
    }

    onDestroy() {
        this.unschedule(this.updateDanmuMsg);
        cv.MessageCenter.unregister('danmu_onOff', this.node);
        cv.MessageCenter.unregister('forbid_chat', this.node);
        cc.game.off(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
        cc.game.off(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
        cv.GameDataManager.clearDanmuMsg();
    }

    /**
     * name
     */
    public onIsAllin(param) {
        this._isallin = param;
        if (!this._isallin && this._danmuCaches.length > 0) {
            for (let index = 0; index < this._danmuCaches.length; index++) {
                const data = this._danmuCaches[index];
                // cv.GameDataManager.addDanmuMsg(data);
                // 发送给服务器
                cv.gameNet.requestSendBarrage(
                    data.content,
                    data.at_list,
                    data.at_uid_list,
                    data.thump_up_status,
                    data.ctype
                );
            }
            this._danmuCaches = [];
        }
    }

    public OnAppEnterBackground() {
        // cv.config.logTime("OnAppEnterBackground");
        // console.log("DanmuView  OnAppEnterBackground");
        cv.config.resetSignTime();
        cv.GameDataManager.clearDanmuMsg();
    }

    public OnAppEnterForeground() {
        // cv.config.logTime("OnAppEnterForeground");
        cv.config.signCurtime();
        // console.log("DanmuView  OnAppEnterForeground");
        cv.GameDataManager.clearDanmuMsg();
    }

    /**
     * 弹幕的关闭与开启
     * @param isOn
     */
    public onDanmu_onOff(isOn: boolean) {
        this.node.opacity = isOn ? 255 : 0;
    }

    // 刷新显示弹幕
    public updateDanmuMsg(f32Delta) {
        const isJackfruit = (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit);
        if (this.currentDanmuActiveNumber() >= this._maxActiveNumber) {
            return;
        }
        const channel: any = this.getIdleChannel();
        if (channel == null) return;
        const barrageData: game_pb.NoticeSendBarrage | jackfruit.NoticeSendBarrage = cv.GameDataManager.getDanmuMsg();
        if (!barrageData) {
            return;
        }
        const danmuNode = this.getDanmuNode();
        const richtext = danmuNode.richtext;
        // AT-3746:In specific languages, richtext will be garbled when cacheMode set to CHAR
        if(cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.hi_IN){
            // richtext.cacheMode = cc.Label.CacheMode.NONE;
            // AT-7058: The language of quick prompts is now English, thus avoiding the condition of AT-3746
            richtext.cacheMode = cc.Label.CacheMode.CHAR;
        }else{
            richtext.cacheMode = cc.Label.CacheMode.CHAR;
        }
        let msg = '';
        let str = '';
        let content = '';

        // 使用弹幕前复原默认的精灵, 且清空富文本图集(因为礼物弹幕会动态修改弹幕的背景图, 也动态添加了富文本图集)
        const bulltScreenBg_normal: cc.Sprite = danmuNode.bulltScreenBgNormal.getComponent(cc.Sprite);
        bulltScreenBg_normal.spriteFrame = this.frame_bulltScreenBg_normal;
        richtext.getComponent(cc.RichText).imageAtlas = null;

        // 资料卡点赞 菠萝蜜和德州公用3
        if (barrageData.ctype === (game_pb.BarrageType.Enum_Liked | jackfruit.BarrageType.Enum_Liked)) {
            content = cv.StringTools.formatC(
                cv.config.getStringData('Star_danmu_like'),
                barrageData.liked_nickname,
                barrageData.nickname
            );
        }
        // 礼物弹幕
        else if (barrageData.ctype === game_pb.BarrageType.Enum_Tip && !isJackfruit) {
            const giftID: number = barrageData.userTipInfo.tip.tipId;
            const giftCount: number = barrageData.userTipInfo.tip.tipCount;
            const strGiftName: string = cv.config.getStringData(`Gift_category_${giftID}`) + ` x ${giftCount}`;
            const strGiftImg: string = ` <img src="gift_icon_${giftID}"/>`;
            const strSent: string = cv.config.getStringData('Gift_sent');
            const strSender: string = barrageData.userTipInfo.player.nickname;
            const strRecipient: string = `<img src="img_icon_star"/> <color=#FFCD7E>${barrageData.userTipInfo.toPlayer.nickname}</color>`;

            const fileName: string = giftID < 1000 ? 'img_danmu_bg_1' : 'img_danmu_bg_2';
            const atlasPath: string = GiftData.GIFT_PLIST_PATH;
            richtext.getComponent(cc.RichText).imageAtlas = cv.resMgr.getSpriteAtlas(atlasPath);
            bulltScreenBg_normal.spriteFrame = cv.resMgr.getSpriteAtlasFrame(atlasPath, fileName);
            content = `${strSender} ${strSent} ${strRecipient} ${strGiftName} ${strGiftImg}`;
        } else if (barrageData.ctype === game_pb.BarrageType.Enum_PlayerEnters) {
            content = barrageData.playerEntersBarrage != null ? barrageData.playerEntersBarrage.nickname : content;
            content = cv.StringTools.formatC(cv.config.getStringData('Danmuview_PlayerEnters_Tips'), content);
        } else if (barrageData.ctype === (game_pb.BarrageType.Enum_Custom | jackfruit.BarrageType.Enum_Custom)) {
            content = barrageData.content;
        }
        else if(barrageData.ctype === (game_pb.BarrageType.Enum_Profile_Liked | jackfruit.BarrageType.Enum_Profile_Liked)){
            const strKey = "Profile_Like_Danmu_";
            content = cv.config.getStringData(strKey + barrageData.content);
        } 
        else {
            if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
                str = 'jackfruit_danmu_label';
            } else {
                str = (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Squid)? 'Squid_danmu_text_' :'Faceview_danmu_text_';
            }
            if (cv.StringTools.isNumber(barrageData.content)) {
                content = cv.config.getStringData(str + barrageData.content);
            } else {
                content = cv.tools.getContentStr(barrageData.content);
                if (content.length === 0) {
                    // not sure why the return but we just gonna map the content if is not empty
                    // return;
                    console.warn(`danmu mapping ${barrageData.content}`);
                    content = barrageData.content;
                }
            }
        }

        // danmuNode.attr({playerId: barrageData.playerid});

        if (
            cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit ||
            (cv.GameDataManager.tRoomData.i32SelfSeat === -1 && !this.isGameStarSeat()) ||
            barrageData.ctype === game_pb.BarrageType.Enum_PlayerEnters
        ) {
            danmuNode.button.active = false;
        } else {
            danmuNode.button.active = true;
            if (
                (barrageData.thump_up_status === 1 || barrageData.thump_up_status === 3) &&
                barrageData.playerid !== cv.dataHandler.getUserData().u32Uid
            ) {
                danmuNode.tag.setData(barrageData);
                danmuNode.buttonThumbUpNormal.active = true;
                danmuNode.buttonThumbUpPressed.active = false;
            } else if (
                barrageData.thump_up_status === 2 &&
                barrageData.playerid !== cv.dataHandler.getUserData().u32Uid
            ) {
                danmuNode.buttonThumbUpNormal.active = false;
                danmuNode.buttonThumbUpPressed.active = true;
            } else {
                danmuNode.button.active = false;
            }
        }

        let atName = '';
        if (
            barrageData.ctype === game_pb.BarrageType.Enum_Liked ||
            barrageData.ctype === game_pb.BarrageType.Enum_Tip
        ) {
            atName = '';
            msg = content;
        } else if (barrageData.ctype === game_pb.BarrageType.Enum_PlayerEnters) {
            atName = '';
            msg = cv.StringTools.formatC('<b><color=#FFDF5D>%s</color></b>', content);
        }else if(barrageData.ctype === game_pb.BarrageType.Enum_Profile_Liked){
            msg = cv.StringTools.formatC(
                '<b><color=#D7C173>%s</color><color=#ffffff>%s</color><b>',
                '@' + barrageData.liked_nickname + ' ',
                content
            );
        }else {
            atName = (barrageData.at_list && barrageData.at_list.length) > 0 ? barrageData.at_list[0] : '';
            atName = atName.replace(/@\s+/g, '@'); // replace space after @
            msg = cv.StringTools.formatC(
                '<b><color=#D7C173>%s</color><color=#ffffff>%s</color><b>',
                atName?atName + ' ': '',
                content
            );
        }

        if (barrageData.ctype === game_pb.BarrageType.Enum_PlayerEnters) {
            danmuNode.bulltScreenBgNormal.active = true;
            danmuNode.bulltScreenBgAtself.active = false;
            // danmuNode.node.opacity = 255 * 0.85;
        } else {
            if (barrageData.playerid === cv.dataHandler.getUserData().u32Uid && 
                barrageData.ctype !== (game_pb.BarrageType.Enum_Profile_Liked | jackfruit.BarrageType.Enum_Profile_Liked)) {
                // 自己发的
                atName = atName.replace(/@\s+/g, '@'); // replace space after @
                msg = cv.StringTools.formatC(
                    '<b><color=#D7C173>%s</color><color=#ffffff>%s</color></b>',
                    atName?atName + ' ': '',
                    content
                );
            }
            danmuNode.bulltScreenBgNormal.active = true;
            danmuNode.bulltScreenBgAtself.active = false;

        }

        if (barrageData.ctype === game_pb.BarrageType.Enum_Tip) {
            danmuNode.setPlayerAvatar(barrageData.userTipInfo?.player.avatar, barrageData.userTipInfo?.player.plat);
            danmuNode.lbNickName.string = barrageData.userTipInfo?.player.nickname;
        }
        else {
            danmuNode.setPlayerAvatar(barrageData.avatar, barrageData.plat);
            danmuNode.lbNickName.string = barrageData.nickname;
        }
        richtext.getComponent(cc.RichText).string = msg;
        // 使用轨道
        this.useChannel(channel);
        const func = cc.callFunc(
            (target, data) => {
                // 回收轨道
                this.freeChannel(data);
            },
            this,
            channel
        );
        danmuNode.node.active = true;
        const txtWidth = richtext.node.getContentSize().width + 115 + 50;
        const nickNameWidth = danmuNode.lbNickName.node.getContentSize().width + 115 + 50;
        const contentWidth = Math.max(txtWidth, nickNameWidth);
        danmuNode.node.setContentSize(cc.size(contentWidth, danmuNode.node.getContentSize().height));
        danmuNode.node.setPosition(cv.config.WIDTH / 2 + txtWidth / 2 + 10, channel.pos);
        const time = (danmuNode.node.width + 10) / (cv.config.WIDTH / this._actionTime);
        danmuNode.node.runAction(
            cc.spawn(
                cc.sequence(
                    cc.moveTo(this._actionTime + time, cc.v2(-cv.config.WIDTH / 2 - txtWidth / 2 - 100, channel.pos)),
                    cc.callFunc(
                        (target, node) => {
                            target.active = false;
                        },
                        this,
                        danmuNode
                    )
                ),
                cc.sequence(cc.delayTime(time), func)
            )
        );
        // 立刻刷新widget
        cv.resMgr.adaptWidget(danmuNode.node, true);
    }

    /**
     * 是否是at自己
     */
    public isAtSelf(atName: string): boolean {
        const atIndex = atName.indexOf('@');
        const name = atName.slice(atIndex + 1, atName.length);
        return cv.StringTools.earseSpace(name) === cv.dataHandler.getUserData().nick_name;
    }

    /**
     * 获取可用的弹幕节点(数量不够自动生成)
     */
    public getDanmuNode(): BulltScreenBg {
        for (let i = 0; i < this.danmuNodes.length; i++) {
            if (!this.danmuNodes[i].node.active) {
                return this.danmuNodes[i];
            }
        }

        const newNodes = cc.instantiate(this.danmuNodes[0].node);
        this.danmuNodes[0].node.getParent().addChild(newNodes);
        newNodes.stopAllActions();
        newNodes.active = false;
        const bulltScreenBg = newNodes.getComponent(BulltScreenBg);
        this.danmuNodes.push(bulltScreenBg);
        return bulltScreenBg;
    }

    /**
     * 重设弹幕的运行轨道
     * 默认值[318, 178, -262]
     */
    public setDanmuChanel(pos: number[] | number): void {
        if (!pos) {
            return;
        }
        const posArr: number[] = typeof pos === 'number' ? [pos] : pos;
        if (posArr.length === 0) {
            return;
        }
        const tmpArr: any[] = this.danmuChanel;
        this.danmuChanel = [];
        for (let i = 0; i < posArr.length; ++i) {
            let obj = null;
            for (let n = 0; n < tmpArr.length; ++n) {
                if (posArr[i] === tmpArr[n].pos) {
                    obj = tmpArr[n];
                    break;
                }
            }
            if (!obj) {
                obj = { pos: posArr[i], idle: true };
            }
            this.danmuChanel.push(obj);
        }
    }

    /**
     * 设置父节点
     * @param node
     */
    public setParentNode(node: cc.Node) {
        if (node) {
            node.addChild(this.node);
        }
    }

    /**
     * 调整最大(显示)条数
     */
    public adjustDanmuMaxNumber(max: number): void {
        if (max) {
            this.unschedule(this.updateDanmuMsg);
            this._maxActiveNumber = max;
            this.schedule(this.updateDanmuMsg, this._actionTime / this._maxActiveNumber);
        }
    }

    /**
     * 点赞
     */
    public onClickThumbUp(evt: cc.Event.EventTouch) {
        if (this.isGameStarSeat()) {
            this.clickThumbUpStar(evt);
        } else {
            this.clickThumbUpNormal(evt);
        }
    }

    private getIdleChannel(): any {
        const tmpArr: any[] = [];
        for (let i = 0; i < this.danmuChanel.length; ++i) {
            if (this.danmuChanel[i].idle) {
                tmpArr.push(this.danmuChanel[i]);
            }
        }
        if (tmpArr.length === 0) {
            return null;
        }
        const rand: number = Math.floor(Math.random() * tmpArr.length);
        return tmpArr[rand];
    }

    private freeChannel(channel: any): void {
        if (channel) {
            channel.idle = true;
        }
    }

    private useChannel(channel: any): void {
        if (channel) {
            channel.idle = false;
        }
    }

    private currentDanmuActiveNumber(): number {
        let cnt: number = 0;
        for (let i = 0; i < this.danmuNodes.length; i++) {
            if (this.danmuNodes[i].node.active) {
                cnt++;
            }
        }
        return cnt;
    }

    private clickThumbUpNormal(evt: cc.Event.EventTouch): void {
        // 普通桌,旧逻辑不变
        // 旁观不能点击
        if (
            cv.GameDataManager.tRoomData.i32SelfSeat === -1 ||
            cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit
        )
            return;
        const target: cc.Node = evt.currentTarget;
        const souredata: game_pb.NoticeSendBarrage = target.getComponent(Tag).getData();
        if (souredata.thump_up_status === 2) {
            return;
        }
        target.getChildByName('thumbUp_normal').active = false;
        target.getChildByName('thumbUp_pressed').active = true;
        souredata.thump_up_status = 2;
        target.getComponent(Tag).setData(souredata);
        // 复制
        const data: game_pb.NoticeSendBarrage = new game_pb.NoticeSendBarrage();
        data.content = souredata.content;
        data.nickname = cv.dataHandler.getUserData().nick_name;
        data.playerid = cv.Number(cv.dataHandler.getUserData().user_id);
        data.thump_up_status = 3; // 点赞状态: 1，未点赞 2，已点赞 3，点赞后复制的
        data.ctype = souredata.ctype;
        data.at_list = souredata.at_list;
        cv.MessageCenter.send('resetCdTime');
        if (this._isallin) {
            this._danmuCaches.push(data);
        } else {
            // cv.GameDataManager.addDanmuMsg(data);
            // 发送给服务器
            cv.gameNet.requestSendBarrage(
                data.content,
                data.at_list,
                data.at_uid_list,
                data.thump_up_status,
                data.ctype
            );
        }
    }

    private clickThumbUpStar(evt: cc.Event.EventTouch): void {
        // 明星桌
        // 被禁言时不能点赞复制
        if (
            cv.GameDataManager.tRoomData.muteCustomBarrageSeconds === -1 ||
            cv.GameDataManager.tRoomData.muteCustomBarrageSeconds > 0
        ) {
            return;
        }
        const target: cc.Node = evt.currentTarget;
        const souredata: game_pb.NoticeSendBarrage = target.getComponent(Tag).getData();
        if (souredata.thump_up_status === 2) {
            return;
        }
        target.getChildByName('thumbUp_normal').active = false;
        target.getChildByName('thumbUp_pressed').active = true;
        souredata.thump_up_status = 2;
        target.getComponent(Tag).setData(souredata);
        // 复制
        const data: game_pb.NoticeSendBarrage = new game_pb.NoticeSendBarrage();
        data.content = souredata.content;
        data.nickname = cv.dataHandler.getUserData().nick_name;
        data.playerid = cv.Number(cv.dataHandler.getUserData().user_id);
        data.thump_up_status = 3;
        data.ctype = souredata.ctype;
        data.at_list = souredata.at_list; // >>>???
        cv.MessageCenter.send('resetCdTime');
        if (this._isallin) {
            this._danmuCaches.push(data);
        } else {
            // cv.GameDataManager.addDanmuMsg(data);
            // 发送给服务器
            cv.gameNet.requestSendBarrage(
                data.content,
                data.at_list,
                data.at_uid_list,
                data.thump_up_status,
                data.ctype
            );
        }
    }

    /**
     * 封禁弹幕玩家
     */
    // public onClickDisableBarrage(evt: cc.Event): void {
    //     if (!this.isGameStarSeat() || cv.GameDataManager.tRoomData.auth != 1 || cv.GameDataManager.tRoomData.forbidden.length == 0) {
    //         return;
    //     }
    //     let playerId = evt.currentTarget['playerId'];
    //     if (typeof playerId == 'number' && playerId != cv.dataHandler.getUserData().u32Uid) {
    //         this.showDisableBarrageBox(playerId);
    //     }
    // }
    // public onDisableBarrage(evt: cc.Event): void {
    //     let target: cc.Node = evt.currentTarget;
    //     if (typeof target['time'] == 'number' && this._disBarragePlayerId != 0) {
    //         cv.gameNet.requestSendBarrageForbidden(this._disBarragePlayerId, target['time'], 0);
    //     }
    //     this._disBarragePlayerId = 0;
    //     this.hideDisableBarrageBox();
    // }
    // private getTimeString(time: number): string {
    //     if (time == -1) {
    //         return cv.config.getStringData("forever");
    //     }
    //     let h = Math.floor(time / 3600);
    //     let m = Math.floor(time % 3600 / 60);
    //     let s = Math.floor(time % 60);
    //     let str: string = "";
    //     if (h > 0) {
    //         str += h + cv.config.getStringData("hour");
    //     }
    //     if (m > 0 || (m == 0 && h > 0 && s > 0)) {
    //         str += m + cv.config.getStringData("minute");
    //     }
    //     if (s > 0) {
    //         str += s + cv.config.getStringData("seconds");
    //     }
    //     return str;
    // }
    // private showDisableBarrageBox(playerId: number): void {
    //     this._disBarragePlayerId = playerId;
    //     this._disBarrageBox.active = true;
    //     this._disBarrageTouch.active = true;
    //     if (this._disBarrageUpdate) {
    //         this._disBarrageUpdate = false;
    //         let tempNode: cc.Node = cc.find("button", this._disBarrageBox);
    //         let forbidden: number[] = cv.GameDataManager.tRoomData.forbidden;
    //         for (let i = 0; i < forbidden.length; ++i) {
    //             let itemNode: cc.Node = cc.instantiate(tempNode);
    //             itemNode.attr({time: forbidden[i]});
    //             cc.find("Background/Label", itemNode).getComponent(cc.Label).string = this.getTimeString(forbidden[i]);
    //             itemNode.active = true;
    //             this._disBarrageBox.addChild(itemNode);
    //         }
    //     }
    // }
    // private hideDisableBarrageBox(): void {
    //     this._disBarrageBox.active = false;
    //     this._disBarrageTouch.active = false;
    // }
    private isGameStarSeat(): boolean {
        return cv.GameDataManager.tRoomData.u32GameID === cv.Enum.GameId.StarSeat;
    }
}
