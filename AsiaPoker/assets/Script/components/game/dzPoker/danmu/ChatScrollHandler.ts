import cv from '../../../lobby/cv';

const { ccclass } = cc._decorator;

@ccclass
export default class ChatScrollHandler extends cc.Component {
    private startTouchPos: cc.Vec2 = cc.Vec2.ZERO;
    private startTime: number = 0;
    private isScrolling: boolean = false;
    private distanceThreshold: number = 10; // px threshold for tap
    private timeThreshold: number = 200; // ms threshold for quick tap

    onLoad() {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    }

    onTouchStart(event: cc.Event.EventTouch) {
        this.isScrolling = false;
        this.startTouchPos = event.touch.getLocation();
        this.startTime = Date.now();
    }

    onTouchMove(event: cc.Event.EventTouch) {
        const currentPos = event.touch.getLocation();
        const distance = currentPos.sub(this.startTouchPos).mag();
        if (distance > this.distanceThreshold) {
            this.isScrolling = true;
        }
    }

    onTouchEnd(event: cc.Event.EventTouch) {
        this.handleTouchEnd(event);
    }

    onTouchCancel(event: cc.Event.EventTouch) {
        this.handleTouchEnd(event);
    }

    private handleTouchEnd(event: cc.Event.EventTouch) {
        const endTime = Date.now();
        const duration = endTime - this.startTime;
        const endPos = event.touch.getLocation();
        const distance = endPos.sub(this.startTouchPos).mag();

        if (!this.isScrolling && distance <= this.distanceThreshold && duration <= this.timeThreshold) {
            cc.error('Closing chat view...');
            cv.MessageCenter.send("hide_scrollview");
        } else {
            cc.error('Not a quick tap; no close triggered.');
        }
    }
}
