import { CircleSprite, Head_Mode } from '../../../../common/tools/CircleSprite';
import Tag from '../../../../common/tools/Tag';

const { ccclass, property } = cc._decorator;

@ccclass
export default class BulltScreenBg extends cc.Component {
    @property(cc.Node) button: cc.Node = null;
    @property(cc.Node) buttonThumbUpNormal: cc.Node = null;
    @property(cc.Node) buttonThumbUpPressed: cc.Node = null;
    @property(cc.Node) bulltScreenBgNormal: cc.Node = null;
    @property(cc.Node) bulltScreenBgAtself: cc.Node = null;
    @property(cc.RichText) richtext: cc.RichText = null;
    @property(cc.Sprite) spAvatar: cc.Sprite = null;
    @property(cc.Label) lbNickName: cc.Label = null;
    @property(Tag) tag: Tag = null;

    public setPlayerAvatar(url: string, plat: number) {
        CircleSprite.setCircleSprite(this.spAvatar.node, url, plat, false, Head_Mode.CIRCLE);
    }
}
