import cv from '../../../lobby/cv';
import { GameScene } from '../GameScene';
import NodeStatusListener from '../../../lobby/nodeManager/NodeStatusListener';
import { NodeGroupType } from '../../../lobby/nodeManager/NodeStatusCenter';
import { JackfruitScene } from '../../jackfruit/JackfruitScene';
import { BarrageDanmuController } from './BarrageDanmuController';
import { FaceController } from './FaceController';
import { EmojiController } from './EmojiController';

const { ccclass, property } = cc._decorator;

enum FaceBarrageTab {
    DANMU = 0,
    EMOJI = 1,
    FACE = 2
}

//
// 明星桌弹幕
//
@ccclass
export default class FaceBarrage extends cc.Component {
    @property(cc.Node) modalLayer: cc.Node = null;
    @property(cc.Node) controllerUi: cc.Node = null;
    @property(cc.Node) tabUi: cc.Node = null;
    @property(cc.Node) barrage_button: cc.Node = null;
    @property(cc.Node) expression_button: cc.Node = null;
    @property(cc.Node) emoji_button: cc.Node = null;
    @property(cc.Node) barrageBtnTxtNode: cc.Node = null;
    @property(cc.Node) barrageBtnLineNode: cc.Node = null;
    @property(cc.Node) expressionBtnTxtNode: cc.Node = null;
    @property(cc.Node) expressionBtnLineNode: cc.Node = null;
    @property(cc.Node) emojiBtnTxtNode: cc.Node = null;
    @property(cc.Node) emojiBtnLineNode: cc.Node = null;

    public game: GameScene | JackfruitScene;
    public danmuController: BarrageDanmuController = null;
    public faceController: FaceController = null;
    public emojiController: EmojiController = null;
    private readonly tabButtonActiveColor: cc.Color = new cc.Color(251, 216, 136, 255);
    private readonly tabButtonInactiveColor: cc.Color = new cc.Color(137, 138, 138, 255);
    private currentView: FaceBarrageTab = FaceBarrageTab.DANMU;

    onLoad() {
        this.danmuController = this.node.getComponent(BarrageDanmuController);
        this.faceController = this.node.getComponent(FaceController);
        this.emojiController = this.node.getComponent(EmojiController);
        this.danmuController.faceView = this;
        this.faceController.faceView = this;
        this.emojiController.faceView = this;
        this.hideUi();
        this.initLanguage();

        // be carefull : add component on the controllerUi , not this.node
        const nodeStatusListener = this.controllerUi.addComponent(NodeStatusListener);
        nodeStatusListener.init([NodeGroupType.H5LiveStreamWebview]);

        cv.gameNet.requestBarrageCount();
    }

    public onBarrageTab(evt: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        this.showView(FaceBarrageTab.DANMU);
    }

    public onExpressionTab(evt: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        this.showView(FaceBarrageTab.FACE);
    }

    public onEmojiTab(evt: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        this.showView(FaceBarrageTab.EMOJI);
    }

    public onHideUi(evt: cc.Event): void {
        this.hideUi();
    }

    /**
     * setGameScene
     */
    public setGameScene(game: GameScene | JackfruitScene) {
        this.game = game;
    }

    start() {
        // this.barrageList.getComponent(ListView).bindScrollEventTarget(this);
        cv.MessageCenter.register('getBarrageCountNotice', this.onGetCounts.bind(this), this.node);
        cv.MessageCenter.register('hide_scrollview', this.hideUi.bind(this), this.node);
    }

    onDestroy() {
        cv.MessageCenter.unregister('getBarrageCountNotice', this.node);
        cv.MessageCenter.unregister('hide_scrollview', this.node);
        cv.GameDataManager.clearBarrageData();
    }

    /**
     * onGetCounts
     */
    public onGetCounts() {
        cv.GameDataManager.sortBarrageData();
    }

    /**
     * 显示ui
     */
    public showUi() {
        this.controllerUi.active = true;
        this.modalLayer.active = true;
        if (cv.GameDataManager.tRoomData.i32SelfSeat !== -1) {
            this.danmuController.setViewStyle(1);
            this.tabUi.active = true;
            this.faceController?.updateEmotionNeedCoin();
            this.showView(this.currentView);
        } else {
            this.danmuController.setViewStyle(0);
            this.tabUi.active = false;
            this.showView(FaceBarrageTab.DANMU);
        }
        this.danmuController.active();
        this.emojiController?.updateConfig();
    }

    /**
     *  hideUi
     */
    public hideUi() {
        this.danmuController.hide();
        this.faceController.hide();
        this.emojiController.hide();
        this.controllerUi.active = false;
        this.modalLayer.active = false;
    }

    public isShowing(): boolean {
        return this.controllerUi.active && this.modalLayer.active;
    }

    public setActiveEmoji(enabled: boolean, forceShow: boolean = false) {
        if (enabled) {
            this.enableView(FaceBarrageTab.EMOJI, forceShow);
        } else {
            this.disableView(FaceBarrageTab.EMOJI);
            this.showView(FaceBarrageTab.DANMU);
        }
    }

    private disableView(view: FaceBarrageTab) {
        let btn: cc.Node = null;
        switch (view) {
            case FaceBarrageTab.DANMU:
                this.danmuController.hide();
                btn = this.barrage_button;
                break;
            case FaceBarrageTab.EMOJI:
                this.emojiController.hide();
                btn = this.emoji_button;
                break;
            case FaceBarrageTab.FACE:
                this.faceController.hide();
                btn = this.expression_button;
                break;
        }
        if (cc.isValid(btn)) {
            btn.active = false;
            btn.parent.getComponent(cc.Layout)?.updateLayout();
        }
    }

    private enableView(view: FaceBarrageTab, forceShow: boolean = true) {
        let btn: cc.Node = null;
        const viewShow: FaceBarrageTab = forceShow ? view : this.currentView;
        switch (view) {
            case FaceBarrageTab.DANMU:
                btn = this.barrage_button;
                break;
            case FaceBarrageTab.EMOJI:
                btn = this.emoji_button;
                break;
            case FaceBarrageTab.FACE:
                btn = this.expression_button;
                break;
        }
        if (cc.isValid(btn)) {
            btn.active = true;
            btn.parent.getComponent(cc.Layout)?.updateLayout();
        }
        this.showView(viewShow);
    }

    private showView(view: FaceBarrageTab): void {
        switch (view) {
            case FaceBarrageTab.DANMU:
                this.danmuController.show();
                this.emojiController.hide();
                this.faceController.hide();
                break;
            case FaceBarrageTab.EMOJI:
                this.danmuController.hide();
                this.emojiController.show();
                this.faceController.hide();
                break;
            case FaceBarrageTab.FACE:
                this.danmuController.hide();
                this.emojiController.hide();
                this.faceController.show();
                break;
        }
        this.setTabTxt(this.barrage_button, view === FaceBarrageTab.DANMU);
        this.setTabTxt(this.emoji_button, view === FaceBarrageTab.EMOJI);
        this.setTabTxt(this.expression_button, view === FaceBarrageTab.FACE);
        this.currentView = view;
    }

    private initLanguage(): void {
        cv.StringTools.setLabelString(this.barrage_button, 'txt', 'Faceview_danmu_button_danmu');
        cv.StringTools.setLabelString(this.expression_button, 'txt', 'Faceview_danmu_button_face');
        cv.StringTools.setLabelString(this.emoji_button, 'txt', 'Faceview_danmu_button_emoji');
    }

    private setTabTxt(node: cc.Node, select: boolean): void {
        const color = select ? this.tabButtonActiveColor : this.tabButtonInactiveColor;

        switch (node) {
            case this.barrage_button:
                this.barrageBtnTxtNode.color = color;
                this.barrageBtnTxtNode.getComponent(cc.Label).enableBold = select;
                this.barrageBtnLineNode.active = select;
                break;
            case this.expression_button:
                this.expressionBtnTxtNode.color = color;
                this.expressionBtnTxtNode.getComponent(cc.Label).enableBold = select;
                this.expressionBtnLineNode.active = select;
                break;
            case this.emoji_button:
                this.emojiBtnTxtNode.color = color;
                this.emojiBtnTxtNode.getComponent(cc.Label).enableBold = select;
                this.emojiBtnLineNode.active = select;
                break;
        }
    }
}
