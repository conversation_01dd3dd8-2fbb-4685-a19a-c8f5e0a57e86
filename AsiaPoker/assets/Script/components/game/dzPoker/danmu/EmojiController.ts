import EmojiView from '../../../../../emoji/script/EmojiView';
import FaceView from './FaceView';
import cv from '../../../lobby/cv';
import JackfruitManager from '../../jackfruit/JackfruitManager';
import { FeeItem } from '../data/RoomData';
import { GameMain } from '../GameMain';
import EmojiLoader from '../../../../../emoji/script/EmojiLoader';
import FaceBarrage from './FaceBarrage';

const { ccclass, property } = cc._decorator;

@ccclass
export class EmojiController extends cc.Component {
    @property(cc.Node) emoji_panel: cc.Node = null;

    public faceView: FaceView | FaceBarrage = null;
    private _emojiView: EmojiView = null;
    private _usd_2_coin_ex_val: number = 0;
    private _coin_2_usd_ex_val: number = 0;

    public show() {
        this.emoji_panel.active = true;
    }

    public hide() {
        this.emoji_panel.active = false;
    }

    public setupEmoji(fullscreen: cc.Node) {
        this._emojiView = this.emoji_panel.getComponentInChildren(EmojiView);
        if (this._emojiView) {
            this._emojiView.setGame(this.faceView.game);
            this._emojiView.fullScreenParent = fullscreen;
        } else {
            cc.error('[setupEmoji] not found EmojiView');
        }
    }

    public emojiIconClick(type: number): void {
        if (cv.roomManager.getCurrentGameID() !== cv.Enum.GameId.Jackfruit) {
            if (cv.GameDataManager.tRoomData.i32SelfSeat !== -1) {
                if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Allin) {
                    cv.aofNet.RequestSendMagicEmoji(cv.GameDataManager.tRoomData.u32RoomId, type);
                } else {
                    cv.gameNet.RequestSendMagicEmoji(cv.GameDataManager.tRoomData.u32RoomId, type);
                }
            }
        } else if (JackfruitManager.tRoomData.nSelfSeatID !== -1) {
            cv.jackfruitNet.requestSendMagicEmoji(cv.roomManager.getCurrentRoomID(), type);
        }
        this.faceView?.hideUi();
    }

    public showEmojiAni(pos: cc.Vec2, aniIndex: number, isSelfSeat: boolean) {
        const isMuted = !cv.tools.isSoundEffectOpen();
        const newPos = this.faceView?.game.gameMain_panel.convertToNodeSpaceAR(pos);
        this._emojiView?.playAnimation(aniIndex, newPos, this.faceView?.game.gameMain_panel, isMuted, isSelfSeat);
        this._emojiView?.showCountdown(); // show all the emoji countdown animations
    }

    public updateConfig(forceUpdate: boolean = false) {
        const isShow: boolean = (this.faceView?.isShowing() && forceUpdate) || EmojiLoader.config.Enable;
        this._emojiView?.updateEmojiInfo();
        this.updateEmojiNeedCoin();
        this.faceView?.setActiveEmoji(isShow);
    }

    protected onLoad() {
        const hlType: number = cv.GameDataManager.tRoomData.currency === 101 ? 1 : 0;
        cv.worldNet.GetScalerQuoteRequest(hlType);
        cv.MessageCenter.register('update_info', this.updateEmojiNeedCoin.bind(this), this.node);
        cv.MessageCenter.register('update_gold', this.updateEmojiNeedCoin.bind(this), this.node);
        cv.MessageCenter.register('pot_changed', this.onPotSizeRestriction.bind(this), this.node);
        cv.MessageCenter.register('onGetScalerQuoteResponse', this.onGetScalerQuoteResponse.bind(this), this.node);
    }

    protected onDestroy() {
        cv.MessageCenter.unregister('update_info', this.node);
        cv.MessageCenter.unregister('update_gold', this.node);
        cv.MessageCenter.unregister('pot_changed', this.node);
        cv.MessageCenter.unregister('onGetScalerQuoteResponse', this.node);
    }

    private updateEmojiNeedCoin() {
        const gold = cv.dataHandler.getUserData().u32Chips;
        const usdt = cv.dataHandler.getUserData().usdt;
        let cost: number = 0;
        let isUsdTable: boolean = false;
        let isEnoughBalance: boolean = true;
        let balance: number = 0;
        if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
            cost = JackfruitManager.tRoomData.fee.magicEmojiFee;
            balance = gold;
        } else {
            const feeItem: FeeItem = cv.GameDataManager.tRoomData.pkPayMoneyItem.magicEmojiFee;
            if (feeItem) {
                isUsdTable = this.faceView?.game.gameMain_panel.getComponent(GameMain).isUSDTable();
                cost = isUsdTable ? feeItem.needUsd : feeItem.needCoin;
                balance = isUsdTable ? usdt : gold;
            } else {
                cc.error('[updateEmojiNeedCoin] not found magicEmojiFee');
            }
        }
        if (cost > balance) {
            // apply auto exchange
            let remainingAmount = cost - balance;
            const exchangeRate = isUsdTable ? this._usd_2_coin_ex_val : this._coin_2_usd_ex_val;
            remainingAmount *= exchangeRate; // Converted to other currency
            const exchangeChips = isUsdTable ? gold : usdt;
            isEnoughBalance = remainingAmount <= exchangeChips; // if the remain value is enough with other currency
        }
        this._emojiView?.updateCost(cost, isEnoughBalance, isUsdTable);
    }

    private onPotSizeRestriction(data: { pot: number; bb: number }): void {
        if (cv.roomManager.getCurrentGameID() !== cv.Enum.GameId.Jackfruit) {
            const isRestriction: boolean =
                data.pot > 0 &&
                data.bb > 0 &&
                EmojiLoader.config.PotSizeRestriction > 0 &&
                data.pot >= data.bb * EmojiLoader.config.PotSizeRestriction;
            this._emojiView?.setPotSizeRestriction(isRestriction);
        }
    }

    private onGetScalerQuoteResponse(msg: any): void {
        const rateNum = Number(msg.rate);
        if (isNaN(rateNum) || rateNum <= 0 || (msg.op_type !== 1 && msg.op_type !== 0)) {
            return;
        }
        if (msg.op_type === 0) {
            this._coin_2_usd_ex_val = rateNum;
        } else {
            this._usd_2_coin_ex_val = rateNum;
        }
    }
}
