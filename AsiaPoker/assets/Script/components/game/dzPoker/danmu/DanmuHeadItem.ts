import {FaceHeadItem} from './FaceHeadItem';
import cv from '../../../lobby/cv';
import {PlayerInfo} from '../data/RoomData';
import {CircleSprite} from '../../../../common/tools/CircleSprite';
import FaceView from './FaceView';

/**
 * @玩家头像
 */
const { ccclass, property } = cc._decorator;

@ccclass
export default class DanmuHeadItem extends FaceHeadItem {
    public onClick() {
        super.onClick();
        const faceView = this._faceView as FaceView;
        if (!faceView.onOff) {
            cv.TT.showMsg(
                cv.config.getStringData('Faceview_danmu_button_onOff_Tips'),
                cv.Enum.ToastType.ToastTypeWarning
            );
            return;
        }
        this.select_img.active = !this.select_img.active;
        if (this._playerData) {
            faceView.danmuController.onclickRoleHead(this._playerData.playerid, this.select_img.active);
        } else {
            console.log('@玩家头像数据为空');
        }
    }

    public setData(data: PlayerInfo) {
        super.setData(data);
        CircleSprite.setCircleSprite(this.roleHead, data.headurl, data.plat, false);
    }

    // IN case of Barrage history, we can't have reliable PlayerInfo data as players can come and leave, removing that player's PlayerInfo data 
    // which is maintianed in GameDatamanager. Hence for Barrage History we only work with URL and plat fields from PlayerInfo.
    public setAvatarURL(url: string, plat: number)
    {
        CircleSprite.setCircleSprite(this.roleHead, url, plat, false);
    }
}
