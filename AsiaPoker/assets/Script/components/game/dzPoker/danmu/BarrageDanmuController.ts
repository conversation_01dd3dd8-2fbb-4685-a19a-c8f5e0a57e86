import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './DanmuController';
import BarrageHeadItem from './BarrageHeadItem';
import { PlayerInfo } from '../data/RoomData';
import cv from '../../../lobby/cv';
import { GameMain } from '../GameMain';
import FaceBarrage from './FaceBarrage';
import game_protocol = require('../../../../common/pb/gs_protocol');
import game_pb = game_protocol.protocol;

const { ccclass, property } = cc._decorator;

enum TabType {
    txt = 0,
    user = 1
}

@ccclass
export class BarrageDanmuController extends DanmuController {
    @property(cc.Node) inputUi: cc.Node = null;
    @property(cc.Node) contentUi: cc.Node = null;
    @property(cc.Node) historyUi: cc.Node = null;
    // input
    @property(cc.Node) stateBtn: cc.Node = null;
    @property(cc.Node) sendBtn: cc.Node = null;
    @property(cc.Node) inputNode: cc.Node = null;
    // content
    @property(cc.Node) barrageList: cc.Node = null;
    @property(cc.Node) userList: cc.Node = null;
    @property(cc.Node) maskBtn: cc.Node = null;
    @property(cc.Node) txtBtn: cc.Node = null;
    @property(cc.Node) atBtn: cc.Node = null;
    @property(cc.Node) descriptionNode: cc.Node = null;
    @property(cc.Node) descriptionTouch: cc.Node = null;
    @property(cc.Node) tipNode: cc.Node = null;

    @property(cc.EditBox) inputEditBox: cc.EditBox = null;
    @property(cc.Node) inputTextNode: cc.Node = null;
    @property(cc.Node) inputContentNode: cc.Node = null;
    @property(cc.Node) inputPriceNode: cc.Node = null;
    @property(cc.Node) inputCharNumNode: cc.Node = null;
    @property(cc.Node) stateBtnOnNode: cc.Node = null;
    @property(cc.Node) descriptionItemNode: cc.Node = null;
    @property(cc.Label) tipNodeText: cc.Label = null;

    @property(cc.Node) txtBtnIconBg: cc.Node = null;
    @property(cc.Node) txtBtnIconLight: cc.Node = null;
    @property(cc.Node) txtBtnIconDark: cc.Node = null;
    @property(cc.Node) atBtnIconBg: cc.Node = null;
    @property(cc.Node) atBtnIconLight: cc.Node = null;
    @property(cc.Node) atBtnIconDark: cc.Node = null;

    public mute: boolean = false; // 禁言
    public muteTime: number = 0; // 禁言时长

    // 配置
    public canBarrage: boolean = true; // 可发自定义弹幕总开关(与预制弹幕无关)
    public canInput: boolean = true; // 桌上玩家可发自定义弹幕开关(与预制弹幕无关)
    public price: number = 0; // 自定义弹幕价格(与预制弹幕无关)
    public needUpdateUi: boolean = true;
    public needUpdateUser: boolean = true;
    public needUpdateTxt: boolean = true;
    public needUpdateDes: boolean = true;
    private roleHeadItemList: BarrageHeadItem[] = [];
    private roleHeadDataList: PlayerInfo[] = [];
    private _atPlayer: any = null;
    private _atStr: string = '@';
    private _atStrEnd: string = ' '; // at后面的空格
    private _autoHideTip: boolean = true;
    private _inputStringFormat: string = '<color=#ffab00>%s</color><color=#ffffff>%s</color>';
    private _inputStringMaxLen: number = 36;

    public active() {
        this.needUpdateUser = true;
        this.needUpdateUi = true;
        this.inputEditBox.string = '';
        this._atPlayer = null;
        this.setController();
        this.selectTabList(TabType.txt);
        this.hideDescription();
        if (this._autoHideTip) {
            this.hideTip();
        }
    }

    public show() {
        this.inputUi.active = true;
        this.contentUi.active = true;
        this.historyUi.active = this.contentUi.activeInHierarchy;
    }

    public hide() {
        this.inputUi.active = false;
        this.contentUi.active = false;
        this.historyUi.active = false;
    }

    public showDescription(): void {
        this.descriptionNode.active = true;
        this.descriptionTouch.active = true;
        if (this.needUpdateDes) {
            this.needUpdateDes = false;
            const explanTxt: string = cv.config.getStringData('FaceBarrage_explan');
            const explanTxtArr: string[] = explanTxt.split('\n');
            if (explanTxtArr.length === 0) {
                return;
            }
            for (let i = 0; i < explanTxtArr.length; ++i) {
                const itemNode: cc.Node = cc.instantiate(this.descriptionItemNode);
                const size: cc.Size = cv.resMgr.getLabelStringSize(
                    itemNode.children[1].getComponent(cc.Label),
                    explanTxtArr[i]
                );
                itemNode.height = size.height;
                itemNode.active = true;
                this.descriptionNode.addChild(itemNode);
            }
        }
    }

    public onselect(playerid: number) {
        for (let i = 0; i < this.roleHeadItemList.length; i++) {
            if (this.roleHeadItemList[i].getPlayerId() !== playerid) {
                this.roleHeadItemList[i].hideSelectImg();
            } else {
                this.roleHeadItemList[i].showSelectImg();
            }
        }
    }

    public currentCdStatus(): boolean {
        const onOff: boolean = cv.tools.isShowBarrage();
        return this.mute || !onOff || this._isInCd;
    }

    // 输入框事件开始-----//
    public onEditBegin(text: string, editbox: cc.EditBox, customEventData: string) {
        this.inputTextNode.opacity = 255;
        this.inputContentNode.active = false;
        this.inputPriceNode.active = false;
        this.inputCharNumNode.active = false;
    }

    public onTextChange(text: string, editbox: cc.EditBox, customEventData: string) {
        let txt: string = this.inputEditBox.string;
        txt = txt.replace(/\r\n|\n/g, '');
        this.inputEditBox.string = txt;
        const atPlayer: PlayerInfo = this.testEditAtPlayer(txt);
        // 刷新 富文本
        let atPlayerTxt: string = '';
        let contentTxt: string = txt;
        if (atPlayer) {
            atPlayerTxt = this._atStr + atPlayer.name;
            contentTxt = txt.substring(atPlayerTxt.length);
            this._atPlayer = { name: atPlayerTxt, playerid: atPlayer.playerid };
        } else {
            this._atPlayer = null;
        }
        const msg: string = cv.StringTools.formatC(this._inputStringFormat, atPlayerTxt, contentTxt);
        this.inputContentNode.getComponent(cc.RichText).string = msg;
        this.setCharNum(txt);
        // 刷新 头像
        this.setSelectHead();
    }

    public onEditEnd(text: string, editbox: cc.EditBox, customEventData: string) {
        this.inputTextNode.opacity = 0;
        this.inputContentNode.active = true;
        let msg: string = this.inputEditBox.string;
        if (msg.length === 0) {
            msg = cv.config.getStringData('FaceBarrage_input_txt');
            cv.StringTools.setRichTextString(this.inputContentNode, msg);
            this.inputPriceNode.active = true;
            this.inputCharNumNode.active = false;
        } else {
            this.inputPriceNode.active = false;
            this.inputCharNumNode.active = true;
        }
    }

    public onTxtTab(evt: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        this.selectTabList(TabType.txt);
        this.hideDescription();
    }

    public onUserTab(evt: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        this.selectTabList(TabType.user);
        this.hideDescription();
    }

    public onHelpBtn(evt: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        const vis: boolean = this.descriptionNode.active;
        if (vis) {
            this.hideDescription();
        } else {
            this.showDescription();
        }
    }

    public onSendBtn(evt: cc.Event): void {
        if (cv.dataHandler.getUserData().u32Chips < this.price) {
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode172'), cv.Enum.ToastType.ToastTypeError);
            return;
        }
        // editBox内容(限制字符数为36):
        // 1 at列表
        // 2 自定义文本(Emoji表情)
        //
        const txt: string = this.inputEditBox.string;
        let contentTxt: string = txt;
        if (this._atPlayer) {
            contentTxt = txt.substring(this._atPlayer.name.length);
        }
        if (txt.length === 0 || contentTxt.length === 0) {
            cv.TT.showMsg(cv.config.getStringData('FaceBarrage_input_txt_empty'), cv.Enum.ToastType.ToastTypeSuccess);
            return;
        }
        if (this.getStrLen(txt) > this._inputStringMaxLen) {
            cv.TT.showMsg(cv.config.getStringData('ServerErrorCode1276'), cv.Enum.ToastType.ToastTypeSuccess);
            return;
        }
        const contentTxtNew: string = cv.StringTools.isSensitiveWords(contentTxt, true);
        contentTxt = contentTxtNew || contentTxt;
        // Specials (Unicode block)
        contentTxt = contentTxt.replace(new RegExp('[\ufff0-\uffff]', 'g'), '*');
        this.sendBarrage(contentTxt, game_pb.BarrageType.Enum_Custom);
    }

    public onStateBtn(evt: cc.Event): void {
        cv.AudioMgr.playButtonSound('button_click');
        const onOff: boolean = cv.tools.isShowBarrage();
        cv.tools.setShowBarrage(!onOff);
        this.clikcOnOff();
    }

    public setViewStyle(style: number): void {
        const historyWidget: cc.Widget = this.historyUi.getComponent(cc.Widget);
        if (style === 0) {
            this.inputUi.color = cc.color(25, 25, 33, 255);
            this.contentUi.height = 830;
            this.barrageList.height = 625;
            this.userList.height = 607;
            historyWidget.top = 160;
            historyWidget.bottom = 1000;
            this.adjustRoleHead(0);
        } else if (style === 1) {
            this.inputUi.color = cc.color(31, 33, 46, 255);
            this.contentUi.height = 680;
            this.barrageList.height = 500;
            this.userList.height = 487;
            historyWidget.top = 160;
            historyWidget.bottom = 1090;
            this.adjustRoleHead(1);
        }
        historyWidget.updateAlignment();
        cv.resMgr.adaptWidget(this.maskBtn)
        cv.resMgr.adaptWidget(this.historyUi, true);
    }

    public onBarrageMute(muteObj: any): void {
        this.mute = muteObj.mute;
        this.muteTime = muteObj.time;
        // 清理
        if (this._isInCd) {
            this._cdTime = 5;
            this._isInCd = false;
            this.unschedule(this.updateDanmuCd);
        }
        this.setController();
        this.unschedule(this.updateMuteTime);
        if (this.mute) {
            this.lockController(true);
            if (this.muteTime === -1) {
                // 永久禁言
                this.showTip(cv.config.getStringData('ServerErrorCode1274'), 3);
            } else {
                this.showTip(
                    cv.StringTools.formatC(
                        cv.config.getStringData('FaceBarrage_danmu_disable'),
                        this.secondToMinute(this.muteTime)
                    )
                );
                this.schedule(this.updateMuteTime, 10);
            }
        } else {
            this.lockController(false);
            this.hideTip();
        }
        cv.MessageCenter.send('updateCdStatus', this.currentCdStatus());
    }

    public onBarrageConfChange(confObj: any): void {
        this.canBarrage = confObj.canBarrage;
        this.canInput = confObj.canInput;
        this.price = confObj.price;
        this.setPrice();
        this.setController();
    }

    public onBarrageUpdate(confObj: any): void {
        this.canBarrage = confObj.canBarrage;
        this.canInput = confObj.canInput;
        this.price = confObj.price;
        this.setPrice();
        this.onBarrageMute({ mute: confObj.mute, time: confObj.time });
    }

    /**
     * name
     */
    public onResetCdtime() {
        // 设置发送cd状态
        this._isInCd = true;
        this._cdTime = 5;
        this.setController();
        this.lockController(true);
        cv.MessageCenter.send('updateCdStatus', this.currentCdStatus());
        this.schedule(this.updateDanmuCd, 1);
    }

    /**
     * 初始化头像
     */
    public initRoleHead() {
        for (let index = 0; index < 6; index++) {
            // 明星桌最多6人
            const roleHead = cc.instantiate(this.roleHeadItemPrefab);
            roleHead.getComponent(BarrageHeadItem).setFaceView(this.faceView);
            roleHead.active = true;
            this.userList.addChild(roleHead);
            this.roleHeadItemList.push(roleHead.getComponent(BarrageHeadItem));
        }
    }

    /**
     * 显示头像
     */
    public showRoleHead() {
        this.userList.active = true;
        if (this.needUpdateUser) {
            this.needUpdateUser = false;
            const playerData: PlayerInfo[] = [];
            const len = cv.GameDataManager.tRoomData.kTablePlayerList.length;
            for (let i = 0; i < len; i++) {
                if (cv.GameDataManager.tRoomData.kTablePlayerList[i].playerid !== cv.dataHandler.getUserData().u32Uid) {
                    playerData.push(cv.GameDataManager.tRoomData.kTablePlayerList[i]);
                }
            }
            this.roleHeadDataList = playerData;
            for (let index = 0; index < this.roleHeadItemList.length; index++) {
                if (index < playerData.length) {
                    this.roleHeadItemList[index].setData(playerData[index]);
                    this.roleHeadItemList[index].node.active = true;
                } else {
                    this.roleHeadItemList[index].clearData();
                    this.roleHeadItemList[index].node.active = false;
                }
            }
        }
    }

    /**
     * 点击@玩家头像
     *
     */
    public onclickRoleHead(playerid: number, isSelect: boolean) {
        let atPlayerItem: BarrageHeadItem = null;
        for (let i = 0; i < this.roleHeadItemList.length; i++) {
            this.roleHeadItemList[i].hideSelectImg();
            if (this.roleHeadItemList[i].getPlayerId() === playerid) {
                atPlayerItem = this.roleHeadItemList[i];
            }
        }
        if (atPlayerItem && isSelect) {
            this.addAtPlayer({ name: this._atStr + atPlayerItem.getRoleName(), playerid });
            atPlayerItem.showSelectImg();
        } else {
            this.removeAtPlayer();
        }
    }

    /**
     * 显示弹幕UI
     */
    public showScrollView() {
        super.showScrollview();
        this.barrageList.active = true;
        this.setViewStyle(cv.GameDataManager.tRoomData.i32SelfSeat === -1 ? 0 : 1);
        if (this.needUpdateTxt) {
            this.needUpdateTxt = false;
            cv.MessageCenter.send('updateCdStatus', this.currentCdStatus());
        } else if (this.needUpdateUi) {
            this.scrollView.getComponent(cc.ScrollView).scrollToTop(0); // 控件bug:当t=0时不触发ui刷新
        }
    }

    public hideScrollView(): void {
        this.barrageList.active = false;
    }

    /**
     * 点击弹幕
     * @param event
     */
    public danmuItemClick(params: any) {
        this.sendBarrage(params);
    }

    public sendBarrage(params: any, type: game_pb.BarrageType = game_pb.BarrageType.Enum_System) {
        cv.AudioMgr.playEffect('zh_CN/game/dzpoker/audio/danmu');
        if (this.currentCdStatus()) {
            return;
        }
        // 设备cd状态
        this._isInCd = true;
        const atPlayer: any = this._atPlayer;
        this.setController();
        this.lockController(true);
        cv.MessageCenter.send('updateCdStatus', this.currentCdStatus());
        this.schedule(this.updateDanmuCd, 1);
        // 自已
        const data: game_pb.NoticeSendBarrage = new game_pb.NoticeSendBarrage();
        data.content = cv.String(params);
        data.nickname = cv.dataHandler.getUserData().nick_name;
        data.playerid = cv.Number(cv.dataHandler.getUserData().user_id);
        data.thump_up_status = 1;
        data.ctype = type;
        const at_list: string[] = [];
        const at_uid_list: number[] = [];
        if (atPlayer) {
            at_list.push(atPlayer.name);
            at_uid_list.push(atPlayer.playerid);
        }
        data.at_list = at_list;
        // cv.GameDataManager.addDanmuMsg(data);
        (this.faceView as FaceBarrage).hideUi();
        // 发送给服务器
        cv.gameNet.requestSendBarrage(cv.String(params), at_list, at_uid_list, 1, type);
    }

    public updateMuteTime(dt: number): void {
        this.muteTime -= dt;
        if (this.muteTime <= 0) {
            cv.GameDataManager.tRoomData.muteCustomBarrageSeconds = 0; // 在DanmuView中使用到
            this.muteTime = 0;
            this.mute = false;
            this.setController();
            this.lockController(false);
            this.hideTip();
            cv.MessageCenter.send('updateCdStatus', this.currentCdStatus());
            this.unschedule(this.updateMuteTime);
        } else {
            this.showTip(
                cv.StringTools.formatC(
                    cv.config.getStringData('FaceBarrage_danmu_disable'),
                    this.secondToMinute(this.muteTime)
                )
            );
        }
    }

    /**
     * 刷新cd时间
     */
    public updateDanmuCd() {
        this._cdTime -= 1;
        if (this._cdTime <= 0) {
            this._cdTime = 5;
            this._isInCd = false;
            // this.updateCdStatus();
            this.setController();
            this.lockController(false);
            this.hideTip();
            cv.MessageCenter.send('updateCdStatus', this.currentCdStatus());
            this.unschedule(this.updateDanmuCd);
        } else {
            this.showTip(cv.StringTools.formatC(cv.config.getStringData('Faceview_danmu_cd_tips'), this._cdTime));
        }
    }

    /**
     * 点击弹幕开关
     */
    public clikcOnOff() {
        cv.MessageCenter.send('danmu_onOff', cv.tools.isShowBarrage());
        this.setController();
        if (cv.tools.isShowBarrage()) {
            this.lockController(false);
            this.showTip(cv.config.getStringData('Faceview_danmu_button_on'), 3);
        } else {
            this.lockController(true);
            this.showTip(cv.config.getStringData('Faceview_danmu_button_off'), 3);
        }
        cv.MessageCenter.send('updateCdStatus', this.currentCdStatus());
    }

    protected onLoad() {
        super.onLoad();
        cv.MessageCenter.register('barrageMute', this.onBarrageMute.bind(this), this.node);
        cv.MessageCenter.register('barrageConfChange', this.onBarrageConfChange.bind(this), this.node);
        cv.MessageCenter.register('barrageUpdate', this.onBarrageUpdate.bind(this), this.node);

        this.initScrollviewData();
        this.descriptionTouch.on(cc.Node.EventType.TOUCH_END, (event) => {
            this.hideDescription();
        });
        this.descriptionTouch._touchListener.setSwallowTouches(false); // touch事件穿透
    }

    protected onDestroy() {
        super.onDestroy();
        cv.MessageCenter.unregister('barrageMute', this.node);
        cv.MessageCenter.unregister('barrageConfChange', this.node);
        cv.MessageCenter.unregister('barrageUpdate', this.node);
        this.unschedule(this.updateDanmuCd);
        this.unschedule(this.updateMuteTime);
    }

    protected start() {
        this.initRoleHead();
    }

    /**
     * 是否汉字(表意字)
     * @param char
     * @returns
     */
    private chineseChar(char: string): boolean {
        if (!char) {
            return false;
        }
        // E000—F8FF 私用区
        const cp: number = char.codePointAt(0);
        if (
            (cp >= 0x4e00 && cp <= 0x9fff) ||
            (cp >= 0x3400 && cp <= 0x4dbf) ||
            (cp >= 0x20000 && cp <= 0x2a6df) ||
            (cp >= 0x2a700 && cp <= 0x2b73f) ||
            (cp >= 0x2b740 && cp <= 0x2b81f) ||
            (cp >= 0x2b820 && cp <= 0x2ceaf) ||
            (cp >= 0x2ceb0 && cp <= 0x2ebef) ||
            (cp >= 0x30000 && cp <= 0x3134f) ||
            (cp >= 0x2f00 && cp <= 0x2fdf) ||
            (cp >= 0x2e80 && cp <= 0x2eff) ||
            (cp >= 0xf900 && cp <= 0xfaff) ||
            (cp >= 0x2f800 && cp <= 0x2fa1f) ||
            (cp >= 0xe815 && cp <= 0xe86f) ||
            (cp >= 0xe400 && cp <= 0xe5e8) ||
            (cp >= 0xe600 && cp <= 0xe6cf) ||
            (cp >= 0x31c0 && cp <= 0x31ef) ||
            (cp >= 0x2ff0 && cp <= 0x2fff) ||
            (cp >= 0x3100 && cp <= 0x312f) ||
            (cp >= 0x31a0 && cp <= 0x31bf) ||
            (cp >= 0x3000 && cp <= 0x303f) ||
            (cp >= 0xff00 && cp <= 0xffef)
        ) {
            return true;
        }
        return false;
    }

    /**
     * 返回字符串所占位置(数量)
     * 2个字节占1位置, 单个汉字强制占2位置
     * utf-16 编码规则:基本平面字符占2字节(\u0000-\uffff),辅助平面字符占4字节(\u010000-\u10ffff)
     * @param str
     * @returns
     */
    private getStrLen(str: string): number {
        const charData: string = !str ? '' : str;
        let cnt: number = 0;
        if (charData.length === 0) {
            return cnt;
        }
        const charArr: string[] = Array.from(charData);
        const len: number = charArr.length;
        let cp: number = 0;
        const cpFFFF: number = 0xffff;
        for (let i = 0; i < len; ++i) {
            if (this.chineseChar(charArr[i])) {
                cnt += 2;
            } else {
                cp = charArr[i].codePointAt(0);
                if (cp > cpFFFF) {
                    cnt += 2; // 辅助平面
                } else {
                    cnt += 1; // 基本平面
                }
            }
        }
        return cnt;
    }

    private hideTip(): void {
        this.tipNode.active = false;
    }

    private showTip(txt: string, time: number = -1): void {
        this.tipNode.stopAllActions();
        this.tipNode.active = true;
        const size: cc.Size = cv.resMgr.getLabelStringSize(this.tipNodeText, txt);
        this.tipNode.setContentSize(size.width + 82 * 2, size.height + 11.29 * 2);
        // this.tipNode.getComponent(cc.Layout).updateLayout();
        // this.tipNode.getComponent(cc.Widget).updateAlignment();
        if (time > 0) {
            this._autoHideTip = true;
            this.tipNode.runAction(cc.sequence(cc.delayTime(time), cc.callFunc(this.hideTip, this)));
        } else {
            this._autoHideTip = false;
        }
    }

    private hideDescription(): void {
        this.descriptionNode.active = false;
        this.descriptionTouch.active = false;
    }

    private selectTabList(tab: TabType): void {
        if (tab === TabType.txt) {
            this.txtBtnIconBg.active = true;
            this.txtBtnIconLight.active = true;
            this.txtBtnIconDark.active = false;
            this.atBtnIconBg.active = false;
            this.atBtnIconLight.active = false;
            this.atBtnIconDark.active = true;
            this.hideRoleHead();
            this.showScrollView();
        } else if (tab === TabType.user) {
            this.txtBtnIconBg.active = false;
            this.txtBtnIconLight.active = false;
            this.txtBtnIconDark.active = true;
            this.atBtnIconBg.active = true;
            this.atBtnIconLight.active = true;
            this.atBtnIconDark.active = false;
            this.hideScrollView();
            this.showRoleHead();
        }
        this.needUpdateUi = false;
    }

    private setStateBtn(): void {
        const sp: cc.Node = this.stateBtnOnNode;
        const btn: cc.Button = this.stateBtn.getComponent(cc.Button);
        const onOff: boolean = cv.tools.isShowBarrage(); // 客户端弹幕开关
        if (this.mute || !this.canCustomBarrage() || this._isInCd) {
            btn.interactable = false;
            btn.enabled = false;
            sp.active = false;
        } else if (!onOff) {
            btn.interactable = true;
            btn.enabled = true;
            sp.active = false;
        } else {
            btn.interactable = true;
            btn.enabled = true;
            sp.active = true;
        }
    }

    private setSendBtn(): void {
        const btn: cc.Button = this.sendBtn.getComponent(cc.Button);
        const onOff: boolean = cv.tools.isShowBarrage();
        if (this.mute || !this.canCustomBarrage() || this._isInCd || !onOff) {
            btn.interactable = false;
            btn.enabled = false;
        } else {
            btn.interactable = true;
            btn.enabled = true;
        }
    }

    private setInputBox(): void {
        if (!cc.isValid(this.inputNode)) return;
        const onOff: boolean = cv.tools.isShowBarrage();
        let msg: string = null;
        this.inputContentNode.active = true;
        this.inputTextNode.opacity = 0;
        if (this.mute || !this.canCustomBarrage()) {
            this.inputEditBox.string = '';
            this._atPlayer = null;
            this.inputEditBox.node.active = false;
            msg = cv.config.getStringData('FaceBarrage_input_txt_disable');
            cv.StringTools.setRichTextString(this.inputContentNode, msg);
            this.inputPriceNode.active = false;
            this.inputCharNumNode.active = false;
        } else if (this._isInCd) {
            this.inputEditBox.string = '';
            this._atPlayer = null;
            this.inputEditBox.node.active = false;
            msg = cv.config.getStringData('FaceBarrage_input_txt_send_disable');
            cv.StringTools.setRichTextString(this.inputContentNode, msg);
            this.inputPriceNode.active = false;
            this.inputCharNumNode.active = false;
        } else if (!onOff) {
            this.inputEditBox.string = '';
            this._atPlayer = null;
            this.inputEditBox.node.active = false;
            msg = cv.config.getStringData('FaceBarrage_danmu_off');
            cv.StringTools.setRichTextString(this.inputContentNode, msg);
            this.inputPriceNode.active = false;
            this.inputCharNumNode.active = false;
        } else {
            // 输入框开启但不处于输入的情况下
            this.inputEditBox.node.active = true;
            msg = this.inputEditBox.string;
            if (msg.length === 0) {
                msg = cv.config.getStringData('FaceBarrage_input_txt');
                cv.StringTools.setRichTextString(this.inputContentNode, msg);
                this.inputPriceNode.active = true;
                this.inputCharNumNode.active = false;
            } else {
                this.inputPriceNode.active = false;
                this.inputCharNumNode.active = true;
                this.setCharNum(msg);
            }
        }
    }

    private setSelectHead(): void {
        const playerid: number = this._atPlayer ? this._atPlayer.playerid : 0;
        for (let i = 0; i < this.roleHeadItemList.length; i++) {
            if (this.roleHeadItemList[i].getPlayerId() !== 0 && this.roleHeadItemList[i].getPlayerId() === playerid) {
                this.roleHeadItemList[i].showSelectImg();
            } else {
                this.roleHeadItemList[i].hideSelectImg();
            }
        }
    }

    private setController(): void {
        this.setInputBox();
        this.setStateBtn();
        this.setSendBtn();
        this.setSelectHead();
    }

    private setPrice(): void {
        const temp: string = cv.StringTools.numToFloatString(this.price);
        const isUSDTable = this.faceView.game.gameMain_panel.getComponent(GameMain).isUSDTable();
        this.inputPriceNode.getComponent(cc.Label).string = cv.StringTools.formatC(
            cv.config.getStringData(
                isUSDTable ? 'FaceBarrage_input_txt_price_usd' : 'FaceBarrage_input_txt_price_gold'
            ),
            temp
        );
    }

    // adjust lock controller
    private lockController(lock: boolean): void {
        this.maskBtn.active = lock || this.currentCdStatus();
    }

    private addAtPlayer(atPlayer: any): void {
        if (this.currentCdStatus() || (this._atPlayer && this._atPlayer.playerid === atPlayer.playerid)) {
            return;
        }
        const txt: string = this.inputEditBox.string;
        let contentTxt: string = txt;
        if (this._atPlayer) {
            let atLen: number = this._atPlayer.name.length;
            atLen = txt[atLen] === this._atStrEnd ? atLen + 1 : atLen;
            contentTxt = txt.substring(atLen); // 去掉at player后真正的内容
        }
        this._atPlayer = atPlayer;
        contentTxt = this._atStrEnd + contentTxt;

        this.inputEditBox.string = atPlayer.name + contentTxt;
        const msg: string = cv.StringTools.formatC(this._inputStringFormat, atPlayer.name, contentTxt);
        this.inputContentNode.getComponent(cc.RichText).string = msg;
        this.inputPriceNode.active = false;
        this.inputCharNumNode.active = true;
        this.setCharNum(this.inputEditBox.string);
    }

    private removeAtPlayer(): void {
        if (this.currentCdStatus() || !this._atPlayer) {
            return;
        }
        const txt: string = this.inputEditBox.string;
        let atLen: number = this._atPlayer.name.length;
        atLen = txt[atLen] === this._atStrEnd ? atLen + 1 : atLen;
        const contentTxt: string = txt.substring(atLen);
        this._atPlayer = null;
        this.inputEditBox.string = contentTxt;
        let msg: string = null;
        if (contentTxt.length === 0) {
            const canCusBarrage: boolean = this.canCustomBarrage();
            msg = !canCusBarrage
                ? cv.config.getStringData('FaceBarrage_input_txt_disable')
                : cv.config.getStringData('FaceBarrage_input_txt');
            this.inputPriceNode.active = canCusBarrage;
            cv.StringTools.setRichTextString(this.inputContentNode, msg);
            this.inputCharNumNode.active = false;
        } else {
            msg = cv.StringTools.formatC(this._inputStringFormat, '', contentTxt);
            this.inputPriceNode.active = false;
            this.inputContentNode.getComponent(cc.RichText).string = msg;
            this.inputCharNumNode.active = true;
            this.setCharNum(contentTxt);
        }
    }

    private testEditAtPlayer(txt: string): PlayerInfo {
        if (txt == null || txt.length <= this._atStr.length) {
            return null;
        }
        let atPlayer: PlayerInfo = null;
        for (let i = 0; i < this.roleHeadDataList.length; ++i) {
            const regexp: RegExp = new RegExp('^' + this._atStr + this.roleHeadDataList[i].name);
            if (regexp.test(txt)) {
                atPlayer = this.roleHeadDataList[i];
                break;
            }
        }
        return atPlayer;
    }

    // 输入框事件结束-----//
    private setCharNum(str: string): void {
        const len: number = this.getStrLen(str);
        const msg: string = cv.StringTools.formatC('%d/%d', len, this._inputStringMaxLen);
        this.inputCharNumNode.getComponent(cc.Label).string = msg;
    }

    private canCustomBarrage(): boolean {
        if (cv.GameDataManager.tRoomData.i32SelfSeat !== -1) {
            // 桌上玩家
            return this.canBarrage && this.canInput;
        }
        return this.canBarrage;
    }

    private secondToMinute(seconds: number): number {
        if (seconds < 0) {
            return 0;
        }
        return Math.ceil(seconds / 60.0);
    }

    private adjustRoleHead(viewStyle: number): void {
        const h: number = viewStyle === 1 ? 239 : 269;
        const y: number = viewStyle === 1 ? 112 : 128;
        const dh: number = viewStyle === 1 ? 250 : 279;
        const dw: number = this.userList.getContentSize().width * 0.5 - 202;
        const x: number = -dw;
        for (let i = 0; i < this.roleHeadItemList.length; ++i) {
            this.roleHeadItemList[i].select_img.height = h;
            const roleHead: cc.Node = this.roleHeadItemList[i].node;
            roleHead.setPosition(x + dw * (i % 3), -(y + dh * Math.floor(i / 3)));
        }
    }

    private hideRoleHead(): void {
        this.userList.active = false;
    }
}
