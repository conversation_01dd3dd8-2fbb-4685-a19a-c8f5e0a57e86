// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import cv from "../../../lobby/cv";
import DanmuHeadItem from "./DanmuHeadItem";
import game_protocol = require('../../../../../Script/common/pb/gs_protocol');
import game_pb = game_protocol.protocol;

const {ccclass, property} = cc._decorator;

@ccclass
export default class BarrageHistoryItem extends cc.Component {

    @property(cc.Label)
    content: cc.Label = null;

    @property(cc.Label)
    username: cc.Label = null;

    @property(cc.Label)
    timestamp: cc.Label = null;

    @property(cc.Color)
    hihglightColor: cc.Color = cc.Color.WHITE;

    @property(DanmuHeadItem)
    protected avatarDisplay: DanmuHeadItem = null;

    @property(cc.Node)
    bg_node: cc.Node = null;


    updateItemData(data: any): void {
        if (!data) {
            return;
        }
        this.username.string = data.nickname;

        // For barrages sent by the local player are highlighted with accent color.
        if (data.playerid === cv.dataHandler.getUserData().user_id) {
            this.username.node.color = this.hihglightColor;
        } else {
            this.username.node.color = new cc.Color().fromHEX("#BCC6E1");
        }
        this.timestamp.string = cv.StringTools.formatTime(data.send_time, cv.Enum.eTimeType.Hour_Min_Sec);
        let ID = NaN;
        if (/^\d+$/.test(data.content)) { 
            ID= parseInt(data.content, 10); 
        } 
        
        let barrageContent = data.ctype === game_pb.BarrageType.Enum_System ? cv.GameDataManager.getBarrageContentById(ID) : data.content;
        
        if (data.content && data.ctype > 1 && cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
            if (cv.StringTools.isNumber(data.content)) {
                barrageContent = cv.config.getStringData("jackfruit_danmu_label" + data.content);
            } else {
                const processedContent = cv.tools.getContentStr(data.content);
                barrageContent = processedContent.length > 0 ? processedContent : data.content;
            }
        }

        if(!barrageContent && data.content.length>0)// if predefine msg is thare then it will show other wise system msg show 
            barrageContent=data.content;

        let atListContent = "";
        if (Array.isArray(data.at_list)) {
            atListContent = data.at_list.map(item => item.replace(/\s+/g, '')).join(" "); // Remove all spaces and join with a single space
        }

        // Combine at_list and barrageContent
        this.content.string = `${atListContent} ${barrageContent}`.trim();
        this.avatarDisplay.setAvatarURL(data.avatar, data.plat);

        // 立刻刷新widget
        // cv.resMgr.adaptWidget(this.node, true);
    }

    updateItemAsTableMessage(data: any, contentString: string)
    {
        if (!data) {
            return;
        }
        this.username.string = cv.config.getStringData("System_Name");
        this.timestamp.string = cv.StringTools.formatTime(data.tableBarrageMessage.send_time, cv.Enum.eTimeType.Hour_Min_Sec);
        cv.resMgr.getLabelStringSize(this.content, contentString);
        this.node.setContentSize(this.node.width,  this.bg_node.height+15); // set node's  height as BG's height 
    }

    updateSVReuseData(index: number, dataArray: any[]): void {
        if (index < 0 || index >= dataArray.length) {
            return;
        }
        // console.error(`[BarrageHistoryItem](updateSVReuseData) index: ${index}, dataArray: ${dataArray.length}`, dataArray);
        this.updateItemData(dataArray[index]);
    }
}
