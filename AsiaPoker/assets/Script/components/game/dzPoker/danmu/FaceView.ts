/**
 * 弹幕界面
 */
import cv from '../../../lobby/cv';
import { GameScene } from '../GameScene';
import { JackfruitScene } from '../../jackfruit/JackfruitScene';
import { FaceDanmuController } from './FaceDanmuController';
import { FaceController } from './FaceController';
import { EmojiController } from './EmojiController';

const { ccclass, property } = cc._decorator;

enum FaceViewTab {
    DANMU = 0,
    EMOJI = 1,
    FACE = 2
}

@ccclass
export default class FaceView extends cc.Component {
    // ui界面
    @property(cc.Node) danmu_ui: cc.Node = null;

    // 表情按钮
    @property(cc.Node) face_button: cc.Node = null;
    // 弹幕的按钮
    @property(cc.Node) danmu_button: cc.Node = null;

    @property(cc.Node) emoji_button: cc.Node = null;

    @property(cc.Node) danmu_button_highlight: cc.Node = null;
    @property(cc.Node) danmu_button_label: cc.Node = null;
    @property(cc.Node) face_button_highlight: cc.Node = null;
    @property(cc.Node) face_button_label: cc.Node = null;
    @property(cc.Node) emoji_button_highlight: cc.Node = null;
    @property(cc.Node) emoji_button_label: cc.Node = null;

    public onOff: boolean = true;

    public game: GameScene | JackfruitScene;
    public danmuController: FaceDanmuController = null;
    public faceController: FaceController = null;
    public emojiController: EmojiController = null;
    @property(cc.Color) tabButtonActiveColor: cc.Color = new cc.Color(251, 216, 136);
    @property(cc.Color) tabButtonInactiveColor: cc.Color = new cc.Color(137, 138, 138);
    private currentView: FaceViewTab = FaceViewTab.DANMU;

    onLoad() {
        this.danmuController = this.node.getComponent(FaceDanmuController);
        this.faceController = this.node.getComponent(FaceController);
        this.emojiController = this.node.getComponent(EmojiController);
        this.danmuController.faceView = this;
        this.faceController.faceView = this;
        this.emojiController.faceView = this;
        this.showView(FaceViewTab.DANMU);
        this.danmu_ui.active = false;
        this.initLanguage();
        if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
            cv.jackfruitNet.requestBarrageCount();
        } else {
            cv.gameNet.requestBarrageCount();
        }
    }

    /**
     * setGameScene
     */
    public setGameScene(game: any) {
        this.game = game;
    }

    start() {
        cv.MessageCenter.register('getBarrageCountNotice', this.onGetCounts.bind(this), this.node);
        cv.MessageCenter.register('hide_scrollview', this.hideUi.bind(this), this.node);
    }

    onDestroy() {
        cv.MessageCenter.unregister('getBarrageCountNotice', this.node);
        cv.MessageCenter.unregister('hide_scrollview', this.node);
        cv.GameDataManager.clearBarrageData();
    }

    /**
     * onGetCounts
     */
    public onGetCounts() {
        cv.GameDataManager.sortBarrageData();
    }

    initLanguage() {
        // let str = "";
        // if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.Jackfruit) {
        //     str = "jackfruit_danmu_label%d";
        // } else {
        //     str = "Faceview_danmu_text_%d";
        // }
        // for (let index = 0; index < 5; index++) {
        //     cv.StringTools.setLabelString(this.node, cv.StringTools.formatC("danmu_ui/danmu_panel/button%d/txt_0", index), cv.StringTools.formatC(str, index));
        // }
        cv.StringTools.setLabelString(this.danmu_button, 'Label', 'Faceview_danmu_button_danmu');
        cv.StringTools.setLabelString(this.face_button, 'Label', 'Faceview_danmu_button_face');
        cv.StringTools.setLabelString(this.emoji_button, 'Label', 'Faceview_danmu_button_emoji');
    }

    public faceBgClick(event: cc.Event) {
        event.stopPropagation();
        this.danmu_ui.active = false;
    }

    /**
     * 点击弹幕按钮
     * @param event
     */
    public onClickDanmuBtn(event: cc.Event) {
        cv.AudioMgr.playButtonSound('button_click');
        this.showView(FaceViewTab.DANMU);
    }

    /**
     * 点击表情按钮
     * @param event
     */
    public onClickFaceBtn(event: cc.Event) {
        cv.AudioMgr.playButtonSound('button_click');
        this.showView(FaceViewTab.FACE);
    }

    public onClickEmojiBtn(event: cc.Event) {
        cv.AudioMgr.playButtonSound('button_click');
        this.showView(FaceViewTab.EMOJI);
    }

    /**
     * 显示ui
     */
    public showUi() {
        this.danmu_ui.active = true;
        this.danmuController.active();
        this.danmuController.showScrollview();
        this.faceController.updateEmotionNeedCoin();
        this.emojiController.updateConfig();
        // 立刻刷新widget
        cv.resMgr.adaptWidget(this.node, true);
    }

    /**
     *  hideUi
     */
    public hideUi() {
        this.danmu_ui.active = false;
    }

    public isShowing(): boolean {
        return this.danmu_ui.active;
    }

    public setActiveEmoji(enabled: boolean, forceShow: boolean = false) {
        if (enabled) {
            this.enableView(FaceViewTab.EMOJI, forceShow);
        } else {
            this.disableView(FaceViewTab.EMOJI);
            this.showView(FaceViewTab.DANMU);
        }
    }

    private disableView(view: FaceViewTab) {
        let btn: cc.Node = null;
        switch (view) {
            case FaceViewTab.DANMU:
                this.danmuController.hide();
                btn = this.danmu_button;
                break;
            case FaceViewTab.EMOJI:
                this.emojiController.hide();
                btn = this.emoji_button;
                break;
            case FaceViewTab.FACE:
                this.faceController.hide();
                btn = this.face_button;
                break;
        }
        if (cc.isValid(btn)) {
            btn.active = false;
            btn.parent.getComponent(cc.Layout)?.updateLayout();
        }
    }

    private enableView(view: FaceViewTab, forceShow: boolean = true) {
        let btn: cc.Node = null;
        const viewShow: FaceViewTab = forceShow ? view : this.currentView;
        switch (view) {
            case FaceViewTab.DANMU:
                btn = this.danmu_button;
                break;
            case FaceViewTab.EMOJI:
                btn = this.emoji_button;
                break;
            case FaceViewTab.FACE:
                btn = this.face_button;
                break;
        }
        if (cc.isValid(btn)) {
            btn.active = true;
            btn.parent.getComponent(cc.Layout)?.updateLayout();
        }
        this.showView(viewShow);
    }

    private showView(view: FaceViewTab): void {
        switch (view) {
            case FaceViewTab.DANMU:
                this.danmuController.show();
                this.emojiController.hide();
                this.faceController.hide();
                break;
            case FaceViewTab.EMOJI:
                this.danmuController.hide();
                this.emojiController.show();
                this.faceController.hide();
                break;
            case FaceViewTab.FACE:
                this.danmuController.hide();
                this.emojiController.hide();
                this.faceController.show();
                break;
        }
        this.danmu_button_highlight.active = view === FaceViewTab.DANMU;
        this.emoji_button_highlight.active = view === FaceViewTab.EMOJI;
        this.face_button_highlight.active = view === FaceViewTab.FACE;
        this.danmu_button_label.color =
            view === FaceViewTab.DANMU ? this.tabButtonActiveColor : this.tabButtonInactiveColor;
        this.danmu_button_label.getComponent(cc.Label).enableBold = view === FaceViewTab.DANMU;
        this.emoji_button_label.color =
            view === FaceViewTab.EMOJI ? this.tabButtonActiveColor : this.tabButtonInactiveColor;
        this.emoji_button_label.getComponent(cc.Label).enableBold = view === FaceViewTab.EMOJI;
        this.face_button_label.color =
            view === FaceViewTab.FACE ? this.tabButtonActiveColor : this.tabButtonInactiveColor;
        this.face_button_label.getComponent(cc.Label).enableBold = view === FaceViewTab.FACE;
        this.currentView = view;
    }
}
