import cv from '../../lobby/cv';
import { AwardInfo } from './data/JackpotData';

const { ccclass, property } = cc._decorator;

@ccclass
export default class GameJackPotSignItemGroupMars extends cc.Component {
    @property(cc.Label) public marsPlayerText: cc.Label = null;
    // @property(cc.Label) public playerNameText: cc.Label = null;
    @property(cc.Label) public awardText: cc.Label = null;
    @property(cc.Label) public awardRatioText: cc.Label = null;

    protected onLoad(): void {
        this.initLanguage();
    }

    initLanguage(): void {
        this.marsPlayerText.string = cv.config.getStringData('Mars_Player');
    }

    setdata(awardPlayer: AwardInfo) {
        if (!awardPlayer) {
            return;
        }
        const str = `${cv.config.getStringData('Mars_Player')} ${awardPlayer.player_name}`;
        cv.StringTools.setShrinkString(this.marsPlayerText.node, str, true);
        this.awardText.string = awardPlayer.award_amount ? cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(awardPlayer.award_amount)) : '';
        this.awardRatioText.string = awardPlayer.award_ratio ? `${awardPlayer.award_ratio}%`: '';
    }


}
