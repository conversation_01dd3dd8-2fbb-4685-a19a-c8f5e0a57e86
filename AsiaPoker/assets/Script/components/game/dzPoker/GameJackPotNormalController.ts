import cv from '../../lobby/cv';
import GameJackPotBaseController from './GameJackPotBaseController';
import { AwardInfos } from './data/JackpotData';

const { ccclass, property } = cc._decorator;

@ccclass
export default class GameJackPotNormalController extends GameJackPotBaseController {
    @property(cc.Label)
    labelContent: cc.Label = null;

    @property(cc.Label)
    labelWinValue: cc.Label = null;

    public init(info: AwardInfos) {
        const name = info.award_player_name;
        const level = cv.config.getStringData(`UITitle${112 + info.hand_level}`);
        const str =
            cv.config.getStringData('UIJackpotHitCardPlayer') + '\n' + cv.config.getStringData('UIJackpotHitCardType');
        this.labelContent.string = cv.StringTools.formatC(str, name, level);
        this.labelWinValue.string = cv.StringTools.serverGoldToShowString(info.award_amount);
    }

    public playAnim() {
        this.anim.play('JackpotBigWin');
    }
}
