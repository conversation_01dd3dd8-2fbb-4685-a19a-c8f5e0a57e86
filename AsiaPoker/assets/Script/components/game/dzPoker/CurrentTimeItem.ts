import cv from "../../lobby/cv";
import { RemarkData } from "../../../data/userData";
import game_protocol = require("./../../../../Script/common/pb/gs_protocol");
import game_pb = game_protocol.protocol;
import { PlayerInfo } from "./data/RoomData";
import GameDataManager from "./data/GameDataManager";
const { ccclass, property } = cc._decorator;
@ccclass
export class CurrentTimeItem extends cc.Component {

    @property({
        type: cc.Label,
        "tooltip": "Nickname"
    })
    yourname: cc.Label = null;

    @property({
        type: cc.Label,
        "tooltip": "Buy In"
    })
    dairu: cc.Label = null;

    @property({
        type: cc.Label,
        "tooltip": "Win/Lose"
    })
    score: cc.Label = null;

    @property({
        type: cc.Label,
        "tooltip": "HandCount"
    })
    handCount: cc.Label = null;

    @property(cc.Node)
    highlightBG: cc.Node = null;

    readonly playerLeavedHex = "#6B6D71";
    readonly playerInGameHex = "#FFFFFF";
    readonly backgroundHex = "#2784DE";

    setdata(data: game_pb.PlayerBuyinInfo) {
        if (cv.dataHandler.getUserData().user_id == data.playerid.toString()) {
            this.highlightBG.color = this.node.color.fromHEX(this.backgroundHex);
            this.highlightBG.active = true;
        }else{
            this.highlightBG.active = false;
        }
        const rdata: RemarkData = cv.dataHandler.getUserData().getRemarkData(data.playerid);
        const kName = rdata.sRemark !== null && rdata.sRemark.length !== 0 ? rdata.sRemark : data.playername;
        const _handCount: number = data.HandCount || 0;
        this.handCount.string = _handCount.toString();
        this.dairu.string = cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(data.total_buyin));
        cv.StringTools.setShrinkString(this.yourname.node, kName);

        if (data.curr_record > 0) {
            this.score.string = "+" + cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(data.curr_record));
        }
        else {
            this.score.string = cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(data.curr_record));
        }

        let player: PlayerInfo = GameDataManager.tRoomData.GetTablePlayer(data.playerid);
        if (player === null) {
            this.yourname.node.color = this.node.color.fromHEX(this.playerLeavedHex);
            this.dairu.node.color = this.node.color.fromHEX(this.playerLeavedHex);
            this.score.node.color = this.node.color.fromHEX(this.playerLeavedHex);
            this.handCount.node.color = this.node.color.fromHEX(this.playerLeavedHex);
        }
        else {
            this.yourname.node.color = this.node.color.fromHEX(this.playerInGameHex);
            this.dairu.node.color = this.node.color.fromHEX(this.playerInGameHex);
            this.score.node.color = this.node.color.fromHEX(this.playerInGameHex);
            this.handCount.node.color = this.node.color.fromHEX(this.playerInGameHex);
        }
    }
}