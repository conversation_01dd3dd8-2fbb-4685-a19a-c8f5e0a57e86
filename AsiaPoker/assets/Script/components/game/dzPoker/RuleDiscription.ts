/* eslint-disable max-classes-per-file */
import { CurrencyType } from "../../../common/tools/Enum";
import cv from "../../lobby/cv";
import { SafeAreaHelper } from "../../../../default/shared/safe_area_helper/SafeAreaHelper";
import NodeStatusListener from "../../lobby/nodeManager/NodeStatusListener";
import { NodeGroupType } from "../../lobby/nodeManager/NodeStatusCenter";

const { ccclass, property } = cc._decorator;
@ccclass
export class RuleSpritesFrame  {
    private static readonly BASE_PATH: string = "zh_CN/game/ruleDiscription/";

    public static generatePath(rule: string, lang: string): string {
        return `${this.BASE_PATH}${rule}-${lang}`;
    }

    public static loadGameRuleSpriteFrames(
        lang: string, 
        gameId: number, 
        gameMode: number, 
        currencyType: CurrencyType,
        onLoaded?: (key, spriteFrame: cc.SpriteFrame ) => void
    ) {
        const requiredRules = this.getRequiredRules(gameId, gameMode, currencyType).reverse();
        const loadFunc = ()=> {
            if (!requiredRules?.length) {
                return;
            }
            const rule = requiredRules.pop();

            // Based on Kenny's request, we need to load the English version of the WEB_MORE_MODES_RULE sprite frame in the Vientnamese language.
            if(rule === "WEB_MORE_MODES_RULE" && lang === cv.Enum.LANGUAGE_TYPE.yn_TH) 
                lang = cv.Enum.LANGUAGE_TYPE.en_US;
            const path = RuleSpritesFrame.generatePath(rule, lang);
            cc.resources.load(path, cc.SpriteFrame, (err, spriteFrame) => {
                if (err) {
                    cc.error(`Failed to load sprite frame: ${path}`);
                }

                onLoaded?.(rule, spriteFrame as cc.SpriteFrame);
                loadFunc();
            });
        }
        
        loadFunc();
    }

    private static getRequiredRules(gameId: number, gameMode: number, currencyType: CurrencyType): string[] {
        const requiredRules: string[] = [];
        const isUSD = currencyType === CurrencyType.USDT;

        if (gameId === cv.Enum.GameId.Bet) {
            requiredRules.push("WEB_ZOOM_DEZHOU_BET");
        }
        else if (cv.roomManager.checkGameIsZoom(gameId)) {
            if (gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
                requiredRules.push(isUSD ? "WEB_ZOOM_DEZHOU_USD_SHORT_RULE" : "WEB_ZOOM_DEZHOU_SHORT_RULE");
            } else {
                requiredRules.push(isUSD ? "WEB_ZOOM_DEZHOU_USD_RULE" : "WEB_ZOOM_DEZHOU_RULE");
            }
        }
        else if (gameId === cv.Enum.GameId.Plo) {
            requiredRules.push(isUSD ? "WEB_PLO_USD_RULE" : "WEB_PLO_RULE");
            requiredRules.push("WEB_INSURANCE_RULE");
        }
        else {
            requiredRules.push(isUSD ? "WEB_DEZHOU_USD_RULE" : "WEB_DEZHOU_RULE");
            requiredRules.push("WEB_DEZHOU_SHORT_RULE");
            requiredRules.push("WEB_INSURANCE_RULE");
        }

        if (!(gameId >= cv.Enum.GameId.ZoomTexas && gameId <= cv.Enum.GameId.ZoomTexasMax)) {
            requiredRules.push("WEB_MORE_MODES_RULE");
        }

        return requiredRules;
    }
}

@ccclass
export default class RuleDiscription extends cc.Component {
    @property(cc.Node) dzpkBtn: cc.Node = null;
    @property(cc.Node) dpBtn: cc.Node = null;
    @property(cc.Node) bxBtn: cc.Node = null;
    @property(cc.Node) moreModesButton: cc.Node = null;
    @property(cc.ScrollView) ndScrollViews: cc.ScrollView=null;
    @property(cc.Node) ruleDiscription: cc.Node = null;
    @property(cc.Node) safearea: cc.Node = null;
    @property(cc.Node) ruleDiscriptionMedelPanel: cc.Node = null;
    @property(cc.Node) menuList: cc.Node = null;
    @property(cc.Layout) layout: cc.Layout = null;

    private _frames: { [key: string]: cc.SpriteFrame } = {};
    
    _onClose: Function;
    
    onLoad(){
        this._frames = {};
        
        if(!cv.config.isH5onNative()) {
            this._loadAllSpriteFrames();
        }
        cv.resMgr.adaptWidget(this.node, true);
        this.setSafeAreaAndScrollView();
        const nodeStatusListener = this.node.addComponent(NodeStatusListener);
        nodeStatusListener.init([NodeGroupType.H5LiveStreamWebview]);
    }

    onEnable() {      
        this.moreModesButton.active = this.isMoreModesBtnVisible();
        this.menuList.getComponent(cc.Layout).updateLayout();
        cv.MessageCenter.register("on_insurance_showing", this.hide.bind(this), this.node);
        cv.MessageCenter.register("on_peekcard_showing", this.hide.bind(this), this.node);
    }

    protected onDisable(): void {
        cv.MessageCenter.unregister("on_insurance_showing", this.node);
        cv.MessageCenter.unregister("on_peekcard_showing", this.node);
    }

    hide() {
        this.node.active = false;
        this._onClose?.();
    }

    btnCloseClick(evt) 
    {
        if (cv.config.isH5onNative()) {
            this.cleanUpFrames();
        }
        this.show(false);
        cv.AudioMgr.playButtonSound('close');

        if ((cv.native.isIosNative() && !cv.native.IsPad())) {
            cv.StatusView.show(true);
        }    
    }

    private _loadAllSpriteFrames() {
        RuleSpritesFrame.loadGameRuleSpriteFrames(
            cv.config.getCurrentLanguageV2(),
            cv.roomManager.getCurrentGameID(),
            cv.GameDataManager.tRoomData.pkRoomParam.game_mode,
            cv.GameDataManager.tRoomData.pkRoomParam.currencyType,
            (key, spriteFrame) => {
                this._frames[key] = spriteFrame;
            }
        );
    }

    private _loadSpriteFrame(rule: string, callback?: (spriteFrame: cc.SpriteFrame) => void) {
        let lang = cv.config.getCurrentLanguageV2();
        if (rule === "WEB_MORE_MODES_RULE" && lang === cv.Enum.LANGUAGE_TYPE.yn_TH) {
            lang = cv.Enum.LANGUAGE_TYPE.en_US;
        }
        const path = RuleSpritesFrame.generatePath(rule, lang);
        cc.resources.load(path, cc.SpriteFrame, (err, spriteFrame) => {
            if (err) {
                cc.error(`Failed to load sprite frame: ${path}`);
                return;
            }
            if (!this._frames) {
                this._frames = {};
            }
            this._frames[rule] = spriteFrame as cc.SpriteFrame;
            callback?.(spriteFrame as cc.SpriteFrame);
        });
    }

    updateFrame(name: string) {
        const getRuleKey = () => {
            switch (name) {
                case this.dzpkBtn.name:
                    return this.dzpkFrame;
                case this.dpBtn.name:
                    return this.dpFrame;
                case this.bxBtn.name:
                    return "WEB_INSURANCE_RULE";
                case this.moreModesButton.name:
                    return "WEB_MORE_MODES_RULE";
                default:
                    return null;
            }
        };

        const ruleKey = getRuleKey();
        if (!ruleKey) return;

        if(cv.config.isH5onNative()) {
            this.cleanUpFrames();

            this._loadSpriteFrame(ruleKey, (sf) => {
                this.switchRulesImage(sf);
            });
            
        } else {
            this.switchRulesImage(this._frames[ruleKey]);
        }
    }

    get dzpkFrame() {
        const curGameId = cv.roomManager.getCurrentGameID();
        const isUSD = cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.USDT;
        if (curGameId === cv.Enum.GameId.Plo) {
            return isUSD ? "WEB_PLO_USD_RULE" : "WEB_PLO_RULE";
        }
        return isUSD ? "WEB_DEZHOU_USD_RULE" : "WEB_DEZHOU_RULE";
    };

    get dpFrame() {
        const isUSD = cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.USDT;
        const curGameId = cv.roomManager.getCurrentGameID();
        if (cv.roomManager.checkGameIsZoom(curGameId)) {
            // 急速
            if (cv.GameDataManager.tRoomData.pkRoomParam.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short)  // 急速短牌
            {
               return isUSD ? "WEB_ZOOM_DEZHOU_USD_SHORT_RULE" : "WEB_ZOOM_DEZHOU_SHORT_RULE";
            }
            return isUSD ? "WEB_ZOOM_DEZHOU_USD_RULE" : "WEB_ZOOM_DEZHOU_RULE";
        }
        if (curGameId === cv.Enum.GameId.Bet) {
            // 必下
           return "WEB_ZOOM_DEZHOU_BET";
        }  
        if(curGameId === cv.Enum.GameId.Plo) {
            return "WEB_INSURANCE_RULE";
        }
        return  "WEB_DEZHOU_SHORT_RULE";
    };

    switchRulesImage(frame: cc.SpriteFrame) {
        const offset = this.layout.node.width - this.ndScrollViews.node.width;
        this.ndScrollViews.node.x = offset > 0 ? offset / 2 : 0;
        this.ndScrollViews.scrollToTop();
        this.ndScrollViews.content.children[0].getComponent(cc.Sprite).spriteFrame = frame;
    }

    onToggle (event:cc.Event) {
        cv.AudioMgr.playButtonSound('tab');
        this.updateFrame(event.target.name);
    }

    setSafeAreaAndScrollView() {
        cv.resMgr.adaptWidget(this.node, true);
        const offsetY = SafeAreaHelper.getUpperDangerZoneYOffset();
        const scrollviewHight = this.ruleDiscription.height - this.ruleDiscriptionMedelPanel.height - this.menuList.height - offsetY;
        this.safearea.height = offsetY;
        this.ndScrollViews.node.height = scrollviewHight;
       // Update the scrollview's content node WIDGET after the HEIGHTS changed
        cv.resMgr.adaptWidget(this.layout.node, true);
        this.layout.updateLayout();
    }

    show(isView: boolean, onClosed?: Function) {
        this.node.active = isView;
        this.showMailBtn(!isView);
        if (isView) {
            this.showDetailRuleByGameID();
        }
        if (isView) {
            this._onClose = onClosed;
        }
        else{
            this.hide();
        }
    }

    showDetailRuleByGameID() {
        if (cv.config.getCurrentScene() !== cv.Enum.SCENE.GAME_SCENE) return;
        const isUSD = cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.USDT;
        const curGameId = cv.roomManager.getCurrentGameID();
        const dpLabel = this.dpBtn.getComponent(cc.Label);  // 中间按钮的文本
        dpLabel.string = cv.config.getStringData("WordPanel_button_panel_short_button");
        this.dzpkBtn.active = false;
        this.bxBtn.active = false;
        this.dpBtn.active = true;
        this.moreModesButton.active = this.isMoreModesBtnVisible();
       
        let ruleKey: string;

        if (curGameId === cv.Enum.GameId.Bet) {                    
            // 必下
            dpLabel.string = cv.config.getStringData("WordPanel_button_panel_dezhou_betbutton");
            this.dpBtn.getComponent(cc.Toggle).isChecked = true;
            ruleKey = "WEB_ZOOM_DEZHOU_BET";
        }
        else if (cv.roomManager.checkGameIsZoom(curGameId)) {  
            // 急速
            this.dpBtn.getComponent(cc.Toggle).isChecked = true;
            if (cv.GameDataManager.tRoomData.pkRoomParam.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short){
                dpLabel.string = cv.config.getStringData("WordPanel_button_panel_dezhou_zoomShortbutton");
                ruleKey = isUSD ? "WEB_ZOOM_DEZHOU_USD_SHORT_RULE": "WEB_ZOOM_DEZHOU_SHORT_RULE";
            } else {
                dpLabel.string = cv.config.getStringData("WordPanel_button_panel_dezhou_zoombutton");
                ruleKey = isUSD ? "WEB_ZOOM_DEZHOU_USD_RULE" : "WEB_ZOOM_DEZHOU_RULE";
            }
        } 
        else if (curGameId === cv.Enum.GameId.Plo) {
            this.dzpkBtn.active = true;
            this.dzpkBtn.getComponent(cc.Toggle).isChecked = true;
            const dpLabelDzpkBtn = this.dzpkBtn.getComponent(cc.Label);
            dpLabel.string = cv.config.getStringData("WordPanel_button_panel_insurance_button");
            dpLabelDzpkBtn.string = cv.config.getStringData("WordPanel_button_panel_plo_button");
            ruleKey = isUSD ? "WEB_PLO_USD_RULE" : "WEB_PLO_RULE";
        }
        else {
            this.dzpkBtn.active = true;
            this.bxBtn.active = true;
            if (cv.GameDataManager.tRoomData.pkRoomParam.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {  
                this.dpBtn.getComponent(cc.Toggle).isChecked = true;
                ruleKey = "WEB_DEZHOU_SHORT_RULE";
            } else {
                this.dzpkBtn.getComponent(cc.Toggle).isChecked = true;
                ruleKey = isUSD ? "WEB_DEZHOU_USD_RULE": "WEB_DEZHOU_RULE";
            }
        }

        if (this._frames[ruleKey]) {
            this.switchRulesImage(this._frames[ruleKey]);
        } else {
            this._loadSpriteFrame(ruleKey, (sf) => {
                this.switchRulesImage(sf);
            });
        }

        [this.dpBtn, this.bxBtn, this.dzpkBtn, this.moreModesButton].forEach(btn => {
            btn.children[0].width = cv.resMgr.getLabelStringSize(btn.getComponent(cc.Label)).width;
        });
    }

    showMailBtn(isView: boolean) {
        cv.MessageCenter.send(isView ? "show_mail_entrance":"hide_mail_entrance" );
    }

    isMoreModesBtnVisible(): boolean {
        const curGameId = cv.roomManager.getCurrentGameID();
       return !(curGameId >= cv.Enum.GameId.ZoomTexas && curGameId <= cv.Enum.GameId.ZoomTexasMax)        
    }

    private _isH5onNative(): boolean {
        return cc.sys.isBrowser && (cc.sys.os === cc.sys.OS_ANDROID || cc.sys.os === cc.sys.OS_IOS);
    }

    cleanUpFrames() {
        Object.keys(this._frames).forEach(key => {
            if (this._frames[key]) {
                cc.assetManager.releaseAsset(this._frames[key]);
            }
        });
        this._frames = null;
        this._frames = {};
    }
}
