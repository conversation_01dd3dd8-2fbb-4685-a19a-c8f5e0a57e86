import cv from "../../lobby/cv";
import { GameMain } from "./GameMain";
import game_protocol = require("./../../../../Script/common/pb/gs_protocol");
import game_pb = game_protocol.protocol;
import GameTableMessages from "./GameTableMessage";


const { ccclass, property } = cc._decorator;

@ccclass
export default class GameTableInfo extends cc.Component {

    @property(cc.Label) blindValue_text: cc.Label = null;
    @property(cc.Label) pokerName_text: cc.Label = null;
    @property(cc.Label) squid_text: cc.Label = null;
    @property(cc.Prefab) gameMessagePrefab: cc.Prefab = null;
    public tableMsgs: GameTableMessages;

    gameMain: GameMain = null;

    protected onLoad(): void {
        this.blindValue_text.node.active = false;
        this.pokerName_text.node.active = false;
        this.squid_text.node.active=false; 
        this.tableMsgs = cc.instantiate(this.gameMessagePrefab)?.getComponent(GameTableMessages);
        
        this.tableMsgs?.initialise(this.gameMain.game);
    }

    setGameMain(_gameMain: GameMain) {
        this.gameMain = _gameMain;
    }

    getBlindText(): string {
        return this.blindValue_text.string;
    }

    updateTableInfo(isInit: boolean = false) {
        if (!isInit) {
            this.pokerName_text.node.active = !this.gameMain.isZoom();
            this.setRoomData();
        }
        else if (!this.gameMain.isZoom()) {
            this.setTableName();
        }
    }

    private setRoomData() {
        this.setTableName();
        this.blindValue_text.node.active = true;
        let blinds: string;
        let minBuyIn = "";
        const roomParam = cv.GameDataManager.tRoomData.pkRoomParam;

        if (roomParam.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            minBuyIn = "  " + cv.StringTools.formatC(cv.config.getStringData("Minimum_Buy_in_Short_Deck"), cv.roomManager.getMinBuyInAmount());
        }

        const anteNum = cv.StringTools.clientGoldByServer(roomParam.rule_ante_amount);
        if (roomParam.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Normal) {
            blinds = this.getNormalModeBlinds(roomParam, anteNum);
        }
        else if (roomParam.game_mode === cv.Enum.CreateGameMode.CreateGame_Mode_Short) {
            const gameAnti: string = cv.config.getStringData("UIGameAnti");
            blinds = gameAnti.replace("%s", cv.StringTools.numberToShowString(anteNum));
        }

        if (roomParam.club_id !== 0) {
            this.blindValue_text.string = blinds;
        } else {
            const roomIdString = (cv.config.getStringData("UIGameRoomId") as string).replace("%d", cv.GameDataManager.tRoomData.u32RoomId.toString());
            this.blindValue_text.string = `${blinds}${roomIdString}`;
        }

        const usdString = this.gameMain.isUSDTable() ? "$" : "";
        this.blindValue_text.string = this.blindValue_text.string.replace(":", `:${usdString}`).split("/").join(`/${usdString}`);

        this.blindValue_text.string += minBuyIn;
        this.setLablesPosition();
    }

    private getNormalModeBlinds(roomParam: any, anteNum: number): string {
        let blinds = cv.config.getblindString(roomParam.rule_blind_enum - 1);
        const splitArr: string[] = blinds.split("/");
        const bigBlind = Number(splitArr[1]);
        const gameBlind: string = cv.config.getStringData("UIGameBlind");
        blinds = gameBlind.replace("%s", blinds);

        if (roomParam.rule_switch_force_straddle) {
            const cbStraddle = cv.StringTools.times(Number(bigBlind), 2.0);
            blinds = cv.StringTools.formatC("%s/%s", blinds, cbStraddle.toString());
        }
        if (anteNum) {
            blinds += cv.StringTools.formatC("(%f)", cv.StringTools.numberToShowNumber(anteNum));
        }
        return blinds;
    }

    private setLablesPosition() {
        // Set the table name and blind information position
        const pokerName = this.pokerName_text.node;
        const tempPos: cc.Vec2 = this.gameMain.getWorldSpacePos();
        const objPos: cc.Vec2 = cc.Vec2.ZERO;
        pokerName.parent.convertToNodeSpaceAR(tempPos, objPos);
        pokerName.setPosition(pokerName.x, objPos.y - 120);
        this.blindValue_text.node.setPosition(this.blindValue_text.node.x, pokerName.getPosition().y - pokerName.getContentSize().height);
        if(cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Squid){
            this.setSquidText();
        }
       
    }


    private setTableName() {
        const _pkRoomParam = cv.GameDataManager.tRoomData.pkRoomParam;
        let roomName: string = _pkRoomParam.game_name;

        if (this.gameMain.isGameStarSeat()) {
            // Star table
            const roomArray = _pkRoomParam.tableTitle.split('#');
            const currentLanguage = cv.config.getCurrentLanguage();
            switch (currentLanguage) {
                case cv.Enum.LANGUAGE_TYPE.zh_CN:
                    roomName = roomArray[0]; // Chinese
                    break;
                case cv.Enum.LANGUAGE_TYPE.yn_TH:
                    roomName = roomArray[2]; // Vietnamese
                    break;
                case cv.Enum.LANGUAGE_TYPE.en_US:
                default:
                    roomName = roomArray[1]; // English & default 
                    break;
            }
        }

        if (_pkRoomParam.manual_created) {
            this.pokerName_text.string = roomName;
        }
        else {
            const maxPlayer: number = cv.GameDataManager.tRoomData.forceWithdrawMode ? _pkRoomParam.player_count_max : 0
            const name = cv.tools.displayChineseName(roomName, maxPlayer);
            this.pokerName_text.string = name;
            if( cv.GameDataManager.tRoomData.u32GameID !== cv.Enum.GameId.Squid) return;
            if (cv.GameDataManager.tRoomData?.squidHuntInfo_SnapShot?.mode === game_pb.SquidHuntGameMode.MULTIPLIER_MODE) {
                this.pokerName_text.string = cv.config.getStringData("Double_squid_title") + "-" + roomName.slice(-4);
            } else if (
                cv.GameDataManager.tRoomData?.squidHuntInfo_SnapShot?.mode === game_pb.SquidHuntGameMode.NORMAL_MODE
                && cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
                this.pokerName_text.string = cv.config.getStringData("Squid_hunt_title") + "-" + roomName.slice(-4);
            }
        }
    }

    public updateUi(_curBgIndex: number) {

        // Updated table name and blind information text colors
        const color = this._getNameColor(_curBgIndex);
        const opacity = this._getNameAlpha(_curBgIndex);
        [this.pokerName_text, this.blindValue_text,this.squid_text].forEach((text) => {
            text.node.color = color;
            text.node.opacity = opacity;
            text.fontSize = 30;
        });
    }

    private _getNameColor(index: number): cc.Color {
        if (this.gameMain.isGameStarSeat()) {
            // Star table background, room name in black
            return cc.Color.BLACK;
        }
        // For index 0,1,2 , WHITE, else default color BLACK
        return (index >= 0 && index <= 2) ? cc.Color.WHITE : cc.Color.BLACK;
    }

    private _getNameAlpha(index: number): number {
        if (this.gameMain.isGameStarSeat()) {
            // Star table, room name Alpha
            return 127.5;
        }
        // For index 0,1,2 , alpha - 102, else default value 127.5
        return (index >= 0 && index <= 2) ? 102 : 127.5;
    }

    setSquidText(str:string = this.squid_text?.string, isSquidStarted : boolean = true)
    {
        this.squid_text.string=str;
        this.squid_text.lineHeight = isSquidStarted? 38 : 30;
        this.squid_text.node.setPosition(this.blindValue_text.node.x, this.blindValue_text.node.getPosition().y - (isSquidStarted? 15 : 20));
        this.squid_text.node.active=true;
    }

}
