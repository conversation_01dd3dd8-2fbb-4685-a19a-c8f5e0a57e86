import { BaseGameTableMessage } from "./BaseGameTableMessage";
import { GameScene } from "./GameScene";

const { ccclass } = cc._decorator;

@ccclass
export default class GameTableMessages extends BaseGameTableMessage {
    private _gameScene: GameScene;

    initialise(gameScene: GameScene) {
        this._gameScene = gameScene;
        this.node.parent = gameScene.gameMain_panel;
        this.node.setSiblingIndex(this._gameScene.gameMain_panel.childrenCount + 1);
        this.node.active = true;
        this.timerContainer.active = false;
    }

    protected get selfSeatPosInThisNode(): cc.Vec2 {
        const selfSeat = this._gameScene?.gameMain?.seatList?.sort((a, b) => a.node.y - b.node.y)?.[0];
        if (!selfSeat) return null;
        const worldPos = selfSeat.node.parent.convertToWorldSpaceAR(selfSeat.node.position);
        return this.node.convertToNodeSpaceAR(worldPos);
    }

    protected getTimerPosition(): cc.Vec2 {
        const pos = this.selfSeatPosInThisNode;
        if (!pos) return null;
        return new cc.Vec2(pos.x, pos.y + 175);
    }

    protected getMessagePosition(): cc.Vec2 {
        const pos = this.selfSeatPosInThisNode;
        if (!pos) return null;

        const actionBtn = this._gameScene?.gameMain?.actionButtonView;
        const freeFill_button = this._gameScene?.gameMain?.freeFill_button;
        const actionBtnPos = this.node.convertToNodeSpaceAR(
            actionBtn.parent.convertToWorldSpaceAR(actionBtn.position)
        );

        return freeFill_button?.node?.activeInHierarchy
            ? new cc.Vec2(0, actionBtnPos.y + actionBtn.height + 56)
            : new cc.Vec2(pos.x, pos.y + 180);
    }
}
