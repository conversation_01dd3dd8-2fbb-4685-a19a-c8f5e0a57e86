const { ccclass, property } = cc._decorator;

interface JackPotEffectController {
    onAnimationCompleted: () => void;
    playAnim: () => void;
    setOnAnimationCompleted: (func: () => void) => void;
    clearOnAnimationCompletedFunc: () => void;
}

@ccclass
export default class GameJackPotBaseController extends cc.Component implements JackPotEffectController {
    @property(cc.Animation)
    anim: cc.Animation = null;

    onAnimationCompleted: () => void = null;

    public playAnim() {
        throw new Error('GameJackPotBaseController :: playAnim must be defined!');
    }

    public setOnAnimationCompleted(func: () => void) {
        this.onAnimationCompleted = () => {
            func();
            this.clearOnAnimationCompletedFunc();
        };
        this.anim.on(cc.Animation.EventType.FINISHED, this.onAnimationCompleted, this);
    }

    public clearOnAnimationCompletedFunc(): void {
        this.anim.off(cc.Animation.EventType.FINISHED, this.onAnimationCompleted, this);
        this.onAnimationCompleted = null;
    }

    
}
