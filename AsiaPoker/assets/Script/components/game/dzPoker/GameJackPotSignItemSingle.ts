import cv from '../../lobby/cv';
import { AwardInfo } from './data/JackpotData';

const { ccclass, property } = cc._decorator;
@ccclass
export class GameJackPotSignItemSingle extends cc.Component {
    @property(cc.Label) public playerName_text: cc.Label = null;
    @property(cc.Label) public cardTypeName_text: cc.Label = null;
    @property(cc.Label) public award_text: cc.Label = null;
    @property(cc.Label) public day_text: cc.Label = null;
    @property(cc.Label) public time_text: cc.Label = null;

    setdata(awardPlayers: AwardInfo) {
        cv.StringTools.setShrinkString(this.playerName_text.node, awardPlayers.player_name, true);
        this.award_text.string = cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(awardPlayers.award_amount));
        this.cardTypeName_text.string = cv.config.getStringData(`UITitle${112 + awardPlayers.hand_level}`);
        this.day_text.string = cv.StringTools.formatTime(awardPlayers.award_time, cv.Enum.eTimeType.Month_Day, false);
        this.time_text.string = cv.StringTools.formatTime(awardPlayers.award_time, cv.Enum.eTimeType.Hour_Minute, false);
    }
}
