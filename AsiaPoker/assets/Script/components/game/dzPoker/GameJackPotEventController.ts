import cv from '../../lobby/cv';
import GameJackPotFallingMarsController from './GameJackPotFallingMarsController';
import GameJackPotNormalController from './GameJackPotNormalController';
import { AwardInfos } from './data/JackpotData';

const { ccclass, property } = cc._decorator;

@ccclass
export default class GameJackPotEventController extends cc.Component {
    @property(cc.Prefab)
    public normalJackpotEffectPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    public fallingMarsJackpotEffectPrefab: cc.Prefab = null;

    @property(cc.Camera)
    public mainCamera: cc.Camera = null;

    private _jackpotInfos: AwardInfos[] = [];
    private _jackPotEffectNode: cc.Node = null;
    private _jackPotAnim: cc.Animation = null;

    private _moveAction: (
        pkStart: cc.Node,
        pkTarget: cc.Node,
        f32MoveDelay: number,
        callBack?: cc.ActionInstant
    ) => void = null;

    private _showPlayerJackpotAction: () => void = null;
    private _showJackpotActionFunc: () => void = null;

    // move chip to pool related nodes
    private _mainPool: cc.Node = null;
    private _jackPotPanel: cc.Node = null;

    public init() {}

    public addEvent() {
        cv.MessageCenter.register('show_hit_jackPotCardType', this._showHitJackPotCardType.bind(this), this.node);
    }

    public setChipMoveAction(func: any, from: cc.Node, target: cc.Node) {
        this._moveAction = func;
        this._jackPotPanel = from;
        this._mainPool = target;
    }

    public setShowPlayerJackpotAction(func: () => void) {
        this._showPlayerJackpotAction = func;
    }

    public setShowJackpotActionFunc(func: () => void) {
        this._showJackpotActionFunc = func;
    }

    public resetJackpot(): void {
        if (this._jackPotEffectNode) {
            this._jackPotAnim.stop();
            this._jackPotEffectNode.removeFromParent(true);
            this._jackPotEffectNode.destroy();
            this._jackPotEffectNode = null;
        }

        this._jackpotInfos = [];
    }

    protected onDestroy(): void {
        cv.MessageCenter.unregister('show_hit_jackPotCardType', this.node);
    }

    private _showJackpotAction(): boolean {
        if (this._jackpotInfos.length === 0 || this._jackPotEffectNode !== null) {
            return;
        }

        if (this._jackpotInfos.length === 1) {
            this._showNormalJackPotEffect();
        } else {
            this._showFallingMarsJackPotEffect();
        }
    }

    private _showNormalJackPotEffect() {
        let info = this._jackpotInfos[0];
        let pkPlayer = cv.GameDataManager.tRoomData.GetTablePlayer(info.award_playid);

        if (pkPlayer) {
            this._createJackPotEffect(this.normalJackpotEffectPrefab);

            const jackPotNormal = this._jackPotEffectNode.getComponent(GameJackPotNormalController);
            this._jackPotAnim = jackPotNormal.anim;
            jackPotNormal.init(info);
            jackPotNormal.playAnim();
            jackPotNormal.setOnAnimationCompleted(this._onJackpotAnimationComplete.bind(this));
            this._showJackpotActionFunc();
        }
    }

    private _showFallingMarsJackPotEffect() {
        let info = this._jackpotInfos;
        this._createJackPotEffect(this.fallingMarsJackpotEffectPrefab);

        const jackPotFallingMars = this._jackPotEffectNode.getComponent(GameJackPotFallingMarsController);
        this._jackPotAnim = jackPotFallingMars.anim;

        jackPotFallingMars.init(info);
        jackPotFallingMars.setupMainCameraAnim(this.mainCamera);
        jackPotFallingMars.playAnim();
        jackPotFallingMars.setOnAnimationCompleted(this._onJackpotAnimationComplete.bind(this));
        this._showJackpotActionFunc();
    }

    private _showHitJackPotCardType() {
        let infos = cv.GameDataManager.tJackPot.noticeJackPotAwardInfo.awardInfo;
        cv.StringTools.deepCopy(infos, this._jackpotInfos);
        let pkCall = cc.callFunc(this._showJackpotAction.bind(this));
        this._moveAction(this._jackPotPanel, this._mainPool, 0, pkCall);
    }

    private _createJackPotEffect(jackPotEffectPrefab: cc.Prefab) {
        this._jackPotEffectNode = cc.instantiate(jackPotEffectPrefab);
        this.node.addChild(this._jackPotEffectNode);
    }

    private _onJackpotAnimationComplete() {
        if (this._jackPotEffectNode === null) return;

        this.resetJackpot();
        this._showPlayerJackpotAction();
    }

}

/**
 * sample normal jackpot data 
 * "awardInfo":[
      {
         "award_playid":41315,
         "award_amount":1000,
         "hand_level":10,
         "award_player_name":"asdf",
         "type":0 # refer to normal jackpot
      }
   ]
 
 * sample falling mars jackpot data
 * "awardInfo":[
      {
         "award_playid":41316,
         "award_amount":1000,
         "hand_level":10,
         "award_player_name":"asdf",
         "type":1 # earth player
      },
       {
         "award_playid":41308,
         "award_amount":1000,
         "hand_level":10,
         "award_player_name":"asdf",
         "type":2 # mars player
      }
   ]
 */
