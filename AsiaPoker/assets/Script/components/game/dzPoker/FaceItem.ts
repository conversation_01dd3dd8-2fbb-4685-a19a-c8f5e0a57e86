// Learn TypeScript:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html
import cv from "./../../lobby/cv"
const { ccclass, property } = cc._decorator;

@ccclass
export class FaceItem extends cc.Component {
    _index: number = 0;
    canClick: boolean = true;

    @property(cc.Node)
    costRoot: cc.Node = null;

    @property(cc.Node) number_text = null;
    @property(cc.Node) image_GoldCoin = null;
    @property(cc.Node) image_USD = null;
    @property(cc.Node) effect_img = null;

    onLoad() {
        cv.MessageCenter.register("FaceItem_canSendFace", this.setClick.bind(this), this.node);
    }

    onDestroy() {
        cv.MessageCenter.unregister("FaceItem_canSendFace", this.node);
    }

    setData(index: number, num: number, isFreeEmoji: boolean,isUSDTable? : boolean) {
        this._index = index;
        // const number_text = cc.find("number_text", this.costRoot);
        // const image_GoldCoin = cc.find("image_Gold", this.costRoot);
        // const image_USD = cc.find("image_USD", this.costRoot);
        // const effect_img = cc.find("effect_img", this.node);
        this.image_GoldCoin.active = (false);
        this.image_USD.active = (false);

        this.showCostLabel(!isFreeEmoji);

        if (num <= 0) {
            this.number_text.active = (false);
            this.effect_img.position.y = 78;
        }
        else {
            this.number_text.getComponent(cc.Label).string = (cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(num)));
            this.number_text.active = (true);
            if(isUSDTable) this.image_USD.active = (true);
            else this.image_GoldCoin.active = (true);
            this.effect_img.position.y = 94;
        }
        cv.resMgr.setSpriteFrame(this.effect_img,"zh_CN/game/dzpoker/animation/icon/item_" + index);
    }

    setClick(canClick: boolean) {
        this.canClick = canClick;
    }

    onBtnItemClick() {
        if (!this.canClick) return;
        cv.MessageCenter.send("effet_call", this._index);
    }

    public showCostLabel(isShow: boolean) {
        this.costRoot.active = (isShow);
    }
}
