import cv from '../../lobby/cv';
import { AwardInfo, AwardPlayerJackpotType } from './data/JackpotData';
import GameJackPotSignItemGroupMars from './GameJackPotSignItemGroupMars';
import GameJackPotSignItemGroupEarth from './GameJackPotSignItemGroupEarth';

const { ccclass, property } = cc._decorator;
@ccclass
export class GameJackPotSignItemGroup extends cc.Component {
    @property(cc.Label) public fallingMarsTextTitle: cc.Label = null;
    @property(cc.Label) public cardTypeNameText: cc.Label = null;
    @property(cc.Label) public dayText: cc.Label = null;
    @property(cc.Label) public timeText: cc.Label = null;

    @property(GameJackPotSignItemGroupMars) gameJackPotSignItemGroupEarthScript: GameJackPotSignItemGroupMars = null;
    @property([GameJackPotSignItemGroupEarth]) gameJackPotSignItemGroupMarsScripts: GameJackPotSignItemGroupEarth[] = [];

    onLoad() {
        this.initLanguage();
    }

    initLanguage() {
        this.fallingMarsTextTitle.string = cv.config.getStringData('Falling_Mars_Jackpot');
    }

    setdata(awardPlayers: AwardInfo[]) {
        this.gameJackPotSignItemGroupMarsScripts.forEach((e) => {
            e.node.active = false;
        });

        const marsAwards = awardPlayers.filter((e) => e.type === AwardPlayerJackpotType.Mars);
        const earthAwards = awardPlayers.filter((e) => e.type === AwardPlayerJackpotType.Earth);


        // toggle earth players item
        for (let i = 0; i < earthAwards.length; i++) {
            this.gameJackPotSignItemGroupMarsScripts[i].node.active = true;
            this.gameJackPotSignItemGroupMarsScripts[i].setdata(earthAwards[i]);
        }

        if (marsAwards.length === 0) {
            this.gameJackPotSignItemGroupEarthScript.node.active = false;
            return;
        }
        
        this.gameJackPotSignItemGroupEarthScript.setdata(marsAwards[0]);
        this.cardTypeNameText.string = cv.config.getStringData(`UITitle${112 + marsAwards[0].hand_level}`);
        this.dayText.string = cv.StringTools.formatTime(marsAwards[0].award_time, cv.Enum.eTimeType.Month_Day, false);
        this.timeText.string = cv.StringTools.formatTime(
            marsAwards[0].award_time,
            cv.Enum.eTimeType.Hour_Minute,
            false
        );
    }
}
