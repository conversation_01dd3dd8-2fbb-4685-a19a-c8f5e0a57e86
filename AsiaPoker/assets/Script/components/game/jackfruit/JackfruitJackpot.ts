import cv from "../../lobby/cv";
import JackfruitManager from "./JackfruitManager";
import { GameJackPotSignItem } from '../dzPoker/GameJackPotSignItem';
import BaseJackpot from "../../../common/prefab/BaseJackpot";

const { ccclass } = cc._decorator;

@ccclass
export default class JackfruitJackpot extends BaseJackpot {
    protected onLoad() {
        cv.MessageCenter.register("currentRoomJackpot", this._updateJackpotPanel.bind(this), this.node);
        cv.MessageCenter.register("updata_jackpotReward", this._updateRewardPanel.bind(this), this.node);
    }

    protected onDestroy() {
        cv.MessageCenter.unregister("currentRoomJackpot", this.node);
        cv.MessageCenter.unregister("updata_jackpotReward", this.node);
    }

    protected _updateJackpotPanel() {
        const data = JackfruitManager.tRoomData.jackpotDataInfo;
        this._setCardProportion(0, cv.StringTools.div(data.huangTongPer, 100));
        this._setCardProportion(1, cv.StringTools.div(data.tongHuaShunPer, 100));
        this._setCardProportion(2, cv.StringTools.div(data.siTiaoPer, 100));

        const num = JackfruitManager.tRoomData.jackpotLeftAmount;
        const amount = Math.round(cv.StringTools.serverGoldToShowNumber(num));
        this.jackpot_num_label.string = this._getAmounts(amount);

        const boundaryScore = cv.StringTools.numToFloatString(data.boundaryScore);
        const contrScore = cv.StringTools.numToFloatString(data.contrScore);
        this.jackpot_des_label.string = cv.StringTools.formatC(cv.config.getStringData("jackfruit_JackpotDetail"), boundaryScore, contrScore, contrScore);
    }

    protected _updateRewardPanel() {
        const jackpotRecords = JackfruitManager.tRoomData.JackpotRecords;
        if (jackpotRecords.length <= 0) return;

        const itemCount = jackpotRecords.length;
        this.potSignScrollViewItemPool.init({ itemCount });

        const signItems = this.potSignScrollViewItemPool.getItems();
        if (!signItems) {
            console.error('JackPot :: _updateJackpotSignRecord : sign items is empty!');
            return;
        }

        signItems.forEach((item, index) => {
            const SignItem = item.getComponent(GameJackPotSignItem);
            if (SignItem) {
                const info = jackpotRecords[index];
                SignItem.setSingleJackFruitData(info);
            } else {
                console.error(`Failed to get GameJackPotSignItem script from item ${index}.`);
            }
        });
    }

    protected _updateLuckyProfile() {
        const luckyOne = JackfruitManager.tRoomData.luckyOne;
        const amount = cv.StringTools.numToFloatString(luckyOne.awardAmount);
        this.jackPotInfo_text.node.active = false;
        cv.StringTools.setShrinkString(this.bigWinnerName_text.node, luckyOne.playerName, true);
        this.bigWinnerCard_type_text.string = cv.config.getStringData(`M_UITitle${112 + luckyOne.level}`);
        this.bigWinnerNumber_text.string = amount;
        this.bigWinnerTime_text.string = luckyOne.awardTime === 0 ? "" : cv.StringTools.formatTime(luckyOne.awardTime, cv.Enum.eTimeType.Month_Day, false);
        this.jackpot_blind_text.string = this._getAnte().toString();
        if (luckyOne === null) {
            this.bigWinnerName_text.node.active = false;
            this.bigWinnerNumber_text.node.active = false;
            this.bigWinnerCard_type_text.node.active = false;
            return;
        }

        if (luckyOne.avatar) {
            const url = cv.dataHandler.getUserData().getImageUrlByPlat(luckyOne.avatar, luckyOne.platform);
            const texture: cc.Texture2D = cv.resMgr.get(url, cc.Texture2D);
            if (texture) {
                this.luckDogAvatarSprite.spriteFrame = new cc.SpriteFrame(texture);
            }
            else {
                cv.resMgr.loadRemote(url, (error: Error, tex: cc.Texture2D): void => {
                    if (error) {
                        console.log(error.message || error);
                        return;
                    }
                    this.luckDogAvatarSprite.spriteFrame = new cc.SpriteFrame(tex);
                });
            }
        }
    }

    protected _initLanguage() {
        super._initLanguage();
        this.reward_des_title.string = cv.StringTools.formatC(cv.config.getStringData("jackfruit_Jackpot_reward_des_title"), this._getAnte().toString());
    }

    protected _getAnte(): number {
        const ante = cv.StringTools.serverGoldToShowNumber(JackfruitManager.tRoomData.param.ante);
        return ante;
    }
}
