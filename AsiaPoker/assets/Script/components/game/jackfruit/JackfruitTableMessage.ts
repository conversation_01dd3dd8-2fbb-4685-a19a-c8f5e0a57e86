// import { BaseGameTableMessage } from "./BaseGameTableMessage";
import { BaseGameTableMessage } from "../dzPoker/BaseGameTableMessage";
import { JackfruitMain } from "./JackfruitMain";
import { JackfruitScene } from "./JackfruitScene";

const { ccclass } = cc._decorator;

@ccclass
export default class JackfruitTableMessage extends BaseGameTableMessage {
    private _gameScene: JackfruitScene;

    initialise(gameScene: JackfruitScene) {
        this._gameScene = gameScene;
        this.node.parent = gameScene.gameMain_panel;
        this.node.setSiblingIndex(this._gameScene.gameMain_panel.childrenCount + 1);
        this.node.active = true;
        this.timerContainer.active = false;
    }

    protected getTimerPosition(): cc.Vec2 {
        const jfMain = this._gameScene.gameMain_panel.getComponent(JackfruitMain);
        const card_panel = jfMain.card_panel;
        card_panel.active = true; // remove
        const centerCards = card_panel.children.filter((child) => child.name === "3");

        if (centerCards.length === 0) return new cc.Vec2(0, -this.node.parent.height * 0.2);

        centerCards.sort((x, y) => x.y - y.y);
        return this.node.convertToNodeSpaceAR(
            card_panel.convertToWorldSpaceAR(
                new cc.Vec2(centerCards[0].position.x, centerCards[0].position.y)
            )
        );
    }

    protected getMessagePosition(): cc.Vec2 {
        return new cc.Vec2(0, this.getTimerPosition().y);
    }
}
