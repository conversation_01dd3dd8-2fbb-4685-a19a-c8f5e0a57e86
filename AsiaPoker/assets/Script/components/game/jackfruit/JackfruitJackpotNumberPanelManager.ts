import cv from '../../lobby/cv';
import JackfruitManager from './JackfruitManager';
import BaseJackpotNumberPanelManager from '../../../common/prefab/BaseJackPotNumberPanelManager';

const { ccclass } = cc._decorator;

@ccclass
export default class JackFruitJackpotNumberPanelManager extends BaseJackpotNumberPanelManager {
    public sendNetJackPot() {
        cv.jackfruitNet.requestJackpotData(JackfruitManager.tRoomData.param.ante);
        cv.jackfruitNet.requestJackpotAwardList(JackfruitManager.tRoomData.param.ante);
    }

    protected _updateJackpotNumEvent() {
        if (JackfruitManager.tRoomData.jackpotLeftAmount === -1) return;
        const num = JackfruitManager.tRoomData.jackpotLeftAmount;
        const amount = Math.round(cv.StringTools.serverGoldToShowNumber(num));
        this._updateJackpotNum(amount);
    }

    protected _hasJackpotEnabled(): boolean {
        return JackfruitManager.tRoomData.jackpotLeftAmount !== -1;
    }
}
