import cv from '../../lobby/cv';

const { ccclass, property } = cc._decorator;

@ccclass
export class JackfruitGameRule extends cc.Component {
    @property(cc.Node) title_text: cc.Node = null;

    @property(cc.ScrollView) scrollview: cc.ScrollView = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) titlePanel: cc.Node = null;

    @property(cc.Node) viewPanel: cc.Node = null;

    @property(cc.Prefab) jfRuleBlock: cc.Prefab = null;

    onLoad() {
        cv.resMgr.adaptWidget(this.node, true);

        cv.resMgr.setSpriteFrame(this.title_text, cv.config.getLanguagePath('game/jackfruit/gamerule/game_rule_title'));

        for (let i = 0; i < 5; i++) {

           this._addScrollviewRuleNode( i + 1 + '.', cv.config.getStringData('jackfruit_rule_label_' + i));

            if (i < 4) {
                let img = new cc.Node();
                img.addComponent(cc.Sprite);
                cv.resMgr.setSpriteFrame(img, cv.config.getLanguagePath('game/jackfruit/gamerule/game_rule_' + i));
                this.scrollview.content.addChild(img);
            }

            if (i == 2) {
                let des: string =
                    cv.config.getStringData('jackfruit_rule_label_' + i + '_Child_0') +
                    '\n' +
                    cv.config.getStringData('jackfruit_rule_label_' + i + '_Child_1');

               this._addScrollviewRuleNode( '', des, 230);

            } else if (i == 3) {
                this._addScrollviewRuleNode( '',  cv.config.getStringData('jackfruit_rule_label_' + i + '_Child'), 230);
            }
        }
    }

    start(){
        this.setSafeAreaAndScrollView();

    }

    private _addScrollviewRuleNode( numStr: string = '', des: string, desOpacity: number = 127) {
        let rNode = cc.instantiate(this.jfRuleBlock);
        this.scrollview.content.addChild(rNode);
        cv.resMgr.adaptWidget(rNode, true);
        rNode.getChildByName('ruleNum').getComponent(cc.Label).string = numStr;
        let releDesNode = rNode.getChildByName('ruleDes');
        releDesNode.getComponent(cc.Label).string = des;
        releDesNode.opacity = desOpacity;
        releDesNode.getComponent(cc.Label)._forceUpdateRenderData();
        rNode.height += releDesNode.height;
    }

    onBtnClose(event, str: string) {
        if (str == 'btn') {
            cv.AudioMgr.playButtonSound('close');
        }

        this.node.active = false;
    }

    show() {
        this.node.active = true;

        this.scrollview.content.setPosition(cc.v2(0, 0));
    }

    setSafeAreaAndScrollView() {
        let offsetY = cv.SafeAreaWithDifferentDevices.getSafeArea();

        this.safearea.height = offsetY;

        let ScrollViewHeight = this.node.height - offsetY - this.titlePanel.height;

        // this.viewPanel.height = ScrollViewHeight;
        this.viewPanel.getComponent(cc.Widget).top = ScrollViewHeight;
        this.scrollview.content.height = this.scrollview.content.height + 50;

        this.layout.updateLayout();
    }
}
