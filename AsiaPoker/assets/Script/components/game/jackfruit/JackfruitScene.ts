import cv from '../../lobby/cv';
import { JackfruitMain } from './JackfruitMain';
import { JackfruitMenu } from './JackfruitMenu';
import { JackfruitReview } from './JackfruitReview';
import { PushNoticeType } from '../../../common/prefab/PushNotice';
import FaceView from '../dzPoker/danmu/FaceView';
import { SliderVerify } from '../../lobby/sliderVerify/SliderVerify';
import JackFruitJackpotNumberPanelManager from './JackfruitJackpotNumberPanelManager';
import EmojiLoader from '../../../../emoji/script/EmojiLoader';
import FriendLinesLoader, { Tabletype } from '../../../common/friendlines/FriendLinesLoader';

const { ccclass, property } = cc._decorator;

@ccclass
export class JackfruitScene extends cc.Component {
    @property(JackFruitJackpotNumberPanelManager) jackPotNumberPanelManager: JackFruitJackpotNumberPanelManager = null;
    @property(cc.Prefab) face_Panel_prefab: cc.Prefab = null;
    @property(cc.Node) emoji_fullscreen: cc.Node = null;
    @property(cc.Prefab) menu_Panel_prefab: cc.Prefab = null;

    // 友好度升级动画
    @property(FriendLinesLoader) friendLinesLoader: FriendLinesLoader = null;
    @property(cc.Node) gameMain_panel: cc.Node = null;
    @property(cc.Prefab) jackfruit_gamerule: cc.Prefab = null;
    @property(cc.Prefab) jackfruit_setting: cc.Prefab = null;
    @property(cc.Prefab) jackfruit_card_score: cc.Prefab = null;
    @property(cc.Prefab) currentTime_prefab: cc.Prefab = null;
    @property(cc.Prefab) review_prefab: cc.Prefab = null;
    @property(cc.Prefab) jackfruit_tips_prefab: cc.Prefab = null;
    @property(cc.Prefab) sliderVerify_prefab: cc.Prefab = null;
    @property(cc.Node) voice_panel: cc.Node = null;
    @property(cc.Sprite) recordComplete_img: cc.Sprite = null;
    @property(cc.Sprite) recording_img: cc.Sprite = null;
    @property(cc.Label) recordTxt: cc.Label = null;
    @property(cc.Label) recordTimeTxt: cc.Label = null;
    face_Panel: cc.Node = null;
    menu_Panel: cc.Node = null;
    gamerule_Panel: cc.Node = null;
    setting_Panel: cc.Node = null;
    card_score_Panel: cc.Node = null;
    currentTime_panel: cc.Node = null;
    review_panel: cc.Node = null;
    jackfruit_tips_panel: cc.Node = null;
    sliderVerify_panel: SliderVerify = null;
    private _time: number = 0;
    private _tipsTime: number = 0;

    protected onLoad() {
        cv.config.setCurrentScene(cv.Enum.SCENE.JACKFRUIT_SCENE);
        cv.config.adaptScreen(this.node);
        cv.resMgr.adaptWidget(this.node, true);
        this.menu_Panel = cc.instantiate(this.menu_Panel_prefab);
        this.node.addChild(this.menu_Panel);
        this.menu_Panel.active = false;
        this.friendLinesLoader.init(this.gameMain_panel.getComponent(JackfruitMain), Tabletype.JackFruit);
        this.setting_Panel = cc.instantiate(this.jackfruit_setting);
        this.node.addChild(this.setting_Panel);
        this.setting_Panel.active = false;
        this.jackPotNumberPanelManager.init(this.node);
        this.face_Panel = cc.instantiate(this.face_Panel_prefab);
        this.node.addChild(this.face_Panel, cv.Enum.ZORDER_TYPE.ZORDER_7);
        const faceView: FaceView = this.face_Panel.getComponent(FaceView);
        faceView?.danmuController.setParentNode(cc.find('chat_panel', this.gameMain_panel));
        if (cv.config.IS_FULLSCREEN) {
            // 大屏5轨8条
            faceView?.danmuController.setDanmuChanel([649, 449, -50, -258, -456]);
            faceView?.danmuController.adjustDanmuMaxNumber(8);
        } else {
            // 小屏3轨5条
            faceView?.danmuController.setDanmuChanel([419, 273, -165]);
            faceView?.danmuController.adjustDanmuMaxNumber(5);
        }

        faceView?.setGameScene(this);
        faceView?.emojiController.setupEmoji(this.emoji_fullscreen);
        this.gamerule_Panel = cv.action.addChildToScene(
            this,
            this.jackfruit_gamerule,
            [],
            cv.Enum.ZORDER_TYPE.ZORDER_TOP,
            true
        );
        this.gamerule_Panel.active = false;
        this.card_score_Panel = cv.action.addChildToScene(
            this,
            this.jackfruit_card_score,
            [],
            cv.Enum.ZORDER_TYPE.ZORDER_TOP,
            true
        );
        this.card_score_Panel.active = false;
        this.currentTime_panel = cv.action.addChildToScene(
            this,
            this.currentTime_prefab,
            [],
            cv.Enum.ZORDER_TYPE.ZORDER_6,
            true
        );
        this.currentTime_panel.active = false;
        this.sliderVerify_panel = SliderVerify.initSingleInst(
            this.sliderVerify_prefab,
            this.node,
            cv.Enum.ZORDER_TYPE.ZORDER_TOP
        );
        this.review_panel = JackfruitReview.getSinglePrefabInst(this.review_prefab);
        this.jackfruit_tips_panel = cc.instantiate(this.jackfruit_tips_prefab);
        this.jackfruit_tips_panel.active = false;
        this.node.addChild(this.jackfruit_tips_panel, cv.Enum.ZORDER_TYPE.ZORDER_LOG);
        this._adaptiveExpand();
        this.menu_Panel.getComponent(JackfruitMenu).setGameScene(this);
        this.gameMain_panel.getComponent(JackfruitMain).setGameScene(this);
        this.jackPotNumberPanelManager.setSafeArea(this.node);
        EmojiLoader.instance.initialize();

        cv.worldNet.requestBackpack();
    }

    protected start() {
        if (cv.GameDataManager.bIsAuthMicphone === false) {
            cv.native.AuthMicphone();
            cv.GameDataManager.bIsAuthMicphone = true;
        }
        // 设置跑马灯类型
        cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_JACKFRUIT);
        this._initLanguage();
        this._time = new Date().getTime();
        this._tipsTime = 0;
    }

    protected onDestroy() {
        cv.gameNet.stopVoice();
        cv.GameDataManager.tRoomData.resetVoice();
        this._time = 0;
        this._tipsTime = 0;
        cv.MessageCenter.unregister('on_intimacy', this.node);
    }

    protected update() {
        if (this._time === 0) return;
        const time = new Date().getTime();
        const num = cv.StringTools.minus(time, this._time) / 3600000;
        const integer = Math.floor(num);
        if (integer > this._tipsTime) {
            this._tipsTime = integer;
            let str = '';
            if (integer > 5) {
                str = cv.config.getStringData('jackfruit_tips_protect_eyes_label_1');
            } else {
                str = cv.StringTools.formatC(cv.config.getStringData('jackfruit_tips_protect_eyes_label_0'), integer);
            }
            const label = cc.find('bg/label', this.jackfruit_tips_panel).getComponent(cc.Label);
            label.string = str;
            this._showTips();
        }
    }

    private _showTips() {
        this.jackfruit_tips_panel.active = true;
        this.jackfruit_tips_panel.scale = 0;
        this.jackfruit_tips_panel.runAction(cc.sequence(cc.scaleTo(0.2, 1), cc.delayTime(3), cc.scaleTo(0.2, 0)));
    }

    private _initLanguage() {
        cv.StringTools.setLabelString(
            this.gameMain_panel,
            'game_end_panel/back_btn/back_label',
            'jackfruit_back_btn_label'
        );
        this.recordTxt.string = cv.config.getStringData('GameScene_voice_panel_cdTime_recording_img_record_txt');
        this.recordTimeTxt.string = cv.config.getStringData(
            'GameScene_voice_panel_cdTime_recording_img_recordTime_txt'
        );
    }

    private _adaptiveExpand() {
        if (cv.config.IS_FULLSCREEN) {
            const menu_layout = cc.find('menu_layout', this.menu_Panel).getComponent(cc.Layout);
            menu_layout.paddingTop = menu_layout.paddingTop + cc.sys.getSafeAreaRect().y;
        }
    }
}
