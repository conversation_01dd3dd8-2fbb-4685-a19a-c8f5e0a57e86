import { length } from './../../../../../engine/engine_siyu/utils/api/creator.d';
// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import * as pf from '../../../poker-framework/scripts/pf'
import { pfAdapter } from "../../common/tools/poker-framework-apatper";
import cv from "../lobby/cv";
import { ENV_MODE } from 'assets/Script/common/tools/Config';

const { ccclass, property } = cc._decorator;


@ccclass
export class BundleUpdateControl extends cc.Component {
    @property(cc.Boolean)
    useRemoteManifest = true;

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start() {
        cv.MessageCenter.register("HideWebview_ShowWindows", pfAdapter.hideWebview.bind(pfAdapter), this);

        if (pf.system.isNative) {
            cc.log('WritablePath: ' + jsb.fileUtils.getWritablePath());
        }
    }

    async updateBundleManifest(data: any) : Promise<void> {
        const json = await this.getDefaultManifest();
        pf.updateManager.setDefaultManifest(json);

        pf.updateManager.loadLocalManifest();
        cc.log('local bundle manifest remoteManifestUrl', pf.updateManager.localManifest.remoteManifestUrl);
                     
        if(data && (cv.config.GET_DEBUG_MODE() == ENV_MODE.PROD || this.useRemoteManifest)) {
            // overwirtie back office remote bundle manifest url to local manifest
            const url = this.getRemoteManifestUrl(data);
            
            if(url && url.length > 0 && pf.updateManager.localManifest.remoteManifestUrl !== url) {
                pf.updateManager.localManifest.remoteManifestUrl = url;
                pf.updateManager.saveLocalManifest();
            }

            cc.log('over wirtie back office remote bundle manifest url', pf.updateManager.localManifest.remoteManifestUrl);
        }
                
        await pf.updateManager.loadRemoteBundleManifest();

        if (pf.compareVersions(pf.updateManager.localManifest.version, pf.macros.FRAMEWORK_VERSION) < 0) {
            console.warn(
                `bundle framework version: ${pf.updateManager.localManifest.version} is lower than current framework version: ${pf.macros.FRAMEWORK_VERSION}}`
            );
        }
    }

    checkUpdate(): void {
        pf.updateManager.checkUpdate();
    }

    private async getDefaultManifest(): Promise<any> {                
        let path = 'bundle/h5-bundle'
        
        if(!pf.system.isBrowser) {
            if (pf.system.isAndroid) {
                path = 'bundle/android-bundle';
            } else if (pf.system.isIOS) {
                path = 'bundle/ios-bundle';
            } 
        }

        const asset = await pf.bundleManager.loadAsset<cc.JsonAsset>(cc.assetManager.resources, path);
        return asset.json
    }

    private getRemoteManifestUrl(data: any) {
        if(pf.system.isNative) { 
            if(pf.system.isAndroid) {
                return data["android"]
            }
            if(pf.system.isIOS) {
                return data["ios"]
            }
        } 
        
        return data["h5"]; 
    }

    // update (dt) {}
}
