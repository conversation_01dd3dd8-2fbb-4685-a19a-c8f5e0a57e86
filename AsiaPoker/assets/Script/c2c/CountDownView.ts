// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import cv from "../components/lobby/cv";

const {ccclass, property} = cc._decorator;

@ccclass
export default class CountDownView extends cc.Component {

    @property(cc.Label)
    remainTimeLabel: cc.Label = null;

    @property(cc.Node)
    countingNode: cc.Node = null;

    @property(cc.Node)
    countingCompleteNode: cc.Node = null;

    private _remainTime: number = 0;
    private _completeCb: Function = null;


    protected onLoad(): void {
        cv.MessageCenter.register("c2c_reset_countDown", this.onResetCountDown.bind(this), this.node);   
    }

    protected onEnable(): void {
      //  this.reset();
    }

    protected onDisable(): void {
    //    this.reset();
    }

    protected onDestroy(): void {
        this.reset();
        cv.MessageCenter.unregister("c2c_reset_countDown", this.node);  
    }

    reset() {
        this.unschedule(this.tick);
        this._completeCb = null;
        this._remainTime = 0;
    }

    private onResetCountDown(remainTime:number) {
        this._remainTime = remainTime;
        this._updateRemain();
    }

    public setData(remainTime:number,  isWithdraw: boolean, completeCb: Function, isInPopup:boolean = false ) {
        isWithdraw = true
        this.reset();

        if(this.countingNode) this.countingNode.active = isWithdraw;
        if(this.countingCompleteNode) this.countingCompleteNode.active = !isWithdraw;
        this._completeCb = completeCb;

        if(isWithdraw) {
            if(remainTime > 0) {
                this._remainTime = remainTime;
                this._updateRemain();
                this.schedule(this.tick, 1);
            }else {
                if(isInPopup) {
                    this._remainTime = 0;
                    this._updateRemain();
                } else {
                    if(this.countingNode) this.countingNode.active = false;
                    if(this.countingCompleteNode) this.countingCompleteNode.active = true;
                }
            }
        }
    }

    private _getRemainedTimeString(sec:number):string {
        let minute:number = Math.floor(sec / 60);
        let second:number = sec % 60;
        let minStr:string = minute.toString();
        let secStr:string = second.toString();
        if(second < 10) {
            secStr = '0' + secStr; 
        }
        if(minute < 10) {
            minStr = '0' + minStr; 
        }
        return minStr + ':' + secStr;
    }

    private tick(): void {
        if(--this._remainTime >= 0) {
            this._updateRemain();
        } else {
            //cv.MessageCenter.send("onC2CWithdrawCountDownComplete");
            if(this.countingNode) this.countingNode.active = false;
            if(this.countingCompleteNode) this.countingCompleteNode.active = true;
            this.unschedule(this.tick);
            this._completeCb?.();
        }      
    }

    private _updateRemain() {
        let timeStr = this._getRemainedTimeString(this._remainTime);
        this.remainTimeLabel.string = timeStr;
    }

    public getRemainTime() : number {
        return this._remainTime;
    }
    // update (dt) {}
}
