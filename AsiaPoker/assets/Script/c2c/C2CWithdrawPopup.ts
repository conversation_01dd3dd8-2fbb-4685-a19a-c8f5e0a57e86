// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import cv from "../components/lobby/cv";
import CountDownView from "./CountDownView";

const {ccclass, property} = cc._decorator;

@ccclass
export default class C2CWithdrawPopup extends cc.Component {

    @property(cc.RichText)
    des: cc.RichText = null;

    @property(CountDownView)
    countDownView: CountDownView = null;

    private _confirmBtnCb: Function = null;
    private _cancelBtnCb: Function = null;

    protected onEnable(): void {
      //  this.reset();
    }

    protected onDisable(): void {
        this.reset();
    }

    protected onDestroy(): void {
        this.reset();
    }
    
    reset() {
        this._confirmBtnCb = this._cancelBtnCb = null;
    //    this.countDownView.reset();
    }

    onShow(deltaSec:number, text: string, confirmBtnCb: Function, cancleBtnCb:Function ) {
        if(this.node.active) return;
        this.reset();
        this._confirmBtnCb = confirmBtnCb;
        this._cancelBtnCb = cancleBtnCb;
        this.countDownView.setData(deltaSec, true, cancleBtnCb);
        this.des.string = text;
        this.node.active = true;
    }

    onBtnCancelClick() {
        cv.AudioMgr.playButtonSound('button_click');
      //  cv.MessageCenter.send('onC2CWithdrawPopupCancelClick', this.countDownView.getRemainTime());
        this._cancelBtnCb?.();
        this.node.active = false;
    }

    onBtnConfirmClick() {
        cv.AudioMgr.playButtonSound('button_click');
        this._confirmBtnCb?.();
        this.node.active = false;
    }

    // update (dt) {}
}
