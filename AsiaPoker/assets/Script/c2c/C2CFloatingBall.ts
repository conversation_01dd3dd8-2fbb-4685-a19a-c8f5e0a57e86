// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { TranHisTuplePrefab } from "../../mtt/prefab/impoker/hall/profile/transfer/TranHisTuplePrefab";
import { SCENE } from "../common/tools/Enum";
import cv from "../components/lobby/cv";
import { sceneType } from "./C2CNotifyManager";
import CountDownView from "./CountDownView";

const {ccclass, property} = cc._decorator;

@ccclass
export default class C2CFloatingBall extends cc.Component {

    @property(cc.Node)
    panel: cc.Node = null;

    @property(cc.Node)
    confirmOrder: cc.Node = null;

    @property(cc.Node)
    confirmToPay: cc.Node = null;

    @property(CountDownView)
    countDownView: CountDownView = null;

    @property(cc.Node)
    shadow: cc.Node = null;

    // @property(cc.Node)
    // justGo: cc.Node = null;

    private _mosueDown:boolean = false;
    private _confirmBtnCb: Function = null;
    //private _defaultPos: cc.Vec2 = null;

    start () {
       // this._defaultPos = cc.v2(this.node.x, this.node.y);
        this._mosueDown = false;
        this.panel.on(cc.Node.EventType.TOUCH_START, this._onMouseDown, this);
        this.panel.on(cc.Node.EventType.TOUCH_END, this._onMouseUp, this);
        this.panel.on(cc.Node.EventType.TOUCH_CANCEL, this._onMouseUp, this);
        this.panel.on(cc.Node.EventType.TOUCH_MOVE, this._onMouseMove, this);  
    }

    private _onMouseMove(event) {
        if(!this._mosueDown) return;
   //     let screenSize:cc.Size = cc.view.getFrameSize();
        let delta = event.getDelta();
        let newY = this.node.y + delta.y;
        let newX = this.node.x + delta.x;
        let hLimit, wLimit;

        if(this.isLandscape()) {
            //landscape
            hLimit = cc.director.getWinSize().height  * 0.95;
            wLimit = cc.director.getWinSize().width   * 0.95; 
        } else {
            //portrait
            hLimit = cc.director.getWinSize().height  * 0.95;
            wLimit = cc.director.getWinSize().width   * 0.9 
        }

        if(newY < hLimit && newY > 50) {
            this.node.y = newY;
        }

        if(newX < wLimit && newX > 100) {
            this.node.x = newX;
        } 
    }

    private _onMouseUp(event) {
        this._mosueDown = false;
    }

    private _onMouseDown(event) {
        this._mosueDown = true;
    }

    protected onEnable(): void {
    //    this.reset();
    }

    protected onDisable(): void {
     //   this.reset();
    }

    protected onDestroy(): void {
        this.reset();
    }
    
    reset() {
        this._confirmBtnCb = null;
        //this.countDownView.reset();
    }

    setData(deltaSec:number, isWithdraw: boolean, confirmBtnCb: Function, countDownCompleteCb: Function, scene:sceneType) {
        this.reset();
        this.confirmToPay.active = !isWithdraw;
        this.confirmOrder.active = isWithdraw;
       // this.countDownView.node.active = isWithdraw;

       // if(isWithdraw) {
            this.countDownView.setData(deltaSec, isWithdraw, countDownCompleteCb);
        //}

        this._confirmBtnCb = confirmBtnCb;
        this.updateBall(scene);

    }

    updateBall(scene:sceneType ) {
        switch (scene) {
            case sceneType.hall:
            case sceneType.default:
                this.node.x = cc.director.getWinSize().width * 0.85;
                this.node.y = cc.director.getWinSize().height * 0.15;
                this.shadow.active = false;
                break;
            case sceneType.normalGame:
                this.node.x = cc.director.getWinSize().width * 0.15;
                this.node.y = cc.director.getWinSize().height * 0.15;
                this.shadow.active = false;
                break;
            case sceneType.miniGame:
                this.node.x = cc.director.getWinSize().width * 0.9;
                this.node.y = cc.director.getWinSize().height * 0.25;
                this.shadow.active = true;
                break;
        }
    }

    isLandscape():boolean {
        let winSize = cc.director.getWinSize();
        return winSize.height < winSize.width;
    }

    onConfirmBtnClick() {
        cv.AudioMgr.playButtonSound('button_click');
        this._confirmBtnCb?.();
     //   this.node.active = false;
    }

}
