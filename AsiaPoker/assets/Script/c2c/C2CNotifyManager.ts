// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ButtonStyle, SCENE } from "../common/tools/Enum";

import { PlayerInfo } from "../components/game/dzPoker/data/RoomData";
import cv from "../components/lobby/cv";
import C2CFloatingBall from "./C2CFloatingBall";
import C2CWithdrawPopup from "./C2CWithdrawPopup";
import ws_protocol = require("../common/pb/ws_protocol");
import world_pb = ws_protocol.pb;
import { eRoundState } from "../components/game/jackfruit/JackfruitData";
const {ccclass, property} = cc._decorator;

export enum sceneType {
    normalGame,
    miniGame,
    hall,
    default,
}

type OrderInfo = {
    order:  world_pb.UserC2CPaymentNotice,
    popupMsg: string,
    confirmClicked: boolean,
    popupConfirmCb: Function,
}
@ccclass
export default class C2CNotifyManager extends cc.Component {

    private _floatingBall:cc.Node = null;
    private _normalGamePopup: cc.Node = null;
    private _miniGamePopup:cc.Node = null;

    private _rootNode: cc.Node = null;
    private _onBackToHallCb: Function = null;

    private _orderInfo :  OrderInfo = null;
    private _webToken: string = null;
    private _shopBillno: number = null;

    private _tipsTag: string = "C2CNotifyManager";

    registerMsg () {
        cc.game.on(cc.game.EVENT_SHOW, this.onResume, this);
        cv.MessageCenter.register("switchSceneFinish", this._onMsgSwitchScene.bind(this), this._rootNode);   

        // normal game
        cv.MessageCenter.register("on_standup_succ", this.onStandupSuccess.bind(this), this._rootNode);  
        cv.MessageCenter.register("on_game_settlement_noti", this.onGameRoundEnd.bind(this), this._rootNode);

        //jackfruit 
        cv.MessageCenter.register("stand_up_succ", this.onStandupSuccess.bind(this), this._rootNode);   
        cv.MessageCenter.register("game_round_end",  this.onGameRoundEnd.bind(this), this._rootNode);

        //cash game next round 
        //cv.MessageCenter.register("on_resetgame_noti", this.onResetGameNoti.bind(this), this._rootNode);
       
        //jackfruit next round
      //  cv.MessageCenter.register("notice_deal", this.onResetGameNoti.bind(this), this._rootNode);

        //videoCowboy  next round
        cv.MessageCenter.register("on_videoCowboy_willstart_notify",  this.onGameRoundEnd.bind(this), this._rootNode);

        //humanboy next round
        cv.MessageCenter.register("on_humanboy_willstart_notify",  this.onGameRoundEnd.bind(this), this._rootNode);

        //cowboy next round
        cv.MessageCenter.register("on_cowboy_willstart_notify",  this.onGameRoundEnd.bind(this), this._rootNode);

        //PokeMaster  next round
        cv.MessageCenter.register("on_pokerMaster_willstart_notify",  this.onGameRoundEnd.bind(this), this._rootNode);
    }

    onDestroy() {
        cc.game.off(cc.game.EVENT_SHOW, this.onResume, this);
        cv.MessageCenter.unregister("switchSceneFinish", this._rootNode);

        //cash game next round
        cv.MessageCenter.unregister("on_standup_succ", this._rootNode);
        cv.MessageCenter.unregister("on_game_endround_noti", this._rootNode);

        //jackfruit next round
        cv.MessageCenter.unregister("on_standup_succ", this._rootNode);
        cv.MessageCenter.unregister("game_round_end",  this._rootNode);

        //videoCowboy  next round
        cv.MessageCenter.unregister("on_videoCowboy_willstart_notify",  this._rootNode);

        //humanboy next round
        cv.MessageCenter.unregister("on_humanboy_willstart_notify",  this._rootNode);

        //cowboy next round
        cv.MessageCenter.unregister("on_cowboy_willstart_notify",  this._rootNode);

        //PokeMaster  next round
        cv.MessageCenter.unregister("on_pokerMaster_willstart_notify",  this._rootNode);
    }

    preloadRes(callback: Function): void {
        let count: number = 0;
        cv.resMgr.load("zh_CN/commonPrefab/C2CFloatingBall", cc.Prefab, (prefab: cc.Prefab): void => {
            if (++count >= 3) {
                if (callback) callback();
            }
        });

        cv.resMgr.load("zh_CN/commonPrefab/C2CWithdrawPopup", cc.Prefab, (prefab: cc.Prefab): void => {
            if (++count >= 3) {
                if (callback) callback();
            }
        });

        cv.resMgr.load("zh_CN/commonPrefab/C2CWithdrawMiniPopup", cc.Prefab, (prefab: cc.Prefab): void => {
            if (++count >= 3) {
                if (callback) callback();
            }
        });
    }

    reset() {
        if (this._rootNode == null) {
            this._rootNode = new cc.Node();
            cc.game.addPersistRootNode(this._rootNode);
        }
        this.registerMsg();
    }

    resetAllUI(hideTips:boolean = true) {
        if(this._floatingBall) {
            this._floatingBall.active = false;
            this._floatingBall.getComponent(C2CFloatingBall).reset();
        }
        if(this._normalGamePopup) this._normalGamePopup.active = false;
        if(this._miniGamePopup) this._miniGamePopup.active = false;
        if(hideTips && cv.TP.getTag() == this._tipsTag) {
            cv.TP.hideTipsPanel();
        }
    }

    onLogout() {
        this.resetAllUI();
        this._orderInfo = null;
    }

    init() {
        this.reset();
        let prefab: cc.Prefab = cv.resMgr.get("zh_CN/commonPrefab/C2CFloatingBall", cc.Prefab);
        this._floatingBall = cc.instantiate(prefab);
        cc.game.addPersistRootNode(this._floatingBall);
        this._floatingBall.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TOP;
        this._floatingBall.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => { event.stopPropagation(); });

        prefab = cv.resMgr.get("zh_CN/commonPrefab/C2CWithdrawPopup", cc.Prefab);
        this._normalGamePopup = cc.instantiate(prefab);
        cc.game.addPersistRootNode(this._normalGamePopup);
        this._normalGamePopup.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TOP;
        this._normalGamePopup.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => { event.stopPropagation(); });

        prefab = cv.resMgr.get("zh_CN/commonPrefab/C2CWithdrawMiniPopup", cc.Prefab);
        this._miniGamePopup = cc.instantiate(prefab);
        cc.game.addPersistRootNode(this._miniGamePopup);
        this._miniGamePopup.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TOP;
        this._miniGamePopup.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => { event.stopPropagation(); });

        this.resetAllUI();
    }

    /*
    export enum SCENE {
    TransitionScene = "TransitionScene",            // 过渡场景
    LOADING_SCENE = "LoadingScene",                 // 加载场景
    LOGIN_SCENE = "LoginScene",                     // 登陆场景
    HALL_SCENE = "HallScene",                       // 大厅场景

    GAME_SCENE = "Game",                            // 游戏场景
    GAME_SCENE_AOF = "GameAof",                     // 游戏场景

    POKERMASTER_SCENE = "PokerMasterScene",         // 扑克大师
    JACKFRUIT_SCENE = "JackfruitScene",             // 菠萝蜜

    HOTUPDATE_SCENE = "HotUpdate",                  // 热更新场景

    SPORTS_SCENE = "SportsScene",                   // 体育赛事
    TOPMATCHE_SCENE = "TopMatcheScene",             // 一起看球
    BLACKJACKPVP_SCENE = "BlackjackPVP",             // 21点
    COWBOY_SCENE = "CowboyScene",                   // 德州牛仔
    VIDEOCOWBOY_SCENE = "VideoCowboyScene",         // 视频牛仔
    HUMANBOY_SCENE = "HumanboyScene",               // 百人德州
    }
    */
    private getSceneType(sceneName:string = null) : sceneType {
        
        if(!sceneName) sceneName = cv.config.getCurrentScene();
        let scene:sceneType = sceneType.hall;
        switch (sceneName) {
            case SCENE.GAME_SCENE:
            case SCENE.JACKFRUIT_SCENE:
            // case SCENE.BLACKJACKPVP_SCENE:
                scene =  sceneType.normalGame;
               // this._backToLobbyCb = cc.director.getScene().getComponent(CowboyScene).backToMainScene;
                break;
            case SCENE.GAME_SCENE_AOF:
                scene =  sceneType.normalGame;
                break;
            case SCENE.VIDEOCOWBOY_SCENE:
            case SCENE.HUMANBOY_SCENE:
            case SCENE.POKERMASTER_SCENE:
            case SCENE.COWBOY_SCENE:
            case SCENE.SPORTS_SCENE:
            case SCENE.TOPMATCHE_SCENE:
                scene =  sceneType.miniGame;
                break;
            case SCENE.HALL_SCENE:
                scene =  sceneType.hall;
                break;
            default:
                scene = sceneType.default;
                break;
        }
        return scene;
    }

    private onResume() {
        if(!this._orderInfo) return;
        let newTime = this.getDeltaTime();
        cv.MessageCenter.send("c2c_reset_countDown", newTime);
    }

    public allowJoinGame () : boolean {
        if(cv.dataHandler.getUserData().is_c2c_block == false) {
            return true;
        } 

        // if(this.getSceneType() === sceneType.miniGame) return true;
        // if(this._orderInfo) {
        //     let msg = "您还有未确认的订单，请先确认订单";
        //     if(cv.TP.getShowingMsgText() != msg) {
        //         cv.TP.showMsg(msg, ButtonStyle.GOLD_BUTTON,  this._orderInfo.popupConfirmCb);
        //     }
        //     return false;
        // }
        return true;
    }

    public onServerNotAllowGame(errorCode:number, billno?: string) {
        let popupMsg = "禁止下注: 您还有未确认的订单，请先确认订单"
        console.log("on server not all game")
        cv.TT.showMsg(popupMsg,cv.Enum.ToastType.ToastTypeWarning);
    }

    private _onMsgSwitchScene(targetScene: string) {
        let scenetype = this.getSceneType(targetScene)
        switch (scenetype) {
            case sceneType.hall:
            case sceneType.miniGame:
            case sceneType.normalGame:
            case sceneType.default:
                if(this._floatingBall.active) {
                    this._floatingBall.getComponent(C2CFloatingBall).updateBall(scenetype);
                } else {
                    this.showBall();
                }
                break;
            default:
                break;
        }
        this._miniGamePopup.active = this._normalGamePopup.active = false;

        if(scenetype === sceneType.hall ) {
            this.showGeneralPopup();
            // this._onBackToHallCb?.();
            // this._onBackToHallCb = null;
        }
    }

    public onWithdrawResponse(msg:world_pb.UserC2CPaymentWithdrawNotice) {
        if(!this._orderInfo) return;
        // order approved

        if(msg.flag == 2) {
            let text:string = null;
            let dt:number =  msg.timestamp - this._orderInfo.order.time;
            let clientCoins:number = 0;
            if(msg.coins) clientCoins = cv.StringTools.clientGoldByServer(msg.coins);

            if(clientCoins > 0.000001) {
                if(dt >= 0 && dt <= 300) {
                    text = "5分钟内及时确认取款到账，额外奖励50%小游戏币，共 "+ cv.StringTools.clientGoldByServer(msg.coins) +" 小游戏币";
                } else if (dt > 300 && dt <= 900){
                    text = "及时确认取款到账，奖励 "+ cv.StringTools.clientGoldByServer(msg.coins) +" 小游戏币";
                }  
            }
            
            this.onLogout();

            if(text){
                cv.TP.showMsg(text, ButtonStyle.GOLD_BUTTON, null);
                cv.TP.setTag(this._tipsTag);
            }
        } else {
            //msg.flag is 3 or other, do only hide floating ball and c2c's tooltips
            this.onLogout();
        }
    }

    private showBall() {
        if(!this._orderInfo || this._floatingBall.active) return;
        let deltaTime = this.getDeltaTime();
        let isWithdraw:boolean = null;
        if(this._orderInfo.order.status == world_pb.paymentC2CStatus.payment_c2c_no_certificate) {
            isWithdraw = false;
        } else if(this._orderInfo.order.status == world_pb.paymentC2CStatus.payment_c2c_confirm) {
            isWithdraw = true;
        } else {
            cc.log("c2c notice msg: paymentC2CStatus error");
            return;
        }
        this._floatingBall.getComponent(C2CFloatingBall).setData(deltaTime, isWithdraw, this.onBallConfirmClick.bind(this), null, this.getSceneType() );
        this._floatingBall.active = true;
    }

    private showInGamePopup() {
        if(!this._orderInfo) return;
        
        let msgText = this._orderInfo.popupMsg;
        
        if(this._orderInfo.order.status === world_pb.paymentC2CStatus.payment_c2c_confirm) {
            msgText= cv.StringTools.formatC("您的取款申请已到账<color=#FCDA8E>%s</c>，5分钟之内确认，您将获得小游戏币奖励",  parseInt(this._orderInfo.order.amount));
        } 

        if (cv.native.isScreenLandscape()) {
            this._miniGamePopup.getComponent(C2CWithdrawPopup).onShow(this.getDeltaTime(), msgText, this.onInGameConfirmClick.bind(this), this.onInGameCancelClick.bind(this));
        } else {
            this._normalGamePopup.getComponent(C2CWithdrawPopup).onShow(this.getDeltaTime(), msgText, this.onInGameConfirmClick.bind(this), this.onInGameCancelClick.bind(this));
        }
    }

    private onInGameConfirmClick() {
        if(this._normalGamePopup) this._normalGamePopup.active = false;
        if(this._miniGamePopup) this._miniGamePopup.active = false;
        this.showBall();
        this.onBallConfirmClick();
    }

    private onBallConfirmClick() {
        this.showGeneralPopup();
    }

    private onInGameCancelClick() {
        this.resetAllUI();
        this.showBall();
    }

    private showGeneralPopup(btnStyle: ButtonStyle = ButtonStyle.TWO_BUTTON, text: string = null, confirmCb: Function = null, cancelCb: Function = null) {
        if(!this._orderInfo) return;

        if(text == "" || !text) text = this._orderInfo.popupMsg;
            
        if(this._orderInfo.popupConfirmCb) confirmCb = this._orderInfo.popupConfirmCb;

        if(!cancelCb) cancelCb = () => { 
            if(this._orderInfo) this._orderInfo.confirmClicked = false;
        }

        if(cv.TP.getShowingMsgText() != text) {
            cv.TP.showMsg(text, btnStyle, confirmCb, cancelCb);
            cv.TP.setTag(this._tipsTag);
        }
    }

    /*
        //搓合支付订单通知
        message UserC2CPaymentNotice {
            paymentC2CProductID product_id = 1;
            string billno = 2;
            string amount = 3;
            paymentC2CStatus status = 4;
            int64 deadline = 5;
            int64 time = 6;
        }
        // 搓合支付订单通知状态
        enum paymentC2CStatus{
            payment_c2c_undefine = 0;
            payment_c2c_no_certificate = 1;
            payment_c2c_failed = 2;
            payment_c2c_confirm = 3;
            payment_c2c_cancel = 4;
            payment_c2c_disable_floating_icon = 5;
            payment_c2c_ok_certificate = 100;
        }
    */
    public setC2CPaymentNoticeData(msg: world_pb.UserC2CPaymentNotice) {
        let popupMsg = "";
        switch (msg.status) {
            case world_pb.paymentC2CStatus.payment_c2c_confirm:
            case world_pb.paymentC2CStatus.payment_c2c_no_certificate:
                if(this._orderInfo) {
                    //若已存在未完成訂單
                    if(this._orderInfo.order.status === world_pb.paymentC2CStatus.payment_c2c_no_certificate && this._orderInfo.order.status ===  msg.status) {
                        this.showGeneralPopup();
                        //    this._orderInfo.confirmClicked = true;
                            // if(this.getSceneType() != sceneType.miniGame) {
                            //     this.showGeneralPopup();
                            // }
                           
                    } else {
                        cc.log("c2c notify send twice, should not be");
                    }
                } else {

                    if(msg.status === world_pb.paymentC2CStatus.payment_c2c_confirm) {
                        popupMsg = cv.StringTools.formatC("您的取款申请已到账%s，5分钟之内确认，您将获得小游戏币奖励", msg.amount);
                    } else {
                        popupMsg =  "请您立刻上传存款凭证，否则将无法成功上分";
                    }

                    this._orderInfo = {
                        popupMsg: popupMsg,
                        order: msg,
                        confirmClicked: false,
                        popupConfirmCb: ()=>{ 
                            if(!this._orderInfo) return;
                            this._orderInfo.confirmClicked = true; 
                            if(this.isPlayingGame()) {
                             //   this.reqStandUp();
                            } else {
                                this.goPaymentPage();
                            }
                        }
                    };

                    if(this.getSceneType() == sceneType.normalGame || this.getSceneType() == sceneType.miniGame) {
                        this.showInGamePopup();
                    } 
                    else  {
                        this.showBall();
                    }
                } 
                break;
            case world_pb.paymentC2CStatus.payment_c2c_failed:  
            case world_pb.paymentC2CStatus.payment_c2c_undefine:
            case world_pb.paymentC2CStatus.payment_c2c_cancel:
                if(msg.status === world_pb.paymentC2CStatus.payment_c2c_failed) {
                    popupMsg = "因凭证未上传，您的订单已失效"; 
                } else {
                    popupMsg = "您的订单已失效";
                }
                this.onLogout();

                if(cv.TP.getShowingMsgText() != popupMsg) {
                    cv.TP.showMsg(popupMsg, ButtonStyle.GOLD_BUTTON, null);
                    cv.TP.setTag(this._tipsTag);
                }
               
                //this.showGeneralPopup(ButtonStyle.GOLD_BUTTON, popupMsg, null);  
                break;
            case world_pb.paymentC2CStatus.payment_c2c_ok_certificate:
            case world_pb.paymentC2CStatus.payment_c2c_disable_floating_icon:
            default:
                this.onLogout();
                break;
        }
    }

    private getDeltaTime(): number {
        let deltaSec = 0;
        if(this._orderInfo && this._orderInfo.order.deadline) {
            let now = Math.round( Date.now() / 1000);
            deltaSec  = this._orderInfo.order.deadline - now;
            deltaSec = deltaSec > 0? deltaSec : 0;
        }
        return deltaSec;
    }

    private goPaymentPage( rightNow: boolean = false) {

        if(!this._orderInfo) return;

        this._orderInfo.confirmClicked = false;
        cv.worldNet.RequestGetWebToken();

        if(this._orderInfo.order.status === world_pb.paymentC2CStatus.payment_c2c_confirm) {
            cv.worldNet.RequestDelCoinOrderRequest(true);
        }
    }
    
    private openShop() {
        let jumpUrl = this.getC2CUrl();

        if(!jumpUrl) {
            cc.log("c2c jump url form error!");
            return;
        } else {
            this._shopBillno = this._webToken = null;
        }

        if(cv.native.isScreenLandscape() || cv.GameDataManager.tRoomData.isZoom()) {
           // this._onBackToHallCb = cv.SHOP.onC2COpenShop.bind(cv.SHOP, jumpUrl);
            this.reqLeaveRoom();
        } else {
            cv.SHOP.onC2COpenShop(jumpUrl);
        } 
    }

    public onGetWebToken(webToken:string) {
        this._webToken = webToken;
        this.openShop();
    }

    public onGetShopBillno(billno: number) {
        this._shopBillno = billno;
        this.openShop();
    }

    private getC2CUrl(): string {
        if(!this._webToken)  return null;
        if (this._orderInfo.order.status === world_pb.paymentC2CStatus.payment_c2c_confirm && !this._shopBillno) {
            return null;
        }

        let imToken: string = cv.dataHandler.getUserData().imToken;
        let token: string = this._webToken;
        let u32Uid = cv.dataHandler.getUserData().u32Uid;
        let acBuffer: string = "";
        if (u32Uid == 0) {
            acBuffer = cv.tools.GetStringByCCFile("user_id");
            if (cv.StringTools.getArrayLength(acBuffer) <= 0) return null;
        }
        else {
            acBuffer = u32Uid.toString();
        }

        let productId = this._orderInfo.order.product_id;
        let nickname = encodeURI(cv.dataHandler.getUserData().nick_name);
        let clubId = cv.dataHandler.getUserData().firstClubId;
        let unionId = cv.dataHandler.getUserData().firstAlliId;

        let amount = cv.dataHandler.getUserData().u32Chips;
        let usdt_amount = cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(cv.dataHandler.getUserData().usdt));
        let points = cv.dataHandler.getUserData().user_points;
        let uuid = cv.native.GetDeviceUUID();
        let device = cv.httpHandler.getDeviceType();

        let timeStamp = Math.floor((new Date()).getTime() / 1000);
        let sign = "294de072c3d679f3a6adc5ff2c50b448e9265ebe";
        let language_type = "";

        if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.zh_CN) {
            language_type = "zh";
        }
        else if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.yn_TH) {
            language_type = "vn";
        } else if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.hi_IN) {
            language_type = "inr";
        } else {
            language_type = "en";
        }
        let extra_param1 = "";
        let isGuest = cv.dataHandler.getUserData().isTouristUser ? 1 : 0;

        let areacode = cv.dataHandler.getUserData().priorityareaCode;
        let upload_billno = this._orderInfo.order.billno;
        let billno = this._shopBillno;
        let webUrl = cv.dataHandler.getUserData().shopUrl; 
        
        let key: string = "";
        let jsonData: string = "";
        let isWebview = cv.SHOP.getIsWebViewPaymentParam();
        
        if(this._orderInfo.order.status ===  world_pb.paymentC2CStatus.payment_c2c_confirm) {
            key = cv.StringTools.formatC("%d", productId) + acBuffer + acBuffer + cv.StringTools.formatC("%d", clubId) + cv.StringTools.formatC("%d", unionId)
            + cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(amount)) + cv.StringTools.formatC("%d", billno) + nickname + token + points + 
            language_type + cv.StringTools.formatC("%lld", timeStamp) + cv.StringTools.formatC("%s", sign);

            webUrl +=  cv.config.getStringData("WEB_API_C2C_WITHDRAW", true);

            jsonData = "product_id=" + cv.StringTools.formatC("%d", productId) + "&user_id=" + acBuffer + "&login_name=" + acBuffer

            + "&club_id=" + cv.StringTools.formatC("%d", clubId) + "&union_id=" + cv.StringTools.formatC("%d", unionId) 

            + "&true_amount=" + cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(amount)) + "&usdt_amount=" + usdt_amount

            + "&billno=" + billno + "&nickname=" + nickname + "&token=" + token + "&points=" + points 

            + "&uuid=" + uuid + "&device=" + device + "&language_type=" + language_type + "&areacode=" + areacode 

            + "&time=" + cv.StringTools.formatC("%lld", timeStamp) + "&key_code=" + cv.md5.md5(key) +  "&imToken="  + imToken + "&isWebview=" + isWebview;

            
        } else if (this._orderInfo.order.status ===  world_pb.paymentC2CStatus.payment_c2c_no_certificate) {
            key = cv.StringTools.formatC("%d", productId) + acBuffer + acBuffer + cv.StringTools.formatC("%d", clubId) + cv.StringTools.formatC("%d", unionId)
            + nickname + token + language_type + cv.StringTools.formatC("%lld", timeStamp) + cv.StringTools.formatC("%d", isGuest) + cv.StringTools.formatC("%s", sign);

            webUrl +=  cv.config.getStringData("WEB_API_C2C_DEPOSITE", true);

            jsonData = "product_id=" + cv.StringTools.formatC("%d", productId) + "&user_id=" + acBuffer + "&login_name=" + acBuffer

            + "&club_id=" + cv.StringTools.formatC("%d", clubId) + "&union_id=" + cv.StringTools.formatC("%d", unionId) + "&nickname=" + nickname

            + "&token=" + token + "&language_type=" + language_type + "&areacode=" + areacode + "&time=" + cv.StringTools.formatC("%lld", timeStamp) + "&extra_param1=" + extra_param1

            + "&guest=" + isGuest + "&key_code=" + cv.md5.md5(key) +  "&upload_billno="  + upload_billno +  "&imToken="  + imToken + "&isWebview=" + isWebview;
        } 

        let url = webUrl + "?" + jsonData;
        console.log("url::" + url);
        return url;
    }

    public isAllowBet(){
        return true;
    }

    public isOrderActive():boolean {
        if(this._orderInfo) {
            if(this._orderInfo.confirmClicked) {
                return true;
            }
            // if(cv.dataHandler.getUserData().is_c2c_block) {
            //     if(this.getDeltaTime() <=0 || this._orderInfo.confirmClicked) {
            //         return true;
            //     }
            // } else {
            //     if(this._orderInfo.confirmClicked) return true;
            // }
        }
        return false;
    }

    /**
     * 一般樸克房: 站起
     * 波羅密: 站起
     * 急速: 沒有站起，考慮改成離開房間 cv.roomManager.RequestLeaveRoom()
     * 小游戲: 不監聽
     */
    private onGameRoundEnd() {
        if(this.isOrderActive()) {
            this.reqStandUp();
        }
    }

    /**
     * 一般樸克房: 在上一局所有動畫結束時 彈出不可關閉彈窗
     * 波羅密: 在上一局所有動畫結束時 彈出不可關閉彈窗
     */
     public onStandupSuccess() {
        if(!this._orderInfo) return;
        if(!this._orderInfo.confirmClicked) return;

        this._orderInfo.confirmClicked = false;

        if(!cv.GameDataManager.tRoomData.isZoom()) {
            let scene = cv.config.getCurrentScene() 
            if(scene === SCENE.JACKFRUIT_SCENE) {
                this.goPaymentPage();
               // this.showGeneralPopup();
            } else {
                // ref: GameMain.ts => this.scheduleOnce(this.CloseSendFun.bind(this), 3.2);
                //等动画结束
               // this.scheduleOnce(this.showGeneralPopup.bind(this), 3.2);
                this.scheduleOnce(this.goPaymentPage.bind(this), 3.2);
            }
        }
    }

    /**
     * only for小游戲: 在上一局所有動畫結束時 彈出不可關閉彈窗
     */
    private onResetGameNoti() {
        if(cv.dataHandler.getUserData().is_c2c_block) {
            this.showGeneralPopup();
        } else {
            if(this._orderInfo && this._orderInfo.confirmClicked) {
                this.reqLeaveRoom();
            }
        }
    }

    private reqLeaveRoom(){
        cv.roomManager.RequestLeaveRoom();
        if(this._orderInfo) this._orderInfo.confirmClicked = false;
    }

    private reqStandUp(){
        let scene = cv.config.getCurrentScene();
       // this._orderInfo.confirmClicked = false;
        if(scene === SCENE.JACKFRUIT_SCENE) {
            cv.jackfruitNet.requestStandUp(cv.GameDataManager.tRoomData.u32RoomId);
        } else if (scene === SCENE.GAME_SCENE || scene === SCENE.GAME_SCENE_AOF || this.getSceneType() === sceneType.miniGame) {
            if(cv.GameDataManager.tRoomData.isZoom() ||  this.getSceneType() === sceneType.miniGame) {
                this.reqLeaveRoom();
            } else {
                cv.gameNet.gameStandUp();
            }
                
        } else {

        }
    }

    private isPlayingGame():boolean {
        //cv.JackfruitManager.tRoomData.kTablePlayerList[i].seatId == cv.JackfruitManager.tRoomData.nSelfSeatID
       
        let scene = cv.config.getCurrentScene();

        if(this.getSceneType() === sceneType.miniGame) return true;

        if(scene === SCENE.JACKFRUIT_SCENE) {
            let jfPlayerList = cv.JackfruitManager.tRoomData.kTablePlayerList;
            for (let i = 0; i <jfPlayerList.length; ++i) {
                if(jfPlayerList[i].seatId == cv.JackfruitManager.tRoomData.nSelfSeatID) {
                    if(cv.JackfruitManager.tRoomData.curState === eRoundState.RS_DUMMY ||
                        cv.JackfruitManager.tRoomData.curState === eRoundState.RS_FREE ||
                        cv.JackfruitManager.tRoomData.curState === eRoundState.RS_READY || 
                        cv.JackfruitManager.tRoomData.curState === eRoundState.RS_WAIT ) {
                            return false;       
                        } else {
                            return true;
                        }
                }
            }
            return false;
        } else if (scene === SCENE.GAME_SCENE || scene === SCENE.GAME_SCENE_AOF) {
            for (let i = 0; i < cv.GameDataManager.tRoomData.kTablePlayerList.length; ++i) {
                let kPlayer: PlayerInfo = cv.GameDataManager.tRoomData.kTablePlayerList[i];
                if(kPlayer.seatid == cv.GameDataManager.tRoomData.i32SelfSeat) {
                    //self player
                    return kPlayer.in_game;
                }
            }
        } else {
            return false;
        }

        //exception
        return false;
    }

    private static instance: C2CNotifyManager;

    public static getInstance(): C2CNotifyManager {
        if (!this.instance || !this.instance._floatingBall || !cc.isValid(this.instance._floatingBall, true)) {
            this.instance = new C2CNotifyManager();
        }
        return this.instance;
    }

}
