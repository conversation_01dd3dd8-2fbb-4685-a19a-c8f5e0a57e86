// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const {ccclass, property} = cc._decorator;


@ccclass
export class ClickEventHandler extends cc.Component {
    private _callBack: Function;

    public setCallBack(callBack: Function){
        this._callBack = callBack;
    }
    
    onClick(eventTouch:cc.Event.EventTouch, param:string){
        this._callBack?.();  
    }
}

export interface TipsPanelStyleConfig {
    backgroundSize?: cc.Size;
    headerSize?: cc.Size;
    headerPosition?: cc.Vec2;
    contentFontSize?: number;
    lineHeight?:number;
    contentPosition: cc.Vec2;
    handerTouchEvent?: boolean;
}

export enum TipsPanelThemeType{
    Default = 0,
    TwoButton_Buyin_USD = 1
}

export default class TipsPanelStyleModel  {

    private static instance: TipsPanelStyleModel;
    private themeData: {[key: number]: TipsPanelStyleConfig}

    public static getInstance(): TipsPanelStyleModel {
        if (!this.instance) {
            this.instance = new TipsPanelStyleModel();
        }
        return this.instance;
    }
    constructor(){
        this.themeData = {
            [TipsPanelThemeType.Default]: {//original setting 
                contentFontSize: 46,
                lineHeight: 64,
                contentPosition: new cc.Vec2(0, 305),
                headerPosition: new cc.Vec2(0, 318),
                headerSize: new cc.Size(860, 140)
            },
            [TipsPanelThemeType.TwoButton_Buyin_USD]: {
                backgroundSize: new cc.Size(860, 504),
                contentFontSize: 44,
                lineHeight: 64,
                contentPosition: new cc.Vec2(0, 252),
                headerPosition: new cc.Vec2(0, 187),
                headerSize: new cc.Size(860, 120),
                handerTouchEvent: true,
            },
        };
    }

    public getTheme(theme: TipsPanelThemeType) : TipsPanelStyleConfig{
        return this.themeData[theme];
    }
}
