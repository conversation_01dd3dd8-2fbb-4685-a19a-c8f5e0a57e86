import cv from "../../components/lobby/cv";
import ws_protocol = require("./../../../Script/common/pb/ws_protocol");
import world_pb = ws_protocol.pb;
import { SafeAreaHelper } from "../../../default/shared/safe_area_helper/SafeAreaHelper";

export type TNParams = {
    type: TNTypes
    title?: string,
    content?: string,
    roomId?: number,        // for MTT_GAME_START 
    time_seconds?: number,   // for MTT_GAME_START 
    mtt_game_start_type? : world_pb.MttNotifyType, // for MTT_GAME_START 
}

export enum TNTypes {
    MTT_GAME_START = 0,
}

/**
 * Usage example:
 * 
   const param: Partial<TNParams> = {
      type: TNTypes.newType,
      title: "notice title",
      content: "notice content"
   };

   cv.TN.showMsg(param as TNParams);
};
*/

const {ccclass, property} = cc._decorator;
@ccclass
export default class TopNotice extends cc.Component {

    @property(cc.Node)
    mainPanel: cc.Node = null;

    @property(cc.RichText)
    title: cc.RichText = null;

    @property(cc.RichText)
    content: cc.RichText = null;

    private _topNotices: any[] = [];
    private _isTopAnimating: boolean = false
    private _TN: TopNotice = null;

    //#region animation params
    private readonly _showDuration = 0.5; 
    private readonly _stayDuration = 5.0; 
    private readonly _fadeDuration = 0.5; 
    //#endregion

    preloadRes(callback: Function): void {
        cv.resMgr.load("zh_CN/commonPrefab/TopNotice", cc.Prefab, (prefab: cc.Prefab): void => {
            if (callback) callback();
        });
    }

    init() {
        const prefab: cc.Prefab = cv.resMgr.get("zh_CN/commonPrefab/TopNotice", cc.Prefab);
        const rootNode = cc.instantiate(prefab);
        this._TN = rootNode.getComponent(TopNotice);
        cc.game.addPersistRootNode(rootNode);
        this._TN.node.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TT;
        this._TN.node.active = false;
    }

    public showMsg(params: TNParams) {
        this._topNotices.push(params);
        if (!this._isTopAnimating) {
            this._showNextMsg();
        }
    }

    private _showNextMsg() {
        if (this._topNotices.length > 0) {
            this._TN.node.active = true;
            const params = this._topNotices.shift();
            this._setData(params);
            this._display();
        } else {
            this._TN.node.active = false;
        }
    }

    private _setData(params: TNParams) {
        let content: string, title: string;
        switch (params.type) {
            case TNTypes.MTT_GAME_START:
            {
                const gameName = params.title.length > 14 ? params.title.substr(0,14) + "..." : params.title;

                switch (params.mtt_game_start_type) {
                    case world_pb.MttNotifyType.notify_type_30min: 
                    {
                        // show the start time as minutes
                        content = cv.StringTools.formatC(cv.config.getStringData("MTT_notice_time_info_1"), gameName, Math.round(params.time_seconds / 60));
                        break;
                    }

                    case world_pb.MttNotifyType.notify_type_60min: 
                    case world_pb.MttNotifyType.notify_type_180min: 
                    {
                        // show the start time as HOURs
                        content = cv.StringTools.formatC(cv.config.getStringData("MTT_notice_time_info_2"), gameName, Math.round(params.time_seconds / 3600));
                        break;
                    }

                    default: 
                    {
                        console.error("World_pb.MsgType type error!");
                        break;
                    }

                }

                title = cv.config.getStringData("MTT_notice_title");
                break;
            }

            default:
                console.error("params is undefined");
                break;
        }

        this._TN.content.string = content;
        this._TN.title.string = title;
    }

    private _display() {
        this._isTopAnimating = true;
        this._TN.mainPanel.x = 0;

        const screenSize = cc.view.getVisibleSize();
        
        // adjust display pos
        const topPosition = screenSize.height;
        
        // moveDistance = (HMFUtils.isPad() || HMFUtils.isPadForWeb() ? 50 : 154) + height - fixHeight;
        const panelHeight = this._TN.mainPanel.height;
        const safeOffset = SafeAreaHelper.getUpperDangerZoneYOffset();
        let extraOffset = 0;
        if (safeOffset > 0) {
            extraOffset = 50;
        }
        let moveDistance = panelHeight + safeOffset - extraOffset;
        this._TN.mainPanel.y = topPosition * 0.5 + panelHeight;

        // fade-in anim (show)
        const fadeInAction = cc.fadeIn(this._showDuration);
        const moveToTopAction = cc.moveBy(this._showDuration, cc.v2(0, -moveDistance));
        const showAction = cc.spawn(fadeInAction, moveToTopAction);

        // fade-out anim
        const fadeOutAction = cc.fadeOut(this._fadeDuration);
        const moveOutAction = cc.moveBy(this._fadeDuration, cc.v2(0, moveDistance));
        const hideAction = cc.spawn(fadeOutAction, moveOutAction);

        // create animation sequence
        const sequenceAction = cc.sequence(
            showAction,
            cc.delayTime(this._stayDuration),
            hideAction,
            cc.callFunc(() => {
                this._isTopAnimating = false;
                this._showNextMsg();
            })
        );

        // run anim (then fade out)
        this._TN.node.runAction(sequenceAction);
    }
    
    protected onDestroy(): void {
        this._isTopAnimating = false;
    }

    private static instance: TopNotice;
    public static getInstance(): TopNotice {
        if (!this.instance || !this.instance._TN.node || !cc.isValid(this.instance._TN.node, true)) {
            this.instance = new TopNotice();
        }
        return this.instance;
    }
}
