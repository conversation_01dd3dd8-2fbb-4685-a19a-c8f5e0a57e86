import cv from "./../../../components/lobby/cv"
import { CircleSprite, Head_Mode } from "../../tools/CircleSprite";
import MttDetailInfoItem from "./MttDetailInfoItem";
import ListView from "../../../../Script/common/tools/ListView";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PokerInfo_mtt extends cc.Component {
    @property(cc.Prefab) pokerInfoItem: cc.Prefab = null;

    msg: any = null;

    // LIFE-CYCLE CALLBACKS:
    backBtnClickFunc: Function = null;

    itemList: Array<cc.Node> = [];

    scrolllHeigt: number = 958;

    @property(cc.ScrollView) scrollview: cc.ScrollView = null;

    @property(cc.Node) mttDetailInfo: cc.Node = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) top: cc.Node = null;

    @property(cc.Node) rank: cc.Node = null;

    @property(cc.Node) scrollView: cc.Node = null;

    onLoad() {
        this.registerMsg();

        this.initLanguage();

        this.setSafeAreaAndScrollView();
    }

    start() {

    }

    onDestroy(): void {
        cv.MessageCenter.unregister("responseMTTDataDetailSuccess", this.node);

        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
    }

    registerMsg() {
        cv.MessageCenter.register("responseMTTDataDetailSuccess", this.responseMTTDataSuccess.bind(this), this.node);

        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);
    }

    initLanguage() {
        //比赛时间 
        cv.StringTools.setLabelString(this.node, "LayoutParent/rank/matchTime/title", "DataView_data_panel_dataInfo_panel_MTT_matchTime_txt");

        //总人数
        cv.StringTools.setLabelString(this.node, "LayoutParent/rank/matchPerson/title", "DataView_data_panel_dataInfo_panel_MTT_matchTotal_txt");

        //起始筹码
        cv.StringTools.setLabelString(this.node, "LayoutParent/rank/matchChip/title", "DataView_data_panel_dataInfo_panel_MTT_matchBeginChip_txt");

        //涨盲时间
        cv.StringTools.setLabelString(this.node, "LayoutParent/rank/matchAddTime/title", "DataView_data_panel_dataInfo_panel_MTT_matchAddBB_txt");

        //名次
        cv.StringTools.setLabelString(this.node, "LayoutParent/rank/titleRank", "DataView_data_panel_dataInfo_panel_MTT_matchRank_txt");

        //昵称
        cv.StringTools.setLabelString(this.node, "LayoutParent/rank/titleNickname", "DataView_data_panel_dataInfo_panel_MTT_nickName_txt");

        //奖励
        cv.StringTools.setLabelString(this.node, "LayoutParent/rank/titleReward", "DataView_data_panel_dataInfo_panel_MTT_matchReward_txt");

        //奖励
        cv.StringTools.setLabelString(this.node, "LayoutParent/top/title_text", "DataView_data_panel_dataInfo_panel_MTT_matchDetail_txt");
    }

    responseMTTDataSuccess(value) {
        this.initData(value);
    }

    onBtnBackClick() {
        cv.AudioMgr.playButtonSound('back_button');

        cv.MessageCenter.send("show_mail_entrance");

        if (this.backBtnClickFunc) {
            this.backBtnClickFunc();

            return;
        }

        console.log("====> back: " + this.node.getAnchorPoint() + ", " + this.node.getPosition())

        cv.action.moveToAction(this.node,
            "TO_RIGHT",
            "OUT",
            "FAST",
            new cc.Vec2(cv.config.WIDTH * 0.5, cv.config.HEIGHT * 0.5),
            new cc.Vec2(cv.config.WIDTH * 1.5, cv.config.HEIGHT * 0.5));
    }

    public bindcallfunc(node: cc.Node, info, i) {
        console.log(info);

        console.log(i);

        let _rewardText = "";

        if (info.data.rewardText) {
            switch (cv.config.getCurrentLanguage()) {
                // 中文
                case cv.Enum.LANGUAGE_TYPE.zh_CN:
                    _rewardText = info.data.rewardText.Ch;
                    break;

                // 越南文
                case cv.Enum.LANGUAGE_TYPE.yn_TH:
                    _rewardText = info.data.rewardText.Vn;
                    break;

                // 泰文
                case cv.Enum.LANGUAGE_TYPE.th_PH:
                    _rewardText = info.data.rewardText.Th;
                    break;

                // arabic
                case cv.Enum.LANGUAGE_TYPE.ar_SA:
                    _rewardText = info.data.rewardText.Ar;
                    break;

                // hindi
                case cv.Enum.LANGUAGE_TYPE.hi_IN:
                    _rewardText = info.data.rewardText.Hi;
                    break;

                // 英文
                default:
                    _rewardText = info.data.rewardText.En;
                    break;
            }
        }

        if (info.type == 0) {
            //输赢列表Item
            node.getComponent(MttDetailInfoItem).setData(info.data.playerInfo, i, info.data.display_currency, _rewardText);
        }
    }

    public getItemType(data, index) {
        return data.type;
    }

    initData(msg: any) {
        if (this.node.activeInHierarchy) {
            console.log("current MttDetaiINfo is activeInHierarchy");

            return;
        }

        this.node.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_2;

        cv.action.moveToAction(this.node,
            "TO_LEFT",
            "ENTER",
            cv.Enum.action_FuncType.dt_NORMAL,
            new cc.Vec2(cv.config.WIDTH * 1.5, cv.config.HEIGHT * 0.5),
            new cc.Vec2(cv.config.WIDTH * 0.5, cv.config.HEIGHT * 0.5));

        this.msg = JSON.parse(msg);

        let Panel_1 = cc.find("LayoutParent/scorellview/content/Panel_1", this.node);

        let contentNode = cc.find("LayoutParent/scorellview/content", this.node);

        let data = this.msg.gameResultDetail;

        cv.StringTools.cleanNodeArray(this.itemList);

        //我的昵称
        cc.find("LayoutParent/rank/myName", this.node).getComponent(cc.Label).string = cv.dataHandler.getUserData().nick_name;

        //我的头像
        let headIcon = cc.find("LayoutParent/rank/headIcon", this.node);

        (CircleSprite).setCircleSprite(headIcon, cv.dataHandler.getUserData().headUrl);

        //我的名次
        const rankStr=data.myRank>0?cv.StringTools.formatC(cv.config.getStringData('DataView_data_panel_dataInfo_panel_MTT_Rank_txt'),data.myRank):cv.config.getStringData('DataView_data_panel_dataInfo_panel_MTT_Rank_Absent');
        cc.find("LayoutParent/rank/rankbg/rank", this.node).getComponent(cc.Label).string = rankStr;

        //比赛时长
        let hour = data.gameTimeSec / 3600;

        let timeStr = "";

        if (hour >= 1) {
            timeStr = hour.toFixed(2) + "h";
        }
        else {
            timeStr = Math.floor(data.gameTimeSec / 60) + "min";
        }

        cc.find("LayoutParent/rank/matchTime/content", this.node).getComponent(cc.Label).string = timeStr;

        //比赛人数
        cc.find("LayoutParent/rank/matchPerson/content", this.node).getComponent(cc.Label).string = data.numPlayers;

        //起始筹码
        cc.find("LayoutParent/rank/matchChip/content", this.node).getComponent(cc.Label).string = data.startingCoins;

        //比赛升盲时间
        cc.find("LayoutParent/rank/matchAddTime/content", this.node).getComponent(cc.Label).string = data.levelTime / 60 + "min";

        //比赛列表
        let playerResults = data.playerResults;

        let dataList = [];

        this.itemList = [];

        if (playerResults) {
            if (playerResults.length > 1) {
                //按名次排序
                playerResults.sort((a: any, b: any): number => { return a.rank - b.rank; });
            }

            for (let i = 0; i < playerResults.length; i++) {
                dataList.push({ type: 0, data: { playerInfo: playerResults[i], display_currency: data.display_currency, rewardText: playerResults[i].toolName } });
            }
        }

        this.scrollview.getComponent(ListView).init(this.bindcallfunc.bind(this), this.getItemType.bind(this));

        this.scrollview.getComponent(ListView).notifyDataSetChanged(dataList);
    }

    setSafeAreaAndScrollView() {
        let offsetY = cv.SafeAreaWithDifferentDevices.getSafeArea();

        this.safearea.height = offsetY;

        let scrollViewHeight = this.mttDetailInfo.height - offsetY - this.top.height - this.rank.height;

        this.scrollView.height = scrollViewHeight;

        this.layout.updateLayout();
    }
}
