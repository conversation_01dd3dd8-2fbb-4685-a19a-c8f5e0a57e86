import cv from "../../../components/lobby/cv"
import { CircleSprite, Head_Mode } from "../../tools/CircleSprite";
import SpinDetailInfoItem from "./SpinDetailInfoItem";
import ListView from "../../tools/ListView";

const { ccclass, property } = cc._decorator;

@ccclass
export default class SpinDetail extends cc.Component {
    @property(cc.Prefab) pokerInfoItem: cc.Prefab = null;

    msg: any = null;

    // LIFE-CYCLE CALLBACKS:
    backBtnClickFunc: Function = null;

    itemList: Array<cc.Node> = [];

    scrolllHeigt: number = 958;

    @property(cc.ScrollView) scrollview: cc.ScrollView = null;

    @property(cc.Node) spinDetailInfo: cc.Node = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) top: cc.Node = null;

    @property(cc.Node) rank: cc.Node = null;

    @property(cc.Node) scrollView: cc.Node = null;

    @property(cc.Label) lblMatchTimeContent: cc.Label = null;

    @property(cc.Label) lblMatchPersonContent: cc.Label = null;

    @property(cc.Label) lblMyName: cc.Label = null;

    @property(cc.Label) lblRank: cc.Label = null;

    @property(cc.Node) headIcon: cc.Node=null;

    onLoad() {
        this.registerMsg();

        this.initLanguage();

        this.setSafeAreaAndScrollView();
    }

    start() {

    }

    onDestroy(): void {
        cv.MessageCenter.unregister("responseSpinDataDetailSuccess", this.node);

        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
    }

    registerMsg() {
        cv.MessageCenter.register("responseSpinDataDetailSuccess", this.responseMTTDataSuccess.bind(this), this.node);

        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.node);
    }

    initLanguage() {
    }

    responseMTTDataSuccess(value) {
        this.initData(value);
    }

    onBtnBackClick() {
        cv.AudioMgr.playButtonSound('back_button');

        cv.MessageCenter.send("show_mail_entrance");

        if (this.backBtnClickFunc) {
            this.backBtnClickFunc();

            return;
        }

        console.log("====> back: " + this.node.getAnchorPoint() + ", " + this.node.getPosition())

        cv.action.moveToAction(this.node,
            "TO_RIGHT",
            "OUT",
            "FAST",
            new cc.Vec2(cv.config.WIDTH * 0.5, cv.config.HEIGHT * 0.5),
            new cc.Vec2(cv.config.WIDTH * 1.5, cv.config.HEIGHT * 0.5));
    }

    public bindcallfunc(node: cc.Node, info, i) {
        console.log(info);

        console.log(i);

        let _rewardText = "";

        if (info.data.rewardText) {
            switch (cv.config.getCurrentLanguage()) {
                // 中文
                case cv.Enum.LANGUAGE_TYPE.zh_CN:
                    _rewardText = info.data.rewardText.Ch;
                    break;

                // 越南文
                case cv.Enum.LANGUAGE_TYPE.yn_TH:
                    _rewardText = info.data.rewardText.Vn;
                    break;

                // 泰文
                case cv.Enum.LANGUAGE_TYPE.th_PH:
                    _rewardText = info.data.rewardText.Th;
                    break;

                // arabic
                case cv.Enum.LANGUAGE_TYPE.ar_SA:
                    _rewardText = info.data.rewardText.Ar;
                    break;

                // hindi
                case cv.Enum.LANGUAGE_TYPE.hi_IN:
                    _rewardText = info.data.rewardText.Hi;
                    break;

                // 英文
                default:
                    _rewardText = info.data.rewardText.En;
                    break;
            }
        }

        if (info.type == 0) {
            //输赢列表Item
            node.getComponent(SpinDetailInfoItem).setData(info.data.playerInfo, i, info.data.display_currency, _rewardText);
        }
    }

    public getItemType(data, index) {
        return data.type;
    }

    initData(msg: any) {
        this.msg = JSON.parse(msg);
        if (this.node.activeInHierarchy) {
            console.log("current SpinDataActivity is activeInHierarchy");
            return;
        }

        this.node.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_2;

        cv.action.moveToAction(this.node,
            "TO_LEFT",
            "ENTER",
            cv.Enum.action_FuncType.dt_NORMAL,
            new cc.Vec2(cv.config.WIDTH * 1.5, cv.config.HEIGHT * 0.5),
            new cc.Vec2(cv.config.WIDTH * 0.5, cv.config.HEIGHT * 0.5));

        let data = this.msg.gameResultDetail;
        cv.StringTools.cleanNodeArray(this.itemList);

        //我的昵称
        this.lblMyName.string = cv.dataHandler.getUserData().nick_name;

        //我的头像

        (CircleSprite).setCircleSprite(this.headIcon, cv.dataHandler.getUserData().headUrl);

        //我的名次
        const rankStr=data.myRank>0?cv.StringTools.formatC(cv.config.getStringData('DataView_data_panel_dataInfo_panel_MTT_Rank_txt'),data.myRank):cv.config.getStringData('DataView_data_panel_dataInfo_panel_MTT_Rank_Absent');
        this.lblRank.string = rankStr;

        //比赛时长
        let hour = data.gameTimeSec / 3600;

        let timeStr = "";

        if (hour >= 1) {
            timeStr = hour.toFixed(2) + "h";
        }
        else {
            timeStr = Math.floor(data.gameTimeSec / 60) + "min";
        }

       
        this.lblMatchTimeContent.string = timeStr;

        //比赛人数
        this.lblMatchPersonContent.getComponent(cc.Label).string = data.numPlayers;

        // This can be enabled in future
        // //起始筹码
        // cc.find("LayoutParent/rank/matchChip/content", this.node).getComponent(cc.Label).string = data.startingCoins || 0;

        // //比赛升盲时间
        // cc.find("LayoutParent/rank/matchAddTime/content", this.node).getComponent(cc.Label).string = (data.levelTime || 0) / 60 + "min";

        //比赛列表
        let playerResults = data.playerResults;

        let dataList = [];

        this.itemList = [];

        if (playerResults) {
            if (playerResults.length > 1) {
                //按名次排序
                playerResults.sort((a: any, b: any): number => { return a.rank - b.rank; });
            }

            for (let i = 0; i < playerResults.length; i++) {
                dataList.push({ type: 0, data: { playerInfo: playerResults[i], display_currency: data.display_currency, rewardText: playerResults[i].toolName } });
            }
        }

        this.scrollview.getComponent(ListView).init(this.bindcallfunc.bind(this), this.getItemType.bind(this));

        this.scrollview.getComponent(ListView).notifyDataSetChanged(dataList);
    }

    setSafeAreaAndScrollView() {
        let offsetY = cv.SafeAreaWithDifferentDevices.getSafeArea();

        this.safearea.height = offsetY;

        const scrollViewHeight = this.spinDetailInfo.height - offsetY - this.top.height - this.rank.height;

        this.scrollView.height = scrollViewHeight;

        this.layout.updateLayout();
    }
}
