// eslint-disable-next-line max-classes-per-file

import cv from "../../components/lobby/cv";
import { SafeAreaHelper } from "../../../default/shared/safe_area_helper/SafeAreaHelper";

// Learn TypeScript:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html
export enum PushNoticeType {
    PUSH_ERROR = 0,
    PUSH_LOBBY,			// 大厅
    PUSH_WORLD,			// 全局
    PUSH_TEXAS,			// 德州扑克
    PUSH_COWBOY,		// 德州牛仔
    PUSH_HUMANBOY,		// 百人德州
    PUSH_ALLIN,			// allin or fold
    PUSH_VIDEOCOWBOY,	// 视频牛仔
    PUSH_ZOOM_TEXAS,	// 极速德州
    PUSH_BET,			// 必下
    PUSH_POKERMASTER,   // 扑克大师
    PUSH_JACKFRUIT,     // 菠萝蜜
    PUSH_PLO,           // 奥马哈
    PUSH_STAR_SEAT,     // 明星桌
    PUSH_SQUID
};

export class PushNoticeData {
    str: string = "";
    msgType: PushNoticeType[] = [];
};
const { ccclass } = cc._decorator;

@ccclass
export class PushNotice extends cc.Component {

    private static instance: PushNotice;
    private prefab: cc.Prefab = null;
    private msgNode: cc.Node = null;
    private _pushNotice_panel: cc.Node = null;
    private _bPuchNoticeIsShowIng: boolean = false;
    private _notice_text: cc.RichText = null;
    private _notice_text_1: cc.RichText = null;
    private _notice_bg: cc.Sprite = null;
    private _pushType: number = 0;
    private m_pushNoticeList: PushNoticeData[] = [];
    public static getInstance(): PushNotice {
        if (!this.instance || !this.instance.msgNode || !cc.isValid(this.instance.msgNode, true)) {
            this.instance = new PushNotice();
        }
        return this.instance;
    };

    getPushNotice(): PushNoticeData[] {
        return this.m_pushNoticeList;
    }

    addPushNotice(pushNoticeData: PushNoticeData): void {
        if (cv.StringTools.getArrayLength(pushNoticeData.str) > 0) {
            /** H5 block star table marquee */
            if (!cv.resMgr.isNative()) {
                let desc: string = cv.config.getStringData("game_review_favor_list_game_type_start_seat_txt");
                if (pushNoticeData.str.indexOf(desc) != -1) {
                    return;
                }
            }
            this.m_pushNoticeList.push(pushNoticeData);
        }
    }

    earseFirstPushNotice(): void {
        this.m_pushNoticeList.splice(0, 1);
    }

    setPushNoticeType(type: number): void {
        if (this._pushNotice_panel) {
            this.setPushType(type);
        }
    }

    hideNoticeLayer(isView?: boolean) {
        isView = isView === true;
        if (this._pushNotice_panel) {
            this._pushNotice_panel.active = false;
        }

    }

    preloadRes(callback: Function): void {
        cv.resMgr.load("zh_CN/commonPrefab/PushNotice", cc.Prefab, (prefab: cc.Prefab): void => {
            if (callback) callback();
        });
    }

    init() {
        const prefab: cc.Prefab = cv.resMgr.get("zh_CN/commonPrefab/PushNotice", cc.Prefab);
        this.prefab = prefab;
        this._bPuchNoticeIsShowIng = false;
        const rootNode: cc.Node = cc.instantiate(this.prefab);
        cc.game.addPersistRootNode(rootNode);
        rootNode.setAnchorPoint(cc.v2(0.5, 0.5));
        rootNode.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TT;
        this._pushNotice_panel = (rootNode.getChildByName("PushNotice_panel"));
        this._notice_text = (this._pushNotice_panel.getChildByName("notice_text")).getComponent(cc.RichText);
        this._notice_text_1 = (this._pushNotice_panel.getChildByName("notice_text_1")).getComponent(cc.RichText);
        this._notice_bg = this._pushNotice_panel.getChildByName("notice_bg").getComponent(cc.Sprite);
        this._pushNotice_panel.active = (false);

        const offsetY = SafeAreaHelper.getUpperDangerZoneYOffset()
        cv.resMgr.adaptWidget(rootNode, true);
        rootNode.setPosition(rootNode.x, rootNode.y - offsetY);

        this.msgNode = rootNode;
        this.schedule(this.Update.bind(this), 1.0);
        cc.game.on(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
    }

    onDestroy() {
        cc.game.off(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
    }

    public reset() {
        if (!this.msgNode) return;
        cv.resMgr.adaptWidget(this.msgNode, true);
        this._notice_text.node.stopAllActions();
        this._notice_text_1.node.stopAllActions();
        this.hidePushNotice();

        const offsetY = SafeAreaHelper.getUpperDangerZoneYOffset()
        this.msgNode.setPosition(this.msgNode.x, this.msgNode.y - offsetY);

        this.msgNode.active = !this.shouldHidePushNotice();
    }

    Update(num: number): void {
        if (this.getPushNotice().length > 0 && !this._bPuchNoticeIsShowIng) {
            this.showPushNotice();
        }
        else {
            this._pushNotice_panel.active = (this._bPuchNoticeIsShowIng);
        }
    }

    getMessage(): string {
        if (this.getPushNotice().length > 0) {
            const data = this.getPushNotice()[0];
            if (this._pushType === PushNoticeType.PUSH_ERROR)					// 如果是不显示跑马灯的场景  抛弃掉
            {
                this.earseFirstPushNotice();
                return this.getMessage();
            }

            const len: number = data.msgType.length;
            for (let i = 0; i < len; i++) {
                // 如果是全局消息 则全场景显示
                if (data.msgType[i] === PushNoticeType.PUSH_WORLD) {
                    // 百人牛仔特殊处理
                    if (this._pushType === PushNoticeType.PUSH_COWBOY
                        || this._pushType === PushNoticeType.PUSH_HUMANBOY
                        || this._pushType === PushNoticeType.PUSH_VIDEOCOWBOY
                        || this._pushType === PushNoticeType.PUSH_POKERMASTER) {
                        cv.MessageCenter.send("showMedalMsg", data.str);
                        this.earseFirstPushNotice();
                        return "";
                    }

                    return data.str;

                }
                // 指定场景匹配显示（包括大厅）
                if (data.msgType[i] === this._pushType) {
                    // 百人牛仔特殊处理
                    if (this._pushType === PushNoticeType.PUSH_COWBOY
                        || this._pushType === PushNoticeType.PUSH_HUMANBOY
                        || this._pushType === PushNoticeType.PUSH_VIDEOCOWBOY
                        || this._pushType === PushNoticeType.PUSH_POKERMASTER) {
                        cv.MessageCenter.send("showMedalMsg", data.str);
                        this.earseFirstPushNotice();
                        return "";
                    }

                    return data.str;

                }
                // 遍历后不匹配抛弃
                if (i === len - 1) {
                    this.earseFirstPushNotice();
                    return this.getMessage();
                }
            }

        }

        return "";
    }

    showPushNotice(): void {
        const text = this.getMessage();
        if (cv.StringTools.getArrayLength(text) > 0) {
            if (cc.director.getActionManager().getNumberOfRunningActionsInTarget(this._notice_text.node) > 0) return;

            // On Device should check this scene should hide push notice, reset() is not hide push notice.
            if (this.shouldHidePushNotice()) {
                this.hidePushNotice();
                return;
            }
            this._bPuchNoticeIsShowIng = true;
            this._pushNotice_panel.active = (true);
            const strList = cv.StringTools.getStringListByLength(this._notice_text.node, this.getPushNotice()[0].str);
            this._notice_text.string = strList[0];
            // this._notice_text_1.string = strData.substr(index, strData.length);
            // 如果长度在第一个控件的显示范围内  则给第二个控件赋值为空格（直接赋值""会有显示BUG  导致显示上一次的内容）
            if (strList.length < 2) {
                this._notice_text_1.string = " ";
            } else {
                this._notice_text_1.string = strList[1];
            }
            const labelWidth = cv.resMgr.getRichTextStringSize(this._notice_text).width;
            const labelWidth1 = cv.resMgr.getRichTextStringSize(this._notice_text_1).width;
            this._notice_text.node.setPosition(this._pushNotice_panel.getContentSize().width, this._notice_text.node.y);
            this._notice_text_1.node.setPosition(this._notice_text.node.x + labelWidth, this._notice_text.node.y);
            // 跑马灯经过一个屏幕宽度的单位时间(可用来调整播放速度)
            let time = 4;
            time += (labelWidth + labelWidth1) / (this._pushNotice_panel.getContentSize().width / time);
            const moveTo = cc.moveTo(time, cc.v2(-(labelWidth + labelWidth1) - this._pushNotice_panel.getContentSize().width, this._notice_text.node.y));
            const moveTo1 = cc.moveTo(time, cc.v2(-labelWidth1 - this._pushNotice_panel.getContentSize().width, this._notice_text.node.y));
            const callBack = cc.callFunc(this.moveCallBack.bind(this, this._notice_text));
            this._notice_text.node.runAction(moveTo);
            this._notice_text_1.node.runAction(cc.sequence(moveTo1, callBack));
        }
        else {
            this.hidePushNotice();
        }
    }

    hidePushNotice() {
        this._bPuchNoticeIsShowIng = false;
        this._pushNotice_panel.active = (false);
    }

    moveCallBack(): void {
        this.earseFirstPushNotice();
        this._bPuchNoticeIsShowIng = false;
    }

    Adaptation(): void {
        const size = cc.director.getScene().getContentSize();
        if (size.height > size.width)// 竖屏
        {
            const offsetY = SafeAreaHelper.getUpperDangerZoneYOffset()
            cv.resMgr.setSpriteFrame(this._notice_bg.node, "zh_CN/common/icon/common_notice_bg");
            this._notice_bg.node.setContentSize(cc.size(size.width, this._notice_bg.node.getContentSize().height));
            this._pushNotice_panel.setContentSize(this._notice_bg.node.getContentSize());
            this._notice_bg.node.setPosition(cc.v2(this._notice_bg.node.getContentSize().width / 2, 0));
            this._pushNotice_panel.setPosition(cc.v2(size.width / 2, size.height - this._pushNotice_panel.getContentSize().height / 2 - offsetY));
        }
        else// 横屏
        {
            cv.resMgr.setSpriteFrame(this._notice_bg.node, "zh_CN/common/icon/common_notice_bg_1");
            this._pushNotice_panel.setContentSize(this._notice_bg.node.getContentSize());
            this._notice_bg.node.setPosition(cc.v2(this._notice_bg.node.getContentSize().width / 2, 0));
            this._pushNotice_panel.setPosition(cc.v2(size.width / 2, size.height / 2));
        }
    }

    setPushType(type: number): void {
        this._pushType = type;
    }

    public OnAppEnterBackground() {
        this.m_pushNoticeList = [];
        this._notice_text.node.stopAllActions();
        this._notice_text_1.node.stopAllActions();
        this.hidePushNotice();
    }

    getPushTypeFromGameId(gameID: number): PushNoticeType {
        switch (gameID) {
            case cv.Enum.GameId.Texas:
                return PushNoticeType.PUSH_TEXAS;
            case cv.Enum.GameId.StarSeat:
                return PushNoticeType.PUSH_STAR_SEAT;
            case cv.Enum.GameId.Allin:
                return PushNoticeType.PUSH_ALLIN;
            case cv.Enum.GameId.Bet:
                return PushNoticeType.PUSH_BET;
            case cv.Enum.GameId.Plo:
                return PushNoticeType.PUSH_PLO;
            case cv.Enum.GameId.Squid:
                return PushNoticeType.PUSH_SQUID;
        }

        if (gameID >= cv.Enum.GameId.ZoomTexas && gameID <= cv.Enum.GameId.ZoomTexasMax) {
            return PushNoticeType.PUSH_ZOOM_TEXAS;
        }
    }

    shouldHidePushNotice(): boolean {
        let shouldHide: boolean = false;
        const currentScene: string = cv.config.getCurrentScene();
        switch (currentScene) {
            case cv.Enum.SCENE.LOADING_SCENE:
            case cv.Enum.SCENE.LOGIN_SCENE:
            case cv.Enum.SCENE.COWBOY_SCENE:
            case cv.Enum.SCENE.VIDEOCOWBOY_SCENE:
            case cv.Enum.SCENE.HUMANBOY_SCENE:
            case cv.Enum.SCENE.POKERMASTER_SCENE:
            case cv.Enum.SCENE.CARIBBEAN_POKER_SCENE:
            case cv.Enum.SCENE.WEALTHTRIO_SCENE:
            case cv.Enum.SCENE.BIT_MASTER_SCENE:
            case cv.Enum.SCENE.ISLOT_SCENE:
            case cv.Enum.SCENE.POCKETGAME_SCENE:
                shouldHide = true;
                break;
            default:
                shouldHide = false;
                break;
        }

        return shouldHide;
    }
}
