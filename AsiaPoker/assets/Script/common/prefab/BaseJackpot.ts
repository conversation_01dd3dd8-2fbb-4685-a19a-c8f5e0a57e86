import cv from "../../components/lobby/cv";
import { JackpotViewType } from "../tools/Enum";
import ScrollViewItemPool from "../tools/ScrollViewItemPool";


const { ccclass, property } = cc._decorator;

@ccclass
export default class BaseJackpot extends cc.Component {
    @property(cc.Node) public backgroundMask: cc.Node = null;
    @property(cc.Node) public jackpot_button: cc.Node = null;
    @property(cc.Node) public jackpot_navigate: cc.Node = null;
    @property(cc.Node) public reward_button: cc.Node = null;
    @property(cc.Node) public reward_navigate: cc.Node = null;
    @property(cc.Node) public jackpot_panel: cc.Node = null;
    @property(cc.Node) public reward_panel: cc.Node = null;

    @property(cc.Label) public jackpot_num_title: cc.Label = null;
    @property(cc.Label) public jackpot_num_label: cc.Label = null;
    @property(cc.Label) public jackpot_des_title: cc.Label = null;
    @property(cc.Label) public jackpot_des_label: cc.Label = null;
    @property(cc.Node) public card_panels: cc.Node[] = [];

    @property(cc.Label) public jackPotInfo_text: cc.Label = null;
    @property(cc.Label) public bigWinnerName_text: cc.Label = null;
    @property(cc.Label) public bigWinnerCard_type_text: cc.Label = null;
    @property(cc.Label) public bigWinnerNumber_text: cc.Label = null;
    @property(cc.Label) public bigWinnerTime_text: cc.Label = null;
    @property(cc.Label) public jackpot_blind_text: cc.Label = null;
    @property(cc.Sprite) public luckDogAvatarSprite: cc.Sprite = null;
    @property(cc.Label) public reward_des_title: cc.Label = null;
    @property(ScrollViewItemPool) public potSignScrollViewItemPool: ScrollViewItemPool = null;

    private _viewType: JackpotViewType = JackpotViewType.JACKPOT;

    protected start() {
        this.backgroundMask.on(cc.Node.EventType.TOUCH_END, function (event) {
            this.node.active = false;
        }, this);
        this.jackpot_button.on(cc.Node.EventType.TOUCH_END, function (event) {
            this._setViewType(JackpotViewType.JACKPOT);
        }, this);
        this.reward_button.on(cc.Node.EventType.TOUCH_END, function (event) {
            this._setViewType(JackpotViewType.REWARD);
        }, this);

        this._initLanguage();
        this._setViewType(JackpotViewType.JACKPOT);

    }

    protected _initLanguage() {
        cv.StringTools.setLabelString(this.jackpot_panel, "jackpot_cardtype_label", "GameJackPot_jackPot_panel_awardType_txt");
        cv.StringTools.setLabelString(this.jackpot_panel, "jackpot_proportion_label", "GameJackPot_jackPot_panel_awardPercent_txt");
        cv.StringTools.setLabelString(this.jackpot_panel, "card_panel_0/cardtype_label", "M_UITitle122");
        cv.StringTools.setLabelString(this.jackpot_panel, "card_panel_1/cardtype_label", "M_UITitle121");
        cv.StringTools.setLabelString(this.jackpot_panel, "card_panel_2/cardtype_label", "M_UITitle120");
        cv.StringTools.setLabelString(this.reward_button, "label", "GameJackPot_button_panel_jackpotRecord_button");
        cv.StringTools.setLabelString(this.jackpot_button, "label", "GameJackPot_button_panel_jackpot_button");
        cv.StringTools.setLabelString(
            this.node,
            'view_panel/jackPotReward_panel/Panel_5/jackPotInfo_text',
            'GameJackPot_jackPotSign_panel_Panel_5_jackPotInfo_text'
        );

        this.jackpot_num_title.string = cv.StringTools.formatC(cv.config.getStringData("UIGameJackpotBlindAwardAmount"), this._getAnte().toString());
        this.jackpot_des_title.string = cv.StringTools.formatC(cv.config.getStringData("UIGameJackpotRecordAwardSet"), this._getAnte().toString());
    }

    protected _setViewType(viewType: JackpotViewType) {
        if (this._viewType === viewType) return;
        cv.AudioMgr.playButtonSound('tab');

        this._viewType = viewType;
        const isJackpot = this._viewType === JackpotViewType.JACKPOT;
        const isReward = this._viewType === JackpotViewType.REWARD;
        this._setBtnSelect(isJackpot, this.jackpot_button);
        this.jackpot_navigate.active = isJackpot;
        this.jackpot_panel.active = isJackpot;
        this._setBtnSelect(isReward, this.reward_button);
        this.reward_panel.active = isReward;
        this.reward_navigate.active = isReward;

        switch (this._viewType) {
            case JackpotViewType.JACKPOT:
                this._updateJackpotPanel();
                break;
            case JackpotViewType.REWARD:
                this._updateRewardPanel();
                this._updateLuckyProfile();
                break;
            default:
                throw new Error("unexpected jackpotViewType error");
        }
    }

    protected _updateJackpotPanel() {
        // Implement this method in the subclass based on the scene-specific logic
    }

    protected _updateRewardPanel() {
        // Implement this method in the subclass based on the scene-specific logic
    }

    protected _updateLuckyProfile() {
        // Implement this method in the subclass based on the scene-specific logic
    }

    protected _setBtnSelect(isSelect: boolean, btn: cc.Node) {
        cc.find("label", btn).color = isSelect ? new cc.Color().fromHEX("fbd888") : new cc.Color().fromHEX("ffffff");
    }


    protected _setCardProportion(index: number, num: number) {
        const label = cc.find("proportion_label", this.card_panels[index]).getComponent(cc.Label);
        label.string = num.toString() + "%";

        const gress: cc.Sprite = cc.find("bar", this.card_panels[index]).getComponent(cc.Sprite);
        gress.fillRange = -1 * num * 0.01;
    }

    protected _getAmounts(amount: number, paddingZero: boolean = false) {
        let amounts = amount.toString();
        const _jackpotNumberLength: number = 8;
        const len = Math.abs(_jackpotNumberLength - amounts.length)
        for (let i = 0; i < len; i++) {
            if (amounts.length < _jackpotNumberLength) {
                if (paddingZero)
                    amounts = "0" + amounts;
            } else if (amounts.length > _jackpotNumberLength) {
                amounts = amounts.substr(1, amounts.length - i - 1);
            }
        }
        return amounts;
    }

    protected _getAnte(): number {
        // Implement this method in the subclass based on the scene-specific logic
        return 0;
    }
}
