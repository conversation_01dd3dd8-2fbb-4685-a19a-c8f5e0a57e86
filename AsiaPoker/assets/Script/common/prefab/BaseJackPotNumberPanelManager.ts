import { SafeAreaHelper } from '../../../default/shared/safe_area_helper/SafeAreaHelper';
import cv from '../../components/lobby/cv';
import JackpotNumberController from '../../../falling_mars_jackpot/script/JackpotNumberController';

const { ccclass, property } = cc._decorator;

@ccclass
export default abstract class BaseJackpotNumberPanelManager extends cc.Component {
    @property(cc.Prefab) jackPot_prefab: cc.Prefab = null;
    @property(cc.Prefab) jackPot_prefab_plo: cc.Prefab = null;
    @property(cc.Widget) jackPot_panel_widget: cc.Widget = null;
    @property(cc.Node) newyear_fu_left_node: cc.Node = null;
    @property(cc.Node) newyear_fu_right_node: cc.Node = null;
    @property(JackpotNumberController) jackpotNumberController: JackpotNumberController = null;

    protected _jackPotPanel: cc.Node = null;

    public init(gameSceneNode: cc.Node) {
        if (cv.GameDataManager.tRoomData.u32GameID === cv.Enum.GameId.Plo) {
            this._jackPotPanel = cc.instantiate(this.jackPot_prefab_plo);
        } else {
            this._jackPotPanel = cc.instantiate(this.jackPot_prefab);
        }
        gameSceneNode.addChild(this._jackPotPanel);
        this._jackPotPanel.active = false;
        this.node.active = false;
        this._checkIfItsNewYear();
        this.jackpotNumberController.onClickJackpotFunc = this._showJackPot.bind(this);
        this.jackpotNumberController.isAssociatedWithJackpot = this._hasJackpotEnabled.bind(this);
        this._addEvents();
    }

    public sendNetJackPot() {
        // Implement this method in the subclass based on the scene-specific logic
    }

    public setSafeArea(gameSceneNode: cc.Node) {
        const safeOffset = SafeAreaHelper.getUpperDangerZoneYOffset();
        const seatOffset = 60;
        const offsetY = seatOffset - safeOffset > 0 ? seatOffset - safeOffset : safeOffset;
        this.jackPot_panel_widget.top = offsetY;
        cv.resMgr.adaptWidget(gameSceneNode, true);
    }

    public reset() {
        this._updateJackpotNum(-1);
    }

    protected onDestroy(): void {
        this._removeEvents();
    }

    protected _addEvents() {
        cv.MessageCenter.register('currentRoomJackpot', this._updateJackpotNumEvent.bind(this), this.node);
        cv.MessageCenter.register('on_jackpot_data', this._updateJackpotNumEvent.bind(this), this.node);
        cv.MessageCenter.register('update_jackpotAmount', this._updateJackpotNumEvent.bind(this), this.node);
    }

    protected _removeEvents() {
        cv.MessageCenter.unregister('currentRoomJackpot', this.node);
        cv.MessageCenter.unregister('on_jackpot_data', this.node);
        cv.MessageCenter.unregister('update_jackpotAmount', this.node);
    }

    protected _showJackPot() {
        if (!cc.isValid(this.node) || !this.node.active) return;
        cv.AudioMgr.playButtonSound('tab');
        this.sendNetJackPot();
        this._jackPotPanel.active = true;
        cv.MessageCenter.send('hide_bombInfoTips');
    }

    protected _updateJackpotNumEvent() {
        // Implement this method in the subclass based on the scene-specific logic
    }

    protected _updateJackpotNum(amount: number) {
        this.jackpotNumberController?.updateJackpotNum(amount);
    }

    protected _checkIfItsNewYear() {
        const isNewYear: boolean = cv.config.isShowNewYear();
        this.newyear_fu_left_node.active = isNewYear;
        this.newyear_fu_right_node.active = isNewYear;
    }

    protected abstract _hasJackpotEnabled(): boolean;
}
