// Learn TypeScript:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html
import cv from "../../components/lobby/cv";
import TipsPanelStyleModel, { TipsPanelThemeType } from "./TipsPanelConfig";
const { ccclass, property } = cc._decorator;

@ccclass
export class TipsPanel extends cc.Component {

    private tipsMsg: cc.Prefab = null;
    private sureCallback: Function = null;
    private cancelCallback: Function = null;
    private msgNode: cc.Node = null;
    public cutDownNode: cc.Node = null;
    public cutDownIcon: cc.Node = null;
    public bgPanel: cc.Node = null;
    public down_txt: cc.Node = null;
    public time: number = 0;
    private _panelTag: string = null;
    private message_text_positionY: number = 305;
    private button_common_y: number = -197;
    public NOT_RESET_TAG = "NOT_RESET_TAG";
    private messageEditBox:cc.EditBox=null;
    private titleLabel: cc.Label = null;
    private content_RichText: cc.RichText = null;
    private content_Label: cc.Label = null;
    private tempText: cc.Label = null;
    private goldButton: cc.Node = null;
    private grayButton: cc.Node = null;
    private sureButton: cc.Node = null;
    private cancelButton: cc.Node = null;
    private imageBG: cc.Node = null;
    private titleBG: cc.Node = null;
    private hideTipsCallback: Function = null;

    preloadRes(callback: Function): void {
        cv.resMgr.load("zh_CN/commonPrefab/TipsPanel", cc.Prefab, (prefab: cc.Prefab): void => {
            if (callback) callback();
        });
    }

    init() {
        const prefab: cc.Prefab = cv.resMgr.get("zh_CN/commonPrefab/TipsPanel", cc.Prefab);
        this.msgNode = cc.instantiate(prefab);
        cc.game.addPersistRootNode(this.msgNode);

        this.msgNode.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TT;

        this.goldButton = cc.find("gold_button", this.msgNode);
        this.grayButton = cc.find("gray_button", this.msgNode);
        this.sureButton = cc.find("sure_button", this.msgNode);
        this.cancelButton = cc.find("cancel_button", this.msgNode);
        this.imageBG = cc.find("image_bg", this.msgNode);
        this.titleBG = cc.find("titleBg", this.msgNode);

        this.messageEditBox = cc.find("edit_text", this.msgNode).getComponent(cc.EditBox);
        this.titleLabel = cc.find("titleBg/title", this.msgNode).getComponent(cc.Label);
        this.content_RichText = cc.find("bgPanel/message_RichText", this.msgNode).getComponent(cc.RichText);
        this.content_Label = cc.find("bgPanel/message_text", this.msgNode).getComponent(cc.Label);

        this.goldButton.on("click", this.onBtnSureClick, this);  //只有一个金色按钮
        this.grayButton.on("click", this.onBtnSureClick, this);  //只有一个灰色按钮

        this.sureButton.on("click", this.onBtnSureClick, this);  //右边确定按钮
        this.cancelButton.on("click", this.onBtnCancelClick, this);  //左边取消按钮

        this.down_txt = cc.find("bgPanel/cutDown/content", this.msgNode);
        this.cutDownNode = cc.find("bgPanel/cutDown", this.msgNode);
        this.cutDownIcon = cc.find("bgPanel/cutdownIcon", this.msgNode)
        this.bgPanel = cc.find("bgPanel", this.msgNode);
        this.msgNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => { event.stopPropagation(); });
        this.msgNode.active = false;
        this.showMsgMail(false);
        this.initLanguage();
        cv.MessageCenter.register(cv.config.CHANGE_LANGUAGE, this.initLanguage.bind(this), this.msgNode);

        this.message_text_positionY = this.content_Label.node.y;
        this.button_common_y = this.sureButton.y;
        this.tempText = cc.find("bgPanel/temp_text", this.msgNode).getComponent(cc.Label);
    }

    onDestroy(): void 
    {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.node);
    }

    public recharge() 
    {
        cc.find("cancel_button/Label", this.msgNode).getComponent(cc.Label).string = cv.config.getStringData("GameDomanShop_recharge_txt");
    }

    public reset() {
        if (!this.msgNode) return;

        if(cc.sys.isBrowser) {
            this.adaptUI();
        } else {
            cv.resMgr.adaptWidget(this.msgNode, true);
        }
        
        //this.msgNode.setPosition(cc.v2(cv.config.WIDTH * 0.5, cv.config.HEIGHT * 0.5));
        if (this._panelTag != this.NOT_RESET_TAG) {
            this.hideTipsPanel();//隐藏跨场景弹框
        }
    }

    initLanguage() {
        cc.find("gold_button/Label", this.msgNode).getComponent(cc.Label).string = cv.config.getStringData("Confirm");
        cc.find("cancel_button/Label", this.msgNode).getComponent(cc.Label).string = cv.config.getStringData("Cancel");
        cc.find("sure_button/Label", this.msgNode).getComponent(cc.Label).string = cv.config.getStringData("Confirm");
    }

    public showImage(isVisible: boolean) {
        isVisible = (typeof isVisible === "undefined") ? false : isVisible;
        cc.find("edit_text", this.msgNode).active = isVisible;
        cc.find("bg", this.msgNode).active = isVisible;
    };

    public showMsgMail(isVisible: boolean) {
        isVisible = (typeof isVisible === "undefined") ? false : isVisible;
        cc.find("Image_duanxin", this.msgNode).active = isVisible;
    };

    public getEditBoxString(): string {
        return cc.find("edit_text", this.msgNode).getComponent(cc.EditBox).string;
    }

    public getEditBox(): cc.EditBox {
        return cc.find("edit_text", this.msgNode).getComponent(cc.EditBox);
    }

    /* 公用弹框
      content: 显示的文本内容
      btnStyle: 弹框显示的按钮风格
                cv.Enum.ButtonStyle.TWO_BUTTON: 显示两个按钮，  如："取消" "确定"
                cv.Enum.ButtonStyle.GOLD_BUTTON: 显示一个按钮， 按钮是金色背景
                cv.Enum.ButtonStyle.GRAY_BUTTON: 显示一个按钮， 按钮是灰色背景
     callback:  按钮回调函数, 两个按钮时，右边按钮回调。 以及单个按钮时按钮回调。
     cancelCallback： 两个按钮时候左边的响应按钮

     needEditBox: 是否显示输入框。 
     titleString: 显示标题内容，如果缺省，表示不显示标题，标题背景将被隐藏
    _horizontalAlign: 文本对齐的方式，默认为居中对齐
                cc.Label.HorizontalAlign.LEFT: 左对齐
                cc.Label.HorizontalAlign.CENTER: 居中对齐
                cc.Label.HorizontalAlign.RIGHT:  靠右对齐
    */
    public showMsg(content: string, btnStyle: number,
        callback: Function, cancelCallback?: Function,
        needEditBox: boolean = false, titleString: string = "",
        horizontalAlign: number = cc.Label.HorizontalAlign.CENTER) 
    {
        if (!this.msgNode) return;
        this.grayButton.active = false;
        this._panelTag = null;
        this.cutDownNode.active = false;
        this.cutDownIcon.active = false;

        cv.MessageCenter.send("HideWebview_ShowWindows");
        cv.MessageCenter.send("hide_bombInfoTips");
        cv.native.SYwebCloseChildWebview();
        this.msgNode.active = true;
        this.sureCallback = callback;
        this.cancelCallback = cancelCallback;
        this.showImage(needEditBox);
        this.showMsgMail(false);
        this.setButtonText(cv.Enum.ButtonType.TWO_BUTTON);
        this.messageEditBox.string = "";
        this.messageEditBox.placeholder = "";
        this.titleLabel.string = titleString;
        this.titleBG.active = titleString !== "";
        this.imageBG.active = false;

        // 倒计时弹框和消息显示弹框的高度不一样
        this.bgPanel.setContentSize(cc.size(this.bgPanel.getContentSize().width, 520));

        this.content_RichText.node.active = false;

        this.content_Label.node.active = true;
        this.content_Label.node.color = cc.color(225, 225, 225);
        this.content_Label.node.setContentSize(780, 280);

        this.content_Label.node.y = this.message_text_positionY;
        this.setCurretnButtonPosY(this.button_common_y);

        this.tempText.string = content;
        const _labelWidth = cv.resMgr.getLabelStringSize(this.tempText, content).width
        this.tempText.node.active = false;

        const lineCount = _labelWidth / this.content_Label.node.getContentSize().width;

        // 根据效果图，多行文本，内容显示都是固定长度 780 x 280 。所以message_text采用shrink模式。
        // 因为通过\n判断当前文本多少行是不准确的（文本控件自动换行是没有\n），所以通过一个临时temp_text来计算实际长度，来大致计算行数。
        // 当临时temp_text渲染文本的width大于780的时候，此时是有多行的。
        // （message_text采用shrink属性，返回的width是固定的780, height固定是280。 不能得到实际宽度）

        // 按照需求效果： 文本默认是居中对齐  
        //              小于等于2行的情况下，fontSize是48。 大于2行情况fontSize是40
        if (lineCount <= 2) {
            // 小于等于2行
            this.content_Label.fontSize = 48;
            this.content_Label.lineHeight = 67;
        } else {
            this.content_Label.fontSize = 48;
            this.content_Label.lineHeight = 56;
        }

        this.content_Label.horizontalAlign = horizontalAlign;  // 对齐方式
        if (this.content_Label.enableWrapText)
        {
            this.content_Label.string = content;
        }
        else
        {
            this.content_Label.string = cv.StringTools.calculateAutoWrapString(this.content_Label.node, content);
        }

        this.SetButtonsStyle(btnStyle);
    };

    public SetButtonsStyle(btnStyle: number)
    {
        switch (btnStyle) {
            case cv.Enum.ButtonStyle.TWO_BUTTON:   
                this.goldButton.active = false;
                this.grayButton.active = false;
                this.cancelButton.active = true;
                this.sureButton.active = true;
                break;

            case cv.Enum.ButtonStyle.GOLD_BUTTON:   
                this.goldButton.active = true;
                this.grayButton.active = false;
                this.cancelButton.active = false;
                this.sureButton.active = false;
                break;

            case cv.Enum.ButtonStyle.GRAY_BUTTON:  
                this.goldButton.active = false;
                this.grayButton.active = true;
                this.cancelButton.active = false;
                this.sureButton.active = false;
                break;
        }
    }

    public getShowingMsgText() : string {
        if(!this.getVisible()) return "";
        
        if(this.content_Label) {
            return this.content_Label.string;
        }

        return "";
    }

    /* 公用弹框
    content: 显示的文本内容
     callback:  按钮回调函数, 两个按钮时，右边按钮回调。 以及单个按钮时按钮回调。
     cancelCallback： 两个按钮时候左边的响应按钮
     needEditBox: 是否显示输入框。
     titleString: 显示标题内容，如果缺省，表示不显示标题，标题背景将被隐藏
    */
    public showTimeMsg(content: string, callback: Function, cancelCallback?: Function, needEditBox: boolean = false,
        titleString: string = "") {
        if (!this.msgNode) return;
        this.cutDownNode.active = true;
        this.cutDownIcon.active = true;
        this.time = 15;
        this.down_txt.getComponent(cc.RichText).string = cv.StringTools.formatC(cv.config.getStringData("dialog_cutdown_sec"), this.time);
        this.schedule(this.scheduleUpdate, 1);
        this.sureButton.active = false;
        cv.MessageCenter.send("HideWebview_ShowWindows");
        cv.MessageCenter.send("hide_bombInfoTips");
        cv.native.SYwebCloseChildWebview();
        const sure1_button = this.sureButton;
        this._panelTag = null;

        this.msgNode.active = true;
        this.sureCallback = callback;
        this.cancelCallback = cancelCallback;
        this.showImage(needEditBox);
        this.showMsgMail(false);
        this.setButtonText(cv.Enum.ButtonType.TWO_BUTTON);
        const lb = cc.find("Label", sure1_button)
        lb.getComponent(cc.Label).string = cv.config.getStringData("TipsPanel_sure0_button");
        lb.getComponent(cc.Label).fontSize = 50;
        this.messageEditBox.string = "";
        this.messageEditBox.placeholder = "";
        this.titleLabel.string = titleString;
        this.titleBG.active = titleString !== "";

        this.bgPanel.setContentSize(cc.size(this.bgPanel.getContentSize().width, 545));

        this.content_RichText.node.active = false;
        this.content_Label.node.active = true;
        this.content_Label.node.color = cc.color(222, 97, 97);

        this.content_Label.node.setContentSize(780, 112);

        this.tempText.string = content;
        const _labelWidth = cv.resMgr.getLabelStringSize(this.tempText, content).width;
        this.tempText.node.active = false;

        const lineCount = _labelWidth / this.content_Label.node.getContentSize().width;

        if (lineCount < 2) {
            //如果只有1行
            this.content_Label.fontSize = 48;
            this.content_Label.lineHeight = 67;
        } else {
            this.content_Label.fontSize = 40;
            this.content_Label.lineHeight = 56;
        }

        this.content_Label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        this.content_Label.verticalAlign = cc.Label.VerticalAlign.CENTER;

        this.content_Label.node.setPosition(this.content_Label.node.x, this.message_text_positionY - 25);    //根据效果图，倒计时文本的y坐标要比普通弹框靠下
        this.setCurretnButtonPosY(this.button_common_y - 19);  //根据效果图，倒计弹框操作按钮的y坐标要比普通弹框靠下
        this.content_Label.string = content;

        this.goldButton.active = false;
        this.cancelButton.active = true;
        this.sureButton.active = true;
    };



    //隐藏公用弹框上面的按钮
    //btnStyle: 需要隐藏弹框显示的按钮风格
    //      cv.Enum.ButtonStyle.TWO_BUTTON:  隐藏两个按钮
    //      cv.Enum.ButtonStyle.GOLD_BUTTON:  隐藏金色按钮
    //      cv.Enum.ButtonStyle.GRAY_BUTTON:  隐藏灰色按钮
    public hideDialogButton(btnStyle: number) {
        switch (btnStyle) {
            case cv.Enum.ButtonStyle.TWO_BUTTON:   //显示左右两个按钮
                this.goldButton.active = false;
                this.grayButton.active = false;
                break;
             
            case cv.Enum.ButtonStyle.GOLD_BUTTON:   //显示一个金色按钮
                this.goldButton.active = false;
                break;

            case cv.Enum.ButtonStyle.GRAY_BUTTON:  //显示一个灰色按钮
                this.grayButton.active = false;
                break;
        }
    }

    //设置当前弹框的Tag
    public setTag(tag: string) {
        this._panelTag = tag;
    }

    //获取当前弹框的Tag
    public getTag() {
        return this._panelTag;
    }

    //设置按钮提示语
    setButtonText(btnType) {

        let _gold_button = this.goldButton.getChildByName("Label").getComponent(cc.Label);  //单个金色按钮
        let _gray_button = this.grayButton.getChildByName("Label").getComponent(cc.Label);  //单个灰色按钮

        let _sureBtnTxt = this.sureButton.getChildByName("Label").getComponent(cc.Label);  //确定按钮
        let _cancleBtnTxt = this.cancelButton.getChildByName("Label").getComponent(cc.Label);  //取消按钮

        switch (btnType) {
            case cv.Enum.ButtonType.TWO_BUTTON_FOLD:
                _sureBtnTxt.string = cv.config.getStringData("Zoom_button_text_2");
                _cancleBtnTxt.string = cv.config.getStringData("Zoom_button_text_1");
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_FOLD_LOOK:
                _sureBtnTxt.string = cv.config.getStringData("Zoom_button_text_2");
                _cancleBtnTxt.string = cv.config.getStringData("Zoom_button_text_3");
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_SILIAO_TIPS:
                _sureBtnTxt.string = cv.config.getStringData("siyu_btn_look");
                _cancleBtnTxt.string = cv.config.getStringData("TipsPanel_cancel_button");
                break;
            case cv.Enum.ButtonType.TWO_BUTTON_BUYIN_TIPS:
                _sureBtnTxt.string = cv.config.getStringData("TipsPanel_sure_button");
                _cancleBtnTxt.string = cv.config.getStringData("GameDomanShop_recharge_txt");
                this.content_Label.lineHeight = cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN ? 50 : 40;
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_LIMIT_TIPS:  //新手带入限制弹框
                _sureBtnTxt.string = cv.config.getStringData("GameScene_sitDownLimit_panel_view_panel_sure_button");
                _cancleBtnTxt.string = cv.config.getStringData("GameScene_sitDownLimit_panel_view_panel_cancel_button");
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_PAUSE_GAME_TIPS:  //牌局暂停弹框
                _gold_button.string = cv.config.getStringData("GameScene_pausePoker_panel_start_button");
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_OPEN_Security_Box:
                _sureBtnTxt.string = cv.config.getStringData("Safe_deposit_immediately");
                _cancleBtnTxt.string = cv.config.getStringData("Safe_continue_takeout");
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_MY_RED_PACKETS:
                _cancleBtnTxt.string = cv.config.getStringData("TipsPanel_cancel_button");
                _sureBtnTxt.string = cv.config.getStringData("TipsPanel_sure_button_redepackets");
                break;
            case cv.Enum.ButtonType.TWO_BUTTON_MTT_FRAME:
                _cancleBtnTxt.string = cv.config.getStringData("MTT_frame_know");
                _sureBtnTxt.string = cv.config.getStringData("MTT_frame_enter");
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_NETWORK:
                _gold_button.string = cv.config.getStringData("Hotupdate_retrybtn"); //重试
                break;
            case cv.Enum.ButtonType.TWO_BUTTON_SWITCH_TABLE:
                _cancleBtnTxt.string = cv.config.getStringData("MiniGames_Exit");
                _sureBtnTxt.string = cv.config.getStringData("MiniGames_Switch_table");
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_CUSTOMER_SERVICE:
                _sureBtnTxt.string = cv.config.getStringData("Contact_Customer_Service");
                _cancleBtnTxt.string = cv.config.getStringData("Cancel");
                break;

            case cv.Enum.ButtonType.TWO_BUTTON_FEATURE_HAND:
                _sureBtnTxt.string = cv.config.getStringData("game_review_feature_hand_submit");
                _cancleBtnTxt.string = cv.config.getStringData("TipsPanel_cancel_button");
                break;
            case cv.Enum.ButtonType.TWO_BUTTON_ENTER_GAME:
                _sureBtnTxt.string = cv.config.getStringData("minigame_new_room_enter");
                _cancleBtnTxt.string = cv.config.getStringData("TipsPanel_cancel_button");
                break;
            case cv.Enum.ButtonType.TWO_BUTTON_FOLD_CHECK:
                _sureBtnTxt.string = cv.config.getStringData("ActionTips1");
                _cancleBtnTxt.string = cv.config.getStringData("ActionTips6");
                break;
            case cv.Enum.ButtonType.TWO_BUTTON_GOTO_BACKPACK:
                _sureBtnTxt.string = cv.config.getStringData("goto_backpack");
                _cancleBtnTxt.string = cv.config.getStringData("Cancel");
                break;
            case cv.Enum.ButtonType.TWO_BUTTON_SQUID_JOIN:
                _sureBtnTxt.string = cv.config.getStringData("Return_To_Squid_Table");
                _cancleBtnTxt.string = cv.config.getStringData("Join_New_Squid_Table");
                break;
            case cv.Enum.ButtonType.OK_BUTTON:
                _gold_button.string = cv.config.getStringData("Safe_ok");
                break;
            default:
                //默认值
                _gold_button.string = cv.config.getStringData("TipsPanel_sure_button");
                _cancleBtnTxt.string = cv.config.getStringData("TipsPanel_cancel_button");
                _sureBtnTxt.string = cv.config.getStringData("TipsPanel_sure0_button");
                _gray_button.string = cv.config.getStringData("TipsPanel_sure0_button");
                break;
        }


    }

    //设置按钮坐标
    private setCurretnButtonPosY(posY: number) {
        this.goldButton.y = posY;
        this.grayButton.y = posY;
        this.cancelButton.y = posY;
        this.sureButton.y = posY;
    }

    getMessageImage() {
        return cc.find("Image_duanxin", this.msgNode).getComponent(cc.Sprite);
    }

    getMessageImageText(): cc.Label {
        return cc.find("Image_duanxin/text_duanxin", this.msgNode).getComponent(cc.Label);
    }
    
    getMessageText() {
        if (this.msgNode != null) {
            return cc.find("bgPanel/message_text", this.msgNode).getComponent(cc.Label)
        }
    }

    setMessageText(str: string) {
        this.content_Label.node.color = cc.color(225, 225, 225);
        this.content_Label.node.setContentSize(780, 280);

        this.tempText.string = str;
        const _labelWidth = cv.resMgr.getLabelStringSize(this.tempText, str).width
        this.tempText.node.active = false;

        const lineCount = _labelWidth / this.content_Label.node.getContentSize().width;

        if (lineCount <= 2) {
            //小于等于2行
            this.content_Label.fontSize = 48;
            this.content_Label.lineHeight = 67;
        } else {
            this.content_Label.fontSize = 40;
            this.content_Label.lineHeight = 56;
        }

        this.content_Label.string = cv.StringTools.calculateAutoWrapString(this.content_Label.node, str);
    }

    hideTipsPanel() {
        if (this && this.msgNode && cc.isValid(this.msgNode, true)) {
            this.msgNode.active = false;
        }
        this.setTheme(TipsPanelThemeType.Default);
    }

    getVisible(): boolean {
        if (this && this.msgNode && cc.isValid(this.msgNode, true)) {
            return this.msgNode.active;
        }
        else
            return false;
    }

    hideTipsByDelay() {
        if (this.msgNode) {
            let panel = this.msgNode;
            this.unschedule(this.scheduleUpdate);
            panel.active = false;
        }
        cv.MessageCenter.send("showSportsScene");
        this.setTheme(TipsPanelThemeType.Default);
        this.hideTipsCallback?.();
        this.hideTipsCallback = null;
    }

    setHideTipsCallBack(cb:Function) {
        this.hideTipsCallback = cb;
    }

    private onBtnSureClick() {
        cv.AudioMgr.playButtonSound('button_click');
        if (this.sureCallback != null) {
            this.sureCallback(cc.find("edit_text", this.msgNode).getComponent(cc.EditBox));
        }

        this.hideTipsByDelay();
    };

    private onBtnCancelClick() {
        cv.AudioMgr.playButtonSound('button_click');
        if (this.cancelCallback != null) {
            this.cancelCallback(cc.find("edit_text", this.msgNode).getComponent(cc.EditBox));
        }
        this.hideTipsByDelay();
    };

    private static instance: TipsPanel;

    public static getInstance(): TipsPanel {
        if (!this.instance || !this.instance.msgNode || !cc.isValid(this.instance.msgNode, true)) {
            this.instance = new TipsPanel();
        }
        return this.instance;
    }

    public destroyMsgNode() {
        cv.MessageCenter.unregister(cv.config.CHANGE_LANGUAGE, this.msgNode);
        cc.game.removePersistRootNode(this.msgNode);
        this.msgNode.destroy();
    }

    public scheduleUpdate(nbr: number) {
        this.time = this.time - 1;
        if (this.time >= 0) {
            this.down_txt.getComponent(cc.RichText).string = cv.StringTools.formatC(cv.config.getStringData("dialog_cutdown_sec"), this.time);
        } else {
            this.onBtnCancelClick();
        }
    }

    public haveMsgNode(): boolean {
        if (!this || !this.msgNode || !cc.isValid(this.msgNode, true)) {
            return false;
        }
        return true;
    }

    public showRichText(content: string, btnStyle: number,
        callback: Function, cancelCallback?: Function,
        needEditBox: boolean = false, titleString: string = "",
        horizontalAlign: number = cc.Label.HorizontalAlign.CENTER, allowWrapContent: boolean = true)
    {
        if (!this.msgNode) return;
        this.grayButton.active = false;
        this._panelTag = null;
        this.cutDownNode.active = false;
        this.cutDownIcon.active = false;

        cv.MessageCenter.send("HideWebview_ShowWindows");
        cv.MessageCenter.send("hide_bombInfoTips");
        cv.native.SYwebCloseChildWebview();
        this.msgNode.active = true;
        this.sureCallback = callback;
        this.cancelCallback = cancelCallback;
        this.showImage(needEditBox);
        this.showMsgMail(false);
        this.setButtonText(cv.Enum.ButtonType.TWO_BUTTON);
        this.messageEditBox.string = "";
        this.messageEditBox.placeholder = "";
        this.titleLabel.string = titleString;
        this.titleBG.active = titleString !== "";
        this.imageBG.active = false;

        this.bgPanel.setContentSize(cc.size(this.bgPanel.getContentSize().width, 520));

        this.content_Label.node.active = false;

        this.content_RichText.node.active = true;
        this.content_RichText.node.color = cc.color(225, 225, 225);
        this.content_RichText.node.setContentSize(780, 280);

        this.setCurretnButtonPosY(this.button_common_y);

        this.content_RichText.horizontalAlign = horizontalAlign; 
        this.content_RichText.string = !allowWrapContent? content: cv.StringTools.calculateAutoWrapString(this.content_RichText.node, content);

        this.SetButtonsStyle(btnStyle);
        this.setTheme(TipsPanelThemeType.Default);
    };

    public getRichTextNode(): cc.RichText {
        return this.content_RichText;
    }

    setTheme(themeType: TipsPanelThemeType): void {
        const theme = TipsPanelStyleModel.getInstance().getTheme(themeType);
        if (!theme) {
            return;
        }

        const { backgroundSize, headerPosition, headerSize, contentPosition, handerTouchEvent, contentFontSize, lineHeight } = theme;
        
        if (headerPosition) this.titleBG.setPosition(headerPosition);
        if (contentPosition) this.content_RichText.node.setPosition(contentPosition);
        if (handerTouchEvent) this.content_RichText.handleTouchEvent = handerTouchEvent;
        if (contentFontSize) this.content_RichText.fontSize = contentFontSize;
        if (lineHeight) this.content_RichText.lineHeight = lineHeight;
        if (headerSize) this.titleBG.setContentSize(headerSize);
        if (backgroundSize) this.bgPanel.setContentSize(backgroundSize);
    }

    public adaptUI() {
        if (this.haveMsgNode()) {
            cv.resMgr.adaptWidget(this.msgNode, true);
        }
    }
}
