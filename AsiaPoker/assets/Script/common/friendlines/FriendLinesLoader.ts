import { GameMain } from '../../components/game/dzPoker/GameMain';
import { JackfruitMain } from '../../components/game/jackfruit/JackfruitMain';
import FriendLinesController from '../friendlines/FriendLinesController';

export enum Tabletype {
    None = 0,
    NormalGame,
    JackFruit
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class FriendLinesLoader extends cc.Component {
    @property(cc.Prefab) normal_friendLines_prefab: cc.Prefab = null;
    @property(cc.Prefab) jackfruit_friendLines_prefab: cc.Prefab = null;
    private _friendLines: cc.Node = null;
    private _friendLinesController: FriendLinesController = null;

    public init(gameMain: GameMain | JackfruitMain, tableType: Tabletype) {
        switch (tableType) {
            case Tabletype.NormalGame:
                this._friendLines = cc.instantiate(this.normal_friendLines_prefab);
                break;
            case Tabletype.JackFruit:
                this._friendLines = cc.instantiate(this.jackfruit_friendLines_prefab);
                break;
            default:
                throw Error('Non-existent type');
        }

        this._friendLines.setParent(this.node);
        this._friendLines.setPosition(cc.Vec2.ZERO);
        this._friendLinesController = this._friendLines.getComponent(FriendLinesController);
        this._friendLinesController.initGameMain(gameMain);
    }
}
