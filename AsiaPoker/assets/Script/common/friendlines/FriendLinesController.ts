import { GameMain } from '../../components/game/dzPoker/GameMain';
import { Seat } from '../../components/game/dzPoker/Seat';
import { JackfruitMain } from '../../components/game/jackfruit/JackfruitMain';
import JackfruitSeat from '../../components/game/jackfruit/JackfruitSeat';
import cv from '../../components/lobby/cv';

const { ccclass, property } = cc._decorator;

@ccclass
export default class FriendLinesController extends cc.Component {
    // Normal Game
    @property(cc.Node) owner: cc.Node = null;
    @property(cc.Animation) ownerAnim: cc.Animation = null;
    @property(cc.Node) owner_heart: cc.Node = null;
    @property(cc.Node) player: cc.Node = null;
    @property(cc.Animation) playerAnim: cc.Animation = null;
    @property(cc.Node) player_heart: cc.Node = null;
    @property(cc.RichText) topMessage: cc.RichText = null;
    @property(cc.Label) levelName: cc.Label = null;
    @property(cc.ParticleSystem) owner_heart_particle: cc.ParticleSystem = null;
    @property(cc.ParticleSystem) player_heart_particle: cc.ParticleSystem = null;
    private _gameMain: GameMain | JackfruitMain = null;

    public initGameMain(gameMain: GameMain | JackfruitMain) {
        // Save GameMain
        this._gameMain = gameMain;
    }

    protected onLoad(): void {
        this.owner.active = false;
        this.owner_heart.active = false;
        this.player.active = false;
        this.player_heart.active = false;
        cv.MessageCenter.register('friendLines_Result', this._onFriendLinesTest.bind(this), this.node);
        cv.MessageCenter.register('on_intimacy', this._onIntimacy.bind(this), this.node);
    }

    protected onDestroy() {
        cv.MessageCenter.unregister('friendLines_Result', this.node);
        cv.MessageCenter.unregister('on_intimacy', this.node);
    }

    private _playAnimation(ownerPosition: cc.Vec2, playerPosition: cc.Vec2) {
        // Hands animation
        this.ownerAnim.play();
        this.playerAnim.play();
        this.ownerAnim.on(cc.Animation.EventType.FINISHED, (event: cc.Event): void => {
            this.ownerAnim.off(cc.Animation.EventType.FINISHED);
            this.owner.active = false;
        });

        this.playerAnim.on(cc.Animation.EventType.FINISHED, (event: cc.Event): void => {
            this.playerAnim.off(cc.Animation.EventType.FINISHED);
            this.player.active = false;
        });

        // Heart animation
        const vectorProportion = 0.2;
        const vectorOwner = new cc.Vec2(
            (ownerPosition.y - playerPosition.y) * vectorProportion,
            (playerPosition.x - ownerPosition.x) * vectorProportion
        );
        const vectorPlayer = new cc.Vec2(
            (playerPosition.y - ownerPosition.y) * vectorProportion,
            (ownerPosition.x - playerPosition.x) * vectorProportion
        );
        const ownerControlPoint = new cc.Vec2(
            (ownerPosition.x + playerPosition.x) / 2 + vectorOwner.x,
            (ownerPosition.y + playerPosition.y) / 2 + vectorOwner.y
        );
        const playerControlPoint = new cc.Vec2(
            (ownerPosition.x + playerPosition.x) / 2 + vectorPlayer.x,
            (ownerPosition.y + playerPosition.y) / 2 + vectorPlayer.y
        );
        this.owner_heart_particle.resetSystem();
        this.player_heart_particle.resetSystem();
        cc.tween(this.owner_heart)
            .bezierTo(0.8, this.owner_heart.position, ownerControlPoint, playerPosition)
            .call(() => {
                this.owner_heart.active = false;
            })
            .start();
        cc.tween(this.player_heart)
            .bezierTo(0.8, this.player_heart.position, playerControlPoint, ownerPosition)
            .call(() => {
                this.player_heart.active = false;
            })
            .start();
    }

    private _onIntimacy(data) {
        if (data.way !== 1) {
            return;
        }

        const otherSeat: Seat | JackfruitSeat = this._gameMain.getSeatByPlayerID(data.playerid);
        const selfSeat: Seat | JackfruitSeat = this._gameMain.getSeatByPlayerID(cv.dataHandler.getUserData().u32Uid);
        if (otherSeat === null || selfSeat === null) {
            return;
        }

        let worldPosOther: cc.Vec2;
        if (otherSeat instanceof Seat) {
            worldPosOther = otherSeat.node.getParent().convertToWorldSpaceAR(otherSeat.node.getPosition());
        } else if (otherSeat instanceof JackfruitSeat) {
            worldPosOther = otherSeat.head_img.getParent().convertToWorldSpaceAR(otherSeat.head_img.getPosition());
        }
        const otherSeatPos = this.node.convertToNodeSpaceAR(worldPosOther);

        let worldPosSelf: cc.Vec2;
        if (selfSeat instanceof Seat) {
            worldPosSelf = selfSeat.node.getParent().convertToWorldSpaceAR(selfSeat.node.getPosition());
        } else if (selfSeat instanceof JackfruitSeat) {
            worldPosSelf = selfSeat.head_img.getParent().convertToWorldSpaceAR(selfSeat.head_img.getPosition());
        }
        const selfSeatPos = this.node.convertToNodeSpaceAR(worldPosSelf);
        this._showFriendLinesAnimation(selfSeatPos, otherSeatPos, data.nickname, data.intimacy);
    }

    private _showFriendLinesAnimation(
        ownerPosition: cc.Vec2,
        playerPosition: cc.Vec2,
        playerName: string,
        levelNum: number
    ) {
        // Set onwer and player positions
        this.owner.setPosition(ownerPosition);
        this.owner_heart.setPosition(ownerPosition);
        this.player.setPosition(playerPosition);
        this.player_heart.setPosition(playerPosition);

        // Set top message and level name
        this.topMessage.string = cv.StringTools.formatC(
            cv.config.getStringData('Star_friend_update_new'),
            playerName,
            levelNum
        );
        this.levelName.string = cv.tools.GetFriendLevelName(levelNum);

        // OpenObject
        this.owner.active = true;
        this.owner_heart.active = true;
        this.player.active = true;
        this.player_heart.active = true;

        // Play animation
        this._playAnimation(ownerPosition, playerPosition);
    }

    // For Mock Tool Test
    private _onFriendLinesTest(msg: any) {
        const worldPosSelf = new cc.Vec2(cv.Number(msg.StartPosition.x), cv.Number(msg.StartPosition.y));
        const selfSeatPos = this.node.convertToNodeSpaceAR(worldPosSelf);
        const worldPosOther = new cc.Vec2(cv.Number(msg.EndPosition.x), cv.Number(msg.EndPosition.y));
        const otherSeatPos = this.node.convertToNodeSpaceAR(worldPosOther);
        this._showFriendLinesAnimation(
            selfSeatPos,
            otherSeatPos,
            cv.String(msg.PlayerName),
            cv.Number(msg.FriendShipLevel)
        );
    }
}
