import cv from "../../components/lobby/cv";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "./NDBSHandler";

export enum GeoComplyError {
    NONE = 0, // for windows - CLNT_OK
    REQUEST_TIMEOUT = 408,
    UNEXPECTED= 600,
    NETWORK_CONNECTION = 602,
    SERVER_COMMUNICATION = 603,
    CLIENT_SUSPENDED = 604,
    DISABLED_SOLUTION = 605,
    INVALID_LICENSE_FORMAT = 606,
    CLIENT_LICENSE_UNAUTHORIZED = 607,
    LICENSE_EXPIRED = 608,
    INVALID_CUSTOM_FIELDS = 609,
    REQUEST_CANCELLED_BY_APP = 610,
    REQUEST_CANCELED = 611,
    GEOLOCATION_IN_PROGRESS = 614,
    PERMISSIONS_NOT_GRANTED = 615, //same code used in macOS & windows (LOCAL_SERVICE_UNSUP_VER).
    GOOGLE_PLAY_SERVICE_NOT_FOUND = 616,
    DEVICE_CALLBACK_NOT_FOUND = 617,
    PLAY_INTEGRITY_API_NOT_FOUND = 618,
    IS_UPDATING_LOCATION = 630, // location data outdated for NDBS
    DISABLED_INDOOR_GEOLOCATION = 631,
    DISABLED_BLUETOOTH = 632,
    UNSUPPORTED_BLUETOOTH = 633, //same code used in iOS (PERMISSION_WHILE_IN_USE_BACKGROUND_MODE).
    INVALID_USER_INPUT = 635,
    PRECISE_LOCATION_PERMISSION_NOT_GRANTED = 637,
    BLUETOOTH_PERMISSIONS_NOT_GRANTED = 638,
    REASON_CODE_MISSING = 639,
    INVALID_HMAC = 640,
    XML_OMITTED = 650,

    // only for iOS
    BACKGROUND_MODE = 634,
    NOT_CONTAIN_ALL_PROTOCOLS = 636,

    // only for macOS
    NOT_CERTIFIED_BINARIES = 601,
    LOCAL_SERVICE_UNAVAILABLE = 612, //same code used in iOS (LOW_MEMORY).
    LOCAL_SERVICE_COMMUNICATION = 613,
    TRANSACTION_INTERRUPTED = 619,
    TRANSACTION_TIMEOUT = 620, // invalid server response for NDBS
    LOCATION_SERVICES_DISABLED = 621,
    NO_MEMORY = 689,
    WRONG_INTEGRATION = 698,
    BROWSER_NOT_COMPATIBLE = 801,
    BROWSER_GEOLOCATION_UNAVAILABLE = 802,
    BROWSER_GEOLOCATION_DENIED = 803,
    BROWSER_GEOLOCATION_TIMEOUT = 804,
    BROWSER_GEOLOCATION_UNKNOWN = 805
}

interface GC_ErrorObj {
    code: GeoComplyError | IpChangeError,
    needRetry?: boolean,
    message: string
    timestamp?: string,
}

export enum LocationError {
    GPS_LOCATION,
    WIFI_AND_MOBILE_NETWORK_LOCATION
}

export enum IpChangeError {
    NO_ERROR = 0,
    CONFIRM_SERVICE_STOPPED = 700,
    LICENSE_NOT_SET,
    CONFIG_NOT_DEFINED,
    SSL_ERROR,
    INVALID_IP_ADDRESS_FORMAT,
    NO_NETWORK_CONNECTION,
    HTTP_ERROR,
    MYIP_HOST_IS_UNREACHABLE,
    MYIP_SERVICE_IS_TIMEOUT = 710,
    UNKNOWN_ERROR = 720
}

export interface iGCUserData {
    licenseKey: string,
    userId: string,
    geolocationReason: string,
    phoneNo: string,
    isIpChange: boolean,
    enableIPService: boolean,
    cancelRequestOnLocationDisabled: boolean
}

export interface iGCLogEventData {
    startTime: number, //start epoch timestamp
}

enum GCRequestLogStatus {
    Requested = 'Requested',
    Success = 'Success',
    Failed = 'Failed',
    Canceled = 'Canceled'
}

export default class GeoComplyHandler {

    private static instance: GeoComplyHandler = null;

    public onGeoLocationSuccess: Function = null;
    public onGeoLocationFailed: Function = null;

    public requestCancelCallback: Function = null;

    // Client exception occured during initialize of client instance
    private clientException: string = null;

    private geoLocationError: string = null;

    private constructor() { }

    public static getInstance(): GeoComplyHandler {
        if (!GeoComplyHandler.instance) {
            GeoComplyHandler.instance = new GeoComplyHandler();
        }
        return GeoComplyHandler.instance;
    }

    /**
     * 
     * @returns return true in case of no client exception
     */
    public isClientInstatiate(): boolean {
        return this.clientException == null;
    }

    public getLastClientError(): number {
        return this.getErrorCode(this.clientException);
    }

    public getLastNativeError(): number {
        return this.getLastClientError() || this.getErrorCode(this.geoLocationError);
    }

    public isLastNativeErrorIsLocationServicesError(): any {
        if(cc.sys.os == cc.sys.OS_ANDROID || cc.sys.os == cc.sys.OS_IOS) {
            let eCode = this.getLastNativeError();
            if(eCode == GeoComplyError.PRECISE_LOCATION_PERMISSION_NOT_GRANTED ||
                eCode == GeoComplyError.REQUEST_CANCELED ||
                eCode == GeoComplyError.PERMISSIONS_NOT_GRANTED) {
                    return true;
            }
            return false;
        }
        return false;
    }

    public initSDK(lienceKey: string = ""): void {
        console.log("GeoComplyCocos InitSDK");
        this.clientException = null;
        this.geoLocationError = null;
        if (cc.sys.isBrowser) {
            NDBSHandler.getInstance().initSDK(lienceKey);
            return;
        }

        if (cc.sys.isNative) {
            if (cc.sys.os === cc.sys.OS_ANDROID) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/GeoComplySDK", 
                                                "startGeoComply", 
                                                "(Ljava/lang/String;)V", 
                                                lienceKey);
            }
            else if (cc.sys.os === cc.sys.OS_IOS) {
                cv.native.invokeSyncFunc(cv.nativeCMD.KEY_START_GEO_COMPLY);
            } 
        }
    }

    /**
     * Triggers geolocation request to the native geocomply SDK
     * @param userId UserId of the player
     * @param geolocationReason Reason for the geolocation request (Login, CashGame, Cashier, etc.)
     * @param phoneNo (optional) Phone no for the player
     */
    public requestGeoLocation(userData: iGCUserData): void {
        console.log("GeoComplyCocos triggerGeolocation##################");
        this.geoLocationError = null;
        if (cc.sys.isBrowser) {
            NDBSHandler.getInstance().requestGeolocation(userData);
            return;
        }
        if (cc.sys.isNative) { 
            if (cc.sys.os === cc.sys.OS_ANDROID) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/GeoComplySDK", "requestGeolocation", "(Ljava/lang/String;)Z", JSON.stringify(userData));
            }
            else if (cc.sys.os === cc.sys.OS_IOS) {
                cv.native.invokeSyncFunc(cv.nativeCMD.KEY_TRIGGER_GEO_LOCATION, userData);
            }
        }
    }

    public cancelOnGoingRequest(cancelReason: string = "Manual", onCancelCallback?: Function) {
        console.log("GeoComplyCocos cancelOnGoingRequest ##################");
        this.requestCancelCallback = null;
        if (cc.sys.isBrowser) {
            if (this.isGeoRequestInProcess()) {
                this.requestCancelCallback = onCancelCallback;
                NDBSHandler.getInstance().cancelGeolocationRequest();
            }
            return;
        }
        if (cc.sys.isNative) { 
            if(this.isGeoRequestInProcess()) {
                this.requestCancelCallback = onCancelCallback;
                if (cc.sys.os === cc.sys.OS_ANDROID) {
                    jsb.reflection.callStaticMethod("org/cocos2dx/javascript/GeoComplySDK", "cancelGeolcationRequest", "(Ljava/lang/String;)V", cancelReason);
                }
                else if (cc.sys.os === cc.sys.OS_IOS) {
                    jsb.reflection.callStaticMethod("GeoComplyHandler", "cancelGeolcationRequest:", cancelReason);
                }
            }
        }
    }

    /**
     * To check if there is any geo location request is already in process
     * Only for Android and iOS
     * @returns True if geo location request is in progress else False
     */
    public isGeoRequestInProcess(): boolean {
        console.log("GeoComplyCocos isGeoRequestInProcess ##################");
        let check: boolean = false;
        if (cc.sys.isBrowser) {
            check =  NDBSHandler.getInstance().IsGeolocationInProgress;
            return check;
        }
        if (cc.sys.isNative) { 
            if (cc.sys.os === cc.sys.OS_ANDROID) {
                check =  jsb.reflection.callStaticMethod("org/cocos2dx/javascript/GeoComplySDK", "isRequestInProgress", "()Z");
            }
            else if (cc.sys.os === cc.sys.OS_IOS) {
                check = jsb.reflection.callStaticMethod("GeoComplyHandler", "isRequestInProgress");
            }
            console.log("GeoComplyCocos isGeoRequestInProcess result : ", check);
        }
        return check;
    }

    /**
     * Open Location service setting page
     */
    public openLocationSettings() {
        console.log("GeoComplyCocos isGeoRequestInProcess ##################");
        if (cc.sys.isBrowser) {
            // TODO: implement location service request on web if NDBS hint UI is not used
            return;
        }
        if (cc.sys.isNative) { 
            if (cc.sys.os === cc.sys.OS_ANDROID) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/GeoComplySDK", "openLocationSettings", "()V");
            }
            else if (cc.sys.os === cc.sys.OS_IOS) {
                cv.permMgr.openAppSettings();
            }
        }
    }

    /**
     * Callback from native plugin
     * this is called when geocomply client gets initialized successFully without any error
     */
    public static onGeoComplyClientInitializedSuccessFully() {
        console.log("GeoComplyCocos onGeoComplyClientInitializedSuccessFully");
        GeoComplyHandler.getInstance().clientException = null;
        if(GeoComplyHandler.getInstance().requestCancelCallback) {
            GeoComplyHandler.onGeoComplyRequestCanceled(true, "Geocomply client has been re-created");
        }
    }

    /**
     * Callback from native plugin
     * this is called when ongoing geocomply location client gets canceled
     */
    public static onGeoComplyRequestCanceled(success: boolean = false, description: string = "") {
        console.log(`GeoComplyCocos onGeoComplyRequestCanceled : ${!!success} : ${description}`);
        GeoComplyHandler.getInstance().requestCancelCallback?.(!!success, description);
        GeoComplyHandler.getInstance().requestCancelCallback = null;
    }
    
    /**
     * Callback from native plugin
     * this is called if exception occurs in GeoComplySDK
     * @param error json string of the exception error object
     */
    public static onGeoComplyClientException(error: string): void {
        // parse json
        console.log("GeoComplyCocos onGeoComplyClientException: ", error.toString());
        GeoComplyHandler.getInstance().clientException = error;
        GeoComplyHandler.getInstance().handleSDKErrors(error, true);
    }

    /**
     * Callback from native plugin
     * this is called on geo location success
     * @param data encrypted string of the location
     */
    public static onGeoComplyLocationSuccess(data: string): void {
        // data is encrypted string
        console.log("GeoComplyCocos onGeoComplyLocationSuccess: ", data);
        GeoComplyHandler.getInstance().onGeoLocationSuccess?.(data);
        GeoComplyHandler.getInstance().geoLocationError = null;
    }

    /**
     * Callback from native plugin
     * this is called if geo location fails
     * @param error json string of the exception error object
     * @param message string of the exception error message
     */
    public static onGeoComplyLocationFailure(error: string, message: string): void {
        // parse json
        console.log("GeoComplyCocos onGeoComplyLocationFailure: ", message);
        console.log("GeoComplyCocos Error code: ", error);

        GeoComplyHandler.getInstance().onGeoLocationFailed?.(error);
        GeoComplyHandler.getInstance().handleSDKErrors(error, true);
        GeoComplyHandler.getInstance().geoLocationError = error;
    }

    public handleSDKErrors(error: string, fromNative: boolean = false) {
        let eCode = this.getErrorCode(error);
        switch(eCode) {
            case GeoComplyError.GEOLOCATION_IN_PROGRESS:
                // location request is already in progress
                break;

            case GeoComplyError.INVALID_LICENSE_FORMAT:
            case GeoComplyError.LICENSE_EXPIRED:
                if (fromNative) {
                    cv.geoComplyManager?.clearLicenseKey();
                    cv.geoComplyManager?.renewLicense();
                }
                else {
                    cv.geoComplyManager?.nativeErrorHandler(eCode)
                }
                break;

            case GeoComplyError.PERMISSIONS_NOT_GRANTED:
                if(cc.sys.os == cc.sys.OS_ANDROID) {
                    cv.geoComplyManager?.forceCloseCheckInProgressPopup();
                    cv.geoComplyManager?.showLocationPermissionPopup(eCode);
                }
                else {
                    cv.geoComplyManager?.nativeErrorHandler(eCode);
                }
                break;
            case GeoComplyError.UNEXPECTED:
                case GeoComplyError.NETWORK_CONNECTION:
                case GeoComplyError.SERVER_COMMUNICATION:
                    if (cv.geoComplyManager?.checkForEnablingBypass(eCode)) {
                        cv.geoComplyManager?.forceCloseCheckInProgressPopup();
                    }
                    break;
            case GeoComplyError.LOCAL_SERVICE_COMMUNICATION:
            case GeoComplyError.LOCAL_SERVICE_UNAVAILABLE:
                    cv.geoComplyManager?.nativeErrorHandler(eCode);
                break;
            
            case GeoComplyError.PRECISE_LOCATION_PERMISSION_NOT_GRANTED:
            case GeoComplyError.REQUEST_CANCELED:
                if(cc.sys.os == cc.sys.OS_IOS || cc.sys.os == cc.sys.OS_ANDROID) {
                    cv.geoComplyManager?.showEnableLocationServiceHelpPopup(eCode);
                }
                else {
                    cv.geoComplyManager?.nativeErrorHandler(eCode);            
                }
                break;
            
            case GeoComplyError.SERVER_COMMUNICATION:
                if (cc.sys.isBrowser && !GeoComplyHandler.getInstance().isGeoRequestInProcess()) {
                    // not needed to handle - handled by client error popup
                }
                else
                {
                    cv.geoComplyManager?.nativeErrorHandler(eCode);
                }
            break;
            
            default:
                cv.geoComplyManager?.nativeErrorHandler(eCode);
                break;
        }
    }

    public getErrorCode(error) : number {
        if(cv.StringTools.isNumber(error)) {
            return cv.Number(error);
        }
        
        let errorJSON: GC_ErrorObj = cv.tryParseJSON(error);
        if (errorJSON && errorJSON.code) {
            return cv.Number(errorJSON.code);
        }
        return 0;
    }
    
    /**
     * Callback from native plugin
     * this is called if IP of the client changes
     * @param ip ip of the client
     */
     public static onGeoComplyIPChanged(ip: string): void {
         console.log("GeoComplyCocos onGeoComplyIPChanged: ", ip);
         // handle client Ip change
         // custom field with custom field ID “geo_ip_detect"

         cv.geoComplyManager?.scheduleGeoTokenTimeout(true);
    }

    /**
     * Callback from native plugin
     * this is called if IP change detection failed
     * @param error json string of the exception error object
     */
    public static onGeoComplyIPFailed(error: string): void {
        console.log("GeoComplyCocos onGeoComplyIPFailed: ", error);
        GeoComplyHandler.getInstance()?.handleSDKErrors(error);
    }

     /**
     * Callback from native plugin
     * this is called if Location services are disabled by user in device 
     * @param error json string of the error
     */
    public static onGeoComplyLocationDisabled(error: string): void {
        console.log("GeoComplyCocos onGeoComplyLocationDisabled: ", error);

        if (cv.geoComplyManager?.isLocationServicesEnabled) {
            cv.geoComplyManager.isLocationServicesEnabled = false;
        }
    }
}

window.GeoComplyClientInitializedSuccessFully = GeoComplyHandler.onGeoComplyClientInitializedSuccessFully;
window.GeoComplyRequestCanceled = GeoComplyHandler.onGeoComplyRequestCanceled;
window.GeoComplyClientException = GeoComplyHandler.onGeoComplyClientException;
window.GeoComplyLocationSuccess = GeoComplyHandler.onGeoComplyLocationSuccess;
window.GeoComplyLocationFailure = GeoComplyHandler.onGeoComplyLocationFailure;
window.GeoComplyIPChanged = GeoComplyHandler.onGeoComplyIPChanged;
window.GeoComplyIPFailed = GeoComplyHandler.onGeoComplyIPFailed;
window.GeoComplyLocationDisabled = GeoComplyHandler.onGeoComplyLocationDisabled;
