import cv from "../../components/lobby/cv";
import { ENV_KEY } from "../tools/Config";
import { iGCUserData } from "./GeoComplyHandler";

export enum GC_REASON_CODE {
    LOGIN = 1,
    PREWAGER = 2,
    INTERVAL = 3,
    IPCHANGE = 4,
    USER_DRIVEN_RETRY = 5,
    POST_INTERVAL_FOREGROUND = 6,
    GAME_LAUNCHER_SWITCH = 7,
    REGISTRATION = 8,
    ACCOUNT_CHANGES_NAME_ADD = "9a",
    ACCOUNT_CHANGES_NAME_CHANGE = "9b",
    ACCOUNT_CHANGES_NAME_REMOVE = "9c",
    ACCOUNT_CHANGES_PHONE_ADD = "9d",
    ACCOUNT_CHANGES_PHONE_CHANGE = "9e",
    ACCOUNT_CHANGES_PHONE_REMOVE = "9f",
    ACCOUNT_CHANGES_EMAIL_ADD = "9g",
    ACCOUNT_CHANGES_EMAIL_CHANGE = "9h",
    ACCOUNT_CHANGES_EMAIL_REMOVE = "9i",
    ACCOUNT_CHANGES_PASSWORD = "9j",
    ACCOUNT_CHANGES_SECURITY_ADD = "9k",
    ACCOUNT_CHANGES_SECURITY_REMOVE = "9l",
    ACCOUNT_CHANGES_PHYSICAL_ADDRESS_ADD = "9m",
    ACCOUNT_CHANGES_PHYSICAL_ADDRESS_CHANGE = "9n",
    ACCOUNT_CHANGES_PHYSICAL_ADDRESS_REMOVE = "9o",
    ACCOUNT_CHANGES_CONTACTS_ADD = "9p",
    ACCOUNT_CHANGES_CONTACTS_CHANGE = "9q",
    ACCOUNT_CHANGES_CONTACTS_REMOVE = "9r",
    PAYMENT_INFORMATION_FUNDING_METHOD_ADD = "10a",
    PAYMENT_INFORMATION_FUNDING_METHOD_CHANGE = "10b",
    PAYMENT_INFORMATION_FUNDING_METHOD_REMOVE = "10c",
    PAYMENT_INFORMATION_WITHDRAWAL_METHOD_ADD = "10d",
    PAYMENT_INFORMATION_WITHDRAWAL_METHOD_CHANGE = "10e",
    PAYMENT_INFORMATION_WITHDRAWAL_METHOD_REMOVE = "10f",
    TRANSACTION_DEPOSIT = "11a",
    TRANSACTION_WITHDRAWAL = "11b",
    TRANSACTION_PURCHASE_WAGER = "11c",
    TRANSACTION_SELL = "11d",
    TRANSACTION_TRANSFER_SAME_USER = "11e",
    TRANSACTION_TRANSFER_DIFFERENT_USER = "11f",
    TRANSACTION_NAVIGATION_BALANCE = "12a",
    TRANSACTION_NAVIGATION_TRANSACTION_HISTORY = "12b",
    TRANSACTION_NAVIGATION_TRANSACTION_DETAILS = "12c",
    TRANSACTION_NAVIGATION_PROFILE = "12d",
    TRANSACTION_NAVIGATION_HELP_CENTER = "12e",
    TRANSACTION_NAVIGATION_TERMS_OF_USE = "12f",
    TRANSACTION_NAVIGATION_CONTACTS = "12g"
}

export enum GC_NDBS_EVENTS {
    abort = "abort",
    before = "before",
    browserIncompatible = "browser.incompatible",
    initFailed = "init.failed",
    reviseFailed = "revise.failed",
    configFailed = "config.failed",
    browserFailed = "browser.failed",
    engineFailed = "engine.failed",
    initSuccess = "init.success",
    configSuccess = "config.success",
    browserSuccess = "browser.success",
    engineSuccess = "engine.success",
    success = "success",
    failed = "failed",
    hint = "hint",
    allFailed = "*.failed",
    all = "**"
}

interface iMessage {
    code?: number;
    message?: string;
    errorCode?: number;
    errorMessage?: string;
}

export default class NDBSHandler {

    private static instance: NDBSHandler = null;
    private geoClient: any = null;

    private isIpListenerEnabled: boolean = false;
    private isGeolocationInProgress: boolean = false;
    
    public get IsGeolocationInProgress() : boolean {
        return this.isGeolocationInProgress;
    }    

    private constructor() {
        if (cc.sys.isBrowser) {
            console.log("NDBS constructor");
            let script = window.document.createElement('script');
            // script.src = './libs/js/gc-html5.js'; 
            let ndbsURL:string = cv.config.GET_ENV_DATA(ENV_KEY.NDBS_URL) as string;

            script.src = ndbsURL;
            script.type = 'text/javascript';
            script.onload = () => {
                console.log("NDBS Init");
                NDBSHandler.getInstance().initSDK();
            };
            window.document.getElementsByTagName('head')[0].appendChild(script);
        }
    }

    public static getInstance(): NDBSHandler {
        if (!NDBSHandler.instance) {
            NDBSHandler.instance = new NDBSHandler();
        }
        return NDBSHandler.instance;
    }

    public setNDBSLicense(lienceKey: string = ""): boolean {
        console.log("geocomplycocos setNDBSLicense");
        console.log(this.geoClient);
        console.log(lienceKey);
        if (!cc.sys.isBrowser || !this.geoClient || !lienceKey) return false;
        this.geoClient.setLicense(lienceKey);
        return true;
    }

    public initSDK(lienceKey: string = ""): void {
        if (!cc.sys.isBrowser) return;

        if (!window.GcHtml5) return;

        let geoClient = this.startGeoComply();

        if (geoClient) {
            console.log("GeoComplyCocos InitSDK");

            this.addNativeListeners(geoClient);
            this.setAllowHint(true);

            if (lienceKey) geoClient.setLicense(lienceKey)
        }
    }

    private startGeoComply(): any {
        if (!cc.sys.isBrowser)
            return;

        return window.GcHtml5?.createClient?.(null, function (ts, message, level) {
            // handle error
        });
    }

    private addNativeListeners(geoClient: any): any {
        if (!cc.sys.isBrowser)
            return;
        if (this.geoClient) this.removeNativeListeners();

        this.geoClient = geoClient;

        // this.geoClient.events.on('before', function () {
        //     console.log("NDBS before");
        // });
        this.geoClient.events.on(GC_NDBS_EVENTS.abort, () => {
            console.log("NDBS abort");
            this.onGeolocationCancelled();
        });

        this.geoClient.events.on(GC_NDBS_EVENTS.initSuccess, function () {
            console.log("NDBS init.success");
            window.GeoComplyClientInitializedSuccessFully();
        });

        this.geoClient.events.on(GC_NDBS_EVENTS.engineSuccess, (text, xml) => {
            console.log("NDBS engine.success");
            // handled in event GC_NDBS_EVENTS.success
            console.log(text);
            console.log(xml);
        });

        this.geoClient.events.on(GC_NDBS_EVENTS.allFailed, function (code, message) {
            console.log(this.event);
            console.log(code);
            console.log(message);
        });

        this.geoClient.events.on(GC_NDBS_EVENTS.engineFailed, (code, message) => {
            console.log("NDBS engine.failed");
            //following error codes: 600, 602, 603, 604,605, 607, 608, 609, 620
            // handled in event GC_NDBS_EVENTS.failed
            // this.onGeolocationFailed(text, xml);

        });

        this.geoClient.events.on(GC_NDBS_EVENTS.success, (text, xml) => {
            console.log("NDBS success");
            console.log(text);
            console.log(xml);
            // logXml(text);
            // if (xml) {
            //     var gcTransaction = xml.getElementsByTagName('gc_transaction').item(0);
            //     if (gcTransaction) {
            //         gcTransaction = gcTransaction.textContent || gcTransaction.innerText;
            //     }
            // }
            this.onGeolocationAvailable(text, xml);

        });

        this.geoClient.events.on(GC_NDBS_EVENTS.failed, (code, message) => {
            let data = {
                code: code,
                message: message
            };
            console.log("NDBS failed: " + JSON.stringify(data));
            this.onGeolocationFailed(code, message);
        });

        this.geoClient.events.on(GC_NDBS_EVENTS.hint, (reason, hint) => {
            console.log("NDBS hint: " + hint);
            this.onLocationServicesDisabled(reason, hint);
        });
        // this.geoClient.events.on('browser.failed', function (err) {
        //     console.log("NDBS browser.failed");
        // geolocationResult = err;
        // document.title = err;
        // });
        window.GcHtml5.onMyIpSuccess = (ip: string) => {
            // to handle ip changes
            window.GcHtml5.ackMyIPSuccess();
            this.onMyIpSuccess(ip);
        };
        window.GcHtml5.onMyIpFailure = (data: iMessage) => {
            this.onMyIpFailure(data);
        };
        this.geoClient.events.on(GC_NDBS_EVENTS.all, function () {
            // all events for logging
            console.log("NDBS event: " + this.event);
            console.log(arguments);
        });

    }

    private removeNativeListeners(): any {
        if (!cc.sys.isBrowser)
            return;
        if (this.geoClient) {
            this.geoClient.removeAllListeners([
                GC_NDBS_EVENTS.abort,
                GC_NDBS_EVENTS.engineSuccess,
                GC_NDBS_EVENTS.engineFailed,
                GC_NDBS_EVENTS.success,
                GC_NDBS_EVENTS.failed,
                GC_NDBS_EVENTS.hint,
                GC_NDBS_EVENTS.initSuccess,
                GC_NDBS_EVENTS.all,
            ]);
        }
    }

    /**
     * Ip change callback
     * @param ip client's new ip
     */
    private onMyIpSuccess(ip: string) {
        window.GeoComplyIPChanged(ip);
    }

    /**
     * IP failure callback
     * @param data iMessage
     */
    private onMyIpFailure(data: iMessage) {
        this.isIpListenerEnabled = false;
        window.GeoComplyIPFailed({ code: data.errorCode, message: data.errorMessage });
    }

    /**
     * Geolocation available callback
     * @param text 
     * @param xml 
     */
    private onGeolocationAvailable(text: string, xml: XMLDocument) {
        window.GeoComplyLocationSuccess(text);
        this.isGeolocationInProgress = false;
    }

    /**
     * Geolocation failed callback
     * @param code error code
     * @param message error message
     */
    private onGeolocationFailed(code: number, message: string) {
        window.GeoComplyLocationFailure(code, message);
        this.isGeolocationInProgress = false;
    }

    /**
     * Location services disabled callback
     * @param reason 
     * @param hint 
     */
    private onLocationServicesDisabled(reason: string, hint: string) {
        // need to set allowHint false in init before showing own popup
        // window.GeoComplyLocationDisabled(reason);
    }

    /**
     * Geolocation cancelled callback
     */
    private onGeolocationCancelled() {
        window.GeoComplyRequestCanceled(true);
        this.isGeolocationInProgress = false;
    }

    /**
     * Geolocation exception callback
     */
    private onClientException(code: number, message: string) {
        window.GeoComplyClientException({ code, message });
    }

    /**
     * Start IP service
     * @param license NDBS sdk license
     * @param resumable resume service automatically after refresh
     */
    public startIpService(license: string, resumable: boolean) {
        if (!cc.sys.isBrowser || this.isIpListenerEnabled || !window.GcHtml5)
            return;
        this.isIpListenerEnabled = true;
        window.GcHtml5.startMyIpService({
            license: license,
            resumable: resumable
        });
    }

    /**
     * Stop IP service
     */
    public stopIpService() {
        if (!cc.sys.isBrowser || !window.GcHtml5)
            return;
        this.isIpListenerEnabled = false;
        window.GcHtml5.stopMyIpService();
    }

    /**
     * Initiates geopacket request to NSBS sdk
     * @param userData data required for geolocation request
     */
    public requestGeolocation(userData: iGCUserData) {
        if (!cc.sys.isBrowser)
            return;
        this.setData(userData);
        this.geoClient.requestGeolocation();
        this.isGeolocationInProgress = true;
    }

    /**
     * Cancell geopacket request to NSBS sdk
     */
    public cancelGeolocationRequest() {
        if (!cc.sys.isBrowser)
            return;
        this.geoClient.abort();
    }

    /**
     * Checks if webpage is in focus
     * @returns boolean webpage is active or not
     */
    public isActive(): boolean {
        return this.geoClient?.isActive();
    }

    // public isRequestInProgress() {

    // }

    /**
     * Show hints (troubleshooter messages) for the NDBS sdk
     * @param status boolean
     */
    public setAllowHint(status: boolean) {
        if (!cc.sys.isBrowser)
            return;
        this.geoClient.allowHint(status);
    }

    /**
     * Set predefined reason code
     * @param reasonCodeValue 
     */
    public setReasonCode(reasonCodeValue: GC_REASON_CODE) {
        if (!cc.sys.isBrowser)
            return;
        this.geoClient.setReasonCode(reasonCodeValue);
    }

    /**
     * Request geopacket with timeout
     * @param userData data required for geolocation request
     * @param timeout timeout in seconds
     */
    public requestWithTimeout(userData: iGCUserData, timeout: number) {
        if (!cc.sys.isBrowser)
            return;
        this.setData(userData);
        this.geoClient.requestWithTimeout(timeout);
        this.isGeolocationInProgress = true;
    }

    /**
     * Sets data to NDBS sdk instance
     * @param userData data required for geolocation request
     */
    private setData(userData: iGCUserData) {
        if (!cc.sys.isBrowser)
            return;
        // this.setAllowHint(true);
        // this.setReasonCode();
        this.geoClient.setLicense(userData.licenseKey);
        this.geoClient.setUserId(userData.userId);
        this.geoClient.setReason(userData.geolocationReason);
        // this.geoClient.setUserSessionId("");
        // this.geoClient.setLang("");
        if (userData.isIpChange) {
            this.geoClient.customFields.set("geo_ip_detect", "gc_ip_change_detect");
        }
        else {
            this.geoClient.customFields.remove("geo_ip_detect", "gc_ip_change_detect");
        }

        if (userData.enableIPService && !this.isIpListenerEnabled) {
            this.startIpService(userData.licenseKey, true);
        }
    }

    /**
     * Invalidate user session
     */
    public invalidateUserSession() {
        if (!cc.sys.isBrowser)
            return;
        this.geoClient.invalidateUserSession();
    }
}
