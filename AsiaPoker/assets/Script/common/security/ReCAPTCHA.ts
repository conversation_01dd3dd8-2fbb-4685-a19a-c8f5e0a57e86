import cv from "../../components/lobby/cv";
import { ENV_MODE } from "../tools/Config";

export class ReCAPTCHA {
    private static instance: ReCAPTCHA;
    private androidRecaptchaKey: string = "6LeRkcMZAAAAAKIJkdAWetQW66S4ZtMy-Nt1vynu";
    private iosRecaptchaKey: string = "6LceI8YZAAAAABg9e69x0brJjqvf6sBW-ZRKTZWD";
    private rejectThreshold: number = -1; // reCAPTCHA Enterprise returns a score (1.0 is very likely a good interaction, 0.0 is very likely a bot).
    private isActive: boolean = true;  // enable/disable captcha check

    private responseCallback: Function = null;

    public static getInstance(): ReCAPTCHA {
        if (!ReCAPTCHA.instance) {
            ReCAPTCHA.instance = new ReCAPTCHA();
        }
        return ReCAPTCHA.instance;
    }

    private constructor() {
        this.initRecaptcha();
    }

    /** Initialize Google reCaptcha native plugin */
    private initRecaptcha(): void {

        this.isActive = cv.config.GET_DEBUG_MODE() === ENV_MODE.PROD;

        if (this.isActive == false)
            return;

        if (cc.sys.isNative) {//recaptcha is only available for ios and android
            if (cc.sys.os == cc.sys.OS_ANDROID) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "initRecaptcha", "(Ljava/lang/String;)V", this.androidRecaptchaKey);
            }
            else if (cc.sys.os == cc.sys.OS_IOS) {
                jsb.reflection.callStaticMethod("RecaptchaWrapper", "initRecaptchaWithKey:", this.iosRecaptchaKey);
            }
        }
    }

    /**
     * Check for captcha score and return if passed of not, with the help of callback function
     * @param callback Callback function for response with signature Function(boolean)
     */
    public checkRecaptcha(callback: Function): void {
        if (this.isActive == false) {
            if (callback)
                callback(true);
            return;
        }

        if (this.rejectThreshold < 0) {
            this.responseCallback = null;
            if (callback)
                callback(true);
        }
        else {
            this.responseCallback = callback;
        }

        if (cc.sys.isNative) {//recaptcha is only available for ios and android
            if (cc.sys.os == cc.sys.OS_ANDROID) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "getRecaptchaToken", "()V");
            }
            else if (cc.sys.os == cc.sys.OS_IOS) {
                jsb.reflection.callStaticMethod("RecaptchaWrapper", "getRecaptchaToken");
            }
            else {
                if (this.responseCallback)
                    this.responseCallback(true);
            }
        }
        else {
            if (this.responseCallback)
                this.responseCallback(true);
        }
    }

    /**
     * on recaptcha token received from native plugin handler
     * send token and key to backend REST API to calculate the score
     * @param token token generated by native plugin
     * https://recapcha-pkw-prod.a5-labs-cloud.com
     * https://kyc-pkw-prod.a5-labs-cloud.com
     * 
     * https://api.spicyonion.net/
     */
    public onRecaptchaTokenReceived(token: string): void {
        let urlData = cv.domainMgr.getServerInfo();
        let url: string = urlData.recaptcha + cv.config.getStringData("WEB_API_RECAPTCHA", true);
        // url += cv.dataHandler.getUserData().user_id;
        let obj = {
            "token": token,
            "siteKey": this.getSiteKey(),
        };
        cv.http.sendRequest(url, obj, this.onRecaptchaResultSuccess.bind(this), cv.http.HttpRequestType.POST,null,false,true);
    }

    /**
     * on get recaptcha details with success handler
     * @param value recaptcha details (score and reason) { "score": 0, "reason": ["string"] }
     */
    protected onRecaptchaResultSuccess(value: any): void {
        console.log("recaptcha result: " + JSON.stringify(value));
        if (value["score"] == null || this.isRejected(value["score"])) {
            this.onRecaptchaResultFailed(null);
        }
        else {
            if (this.responseCallback)
                this.responseCallback(true);
        }
    }

    /**
     * on recaptcha failed handler
     * TODO: show custom captcha to be solved by user
     * @param error why did recaptcha failed
     */
    protected onRecaptchaResultFailed(error: any): void {
        cv.TT.showMsg(cv.config.getStringData("recaptcha-failed"), cv.Enum.ToastType.ToastTypeError);
        if (this.responseCallback)
            this.responseCallback(false);
    }

    /** get captcha key based on current platform */
    public getSiteKey(): string {
        if (cc.sys.os == cc.sys.OS_ANDROID)
            return this.androidRecaptchaKey;
        else if (cc.sys.os == cc.sys.OS_IOS)
            return this.iosRecaptchaKey;
        else
            return "";
    }

    /**
     * reCAPTCHA Enterprise returns a score (1.0 is very likely a good interaction, 0.0 is very likely a bot).
     * @param score captcha score
     */
    public isRejected(score: number): boolean {
        if (score <= this.rejectThreshold)
            return true;
        return false;
    }

    /**
     * Callback function for native plugin
     * @param token token generated by native plugin
     */
    public static NativeReCAPTCHACallback(token: string): void {
        ReCAPTCHA.getInstance().onRecaptchaTokenReceived(token);
    }
}

window.NativeReCAPTCHACallback = ReCAPTCHA.NativeReCAPTCHACallback;