import GeoComplyCheckPopup, { eGeoComplyCheckPopupIconType } from "../../components/game/dzPoker/geocomply/GeoComplyCheckPopup";
import GeoComplyErrorPopup from "../../components/game/dzPoker/geocomply/GeoComplyErrorPopup";
import cv from "../../components/lobby/cv";
import Geo<PERSON>omply<PERSON><PERSON><PERSON>, { GeoComplyError, iGCUserData } from "./GeoComplyHandler";

interface iLicenseKey {
    key : string,
    expiry : number
}

interface iGeoToken {
    token : string,
    expiry : number,
    latitude: number,
    longitude: number,
    geolocationSource: string,  // one of ip, wifi, gps, gsm
}

interface iGeoComplyCheckError {
    code: string,
    reason: {
        label : string,
        message : string,
        payload : {
            ruleViolations: string[],
            software: iGeoComplyBlockedSoftware[]
        }
    }
}

interface iGeoComplyBlockedSoftware {
    label: string,
    name: string,
    type: string
}

interface iGeoComplyCheckErrorSimplified {
    code: string,
    subRules: string[],
    softwareNames: string[],
}

export default class GeoComplyManager extends cc.Object {

    private static instance: GeoComplyManager = null;

    private constructor() {
        super();
        if (cv.config.BYPASS_GEOCOMPLY) {
            this._maxRetryCount = 1; // turn off retry when in offline mode
        }
        if(cv.config.USE_GEOCOMPLY) {
            cv.MessageCenter.register(cv.Enum.geocomplyAction.OnLoginSuccess, this.onLoginSccess.bind(this), this);
            cv.MessageCenter.register(cv.Enum.geocomplyAction.onGetGeocomplyConfig, this.onGetGeocomplyConfig.bind(this), this);
            cv.MessageCenter.register(cv.Enum.geocomplyAction.onGeoComplyLicenseReponse, this.onGetLicenseKeySuccess.bind(this), this);
            cv.MessageCenter.register(cv.Enum.geocomplyAction.onGeoComplyStoreTokenReponse, this.onGetGeoTokenSuccess.bind(this), this);
            cv.MessageCenter.register("onLogout", this.onLogoutSccess.bind(this), this);
        
            GeoComplyCheckPopup.getInstance();
            GeoComplyErrorPopup.getInstance();
            GeoComplyHandler.getInstance().initSDK();
            GeoComplyHandler.getInstance().onGeoLocationSuccess = this.onGetGeoLocationPackageSuccess.bind(this);
            GeoComplyHandler.getInstance().onGeoLocationFailed = this.onGetGeoLocationPackageFailed.bind(this);

            this.locationServicesCache = false;
        }
    }

    public static getInstance(): GeoComplyManager {
        if (!GeoComplyManager.instance) {
            GeoComplyManager.instance = new GeoComplyManager();
        }
        return GeoComplyManager.instance;
    }

    public get bypassGeoComply(): boolean {
        return cv.config.BYPASS_GEOCOMPLY || cv.config.GC_IS_USER_WHITELISTED || this.disableGeoRestriction;
    }

    public set setGeoRestriction(status: boolean) {
        console.log("geocomplycocos setGeoRestriction");
        console.log("_disableGeoRestriction = " + status);
        this.disableGeoRestriction = status;          
        if (status) {
            this.schedulePollingService();
            this._maxRetryCount = 1;
        }    
        else
        {
            this.clearPollingScheduler();
            this._maxRetryCount = 3;
        }
    }
    /**
     * Schedule geocomply polling service
     */
    private schedulePollingService() {
        this.clearPollingScheduler();
        this.geocomplyInterval = setInterval(() => {
            console.log("GeoComplyCocos geocomplyInterval");
            this.renewLicense(true);
        }, this.geocomplyPollingInterval);
    }
    /**
     * Clear geocomply polling service
     */
    private clearPollingScheduler() {
        if (this.geocomplyInterval) {
            clearInterval(this.geocomplyInterval);
            this.geocomplyInterval = null;
        }
    }

    private _currentRetryCount = {
        licenseKey : 0,
        geoToken: 0,
        geoPackage: 0
    };

    private getLicenseKeyTimeout: any = null;
    private getTokenTimeout: any = null;
    private packageTimeout: number = 2 * 60; // seconds
    private getPackageTimeout: any = null;

    private license: iLicenseKey = {
        key : "",
        expiry : 0
    }

    private geoPackage: string = null;
    private geoToken: iGeoToken = {
        token : "",
        expiry : 0,
        latitude : 0,
        longitude : 0,
        geolocationSource : "",
    }

    private _tokenExpiryBuffer = 90;

    private _maxRetryCount = 3;

    private isGeoComplyCheckPopupShownOnceAfterLogin = false;

    private _isAccountVerificationInProgress: boolean = false;
    
    public get isAccountVerificationInProgress() : boolean {
        return this._isAccountVerificationInProgress;
    }

    public set isAccountVerificationInProgress(bool: boolean) {
        this._isAccountVerificationInProgress = bool;
    }

    private getTokenSuccessCallback: Function[] = [];
    private getTokenErrorCallback: Function[] = [];
    private getTokenCancleCallback: Function[] = [];

    private initAllowed: boolean = false;

    // flag to limit location services help popup to show once per app session
    private locationServicesCache: boolean = false;
    // flag to store location service status
    private _locationServicesEnabled: boolean = true;

    private tokenReqDelay: number = 2 * 60; // seconds
    private tokenReqTimeout: any = null;
    
    public get isLocationServicesEnabled() : boolean {
        return this._locationServicesEnabled;
    };
    
    public set isLocationServicesEnabled(bool: boolean) {
        this._locationServicesEnabled = bool;
    };

    private _delayComplianceRetry = 2000; // Delay time in milliSeconds
    private disableGeoRestriction = false;
    private geocomplySDKRestriction = false;
    private geocomplyPollingInterval = 5 * 60 * 1000; // Delay time in milliSeconds (5 mins)
    private geocomplyInterval = null;
    private gcBypassSDKErrors = [600, 602, 603];
    private isWhitelistConfigReponseReceived: boolean = false; // True if we get response(success/fail) from whitelist api call. 

    public updateVerificationCheckStatusAndShowCheckPopup(inProgress: boolean, checkValid: boolean = true, showPopup: boolean = false, hidePopup: boolean = false) {
        if(this.isAccountVerificationInProgress != inProgress) {
            this.isAccountVerificationInProgress = inProgress;
            if(showPopup && !this.bypassGeoComply) {
                if(inProgress && !GeoComplyCheckPopup.getInstance().isNodeActive() && !this.isGeoComplyCheckPopupShownOnceAfterLogin) {
                    this.isGeoComplyCheckPopupShownOnceAfterLogin = true;
                    GeoComplyCheckPopup.getInstance().showMsgI18n({
                        iconType: eGeoComplyCheckPopupIconType.IN_PROGRESS,
                        enableAutoHide: false,
                        closeCallback: null,
                    });
                }
                else if(!inProgress && GeoComplyCheckPopup.getInstance().isNodeActive()) {
                    GeoComplyCheckPopup.getInstance().showMsgI18n({
                        iconType: checkValid ? eGeoComplyCheckPopupIconType.VALID : eGeoComplyCheckPopupIconType.INVALID,
                        enableAutoHide: true,
                        closeCallback: null,
                    });
                }
            }
        }
        if (hidePopup && GeoComplyCheckPopup.getInstance().isNodeActive()) {
            GeoComplyCheckPopup.getInstance().hideAllPopup(true);
        }
    }

    public forceCloseCheckInProgressPopup() {
        this.updateVerificationCheckStatusAndShowCheckPopup(false, false, false, true);
    }

    public isUserLoggedIn() {
        return cv.dataHandler.getUserData().isLoggedIn() && !cv.dataHandler.getUserData().isTouristUser;
    }

    private onLoginSccess() {
        console.log("GeoComplyCocos onLoginSccess");
        this.initAllowed = true;
    }

    private onGetGeocomplyConfig() {
        console.log("GeoComplyCocos onGetGeocomplyConfig");  
        this.isWhitelistConfigReponseReceived = true;
        if (this.isUserLoggedIn() && this.initAllowed) {
            this.initAllowed = false;
            if(!GeoComplyHandler.getInstance().isClientInstatiate()) {
                if(GeoComplyHandler.getInstance().getLastNativeError() != null) {
                    GeoComplyHandler.getInstance().handleSDKErrors(GeoComplyHandler.getInstance().getLastNativeError().toString());
                }
                return; 
            }
            if(this.isAccountVerificationInProgress) {
                this.isGeoComplyCheckPopupShownOnceAfterLogin = true;
                GeoComplyCheckPopup.getInstance().showMsgI18n({
                    iconType: eGeoComplyCheckPopupIconType.IN_PROGRESS,
                    enableAutoHide: false,
                    closeCallback: () => {
                        this.clearTokenCallback();
                    }
                });
                return;
            }
            this.geoPackage = null;
            this.renewLicense();
        }
    }

    private onLogoutSccess() {
        this.geoPackage = null;
        this.clearTokenCallback();
        this.clearGeoToken();
        this.clearTokenRenewOnExpiryScheduler();
        this.clearPackageScheduler();
        GeoComplyHandler.getInstance().cancelOnGoingRequest("User Logged Out");
        this.forceCloseCheckInProgressPopup();
        GeoComplyErrorPopup.getInstance().hideAllPopup();
        this.setGeoRestriction = false;
        this.geocomplySDKRestriction = false;
        this.isGeoComplyCheckPopupShownOnceAfterLogin = false;
        this.isWhitelistConfigReponseReceived = false;
    }

    public renewLicense(startOver: boolean = true, delay: number = 0): boolean {
        if(!GeoComplyHandler.getInstance().isClientInstatiate()) {
            GeoComplyHandler.getInstance().initSDK();
        }

        if(!this.isUserLoggedIn()) {
            return false;
        }
        console.log("GeoComplyCocos renewLicense");

        if(!this.isLicenseKeyValid()) {
            this.clearLicenseKey();
        }

        if(startOver) {
            this._currentRetryCount.licenseKey = 0;
        }

        if (this._currentRetryCount.licenseKey < this._maxRetryCount) {
            if (delay) {
                // handle delay
                setTimeout(() => {
                    this.renewLicense(startOver)
                }, delay);
                return true;
            }
            this.updateVerificationCheckStatusAndShowCheckPopup(true, false, true, false);
            this.requestLicenseKey();
            return true; // requested
        }
        this.updateVerificationCheckStatusAndShowCheckPopup(false, false, false, true);
        return false; // not requested
    }

    private isLicenseKeyValid() : boolean {
        const c_time = Math.floor(Date.now());
        if(this.license.key && c_time < this.license.expiry) {
            return true;
        }
        return false;
    }

    public clearLicenseKey() {
        this.license = {
            key : "",
            expiry : 0
        };
    }

    private requestLicenseKey() {
        this.clearGeoToken();
        this.clearLicenseRenewOnExpiryscheduler();
        if(!this.isLicenseKeyValid()) {
            this._currentRetryCount.licenseKey++;
            cv.worldNet.requestGeoComplyLicense();
        }
        else {
            this.onGetLicenseKeySuccess(this.license);
        }
    }

    private onGetLicenseKeySuccess(result: any) {
        console.log("GeoComplyCocos onGetLicenseKeySuccess : ", JSON.stringify(result));
        console.log(JSON.stringify(result));
        if (result && result.license) result.key = result.license;
        if(result && result.key) {
            this.license = result as iLicenseKey;
            if (this.isLicenseKeyValid()) {
                if (this.disableGeoRestriction && !this.geocomplySDKRestriction) {
                    // turn off client bypass if copmplaiance server is available
                    this.setGeoRestriction = false;
                }
                this.scheduleLicenseRenewOnExpiry();
                this.renewGeoToken();                
            }
            else if(!this.renewLicense(false)){
                    // stop retry if it is sending exprired license more then 3 times
                    this.serverErrorHandler(result, null, null, null, {
                        "ErrorType" : "ExpiredOrInvalidLicense",
                        "ErrorDetails": JSON.stringify(result),
                    });
                    this.fullFillPendingTokenRequest();
                }
        }
        else {
            console.log("GeoComplyCocos onGetLicenseKeyFailed : ", JSON.stringify(result));
            if ((result && !result.license) || !this.renewLicense(false, this._delayComplianceRetry)) {
                // bypass enabled
                if (this.checkForEnablingBypass(result, true)) {
                    this.forceCloseCheckInProgressPopup();
                }       
            }      
        }
    }

    private clearLicenseRenewOnExpiryscheduler() {
        if (this.getLicenseKeyTimeout) {
            clearTimeout(this.getLicenseKeyTimeout);
            this.getLicenseKeyTimeout = null;
        }
    }

    private scheduleLicenseRenewOnExpiry() {
        this.clearLicenseRenewOnExpiryscheduler();
        const c_time = Date.now();
        const d_time = this.license.expiry - c_time;
        if (d_time > 0) {
            this.getLicenseKeyTimeout = setTimeout(() => {
                console.log("GeoComplyCocos onLicenseExpire");
                if(!this.isAccountVerificationInProgress) {
                    this.renewLicense();
                }
            }, d_time);
        }
    }

    /**
     * to start process to get new geo token
     * @param startOver reset retry count and start process as fresh
     * @param ipChange true if request needs made due to ipChange
     * @returns true if request made
     */
    public renewGeoToken(startOver: boolean = true, ipChange: boolean = false, delay: number = 0): boolean {
        console.log("GeoComplyCocos renewGeoToken : ", startOver, ipChange);
        if (!this.isUserLoggedIn()) {
            return false;
        }

        if(startOver) {
            this._currentRetryCount.geoToken = 0;
            this._currentRetryCount.geoPackage = 0;
        }
        
        if (this._currentRetryCount.geoToken < this._maxRetryCount) {
            if (delay) {
                // handle delay
                setTimeout(() => {
                    this.renewGeoToken(startOver, ipChange)
                }, delay);
                return true;
            }
            if(GeoComplyHandler.getInstance().isGeoRequestInProcess()) {
                GeoComplyHandler.getInstance().cancelOnGoingRequest("IPChange", (success: boolean, description: string) => {
                    if(success) {
                        this.requestGeoLocationPackage(ipChange);
                    }
                });
            }
            else {
                this.requestGeoLocationPackage(ipChange);
            }
            return true; // requested
        }

        this.updateVerificationCheckStatusAndShowCheckPopup(false, false, false, true);
        return false; // not requested
    }
    
    /**
     * Get user data json object for geo location
     * @param ipChange true if request needs made due to ipChange
     * @returns object of iGCUserData
     */
    private getUserDataToTriggerGeoLocation(ipChange: boolean = false): iGCUserData {              
        // json object to hold required info to pass on native side.
        const userId = cv.dataHandler.getUserData().user_id;
        return {
            licenseKey: this.license.key,
            userId: userId ? userId.toString() : "",
            geolocationReason: ipChange ? "IPChange" : "Login",
            phoneNo: "",
            isIpChange: ipChange,
            enableIPService: cv.config.GC_IS_IPSERVICE_ENABLED,
            cancelRequestOnLocationDisabled: false
        }
    }

    /**
     * Request for native geo package/packet
     * @param ipChange true if request needs made due to ipChange
     */
    private requestGeoLocationPackage(ipChange: boolean = false) {
        console.log("GeoComplyCocos requestGeoLocationPackage : ", ipChange, this.geoPackage);
        if(ipChange) {
            if(this.isAccountVerificationInProgress) {
                return; // return if request is in progress already
            }
            this.geoPackage = null;
            this.clearGeoToken();
        }
        this.isLocationServicesEnabled = true;
        this.clearTokenRenewOnExpiryScheduler();
        this.updateVerificationCheckStatusAndShowCheckPopup(true, false, true, false);

        if (this.geoPackage != null) {
            this.requestGeoToken(this.geoPackage);
        }
        else {
            this.schedulePackageTimeout();
            GeoComplyHandler.getInstance().requestGeoLocation(this.getUserDataToTriggerGeoLocation(ipChange));
        }
    }

    /**
     * on successfully get geo package
     * @param result geo package string
     */
    private onGetGeoLocationPackageSuccess(result: string) {
        console.log("GeoComplyCocos onGetGeoLocationPackageSuccess : ", JSON.stringify(result));
        if(this.isUserLoggedIn()) {
            this.geoPackage = result;
            this.requestGeoToken(result);
        }
        else {
            this.geoPackage = null;
            this.forceCloseCheckInProgressPopup();
        }
        this.clearPackageScheduler();
    }

    /**
     * on failed to get geo package
     * @param result geo package string
     */
    private onGetGeoLocationPackageFailed(error: any) {
        console.log("GeoComplyCocos onGetGeoLocationPackageFailed : ", JSON.stringify(error));
        this.clearPackageScheduler();
        
    }

    /**
     * Made api request to store geo package and return valid geo token
     * @param geoPackage geo package string
     */
    private requestGeoToken(geoPackage: string) {
        console.log("GeoComplyCocos requestGeoToken : ", geoPackage);
        this._currentRetryCount.geoToken++;
        const userId = cv.dataHandler.getUserData().user_id;
        cv.worldNet.requestGeoComplyStoreToken(+userId, geoPackage);
    }

    /**
     * on successfully recive geo token
     * @param result token with expiry
     */
    private onGetGeoTokenSuccess(result: any) {
        console.log("GeoComplyCocos onGetGeoTokenSuccess : ", JSON.stringify(result));
        console.log(JSON.stringify(result));
        this.geoPackage = null;
        if(result && result.token) {
            this.geoToken = result as iGeoToken;
            if (this.isGeoTokenValid()) {
                if (this.disableGeoRestriction) {
                    // turn off client bypass if copmplaiance server is available
                    this.setGeoRestriction = false;
                }
                if(this.getTokenSuccessCallback && this.getTokenSuccessCallback.length > 0) {
                    this.updateVerificationCheckStatusAndShowCheckPopup(false, false, false, true);
                }
                else {
                    this.updateVerificationCheckStatusAndShowCheckPopup(false, true, true, false);
                }
                this.fullFillPendingTokenRequest(this.geoToken.token);
                this.scheduleTokenRenewOnExpiry();

                // Trigger gloabal event when get new geo token
                cv.MessageCenter.send(cv.Enum.geocomplyAction.onGetNewGeoToken, this.geoToken.token);
            }
            else if(!this.renewGeoToken(false)){
                    // stop retry getting geo token if it is sending exprired token more then 3 times
                    this.serverErrorHandler(result, null, null, null, {
                        "ErrorType" : "ExpiredOrInvalidToken",
                        "ErrorDetails": JSON.stringify(result),
                    });
                    this.fullFillPendingTokenRequest();
                }
        }
        else {
            console.log("GeoComplyCocos onGetGeoTokenFailed : ", JSON.stringify(result));
            if ((result && !result.token) || !this.renewGeoToken(false, false, this._delayComplianceRetry)) {
                // bypass enabled
                if (this.checkForEnablingBypass(result, true)) {
                    this.forceCloseCheckInProgressPopup();
                }       
            }
        }
    }

    // check if geocomply bypass needs to be enabled on client
    checkForEnablingBypass(error: any, forceStatus = false): boolean {
        console.log("geocomplycocos checkForEnablingBypass");
        console.log(JSON.stringify(error));
        // return true if already bypass is enabled
        if (this.disableGeoRestriction) return true;
        if (forceStatus) return this.setGeoRestriction = true;
        if (error && this.gcBypassSDKErrors.includes(error)) {
            this.geocomplySDKRestriction = true;
            return this.setGeoRestriction = true;
        }
        return false;
    }

    /**
     * To check whether the geo token is valid or not
     * @returns true id token is still valid
     */
    private isGeoTokenValid() : boolean {
        if(!this.isUserLoggedIn())
            return false;

        const c_time = Math.floor(Date.now()/1000);
        if(this.geoToken.token && this.geoToken.expiry - c_time > 0) {
            return true;
        }
        return false;
    }

    private clearTokenRenewOnExpiryScheduler() {
        if (this.getTokenTimeout) {
            clearTimeout(this.getTokenTimeout);
            this.getTokenTimeout = null;
        }
    }

     /**
     * Clear timeout for geo packet request
     */
    private clearPackageScheduler() {
        if (this.getPackageTimeout) {
            clearTimeout(this.getPackageTimeout);
            this.getPackageTimeout = null;
        }
    }

    /**
     * Schedule function to renew geo token based to expiry timestamp
     */
    private scheduleTokenRenewOnExpiry() {
        this.clearTokenRenewOnExpiryScheduler();
        const c_time = Math.floor(Date.now()/1000);
        let d_time = this.geoToken.expiry - c_time;
        d_time -= this._tokenExpiryBuffer;
        if (d_time > 0) {
            this.getTokenTimeout = setTimeout(() => {
                console.log("GeoComplyCocos onTokenExpire");
                if(!this.isAccountVerificationInProgress) {
                    this.renewGeoToken();
                }
            }, d_time * 1000);
        }
    }

    /**
     * Schedule timeout for geo packet request
     */
    private schedulePackageTimeout() {
        this.clearPackageScheduler();
        this.getPackageTimeout = setTimeout(() => {
            console.log("GeoComplyCocos onPackageTimeout");
            this.clearPackageRequest();
        }, this.packageTimeout * 1000);
    }

    // Request for renew token after the given time.
    public scheduleGeoTokenTimeout(ipChange: boolean = false) {
        if(this.tokenReqTimeout) {
            clearTimeout(this.tokenReqTimeout);
            this.tokenReqTimeout = null;
        }

        this.tokenReqTimeout = setTimeout(() => {
            console.log("GeoComplyCocos onGeoTokenTimeout");
            this.renewGeoToken(true, ipChange);
        }, this.tokenReqDelay * 1000);
    }

    /**
     * Function to cancel ongoing geo packet request and retry in case of timeout
     */
    private clearPackageRequest() {
        console.log("GeoComplyCocos clearPackageRequest");

        this._currentRetryCount.geoPackage++;

        if (this._currentRetryCount.geoPackage < this._maxRetryCount) {

            // Cancel ongoing request if request is in progress
            if (GeoComplyHandler.getInstance().isGeoRequestInProcess()) {
                console.log("_currentRetryCount geoPackage: ", this._currentRetryCount.geoPackage);
                GeoComplyHandler.getInstance().cancelOnGoingRequest("Request Timeout", (success: boolean, description: string) => {
                    if (success) {

                        // retry geopacket generation if request successfully cancelled
                        this.requestGeoLocationPackage();
                        if (GeoComplyCheckPopup.getInstance().isNodeActive()) {
                            GeoComplyCheckPopup.getInstance().showMsgI18n({
                                iconType: eGeoComplyCheckPopupIconType.IN_PROGRESS,
                                enableAutoHide: false,
                                text: "GeoComplyVerifyTimeOutPopupText",
                                closeCallback: () => {
                                    this.clearTokenCallback();
                                }
                            });
                        }
                    }
                });
            }
        }
        else {
            // Show timeout popup if retries are depleted
            this.showTimeoutPopup();
        }
    }

    /**
     * Cancel ongoing geocomply request and enable geocomply bypass
     */
    private showTimeoutPopup() {
        if (GeoComplyHandler.getInstance().isGeoRequestInProcess()) {
            GeoComplyHandler.getInstance().cancelOnGoingRequest("Request Timeout", (success: boolean, description: string) => {
                if (success) {

                    // show timeout error popup on successfull cancellation of existing request
                    this.isAccountVerificationInProgress = false;
                    this.forceCloseCheckInProgressPopup();
                    if (!this.disableGeoRestriction) {
                        this.geocomplySDKRestriction = true;
                        this.setGeoRestriction = true;
                    }

                }
            });
        }
    }
    
    public clearGeoToken() {
        this.geoToken = {
            token : "",
            expiry : 0,
            latitude : 0,
            longitude : 0,
            geolocationSource : "",
        };
    }

    /**
     * to get valid geo token
     * @returns token string
     */
    public getGeoToken(): string {
        if (!cv.config.USE_GEOCOMPLY)
            return null;

        if (this.isAccountVerificationInProgress)
            return null;
        
        if (this.isGeoTokenValid())
            return this.geoToken.token;

        return null;
    }

    /**
     * check if location serivices help popup needs to be shown
     * @param retryAction [Optional] retry callback
     * @param isAutoJoin [Optional] is game join request for auto join
     * @returns status boolean
     */
    public checkForLocationServices(retryAction?: Function): boolean {
        if (this.isUserLoggedIn() && cv.config.USE_GEOCOMPLY && !this.bypassGeoComply) {
            if (!this.isLocationServicesEnabled && !this.locationServicesCache && retryAction != null) {
                this.locationServicesCache = true;
                if (cc.sys.isNative) {
                    if (cc.sys.os === cc.sys.OS_ANDROID || cc.sys.os === cc.sys.OS_IOS) {
                        if (cv.SwitchLoadingView.isShow())
                            cv.SwitchLoadingView.hide();
                        this.showEnableLocationServiceHelpPopup(0, retryAction);
                        return true;
                    }
                }
            }
        }
    }

    /* check if native error popup needs to shown
     * @returns status boolean
     */
    private checkForNativeError(): boolean {
        if (this.isUserLoggedIn() && cv.config.USE_GEOCOMPLY && !this.bypassGeoComply) {
            if(this.getGeoToken() == null && GeoComplyHandler.getInstance().getLastNativeError() != null) {
                GeoComplyHandler.getInstance().handleSDKErrors(GeoComplyHandler.getInstance().getLastNativeError().toString());
                console.log("Geocomplycocos checkForNative615Error true");
                return true;
            }
        }
        return false;
    }

    public getGeoTokenLocation(): { latitude: number, longitude: number } {
        return { latitude: this.geoToken.latitude, longitude: this.geoToken.longitude };
    }

    public isGeoTokenRequired(): boolean {
        return this.isUserLoggedIn() && cv.config.USE_GEOCOMPLY && !this.bypassGeoComply && this.getGeoToken() == null;
    }

    /**
     * To get geo token in case of getting token process in progress
     * @param successCallback invoke when we get valid geo token
     * @param errorCallback invokes if it failed to get valid token
     * @param cancleCallback (optional) invokes if the request to get token is cancelled by user interaction
     */
    public requestValidToken(successCallback: (geoToken: string) => void, errorCallback: () => void, cancleCallback?: () => void, showLoadingPopup: boolean = true) {
        if(!this.isUserLoggedIn()) {
            this.clearTokenCallback();
            return;
        }
        
        if(successCallback)
            this.getTokenSuccessCallback.push(successCallback);

        if(errorCallback)    
            this.getTokenErrorCallback.push(errorCallback);

        if(cancleCallback)    
            this.getTokenCancleCallback.push(cancleCallback);

        if(this.isAccountVerificationInProgress || !this.isWhitelistConfigReponseReceived) {
            if (!this.bypassGeoComply && !GeoComplyCheckPopup.getInstance().isNodeActive() && showLoadingPopup) {
                if(!this.isGeoComplyCheckPopupShownOnceAfterLogin && !this.isWhitelistConfigReponseReceived) {
                    this.isGeoComplyCheckPopupShownOnceAfterLogin = true;
                }
                GeoComplyCheckPopup.getInstance().showMsgI18n({
                    iconType: eGeoComplyCheckPopupIconType.IN_PROGRESS,
                    enableAutoHide: false,
                    closeCallback: () => {
                        while(this.getTokenCancleCallback.length > 0 ) {
                            this.getTokenCancleCallback.shift()();
                        }
                        this.clearTokenCallback();
                    }
                });
            }
        }
        else if(this.isGeoTokenValid()) {
            this.fullFillPendingTokenRequest(this.geoToken.token);
        }
        else {
            this.fullFillPendingTokenRequest();
            if (this.checkForNativeError()) {
                
            } else {
                this.serverErrorHandler(1001, null, null, null, {
                    "ErrorType" : "NoValidTokenAvailable",
                    "ErrorDetails": JSON.stringify(this.geoToken),
                });
            }
        }
    }

    /**
     * to clear get token success and error callbacks
     */
    clearTokenCallback() {
        this.getTokenSuccessCallback = [];
        this.getTokenErrorCallback = [];
        this.getTokenCancleCallback = [];
    }

    /**
     * to fullfill get token request 
     * @param token geo token if available
     */
    fullFillPendingTokenRequest(token: string = null) {
        if (this.isUserLoggedIn()) {
            if(token) {
                while(this.getTokenSuccessCallback.length > 0 ) {
                    this.getTokenSuccessCallback.shift()(token);
                }
            }
            else {
                while(this.getTokenErrorCallback.length > 0 ) {
                    this.getTokenErrorCallback.shift()();
                }
            }
        }
        this.clearTokenCallback();
    }

    /**
     * get sub-rules/main error violations codes from check object
     * @param error check error json object
     * @returns list of error codes
     */
    private getGeoComplyCheckErrorsCodes(error: iGeoComplyCheckError): iGeoComplyCheckErrorSimplified {
        const simplifiedError: iGeoComplyCheckErrorSimplified = {
            code : error.code,
            subRules : [],
            softwareNames : []
        };
        
        if(error.reason && error.reason.payload) { // add sub rules errors
            if(error.reason.payload.ruleViolations) {
                const errList: string[] = error.reason.payload.ruleViolations;
                errList.forEach((element: string) => {
                    const text = cv.config.getStringData("GeoComplyRuleError_" + element.toString());
                    if(text && text.length > 0) {
                        simplifiedError.subRules.push(element);
                    }
                }); 
            }

            if(error.reason.payload.software) {
                const errList: iGeoComplyBlockedSoftware[] = error.reason.payload.software;
                errList.forEach((element: iGeoComplyBlockedSoftware) => {
                    simplifiedError.softwareNames.push(element.name);
                }); 
            }
        }

        return simplifiedError;
    }

    /**
     * To get all error codes in failed response
     * @param error error response object
     * @returns list of all error codes
     */
    private getErrorCodeArray(error: any): iGeoComplyCheckErrorSimplified[] {
        const allCodes: iGeoComplyCheckErrorSimplified[] = [];
        
        if(cv.StringTools.isNumber(error)) { // Handle numeric error code
            allCodes.push({
                code : error.toString(),
                subRules : null,
                softwareNames: null,
            });
        }
        else if(cv.StringTools.isNumberArray(error)) { // Handle array of numeric error code
            error.forEach(eItem => {
                allCodes.push({
                    code : eItem.toString(),
                    subRules : null,
                    softwareNames: null,
                });
            });
        }
        else if(Array.isArray(error) && error.length > 0) { // Handle Array of geocomply errors
            error.forEach(item => {
                const simplifiedError: iGeoComplyCheckErrorSimplified = this.getGeoComplyCheckErrorsCodes(item as iGeoComplyCheckError);
                allCodes.push(simplifiedError);
            });
        }
        else if(error.hasOwnProperty('checks') && Array.isArray(error.checks) && error.checks.length > 0) { // Handle Array of geocomply errors
            const checks = error.checks;
            checks.forEach(item => {
                const simplifiedError: iGeoComplyCheckErrorSimplified = this.getGeoComplyCheckErrorsCodes(item as iGeoComplyCheckError);
                allCodes.push(simplifiedError);
            });
        }

        return allCodes;
    }

    /**
     * To show error popup for server side error for geo comply check
     * @param error geo comply error response object    
     * @param retryCallback  
     */
    public serverErrorHandler(error: any, cashGameDirector: cc.Director = null, retryCallback: Function = null, observeCallback?: Function, NRErrorExtraData: any = null, enableRetry = true) {
        
        if(!this.isUserLoggedIn() || this.checkForNativeError()) {
            return;
        }

        if(this.bypassGeoComply && retryCallback == null) {
            return;
        }

        if (error?.status == 404) {
            // disable client side geocomply bypass
            this.setGeoRestriction = false;
            this.renewLicense(true);
            if (retryCallback) this.getTokenSuccessCallback.push(retryCallback);
            return;
        }
        
        console.log("GeoComplyCocos serverErrorHandler ", error.toString());
        const simplifiedErrors: iGeoComplyCheckErrorSimplified[] = this.getErrorCodeArray(error);
        const texts: string[] = [];
        const allErrorCodes: string[] = [];
        let intructStepsKey = "GeoComplyErrorGenaralSolutionStep";

        simplifiedErrors.forEach((item: iGeoComplyCheckErrorSimplified) => {

            if(item.subRules && item.subRules.length > 0) {
                item.subRules.forEach((subRule) => {
                    
                    allErrorCodes.push(subRule);

                    let text = cv.config.getStringData("GeoComplyRuleError_" + subRule.toString());
                    if(subRule == "blocked_software" && item.softwareNames && item.softwareNames.length > 0) {
                        text += " ";
                        text += cv.StringTools.formatC(cv.config.getStringData("GeoComplyRuleError_2001_software_names"), item.softwareNames.join(", "));
                    }
                    if(text && text.length > 0 && !texts.includes(text)) {
                        texts.push(text);
                    }
                    if (subRule == "blocked_software") {
                        intructStepsKey = "GeoComplyErrorBlockedSoftwareSolutionStep";
                    }
                    else if (["proxy", "VPN_adapters_rule"].includes(subRule)) {
                        intructStepsKey = "GeoComplyErrorProxySolutionStep";                        
                    }
                });
            }
            else {
                allErrorCodes.push(item.code.toString());
                const text = cv.config.getStringData("GeoComplyRuleError_" + item.code.toString());
                if(text && text.length > 0 && !texts.includes(text)) {
                    texts.push(text);
                }
            }
        });

        if(texts.length <= 0) {
            texts.push(cv.config.getStringData("GeoComplyRuleError_2004"));
            allErrorCodes.push(error.toString());
        }
        else if (texts.length > 1) {
            intructStepsKey = "GeoComplyErrorGenaralSolutionStep";
        }
        console.log("GeoComplyCocos allErrorCodes ", allErrorCodes.toString());

        if(enableRetry) {
            enableRetry = this.shouldEnableRetryButton(allErrorCodes); 
        }

        this.showErrorPopup(texts, intructStepsKey, cashGameDirector, NRErrorExtraData == null, retryCallback, observeCallback, enableRetry);
    }

    /**
     * To show error popup for native side error for geo comply check
     * @param error geo comply error response object    
     * @param retryCallback  
     */
    public nativeErrorHandler(eCode: number,  cashGameDirector: cc.Director = null, retryCallback?: Function) {
        if(!this.isUserLoggedIn() || this.bypassGeoComply) {
            return;
        }

        const texts: string[] = [];
        switch (eCode) {
            case GeoComplyError.REQUEST_TIMEOUT:
                texts.push(cv.config.getStringData("GeoComplyVerifyTimeoutRetryText"));
                break;
        
            default:
                // for all unhandled 600 and 700 errors show general message for now
                texts.push(cv.config.getStringData("GeoComplyRuleError_2004"));
                break;
        }
       

        const enableRetry = this.shouldEnableRetryButton([eCode.toString()]);
        this.showErrorPopup(texts, "GeoComplyErrorGenaralSolutionStep", cashGameDirector, true, retryCallback, null, enableRetry);
    }

    /**
     * To show geocomply error popups
     * @param texts Array of error text to display
     * @param retryCallback 
     */
    private showErrorPopup(texts: string[], intructStepsKey: string = null, cashGameDirector: cc.Director = null, clientSideError: boolean, retryCallback?: Function, observeCallback?: Function, enableRetry: boolean = false) {
        this.forceCloseCheckInProgressPopup();
        
        if (clientSideError && this.bypassGeoComply) {
            return;
        }

        let confirmCB: Function = () => {
            cv.netWorkManager.Logout();
        };
        let confirmBtnTextKey = "LOGOUT";
        
        if(enableRetry) { // id retry should be enable change callback and button text for retry
            confirmBtnTextKey = "Hotupdate_retrybtn";
            confirmCB = () => {
                if(!this.isAccountVerificationInProgress) {
                    this.renewLicense();
                }
                retryCallback?.();
            };
        }

        const intructSteps = [...cv.config.getStringDataArray(intructStepsKey)];

        GeoComplyErrorPopup.getInstance().showMsg({
            title: cv.config.getStringData("ValidationError"),
            texts,
            instructionSteps: intructSteps,
            confirmButtonText: cv.config.getStringData(confirmBtnTextKey),
            confirmCallback: confirmCB,
            enableConfirmButton: true,
            observeCallback,
            callObserverCallbackOnCloseClick: true,
            cashGameDirector,
        });
    }

    /**
     * Check if retry button should show for given errors
     * @param eCodes list of error codes
     * @returns True to show retry button, False to fallback to other button 
     */
    shouldEnableRetryButton(eCodes: string[]): boolean {
        console.log("GeoComplyCocos shouldEnableRetryButton ", cv.config.enableGeoComplyRetryForErrorCodes.toString());
        while(eCodes.length > 0) {
            if(!cv.config.enableGeoComplyRetryForErrorCodes.includes(eCodes.shift())){
                return false;
            }
        }
        return true;
    }

    /**
     * check if location serivices help popup needs to be shown
     * @param eCode [Optional] Geocomply Error code
     * @param retryAction [Optional] retry callback function
     */
    public showEnableLocationServiceHelpPopup(eCode?: number, retryAction?: Function) {
        this.forceCloseCheckInProgressPopup();

        if (!this.isUserLoggedIn() || this.bypassGeoComply) {
            return;
        }

        let textKey: string = null;
        if (cc.sys.os == cc.sys.OS_ANDROID) {
            textKey = "GeoComplyErrorLocationErrorWithStep_Android";
        }
        else if (cc.sys.os == cc.sys.OS_IOS) {
            textKey = "GeoComplyErrorLocationErrorWithStep_IOS";
        }

        const texts = [];
        let intructSteps = [];
        if (textKey) {
            const helpTextArray = [...cv.config.getStringDataArray(textKey)];
            texts.push(helpTextArray.shift());
            intructSteps = [...helpTextArray];
        }
        else {
            texts.push(cv.config.getStringData("GeoComplyRuleError_2004"));
            intructSteps = [...cv.config.getStringDataArray("GeoComplyErrorGenaralSolutionStep")];
        }

        const confirmCB: Function = () => {
            if (cc.sys.os === cc.sys.OS_ANDROID || cc.sys.os === cc.sys.OS_IOS) {
                const onAppBackground = () => {
                    // when app goes to background clear token and hide error popup               
                    GeoComplyErrorPopup.getInstance().hideAllPopup();
                    this.clearGeoToken();                
                }
    
                const onAppForeground = () => {
                    // when app comes to foreground retry user action after geotoken is generated again
                    this.renewLicense();
                    this.getTokenSuccessCallback.push(retryAction);                
                };
                this.registerAppFocusChangeCallbackOnce(onAppBackground, onAppForeground);
            }
            // open locations settings page
            GeoComplyHandler.getInstance()?.openLocationSettings();
        };

        const observeCB = () => {
            if (this.isGeoTokenValid())
                retryAction?.(this.getGeoToken());
        };

        GeoComplyErrorPopup.getInstance().showMsg({
            title: cv.config.getStringData("ValidationError"),
            texts,
            instructionSteps: intructSteps,
            confirmButtonText: cv.config.getStringData('OpenSettings'),
            confirmCallback: confirmCB,
            enableConfirmButton: true,
            observeButtonText: cv.config.getStringData("Login_Scene_login_panel_tourist_continue_button"),
            observeCallback: observeCB,
            cashGameDirector: null
        });
    }

    /**
     * Register for callback (once) when app focus changes
     * @param backGroundCb on app background callback function
     * @param foreGroundCb on app foreground callback function
     */
    registerAppFocusChangeCallbackOnce(backGroundCb: Function, foreGroundCb: Function): void {
        const onAppForeground = () => {
            console.log("Geocomplycocos onAppForeground");
            cc.game.off(cc.game.EVENT_SHOW, onAppForeground, this);
            foreGroundCb();
        }
        const onAppBackground = () => {
            console.log("Geocomplycocos onAppBackground");
            cc.game.off(cc.game.EVENT_HIDE, onAppBackground, this);
            backGroundCb();
            cc.game.on(cc.game.EVENT_SHOW, onAppForeground, this);
        }
        if (cc.sys.isNative) {
            cc.game.on(cc.game.EVENT_HIDE, onAppBackground, this);
        }
    }

    public showLocationPermissionPopup(eCode?: number) {
        if (this.bypassGeoComply) {
            return;
        }
        const textKey: string = "GeoComplyError_LocationPermission_615_android";
        const texts = [];
        let intructSteps = [];
        const helpTextArray = [...cv.config.getStringDataArray(textKey)];
        texts.push(helpTextArray.shift());
        intructSteps = [...helpTextArray];
        const enableRetry = this.shouldEnableRetryButton([eCode.toString()]);
        let confirmBtnTextKey = "LOGOUT";
        
        let confirmCB: Function = () => {
            cv.netWorkManager.Logout();
        };
        if(enableRetry) { // id retry should be enable change callback and button text for retry
            confirmBtnTextKey = "Hotupdate_retrybtn";
            confirmCB = () => {
                this.checkPermissionAndRenewGeoToken();
            };
        }
        const observeCB: Function = (isClose?: boolean) => {
            if (isClose) return;
            if (cc.sys.os === cc.sys.OS_ANDROID || cc.sys.os === cc.sys.OS_IOS) {
                this.registerAppFocusChangeCallbackOnce(() => { }, () => {
                    // renew geotoken when app comes to foreground
                    this.checkPermissionAndRenewGeoToken();
                });
            }
            cv.permMgr?.openAppSettings();
        };
        GeoComplyErrorPopup.getInstance()?.showMsg({
            title: cv.config.getStringData("ServerErrorCode136"),
            texts,
            instructionSteps: intructSteps,
            confirmButtonText: cv.config.getStringData(confirmBtnTextKey),
            confirmCallback: confirmCB,
            enableConfirmButton: enableRetry,
            observeButtonText: cv.config.getStringData("OpenSettings"),
            observeCallback: observeCB,
            cashGameDirector: null
        });
    }

    /**
     * Check for permission before trying to renew geotoken
     */
    private checkPermissionAndRenewGeoToken(): void {
        GeoComplyErrorPopup.getInstance()?.hideAllPopup();
        GeoComplyHandler.getInstance().initSDK();
        // check for client exception after a delay and renew geotoken
        setTimeout(() => {
            if (GeoComplyHandler.getInstance().isClientInstatiate()) {
                if (!this.isAccountVerificationInProgress) {
                    this.renewLicense();
                }
            }
        }, 500);       
    }
}