enum HeartBeatState {
    Idle = 0,
    Requesting,
    Receiving,
    Received
}

export default class HeartBeat {
    private _intervalSecs: number = 0;
    private _timeOutSecs: number = 0;
    private _requestFunc: () => boolean;
    private _receiveFunc: () => void;
    private _timeOutFunc: () => void;
    private _state: HeartBeatState = HeartBeatState.Idle;

    private get scheduler() {
        return cc.director.getScheduler();
    }

    constructor(interval: number, timeOutSecs: number, requestFunc: () => boolean, timeOutFunc: () => void, receiveFunc: () => void) {
        this._intervalSecs = interval;
        this._timeOutSecs = timeOutSecs;
        this._requestFunc = requestFunc;
        this._timeOutFunc = timeOutFunc;
        this._receiveFunc = receiveFunc;
        this.scheduler.enableForTarget(this);
    }

    public stop(): void {
        this._state = HeartBeatState.Idle;
        this.scheduler.unscheduleAllForTarget(this);
    }

    public dispose():void{
        this._requestFunc = null;
        this._timeOutFunc = null;
        this._receiveFunc = null;
    }

    public startHeartBeat(): void {
        this.scheduler.schedule(this._doHeartBeat, this, this._intervalSecs);
    }

    private _doHeartBeat() {
        this._state = HeartBeatState.Requesting;
        if (this._requestFunc()) {
            this._state = HeartBeatState.Receiving;
            setTimeout(() => {
                this._checkTimeout();
            }, this._timeOutSecs * 1000);
        } else {
            this._onTimeOut();
        }
    }

    public receiveHeartBeat(): void {
        this._state = HeartBeatState.Received;
        if (this._receiveFunc) {
            this._receiveFunc();
        }
    }

    private _checkTimeout(): void {
        if (this._state === HeartBeatState.Receiving) {
            this._onTimeOut();
        }
    }

    private _onTimeOut(): void {
        this._state = HeartBeatState.Idle;
        this._timeOutFunc();
    }
}
