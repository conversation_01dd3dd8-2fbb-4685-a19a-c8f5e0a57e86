/************************************************************************/
/* 
*   网络重连管理
*/
/************************************************************************/
const { ccclass, property } = cc._decorator;
import cv from "../../components/lobby/cv";
import { ecdhHandler } from "../../common/ecdh/ecdhHandler";
import { pb } from "../../../Script/common/pb/ws_protocol";
import BJPVPConnector from "../../../blackjackpvp/script/common/BJPVPConnector";
import { PKFConfig } from "../../../pkf/script/common/config/PKFConfig";
import MTTConnector from "../../../mtt/script/common/MTTConnector";
import { httpApis } from "../../../mtt/script/common/net/httpApis";
import HeartBeat from "./HeartBeat";
import { pfAdapter } from "../tools/poker-framework-apatper";
import { HandMapCacheManager } from "../tools/HandMapCacheManager";

@ccclass
export class NetWorkManager extends cc.Component {
    ReconnectCounts: number;
    serverFailCounts: number;
    _isswitchServer: boolean = false;
    _isManualSwitch:boolean = false;
    _isOpenSwitch: boolean = false;
    isInVideoCowBoyScene: boolean = false;
    isInPKFLiveScene: boolean = false;
    _worldHeartBeat:HeartBeat = null;
    _gameHeartBeat:HeartBeat = null;

    doSwitchFromHallscene: boolean =  false;

    private static _g_instance: NetWorkManager = null;          // 单例
    private _bOnAppEnterBack: boolean = false;                  // 程序是否处于后台状态
    public _isLoginFailed: boolean = false;                            //cowboy_web专用

    public static getInstance(): NetWorkManager {
        if (!this._g_instance) {
            this._g_instance = new NetWorkManager();
            this._g_instance.init();
        }
        return this._g_instance;
    }

    private _lastDisconnectedTime:number = null;
    private readonly _DisconnectedInterval = 3000 // ms

    public init() {

        //私语网页版, 在跑在私语app上面，不需要监听
        if (!cv.config.isSiyuType()) {
            cc.game.on(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
            cc.game.on(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
        }

        this.ReconnectCounts = 0;
        this._isswitchServer = false;
        this._isOpenSwitch = false;
        cv.MessageCenter.register("switchSceneFinish", this._onMsgSwitchScene.bind(this),this); 
        this._worldHeartBeat = new HeartBeat(12,5,this.requestHeartBeat.bind(this), this.OnWorldTimeOut.bind(this), this.sendChangeDelay.bind(this));
        this._gameHeartBeat = new HeartBeat(12,8,this.toHeartBeat.bind(this), this.OnGameTimeOut.bind(this),null);
    }

    requestHeartBeat():boolean{
        return cv.worldNet.requestHeartBeat();
    }

    onDestroy() {
        cc.game.off(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
        cc.game.off(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
        cv.MessageCenter.unregister("switchSceneFinish", this);
    }

    private _onMsgSwitchScene() {
        if(cv.dataHandler.getUserData().isBanDelay){
            this._bannedHandler();
        }
    }

    public SceneOnLoad() {
        if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb) {
            cv.cowboyNet.initCommon(cv.Enum.GameId.CowBoy);
        }
        else {
            cv.worldNet.initCommon(cv.Enum.GameId.World);
            cv.gameNet.initCommon(cv.Enum.GameId.Texas);
            cv.cowboyNet.initCommon(cv.Enum.GameId.CowBoy);
            cv.videoCowboyNet.initCommon(cv.Enum.GameId.VideoCowboy);
            cv.humanboyNet.initCommon(cv.Enum.GameId.HumanBoy);
            cv.pokerMasterNet.initCommon(cv.Enum.GameId.PokerMaster);
            cv.aofNet.initCommon(cv.Enum.GameId.Allin);
            cv.dataNet.initCommon(cv.Enum.GameId.Data);
            cv.jackfruitNet.initCommon(cv.Enum.GameId.Jackfruit);
        }

        if (!cv.netWork.isConnect()) {
            cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
            cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
            cv.netWork.connectServer();
        }
        else {
            if (cv.config.GET_CLIENT_TYPE() != cv.Enum.ClientType.CowboyWeb) {
                cv.worldNet.requestGetUserData();
                this.requestClubInfo();
            }
            this.StartChecknetNetwork();
        }
    }

    public OnWorldServerLogin(pSend) {
        if (cv.netWork.isEncrypt(cv.roomManager.getCurrentGameID())) {
            ecdhHandler.getInstance().ecdh_init();
        }

        if (cv.config.getCurrentScene() == cv.Enum.SCENE.HALL_SCENE) {
            //解决bug:5784
            //在大厅切换线路，重新登录world服后，服务器会将secretKey清除掉。会导致重新交换key之前，客户端与服务器secretkey不一致。
            //在登录world后，重新交换这个状态设置为false。这样在大厅点切换线路，再点击房间列表，joinRoom的时候就会重新交换密钥（不重新执行ecdh_init()初始化）
            ecdhHandler.getInstance().ecdh_setNeedGenKeyState(false);
        }

        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.GameId_Dummy) {
            // if (cv.dataHandler.getUserData().m_bIsNewRegisted) {
            cv.worldNet.requestDeviceInfoReport(cv.Enum.ReportType.REPORT_REGEGIST);
            // }
        }
        //请求banner
        if (cv.dataHandler.getBannerMapSize() == 0) {
            cv.worldNet.BannerRequest();
        }
        
        this.ReconnectCounts = 0;
        cv.dataHandler.getUserData().isFirstLogin = false;

        cv.domainMgr.writeLastLogin();
        cv.domainMgr.initLoginServer();
        if (this._isswitchServer) {
            this._isswitchServer = false;
            if(this._isManualSwitch){
                cv.TT.showMsg(cv.config.getStringData("UIWitchServer1"), cv.Enum.ToastType.ToastTypeSuccess);

                // if manual switch success, notify hallscene the result
                // hallscene will do reset the network line number for switch server button - line number
                // only emit when switch maneul from hallscene.
                if(this.doSwitchFromHallscene){
                    cv.MessageCenter.send('HallScene-SwitchSuccess');
                    this.doSwitchFromHallscene = false;
                }
            }
        }

        if(!cv.domainMgr.isPreserveDomain()){
            cv.tools.SaveStringByCCFile('lastDomain', cv.domainMgr.getServerInfo().gate_server);
        }

        if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.GameId_Dummy) {
            this.requestClubInfo();
        }



    }

    public OnGameServerLogin(pSend) {
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = true;
        this.StartGameHeartBeat();
        cv.roomManager.RequestJoinRoom(cv.roomManager.getCurrentGameID(), cv.roomManager.getCurrentRoomID(), cv.roomManager.getIsQuickRoom(), cv.roomManager.getIsNeedPassword(), cv.roomManager.getRoomPassWord(), false);
        if (this._isswitchServer) {//！存在word服连不上而游戏服能连的时候，这里切换线路仍然给出切换成功的提示
            this._isswitchServer = false;
            cv.TT.showMsg(cv.config.getStringData("UIWitchServer1"), cv.Enum.ToastType.ToastTypeSuccess);
        }
    }

    public OnCowboyServerLogin() {
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = true;
        this.StartGameHeartBeat();
        if (this._isswitchServer) {
            this._isswitchServer = false;
            cv.TT.showMsg(cv.config.getStringData("UIWitchServer1"), cv.Enum.ToastType.ToastTypeSuccess);
        }
        cv.roomManager.RequestJoinRoom(cv.Enum.GameId.CowBoy, cv.roomManager.getCurrentRoomID());
    }

    public OnVideoCowboyServerLogin() {
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = true;
        this.StartGameHeartBeat();
        if (this._isswitchServer) {
            this._isswitchServer = false;
            cv.TT.showMsg(cv.config.getStringData("UIWitchServer1"), cv.Enum.ToastType.ToastTypeSuccess);
        }
        cv.roomManager.RequestJoinRoom(cv.Enum.GameId.VideoCowboy, cv.roomManager.getCurrentRoomID());
    }

    public OnHumanboyServerLogin() {
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = true;
        this.StartGameHeartBeat();
        if (this._isswitchServer) {
            this._isswitchServer = false;
            cv.TT.showMsg(cv.config.getStringData("UIWitchServer1"), cv.Enum.ToastType.ToastTypeSuccess);
        }
        cv.roomManager.RequestJoinRoom(cv.Enum.GameId.HumanBoy, cv.roomManager.getCurrentRoomID());
    }

    public OnPokerMasterServerLogin() {
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = true;
        this.StartGameHeartBeat();
        if (this._isswitchServer) {
            this._isswitchServer = false;
            cv.TT.showMsg(cv.config.getStringData("UIWitchServer1"), cv.Enum.ToastType.ToastTypeSuccess);
        }
        cv.roomManager.RequestJoinRoom(cv.Enum.GameId.PokerMaster, cv.roomManager.getCurrentRoomID());
    }

    public StartChecknetNetwork() {
        this.schedule(this.UpdateNetwork, 3);

    }

    public UpdateNetwork(f32Delta) {
        if(this._getLocalConnectionStatus()) {
            // connection good, reset  _lastDisconnectedTime
            this._lastDisconnectedTime = null;
        } else {
            // no connection, may show popup and then return
            this._onLocalDisconnected()
            return;
        }
        
        if(!cv.native.CheckNetWork()) {
            this.unschedule(this.UpdateNetwork);
            if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb) {
                cv.TP.showMsg(cv.config.getStringData("ErrorToast43"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
            }
            else{
                if (!cv.netWork.isConnect() && !cv.netWork.isConnecting()) {
                    this.reconnect();
                  }
                  else{
                    cv.TT.showMsg(cv.config.getStringData("ErrorToast33"), cv.Enum.ToastType.ToastTypeError);
                    this.scheduleOnce(this.Logout, 1.5);
                  }
            }
        }
        else {
            if (!cv.netWork.isConnect() && !cv.netWork.isConnecting()) {
                this.reconnect();
            }
        }
    }

    private _onLocalDisconnected() {
        const now = (new Date()).getTime();

        if(!this._lastDisconnectedTime) {
            this._lastDisconnectedTime = now;
        }

        if(now - this._lastDisconnectedTime > this._DisconnectedInterval) {
            cv.TP.showMsg(
                cv.config.getStringData('UIWitchServer'),
                cv.Enum.ButtonStyle.TWO_BUTTON,
                ()=>{
                    cv.MessageCenter.send('HideWebview_ShowWindows', true);
                    this.onGoReconnect();
                },
                ()=>{
                    cv.MessageCenter.send('HideWebview_ShowWindows', true);
                }
            );
        }
    }

    public userTouchToReconnect(){
        cv.LoadingCommonView.show(cv.Enum.LOADINGTYPE.RECONNECT);
        //显示3s loading界面
        this.scheduleOnce(function(){
            cv.LoadingCommonView.hide();
            this.reconnect();
        }, 3)
    }

    public StartWorldHeartBeat() {
        this.StartChecknetNetwork();
        this._worldHeartBeat.startHeartBeat();
    }

    public onWorldHeartBeat() {
        this._worldHeartBeat.receiveHeartBeat();
    }

    public StartGameHeartBeat() {
        this._gameHeartBeat.startHeartBeat();
    }

    public toHeartBeat(): boolean {
        const gameId: number = cv.roomManager.getCurrentGameID();
        let result: boolean = false;

        switch (gameId) {
            case cv.Enum.GameId.CowBoy: 
                result = cv.cowboyNet.requestHeartBeat();
                break;
            case cv.Enum.GameId.VideoCowboy: 
                result = cv.videoCowboyNet.requestHeartBeat();
                break;
            case cv.Enum.GameId.HumanBoy:
                result = cv.humanboyNet.requestHeartBeat();
            break;
            case cv.Enum.GameId.PokerMaster:
                result = cv.pokerMasterNet.requestHeartBeat();
            break;
            case cv.Enum.GameId.Texas:
            case cv.Enum.GameId.Squid:
            case cv.Enum.GameId.StarSeat:
            case cv.Enum.GameId.Bet:
            case cv.Enum.GameId.Plo:
                result = cv.gameNet.requestHeartBeat();
            break;
            case cv.Enum.GameId.Allin:
                result = cv.aofNet.requestHeartBeat();
            break;
            case cv.Enum.GameId.Jackfruit:
                result = cv.jackfruitNet.requestHeartBeat();
            break;
            default:
                if (cv.GameDataManager.tRoomData.isZoom()) {
                    result = cv.gameNet.requestHeartBeat();
                }
            break;
        }
        return result;
    }

    public onGameHeartBeat() {
        this._gameHeartBeat.receiveHeartBeat();
    }

    /**
     * 发送延时信息，监测网络状态
     */
    public sendChangeDelay() {
        const date: Date = new Date();
        console.log(cv.StringTools.formatC("WorldHeartBeat end Time=========>>:%d", date.getTime()));
        cv.dataHandler.getUserData().u64DelayEnd = date.getTime();
        cv.MessageCenter.send("on_change_delay");
    }

    //! 心跳超时就认为世界服已断开。此时重新执行登录就行。
    public OnWorldTimeOut() {
        this.sendChangeDelay();
        cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
        cv.worldNet.requestLoginServer();
    }

    //! 心跳超时就认为游戏服已断开。此时重新执行登录就行。
    public OnGameTimeOut(f32Delta) {
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
        if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb) {
            cv.cowboyNet.requestLoginServer();
        }
        else {
            cv.roomManager.RequestJoinRoom();
        }
    }

    public reconnect() {
        if (this.ReconnectCounts < 1) {
            this.ReconnectCounts++;
            cv.netWork.close();

            cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
            cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
            this._worldHeartBeat.stop();
            this._gameHeartBeat.stop();
            // this.unschedule(this.UpdateNetwork);
            //!重连
            cv.netWork.connectServer();
        }
        else {
            if (cv.dataHandler.getUserData().isFirstLogin) {
                if (cv.domainMgr.isHaveNextServer())//有值为true
                {
                    cv.domainMgr.switchServer();
                    this.ReconnectCounts = 0;
                    this.UpdateNetwork(0);
                }
                else {
                    if(cv.config.getCurrentScene() == cv.Enum.SCENE.HALL_SCENE)
                        cv.TT.showMsg(cv.config.getStringData("ErrorToast21"), cv.Enum.ToastType.ToastTypeError);
                    this.unschedule(this.UpdateNetwork);
                }
            }
            else if (!this._isOpenSwitch) {
                cv.netWork.close();
                cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
                this.unschedule(this.UpdateNetwork);
                this.closeGameConnect(true);
                this.closeWorldConnect(true);
                this.ReconnectCounts = 0;
                this._isOpenSwitch = true;
                cv.TP.showMsg(cv.StringTools.formatC("%s", cv.config.getStringData("UIWitchServer")), cv.Enum.ButtonStyle.TWO_BUTTON, this.onGoReconnect.bind(this), this.onGoLogout.bind(this));
            }
        }
    }

    public reconnectByServerFailed() {
        if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb && this._isLoginFailed == true) return;
        if (this.serverFailCounts < 10) {
            this.serverFailCounts++;
            cv.netWork.close();
            cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
            cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
            this._worldHeartBeat.stop();
            this._gameHeartBeat.stop();
            // this.unschedule(this.UpdateNetwork);
            //!重连
            cv.netWork.connectServer();
        }
        else {
            if (cv.dataHandler.getUserData().isFirstLogin) {
                if (cv.domainMgr.isHaveNextServer())//有值为true
                {
                    cv.domainMgr.switchServer();
                    this.ReconnectCounts = 0;
                    this.UpdateNetwork(0);
                }
                else {
                    if(cv.config.getCurrentScene() == cv.Enum.SCENE.HALL_SCENE)
                        cv.TT.showMsg(cv.config.getStringData("ErrorToast21"), cv.Enum.ToastType.ToastTypeError);
                    this.unschedule(this.UpdateNetwork);
                }
            }
            else if (!this._isOpenSwitch) {
                cv.netWork.close();
                cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
                this.unschedule(this.UpdateNetwork);
                this.closeGameConnect(true);
                this.closeWorldConnect(true);
                this.ReconnectCounts = 0;
                this._isOpenSwitch = true;
                this.onGoReconnect();
            }
        }
    }

    public closeGameHeart() {
        console.log("closeGameHeart");
        this._gameHeartBeat.stop();
    }

    public closeGameConnect(isReconnet: boolean = false) {
        console.log("closeGameConnect");
        this._gameHeartBeat.stop();
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
        cv.roomManager.resetRoomCache();
        if (isReconnet) return;
        cv.roomManager.reset();
        this.ReconnectCounts = 0;
        this._isOpenSwitch = false;
    }

    public closeWorldConnect(isReconnet: boolean = false) {

        cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
        this._worldHeartBeat.stop();
        cv.roomManager.resetRoomCache();
        if (isReconnet) return;
        this.ReconnectCounts = 0;
        this._isOpenSwitch = false;
    }

    public Logout() {
        cv.C2CNotify.onLogout();
        if (this.isInVideoCowBoyScene && !cc.sys.isBrowser) {
            cv.MessageCenter.send("videoCowBoyToLogout");
            return;
        }
        if (this.isInPKFLiveScene && !cc.sys.isBrowser) {
            cv.MessageCenter.send(PKFConfig.broadcast.LOGOUT_AND_LEAVE_LIVE);
            return;
        }
        this.unscheduleAllCallbacks();
        this._isswitchServer = false;
        if (cv.config.getCurrentScene() != cv.Enum.SCENE.LOGIN_SCENE && cv.config.GET_CLIENT_TYPE() != cv.Enum.ClientType.CowboyWeb) {
            pfAdapter.logout();
            cv.SHOP.msgNode.active = false;
            cv.action.switchScene(cv.Enum.SCENE.LOGIN_SCENE);
        }
        cv.netWork.close();
        this.closeGameConnect();
        this.closeWorldConnect();
        this.unschedule(this.UpdateNetwork);
        // 清除相关数据
        do {
            cv.GameDataManager.tGameData.reset();                   // 清除游戏数据
            cv.GameDataManager.tGiftData.reset();                   // 清除礼物数据
            cv.GameDataManager.tGameRecords.reset();                // 清除游戏战绩数据
            cv.GameDataManager.tCollectPokerMapData.reset();        // 清除游戏收藏数据
            cv.dataHandler.clearData();                             // 清除玩家自身数据
            cv.globalMsgDataMgr.clearData();                        // 清除全局消息数据
            cv.viewAdaptive.reset();
            HandMapCacheManager.clearAllCache();
        } while (false);

        if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb) {
            console.log("--------- ccjs://back-abnormal");
            document.location.href = "ccjs://back-abnormal";
        }
        if( cv.config.HAVE_BLACKJACK )
        {
            BJPVPConnector.instance.logout();
        }
        if (cv.config.HAVE_MTT){
            MTTConnector.instance.onLogout();
        }

        cv.MessageCenter.send(cv.Enum.geocomplyAction.onLogout);
    }

    public cleanNetWork() {
        this.unscheduleAllCallbacks();
        this._isswitchServer = false;
        cv.netWork.close();
        this.closeGameConnect();
        this.closeWorldConnect();
        this.unschedule(this.UpdateNetwork);
        // 清除相关数据
        do {
            cv.GameDataManager.tGameData.reset();                   // 清除游戏数据
            cv.GameDataManager.tGiftData.reset();                   // 清除礼物数据
            cv.GameDataManager.tGameRecords.reset();                // 清除游戏战绩数据
            cv.GameDataManager.tCollectPokerMapData.reset();        // 清除游戏收藏数据
            cv.dataHandler.clearData();                             // 清除玩家自身数据
            cv.globalMsgDataMgr.clearData();                        // 清除全局消息数据
        } while (false);
    }

    public onGoLogout() {
        this._isOpenSwitch = false;
        this.unschedule(this.UpdateNetwork);
        if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb) {
            cv.TP.showMsg(cv.config.getStringData("ErrorToast43"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
        }
        else {
            cv.TT.showMsg(cv.config.getStringData("ErrorToast33_5"), cv.Enum.ToastType.ToastTypeError);
            this.Logout();
        }
    }

    public OnUpdateNetWork(psender) {
        this.UpdateNetwork(0);
    }

    public onGoReconnect(isManualSwitch:boolean = false)//切换线路需要close所有socket进行重连
    {
        cv.MessageCenter.send("OnGoReconnect");
     
        this._isOpenSwitch = false;
        cv.netWork.close();
        this.closeGameConnect(true);
        this.closeWorldConnect(true);
        if(isManualSwitch === true){
            cv.domainMgr.reset_reconnet_num();
        }
        if (this._getLocalConnectionStatus() && cv.domainMgr.isHaveNextServer()) {
            this._isManualSwitch = isManualSwitch;
            this._isswitchServer = true;
            cv.domainMgr.switchServer();
            this.StartChecknetNetwork();
            this.UpdateNetwork(0);
        }
        else if(this._getLocalConnectionStatus() && !cv.domainMgr.isHaveNextServer()){
            if(cv.config.getCurrentScene() === cv.Enum.SCENE.HALL_SCENE)
                cv.TT.showMsg(cv.config.getStringData("ErrorToast21"), cv.Enum.ToastType.ToastTypeError);
        }
        else {
            this.StartChecknetNetwork();
        }
    }

    // modify from onGoReconnect, only change switchSpecificServer, other reconnect logic not change
    // since is already have specific domain
    public onReconnectToSpecificLine(lineIndex: number, isManualSwitch: boolean = true) {
        cc.log(`onReconnectToSpecificLine, Line = ${lineIndex}`);

        this.doSwitchFromHallscene = true;

        this._isOpenSwitch = false;
        cv.netWork.close();
        this.closeGameConnect(true);
        this.closeWorldConnect(true);

        if (isManualSwitch) {
            cv.domainMgr.reset_reconnet_num();
        }
        if (this._getLocalConnectionStatus() && cv.domainMgr.isHaveNextServer()) {
            this._isManualSwitch = isManualSwitch;
            this._isswitchServer = true;
            cv.domainMgr.switchSpecificServer(lineIndex);
            this.StartChecknetNetwork();
            this.UpdateNetwork(0);
        }
        else if (this._getLocalConnectionStatus() && !cv.domainMgr.isHaveNextServer()) {
            if (cv.config.getCurrentScene() === cv.Enum.SCENE.HALL_SCENE)
                cv.TT.showMsg(cv.config.getStringData("ErrorToast21"), cv.Enum.ToastType.ToastTypeError);
        }
        else {
            this.StartChecknetNetwork();
        }

    }

    public requestClubInfo() {
        cv.worldNet.requestGetAllRemarks();
        cv.worldNet.requestCurrentBoardList();
        cv.worldNet.GoodsListRequest();
    }

    public onReadServer() {

    }
    public onThredEndTologout() {
        this.isInVideoCowBoyScene = false;
        this.Logout();
    }

    public onPKFLiveLogout() {
        this.isInPKFLiveScene = false;
        this.Logout();
    }

    /**
     * CowboyWeb挤账号
     */
    public OnCowboyWebRelogin() {
        this.OnNeedRelogin(4);
    }

    /**
     * CowboyWeb登录失败
     * @param str 
     */
    public OnCowboyWebLoginFailed(str: string): void {
        this._isLoginFailed = true;
        this.unscheduleAllCallbacks();
        this.unschedule(this.UpdateNetwork);
        cv.netWork.close();
        this.closeGameConnect(true);
        this.closeWorldConnect(true);
        this.ReconnectCounts = 0;
        cv.TP.showMsg(str, cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
    }

    public OnCowboyWebNetFailed(): void {
        if (cv.TP.haveMsgNode()) {
            cv.TP.showMsg(cv.config.getStringData("ServerErrorCode224"), cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
                document.location.href = "ccjs://back-abnormal";
            });
        }
        else {
            console.log("--------- ccjs://back-abnormal");
            document.location.href = "ccjs://back-abnormal";
        }
    }

    public OnNeedRelogin(pSend) {
        if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.VideoCowboy) {
            this.isInVideoCowBoyScene = true;
        }
        // 清理loading数据
        //g_pLoading->clearMap();
        const code = pSend;
        this.unscheduleAllCallbacks();
        this.unschedule(this.UpdateNetwork);
        cv.netWork.close();
        this.closeGameConnect(true);
        this.closeWorldConnect(true);
        this.ReconnectCounts = 0;
        if (code === 4) {
            cv.TP.showMsg(cv.config.getStringData("ServerErrorCode4"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
            cv.TP.setTag(cv.TP.NOT_RESET_TAG);
            cv.MessageCenter.send("onForcedLogout");
        }
        else if (code === 5) {
            cv.TP.showMsg(cv.config.getStringData("ServerErrorCode5"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
        }
        else if (code === 2) {
            cv.TP.showMsg(cv.config.getStringData("Hotupdate_Update"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.LowVirsionForSmallVersion.bind(this));
        }
        else if (code === 3) {
            cv.TP.showMsg(cv.config.getStringData("ServerErrorCode3"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
        }
        else if (code === 197) {
            if(MTTConnector.instance.isInMTTGame()){
                cv.dataHandler.getUserData().isBanDelay = true;
                cv.dataHandler.getUserData().isban = true
            } else {
                this._bannedHandler();
            }
        }
        else if (code === 224) {
            cv.TP.showMsg(cv.config.getStringData("ServerErrorCode224"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
        }
        else if (code === 225) {
            cv.TP.showMsg(cv.config.getStringData("ServerErrorCode225"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
        }
        else if (code === 229) {
            cv.TP.showMsg(cv.config.getStringData("ServerErrorCode229"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
        }
        else if (code === 31123) {
            this._serverMaintenanceHandler();
        }
    }

    private sendCustomerServiceMsg(autoLogout:boolean) {
        cv.MessageCenter.send("onCustomerServiceClick");
        if(autoLogout) {
            this.scheduleOnce( ()=>{
                cv.httpHandler.requestLogout();
            });
        }
        
    }

    private _bannedHandler() {
        cv.TP.showMsg(
            cv.config.getStringData("ServerErrorCode197"),
            cv.Enum.ButtonStyle.TWO_BUTTON,
            this.sendCustomerServiceMsg.bind(this, true),
            this.Logout.bind(this)
        );
        cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_CUSTOMER_SERVICE);
    }

    private _serverMaintenanceHandler(){
        cv.TP.showMsg(
            cv.config.getStringData("ServerErrorCode31123"),
            cv.Enum.ButtonStyle.TWO_BUTTON,
            this.sendCustomerServiceMsg.bind(this, true),
            this.Logout.bind(this)
        );
        cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_CUSTOMER_SERVICE);
    }

    /**
     * 大版本更新清除缓存文件。
     */
    public LowVirsion() {
        let _storagePath = ((jsb.fileUtils ? jsb.fileUtils.getWritablePath() : '/') + 'blackjack-remote-asset');
        if (jsb.fileUtils.isDirectoryExist(_storagePath)) {
            console.log("检测到缓存文件");
            jsb.fileUtils.removeDirectory(_storagePath + "/");
        }
        if (jsb.fileUtils.isDirectoryExist(_storagePath + "_temp")) {
            console.log("检测到临时缓存文件");
            jsb.fileUtils.removeDirectory(_storagePath + "_temp/");
        }
        cv.native.JumpToUpdateSite(cv.dataHandler.getUserData().download_url);
    }

    public LowVirsionForSmallVersion() {
        cv.SHOP.unregisterMsg();
        cv.AudioMgr.stopAll();
        cc.game.restart();
    }

    public OnWebServerError(pkSender) {
        cv.TP.showMsg(pkSender, cv.Enum.ButtonStyle.GOLD_BUTTON, this.Logout.bind(this));
    }

    public OnLogoutSucc(pSend) {
        this.Logout();
    }

    public OnHttplogin() {
        cv.TP.showMsg(cv.config.getStringData("ServerErrorCode2"), cv.Enum.ButtonStyle.GOLD_BUTTON, this.LowVirsion.bind(this));
    }

    public OnReconnect(pSend) {
        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.GameId_Dummy) {
            this.OnWorldTimeOut();
        }
        else {
            this.OnGameTimeOut(0);
        }
    }

    /**
     * 程序切回前台通知
     */
    public OnAppEnterForeground() {
        console.log("OnAppEnterForeground==> websocket");
        cv.StatusView.updateValue();
        this._bOnAppEnterBack = false;
        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.VideoCowboy) {
            cv.MessageCenter.send("playVideoCowBoy");
        }
        if (cv.config.getCurrentScene() == cv.Enum.SCENE.PKF_LIVE_SCENE) {
            cv.MessageCenter.send(PKFConfig.broadcast.PLAY_LIVE);
        }
        if (cv.config.getCurrentScene() == cv.Enum.SCENE.LOGIN_SCENE || cv.config.getCurrentScene() == cv.Enum.SCENE.LOADING_SCENE) return;

        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.GameId_Dummy) {
            console.log("OnAppEnterForeground==> 000000");
            if (cv.netWork.isConnect()) {
                if (cv.dataHandler.getUserData().m_bIsLoginServerSucc && cv.config.GET_CLIENT_TYPE() != cv.Enum.ClientType.CowboyWeb) {
                    cv.worldNet.requestGetUserData();
                }
            }
            else if (!cv.netWork.isConnecting()) {
                // cv.netWork.close();
                console.log("OnAppEnterForeground==> 111111");
                if(cc.director.getScene().name != cv.Enum.SCENE.HOTUPDATE_SCENE && cc.director.getScene().name != cv.Enum.SCENE.LOGIN_SCENE){
                    this.UpdateNetwork(0);
                }
            }
        }
        else {
            console.log("OnAppEnterForeground==> 222222");
            if (cv.netWork.isConnect()) {
                if (!cv.dataHandler.getUserData().m_bIsLoginServerSucc) return;
                if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.CowBoy) {
                    cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    cv.roomManager.RequestJoinRoom();
                }
                if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.VideoCowboy) {
                    // cv.MessageCenter.send("playVideoCowBoy");
                    cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    cv.roomManager.RequestJoinRoom();
                }
                else if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Texas
                    || cv.GameDataManager.tRoomData.isZoom()
                    || cv.roomManager.getCurrentGameID() === cv.Enum.GameId.StarSeat
                    || cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Squid
                    || cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Plo) {
                    cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    cv.gameNet.RequestSnapshot(cv.GameDataManager.tRoomData.u32RoomId);
                }
                else if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.HumanBoy) {
                    cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    cv.roomManager.RequestJoinRoom();
                }
                else if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.PokerMaster) {
                    cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    cv.roomManager.RequestJoinRoom();
                }
                else if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.Allin) {
                    cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    cv.aofNet.RequestSnapshot(cv.GameDataManager.tRoomData.u32RoomId);
                } else if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.Bet) {
                    cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    cv.gameNet.RequestSnapshot(cv.GameDataManager.tRoomData.u32RoomId);
                } else if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.Jackfruit) {
                    cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    cv.jackfruitNet.requestGameDataSync(cv.roomManager.getCurrentRoomID());
                } else if (cv.roomManager.getCurrentGameID() == pb.GameId.Sports) {
                    // cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
                    // cv.worldNet.SportsLoginRequest();
                }
            }
            else if (!cv.netWork.isConnecting()) {
                // cv.netWork.close();
                // this.closeGameConnect(true);
                // this.closeWorldConnect(true);

                this.UpdateNetwork(0);
            }
        }
    }

    /**
     * 程序切入后台通知
     */
    public OnAppEnterBackground() {
        this._bOnAppEnterBack = true;
        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.VideoCowboy) {
            cv.MessageCenter.send("stopVideoCowBoy");
        }
        if (cv.config.getCurrentScene() == cv.Enum.SCENE.PKF_LIVE_SCENE) {
            cv.MessageCenter.send(PKFConfig.broadcast.STOP_LIVE);
        }
    }

    /**
     * 程序是否处于后台状态
     */
    public isAppEnterBackground(): boolean {
        return this._bOnAppEnterBack;
    }

    private _getLocalConnectionStatus():boolean{
        if(cc.sys.isNative)
            return cv.native.CheckNetWork();
        else 
          return navigator.onLine;
    }
}
