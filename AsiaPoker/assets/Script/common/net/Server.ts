import { HashMap } from "../tools/HashMap";
/**
 * web服 data服等相关域名接口信息
 */

export class Server {
    private static _g_instence: Server = null;
    // server map
    public serverMap: HashMap<string, string> = new HashMap();
    public DATA_GETDATA: string = "getdata";
    public DATA_GETROOMRECORDLIST: string = "room_records_list";
    public DATA_GETROOMRECORD: string = "room_record";
    public DATA_GETGAMEHAND: string = "game_hand";
    public DATA_DOFAVORITE: string = "do_favorite";
    public DATA_GETFAVORITELIST: string = "favorite_list";
    public DATA_GETFAVORITEHAND: string = "favorite_hand";
    public DATA_GETFAVORITELISTNew: string = "favorite_list_new";
    public DATA_GETPUBLICDATA: string = "get_public_data";
    public DATA_GETNEWHAND: string = "get_newhand_data";
    public DATA_GETONEHAND: string = "get_onehand_data";
    public WEB_API_SHOP: string = "";
    public WEB_API_EXCHANGE: string = "withdraw";
    public WEB_API_C2C_WITHDRAW: string = "confirm";
    public WEB_API_C2C_DEPOSITE: string = "upload";
    public WEB_API_TRADE_RECORD: string = "trade-record";
    public WEB_API_GET_RESOURCE_URL: string = "e1/user/login/getResources";


    //#region ENV DIFF CONFIGS REGION
    // ==========================  ENV DIFF CONFIGS REGION ============================/
    public WEB_API_HEAD: string = "https://a92smtp.depucloud.cn:22368/";
    public WEB_API_HEAD_SPARE: string =  "https://203.107.46.107:22368/";  
    public WEB_API_HEAD_SPARE2: string = "https://92smtp.omq2uk.com:22368/";
    public WEB_API_HEAD_SPARE3: string = "https://92smtp.codepurify.com:22368/";

    public WEB_API_HEAD_DEV: string = "https://coin5.dev.liuxinyi1.cn/";
    public WEB_API_HEAD_DEV_SPARE: string = "https://coin5.dev.liuxinyi1.cn/";
    public WEB_API_HEAD_DEV_SPARE2: string = "https://coin5.dev.liuxinyi1.cn/";
    public WEB_API_HEAD_DEV_SPARE3: string = "https://coin5.dev.liuxinyi1.cn/";

    public WEB_API_HEAD_STAGE: string = "http://*************:20000/";
    public WEB_API_HEAD_STAGE_SPARE: string = "http://*************:20000/";
    public WEB_API_HEAD_STAGE_SPARE2: string = "http://*************:20000/";
    public WEB_API_HEAD_STAGE_SPARE3: string = "http://*************:20000/";

    public WEB_API_CDN: string = "bp449Jw+Y3mMm//XZ3U5pNoysz8XtSYOwTla9AQ5jY7+qiCKCG350IS7PO/ny1RT";
    public WEB_API_CDN_DEV: string = "M2IbTbX8CAoO3TViyja/oSO++0JiG8T+n4RzO0y6kdLXuyL5iEJcLISQ5y/aO7U/";
    public WEB_API_CDN_STAGE: string = "M2IbTbX8CAoO3TViyja/oSO++0JiG8T+n4RzO0y6kdLXuyL5iEJcLISQ5y/aO7U/";

    public IP_CACHE_FOLDER: string = "ip/";
    public IP_CACHE_FOLDER_DEV: string = "ip_debug/";
    public IP_CACHE_FOLDER_STAGE: string = "ip_stage/";

    public WEB_API_SHOP_SIGN: string = "294de072c3d679f3a6adc5ff2c50b448e9265ebe";
    public WEB_API_SHOP_SIGN_DEV: string = "JWF7d20kSGdrQntCNiFTUGwraiRWejlCM3QkX3pedUw=";
    public WEB_API_SHOP_SIGN_STAGE: string = "JWF7d20kSGdrQntCNiFTUGwraiRWejlCM3QkX3pedUw=";

    public WEB_GEOCOMPLY_NDBS_URL: string = "https://cdn.geocomply.com/187/gc-html5.js";
    public WEB_GEOCOMPLY_NDBS_URL_DEV: string = "https://stg-cdn.geocomply.com/187/gc-html5.js";
    public WEB_GEOCOMPLY_NDBS_URL_STAGE: string = "https://stg-cdn.geocomply.com/187/gc-html5.js";

    public SEG_TOOL_WRITE_KEY: string = "PxwIQ3GqrdCa5sOWYB7MLohp4G0B1mWd";
    public SEG_TOOL_WRITE_KEY_DEV: string = "uNut3w5e699iteDFDPdrEqG3OcqgQQbl";
    public SEG_TOOL_WRITE_KEY_STAGE: string = "uNut3w5e699iteDFDPdrEqG3OcqgQQbl";

    // WEB AGORA SDK WEB
    public WEB_AGORA_REQ_HEAD: string = window.location.href;
    public WEB_AGORA_REQ_HEAD_DEV: string = "https://frontend.dev.liuxinyi1.cn/pkw-lite/";
    public WEB_AGORA_REQ_HEAD_STAGE: string = "https://frontend.dev.liuxinyi1.cn/pkw-lite/";
    // ==========================  ENV DIFF CONFIGS REGION ============================/
    //#endregion


    // IS_BRANCH is set to FALSE always, we can consider to remove it
    public WEB_API_HEAD_DEBUG_BRANCH: string = "https://coin5.dev.liuxinyi1.cn/";

    // barely used, can consider to remove it
    public WEB_API_HEAD_DEVELOP: string = "http://35.186.149.49:20000/";

    public WEB_API_FORGET_GET_VCODE: string = "index.php/User/Login/getVcodeByForgetPwd";
    public WEB_API_FORGET_VCODE: string = "index.php/User/Login/checkVcodeByForgetPwd";
    public WEB_API_FORGET_SUBMIT: string = "index.php/v3/User/Login/forgetPwd";
    public WEB_API_TRANSFER_GET_VCODE: string = "index.php/User/Club/getVcodeByClubTransfer";
    public WEB_API_TRANSFER_SUBMIT: string = "index.php/User/Club/checkVcodeByClubTransfer";
    public WEB_API_RESET_SAFE_VCODE: string = "index.php/User/Ucenter/getVcodeByResetSafe";
    public WEB_API_RESET_SAFE: string = "index.php/v3/User/Ucenter/resetSafe";
    public WEB_API_SET_DEFAULT_HEAD: string = "index.php/v3/User/Ucenter/uploadLocalAvatar";
    public WEB_API_VCODE: string = "index.php/User/Register/getVcodeByRegister";
    public WEB_API_TOURIST_VCODE: string = "index.php/User/tourists/getVcode";
    public WEB_API_CHECK_VCODE: string = "index.php/User/Register/checkVcodeByRegister";
    public WEB_API_CHECK_TOURIST_VCODE: string = "index.php/User/tourists/checkVcode";
    public WEB_API_REG: string = "index.php/v3/User/Register/userAdd";
    public WEB_API_TOURIST_UPGRADE: string = "index.php/v3/User/tourists/upgrade";
    public WEB_API_CHECK_NICK_NAME: string = "index.php/User/Register/checkNickname";
    public WEB_API_CHECK_USER_NAME: string = "index.php/User/Register/checkUsername";
    public WEB_API_GET_CAPTCHA: string = "index.php/User/Register/getCaptcha";
    public WEB_API_MODIFY_INFO: string = "index.php/v3/User/Ucenter/modifyUserInfo";
    public WEB_API_MODIFY_UPLOADVAR: string = "uploadavar";
    public WEB_API_MODIFY_UPLOADIMG: string = "uploadimg";
    public WEB_API_MODIFY_UPLOADIMGS: string = "uploadimgs";
    public WEB_API_LOGIN: string = "index.php/User/Login/loginByMobile";
    public WEB_API_LOGIN_BY_USER_NAME: string = "index.php/v3/e1/User/Login/loginByUsername"; 
    public WEB_API_LOGIN_BY_TOURIST_NAME: string = "index.php/v3/e1/User/tourists/login";
    public WEB_API_SET_USER_NAME: string = "index.php/v3/User/Login/setUsername";
    public WEB_API_RESET_PASS_VCODE: string = "index.php/User/Login/getVcodeByResetPwd";
    public WEB_API_RESET_PASS: string = "index.php/v3/User/Login/resetPwd";
    public WEB_API_LOGOUT: string = "index.php/v3/User/Ucenter/logout";
    public WEB_API_UPLOAD_VOICE: string = "index.php/User/Ucenter/uploadFileToQiniu";
    public WEB_API_UPLOAD_CLUB_HEAD: string = "index.php/User/Club/uploadClubAvatar";
    public WEB_API_CHECK_SAFE: string = "index.php/User/Ucenter/checkSafe";
    public WEB_API_QRCODE: string = "index.php/User/Qrcode/getCode";
    public WEB_API_NOTICE_LIST: string = "index.php/user/Article/getList";
    public WEB_API_GET_CUSTOM: string = "index.php/e1/user/register/getCustom";
    public WEB_PROTOTAL_RULE: string = "user/article/getAgreement?unique=register";
    public WEB_REQUEST_CRASH_INFO: string = "user/func/report";
    public WEB_BIND_SAFE_DEVICE: string = "/v3/user/device/bindSafeDevice";
    public WEB_GET_VCODE_BY_DEVICE: string = "/user/device/getVcodeByDevice";
    public WEB_ADD_FEEDBACK: string = "/user/addFeedback";
    public WEB_GET_FEEDBACK_LISTS: string = "/user/getFeedbackLists";
    public WEB_ADD_FEEDBACK_COMMENT: string = "/user/addFeedbackComment";
    public WEB_GET_FEEDBACK_COMMENT_LISTS: string = "/user/getFeedbackCommentLists";
    public WEB_MTT_GET_MATCH_LIST_DATA: string = "http://47.76.215.147:23333/mtt_game_results";  // MTT获取战绩列表链接
    public WEB_MTT_GET_MATCH_DEATAIL_DATA: string = "http://47.76.215.147:23333/mtt_game_result_detail";  // MTT获取战绩详情链接
    public WEB_MTT_GET_USER_INFO_DATA: string = "http://47.76.215.147:23333/mtt_game_sum";  // MTT获取战绩详情链接

    // Google Recaptcha
    public WEB_API_RECAPTCHA: string = "api/v1/config/recaptcha";
    // Google Recaptcha
    public WEB_API_RECAPTCHA_DOMAIN: string = "https://api.spicyonion.net/";

    // Jumio KYC
    public WEB_API_GET_JUMIO_CREDENTIAL: string = "api/v1/config/credentials";
    public WEB_API_POST_JUMIO_SCAN_REFERENCE: string = "api/v1/identity/jumio/check";
    public WEB_API_GET_JUMIO_STATUS: string = "api/v1/identity/jumio/status"

    // redirct url for EQUITY CALCULATOR 
    public WEB_API_EQUITY_CALCULATOR: string = "https://web.zcgfn.com";

    // 注册 或者  升级绑定私聊接口
    public WEB_BIND_SILIAO_GET_VCODE: string = "/user/register/getSlVcodeForAccount"; // 获取私聊验证码   
    public WEB_BIND_SILIAO_CHECK_VCODE: string = "/user/register/checkSlVcodeForAccount"; // 获取私聊验证码 
    // 激活短信 或者 私聊验证接口
    public WEB_BIND_ACTIVE_GET_PHONE_VCODE: string = "/User/Ucenter/getVcodeByChangeTypeForSms"; // 激活私聊验证，获取短信验证码
    public WEB_BIND_ACTIVE_CHECK_CHANGE_TYPE_MSG: string = "/User/Ucenter/checkVcodeByChangeTypeForSms"; // 激活检测短信验证
    public WEB_BIND_ACTIVE_GET_SILIAO_VCODE: string = "/User/Ucenter/getVcodeByChangeTypeForSL"; // 激活私聊验证，获取私聊验证码
    public WEB_BIND_ACTIVE_CHECK_CHANGE_TYPE_APP: string = "/User/Ucenter/checkVcodeByChangeTypeForSL"; // 激活私聊验证
    // 新设备通过私聊绑定接口
    public WEB_BIND_DEVICE_GET_VCODE: string = "/User/Device/getSlVcode"; // 绑定手机设备获取私聊验证码
    public WEB_BIND_DEVICE_CHECK_VCODE: string = "/v3/User/Device/checkSlVcode"; // 通过私聊绑定是手机

    public WEB_API_POST_DOMAIN_REPORT: string = "index.php/domain/report";

    // Agora SDK WEB 接口
    public WEB_AGORA_APP_ID: string = 'e3e16446c0d44bb6a04597f0668b9b6a';
    public WEB_AGORA_REQ_URL: string = 'extern/h5StreamLive/joinReq/index1.html';
    
    // Banner and Pop Out click counter
    public WEB_CLICK_COUNTER: string = 'click/counter';

    // Agora SDK WEB 本地调试接口
//    public WEB_AGORA_REQ_HEAD_DEBUG: string = 'https://jackychiu1024.github.io/'
//    public WEB_AGORA_REQ_URL: string = 'agoraH5/joinReq/index1.html';
    
    //key
    private  encryptKey: string = '@lnFi8' + '<eIKYazt:$_;' + 'MX9T/d(gk[JW3{Upcw';

    public init() {
        this.serverMap.add("DATA_GETDATA", this.DATA_GETDATA);
        this.serverMap.add("DATA_GETROOMRECORDLIST", this.DATA_GETROOMRECORDLIST);
        this.serverMap.add("DATA_GETROOMRECORD", this.DATA_GETROOMRECORD);
        this.serverMap.add("DATA_GETGAMEHAND", this.DATA_GETGAMEHAND);
        this.serverMap.add("DATA_DOFAVORITE", this.DATA_DOFAVORITE);
        this.serverMap.add("DATA_GETFAVORITELIST", this.DATA_GETFAVORITELIST);
        this.serverMap.add("DATA_GETFAVORITEHAND", this.DATA_GETFAVORITEHAND);
        this.serverMap.add("DATA_GETFAVORITELISTNew", this.DATA_GETFAVORITELISTNew);
        this.serverMap.add("DATA_GETPUBLICDATA", this.DATA_GETPUBLICDATA);
        this.serverMap.add("DATA_GETNEWHAND", this.DATA_GETNEWHAND);
        this.serverMap.add("DATA_GETONEHAND", this.DATA_GETONEHAND);
        this.serverMap.add("WEB_API_SHOP", this.WEB_API_SHOP);
        this.serverMap.add("WEB_API_EXCHANGE", this.WEB_API_EXCHANGE);
        this.serverMap.add("WEB_API_C2C_WITHDRAW", this.WEB_API_C2C_WITHDRAW);
        this.serverMap.add("WEB_API_C2C_DEPOSITE", this.WEB_API_C2C_DEPOSITE);
        this.serverMap.add("WEB_API_TRADE_RECORD", this.WEB_API_TRADE_RECORD);
        this.serverMap.add("WEB_API_GET_RESOURCE_URL", this.WEB_API_GET_RESOURCE_URL);

        //#region ENV DIFF CONFIGS REGION
        // ==========================  ENV DIFF CONFIGS REGION ============================/
        this.serverMap.add("WEB_API_HEAD", this.WEB_API_HEAD);
        this.serverMap.add("WEB_API_HEAD_SPARE", this.WEB_API_HEAD_SPARE);
        this.serverMap.add("WEB_API_HEAD_SPARE2", this.WEB_API_HEAD_SPARE2);
        this.serverMap.add("WEB_API_HEAD_SPARE3", this.WEB_API_HEAD_SPARE3);

        this.serverMap.add("WEB_API_HEAD_DEV", this.WEB_API_HEAD_DEV);
        this.serverMap.add("WEB_API_HEAD_DEV_SPARE", this.WEB_API_HEAD_DEV_SPARE);
        this.serverMap.add("WEB_API_HEAD_DEV_SPARE2", this.WEB_API_HEAD_DEV_SPARE2);
        this.serverMap.add("WEB_API_HEAD_DEV_SPARE3", this.WEB_API_HEAD_DEV_SPARE3);

        this.serverMap.add("WEB_API_HEAD_STAGE", this.WEB_API_HEAD_STAGE);
        this.serverMap.add("WEB_API_HEAD_STAGE_SPARE", this.WEB_API_HEAD_STAGE_SPARE);
        this.serverMap.add("WEB_API_HEAD_STAGE_SPARE2", this.WEB_API_HEAD_STAGE_SPARE2);
        this.serverMap.add("WEB_API_HEAD_STAGE_SPARE3", this.WEB_API_HEAD_STAGE_SPARE3);

        this.serverMap.add("WEB_API_CDN", this.WEB_API_CDN);
        this.serverMap.add("WEB_API_CDN_DEV", this.WEB_API_CDN_DEV);
        this.serverMap.add("WEB_API_CDN_STAGE", this.WEB_API_CDN_STAGE);

        this.serverMap.add("IP_CACHE_FOLDER", this.IP_CACHE_FOLDER);
        this.serverMap.add("IP_CACHE_FOLDER_DEV", this.IP_CACHE_FOLDER_DEV);
        this.serverMap.add("IP_CACHE_FOLDER_STAGE", this.IP_CACHE_FOLDER_STAGE);

        this.serverMap.add("WEB_API_SHOP_SIGN", this.WEB_API_SHOP_SIGN);
        this.serverMap.add("WEB_API_SHOP_SIGN_DEV", this.WEB_API_SHOP_SIGN_DEV);
        this.serverMap.add("WEB_API_SHOP_SIGN_STAGE", this.WEB_API_SHOP_SIGN_STAGE);

        this.serverMap.add("WEB_GEOCOMPLY_NDBS_URL", this.WEB_GEOCOMPLY_NDBS_URL);
        this.serverMap.add("WEB_GEOCOMPLY_NDBS_URL_DEV", this.WEB_GEOCOMPLY_NDBS_URL_DEV);
        this.serverMap.add("WEB_GEOCOMPLY_NDBS_URL_STAGE", this.WEB_GEOCOMPLY_NDBS_URL_STAGE);

        this.serverMap.add("SEG_TOOL_WRITE_KEY", this.SEG_TOOL_WRITE_KEY);
        this.serverMap.add("SEG_TOOL_WRITE_KEY_DEV", this.SEG_TOOL_WRITE_KEY_DEV);
        this.serverMap.add("SEG_TOOL_WRITE_KEY_STAGE", this.SEG_TOOL_WRITE_KEY_STAGE);

        // Agora SDK WEB 接口
        this.serverMap.add("WEB_AGORA_REQ_HEAD", this.WEB_AGORA_REQ_HEAD);
        this.serverMap.add("WEB_AGORA_REQ_HEAD_DEV", this.WEB_AGORA_REQ_HEAD_DEV);
        this.serverMap.add("WEB_AGORA_REQ_HEAD_STAGE", this.WEB_AGORA_REQ_HEAD_STAGE);
        // ==========================  ENV DIFF CONFIGS REGION ============================/
        //#endregion


        // barely no used, can consider to remove
        this.serverMap.add("WEB_API_HEAD_DEBUG_BRANCH", this.WEB_API_HEAD_DEBUG_BRANCH);
        this.serverMap.add("WEB_API_HEAD_DEVELOP", this.WEB_API_HEAD_DEVELOP);

        this.serverMap.add("WEB_API_FORGET_GET_VCODE", this.WEB_API_FORGET_GET_VCODE);
        this.serverMap.add("WEB_API_FORGET_VCODE", this.WEB_API_FORGET_VCODE);
        this.serverMap.add("WEB_API_FORGET_SUBMIT", this.WEB_API_FORGET_SUBMIT);
        this.serverMap.add("WEB_API_TRANSFER_GET_VCODE", this.WEB_API_TRANSFER_GET_VCODE);
        this.serverMap.add("WEB_API_TRANSFER_SUBMIT", this.WEB_API_TRANSFER_SUBMIT);
        this.serverMap.add("WEB_API_RESET_SAFE_VCODE", this.WEB_API_RESET_SAFE_VCODE);
        this.serverMap.add("WEB_API_RESET_SAFE", this.WEB_API_RESET_SAFE);
        this.serverMap.add("WEB_API_SET_DEFAULT_HEAD", this.WEB_API_SET_DEFAULT_HEAD);
        this.serverMap.add("WEB_API_VCODE", this.WEB_API_VCODE);
        this.serverMap.add("WEB_API_TOURIST_VCODE", this.WEB_API_TOURIST_VCODE);
        this.serverMap.add("WEB_API_CHECK_VCODE", this.WEB_API_CHECK_VCODE);
        this.serverMap.add("WEB_API_CHECK_TOURIST_VCODE", this.WEB_API_CHECK_TOURIST_VCODE);
        this.serverMap.add("WEB_API_REG", this.WEB_API_REG);
        this.serverMap.add("WEB_API_TOURIST_UPGRADE", this.WEB_API_TOURIST_UPGRADE);
        this.serverMap.add("WEB_API_CHECK_NICK_NAME", this.WEB_API_CHECK_NICK_NAME);
        this.serverMap.add("WEB_API_CHECK_USER_NAME", this.WEB_API_CHECK_USER_NAME);
        this.serverMap.add("WEB_API_GET_CAPTCHA", this.WEB_API_GET_CAPTCHA);
        this.serverMap.add("WEB_API_LOGIN", this.WEB_API_LOGIN);
        this.serverMap.add("WEB_API_MODIFY_INFO", this.WEB_API_MODIFY_INFO);
        this.serverMap.add("WEB_API_MODIFY_UPLOADVAR", this.WEB_API_MODIFY_UPLOADVAR);
        this.serverMap.add("WEB_API_LOGIN_BY_USER_NAME", this.WEB_API_LOGIN_BY_USER_NAME);
        this.serverMap.add("WEB_API_LOGIN_BY_TOURIST_NAME", this.WEB_API_LOGIN_BY_TOURIST_NAME);
        this.serverMap.add("WEB_API_SET_USER_NAME", this.WEB_API_SET_USER_NAME);
        this.serverMap.add("WEB_API_RESET_PASS_VCODE", this.WEB_API_RESET_PASS_VCODE);
        this.serverMap.add("WEB_API_RESET_PASS", this.WEB_API_RESET_PASS);
        this.serverMap.add("WEB_API_LOGOUT", this.WEB_API_LOGOUT);
        this.serverMap.add("WEB_API_RESET_PASS", this.WEB_API_RESET_PASS);
        this.serverMap.add("WEB_API_UPLOAD_VOICE", this.WEB_API_UPLOAD_VOICE);
        this.serverMap.add("WEB_API_UPLOAD_CLUB_HEAD", this.WEB_API_UPLOAD_CLUB_HEAD);
        this.serverMap.add("WEB_API_CHECK_SAFE", this.WEB_API_CHECK_SAFE);
        this.serverMap.add("WEB_API_QRCODE", this.WEB_API_QRCODE);
        this.serverMap.add("WEB_API_NOTICE_LIST", this.WEB_API_NOTICE_LIST);
        this.serverMap.add("WEB_API_GET_CUSTOM", this.WEB_API_GET_CUSTOM);
      
        this.serverMap.add("WEB_PROTOTAL_RULE", this.WEB_PROTOTAL_RULE);
        this.serverMap.add("WEB_REQUEST_CRASH_INFO", this.WEB_REQUEST_CRASH_INFO);
        this.serverMap.add("WEB_BIND_SAFE_DEVICE", this.WEB_BIND_SAFE_DEVICE);
        this.serverMap.add("WEB_GET_VCODE_BY_DEVICE", this.WEB_GET_VCODE_BY_DEVICE);
        this.serverMap.add("WEB_MTT_GET_MATCH_LIST_DATA", this.WEB_MTT_GET_MATCH_LIST_DATA);
        this.serverMap.add("WEB_MTT_GET_MATCH_DEATAIL_DATA", this.WEB_MTT_GET_MATCH_DEATAIL_DATA);
        this.serverMap.add("WEB_MTT_GET_USER_INFO_DATA", this.WEB_MTT_GET_USER_INFO_DATA);
        this.serverMap.add("WEB_API_RECAPTCHA", this.WEB_API_RECAPTCHA);
        this.serverMap.add("WEB_API_RECAPTCHA_DOMAIN", this.WEB_API_RECAPTCHA_DOMAIN);

        this.serverMap.add("WEB_API_GET_JUMIO_CREDENTIAL", this.WEB_API_GET_JUMIO_CREDENTIAL);
        this.serverMap.add("WEB_API_POST_JUMIO_SCAN_REFERENCE", this.WEB_API_POST_JUMIO_SCAN_REFERENCE);
        this.serverMap.add("WEB_API_GET_JUMIO_STATUS", this.WEB_API_GET_JUMIO_STATUS);
        
        this.serverMap.add("WEB_API_EQUITY_CALCULATOR", this.WEB_API_EQUITY_CALCULATOR);

        this.serverMap.add("WEB_BIND_SILIAO_GET_VCODE", this.WEB_BIND_SILIAO_GET_VCODE);
        this.serverMap.add("WEB_BIND_SILIAO_CHECK_VCODE", this.WEB_BIND_SILIAO_CHECK_VCODE);
        this.serverMap.add("WEB_BIND_ACTIVE_GET_PHONE_VCODE", this.WEB_BIND_ACTIVE_GET_PHONE_VCODE);
        this.serverMap.add("WEB_BIND_ACTIVE_CHECK_CHANGE_TYPE_MSG", this.WEB_BIND_ACTIVE_CHECK_CHANGE_TYPE_MSG);
        this.serverMap.add("WEB_BIND_ACTIVE_GET_SILIAO_VCODE", this.WEB_BIND_ACTIVE_GET_SILIAO_VCODE);
        this.serverMap.add("WEB_BIND_ACTIVE_CHECK_CHANGE_TYPE_APP", this.WEB_BIND_ACTIVE_CHECK_CHANGE_TYPE_APP);
        this.serverMap.add("WEB_BIND_DEVICE_GET_VCODE", this.WEB_BIND_DEVICE_GET_VCODE);
        this.serverMap.add("WEB_BIND_DEVICE_CHECK_VCODE", this.WEB_BIND_DEVICE_CHECK_VCODE);
        this.serverMap.add("WEB_API_POST_DOMAIN_REPORT", this.WEB_API_POST_DOMAIN_REPORT);

        // Agora SDK WEB 接口
        this.serverMap.add("WEB_AGORA_REQ_URL", this.WEB_AGORA_REQ_URL);
        this.serverMap.add("WEB_AGORA_APP_ID", this.WEB_AGORA_APP_ID);
        
        this.serverMap.add("WEB_CLICK_COUNTER", this.WEB_CLICK_COUNTER);
    }

    /**
     * 是否有此key
     * @param key 
     */
    public ishaveKey(key: string): boolean {
        return this.serverMap.has(key);
    }

    /**
     * 获取web接口
     * @param key 
     */
    public getString(key: string): string {
        return this.serverMap.get(key);
    }

    public static getInstance(): Server {
        if (!Server._g_instence) {
            Server._g_instence = new Server();
            Server._g_instence.init();
        }
        return Server._g_instence;
    }

    public GET_ENCRYPT_KEY(): string {
        return this.encryptKey.substring(0, 32);
    }
}
