// import cv from "../../Script/components/lobby/cv";
// import { AppConfig } from "../../Script/config/AppConfig";
import { FontOverride, FontSystemModel, FontSystemType, FontSystemWeight, FontSystemWeightCustom } from "./FontSystemModel";

const { ccclass, property, executeInEditMode, playOnFocus } = cc._decorator;

@ccclass
@executeInEditMode
@playOnFocus
export default class FontSystemInfo extends cc.Component {
    @property({ type: cc.Enum(FontSystemType), readonly: true }) fontType: FontSystemType = FontSystemType.SFProDisplay;
    @property({ 
        type: cc.Enum(FontOverride), 
    }) useMethod: FontOverride = FontOverride.MainFont;
    @property({ 
        type: cc.Enum(FontSystemType),
        visible: function (this: FontSystemInfo) { 
            return this.useMethod == FontOverride.CustomOverride;
          }
        }
    ) customOverrideFontType: FontSystemType = FontSystemType.SFProDisplay;
    @property({ type: cc.Enum(FontSystemWeight) }) fontWeight: FontSystemWeight = FontSystemWeight.Custom;
    @property({ 
        type: cc.Enum(FontSystemWeightCustom),
        visible: function (this: FontSystemInfo) { 
            return this.fontWeight == FontSystemWeight.Custom;
          }
        }
    ) fontCustomWeight: FontSystemWeightCustom = FontSystemWeightCustom.W100;
    @property fontSize: number = 40;

    // keep track current font values.
    currentValues: any = {}
    editorFocus: boolean = false;


    onLoad(){ 
        if (CC_EDITOR) {
            this.setCurrentValues();
        } 
      
        this.setFont(this.getDefaultFont());
    }

    private getDefaultFont(): FontSystemType {
        let font: FontSystemType;
        if (this.useMethod == FontOverride.MainFont) {
            font = this.getMainFontUse();
        } else if (this.useMethod == FontOverride.SecondaryFont) {
            font = this.getSecondaryFontUse();
        } else {
            font = this.customOverrideFontType;
        }
        return font;
    }

    private getMainFontUse(): FontSystemType {
        return FontSystemType.SFProDisplay;
        // let font:FontSystemType;
        // if (CC_EDITOR) {
        //     font = AppConfig.Instance.mainFontUse;
        // } 
        // else {
        //     font = cv.appConfig.mainFontUse;
        // }
        // return font;
    }

    private getSecondaryFontUse(): FontSystemType {
        return FontSystemType.SFProDisplay;
        // let font:FontSystemType;
        // if (CC_EDITOR) {
        //     font = AppConfig.Instance.secondaryFontUse;
        // } 
        // else {
        //     font = cv.appConfig.secondaryFontUse;
        // }
        // return font;
    }

    onFocusInEditor() {
        this.editorFocus = true;
    }

    onLostFocusInEditor() {
        this.editorFocus = false;
    }

    update(dt: number) {
        if (this.editorFocus)
        {
            if(this.isChanged())
            {
                this.setFont(this.getDefaultFont());
                this.setCurrentValues();
            }
        }
    }

    // check if has any change
    public isChanged(): boolean
    {
        return this.useMethod == FontOverride.CustomOverride && this.currentValues.fontType != this.customOverrideFontType ||
               this.useMethod == FontOverride.SecondaryFont && this.currentValues.fontType != this.getSecondaryFontUse() ||
               this.useMethod == FontOverride.MainFont && this.currentValues.fontType != this.getMainFontUse() ||
               this.currentValues.fontWeight !== this.fontWeight ||
               this.currentValues.fontType !== this.fontType ||
               this.currentValues.fontSize !== this.fontSize ||
               this.currentValues.fontCustomWeight !== this.fontCustomWeight;
    }

    // set current value to be updated
    public setCurrentValues(): void
    {
        this.currentValues.fontWeight = this.fontWeight;
        this.currentValues.fontType  = this.fontType;
        this.currentValues.fontSize = this.fontSize;
        this.currentValues.fontCustomWeight = this.fontCustomWeight;
    }

    public setFont(font: FontSystemType){
        this.fontType = font;
        this.applyFont();
    }

    protected applyFont(): void {
        FontSystemModel.getInstance().getFont(this.getFontAndApply.bind(this), this.fontType, this.fontWeight, this.fontCustomWeight);
    }

    protected getFontAndApply(font: cc.Font): void {
    }
}
