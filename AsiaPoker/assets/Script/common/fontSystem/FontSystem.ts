import FontSystemInfo from "./FontSystemInfo";
const { ccclass, requireComponent, menu } = cc._decorator;

@ccclass
@requireComponent(cc.Label)
@menu("Design System/Font System")
export default class FontSystem extends FontSystemInfo {
    editorFocus: boolean = false;
    label:  cc.Label  = null;

    protected applyFont() {
        if (this.label == null)
            this.label = this.getComponent(cc.Label);
        super.applyFont();
    }

    protected getFontAndApply(font: cc.Font): void {
        if (!this.label) return;
        this.label.font = font;
        this.label.useSystemFont = false;
        this.label.fontSize = this.fontSize;
        // if bold or italic is enabled then this label will fall back on using system font instead of custom one, especially in builds
        this.label.enableBold = false;
        this.label.enableItalic = false;
    }
}
