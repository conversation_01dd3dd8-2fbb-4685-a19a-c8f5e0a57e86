//import { AppConfig } from "../../Script/config/AppConfig";

export enum FontSystemType{
    SFProDisplay = 0,
    Montserrat = 1,
    Averta = 2
}

export enum FontOverride {
    MainFont = 0,
    SecondaryFont = 1,
    CustomOverride = 2
}

export enum FontSystemWeight{
    Custom = 0,
    TitleSemibold,
    HeadlineSemibold,
    TextSemiBold,
    Text1Medium,
    Text2Regular,
    Text3Regular,
    Text4Medium,
    Subhead1Medium,
    Subhead2Semibold,
    CaptionCapsSemibold,
    CaptionCapsMedium
}

export enum FontSystemWeightCustom{
    W100 = 0,
    W200,
    W300,
    W400,
    W500,
    W600,
    W700,
    W800,
    W900
}

// Array of font system
// font file name should be same as font family name in order to work on windows platform
export const FontSystems=[
    { key: FontSystemType.SFProDisplay, value:[
       {key: FontSystemWeightCustom.W100, value:"SFProDisplay_Thin" },
       {key: FontSystemWeightCustom.W200, value:"SFProDisplay_ExtraLight" },
       {key: FontSystemWeightCustom.W300, value:"SFProDisplay_Light" },
       {key: FontSystemWeightCustom.W400, value:"SFProDisplay_Regular" },
       {key: FontSystemWeightCustom.W500, value:"SFProDisplay_Medium" },
       {key: FontSystemWeightCustom.W600, value:"SFProDisplay_SemiBold" },
       {key: FontSystemWeightCustom.W700, value:"SFProDisplay_Bold" },
       {key: FontSystemWeightCustom.W800, value:"SFProDisplay_Heavy" },
       {key: FontSystemWeightCustom.W900, value:"SFProDisplay_Black" },
    ]},
    { key: FontSystemType.Montserrat, value:[
       {key: FontSystemWeightCustom.W100, value:"Montserrat_Thin" },
       {key: FontSystemWeightCustom.W200, value:"Montserrat_ExtraLight" },
       {key: FontSystemWeightCustom.W300, value:"Montserrat_Light" },
       {key: FontSystemWeightCustom.W400, value:"Montserrat_Regular" },
       {key: FontSystemWeightCustom.W500, value:"Montserrat_Medium" },
       {key: FontSystemWeightCustom.W600, value:"Montserrat_SemiBold" },
       {key: FontSystemWeightCustom.W700, value:"Montserrat_Bold" },
       {key: FontSystemWeightCustom.W800, value:"Montserrat_ExtraBold" },
       {key: FontSystemWeightCustom.W900, value:"Montserrat_Black" },
   ]},
   { key: FontSystemType.Averta, value:[
       {key: FontSystemWeightCustom.W100, value:"AvertaCY-Thin" },
       {key: FontSystemWeightCustom.W200, value:"AvertaCY-ExtraLight" },
       {key: FontSystemWeightCustom.W300, value:"AvertaCY-Light" },
       {key: FontSystemWeightCustom.W400, value:"AvertaCY-Regular" },
       {key: FontSystemWeightCustom.W500, value:"AvertaCY-Regular" },
       {key: FontSystemWeightCustom.W600, value:"AvertaCY-SemiB" },
       {key: FontSystemWeightCustom.W700, value:"AvertaCY-Bold" },
       {key: FontSystemWeightCustom.W800, value:"AvertaCY-ExtraB" },
       {key: FontSystemWeightCustom.W900, value:"AvertaCY-Black" },
   ]},
];


export class FontSystemModel{
    private static instance: FontSystemModel;
    public static getInstance(): FontSystemModel {
        if (!this.instance) {
            this.instance = new FontSystemModel();
        }
        return this.instance;
    }

    private fontCache : Map<string, cc.Font> = new Map<string, cc.Font>();

    constructor(){
        // this.initFontCache(AppConfig.Instance.mainFontUse);
        // this.initFontCache(AppConfig.Instance.secondaryFontUse);
        this.initFontCache(FontSystemType.SFProDisplay);
    }

    private initFontCacheAll(){
        for (let font = 0; font < Object.keys(FontSystemType).length; font++) {
            this.initFontCache(font);
        }
    }

    private initFontCache(font: FontSystemType) {
        for (let weight = 0; weight < Object.keys(FontSystemWeightCustom).length; weight++) {
            this.addFontWeightToCache(this.getFontPath(font, FontSystemWeight.Custom, weight));
        }
    }

    private addFontWeightToCache(fontPath: string, callback: (font: cc.Font) => void = null) {
        if(CC_EDITOR){
            let editorPath = Editor.assetdb.remote.urlToUuid("db://assets/resources/" + fontPath + ".ttf");
            cc.assetManager.loadAny({ "uuid": editorPath}, function  (err, font){
                if (err) {
                    cc.error(err.message || err);
                    return;
                }
                this.fontCache.set(fontPath, font);
                callback?.(font);
            }.bind(this));
            return;
        };
        cc.resources.load(fontPath, cc.Font, function  (err, font){
            if (err) {
                cc.error(err.message || err);
                return;
            }
            this.fontCache.set(fontPath, font);
            callback?.(font);
        }.bind(this));
    }

    private getFontPath(font: FontSystemType, weight: FontSystemWeight, customWeight: FontSystemWeightCustom): string{
        let fontName = "Fonts/" + FontSystemType[font];
        
        if (weight == FontSystemWeight.Custom) {
            fontName += this.getFontFileName(font, customWeight);
        } 
        else {
            switch(weight) {
                case FontSystemWeight.TitleSemibold:
                case FontSystemWeight.HeadlineSemibold:
                case FontSystemWeight.TextSemiBold:
                case FontSystemWeight.Subhead1Medium:
                case FontSystemWeight.Subhead2Semibold:
                case FontSystemWeight.CaptionCapsMedium:
                    fontName += this.getFontFileName(font, FontSystemWeightCustom.W600);
                    break;
                case FontSystemWeight.Text1Medium:
                case FontSystemWeight.Text4Medium:
                    fontName += this.getFontFileName(font, FontSystemWeightCustom.W500);
                    break;
                case FontSystemWeight.Text2Regular:
                case FontSystemWeight.Text3Regular:
                    fontName += this.getFontFileName(font, FontSystemWeightCustom.W400);
                    break;
                case FontSystemWeight.CaptionCapsSemibold:
                    fontName += this.getFontFileName(font, FontSystemWeightCustom.W700);
                    break;
            }
        }
        return fontName;
    }

    public getFont(callback: (font: cc.Font) => void, font: FontSystemType, weight: FontSystemWeight, customWeight: FontSystemWeightCustom = 0): void {
        let fontPath = this.getFontPath(font, weight, customWeight);
        if (this.fontCache.has(fontPath))
            callback?.(this.fontCache.get(fontPath));
        else
            this.addFontWeightToCache(fontPath, callback);
    }

    /** get file name of the selected font */
    private getFontFileName(fontType: FontSystemType, customWeight: FontSystemWeightCustom): string {
        const fonts = this.getElementByKey(FontSystems, fontType);
        let ret: string ="";
        if(fonts !== undefined)
        {
            const font = this.getElementByKey(fonts.value, customWeight );
            if(font !== undefined)
            ret += "/" + font.value;
        }
        
        return ret;
    }

    /** get element by key in an array of element */
    private getElementByKey(elements: any, key: any): any
    {
        return elements.find(e =>  e.key === key);
    }
}
