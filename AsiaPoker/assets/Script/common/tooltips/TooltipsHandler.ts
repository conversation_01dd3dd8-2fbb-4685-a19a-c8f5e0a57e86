import cv from "../../components/lobby/cv";

const { ccclass, property } = cc._decorator;

export enum TooltipType {
    ArrowUpLeft = 0,
    ArrowUpMiddle = 1,
    ArrowUpRight = 2,
}

export enum EdgeDistance {
    Middle = 0,
    Right = 36,
    Left = 36,
}

@ccclass
export default class TooltipsHandler extends cc.Component {
    @property({
        type: cc.Node
    }) base: cc.Node = null;
    @property({
        type: cc.Label,
        tooltip: "Tooltip content for pointer left"
    }) content: cc.Label = null;
    @property({
        type: cc.Node
    }) bg: cc.Node = null;
    @property({
        type: cc.Node
    }) upArrow: cc.Node = null;

    public onDisableCallback: Function = null;
    private baseBgY: number;
    private baseArrowY: number;
    
    protected onLoad(): void {
        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick.bind(this));
        this.baseBgY = this.bg.position.y;
        this.baseArrowY = this.upArrow.position.y;
    }

    private onNodeClick(): void {
        this.hideNode();
    }

    public hideNode(): void {
        this.node.active = false;
        this.onDisableCallback(this.node);
    }

    /**
     * Set toolTips panel width fit content or title's width or user defined maximum width.
     * @param content       Tooltips content Node: could be this.contentPointerDown.node or this.contentPointerLeft.node
     * @param bg            Tooltips backGround Node: could be this.pointerDownMessageBg or this.pointerLeftMessageBg
     * @param maxWidth      user defined maximum width
     */
    private _fitWidth(content: cc.Node, bg: cc.Node, maxWidth: number) {
        const _contentLabel: cc.Label = content.getComponent(cc.Label);
        const _textWidth: number = cv.resMgr.getLabelStringSize(_contentLabel, _contentLabel.string).width;
        const _contentWidget: cc.Widget = content.getComponent(cc.Widget);
        const newContentWidth = _textWidth + _contentWidget.left + _contentWidget.right;
        let newWidth: number = bg.width;
        if (maxWidth == 0) {
            newWidth = newContentWidth;
        }
        bg.width = newWidth;
    }

    public showToolTips(content: string, type: TooltipType, maxTooltipWidth: number = 0): void {
        switch (type) {
            case TooltipType.ArrowUpLeft:
                this._setToolTipContentAndArrow(content, maxTooltipWidth, EdgeDistance.Left, type);
                break;
            case TooltipType.ArrowUpMiddle:
                this._setToolTipContentAndArrow(content, maxTooltipWidth, EdgeDistance.Middle, type);
                break;
            case TooltipType.ArrowUpRight:
                this._setToolTipContentAndArrow(content, maxTooltipWidth, EdgeDistance.Right, type);
                break;
            default:
                throw new Error ('TooltipsHandler :: showToolTips : Can not find this kind of TooltipType');
        }

        // move the tooltip outside the screen to update everything then move it back to correct possition after 1 frame, to not flicker while updating components
        this.node.setPosition(5000);
        this.scheduleOnce(() => {
            this.base.position = new cc.Vec2(0, 0);
        }, 0.02);
    }

    private _setToolTipContentAndArrow(content: string, maxTooltipWidth: number = 0, setXEdgeDistance: number, toolTipType: TooltipType) {
        this.content.string = content;
        this._fitWidth(this.content.node, this.bg, maxTooltipWidth);
        switch (toolTipType) {
            case TooltipType.ArrowUpLeft:
                this.bg.setPosition((this.bg.width / 2 - setXEdgeDistance), this.baseBgY);
                break;
            case TooltipType.ArrowUpMiddle:
                this.bg.setPosition(0, this.baseBgY);
                break;
            case TooltipType.ArrowUpRight:
                this.bg.setPosition(-(this.bg.width / 2 - setXEdgeDistance), this.baseBgY);
                break;
            default:
                throw new Error ('TooltipsHandler :: showToolTips : Can not find this kind of TooltipType');
        }
        this.upArrow.setScale(1, 1);
        this.upArrow.setPosition(this.upArrow.position.x, this.baseArrowY);
    }
}
