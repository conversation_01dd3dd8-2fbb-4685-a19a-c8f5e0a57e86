import cv from "../../components/lobby/cv";

const { ccclass, property } = cc._decorator;

@ccclass
export default class CoachTip extends cc.Component {

    @property(cc.RichText)
    richText: cc.RichText = null;

    @property(cc.Texture2D)
    texture: cc.Texture2D = null;

    // ｜---|===|---|===|----|
    // the parts "===" are the slice parts.
    // x-y z-w are the coordinates of the slice.
    @property(cc.Vec4)
    segments: cc.Vec4 = new cc.Vec4(0, 0, 0, 0);

    // the vertical part is divided into 3 parts, stretching the middle part and keeping the characteristics of the two sides
    // x y are the coordinates of the slice in the texture.
    @property(cc.Vec2)
    segmentsVerttical: cc.Vec2 = new cc.Vec2(0, 0);

    // the transparent area of the arrow head to the edge of the texture.
    @property(cc.Integer)
    offset: number = 0;

    private _displayTime = 0;
    private _displayDuration = 3;

    private _onClose: Function = null;
    private _closeMessages: string[] = [];
    private _direction: number = 1;

    private _aniDuration = .25;
    private _aniTime = 0;
    private _isClosing = false;

    protected onLoad(): void {
        this.node.on(cc.Node.EventType.TOUCH_END, this.close, this);
    }

    protected onEnable(): void {
        this._aniTime = 0;
        this._displayTime = 0;
    }

    protected onDisable(): void {
        while(this._closeMessages.length) {
            const message = this._closeMessages.pop();
            cv.MessageCenter.unregister(message, this.node);
        }
        this.unschedule(this.close);
        this._onClose?.();
        this._onClose = null;
        this._isClosing = false;
    }

    /**
     * 
     * @param text the text display on the tip
     * @param duration the duration of the tip, if 0, it will not close automatically
     */
    public show(text: string, duration = 3) {
        this.node.active = true;
        this.setText(text);

        this._displayDuration = duration;
        this._displayTime = 0;
    }

    public setDirectionUp(up = true) {
        this.node.scaleY = up ? -1 : 1;
        this.richText.node.scaleY = up ? -1 : 1;
        this._direction = up ? -1 : 1;
    }

    /** 
     * @param onClose the callback function when the tip is closed, it will be set to null after the tip is closed
    */
    public setCloseListener(onClose: Function) {
        this._onClose = onClose;
    }

    /** 
     * In this life cycle, add custom close message name to close the tip
     * @param message the message name
     * If you addCloseMessage("message"), when you use cv.MessageCenter.send(message), the tip will be closed
     */
    public addCloseMessage(message: string) {
        if(this._closeMessages.indexOf(message) !== -1) {
            return;
        }
        this._closeMessages.push(message);
        cv.MessageCenter.register(message, this.close.bind(this), this.node);
    }

    public setText(text: string) {
        const richTxt = this.richText;
        richTxt.string = text;
    }

    close() {
        if(this._isClosing) {
            return;
        }
        this._isClosing = true;
        this._aniTime = 0;
    }

    private _updateMesh() {
        const paddingOneSide = 50;
        this.node.width = this.richText.node.width + paddingOneSide * 2;
        this.node.height = this.richText.node.height;

        const contentHeight = this.richText.node.height;
        // p0-p1 p2-p3 are the coordinates of the slice.
        const { x: p0, y: p1, z: p2, w: p3 } = this.segments;

        const texWidth = this.texture.width;

        const nodeWidth = this.node.width;

        const texture = this.texture;

        const uvBottom = 0; 
        const uvTop = 1; 
        const uvLeft = 0; 
    
        const uvWidth = 1; 

        // calculate uv in one row
        const u0 = uvLeft + (0 / texWidth) * uvWidth;
        const u1 = uvLeft + (p0 / texWidth) * uvWidth;
        const u2 = uvLeft + (p1 / texWidth) * uvWidth;
        const u3 = uvLeft + (p2 / texWidth) * uvWidth;
        const u4 = uvLeft + (p3 / texWidth) * uvWidth;
        const u5 = uvLeft + (texWidth / texWidth) * uvWidth;

        const us = [u0, u1, u2, u3, u4, u5];

        const fixedWidthLeft = p0;
        const fixedWidthCenter = p2 - p1;
        const fixedWidthRight = texWidth - p3;

        const stretchTarget = Math.max(nodeWidth - (fixedWidthLeft + fixedWidthCenter + fixedWidthRight), 0);
        const stretchEach = stretchTarget / 2;

        // splice 5 parts, the left, center, right are fixed, the middle is stretchable.
        const widths = [
            fixedWidthLeft,
            stretchEach,
            fixedWidthCenter,
            stretchEach,
            fixedWidthRight,
        ];

        // calculate the x position of each vertex
        const x = [- nodeWidth * this.node.anchorX];
        for (let i = 0; i < widths.length; i++) {
            x.push(x[i] + widths[i]);
        }

        // split height to 3 parts
        const y0 = 0;
        const y1 = this.segmentsVerttical.x;
        const y2 = this.segmentsVerttical.y;

        const v0 = uvBottom;
        const v1 = uvBottom + (y1 / texture.height);
        const v2 = uvBottom + (y2 / texture.height);
        const v3 = uvTop;
        const vs = [v0, v1, v2, v3];

        const fixedHeightBottom = y1;
        const fixedHeightTop = texture.height - y2;
        const fixedHeightCenter = Math.max(0, contentHeight);

        // center
        this.richText.node.y = -(y1 + fixedHeightCenter * .5) + this.offset;

        const heights = [
            fixedHeightBottom,
            fixedHeightCenter,
            fixedHeightTop,
        ]
        const y = [y0 + this.offset];
        for (let i = 0; i < heights.length; i++) {
            y.push(y[i] - heights[i]);
        }

        const vertices: number[] = [];
        for (let i = 0; i < x.length; i++) {
            for(let j = 0; j < y.length; j++) {
                vertices.push(x[i], y[j]);
            }
        }

        // calculate the uv position of each vertex
        const uvs: number[] = [];
        for (let i = 0; i < us.length; i++) {
           for(let j = 0; j < vs.length; j++) {
                uvs.push(us[i], vs[j]);
            }
        }

        // create mesh and set it's vertexFormat 
        const mesh = new cc.Mesh();
        let gfx: any = cc['gfx'];
        let vfmt: any = new gfx.VertexFormat([
            { name: gfx.ATTR_POSITION, type: gfx.ATTR_TYPE_FLOAT32, num: 3 },
            { name: gfx.ATTR_COLOR, type: gfx.ATTR_TYPE_UINT8, num: 4 },
            { name: gfx.ATTR_UV0, type: gfx.ATTR_TYPE_FLOAT32, num: 2 },
        ]);

        const positions = [];
        const uv0s = [];
        const colors = [];

        const color = cc.Color.scale(new cc.Color, this.node.color, 1/255);
        // fill the mesh vertices
        for (let i = 0; i < vertices.length; i += 2) {
            const x = vertices[i];
            const y = vertices[i + 1];
            const worldPos = this.node.convertToWorldSpaceAR(cc.v2(x, y));
            // set the vertex position
            positions.push(worldPos.x, worldPos.y, 0);
            // set the vertex uv
            uv0s.push(uvs[i], uvs[i + 1]);
            // set the vertex color to white.
            colors.push(color.r, color.g, color.b, color.a);
        }

        // const vertexCountPerRow = uvX.length;
        const indices: number[] = [];
        // calculate the triangle indices, total 5 Quuad and 2 triangles per Quad.
        for (let i = 0; i < us.length - 1; i ++) {
            for(let j = 0; j < vs.length - 1; j++) {
                const ind = i * vs.length + j;
                const lb = ind;
                const lt = ind + 1;
                // skip a whole row, so need to skip a whole row
                const rb = ind + vs.length;
                const rt = ind + vs.length + 1;
                // 2 triangles per Quad
                indices.push(lb, lt, rt, lb, rt, rb);
            }
        }

        mesh.init(vfmt, indices.length);
        mesh.setVertices(gfx.ATTR_POSITION, positions);
        mesh.setVertices(gfx.ATTR_COLOR, colors);
        mesh.setVertices(gfx.ATTR_UV0, uv0s);
        mesh.setIndices(indices);

        this.getComponent(cc.MeshRenderer).mesh = mesh;
        this.getComponent(cc.MeshRenderer).getMaterial(0).setProperty('texture', texture);
    }


    // update mesh when the transform is changed.
    lateUpdate() {
        const renderFlag = this.node['_renderFlag'] | this.richText.node['_renderFlag'];
        if(renderFlag & cc['RenderFlow'].FLAG_WORLD_TRANSFORM) {
            this._updateMesh();
        }
    }

    update(dt: number) {
        // in-out animations
        if(this._aniTime < this._aniDuration) {
            this._aniTime += dt;
            let t = this._aniTime / this._aniDuration;

            let scale = 0;
            if(this._isClosing) {
                t = cc.easeBackIn().easing(t);
                scale = cc.misc.lerp(1, 0, t);
            }else {
                t = cc.easeBackOut().easing(t);
                scale = cc.misc.lerp(0, 1, t);
            }

            this.node.scaleX = scale;
            this.node.scaleY = scale * this._direction;
            this.richText.node.scaleY = this._direction;

            if(this._aniTime >= this._aniDuration && this._isClosing) {
                this.node.active = false;
            }
            return;
        }

        if(this._displayTime < this._displayDuration) {
            this._displayTime += dt;
            if(this._displayTime >= this._displayDuration) {
                this.close();
            }
        }
    }
}
