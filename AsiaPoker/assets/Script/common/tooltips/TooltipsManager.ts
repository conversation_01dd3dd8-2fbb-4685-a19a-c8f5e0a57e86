import cv from "../../../Script/components/lobby/cv";
import CoachTip from "./CoachTip";
import TooltipsHandler, { TooltipType } from "./TooltipsHandler";

const TOOLTIPS_PREFAB_PATH = "zh_CN/commonPrefab/tooltips";
const TIP_DIR_PATH = "zh_CN/commonPrefab/";

const TipMap = {
    "CoachTip": CoachTip
};  

type TipMap = typeof TipMap;
type TipClass = new (...args: any[]) => cc.Component; // 即所有 value 类的联合类型

export default class TooltipsManager {
    private static instance: TooltipsManager;

    public static getInstance(): TooltipsManager {
        if (!this.instance) {
            TooltipsManager.instance = new TooltipsManager();
            TooltipsManager.instance.init();
        }
        return TooltipsManager.instance;
    };

    public init(): void {
        cv.resMgr.load(TOOLTIPS_PREFAB_PATH, cc.Prefab, (prefab: cc.Prefab): void => {
            this.tooltipPrefab = prefab;
        });

        for(let tipName in this.TipType) {
            const path = TIP_DIR_PATH + tipName;
            cv.resMgr.load(path, cc.Prefab, (prefab: cc.Prefab): void => {
                this._prefabs.set(this.TipType[tipName], prefab);
            });
        }

        this.nodePool = new cc.NodePool();
    }

    /**
     * @param position 
     * @param content 
     * @param type 
     * @param maxTooltipWidth 
     */
    public showToolTips(content: string, type: TooltipType = TooltipType.ArrowUpLeft, maxTooltipWidth: number = 0, targetNode: cc.Node) {
        if (!cc.isValid(targetNode)) {
            throw new Error ('TooltipsManager :: showToolTips : No targetNode');
        }

        if (maxTooltipWidth != 0 && (maxTooltipWidth >= cc.view.getVisibleSize().width || maxTooltipWidth < 0)) {
            maxTooltipWidth = cc.view.getVisibleSize().width - 100;
        }  

        this._showToolTips(content, type, maxTooltipWidth, targetNode);
    }

    protected tooltipPrefab: cc.Prefab = null;
    private nodePool: cc.NodePool = null;
    private containerNode: cc.Node = null;
    private _listShowedTooltips: cc.Node[] = [];

    public isAnyTooltipsShowed(): boolean {
        return this._isAnyTooltipsShowed();
    }

    private _isAnyTooltipsShowed(): boolean {
        return this._listShowedTooltips.length > 0;
    }

    public hideAllTooltips() {
        this._executeHideAllTooltips();
    }

    private _executeHideAllTooltips(): void {
        while (this._listShowedTooltips.length > 0) {
            let aTooltip: cc.Node = this._listShowedTooltips[0];
            if (cc.isValid(aTooltip, true)) {
                let handler: TooltipsHandler = aTooltip?.getComponent(TooltipsHandler);
                if (handler) {
                    handler.hideNode();
                } 
                else {
                    this._listShowedTooltips.splice(0, 1);
                }
            } 
            else {
                this._listShowedTooltips.splice(0, 1);
            }
        }
    }

    private _showToolTips(content: string, type: TooltipType = TooltipType.ArrowUpLeft, maxTooltipWidth: number = 0, targetNode: cc.Node): void {
        let aNode: cc.Node = this._getTooltipsNode(targetNode);
        if (aNode === null) {
            cc.error("Can not load a new tooltips node");
            return;
        }

        aNode.active = true;

        let tooltipsHandler = aNode.getComponent(TooltipsHandler);
        if (tooltipsHandler === null) {
            cc.error("Can not get TooltipsHandler");
            return;
        }

        tooltipsHandler.showToolTips(content, type, maxTooltipWidth);

        let foundOldTooltips: boolean = false;
        for (let index = 0; index < this._listShowedTooltips.length; index++) {
            let aTooltip: cc.Node = this._listShowedTooltips[index];

            if (aTooltip === aNode) {
                foundOldTooltips = true;
                break;
            }
        }

        if (!foundOldTooltips){
            this._listShowedTooltips.push(aNode);
        }
    }

    private _onTooltipsDisable(disabledNode: cc.Node): void {
        this.nodePool.put(disabledNode);

        for (let index = 0; index < this._listShowedTooltips.length; index++) {
            let aTooltip: cc.Node = this._listShowedTooltips[index];

            if (aTooltip === disabledNode) {
                this._listShowedTooltips.splice(index, 1);
                break;
            }
        }
    }

    private _getTooltipsNode(targetNode): cc.Node {
        let resultNode: cc.Node;
        
        if ((this.containerNode !== null && !cc.isValid(this.containerNode)) || this.containerNode === null) {
            this.containerNode = new cc.Node("TooltipContainer");
            this.containerNode.setParent(targetNode);
            this.containerNode.width = cc.view.getVisibleSize().width * 2;
            this.containerNode.height = cc.view.getVisibleSize().height * 2;
        }

        if (this.nodePool.size() > 0) {
            resultNode = this.nodePool.get();
            resultNode.setParent(this.containerNode);                
        } 
        else {
            if (!this.tooltipPrefab) {
                throw new Error ('TooltipsManager :: showToolTips : Can not load tooltip prefab');
            }

            resultNode = cc.instantiate(this.tooltipPrefab);

            if (!resultNode) {
                cc.error("Can not instantiate tooltip prefab");
                return null;
            }

            resultNode.setParent(this.containerNode);

            let tooltipsHandler = resultNode.getComponent(TooltipsHandler);
            if (tooltipsHandler === null) {
                cc.error("Can not get TooltipsHandler");
                return resultNode;
            }
            tooltipsHandler.onDisableCallback = this._onTooltipsDisable.bind(this);
        }
        return resultNode;
    }

    private _rootNode: cc.Node = null;
    private _prefabs: Map<TipClass, cc.Prefab> = new Map<TipClass, cc.Prefab>();
    private _nodePools: Map<TipClass, cc.NodePool> = new Map<TipClass, cc.NodePool>();
    public readonly TipType = TipMap;
    
    private _getTipNode<T extends TipClass>(type: T): InstanceType<T> {
    
        let resultNode: cc.Node;

        let nodepool = this._nodePools.get(type);
        if(!nodepool) {
            this._nodePools.set(type, new cc.NodePool());
            nodepool = this._nodePools.get(type);
        }

        if (this.nodePool.size() > 0) {
            resultNode = this.nodePool.get();
        } 
        else {
            const prefab = this._prefabs.get(type);
            if (!prefab) {
                throw new Error ('TooltipsManager :: showToolTips : Can not load tooltip prefab');
            }

            resultNode = cc.instantiate(prefab);

            if (!resultNode) {
                cc.error("Can not instantiate tooltip prefab");
                return null;
            }
        }

        return resultNode.getComponent(type);
    }

    /**
     * 
     * @param type TooltipManager.TipType
     * @param options 
     * @returns The instance of the tooltip component
     */
    public showTip<T extends TipClass>(type: T, options?: {
        single: boolean,
        position: cc.Vec2,
    }): InstanceType<T> {
        if (!this._rootNode || !cc.isValid(this._rootNode)) {
            this._rootNode = new cc.Node("TooltipRoot");
            this._rootNode.setParent(cc.director.getScene());
            this._rootNode.addComponent(cc.Widget);
        }
        let comp: InstanceType<T>;
        if(options?.single) {
            comp = this._rootNode.children.find(item => item.getComponent(type)).getComponent(type);
        }
        if(!comp) {
            comp = this._getTipNode(type);
        }
        // comp need emit this event to trigger recoverTip
        comp.node.on('active-in-hierarchy-changed', this._recoverTip.bind(this, type, comp), this)
        comp.node.setParent(this._rootNode);
        if(options?.position) {
            comp.node.setPosition(options.position);
        }
        return comp;
    }

    private _recoverTip<T extends TipClass>(type: T, comp: InstanceType<T>): void {
        if(!comp?.node.active) {
            const pool = this._nodePools.get(type);
            pool.put(comp.node);
        }
    }
}
