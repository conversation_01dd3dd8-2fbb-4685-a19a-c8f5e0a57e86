import cv from "../../components/lobby/cv";

const {ccclass} = cc._decorator;

export enum ShowBBSwitchType {
    None,
    ServerGoldToNumberStr,
    NumberToStr,
    NumToFloatStr,
}

@ccclass
export class ShowBBTagCom extends cc.Component {
    private _numTag : number = 0;

    private _changeLabel: cc.Label = null;
    private _stringSwitchType: ShowBBSwitchType = ShowBBSwitchType.None;
    private _prefix: string = "";

    private _allowConvert: boolean = true;
    
    // called while text update
    private _textChangeListener: (showBBTagCom: ShowBBTagCom) => void;

    public getTag():number{
        return this._numTag;
    }

    public setTag(num: number, switchType: ShowBBSwitchType, prefix: string = ""): ShowBBTagCom {
        this._numTag = num;
        this._stringSwitchType = switchType;
        this._prefix = prefix;
        return this;
    }

    // some times we don't need to convert to bb.
    public setAllowConvert(allowConvert: boolean) {
        this._allowConvert = allowConvert;
    }

    /**
     * set callback for text update.
     * for example:
     * If u want to add a prefix "+" when the chip is positive, u can do it in the callback.
     */
    public setTextChangeListener(listener: typeof this._textChangeListener): void {
        // to optimize: add condition for setting _textChangeListener
        this._textChangeListener = listener;
    }

    onDestroy() {
        cv.MessageCenter.unregister("chip_display_mode_change", this.node);
    }

    public registerCDModeMessage() {
        cv.MessageCenter.register("chip_display_mode_change", this.updateLabel.bind(this), this.node);
    }

    public updateLabel(){
        // It should not be called when the component is ready to be destroy at next frame.
        // because all of attributes like the node have been set null by engine.
        if(!cc.isValid(this, true)) {
            return;
        }
        if(!this._changeLabel) {
            this._changeLabel = this.getComponent(cc.Label);
        }

        if(this._stringSwitchType === ShowBBSwitchType.None || !this._changeLabel){
            return;
        }

        // Does it need to be converted?
        const allowConvert = this._allowConvert;

        switch(this._stringSwitchType){
            case ShowBBSwitchType.ServerGoldToNumberStr:
                this._changeLabel.string = this._getPrefix() + cv.StringTools.serverGoldToShowString(this._numTag, allowConvert);
                break;
            case ShowBBSwitchType.NumberToStr:
                this._changeLabel.string = this._getPrefix() + cv.StringTools.numberToShowString(this._numTag, allowConvert);
                break;
            case ShowBBSwitchType.NumToFloatStr:
                this._changeLabel.string = this._getPrefix() + cv.StringTools.numToFloatString(this._numTag, allowConvert);
                break;
            default:
                console.error("unkonwn showbbswitchtype:" + this._stringSwitchType);
        }

        if (this._textChangeListener) {
            this._textChangeListener(this);
        }
    }

    private _getPrefix(): string {
        if(cv.tools.showChipAsBBorAnte()) {
            return "";
        }
        return this._prefix;
    }

    public getLabel(): cc.Label {
        return this.getComponent(cc.Label);
    }

    public unSetTag() {
        this._changeLabel = null;
        this._prefix = "";
        this._stringSwitchType = ShowBBSwitchType.None;
    }
}