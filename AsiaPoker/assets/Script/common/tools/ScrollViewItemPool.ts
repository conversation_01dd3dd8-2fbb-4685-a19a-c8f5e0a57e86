const { ccclass, property } = cc._decorator;

export interface ScrollViewItemPoolConfig {
    itemCount: number;
    anchorX?: number;
    anchorY?: number;
    initialX?: number;
}

@ccclass
export default class ScrollViewItemPool extends cc.Component {
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    @property(cc.Prefab)
    itemPrefab: cc.Prefab = null;

    private _itemPool: cc.NodePool = new cc.NodePool();
    private _lastItemCount: number = 0;

    protected _scrollViewContentLayout: cc.Layout = null;

    public init(config: ScrollViewItemPoolConfig) {
        const { itemCount, anchorX, anchorY, initialX } = config;
        if (this._lastItemCount === itemCount) return;
        this.clear();
        this._lastItemCount = itemCount;
        for (let i = 0; i < itemCount; i++) {
            const itemNode = this._createItemNodeFromPool(anchorX, anchorY, initialX);
            this.scrollView.content.addChild(itemNode);
        }
        // TODO map according to dynamic item size to prevent that one frame layout adjustment
        if (!this._scrollViewContentLayout)
            this._scrollViewContentLayout = this.scrollView.content.getComponent(cc.Layout);
        if (this._scrollViewContentLayout) {
            const layoutNode = this._scrollViewContentLayout.node;
            layoutNode.active = false;
            this._scrollViewContentLayout.updateLayout();
            layoutNode.active = true;
        }
    }

    public getItems() {
        return this.scrollView.content.children;
    }

    public clear() {
        if (!this.scrollView) {
            return;
        }

        this._lastItemCount = 0;

        const children = this.getItems();
        if (children.length) {
            children.forEach(this._recycleItemNodeToPool.bind(this));
            this.scrollView.content.removeAllChildren(true);
            this.scrollView.content.height = 0;
        }
    }

    private _createItemNodeFromPool(anchorX = 0, anchorY = 1, initialX = 0) {
        const pool = this._itemPool;
        let itemNode = pool.size() > 0 ? pool.get() : cc.instantiate(this.itemPrefab);
        itemNode.setAnchorPoint(anchorX, anchorY);
        itemNode.x = initialX;
        return itemNode;
    }

    private _recycleItemNodeToPool(itemNode: cc.Node) {
        this._itemPool.put(itemNode);
    }
}
