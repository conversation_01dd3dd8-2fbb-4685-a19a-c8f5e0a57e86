import { md5 } from '../../common/tools/mdR5';
import { NativeEvent } from '../../common/tools/NativeEvent';
import { Config } from '../../common/tools/Config';
import {ENV_MODE } from './Config';


const Handled = {};

/**
 * Global JS runtime error handler
 * @param {string} file - The file where the error occurred
 * @param {string} line - The line number where the error occurred
 * @param {string} msg - The error message
 * @param {any} error - The error object
 */
function errorHandler(file, line, msg, error) {
    cc.error('errorHandler: ', file, line, msg, error);

    const key = md5.getInstance().md5(`${file}${line}${msg}`);
    if (Handled[key]) {
        return;
    }
    Handled[key] = 1;
 
    // This part is for sending error message to server
    // const info = {
    //     file,
    //     line,
    //     msg,
    //     error,
    // };
    // const param = {
    //     sendDate: HMFExtension.formatDate(Date.now()),
    //     userId: CurrentUserInfo.user.userId,
    //     msgType: 'jserror',
    //     sendTime: Date.now(),
    //     msgObj: info,
    //     version: HMFAppSetting.appLocalVersion
    // };

    // HMFHTTPClient.sendHttpErrorMsgToServicer(param);

    if (Config.getInstance().GET_DEBUG_MODE() === ENV_MODE.DEV) {
        const errMsg = `${Date().toString()}\n${file}\n${line}\n${msg}\n${error}`;
        if (NativeEvent.getInstance().setClipBoardString) {
            NativeEvent.getInstance().setClipBoardString(errMsg);
        } else {
            cc.error('NativeEvent.getInstance().setClipBoardString is null');
        }
    }
}

(window as any).__errorHandler = errorHandler;
