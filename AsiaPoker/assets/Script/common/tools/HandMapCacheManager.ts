import cv from "../../../Script/components/lobby/cv";

export class HandMapCacheManager  {
    private _mHandMapCache: { [key: string]: any } = null;
    constructor() {
        this._mHandMapCache = null;
        HandMapCacheManager.isLock = false;
    }

    private static handMapCacheKey: string = `PKW_92_GameReviewHandMapCache`;
    private static isLock = false;

    private get _lock(): boolean {
        return HandMapCacheManager.isLock;
    }

    private get _handMapCacheKey(): string {
        return HandMapCacheManager.handMapCacheKey;
    }

    public clearHandMapCache(){
        this._mHandMapCache = null;
        cv.tools.RemoveStringByCCFile(this._handMapCacheKey);
    }

    public loadCache() {
        this._mHandMapCache = {};
        const storedHashMap = cv.tools.GetStringByCCFile(this._handMapCacheKey);
        if (storedHashMap) {
            this._mHandMapCache = JSON.parse(storedHashMap);
        }
    }

    public has (key: string): boolean {
        return this._mHandMapCache.hasOwnProperty(key);
    }
    
    public getHand (key: string): any {
        return this._mHandMapCache[key];
    }

    public addHand (key: string, value: any) {
        this._mHandMapCache[key] = value;
    }

    public saveCache(){
        if (this._lock) return;
        localStorage.setItem(this._handMapCacheKey, JSON.stringify(this._mHandMapCache));
    }

    public static clearAllCache(){
        localStorage.removeItem(HandMapCacheManager.handMapCacheKey);
        this.isLock = true;
    }

    public removeItem(key:string){
       delete this._mHandMapCache[key];
    }
}
