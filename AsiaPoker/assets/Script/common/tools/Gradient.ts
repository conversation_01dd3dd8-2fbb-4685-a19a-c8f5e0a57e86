import { CustomShader } from "./CustomShader";

const { ccclass, property, executeInEditMode } = cc._decorator;

@ccclass
@executeInEditMode
export class Gradient extends CustomShader {
    @property({ serializable: true })
    private _gradientTex: cc.Texture2D = null;
    @property({ type: cc.Texture2D, tooltip: "Gradient texture to be used.\nThis takes priority of colors." })
    public set gradientTex(value: cc.Texture2D) {
        this._gradientTex = value;
        this.updateMaterial();
    }
    public get gradientTex(): cc.Texture2D {
        return this._gradientTex;
    }

    @property({ serializable: true })
    private _startColor: cc.Color = cc.Color.WHITE;
    @property({
        type: cc.Color, tooltip: "Start color of the gradient"
        , visible: function (this: Gradient) { return this.editorColorsAreUsed(); }
    })
    public set startColor(value: cc.Color) {
        this._startColor = value;
        this.updateMaterial();
    }
    public get startColor(): cc.Color {
        return this._startColor;
    }

    @property({ serializable: true })
    private _endColor: cc.Color = cc.Color.BLUE;
    @property({
        type: cc.Color, tooltip: "End color of the gradient"
        , visible: function (this: Gradient) { return this.editorColorsAreUsed(); }
    })
    public set endColor(value: cc.Color) {
        this._endColor = value;
        this.updateMaterial();
    }
    public get endColor(): cc.Color {
        return this._endColor;
    }

    @property({ serializable: true })
    private _startPos: cc.Vec2 = cc.v2(0, 0.5);
    @property({
        type: cc.Vec2, tooltip: "Start position of the gradient, used to calculate gradient direction, left top point is 0 0"
        , visible: function (this: Gradient) { return this.editorColorsAreUsed(); }
    })
    public set startPos(value: cc.Vec2) {
        this._startPos = value;
        this.updateMaterial();
    }
    public get startPos(): cc.Vec2 {
        return this._startPos;
    }

    @property({ serializable: true })
    private _endPos: cc.Vec2 = cc.v2(1, 0.5);
    @property({
        type: cc.Vec2, tooltip: "End position of the gradient, used to calculate gradient direction, right bottom point is 1 1"
        , visible: function (this: Gradient) { return this.editorColorsAreUsed(); }
    })
    public set endPos(value: cc.Vec2) {
        this._endPos = value;
        this.updateMaterial();
    }
    public get endPos(): cc.Vec2 {
        return this._endPos;
    }

    @property({ serializable: true })
    private _useMiddleColor: boolean = false;
    @property({
        type: cc.Boolean, tooltip: "If a 3rd middle color should be used"
        , visible: function (this: Gradient) { return this.editorColorsAreUsed(); }
    })
    public set useMiddleColor(value: boolean) {
        this._useMiddleColor = value;
        this.updateMaterial();
    }
    public get useMiddleColor(): boolean {
        return this._useMiddleColor;
    }

    @property({ serializable: true })
    private _middleColor: cc.Color = cc.Color.YELLOW;
    @property({
        type: cc.Color, tooltip: "Middle color of the gradient"
        , visible: function (this: Gradient) { return this.editorShowMiddleColor(); }
    })
    public set middleColor(value: cc.Color) {
        this._middleColor = value;
        this.updateMaterial();
    }
    public get middleColor(): cc.Color {
        return this._middleColor;
    }

    @property({ serializable: true })
    private _middlePos: number = 0.5;
    @property({
        type: cc.Float, slide: true, min: 0.0, max: 1.0, tooltip: "Position of middle color on gradient direction (between 0 and 1)"
        , visible: function (this: Gradient) { return this.editorShowMiddleColor(); }
    })
    public set middlePos(value: number) {
        this._middlePos = value;
        this.updateMaterial();
    }
    public get middlePos(): number {
        return this._middlePos;
    }

    @property({ tooltip: "If the Node opacity should be affected by the HACK or not" }) affectNodeOpacity: boolean = true;

    protected effectPath: string = "db://assets/Shader/Gradient/Gradient.effect";


    private editorColorsAreUsed(): boolean {
        return this.gradientTex == null;
    }

    private editorShowMiddleColor(): boolean {
        return this.editorColorsAreUsed() && this.useMiddleColor;
    }


    protected onLoad(): void {
        this.calcWorldPosChangeReq = true;
        super.onLoad();
    }


    protected onNodeSizeChanged(): void {
        if (this.haveCorrectEffect) {
            let _contentSize: cc.Size = this.node.getContentSize();
            let _size: number[] = [_contentSize.width, _contentSize.height];
            this.material.setProperty("nodeSize", _size);

            this.onNodePositionChanged();
        }
    }

    protected onNodePositionChanged(): void {
        if (this.haveCorrectEffect) {
            let _contentSize: cc.Size = this.node.getContentSize();
            let _contentPos: cc.Vec2 = this.node.parent.convertToWorldSpaceAR(this.node.getPosition());
            _contentPos.x -= this.node.anchorX * _contentSize.width;
            _contentPos.y -= this.node.anchorY * _contentSize.height;
            let _offset: number[] = [_contentPos.x, _contentPos.y];
            this.material.setProperty("nodeOffset", _offset);
        }
    }

    protected updateMaterial(): void {
        if (this.haveCorrectEffect) {
            if (this.gradientTex) {
                this.material.define("USE_GRADIENT_TEXTURE", true);
                this.material.define("USE_GRADIENT_COLORS", false);
                this.material.setProperty("gradientTex", this.gradientTex);
            }
            else {
                let _startA: number = this.startColor.a;
                let _endA: number = this.endColor.a;
                let _midA: number = this.middleColor.a;

                // HACK: if all gradient colors have same opacity and source blend factor is not src_alpha then set node opacity
                // this issue should be fixed from shader or from texture
                if (this.affectNodeOpacity) {
                    if (this.sprite && this.sprite.srcBlendFactor != cc.macro.BlendFactor.SRC_ALPHA) {
                        if (_startA == _endA && (!this.useMiddleColor || _startA == _midA)) {
                            this.node.opacity = _startA;
                            _startA = _endA = _midA = 255.0;
                        }
                    }
                }
                
                this.material.define("USE_GRADIENT_TEXTURE", false);
                this.material.define("USE_GRADIENT_COLORS", true);
                this.material.setProperty("startColor", [this.startColor.r / 255.0, this.startColor.g / 255.0, this.startColor.b / 255.0, _startA / 255.0]);
                this.material.setProperty("endColor", [this.endColor.r / 255.0, this.endColor.g / 255.0, this.endColor.b / 255.0, _endA / 255.0]);
                this.material.setProperty("startPos", [this.startPos.x, this.startPos.y]);
                this.material.setProperty("endPos", [this.endPos.x, this.endPos.y]);
                this.material.define("USE_EXTRA_COLOR", this.useMiddleColor);
                if (this.useMiddleColor) {
                    this.material.setProperty("middleColor", [this.middleColor.r / 255.0, this.middleColor.g / 255.0, this.middleColor.b / 255.0, _midA / 255.0]);
                    this.material.setProperty("middlePos", this.middlePos);
                }
            }
        }
    }
}
