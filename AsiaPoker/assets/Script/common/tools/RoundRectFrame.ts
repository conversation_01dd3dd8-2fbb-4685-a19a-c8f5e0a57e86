const { property, ccclass, executeInEditMode, disallowMultiple, requireComponent, menu } = cc._decorator;

cc.macro.ENABLE_WEBGL_ANTIALIAS = true;

@ccclass
// @ts-ignore
@executeInEditMode(true)
// @ts-ignore
@disallowMultiple(true)
@requireComponent(cc.Mask)
@menu("Redercomponent/RoundRectMask")
export class RoundRectFrame extends cc.Component {
    @property(cc.Color) public BackgroundColor: cc.Color = cc.color(0, 0, 0, 255);
    @property(cc.Graphics) public BorderGraph: cc.Graphics = null;
    @property() private _borderWidth: number = 10;

    @property({ tooltip: "Outfreame Border Width" })
    public get borderWidth(): number {
        return this._borderWidth;
    }

    public set borderWidth(w: number) {
        this._borderWidth = w;
        this._updateMask(this._radius);
    }

    @property() private _radius: number = 50;
    @property({ tooltip: "Radius Range: 0 - 1, From Rect to Cicle" })
    public get radius(): number {
        return this._radius;
    }

    public set radius(r: number) {
        this._radius = r;
        this._updateMask(r);
    }

    protected mask: cc.Mask = null;

    protected onEnable(): void {
        this.mask = this.getComponent(cc.Mask);
        this._updateMask(this.radius);
    }

    private _updateMask(r: number) {
        let _radius = r >= 0 ? r : 0;
        if (_radius < 1) {
            _radius *= Math.min(this.node.width, this.node.height);
        }
        // @ts-ignore.
        this.mask.radius = _radius;
        // @ts-ignore.
        this.mask.onDraw = this.onDraw.bind(this.mask);
        // @ts-ignore.
        this.mask._updateGraphics = this._updateGraphics.bind(this.mask);
        this.mask.type = cc.Mask.Type.RECT;

        this.drawBorder(this.BorderGraph);
    }

    private _updateGraphics() {
        // @ts-ignore.
        const graphics = this._graphics;
        if (!graphics) {
            return;
        }
        this.onDraw(graphics);
    }

    protected onDraw(graphics: cc.Graphics) {
        graphics.clear(false);
        const node = this.node;
        const width = node.width;
        const height = node.height;
        const x = -width * node.anchorX;
        const y = -height * node.anchorY;
        graphics.roundRect(x, y, width, height, this.radius);
        graphics.fill();
    }

    protected drawBorder(graphics: cc.Graphics) {
        graphics.clear(false);
        const node = this.node;
        const width = node.width;
        const height = node.height;
        const x = -width * node.anchorX;
        const y = -height * node.anchorY;
        graphics.roundRect(x, y, width, height, this.radius);
        graphics.lineWidth = this._borderWidth;
        graphics.strokeColor = this.BackgroundColor;
        graphics.stroke();
    }
}