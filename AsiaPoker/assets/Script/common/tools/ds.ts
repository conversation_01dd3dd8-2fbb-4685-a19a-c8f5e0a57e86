type QueueNode<T> = {
    value: T;
    priority: number;
  };
  
  export class PriorityQueue<T> {
    private heap: QueueNode<T>[] = [];
  
    private parent(index: number): number {
      return Math.floor((index - 1) / 2);
    }
  
    private leftChild(index: number): number {
      return 2 * index + 1;
    }
  
    private rightChild(index: number): number {
      return 2 * index + 2;
    }
  
    private swap(i: number, j: number): void {
      [this.heap[i], this.heap[j]] = [this.heap[j], this.heap[i]];
    }
  
    private heapifyUp(index: number): void {
      let current = index;
      while (
        current > 0 &&
        this.heap[current].priority < this.heap[this.parent(current)].priority
      ) {
        this.swap(current, this.parent(current));
        current = this.parent(current);
      }
    }
  
    private heapifyDown(index: number): void {
      let smallest = index;
      const left = this.leftChild(index);
      const right = this.rightChild(index);
  
      if (
        left < this.heap.length &&
        this.heap[left].priority < this.heap[smallest].priority
      ) {
        smallest = left;
      }
  
      if (
        right < this.heap.length &&
        this.heap[right].priority < this.heap[smallest].priority
      ) {
        smallest = right;
      }
  
      if (smallest !== index) {
        this.swap(index, smallest);
        this.heapifyDown(smallest);
      }
    }
  
    enqueue(value: T, priority: number): void {
      const node: QueueNode<T> = { value, priority };
      this.heap.push(node);
      this.heapifyUp(this.heap.length - 1);
    }
  
    dequeue(): T | undefined {
      if (this.isEmpty()) return undefined;
  
      const root = this.heap[0];
      const last = this.heap.pop();
  
      if (this.heap.length > 0 && last) {
        this.heap[0] = last;
        this.heapifyDown(0);
      }
  
      return root.value;
    }
  
    peek(): T | undefined {
      return this.heap[0]?.value;
    }
  
    isEmpty(): boolean {
      return this.heap.length === 0;
    }
  
    size(): number {
      return this.heap.length;
    }
  }
  