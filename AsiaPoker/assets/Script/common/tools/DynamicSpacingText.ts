const {ccclass, property} = cc._decorator;

@ccclass
export default class DynamicSpacingText extends cc.Component {
    @property(cc.Label)
    targetLabel: cc.Label = null;

    @property(cc.Layout)
    layout: cc.Layout = null;

    @property({ type: cc.Float })
    spacing: number = 0;

    private charNodes: cc.Node[] = [];

    protected onLoad(): void {
        this.layout.spacingX = this.spacing;
    }

    public setText(text: string) {

        this.targetLabel.node.active = false;

        this.charNodes.forEach(node => {
            node.active = false;
        });

        for (let i = this.charNodes.length; i < text.length; i++) {
            const charNode = cc.instantiate(this.targetLabel.node);
            charNode.active = false;
            charNode.parent = this.layout.node;
            this.charNodes.push(charNode);
        }

        for (let i = 0; i < text.length; i++) {
            const charNode = cc.instantiate(this.targetLabel.node);
            charNode.active = true;
            const charLabel = charNode.getComponent(cc.Label);
            charLabel.string = text[i];
            charNode.parent = this.node;
        }
    }

    public setSpacing(spacing: number) {
        this.spacing = spacing;
        this.layout.spacingX = spacing;
    }
}
