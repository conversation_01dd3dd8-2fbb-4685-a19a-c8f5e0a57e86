import cv from "../../components/lobby/cv";
var rs = require('./jsrsasign');

export type LoginKeys = {
    priKey: any;
    pubKey: string;
}

export class TokenCrypto {
    private static _instance: TokenCrypto = null;
    public loginkey: LoginKeys = null;

    public static getInstance(): TokenCrypto {
        if (!this._instance) {
            this._instance = new TokenCrypto();
        }
        return this._instance;
    }

    public getLoginKeys(): LoginKeys {
        if (!this.loginkey) {
            this.loginkey = this._generateRSAKeys();
        }
        return this.loginkey;
    }

    private _generateRSAKeys(): LoginKeys {
        const keypair = rs.KEYUTIL.generateKeypair("RSA", 512);
        let publicKey = rs.KEYUTIL.getPEM(keypair.pubKeyObj);
        publicKey = cv.base64.encode(publicKey);

        return { priKey: keypair.prvKeyObj, pubKey: publicKey };
    }

    //decrypt the encrypted token from server
    public decryptToken(encryptedToken) {
        let encryptedHex = cv.base64.decode(encryptedToken);
        encryptedHex = rs.b64tohex(encryptedToken);
        const decryptedHex = rs.KJUR.crypto.Cipher.decrypt(encryptedHex, this.loginkey.priKey, 'RSA');
        return decryptedHex;
    }

    public createClientOneTimeToken(decryptedToken, reqBody) {
        const timestamp = Math.floor(new Date().getTime() / 1000);
        const md = new rs.KJUR.crypto.MessageDigest({alg: 'md5', prov: 'cryptojs'});
        md.updateString(`${timestamp}${decryptedToken}${reqBody}`);
        const clientHash = md.digest();
    
        return `${timestamp}.${clientHash}`;
    }
}