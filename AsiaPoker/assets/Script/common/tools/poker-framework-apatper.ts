// import G = require('glob');
import * as pf from '../../../poker-framework/scripts/pf';
import cv from '../../components/lobby/cv';
import { CurrencyType } from '../tools/Enum';
import { PushNoticeType } from '../prefab/PushNotice';
import { humanboy_proto } from "../pb/humanboy";
import { cowboy_proto } from "../pb/cowboy";
import { pokermaster_proto } from "../pb/pokermaster";

enum BundleName {
    HumanBoy = 'humanboy',
    Cowboy = 'cowboy',
    PokerMaster = 'poker-master'
}

type DownloadProgressCallback = (
    bundleCount: number,
    totalBundle: number,
    downloadBytes: number,
    totalBytes: number,
    percentage: number
) => void;

export class PokerFrameworkAdapter {
    private _language = '';

    private _bundleRunning = false;

    initView(): void {
        pf.system.view.init();
    }

    createClient(username: string) {
        cc.log('PokerFrameworkAdapter createClient');

        const serverInfo = cv.domainMgr.getServerInfo();

        let context = null;

        if (!pf.app.gameContext) {
            context = new pf.services.MiniGameContext();
            pf.app.gameContext = context;
        } else {
            context = pf.app.getGameContext<pf.services.MiniGameContext>();
        }

        const client = pf.client.PokerClient.create('pkw', '', {
            appVersion: cv.config.GET_CLIENT_VERSION(),
            clientType: cv.config.GET_CLIENT_TYPE(),
            deviceType: cv.httpHandler.getDeviceType(),
            deviceId: cv.native.GetDeviceUUID(),
            baseURL: serverInfo.web_server,
            basePath: 'index.php',
            os: pf.system.os,
            osVersion: pf.system.osVersion,
            deviceInfo: JSON.stringify(pf.system.getDeviceInfo()),
            langauage: cv.config.getCurrentLanguage()
        });

        context.platform = 'pkw';
        context.client = client;
        context.socket = null;

        const serverInfoArray = cv.domainMgr.getAllServerInfo();
        const domains: pf.client.IDomainInfo[] = [];

        serverInfoArray.forEach((info) => {
            const domainInfo: pf.client.IDomainInfo = {
                gateServer: info.gate_server,
                imageServer: info.image_server,
                avatarServer: info.image_server,
                imageUploadServer: info.image_server_2,
                webServer: info.web_server,
                imageServerWpk: info.image_server_wpk,
                imageServerWpto: info.image_server_wpto
            };

            domains.push(domainInfo);
        });

        const userData = cv.dataHandler.getUserData();

        // link client to exist login session
        client.link(
            {
                userId: Number(userData.user_id),
                token: userData.user_token
            },
            {
                user: {
                    userId: Number(userData.user_id),
                    username,
                    nickname: username,
                    ip: userData.user_ip.toString(),
                    avatarURL: userData.headUrl,
                    shopURL: userData.shopUrl,
                    payType: userData.pay_type,
                    areaCode: userData.areaCode.toString(),
                    isTouristPlayer: userData.isTouristUser,
                    imToken: userData.imToken,
                    sex: userData.gender,
                    // from response of socket login
                    firstClubId: 0,
                    firstAlliId: 0,

                    mobile: '',
                    userGold: 0,
                    clubsMax: 0,
                    currentClubs: 0,
                    userMarks: '',
                    cardType: 0,
                    depositGold: 0,
                    gameCoin: 0,
                    userPoints: 0,
                    ratio: 0,
                    totalAmount: 0,
                    usdt: 0,
                    depositUsdt: 0,
                    priorityAreaCode: '',
                    priorityMobile: '',
                    systemTime: 0,
                    calmDownDeadlineTime: 0,
                    sportsTrialCoin: 0,
                    sportsTrialCoinExpiration: 0,
                    sportsBettingBalance: 0
                },
                domains
            }
        );

        // create services
        const authService = new pf.services.AuthService(client);
        pf.serviceManager.register(authService);

        const domainService = new pf.services.DomainService(client);
        domainService.domainIndex = cv.domainMgr.domain_index;
        pf.serviceManager.register(domainService);

        cc.log('PokerFrameworkAdapter client created', context.client);
    }

    linkSocket(): void {
        cc.log('PokerFrameworkAdapter linkSocket');

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();

        if (context.socket === null) {
            this.createSocket();
        } else {
            context.socket.link(cv.netWork.wSocket);
        }

        cc.log('PokerFrameworkAdapter socket linked', context.socket);
    }

    unlinkSocket(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        context?.socket?.unlink();
    }

    linkUserData(data: pf.client.INoticeGetUserData): void {
        const authService = pf.serviceManager.get(pf.services.AuthService);
        authService?.onUserDataNotify(data);
    }

    async joinRoom(gameId: number, roomId: number): Promise<void> {
        cc.log(`PokerFrameworkAdapter joinRoom gameId: ${gameId} roomId: ${roomId}`);

        const bundle = this.getGameBundleName(gameId);
        if (bundle === '') {
            cc.warn(`Not supported game: ${gameId}`);
            return;
        }

        if (!this._bundleRunning) {
            this.enterBundle(bundle, roomId)
                .then(() => {})
                .catch((err: Error | pf.ServerError) => {
                    // HANDLE error
                    cc.log(`PokerFrameworkAdapter enterBundle failed: ${err}`);

                    const serverError = err as pf.ServerError;
                    if (serverError) {
                        this._serverErrorHandler(serverError);
                    }
                    this._bundleRunning = false;
                })
                .finally(() => {
                    cv.SwitchLoadingView.hide();
                });
        } else {
            // entering or already entered
            const context = pf.app.getGameContext<pf.services.MiniGameContext>();
            if (context.bundle === bundle) {
                if (context.service) {
                    // arleady login. login & join room again
                    cc.log('PokerFrameworkAdapter re-login service');
                    await context.service.login();

                    if (context.room) {
                        cc.log('PokerFrameworkAdapter re-join room');
                        // re-join game room
                        await context.room.joinRoom();
                    }
                }
            } else {
                cc.warn(`Join different game: ${gameId}`);
            }
        }
    }

    logout(): void {
        cc.log('PokerFrameworkAdapter logout');

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        if (!context) return;

        if (context.bundle.length > 0) {
            if (context.exitCallback) {
                const entry = pf.bundleManager.getEntry(context.bundle);

                entry.onAfterExit = () => {
                    // make sure to re-create client and socket
                    pf.serviceManager.unregisterAll();
                    context.client = null;
                    context.socket = null;
                    this._bundleRunning = false;
                };

                context.exitCallback();
            }
        } else {
            // make sure to re-create client and socket
            pf.serviceManager.unregisterAll();
            context.client = null;
            context.socket = null;
        }
    }

    removeLuckTurntableRecord(recordId: number) {
        const luckTurntableService = pf.serviceManager.get(pf.services.LuckTurntableService);
        luckTurntableService.removeLuckTurntableRecord(recordId);
    }

    private createSocket() {
        cc.log('PokerFrameworkAdapter createSocket');

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();

        const socket = context.client.createSocket();
        context.socket = socket;

        socket.link(cv.netWork.wSocket);

        const userData = cv.dataHandler.getUserData();
        context.client.getCurrentUser().firstAlliId = userData.firstAlliId;
        context.client.getCurrentUser().firstClubId = userData.firstClubId;

        pf.serviceManager.register(new pf.services.RankService(socket));
        pf.serviceManager.register(new pf.services.WalletService(socket));
        pf.serviceManager.register(new pf.services.PushNotificationService(socket));
        pf.serviceManager.register(new pf.services.ShopService(socket));
        pf.serviceManager.register(new pf.services.LuckTurntableService(socket));
        pf.serviceManager.register(new pf.services.ErrorMessageService());
        pf.serviceManager.register(new pf.services.CalmDownService(socket));
        pf.serviceManager.register(new pf.services.ExchangeCurrencyService(socket));
        pf.serviceManager.register(new pf.services.RebateService(socket));

        pf.serviceManager.get(pf.services.AuthService).registerNotificationHandlers();

        pf.serviceManager.get(pf.services.LuckTurntableService).onLuckTurntableRecordRemoved = (recordId: number) => {
            for (let i = 0; i < cv.dataHandler.getUserData().luckTurntables.length; i++) {
                if (cv.dataHandler.getUserData().luckTurntables[i].record_id === recordId) {
                    cv.dataHandler.getUserData().luckTurntables.splice(i, 1);
                    break;
                }
            }
        };
    }

    private async enterBundle(bundleName: string, roomId: number): Promise<void> {
        this._bundleRunning = true;

        let progress = 0;

        this.displayEnterBundleProgress(progress);

        cc.log(`PokerFrameworkAdapter enterBundle ${bundleName} roomId:${roomId}`);

        let resourceBundleProgress = 0;

        let commonEntry = pf.bundleManager.getEntry('common-resource');

        if (!commonEntry || this._language !== pf.languageManager.currentLanguage) {
            // NOTE:
            // re-entry common-resource when language here to prevent race condition
            if (this._language !== pf.languageManager.currentLanguage) {
                pf.languageManager.currentLanguage = this._language;

                if (commonEntry && commonEntry.state === pf.BundleState.Entered) {
                    await commonEntry.exit();
                }
            }

            resourceBundleProgress = 50;
            await this.loadCommonResourceBundle((finish: number, total: number) => {
                const newProgress = Math.trunc((resourceBundleProgress * finish) / total);
                if (newProgress > progress) {
                    progress = newProgress;
                    this.displayEnterBundleProgress(progress);
                }
            });
        }

        const updateItem = pf.updateManager.getUpdateItem(bundleName);
        cc.log(`PokerFrameworkAdapter load bundle url:${updateItem.getBundleUrl()}`);
        const entry = await pf.updateManager.loadBundle(updateItem);

        entry.onBeforeExit = () => {
            cv.SwitchLoadingView.toggleBlackOverlay(true);
        };

        entry.onAfterExit = () => {
            cv.roomManager.reset();
            // revert back to portrait
            cv.native.setPortrait();
            cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene) => {
                const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                if (!context.isSelfRecharge) {
                    cv.MessageCenter.send('switchSceneToMiniGame');
                }
            });

            cc.log(`PokerFrameworkAdapter enterBundle ${bundleName} exit`);
            this._bundleRunning = false;
        };

        entry.onBeforeLoadScene = () => {
            cv.SwitchLoadingView.toggleBlackOverlay(true);
            if (bundleName === BundleName.HumanBoy) {
                cv.config.setCurrentScene(cv.Enum.SCENE.HUMANBOY_SCENE);
                this._changeOrientation(bundleName);
                this._setPushNotificationType(bundleName);
            } else if (bundleName === BundleName.Cowboy) {
                cv.config.setCurrentScene(cv.Enum.SCENE.COWBOY_SCENE);
                this._changeOrientation(bundleName);
                this._setPushNotificationType(bundleName);
            } else if (bundleName === BundleName.PokerMaster) {
                cv.config.setCurrentScene(cv.Enum.SCENE.POKERMASTER_SCENE);
                this._changeOrientation(bundleName);
                this._setPushNotificationType(bundleName);
            }
        };

        const options: pf.IBundleOptions = {
            roomId,
            onProgress: (finish, total) => {
                const newProgress = Math.trunc(
                    ((100 - resourceBundleProgress) * finish) / total + resourceBundleProgress
                );
                if (newProgress > progress) {
                    progress = newProgress;
                    this.displayEnterBundleProgress(progress);
                }
            }
        };

        await entry.enter(options);
        entry.preload();
        commonEntry = pf.bundleManager.getEntry('common-resource');
        commonEntry.preload();

        // 一些重置操作
        cv.LoadingView.reset();
        cv.TP.reset();
        cv.TT.reset();
        cv.StatusView.show(false);
        cv.pushNotice.reset();
        cv.dialogMager.processClose();
    }

    private displayEnterBundleProgress(progress: number): void {
        let desc = '';

        if (pf.system.isBrowser) {
            /// display the progress of asset download
            desc = cv.config.getStringData('Loading_resource') + ` ${progress}%`;
            cv.SwitchLoadingView.initDes(desc);
        } else {
            /// display enter game
            desc = cv.config.getStringData(
                cv.GameDataManager.tappedCurrency === CurrencyType.USDT
                    ? 'SwitchLoadingView_des_1_usd'
                    : 'SwitchLoadingView_des_1'
            );
        }

        if (progress === 0) {
            cv.SwitchLoadingView.show(desc);
        }
    }

    private async loadCommonResourceBundle(onProgress?: (finish: number, total: number) => void): Promise<void> {
        const bundleName = 'common-resource';
        const updateItem = pf.updateManager.getUpdateItem(bundleName);

        cc.log(`PokerFrameworkAdapter load bundle url:${updateItem.getBundleUrl()}`);
        const entry = await pf.updateManager.loadBundle(updateItem);

        const options: pf.IBundleOptions = {
            onProgress
        };

        return entry.enter(options);
    }

    isBundleNeedDownload(bundle: string): boolean {
        if (bundle !== 'common-resource' && bundle !== BundleName.HumanBoy && bundle !== BundleName.Cowboy && bundle !== BundleName.PokerMaster) {
            return false;
        }

        const updateItem = pf.updateManager.getUpdateItem(bundle);
        if (updateItem) {
            return updateItem.state !== pf.UpdateState.UP_TO_DATE;
        }
        return false;
    }

    isMiniGameNeedDownload(gameId: number): boolean {
        const bundle = this.getGameBundleName(gameId);
        return this.isBundleNeedDownload(bundle);
    }

    async setLanguage(language: string): Promise<void> {
        this._language = language;
    }

    hideWebview(): void {
        pf.app.emit('hideWebview');
    }

    private _changeOrientation(bundleName: BundleName | string) {
        switch (bundleName) {
            case BundleName.HumanBoy:
            case BundleName.Cowboy:
            case BundleName.PokerMaster:
                if (cv.native.isScreenLandscape()) {
                    cv.native.setPortrait();
                } else {
                    cv.native.setLandscape();
                }
                break;
            default:
                break;
        }
    }

    getGameBundleName(gameId: number): string {
        switch (gameId) {
            case pf.client.GameId.HumanBoy:
                return BundleName.HumanBoy;

            case pf.client.GameId.CowBoy:
                return BundleName.Cowboy;

            case pf.client.GameId.PokerMaster:
                return BundleName.PokerMaster;

            default:
                return '';
        }
    }

    // error code handling soon to be migrated into independent class as we need to incorporate the function from existing project (cv toast)
    private _serverErrorHandler(serverError: pf.ServerError) {
        const errorCode = serverError.errorCode;
        // if errorCode is not defined
        if (errorCode === undefined) {
            return;
        }
        // need to consider various error code handling, this part is not supposed to be coupling to our bundle hence we may need to keep a copy of error code in our poker-framework as reference
        // refer RoomManager.ts LOC 454 - 463
        // refer to cv LOC 266 - 274

        if (errorCode === 41001) {
            cv.netWorkManager.OnNeedRelogin(2);
            return;
        }

        // const strKey = cv.StringTools.formatC('Humanboy_ServerErrorCode%d', serverError.errorCode);
        // cv.TT.showMsg(cv.config.getStringData(strKey), cv.Enum.ToastType.ToastTypeError);
        let explanation = '';
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        switch (context.gameId) {
            case pf.client.GameId.HumanBoy:
                explanation = pf.languageManager.getString(pf.StringUtil.formatC('Humanboy_ServerErrorCode%d', errorCode));
                if (!explanation) {
                    if (errorCode === humanboy_proto.ErrorCode.ROOM_SYSTEM_FORCE_CLOSED) {
                        explanation = pf.languageManager.getString('MiniGames_SYSTEM_FORCE_CLOSED');
                    } else {
                        explanation = `ErrorCode:${errorCode}`;
                    }
                }
                break;
            case pf.client.GameId.CowBoy:
                explanation = pf.languageManager.getString(pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', errorCode));
                if (!explanation) {
                    if (errorCode === cowboy_proto.ErrorCode.ROOM_SYSTEM_FORCE_CLOSED) {
                        explanation = pf.languageManager.getString('MiniGames_SYSTEM_FORCE_CLOSED');
                    } else {
                        explanation = `ErrorCode:${errorCode}`;
                    }
                }
                break;
            case pf.client.GameId.PokerMaster:
                explanation = pf.languageManager.getString(pf.StringUtil.formatC('PokerMaster_ServerErrorCode%d', errorCode));
                if (!explanation) {
                    if (errorCode === pokermaster_proto.ErrorCode.ROOM_SYSTEM_FORCE_CLOSED) {
                        explanation = pf.languageManager.getString('MiniGames_SYSTEM_FORCE_CLOSED');
                    } else {
                        explanation = `ErrorCode:${errorCode}`;
                    }
                }
                break;
            default:
                explanation = `ErrorCode:${errorCode}`;
                break;
        }

        cv.TT.showMsg(explanation, cv.Enum.ToastType.ToastTypeError);
    }

    private _setPushNotificationType(bundleName: BundleName | string) {
        switch (bundleName) {
            case BundleName.HumanBoy:
                cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_HUMANBOY);
                break;
            case BundleName.Cowboy:
                cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_COWBOY);
                break;
            case BundleName.PokerMaster:
                cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_POKERMASTER);
                break;
            default:
                break;
        }
    }
}

export const pfAdapter = new PokerFrameworkAdapter();
