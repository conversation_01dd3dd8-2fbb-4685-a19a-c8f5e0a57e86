/**
 * 枚举类
 */
export enum native_cfg {
    PAGE_LOGIN = "login",
    PAGE_LOGIN_FIND_PASS = "login_find_pass",
    PAGE_LOGIN_REGISTER_0 = "login_register_0",
    PAGE_LOGIN_REGISTER_1 = "login_register_1",
    PAGE_LOGIN_REGISTER_2 = "login_register_2",

    EVENT_COUNT_CREATEROOM = "create_room",
    EVENT_COUNT_HANDMAP = "hand_map_count",
    EVENT_COUNT_EMOJI_TYPE = "face_type",
    EVENT_COUNT_MEMOJI_TYPE = "emoji_type",
    EVENT_COUNT_ACTION_DELAY_TIME = "action_delay_time",
    EVENT_COUNT_ROOM_DELAY_TIME = "room_delay_time",
    EVENT_COUNT_INSURANCE_DELAY_TIME = "insurance_delay_time",
    EVENT_COUNT_INSURANCE = "insurance_info",

    ANDROID_PACKAGE_NAME = "org/cocos2dx/javascript/AppActivity",
    IOS_COCOAHELPER = "CocoaHelper",
}

export enum SeatStatus {
    SeatStatus_empty = 0,
    SeatStatus_waiting,
    SeatStatus_leave_a_monment, // For Squid game. it will treated as AWAY status flow
    SeatStatus_inGame_OnAction,
    SeatStatus_inGame_actionType
}

/**
 * 座位手牌类型
 */
export enum SeatHandsCardType {
    /**
     * 无
     */
    SHCT_NONE = 0,

    /**
     * 德州(2张底牌)
     */
    SHCT_TEXAS,

    /**
     * 奥马哈(4张底牌)
     */
    SHCT_PLO,
}

/**
 * moment.tz.zonesForCountry can be use to get time zone name by passing countrycode
 * e.g moment.tz.zonesForCountry('US') will return all available time zone name for US.
 * CN, IN can be pass for China and india
 */
export enum TIME_ZONE {
    LOCAL = 10,
    NJ = "America/New_York",
    INDIA = "Asia/Kolkata",
    MT = "Europe/Malta",
    IM = "Europe/Isle_of_Man",

}

export enum TipsType {
    Tips_call = 0,
    Tips_waitOrLeave,
    Tips_check,
    Tips_bet,
    Tips_mendAnte,// 补盲，补ante 补straddle共用(统一显示补盲)
    Tips_straddle,// straddle
    // Tips_mendStraddle//补straddle
}
export enum BType {
    BType_Call = 0,
    BType_Bet,
    BType_Rasie
}

export enum NameTextType {
    SetNameType_setRole_Name = 0,
    SetNameType_setWinNumber,
}

export enum ActionButtonStatus {
    Control_Bet = 0,
    Control_Raise,
    Control_AllIn,
    Control_add_AllIn,
    Control_Just_Call,
    Control_Default_fallOrCheck,
    Control_Default_Call,
    Control_AllInOrFold,
    Control_Null
}

export enum SeatType {
    SeatType_GameSeat = 0,
    SeatType_ReplaySeat,
    SeatType_FavorReplaySeat
}

export enum SCENE {
    TransitionScene = "TransitionScene",            // 过渡场景
    LOADING_SCENE = "LoadingScene",                 // 加载场景
    LOGIN_SCENE = "LoginScene",                     // 登陆场景
    HALL_SCENE = "HallScene",                       // 大厅场景
    GAME_SCENE = "Game",                            // 游戏场景
    GAME_SCENE_AOF = "GameAof",                     // 游戏场景
    COWBOY_SCENE = "CowboyScene",                   // 德州牛仔
    VIDEOCOWBOY_SCENE = "VideoCowboyScene",         // 视频牛仔
    HUMANBOY_SCENE = "HumanboyScene",               // 百人德州
    POKERMASTER_SCENE = "PokerMasterScene",         // 扑克大师
    JACKFRUIT_SCENE = "JackfruitScene",             // 菠萝蜜
    HOTUPDATE_SCENE = "HotUpdate",                  // 热更新场景
    SPORTS_SCENE = "SportsScene",                   // 体育赛事
    POCKETGAME_SCENE = "PocketGameScene",           // 电子小游戏
    BLACKJACK_SCENE = "BlackJackScene",
    FISHINGKING_SCENE = "FishingKingScene",
    TOPMATCHE_SCENE = "TopMatcheScene",             // 一起看球
    // BLACKJACKPVP_SCENE = "BlackjackPVP",             // 21点
    PKF_LIVE_SCENE = "PKFLiveScene",                // PKF 直播
    CARIBBEAN_POKER_SCENE = "CaribbeanPokerScene",                // Caribbean Stud Poker
    WEALTHTRIO_SCENE = "WealthTrioScene",
    BIT_MASTER_SCENE = "BitMasterScene",
    ISLOT_SCENE = "ISlotScene",                // ISlot slot machine

}

export enum ToastType {
    ToastTypeDefault,
    ToastTypeSuccess,
    ToastTypeError,
    ToastTypeWarning,
    ToastTypeInfo,
    ToastTypeInfoCenter
}

export enum ToastPosition {
    TOP,
    MIDDLE,
    BOTTOM
}

export enum ZORDER_TYPE {
    ZORDER_low = -10,
    ZORDER_0 = 0,
    ZORDER_1 = 1,
    ZORDER_2 = 2,
    ZORDER_3 = 3,
    ZORDER_4 = 4,
    ZORDER_5 = 5,
    ZORDER_6 = 6,
    ZORDER_7 = 7,
    ZORDER_SHADER = 9,
    ZORDER_TOP = 10,
    ZORDER_TT_new = 11,
    ZORDER_TT = 12,
    ZORDER_ACTIVITY = 13,
    ZORDER_LOADING = 14,
    ZORDER_LOG = 15,
}

export enum ServerButtonType {
    ServerButtonType_none = 0,
    ServerButtonType_develop,
    ServerButtonType_chun,
    ServerButtonType_ya,
    ServerButtonType_bin,
    ServerButtonType_wei,
    ServerButtonType_fei,
    ServerButtonType_tao,
    ServerButtonType_jason,
    ServerButtonType_changxing,
    ServerButtonType_max,

    ServerButtonType_ceshifu,
    ServerButtonType_zhenshifu,
    ServerButtonType_special = 200000,
    ServerButtonType_invalid = 200001,
}
/**
 * moveToAction函数动作枚举
 */
export enum action_FuncType {
    to_left = "TO_LEFT",
    to_right = "TO_RIGHT",
    enter = "ENTER",
    out = "OUT",
    dt_FAST = "FAST",
    dt_NORMAL = "NORMAL",
    dt_SLOW = "SLOW",
}

/**
 * 牌值
 */
export enum CardNum {
    CARD_2 = 0,
    CARD_3,
    CARD_4,
    CARD_5,
    CARD_6,
    CARD_7,
    CARD_8,
    CARD_9,
    CARD_10,
    CARD_J,
    CARD_Q,
    CARD_K,
    CARD_A,
    CARD_INVALID,
    CardNum_MAX
}

/**
 * 牌花色
 */
export enum CardSuit {
    CARD_DIAMOND = 0,                   // 方片
    CARD_CLUB,                          // 梅花
    CARD_HEART,                         // 红心
    CARD_SPADE,                         // 黑桃
    CardSuit_MAX
}

/**
 * 创建牌局模式
 */
export enum CreateGameMode {
    CreateGame_Mode_None = 0,           // 无
    CreateGame_Mode_Normal,             // 普通牌局
    CreateGame_Mode_MATCH,              //
    CreateGame_Mode_Short,              // 短牌局
    CreateGame_Mode_Other,              //
}

/**
 * 牌局回顾数据源类型
 */
export enum GameReviewDataType {
    /**
     * 无
     */
    EDST_NONE = 0,

    /**
     * 从战绩列表中获取数据
     */
    EDST_RECORD,

    /**
     * 从游戏房间中获取数据
     */
    EDST_GAMEROOM,

    /**
     * 从个人收藏中获取数据
     */
    EDST_COLLECTION,
}

export enum Event {
    TURN_BACK = 0,
    TURN_FACE = 1,
    DEAL_OVER = 2,
}

export enum CardBack {
    CARD_BACK_0,
    CARD_BACK_1,
    CARD_BACK_2,
    CARD_BACK_3,
    CARD_BACK_4,
    CARD_BACK_MAX
}

export enum CardFace {
    CARD_FACE_0,
    CARD_FACE_1,
    CARD_FACE_2,
    CARD_FACE_3,
    CARD_FACE_4,
    CARD_FACE_5,
    CARD_FACE_6,
    CARD_FACE_MAX
}

export enum TableBack {
    TABLE_BACK_STAR = -1, // 明星桌第一张桌布
    TABLE_BACK_0,
    TABLE_BACK_1,
    TABLE_BACK_2,
    TABLE_BACK_3,
    TABLE_BACK_4,
    TABLE_BACK_5,
    TABLE_BACK_6,
    TABLE_BACK_7,
    TABLE_BACK_8,
    TABLE_BACK_MAX
}

export enum SeatDirection {
    DIRECTION_LEFT_MIDDLEDOWN,       // 左边中和中下
    DIRECTION_LEFT_UP,               // 左边中上
    DIRECTION_RIGHT_MIDDLEDOWN,      // 右边中和中下
    DIRECTION_RIGHT_UP,              // 右边中上
    DIRECTION_TOP_LEFT,              // 顶部左边
    DIRECTION_TOP_RIGHT,             // 顶部右边
    DIRECTION_BOTTOM                 // 最下面
}

export enum ActionType {
    Enum_Action_Null = 0,
    Enum_Action_Check = 1,
    Enum_Action_Fold = 2,
    Enum_Action_Call = 3,
    Enum_Action_Bet = 4,
    Enum_Action_Raise = 5,
    Enum_Action_Allin = 6,
    Enum_Action_CallMuck = 7,
    Enum_Action_AddActionTime = 8,
    Enum_Action_SendCard_Common = 9,
    Enum_Action_Send_HoleCards = 10,
    Enum_Action_Straddle = 11,
    Enum_Action_Post = 12,
}
export enum ChatType {
    Enum_Emoji = 0,
    Enum_Voice = 1
}

/**
 * 格式化时间类型
 */
export enum eTimeType {
    Year_Month_Day = 0,		            // 年月日
    Hour_Min_Sec,                       // 时分秒
    Hour_Minute,			            // 时分
    Month_Day,                          // 月日
    Month_Day_Hour_Min_Sec,             // 月日时分秒
    Year_Month_Day_Hour_Min_Sec,        // 年月日时分秒
    Year_Month_Day_Hour_Min,            // 年月日时分
    Month_Day_Hour_Min,                 // 月日时分
    Day_Month_Hour_Min,                 // 日月时分
    DD_MMMM,            // 01 January
}

export enum BettingRoundType {
    Enum_BettingRound_Preflop = 0,
    Enum_BettingRound_Flop,
    Enum_BettingRound_Turn,
    Enum_BettingRound_River,
}

export enum ITEMType_Alliance {
    GROUP_ITEM = 0,
    POKERINFO_RESULT_ITEM,
}

export enum ResultType_PokerInfo {
    Insurance_type,
    Integral_type,
    Jackpot_type,
    Award_type
}

/**
 * 语言类型
 */
export enum LANGUAGE_TYPE {
    /**
     * 汉语
     */
    zh_CN = "zh_CN",

    /**
     * 英语
     */
    en_US = "en_US",

    /**
     * 越南语
     */
    yn_TH = "yn_TH",

    /**
     * 泰语
     */
    th_PH = "th_PH",

    /**
     * 阿拉伯语
     */
    ar_SA = "ar_SA",

    /**
     * 印地语
     */
    hi_IN = "hi_IN",
}

export enum ReportType {
    REPORT_REGEGIST = 1,
    REPORT_PAYMENT = 2,
    REPORT_JOINROOM = 4
}

export enum SeverType {
    SeverType_None = 0,
    SeverType_World,
    SeverType_Game,
    SeverType_Max,
    ServerType_RoomList = 90,
    SeverType_RANK = 101,
}

export enum GameId {
    GameId_Dummy = 0,		// 初始值
    World = 1,				// 世界服
    Texas = 2,				// 德州
    StarSeat = 3,  			// 德州明星桌
    CowBoy = 10,			// 牛仔
    Allin = 20,				// AOF
    HumanBoy = 30,			// 百人
    ZoomTexas = 40,			// 极速游戏
    ZoomTexasMax = 49,		// 极速游戏
    VideoCowboy = 50,		// 视屏牛仔
    Bet = 60,				// 必下
    PokerMaster = 70,		// 大师
    Jackfruit = 80,			// 菠萝蜜
    Plo = 90,			    // 奥马哈
    Mtt = 900,              // mtt
    Sport = 1000,           // sport
    PG = 1010,              // PG
    PP = 1030,              // PP
    BlackJackDual = 1020,       // 21点
    BlackJack = 1021,       // 21点
    Data = 10101,			// 数据服
    BLSpin = 901, // BL Spin game id
    CaribbeanStud = 6001, // Caribbean Stud Game Id
    CaribbeanTexasHold = 6002, // Caribbean Texas Hold Game Id
    WealthTrio = 6003,
    BitMasterGoldCoin = 6004,
    BitMasterUSD = 6005,
    ISlot = 6100, // ISlot game id
    Squid = 5001,
    USDTtable = 101, // 美金桌
    OBGames = 1100
}


// 客户端类型(详情参见"Config.ts"注释)
export enum ClientType {
    Dummy = 0,              // 无效的值
    Normal = 1,             // c++
    H5 = 3,                 // h5版
    OverSeas = 4,           // H5海外版app
    H5WebPage = 5,          // 私语H5网页版
    OverSeasWebPage = 6,    // h5海外缩减版网页版
    Vietnam = 7,            // h5越南版
    VietnamWebPage = 8,     // h5越南版网页版
    CowboyWeb = 9,          // 牛仔网页版(值应为9，如果要测试暂时写5)
    Thai = 10,              // 泰语版
    ThaiWebPage = 11,       // 泰语网页版
    Arab = 12,              // 阿拉伯版
    India = 13,             // 印地语版
    Mempoker = 14,          // mempoker
    PC = 15,                // PC
}

// ecdh使用密码类型
export enum ECDH_SECRET_TYPE {
    UseX = 0,
    UseY = 1,
    UseXY = 2,
}

export enum ConnectServerFailedReason {
    Null = 0,
    // 未发现服务器
    NotFound = 1,
    // 连接游戏服失败
    DialFailed = 2,
}

export enum GATE_MSGID {
    CONNECT_SERVER_FAILED_NOTIFY = 1003,
    // 服务器关闭通知
    SERVER_CLOSE_NOTIFY = 1006,
}

// 提示框按钮类型枚举
export enum ButtonType {
    ONE_BUTTTON = 0,
    TWO_BUTTON,
    TWO_BUTTON_AOF,
    TWO_BUTTON_BUYAOF,
    ONE_BUTTON_LUCKTURNTABLE,
    TWO_BUTTON_FOLD_LOOK,
    TWO_BUTTON_FOLD,
    TWO_BUTTON_SILIAO_TIPS,
    TWO_BUTTON_BUYIN_TIPS,
    TWO_BUTTON_LIMIT_TIPS,         // 新手限制
    TWO_BUTTON_PAUSE_GAME_TIPS,  // 牌局暂停
    TWO_BUTTON_OPEN_Security_Box,
    TWO_BUTTON_MY_RED_PACKETS,      // 我的红包
    TWO_BUTTON_MTT_FRAME,
    TWO_BUTTON_NETWORK,
    TWO_BUTTON_SWITCH_TABLE,        // 小游戏换桌
    TWO_BUTTON_CUSTOMER_SERVICE,        // 開啟客服
    TWO_BUTTON_FEATURE_HAND,        // Feature Hand Submit
    TWO_BUTTON_ENTER_GAME,
    TWO_BUTTON_FOLD_CHECK,
    TWO_BUTTON_GOTO_BACKPACK, // right (sure) btn shows "Go to Backpack"
    TWO_BUTTON_SQUID_JOIN,
    OK_BUTTON,
}

// 按钮显示样式
export enum ButtonStyle {
    TWO_BUTTON = 0,  // 显示左右两个按钮
    GRAY_BUTTON,  // 只显示一个灰色按钮  如“取消”
    GOLD_BUTTON,   // 只显示金色的按钮  如“确定” 
}

export enum ListRecordType {
    ListRecordTypeType_Get = 0,
    ListRecordTypeType_send,
}

// 红包奖励类型
export enum RedItemType {
    gold = 0,           // 金币
    integral = 1,       // 小游戏金币
    usdt = 2,           // usdt
    goods = 3,           // 实物
    trial_coins = 5,           // Trial coins
    sports_betting = 6           // Sports Betting
}

export enum CurrencyType {
    GOLD = 0,
    USDT = 101
}

// 验证码接收方式
export enum VerityGetType {
    NULL = 0,
    Message_Get = 1,   // 短信验证
    AppGet_Get = 2,  // 私聊APP验证
}

/**
 * 数据采集-功能枚举
 */
export enum Functionality {
    login = "login",                                            // 登录
    registration = "registration",                              // 注册
    payments = "payments",                                      // 支付
    invite = "invite",                                          // 邀请
    casino = "casino",                                          // 赌场
    poker = "poker",                                            // 扑克
}

/**
 * 数据采集-应用场景枚举
 */
export enum CurrentScreen {
    ApplicationStarted = "none",                                // 应用启动
    promotionScreen = "promotionScreen",                        // 弹出广告页
    Login = "mainLoginScreen",                                  // 登录页面
    validation = "validation",                                  // 注册验证页面
    sendCodePopup = "sendCodePopup",                            // 注册页面-确认发送Code弹出框
    account = "account",                                        // 注册账户页面
    profile = "profile",                                        // 注册用户信息页面
    visitorPopup = "visitorPopup",                              // 游客弹出界面
    deposit = "deposit",                                        // 充值
    profileSettings = "profileSettings",                        // 用户设置界面
    inviteFriends = "inviteFriends",                            // 邀请码列表
    referralLink = "referralLink",                              // 邀请码界面
    casinoGameSelection = "casinoGameSelection",                // 小游戏列表界面
    lobby = "lobby",                                            // 大厅
    room = "room",                                              // 房间
    store = "store",
}

/**
 * 数据采集-事件枚举
 */
export enum segmentEvent {
    ApplicationStarted = "ApplicationStarted",                  // 程序启动
    ScreenOpened = "ScreenOpened",                              // 打开页面
    PromotionShown = "PromotionShown",                          // 广告, 活动弹窗显示
    Clicked = "Clicked",                                        // 点击事件
    LogInInitiated = "LogInInitiated",                          // 尝试登录
    UserLoggedIn = "UserLoggedIn",                              // 登录成功
    GetCodeInitiated = "GetCodeInitiated",                      // 统一记录获取Code方式
    InputFieldValueEntered = "InputFieldValueEntered",          // 输入完成事件
    UserRegistrationStarted = "UserRegistrationStarted",        // 注册开始事件
    UserRegistered = "UserRegistered",                          // 注册完成事件
    InviteLinkCopied = "InviteLinkCopied",                      // 用户邀请码复制
    CasinoGameSelected = "CasinoGameSelected",                  // 点击游戏事件
    LobbyGameTypeSelected = "LobbyGameTypeSelected",            // 大厅发现列表游戏选项
    LobbyStakeSelected = "LobbyStakeSelected",                  // 大厅发现列表"微小中大"选项
    LobbyFilterApplied = "LobbyFilterApplied",                  // 大厅发现列表筛选面板
    PokerRoomJoined = "PokerRoomJoined",                        // 进入德州房间
    PokerRoomJoiningDenied = "PokerRoomJoiningDenied",          // 进入德州房间失败
    PokerTableBoughtIn = "PokerTableBoughtIn",                  // 点击带入按钮
    PlayerBuyInFailed = "PlayerBuyInFailed",                    // 带入失败(本地 + 远程)
    PokerRoomLeft = "PokerRoomLeft",                            // 离开德州房间
    PokerTableSatOut = "PokerTableSatOut",                      // 离开德州座位
    PokerTableSittingDenied = "PokerTableSittingDenied",        // 德州坐下失败
    DepositInitiated = "DepositInitiated",
}

// commonLoading 类型
export enum LOADINGTYPE {
    RECONNECT = 0,           // 重新
}

/**
 * 跳转小游戏类型
 */
export enum JUMPGAMETYPE {
    /**
     * 跳转到牛仔，百人，扑克大师
     */
    JUMP_TO_MINI_GAME = 0,

    /**
     * 跳转到体育
     */
    JUMP_TO_SPORT,

    /**
     * 跳转到电子小游戏列表
     */
    JUMP_TO_ELECT_LIST,

    /**
     * 跳转到电子游戏
     */
    JUMP_TO_ELECT_GAME,

    /**
     * 一起看球
     */
    JUMP_TO_WATCH_MACTCHS,

    /**
     * 加勒比
     */
    JUMP_TO_CARIBBEAN_STUD,

    /* 
    一起ISLOT
     */
    JUMP_TO_ISLOT,
    
    JUMP_TO_WEALTH_TRIO,
    JUMP_TO_BIT_MASTER,
    JUMP_TO_VIDEO_COWBOY,
    JUMP_TO_OB_GAME,
    JUMP_TO_BLACK_JACK,
}



// 小游戏冷静弹框类型
export enum popSilenceType {
    calmDownNotice = 0,           // 游戏内冷静开始提示 （横屏）
    countDownHall = 1,       // 大厅倒计时
    countDownGame = 2,        // 游戏内倒计时
}

// Sports Betting Tips Type
export enum sportsBettingTipsType {
    loginTip = 0,
    beforeTip = 1,
    afterTip = 2,
}

export enum GameSizeType {
    None = 0,
    Micro = 1,
    Small = 2,
    Medium = 3,
    High = 4
}

export enum JackpotViewType {
    JACKPOT = 0,
    REWARD = 1,
}

export enum geocomplyAction {
    OnLoginSuccess = "onLoginSuccess",
    OnLoginServer = "OnLoginServer",
    onGetNewGeoToken = "OnGetNewGeoToken",
    onGeoComplyLicenseReponse = "onGeoComplyLicenseReponse",
    onGeoComplyStoreTokenReponse = "onGeoComplyStoreTokenReponse",
    onLogout = "onLogout",
    onGetGeocomplyConfig = "onGetGeocomplyConfig"
}

export enum SportAidEnum {
    Main = 0,
    EuropeCup = 2,
}

export enum SQUID_EVENT {
    NoticeRegisterSquidHuntGame = "NoticeRegisterSquidHuntGame",
    NoticeSpanshotRoomInfo = "on_snapshot_roominfo",
    NoticeGameSettlement = "on_game_settlement_noti",
    NoticeStartSquidHuntGameFailed = "NoticeStartSquidHuntGameFailed",
    RequestJoinSquidHuntGame = "RequestJoinSquidHuntGame",
    ResponseJoinSquidHuntGame = "ResponseJoinSquidHuntGame",
    NoticeJoinSquidHuntGame = "NoticeJoinSquidHuntGame",
    ShowSquidRules = "ShowSquidRules",
    NoticeStartSquidHuntGame = "NoticeStartSquidHuntGame",
    NoticeSquidHuntRefund = "NoticeSquidHuntRefund",
    SquidHuntGameFinalSettlement = "SquidHuntGameFinalSettlement",
    OnForceShowCard = "on_force_showcard",
    OnStartgameNotice = "on_startgame_noti",
    OnStandupSucc = "on_standup_succ",
    OnSitdownSucc = "on_sitdown_succ",
    OnPlayerInfoUpdate = "OnPlayerInfoUpdate",
    NoticeWaitingOtherPlayerRebuyIn="NoticeWaitingOtherPlayerRebuyIn",
    NoticeStartSquidHuntGameGracePeriod="NoticeStartSquidHuntGameGracePeriod",
    OnResetTableDoneForWaitttingStatus="OnResetTableDoneForWaitttingStatus",
    SquidGameLeaveExistingRoomResponse = "SquidGameLeaveExistingRoomResponse",
    SquidGameBlockStatusResponse = "SquidGameBlockStatusResponse",
    ShowSquidStatus = "ShowSquidStatus"
}

export enum ChipDisplayMode {

    // normal default mode
    ShowAsChip = 0,

    // show chip as BB or Ante (short card game)
    ShowAsBBorAnte = 1,
}

export const GameModeType =  {
    Classic: "classic",
    Loose: "loose",
    Squid: "squid",
    Critical: "critical"
}

export enum QuickBetButtonType {
    BUTTON_3 = 3,
    BUTTON_5 = 5,
    BUTTON_7 = 7,
}

export enum LobbySecurityGuardMessage {

    PlayerBanMainRequest = "PlayerBanMainRequest",
    PlayerBanMainResponse = "PlayerBanMainResponse",
    PlayerBanListRequest = "PlayerBanListRequest",
    PlayerBanListResponse = "PlayerBanListResponse",
}

export enum ClickCounterCategory {
    POP_OUT = 1,
    BANNER = 2
}