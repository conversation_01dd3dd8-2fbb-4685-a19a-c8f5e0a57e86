const { ccclass, property } = cc._decorator;

@ccclass
export class CustomShader extends cc.Component {
    @property({ serializable: true })
    _effect: cc.EffectAsset = null;
    @property(cc.EffectAsset)
    public set effect(value: cc.EffectAsset) {
        this._effect = value;
        this.updateEffect();
    }
    public get effect(): cc.EffectAsset {
        return this._effect;
    }

    public get haveCorrectEffect(): boolean {
        return this.material != null && this.material["effectAsset"] != null &&  this.material["effectAsset"].name == this.effect.name;
    }

    protected effectPath: string = "";
    protected renderer: cc.RenderComponent = null;
    protected label: cc.Label = null;
    protected sprite: cc.Sprite = null;
    protected material: cc.MaterialVariant = null;
    protected calcWorldPosChangeReq: boolean = false;
    protected lastWorldPos: cc.Vec2 = cc.v2(0, 0);


    protected onLoad(): void {
        if (CC_EDITOR) {
            this.editor_updateEffectRef();
        }
        else {
            if (this.effect == null)
                this.enabled = false;
        }
    }

    protected onEnable(): void {
        this.init();
        // this is redundant but is a fix for when the component appear for the first time
        // for some reason node opacity is not considered sometimes in onEnable() when the component appear for the first time
        // this is Cocos bug
        // TODO: check with newer cocos version
        setTimeout(this.init.bind(this), 20);   // call init() again after 1 frame

        if (this.calcWorldPosChangeReq)
            this.lastWorldPos = this.getWorldPos();
        this.node.on(cc.Node.EventType.SIZE_CHANGED, this.onNodeSizeChanged, this);
    }

    protected onDisable(): void {
        this.node.off(cc.Node.EventType.SIZE_CHANGED, this.onNodeSizeChanged, this);

        if (!this.enabled) {
            this.reset(true);
        }
    }

    protected onDestroy(): void {
        if (!CC_EDITOR) {
            this.reset(false);
        }
    }

    public onRestore(): void {
        this.editor_updateEffectRef();
        this.init();
    }

    protected editor_updateEffectRef(): void {
        if (!this.effectPath || this.effect) return;
        let self = this;
        Editor.assetdb.queryAssets(this.effectPath, Editor.assettype2name[cc.js.getClassName(cc.EffectAsset)], function (err, results) {
            if (err) {
                cc.warn(err.message || err);
                return;
            }
            results.forEach(function (result) {
                cc.assetManager.loadAny({ uuid: result.uuid}, (err, shader) => {
                    if (err) {
                        cc.warn(err.message || err);
                        return;
                    }
                    self.effect = shader as cc.EffectAsset;
                });
            });
        });
    }

    protected update(dt: number): void {
        if (this.calcWorldPosChangeReq) {
            let currentWorldPos: cc.Vec2 = this.getWorldPos();
            if (Math.abs(this.lastWorldPos.x - currentWorldPos.x) + Math.abs(this.lastWorldPos.y - currentWorldPos.y) >= 1)
                this.onNodePositionChanged();
            this.lastWorldPos = currentWorldPos;
        }
    }


    protected getWorldPos(): cc.Vec2 {
        if (this.node.parent == null)
            return this.node.getPosition();
        return this.node.parent.convertToWorldSpaceAR(this.node.getPosition());
    }

    protected init(): void {
        if (!this.enabled) return;
        if (this.renderer == null) {
            if(this.node == null){
                this.destroy();
                return;
            } 
            this.renderer = this.node.getComponent(cc.RenderComponent);
            this.label = this.node.getComponent(cc.Label);
            this.sprite = this.node.getComponent(cc.Sprite);

            if (this.label) {
                if (this.label.cacheMode == cc.Label.CacheMode.CHAR && this.label.useSystemFont == false) {
                    this.label.cacheMode = cc.Label.CacheMode.BITMAP;
                }
            }
        }

        this.updateEffect();
    }

    /** 
     * change material back to default one (useful in editor) and release the created one from memory (useful in runtime) 
     * @param setDefault should change material back to default one?
     */
    protected reset(setDefault: boolean): void {
        if (this.renderer) {
            if (this.haveCorrectEffect) {
                this.material.decRef();
            }

            if (setDefault) {
                this.material = cc.Material.getBuiltinMaterial(cc.Material.BUILTIN_NAME.SPRITE.toString());
                this.renderer.setMaterial(0, this.material);
            }
        }
    }

    public forceUpdate():void {
        this.updateEffect();
    }

    protected updateEffect(): void {
        if (this.effect) {
            if (this.material == null) {
                if (this.renderer) {
                    this.material = this.renderer.getMaterial(0);
                }
                else {
                    return;
                }
            }

            if (!this.haveCorrectEffect) {
                this.material = cc.Material.create(this.effect);
                this.renderer.setMaterial(0, this.material);
                this.material.addRef();
            }

            this.onNodeSizeChanged();
            this.onNodePositionChanged();
            this.updateMaterial();
        }
        else {
            this.reset(true);
        }
    }

    protected onNodeSizeChanged(): void { }
    protected onNodePositionChanged(): void { }
    protected updateMaterial(): void { }
}
