/**
 * WPK用来调用原生方法的桥接类
 *
 * 使用：
 * HMFJSBridge.invokeNavtive(methodName, paramerters, callback);
 */
window.HMFMsgTypes = {
  refreshHallNotify: "refreshHallNotify",    
}

window.HMFJSBridge = {
  nameOpenWebView: "openWebView",
  nameCloseWebView: "closeWebView",
};

/**
 * 打开原生网页视图
 * @param  {String}   url      打开原生网页视图
 * @param  {Function} callback [description]
 * @param  {Number} navHidden 是否隐藏导航栏   0： 显示， 1 ：隐藏
 * @param  {String} bgColor webview背景颜色；
 */
HMFJSBridge.openWebView = function (url, callback, navHidden = 0, bgColor = "FFFFFF", isForceProtrait = 1, game = "", isDark = 1) {
  if (!cc.sys.isNative ) return;

  //判断是否在外部浏览器中
  let isOutsideBrowser = false;
  if (url.includes("?")) { //有参数
    url = `${url}&isOutsideBrowser=${isOutsideBrowser}`;
  } else {
    url = `${url}?isOutsideBrowser=${isOutsideBrowser}`;
  }

  if (bgColor) bgColor = "#" + bgColor;

  var parameter = {
    url: url,
    bgColor: bgColor,
    navHidden: navHidden,
    //包含开启分享参数
    isShareEnable: url.indexOf("isShareEnable=true") >= 0,
    isForceProtrait: isForceProtrait,
    game: game,
    isDark: isDark,//系统状态栏（黑色为1，还是白色为0）
  };

  var callbackFun = function (data) {
    if (callback) {
      callback(data);
    }
    //刷新大厅本地通知
    var socketEvent = new cc.Event.EventCustom(HMFMsgTypes.refreshHallNotify);
    socketEvent.setUserData({});
    cc.director.dispatchEvent(socketEvent);
    
  }
  HMFJSBridge.invokeNavtive(HMFJSBridge.nameOpenWebView, parameter, callbackFun);
};

HMFJSBridge.closeWebView = function () {
  if (!cc.sys.isNative ) return;

  var parameter = {};

  var callbackFun = function (data) {}
  HMFJSBridge.invokeNavtive(HMFJSBridge.nameCloseWebView, parameter, callbackFun);
};

/**
 * 调用原生方法
 * @param  {String}   method     要调用的原生方法的方法名
 * @param  {Object}   parameters 参数（可选）
 * @param  {Function} callback   回调函数 （可选）
 * @param  {boolean} webMethod   是否包含Web平台 （可选）
 * @return {Object}   返回值是根据调用的方法不同而不同的
 */
HMFJSBridge.invokeNavtive = function (method, parameters, callback, webMethod) {
  var invokeParameters = {
    invokeName: method,
  };

  if (parameters) {
    invokeParameters["parameters"] = parameters;
  }

  if (callback) {
    var callback_id = HMFNativeCallbackIDGenerater();
    invokeParameters["callback_id"] = callback_id;
    HMFNativeCallbacks[callback_id] = callback;
  }

  var invokeParametersString = JSON.stringify(invokeParameters);

  if (cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
    return this.invokeJava(invokeParametersString);
  }
  if (cc.sys.isNative && cc.sys.os === cc.sys.OS_IOS) {
    return this.invokeOC(invokeParametersString);
  }
  return null;
};

/**
 * 供本地原生代码回调的方法
 * @param  {String} callbackId      回调id
 * @param  {String || Object} data  回调的参数 (json字符串 或 对象)
 */
HMFJSBridge.navtiveCallback = function (callbackId, data) {
  var callback = HMFNativeCallbacks[callbackId];
  if (typeof (data) == "string") {
    data = JSON.parse(data);
  }

  if (callback) {
    callback(data);
    delete HMFNativeCallbacks[callbackId];
  }
}

HMFJSBridge.invokeJava = function (parameters) {
  var result = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "jsInvokeMethod", "(Ljava/lang/String;)Ljava/lang/String;", parameters);
  return result;
};

HMFJSBridge.invokeOC = function (parameters) {
  return jsb.reflection.callStaticMethod("HMFJSMethods", "jsInvokeMethod:", parameters);
};

var HMFNativeCallbackIDGenerater = (function () {
  var callbackId = 0;

  return function () {
    callbackId += 1;
    return "hm_native_callback_" + callbackId;
  };
})();

var HMFNativeCallbacks = {};


