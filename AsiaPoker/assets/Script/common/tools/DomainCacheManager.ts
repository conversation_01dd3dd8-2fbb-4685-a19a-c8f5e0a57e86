const CACHE_KEY = 'WHITELIST_DOMAINS_CACHE';

interface CachedItem {
    url: string;
    domain: string;
    report:boolean;
    timestamp: number;
}

export class DomainCacheManager {
    private cache: CachedItem[];

    constructor() {
        this.cache = this.loadCache();
        this.removeExpiredDomains();
    }

    private loadCache(): CachedItem[] {
        const cachedData = localStorage.getItem(CACHE_KEY);
        return cachedData ? JSON.parse(cachedData) : [];
    }

    private saveCache(): void {
        localStorage.setItem(CACHE_KEY, JSON.stringify(this.cache));
    }

    public cacheDomain( domain: string, url: string, report: boolean): void {

        const existingItem =  this.cache.find(item => item.domain === domain);
        if (existingItem) {
            existingItem.report = report;
            existingItem.timestamp = new Date().getTime();
            this.saveCache();
            return;
        }

        const timestamp = new Date().getTime();
        this.cache.push({ domain,url, report, timestamp });
        this.saveCache();
    }

    public getAllUnExpiredDomains(): String[] {
        const currentTime = new Date().getTime();
        const cacheDuration = 24 * 60 * 60 * 1000; 

        return this.cache.filter(item => currentTime - item.timestamp < cacheDuration).map(item => item.domain);
    }


    public removeExpiredDomains(): void {
        const currentTime = new Date().getTime();
        const cacheDuration = 24 * 60 * 60 * 1000; 

        this.cache = this.cache.filter(item => currentTime - item.timestamp < cacheDuration);
        this.saveCache();
    }

    public getDomain(domain: string): CachedItem | null {
        const cachedItem = this.cache.find(item => item.domain === domain);
        return cachedItem ? cachedItem : null;
    }

    public isReported(domain: string): boolean {
        const cachedItem = this.cache.find(item => item.domain === domain);
        return cachedItem ? cachedItem.report : false;
    }

    public setReported(domain: string): void {
        const cachedItem = this.cache.find(item => item.domain === domain);
        if (cachedItem) {
            cachedItem.report = true;
            this.saveCache();
        }
    }

    public getAllUnReportedDomain(): CachedItem[] {
        return this.cache.filter(item => item.report == false ).map(item => item);
    }

    public setReportedAll(): void {
        this.cache.forEach(item => item.report = true);
        this.saveCache();
    
    }
    public domainExist(domain: string): boolean {

        return this.cache.some(item => item.domain === domain);
    }

    public clearCache(): void {
        this.cache = [];
        this.saveCache();
    }
}
