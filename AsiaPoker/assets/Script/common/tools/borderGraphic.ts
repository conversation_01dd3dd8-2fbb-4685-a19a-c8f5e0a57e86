import { Tools } from "../tools/Tools";
import { Gradient } from "./Gradient";

const { ccclass, property, executeInEditMode, playOnFocus, menu, requireComponent } = cc._decorator;

export enum borderDirEnum {
    Center,
    Inside,
    Outside
}

@ccclass
@executeInEditMode
@playOnFocus
@menu("Custom UI/Border")
@requireComponent(cc.Graphics)
export default class borderGraphic extends cc.Component {
    @property
    borderLineWidth: number = 3;

    @property
    _borderRoundRadius: number = 30;

    @property({ type: cc.Float })
    public set borderRoundRadius(value: number) {
        this._borderRoundRadius = value;
        this.reDraw = true;
    }

    public get borderRoundRadius(): number {
        return this._borderRoundRadius;
    }

    @property
    _enabledFillColor: boolean = false;

    @property({ type: cc.Boolean })
    public set enabledFillColor(value: boolean) {
        this._enabledFillColor = value;
        this.reDraw = true;
    }

    public get enabledFillColor(): boolean {
        return this._enabledFillColor;
    }

    @property({
        visible (this: borderGraphic) {
            return this.enabledFillColor;
        }
    })
    fillColor: cc.Color = cc.Color.WHITE;

    @property
    _enabledStrokeColor: boolean = true;

    @property({ type: cc.Boolean })
    public set enabledStrokeColor(value: boolean) {
        this._enabledStrokeColor = value;
        this.reDraw = true;
    }

    public get enabledStrokeColor(): boolean {
        return this._enabledStrokeColor;
    }
    
    @property({ 
        visible (this: borderGraphic) {
            return this.enabledStrokeColor;
        }
    })
    strokesColor: cc.Color = cc.Color.WHITE;

    @property
    _borderDir: borderDirEnum = borderDirEnum.Center;

    @property({ 
        type: cc.Enum(borderDirEnum),
        visible (this: borderGraphic) {
            return this.enabledStrokeColor;
        }
    })
    public set borderDir(value: borderDirEnum) {
        this._borderDir = value;
        this.reDraw = true;
    }

    public get borderDir(): borderDirEnum {
        return this._borderDir;
    }

    @property
    private _enabledGradient: boolean = false;
    @property({ type: cc.Boolean })
    public set enabledGradient(value: boolean) {
        this._enabledGradient = value;
        this.updateGradientComp();
    }
    public get enabledGradient(): boolean {
        return this._enabledGradient;
    }


    protected graphic: cc.Graphics = null;
    protected gradient: Gradient = null;
    protected reDraw: boolean = true;
    protected boderWidth: number = 0;
    protected boderHeight: number = 0;
    private editorFocus: boolean = false;

    private _customOpacity: number = null;
    public set customOpacity(value: number) {
        this._customOpacity = value;
    }

    start() {
        
        this.graphic = this.node.getComponent(cc.Graphics);
        this.updateGradientComp();
        this.calculateLineWidth();
        this.drawBorder();
        this.node.on(cc.Node.EventType.ANCHOR_CHANGED, this.ForceDraw, this);
        
      
    }

    onEnable() {
        cc.view.on('canvas-resize', this.calculateLineWidth, this);
    }

    onDisable() {
        cc.view.off('canvas-resize', this.calculateLineWidth, this);
    }

    protected onFocusInEditor(): void {
        this.editorFocus = true;
    }

    protected onLostFocusInEditor(): void {
        this.editorFocus = false;
    }



    update() {
        if (this.editorFocus) {
            this.calculateLineWidth();
        }
        this.drawBorder();
    }

    setStrokesColor(color:cc.Color) {
        this.strokesColor = color;
    }

    setFillsColor(color:cc.Color) {
        this.fillColor = color;
    }

    private updateGradientComp(): void {
        if (this.enabledGradient) {
            this.gradient = this.node.getComponent(Gradient);
            if (!this.gradient) {
                this.gradient = this.node.addComponent(Gradient);
            }
        }
        else {
            this.gradient = null;
            this.node.removeComponent(Gradient);
        }
    }
  

    protected ForceDraw(): void {
        this.reDraw = true;
        this.drawBorder();
    }

    protected drawBorder(): void {
        if (this.enabledFillColor == false && this.enabledStrokeColor == false || this.graphic == null)
            return;

        if (Tools.compareColors(this.graphic.strokeColor, this.strokesColor) == false
            || Tools.compareColors(this.graphic.fillColor, this.fillColor) == false
            || Tools.compareNumbers(this.boderWidth, this.node.width, 4) != 0
            || Tools.compareNumbers(this.boderHeight, this.node.height, 4) != 0)
            this.reDraw = true;

        if (this.reDraw) {
            this.boderWidth = this.node.width;
            this.boderHeight = this.node.height;
            this.graphic.clear();
            
            if ((this.enabledStrokeColor && this._borderDir == borderDirEnum.Center) || this.enabledFillColor) {
                this.graphic.roundRect(-this.node.anchorX * this.boderWidth, 
                    -this.node.anchorY * this.boderHeight, 
                    this.boderWidth, 
                    this.boderHeight, 
                    this.borderRoundRadius);
                if (this.enabledFillColor) {
                    const fillColor = this.fillColor;
                    this.graphic.fillColor
                        .setR(fillColor.r)
                        .setG(fillColor.g)
                        .setB(fillColor.b)
                        .setA(this._customOpacity != null ? this._customOpacity : fillColor.a);
                    this.graphic.fill();
                }
            }

            if (this.enabledStrokeColor) {
                const halfLineWidth: number = this.graphic.lineWidth / 2;
                if (this._borderDir == borderDirEnum.Outside) {
                    this.graphic.roundRect(-this.node.anchorX * this.boderWidth - halfLineWidth, 
                        -this.node.anchorY * this.boderHeight - halfLineWidth, 
                        this.boderWidth + this.graphic.lineWidth, 
                        this.boderHeight + this.graphic.lineWidth, 
                        this.borderRoundRadius * 1.06);
                }
                else if (this._borderDir == borderDirEnum.Inside) {
                    this.graphic.roundRect(-this.node.anchorX * this.boderWidth + halfLineWidth - 0.1, 
                        -this.node.anchorY * this.boderHeight + halfLineWidth - 0.1, 
                        this.boderWidth - this.graphic.lineWidth + 0.2, 
                        this.boderHeight - this.graphic.lineWidth + 0.2, 
                        this.borderRoundRadius - this.graphic.lineWidth / 1.5);
                }
                const strokesColor = this.strokesColor;
                this.graphic.strokeColor
                    .setR(strokesColor.r)
                    .setG(strokesColor.g)
                    .setB(strokesColor.b)
                    .setA(this._customOpacity != null ? this._customOpacity : strokesColor.a);
                this.graphic.stroke();
            }

            this.reDraw = false;
        }
    }

    private calculateLineWidth(): number {
        if (!this.graphic) return this.borderLineWidth;
        let scale: number = 1;
        const _canvas: cc.Canvas = cc.Canvas.instance;
        const frame_width:number=cc.view.getFrameSize().width;
        const frame_height:number=cc.view.getFrameSize().height;
      
        if (_canvas == null || _canvas.fitHeight == _canvas.fitWidth) {
            scale = Math.min(frame_width / Math.round(cc.view.getDesignResolutionSize().width),
                            frame_height / Math.round(cc.view.getDesignResolutionSize().height));
        }
        else if (_canvas.fitHeight)
            scale = frame_height / Math.round(cc.view.getDesignResolutionSize().height);
        else if (_canvas.fitWidth)
            scale = frame_width / Math.round(cc.view.getDesignResolutionSize().width);
        let lineWidth: number = this.borderLineWidth;
        lineWidth += (1/scale);
        lineWidth = Math.min(lineWidth, this.node.width / 2, this.node.height / 2);
        lineWidth = Number(lineWidth.toFixed(4));
        this.graphic.lineWidth = lineWidth;
        this.reDraw = true;
        return lineWidth;
    }

  

  
    public runEffectOpacity(fromOpacity: number, toOpacity: number, duration: number) {
        const n = { v: fromOpacity };
        cc.tween(n).to(duration, { v: toOpacity }, { progress: (start, end, current, ratio) => {
            this.customOpacity = fromOpacity + (toOpacity - fromOpacity) * ratio;
        }}).start();
    }
}
