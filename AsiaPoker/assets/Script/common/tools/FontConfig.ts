// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import cv from "../../components/lobby/cv";
import { LANGUAGE_TYPE } from "./Enum";

const {ccclass, property} = cc._decorator;
export enum LANGUAGE {
    zh_CN,
    en_US,
    yn_TH,
    th_PH,
  }

@ccclass("TextConfig")
class  TextConfig {
    @property({type: cc.Enum(LANGUAGE) }) language: LANGUAGE = LANGUAGE.en_US;
    @property({type: cc.Float }) fontSize: number = 30;
    @property({type: cc.Float }) lineHeight: number = 30;
    @property(cc.Color) fontColor: cc.Color = cc.Color.WHITE;
}

@ccclass
export default class FontConfig extends cc.Component {

    @property(cc.RichText) label: cc.RichText = null;
    @property (TextConfig) configs: TextConfig[] = [];

    protected onLoad(): void {
        if (this.label == null) {
            this.label = this.getComponent(cc.RichText);
        }
        const config =  this.configs.find((config) => config.language == this.getlanguage());
        if (config) {
            this.applyFont(config);
        }
    }

    applyFont(config: TextConfig): void {
        this.label.fontSize = config.fontSize;
        this.label.lineHeight = config.lineHeight;
        this.label.node.color = config.fontColor;
    }

    private getlanguage(): LANGUAGE {
        switch (cv.config.getCurrentLanguageV2()) {
            case LANGUAGE_TYPE.en_US:
                return LANGUAGE.en_US;
            case LANGUAGE_TYPE.zh_CN:
                return LANGUAGE.zh_CN;
            case LANGUAGE_TYPE.th_PH:
                return LANGUAGE.th_PH;
            case LANGUAGE_TYPE.yn_TH:
                return LANGUAGE.yn_TH;
            default:
                return LANGUAGE.en_US;                    
        }
    }
}
