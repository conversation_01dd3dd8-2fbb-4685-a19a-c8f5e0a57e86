import cv from "../../components/lobby/cv";

export enum PermissionType {
    Camera = "CAMERA",
    Location = "LOCATION"
}

enum AndroidPermission {
    CAMERA = "android.permission.CAMERA",
    LOCATION = "android.permission.ACCESS_COARSE_LOCATION"
}

enum iOSPermission {
    CAMERA = "camera",
    LOCATION = "location"
}

enum NativeEventType {
    OnForcePermResponse = "OnForcePermResponse"     // Permission got response from user
}

interface IPermResponse {
    accepted: boolean;
}

/** Deal with permissions (like Camera for Jumio) */
export class PermissionMgr extends cc.Object {
    private static instance: PermissionMgr;
    public static getInstance(): PermissionMgr {
        if (!PermissionMgr.instance) {
            PermissionMgr.instance = new PermissionMgr();
        }
        return PermissionMgr.instance;
    }

    private forcePermName: PermissionType;
    private forcePermCallback: Function = null;
    private requestingPerm: boolean = false;


    public constructor() {
        super();
        cv.MessageCenter.register(NativeEventType.OnForcePermResponse, this.onPermResponse.bind(this), this);
        cc.game.on(cc.game.EVENT_SHOW, this.onAppEnterForeground, this);
    }

    /** Open app settings page */
    public openAppSettings(permission?: PermissionType, callback?: Function): void {
        if (permission)
            this.forcePermName = permission;
        if (callback)
            this.forcePermCallback = callback;

        if (cc.sys.isNative) {
            if (cc.sys.os === cc.sys.OS_ANDROID) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/PermissionMgr", "openAppSettings", "()V");
            }
            else if (cc.sys.os === cc.sys.OS_IOS) {
                jsb.reflection.callStaticMethod("PermissionMgr", "openAppSettings");
            }
            else {
                cc.warn("[NativeEvent] -> openAppSettings() Not Supported! isNative: " + cc.sys.isNative + " os: " + cc.sys.os);
            }
        }
        else {
            cc.warn("[NativeEvent] -> openAppSettings() Not Supported! isNative: " + cc.sys.isNative + " os: " + cc.sys.os);
        }
    }

    /**
     * Check if a premission was granted or not
     * @param permission permission string
     */
    public checkPermission(permission: string): boolean {
        if (permission) {
            if (cc.sys.isNative) {
                if (cc.sys.os === cc.sys.OS_ANDROID) {
                    return jsb.reflection.callStaticMethod("org/cocos2dx/javascript/PermissionMgr", "checkPermission", "(Ljava/lang/String;)I", permission) >= 0;
                }
                else if (cc.sys.os === cc.sys.OS_IOS) {
                    return jsb.reflection.callStaticMethod("PermissionMgr", "checkPermission:", permission) >= 0;
                }
            }
        }
        return true;
    }

    /**
     * Request permission from the user
     * @param permission permission string
     */
    public requestPermission(permission: string): void {
        if (permission) {
            this.requestingPerm = true;
            if (cc.sys.isNative) {
                if (cc.sys.os === cc.sys.OS_ANDROID) {
                    jsb.reflection.callStaticMethod("org/cocos2dx/javascript/PermissionMgr", "requestPermission", "(Ljava/lang/String;)V", permission);
                }
                else if (cc.sys.os === cc.sys.OS_IOS) {
                    jsb.reflection.callStaticMethod("PermissionMgr", "requestPermission:", permission);
                }
            }
        }
    }


    private onAppEnterForeground() {
        if (this.requestingPerm) {
            this.requestingPerm = false;
            return;
        }

        this.forcePermCallback?.(this.checkPermission(this.getPermissionString(this.forcePermName)));
        this.reset();
    }

    private getPermissionString(permission: PermissionType): string {
        let result: string = "";
        if (cc.sys.isNative) {
            if (cc.sys.os === cc.sys.OS_ANDROID) {
                result = AndroidPermission[permission];
            }
            else if (cc.sys.os === cc.sys.OS_IOS) {
                result = iOSPermission[permission];
            }
        }
        return result;
    }

    private reset(): void {
        this.forcePermCallback = null;
    }


    public forcePermission(permission: PermissionType, callback?: Function): void {
        let permissionString: string = this.getPermissionString(permission);
        if (permissionString == "") {
            callback?.(true, false);
            return;
        }

        if (!this.checkPermission(permissionString)) {
            this.forcePermName = permission;
            this.forcePermCallback = callback;
            this.requestPermission(permissionString);
        }
        else {
            callback?.(true, false);
        }
    }

    protected onPermResponse(response: IPermResponse) {
        if (response.accepted) {
            this.forcePermCallback?.(true);
            this.reset();
        }
        else {
            cv.TT.showMsg("PermissionDenied_" + this.forcePermName, cv.Enum.ToastType.ToastTypeWarning);
        }
    }
}
