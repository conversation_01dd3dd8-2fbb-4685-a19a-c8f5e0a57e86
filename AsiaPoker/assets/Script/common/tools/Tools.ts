import { localStorage } from "../../../poker-framework/scripts/pf";
import ws_protocol = require("./../../../Script/common/pb/ws_protocol");
import world_pb = ws_protocol.pb;
import cv from "../../components/lobby/cv";
import { aesHandler } from "../plugg/aesHandler";
import { HashMap } from "../tools/HashMap";
import { ChipDisplayMode, LANGUAGE_TYPE } from "./Enum";
import { ShowBBTagCom } from "./ShowBBTagCom";
import { Server } from "../net/Server";

const { ccclass, property } = cc._decorator;
@ccclass
export class Tools extends cc.Component {
    private tag: string = "logObject:";
    constructor() {
        super();
    }

    public static instance: Tools;
    public static getInstance(): Tools {
        if (!this.instance) {
            this.instance = new Tools();
        }
        return this.instance;
    }

    private readonly cardFaceKey = "user_cardface_type";
    private readonly cardBackKey = "user_cardback_type";
    private readonly tableBackKey = "user_tablebg_type";

    private readonly tableBackKey_star = "user_tablebg_type_star";  // 明星桌背景设置

    private m_eCardFaceType: number = 0;
    private m_eCardBackType: number = 0;
    private m_eTableBackType: number = 0;  // 普通桌桌布
    private m_eTableBackType_star: number = -1;  // 明星桌桌布
    private readonly cardFaceJackfruitKey = "user_cardface_jackfruit_type";
    private m_eCardFaceJackfruitType: number = 0;

    // 奥马哈牌面设置
    private readonly cardFacePloKey = "user_cardface_plo_type";
    private m_eCardFacePloType: number = 0;

    private _bEnterbackground = false; // 是否进入了后台

    private _chipDisplayMode: ChipDisplayMode = ChipDisplayMode.ShowAsChip;

    private readonly _E1_PREFIX = "e1---";

    private _showBBTipsFlag: boolean = false;

    public init(): void {
        
        // get ChipDisplayMode from local storage and update this._chipDisplayMode
        this._initChipDisplayMode();
        
        // 牌背
        const kCardBackValue = this.GetStringByCCFile(this.cardBackKey);
        this.SetCardBack(cv.Number(kCardBackValue || 0));

        // 牌面
        const kCardFaceValue = this.GetStringByCCFile(this.cardFaceKey);
        this.SetCardFace(cv.Number(kCardFaceValue || 0));

        // 桌布
        const kTableBackValue = this.GetStringByCCFile(this.tableBackKey);
        this.SetTableBack(cv.Number(kTableBackValue || 0));

        // 明星桌桌布
        const kTableBackValue_star = this.GetStringByCCFile(this.tableBackKey_star);
        this.SetTableBack(cv.Number(kTableBackValue_star || -1), true);

        // 菠萝蜜牌面
        const kCardFaceJackfruitValue = this.GetStringByCCFile(this.cardFaceJackfruitKey);
        this.SetCardFaceJackfruit(cv.Number(kCardFaceJackfruitValue || 0));

        // 奥马哈牌面
        const kCardFacePloValue = this.GetStringByCCFile(this.cardFacePloKey);
        this.SetCardFacePlo(cv.Number(kCardFacePloValue || 1));
    }

    /**
     * 这是牌面资源类型(这个方法涉及到原始牌资源路径, 很重要)
     * 结合目前引用该方法的逻辑, 此处 eType 数据来源不太可靠, 需要严格筛选
     * @param eType 
     */
    public SetCardFace(eType: number) {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= 0 && eType < cv.Enum.CardFace.CARD_FACE_MAX) {
            const kValue = cv.StringTools.formatC("%d", eType);
            this.SaveStringByCCFile(this.cardFaceKey, kValue);
            this.m_eCardFaceType = eType;
        }
    }

    public SetCardBack(eType: number) {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= 0 && eType < cv.Enum.CardBack.CARD_BACK_MAX) {
            const kValue = cv.StringTools.formatC("%d", eType);
            this.SaveStringByCCFile(this.cardBackKey, kValue);
            this.m_eCardBackType = eType;
        }
    }


    public SetTableBack(eType: number, starRoom: boolean = false) {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= cv.Enum.TableBack.TABLE_BACK_STAR && eType < cv.Enum.TableBack.TABLE_BACK_MAX) {
            const kValue = cv.StringTools.formatC("%d", eType);
            if (starRoom) {

                this.SaveStringByCCFile(this.tableBackKey_star, kValue);  // 明星桌桌布
                this.m_eTableBackType_star = eType;
            } else {
                this.SaveStringByCCFile(this.tableBackKey, kValue);
                this.m_eTableBackType = eType;
            }
        }
    }

    public SetCardFaceJackfruit(eType: number) {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= 0 && eType < cv.Enum.CardFace.CARD_FACE_MAX) {
            const kValue = cv.StringTools.formatC("%d", eType);
            this.SaveStringByCCFile(this.cardFaceJackfruitKey, kValue);
            this.m_eCardFaceJackfruitType = eType;
        }
    }

    /**
     * 设置奥马哈牌面
     * @param eType 
     */
    public SetCardFacePlo(eType: number): void {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= 0 && eType < cv.Enum.CardFace.CARD_FACE_MAX) {
            const kValue = cv.StringTools.formatC("%d", eType);
            this.SaveStringByCCFile(this.cardFacePloKey, kValue);
            this.m_eCardFacePloType = eType;
        }
    }

    /**
     * Compare 2 colors if they are same or not
     * @param color1 first color
     * @param color2 second color
     * @param alsoAlpha should colors alpha be compared?
     * @returns true if colors match, false otherwise
     */
     public static compareColors(color1: cc.Color, color2: cc.Color, alsoAlpha: boolean = true): boolean {
        if (color1.r !== color2.r)
            return false;
        if (color1.g !== color2.g)
            return false;
        if (color1.b !== color2.b)
            return false;
        if (alsoAlpha && color1.a !== color2.a)
            return false;
        return true;
    }

    public static compareNumbers(nr1: number, nr2: number, decimals: number): number {
        const factor: number = 10 * decimals;
        const _nr1: number = Math.round(nr1 * factor);
        const _nr2: number = Math.round(nr2 * factor);
        if (_nr1 < _nr2)
            return 1;
        if (_nr2 < _nr1)
            return -1;
        return 0;
    }

    /**
     * 获取奥马哈牌面索引
     * @returns 
     */
    public GetCardFacePlo(): number {
        return this.m_eCardFacePloType;
    }

    public GetCardBack() {
        return this.m_eCardBackType;
    }

    public GetCardFace() {
        return this.m_eCardFaceType;
    }

    // bStar 当前是否是明星桌
    public GetTableBack(bStarRoom: boolean = false) {
        if (bStarRoom) {
            return this.m_eTableBackType_star;
        } 
            return this.m_eTableBackType;
        
    }

    public GetCardFaceJackfruit() {
        return this.m_eCardFaceJackfruitType;
    }

    public IsFaction(param: string): number {
        const Dig = '/';
        const idx = param.indexOf(Dig);
        return idx;
    }

    /**
     * 
    自由加注本质是生成一个Step的数组  这样上滑的时候 根据进度取数组里面的值来生成下注筹码
        ChipsStep 为自由加注的数组  
        u32MiniRaise 是最小下注筹码
        u32Stake 是最大下注筹码
        isSmall 判断是否微局

        ChipsStep 的第一个值为 0 第二个值为最小下注 

        定义一个i32Stake  初始值为最小筹码u32MiniRaise
        通过调用 GetRaiseLevel 函数 传递i32Stake 获取一个i32Step值
        定义一个while循环  只要i32Stake  不等于 最大下注 就让 i32Stake累加一个i32Step值
        如果累加后的i32Stake 大于最大下注  就让它等于最大下注终止循环
        然后把得到的 i32Stake 值存入ChipsStep数组
        用累加后的 i32Stake 值 再调用 GetRaiseLevel 函数获取最新的i32Step值

        微局和非微局的区别在于调用 GetRaiseLevel 时  微局直接调用，可以得到小数 

        非微局需要先把 参数转换为客户端显示值 也就是除100  然后再把得到的值 转换成服务器值乘100  
        这样最终存入 ChipsStep 里的数据就是非小数的值

        最终客户端自由加注条 根据上滑的进度  对应 ChipsStep数组的值来显示和下注
        如果上滑到最上面就是allin
     */
    public SplitChipsLevel(u32Stake: number, u32MiniRaise: number, isSmall: number): number[] {
        const ChipsStep: number[] = [];
        ChipsStep.push(0);
        let i32Stake = u32MiniRaise;
        let i32Step = 0;
        if (isSmall) {
            i32Step = this.GetRaiseLevel(i32Stake);
        }
        else {
            i32Step = cv.StringTools.serverGoldByClient(this.GetRaiseLevel(cv.StringTools.clientGoldByServer(i32Stake)));
        }
        ChipsStep.push(u32MiniRaise);

        while (i32Stake !== u32Stake) {
            if (i32Stake + i32Step <= u32Stake) {
                i32Stake += i32Step;
            }
            else {
                i32Stake = u32Stake;
            }
            ChipsStep.push(i32Stake);
            if (isSmall) {
                i32Step = this.GetRaiseLevel(i32Stake);
            }
            else {
                i32Step = cv.StringTools.serverGoldByClient(this.GetRaiseLevel(cv.StringTools.clientGoldByServer(i32Stake)));
            }

        }

        return ChipsStep;
    }

    public GetRaiseLevel(u32Stake: number): number {
        let i32Step: number = 1;
        if (u32Stake < 50) {
            i32Step = 1;
        }
        else if (u32Stake < 100) {
            i32Step = 5;
        }
        else if (u32Stake < 1000) {
            i32Step = 10;
        }
        else if (u32Stake < 10000) {
            i32Step = 100;
        }
        else if (u32Stake < 100000) {
            i32Step = 1000;
        }
        else if (u32Stake < 1000000) {
            i32Step = 2000;
        }
        else if (u32Stake < ********) {
            i32Step = 10000;
        }
        else if (u32Stake < *********) {
            i32Step = 20000;
        }
        else {
            i32Step = 100000;
        }
        return i32Step;
    }

    public Fraction2Decimal(Value: string): number {
        const Dig = "/";
        const idx = Value.indexOf(Dig);

        if (idx !== -1) {
            const Numerator: string = Value.substr(0, Value.indexOf('/'));
            const Denominator: string = Value.substr(Value.lastIndexOf('/') + 1, Value.length);

            const fNumerator = parseFloat(Numerator);
            const fDenominator = parseFloat(Denominator);
            return fNumerator / fDenominator;
        }
        return 0;
    }

    public round_double(number: number) {
        return Math.round(number);
    }

    public RoundingNum(Num: number) {
        if (Num > 100 && Num <= 1000) {
            const f32Value = this.round_double(Num / 10);// 抹掉个位数
            return Math.floor(f32Value * 10);
        }
        if (Num > 1000 && Num <= 10000) {
            const f32Value = this.round_double(Num / 100);// 抹掉十位数 以下依此类推
            return Math.floor(f32Value * 100);
        }
        if (Num > 10000 && Num <= 100000) {
            const f32Value = this.round_double(Num / 1000);
            return Math.floor(f32Value * 1000);
        }
        if (Num > 100000 && Num <= 1000000) {
            const f32Value = this.round_double(Num / 10000);
            return Math.floor(f32Value * 10000);
        }
        if (Num > 1000000 && Num <= ********) {
            const f32Value = this.round_double(Num / 100000);
            return Math.floor(f32Value * 100000);
        }
        if (Num > ******** && Num <= *********) {
            const f32Value = this.round_double(Num / 1000000);
            return Math.floor(f32Value * 1000000);
        }
        if (Num > *********) {
            const f32Value = this.round_double(Num / 1000000);
            return Math.floor(f32Value * 1000000);
        }
            return Num;
        
    }

    SaveStringByCCFile(kkey: string, kValue: string) {
        localStorage.setItem<string>(kkey, kValue);
        // 存keychain
        if (kkey === "user_account") {
            if (cc.sys.os === cc.sys.OS_IOS) {
                cv.native.invokeSyncFunc(cv.nativeCMD.KEY_SAVE_USERNAME_INKEY, { "username": kValue }); // 保存账号到手机
            }
            else if (cc.sys.os === cc.sys.OS_ANDROID) {
                cv.native.writeToFileForAndroid("pkuserinfo", kValue);
            }
        } else if (kkey === "user_password") {
            if (cc.sys.os === cc.sys.OS_IOS) {
                cv.native.invokeSyncFunc(cv.nativeCMD.KEY_SAVE_PASSWORD_INKEY, { "password": kValue }); // 保存账号到手机
            }
            else if (cc.sys.os === cc.sys.OS_ANDROID) {
                cv.native.writeToFileForAndroid("pkpassinfo", kValue);
            }
        }
    }


    static checkifSystemAvatar(avt : number){
        const _avatar = Number(avt);
        return !isNaN(_avatar) && _avatar >= 1 && _avatar <= 200;
    }

    static checkCustomAvatar(avt){
        const _avtar = avt+"";
        return !(_avtar.includes(".js") || _avtar.includes(".ts")); 
    }

    public static checkIfAudio(avd){
        const _audio = avd+"";
        return !(_audio.includes(".js") || _audio.includes(".ts")); 
    }

    public static checkAvatar(avt){
        let _avtar = avt;
        if (_avtar === null || _avtar === "") {
           _avtar = "";
        }else if(this.checkifSystemAvatar(_avtar) || this.checkCustomAvatar(_avtar)){
             _avtar =  avt;
        }else{
            _avtar = "";
        }
        return _avtar;

    }


    GetStringByCCFile(kkey: string): string {
        // AT-7241 - Using locaslStorage module from Poker framework instead of cc.sys.localStorage.
        let kRet = localStorage.getItem(kkey);
        cc.log("[Tools] GetStringByCCFile : KEY: " + kkey + " -- VALUE:" + kRet);
        if (kRet === "" || kRet === null) {
            if (kkey === "user_account") {
                if (cc.sys.os === cc.sys.OS_IOS) {
                    kRet = cv.native.invokeSyncFunc(cv.nativeCMD.KEY_GET_USERNAME_INKEY); // 保存账号到手机
                }
                else if (cc.sys.os === cc.sys.OS_ANDROID) {
                    const result: string = cv.native.readFileForAndroid("pkuserinfo");
                    return result;
                }
            } else if (kkey === "user_password") {
                if (cc.sys.os === cc.sys.OS_IOS) {
                    kRet = cv.native.invokeSyncFunc(cv.nativeCMD.KEY_GET_PASSWORD_INKEY); // 保存账号到手机
                }
                else if (cc.sys.os === cc.sys.OS_ANDROID) {
                    const result: string = cv.native.readFileForAndroid("pkpassinfo");
                    return result;
                }
            }
        }
        return kRet;
    }

    RemoveStringByCCFile(kkey: string): void {
        // cc.sys.localStorage.removeItem(kkey);
        localStorage.removeItem(kkey);
    }

    /**
     * 数组去重(仿c++的std::unique)
     */
    unique(array: any[]): any[] {
        const vRet: any[] = [];

        if (cv.StringTools.getArrayLength(array) > 0) {
            const json: object = {};
            for (let i = 0; i < array.length; ++i) {
                const key = array[i];
                if (!json[key]) {
                    json[key] = true;
                    vRet.push(key);
                }
            }
        }

        return vRet;
    }

    /**
     * 音效声音是否开启(true-开启, false-关闭)
     */
    isSoundEffectOpen(): boolean {
        const value = this.GetStringByCCFile(this.getSoundEffectKey());
        return (value === null || value === "true");
        // return value;
    }

    /**
     * 设置声音
     */
    setSoundEffect(bSlience: boolean): void {
        bSlience = Boolean(bSlience);

        const key: string = this.getSoundEffectKey();
        const value: string = cv.String(bSlience);
        this.SaveStringByCCFile(key, value);
    }

    getSoundEffectKey() {
        // if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
        //     return "client_slience_jackfruit_key";
        // }
        return "client_slience_key";
    }

    setVibrate(isVibrate: boolean) {
        this.SaveStringByCCFile(this.getVibrateKey(), cv.String(isVibrate));
    }

    isVibrate() {
        const isVibrate = this.GetStringByCCFile(this.getVibrateKey());
        return (isVibrate === null || isVibrate === "true");
    }

    getVibrateKey() {
        // if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
        //     return "client_vibrate_jackfruit_key";
        // }
        return "client_vibrate_key";
    }

    setShowBarrage(isShowBarrage: boolean) {
        this.SaveStringByCCFile(this.getShowBarrageKey(), cv.String(isShowBarrage));
    }

    isShowBarrage() {
        const isShowBarrage = this.GetStringByCCFile(this.getShowBarrageKey());
        return isShowBarrage === null || isShowBarrage === "true";
    }

    getShowBarrageKey() {
        // if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
        //     return "client_show_barrage_jackfruit_key";
        // }
        if (cv.GameDataManager.tRoomData.u32GameID === cv.Enum.GameId.StarSeat) {
            return "client_show_barrage_starseat_key";
        }
        return "client_show_barrage_key";
    }

    setShowGiftAnim(isShowGiftAnim: boolean) {
        this.SaveStringByCCFile(this.getShowGiftAnimKey(), cv.String(isShowGiftAnim));
    }

    isShowGiftAnim() {
        const isShowBarrage = this.GetStringByCCFile(this.getShowGiftAnimKey());
        return isShowBarrage === null || isShowBarrage === "true";
    }

    getShowGiftAnimKey() {
        return "client_GiftAnim_key";
    }

    setBetPreflop(value: boolean): void {
        this.SaveStringByCCFile(this.getBetPreflopKey(), cv.String(value));
    }

    isBetPreflop(): boolean {
        const value: any = this.GetStringByCCFile(this.getBetPreflopKey());
        return value === null || value === "true";
    }

    getBetPreflopKey(): string {
        return "client_BetPreflop_key";
    }

    setSportsBettingBackendEnabled(value:boolean): void {
        this.SaveStringByCCFile(this.getSportsBettingBackendEnabledKey(), cv.String(value));
    }

    isSportsBettingBackendEnabled(): boolean {
        const value: any = this.GetStringByCCFile(this.getSportsBettingBackendEnabledKey());
        return value === null || value === "true";
    }

    getSportsBettingBackendEnabledKey(): string {
        return "client_SportsBetting_Backend_Enabled_key";
    }

    setSportsBettingToggle(value: boolean): void {
        this.SaveStringByCCFile(this.getSportsBettingKey(), cv.String(value));
    }

    isSportsBettingToggle(): boolean {
        const value: any = this.GetStringByCCFile(this.getSportsBettingKey());
        return value === null || value === "true";
    }

    getSportsBettingKey(): string {
        return "client_SportsBetting_key";
    }

    setPlayVoice(isPlayVoice: boolean) {
        this.SaveStringByCCFile(this.getPlayVoiceKey(), cv.String(isPlayVoice));
    }

    isPlayVoice() {
        const isPlayVoice = this.GetStringByCCFile(this.getPlayVoiceKey());
        return isPlayVoice === null || isPlayVoice === "true";
    }

    getPlayVoiceKey() {
        // if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
        //     return "client_play_voice_jackfruit_key";
        // }
        return "client_play_voice_key";
    }


    /**
    * Set Auto-Join Squid Game
    */
    getAutoJoinSquidGame() {
        return "auto_join_squid_game";
    }

    isAutoJoinSquidGame() {
        let value:any = this.GetStringByCCFile(this.getAutoJoinSquidGame());
        value = value === "1" || value === 1;
        return !!value;
    }

    setAutoJoinSquidGame(value: boolean) {
        const key = this.getAutoJoinSquidGame();
        this.SaveStringByCCFile(key, (value ? 1 : 0).toString());
        cv.MessageCenter.send("auto_join_squid_game_setting_updated", value);
    }

    /**
     * 设置是否进入后台
     */
    setEnterbackState(bEnter: boolean = false): void {
        this._bEnterbackground = bEnter;
    }

    /**
     * 获取是否进入后台状态
     */
    getEnterbackState(): boolean {
        return this._bEnterbackground;
    }


    /**
     * 是否播放背景音乐(true-播放, false-关闭)
     */
    isPlayMusic() {
        const Key = "client_music_key";
        // if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
        //     Key = "jackfruit_music_key_new"
        // }
        return this.GetStringByCCFile(Key) !== "false";
    }

    /**
    * 设置是否播放背景音乐
    */
    SetPlayMusic(b: boolean): void {
        b = Boolean(b);
        const value: string = cv.String(b);
        const Key = "client_music_key";
        // if (cv.roomManager.getCurrentGameID() === cv.Enum.GameId.Jackfruit) {
        //     Key = "jackfruit_music_key_new"
        // }
        this.SaveStringByCCFile(Key, value);
    }

    GetBase64String(kName: string): string {
        let kPath: string;
        kPath += kName;

        if (cc.sys.isNative) {
            const data = jsb.fileUtils.getDataFromFile(kName);
            const _baseData: string = aesHandler.bytesToBase64(data);
            return _baseData;
        }

        return "";
    }

    getGreenColor() {
        let greenColor: cc.Color = cc.color(0, 255, 0);

        // 游戏场景 与外面绿色使用不一致
        if (cv.config.getCurrentScene() !== cv.Enum.SCENE.GAME_SCENE &&
            cv.config.getCurrentScene() !== cv.Enum.SCENE.GAME_SCENE_AOF) {
            greenColor = cc.color(67, 198, 116);  // #43C674
        }

        return greenColor;
    }

    getRedColor() {
        let redColor: cc.Color = cc.color(255, 0, 0);

        // 游戏场景 与外面红色使用不一致
        if (cv.config.getCurrentScene() !== cv.Enum.SCENE.GAME_SCENE &&
            cv.config.getCurrentScene() !== cv.Enum.SCENE.GAME_SCENE_AOF) {
            redColor = cc.color(228, 69, 69);  // #e44545
        }

        return redColor;
    }

    /**
     * 通过指定语言判断赢面是否该显示红色(默认为当前设置的语言)
     * @param laguage 
     */
    isRedWinColorByLanguage(laguage?: LANGUAGE_TYPE): boolean {
        if (!laguage || typeof laguage === "undefined") laguage = cv.config.getCurrentLanguage();
        return laguage === cv.Enum.LANGUAGE_TYPE.zh_CN;
    }

    /**
     * 通过语言获取赢的颜色
     */
    getWinColor(): cc.Color {
        const redColor: cc.Color = this.getRedColor();
        const greenColor: cc.Color = this.getGreenColor();
        return this.isRedWinColorByLanguage() ? redColor : greenColor;
    }

    /**
     * 通过语言获取输的颜色
     */
    getLoseColor(): cc.Color {
        const greenColor: cc.Color = this.getGreenColor();
        const redColor: cc.Color = this.getRedColor();
        return this.isRedWinColorByLanguage() ? greenColor : redColor;
    }

    getLanguageStr(lang: string): string {
        let indx = 0;
        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            indx = 0;
        }
        else if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.yn_TH) {
            indx = 2;
        }
        else {
            indx = 1;
        }
        const strArr = lang.split("(*)");
        if (strArr.length < indx + 1) {
            indx = 0;
        }
        return strArr[indx];
    }

    /**
     * 遍历目标节点   采用广度遍历
     */
    public ThroughNode(obj: cc.Node) {
        const objchilds = obj.children;
        let childs: cc.Node[] = [];
        childs = childs.concat(objchilds);
        let count = 0;
        while (childs.length > count) {
            const child = childs[count];
            count++;
            if (child.childrenCount > 0) {
                const childarr = child.children;
                for (let index = 0; index < childarr.length; index++) {
                    childs.push(childarr[index]);
                }
            }
            const sp = child.getComponent(cc.Sprite);
            console.log(cv.StringTools.formatC("length:%d count:%d  obj.childrenCount:%d", childs.length, count, obj.childrenCount));
            if (sp && !cc.isValid(sp.spriteFrame, true)) {
                console.log(cv.StringTools.formatC("sp:%s", sp.name));
                sp.spriteFrame = null;
            }

        }
    }

    private _gameMap: HashMap<string, string> = null;
    // 获取game中文名
    public displayChineseName(str: string, playerCount:number = 0): string {

        if(!this._gameMap) {
            this._gameMap = new HashMap();
            this._gameMap.add("HL", "德州扑克");
            this._gameMap.add("LSHL", "松浪德州扑克");
            this._gameMap.add("HLZ", "极速扑克");
            this._gameMap.add("HLB", "暴击德州扑克");
            this._gameMap.add("HS", "短牌德州");
            this._gameMap.add("HSZ", "极速短牌");
            this._gameMap.add("HSB", "暴击短牌");
            this._gameMap.add("AL", "AoF德州扑克");
            this._gameMap.add("AS", "AoF短牌");
            this._gameMap.add("AN", "必下");
            this._gameMap.add("SQ", "德州扑克");
            this._gameMap.add("JF", "菠萝蜜");
            this._gameMap.add("PLO", "底池限注奥马哈");
            this._gameMap.add("-Seat Deep Stack SD", "人深筹短牌德州");
        }

        const strArray = str.match(/\d+|\D+/g);
        if(!strArray) {
            return str;
        }

        if(playerCount) {
            let middleName = "-Seat Deep Stack SD";
            if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
                middleName = this._gameMap.get(middleName);
            }
            return playerCount + middleName + "-" + strArray[1];
        }

        // 模式匹配
        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            if(this._gameMap.get(strArray[0])) {
                return this._gameMap.get(strArray[0]) + "-" + strArray[1];
            }
        } 

        return strArray[0] + strArray[1];
    }

    // 分别截取房间名称 和 房间号
    // 返回 房间名称 0位置  和 房间号 1位置
    public getSplitRoominfo(str: string): string[] {

        const pattern = /[0-9]/;
        const array = str.match(pattern);
        const strArray = str.split(array[0]);
        const name_array: string[] = [];  // 0 位置是房间名称，1位置是房间号
        // 模式匹配
        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            if (array !== null) {

                const gameMap: HashMap<string, string> = new HashMap();
                gameMap.add("HL", "德州扑克");
                gameMap.add("LSHL", "松浪德州扑克");
                gameMap.add("HLZ", "极速扑克");
                gameMap.add("HLB", "暴击德州扑克");
                gameMap.add("HS", "短牌德州");
                gameMap.add("HSZ", "极速短牌");
                gameMap.add("HSB", "暴击短牌");
                gameMap.add("AL", "AoF德州扑克");
                gameMap.add("AS", "AoF短牌");
                gameMap.add("AN", "必下");
                gameMap.add("JF", "菠萝蜜");
                gameMap.add("PLO", "底池限注奥马哈");
                gameMap.add("SQ", "德州扑克");
                if (gameMap.has(strArray[0])) {
                    name_array[0] = gameMap.get(strArray[0]);
                    name_array[1] = str.replace(strArray[0], "");
                }else{
                    name_array[0] = strArray[0];
                    name_array[1] = str.replace(strArray[0], "");
                }
            }
        } else if (array !== null) {
            name_array[0] = strArray[0];
            name_array[1] = str.replace(strArray[0], "");
        }
        return name_array;
    }


    /**
     * 打印一个对象所有的值
     * @param obj 
     */
    public logObject(obj: Object, tag: string = "打印：") {
        this.logMsg(tag, `对象信息\n${JSON.stringify(obj, null, 2)}`);
    }


    /**
     * 将一个对象的所有属性拷贝给另外一个对象
     */
    public copyObjectProperties(obj: any, soureObj: Object) {
        if (!soureObj) {
            console.log("copyObjectProperties 不能传入空对象");
            return;
        }

        if (!obj) {
            obj = {};
        }
        for (const i in soureObj) {
            obj[i] = soureObj[i];
        }
        cv.tools.logObject(obj);
    }

    /**
     * 输出信息
     * @param tag 
     * @param msg 
     */
    public logMsg(tag: string, msg: string) {
        console.log(`${tag} -> ${msg}`);
    }

    // 将秒转化为小时分钟
    secondFormat(result) {
        const h = Math.floor(result / 3600 % 24);
        const m = Math.floor(result / 60 % 60);
        if (h < 1) {
            if (m < 10) {
                return result = "00:" + "0" + m;
            }
            return result = "00:" + m;
        } 
            if (h < 10) {
                return result = "0" + h + ":" + m;
            }
            
                return result = h + ":" + m;
            
        
    }

    formatTime(second): string {
        const now = new Date(second);
        // Y = now.getFullYear(),
        // M = now.getMonth()+1,
        // D= now.getDate(),
        const h = now.getHours();
        const m = now.getMinutes();
        const s = now.getSeconds();
        // M = M<10?'0'+M:M;
        // D = D<10?'0'+D:D;
        const hour = (h < 10 ? '0' + h.toString() : h)
        const min = m < 10 ? '0' + m : m;
        const sec = s < 10 ? '0' + s : s;
        return hour + ':' + min + ':' + sec;

        // return [parseInt((second / 60 / 60).toString()), second / 60 % 60, second % 60].join(":")
        //     .replace(/\b(\d)\b/g, "0$1");
    }

    public GetFriendLevelName(intimacy: number): string {
        let level = -1;
        if (intimacy < 100 && intimacy >= 0) {
            level = 0;
        }
        else if (intimacy <= 299 && intimacy >= 100) {
            level = 1;
        }
        else if (intimacy <= 999 && intimacy >= 300) {
            level = 2;
        }
        else if (intimacy <= 2999 && intimacy >= 1000) {
            level = 3;
        }
        else if (intimacy <= 6999 && intimacy >= 3000) {
            level = 4;
        }
        else if (intimacy <= 12999 && intimacy >= 7000) {
            level = 5;
        }
        else if (intimacy <= 19999 && intimacy >= 13000) {
            level = 6;
        }
        else if (intimacy <= 49999 && intimacy >= 20000) {
            level = 7;
        }
        else if (intimacy >= 50000) {
            level = 8;
        }

        return level > -1 ? cv.config.getStringData("Star_friend_" + level) : "";
    }

    getRandomIntInclusive(min: number, max: number) {
        const minNum = Math.ceil(min);
        const maxNUm = Math.floor(max);
        return Math.floor(Math.random() * (maxNUm - minNum + 1)) + minNum; // The maximum is inclusive and the minimum is inclusive
    }

    public getRandomArrayElements(arr, count) {
        const shuffled = arr.slice(0); let i = arr.length; const min = i - count; let temp; let index;
        while (i-- > min) {
            index = Math.floor((i + 1) * Math.random());
            temp = shuffled[index];
            shuffled[index] = shuffled[i];
            shuffled[i] = temp;
        }
        return shuffled.slice(min);
    }

    getActualSize(node: cc.Node) {
        const newNode = new cc.Node();
        const namelabel = newNode.addComponent(cc.Label);
        namelabel.fontSize = node.getComponent(cc.Label).fontSize;
        const actualSize = cv.resMgr.getLabelStringSize(namelabel, node.getComponent(cc.Label).string);
        newNode.destroy();
        if (actualSize.width <= node.width) {
            return actualSize;
        }
        
            return node.getContentSize();
        
    }

    // changecard
    public dealRaiseData(raiseArray: string[]) {
        for (let i = 0; i < raiseArray.length; i++) {
            raiseArray[i] = this.dealRaiseDataStr(raiseArray[i]);
        }
    }

    public dealRaiseDataStr(raiseArray: string): string {
        return this.dealRaiseDataNumber(cv.Number(raiseArray));
    }


    /**
     * 快捷下注通过百分比取分数
     * @param raiseArray 
     */
    public dealRaiseDataNumber(raiseArray: number): string {
        let retStr = "";

        // 百分比
        switch (raiseArray) {
            // 1/4
            case 25: retStr = cv.config.getStringData("UITableSetBetBtnValue0"); break;
            // 1/3
            case 34: retStr = cv.config.getStringData("UITableSetBetBtnValue1"); break;
            // 1/2
            case 50: retStr = cv.config.getStringData("UITableSetBetBtnValue2"); break;
            // 2/3
            case 67: retStr = cv.config.getStringData("UITableSetBetBtnValue3"); break;
            // 3/4
            case 75: retStr = cv.config.getStringData("UITableSetBetBtnValue4"); break;
            // 1
            case 100: retStr = cv.config.getStringData("UITableSetBetBtnValue5"); break;
            // 1.2
            case 120: retStr = cv.config.getStringData("UITableSetBetBtnValue6"); break;
            // 1.5
            case 150: retStr = cv.config.getStringData("UITableSetBetBtnValue7"); break;
            // 2
            case 200: retStr = cv.config.getStringData("UITableSetBetBtnValue8"); break;
            // 其他
            default: retStr = (raiseArray / 100).toFixed(2); break;
        }

        return retStr;
    }

    // 将时间（秒数）转换为xx时xx分xx秒形式  支持多语言(如果 时 分为0则省略)
    public getStringByTime(time: number): string {
        // let h = Math.floor(time/3600%24);
        // let m = Math.floor((time - h * 3600) /60%60);
        // let s = time - h * 3600 - m * 60;
        const h = Math.floor(time / 3600);
        const m = Math.floor(time % 3600 / 60);
        const s = Math.floor(time % 60);
        if (h === 0) {
            if (m === 0) {
                return s + cv.config.getStringData("seconds");
            } 
                return m + cv.config.getStringData("minute") + s + cv.config.getStringData("seconds");
            
        } 
            return h + cv.config.getStringData("hour") + m + cv.config.getStringData("minute") + s + cv.config.getStringData("seconds");
        
    }

    // 将时间（秒数）转换为天数 不足一天则转换为xx时xx分xx秒形式  支持多语言(如果 时 分为0则省略)
    public getStringByDay(time: number) {
        const d = Math.floor(time / 3600 / 24);
        if (d > 0) {
            return d + cv.config.getStringData("day");
        } 
            return this.getStringByTime(time);
        
    }

    showError(arg, showTips: boolean = true) {
        let errorMsg = null;
        let errorType = null;
        const check = function (name) {
            return name !== undefined;
        }

        if (check(arg.KVCode)) {
            if (arg.KVCode.length !== 6) {
                errorMsg = "ErrorToast39";
                errorType = "ToastTypeError";
            }
        }

        if (check(arg.phoneNum)) {
            if (arg.phoneNum.length <= 0) {
                errorMsg = "ErrorToast38";
                errorType = "ToastTypeError";
            } else if (check(arg.AreaCode)) {
                if (arg.AreaCode === "86" && arg.phoneNum.length !== 11) {
                    errorMsg = "ErrorToast28";
                    errorType = "ToastTypeError";
                }
            }
        }

        if (check(arg.kAccount0)) {
            const leng = cv.StringTools.getStrLen(arg.kAccount0);
            if (arg.kAccount0.length <= 0) {
                errorMsg = "ErrorCode8";
                errorType = "ToastTypeError";
            } else if (leng < 8 || leng > 32) {
                errorMsg = "ErrorToast41";
                errorType = "ToastTypeError";
            }
        }

        if (check(arg.kAccount0) && check(arg.kAccount1)) {
            if (arg.kAccount0 !== arg.kAccount1) {
                errorMsg = "ErrorToast40";
                errorType = "ToastTypeError";
            }
        }

        if (check(arg.password0)) {
            const leng = cv.StringTools.getStrLen(arg.password0);
            if (arg.password0.length <= 0) {
                errorMsg = "ErrorCode9";
                errorType = "ToastTypeError";
            } else if (leng < 6 || leng > 14) {
                errorMsg = "ErrorCode7";
                errorType = "ToastTypeError";
            } else if (arg.password0.indexOf(" ") !== -1) {
                errorMsg = "recetPassWord_recetPassWord_panel_des_text";
                errorType = "ToastTypeError";
            }
        }

        if (check(arg.password0) && check(arg.password1)) {
            if (arg.password0 !== arg.password1) {
                errorMsg = "ErrorToast17";
                errorType = "ToastTypeError";
            }
        }

        if (check(arg.nickname)) {
            const leng = cv.StringTools.getStrLen(arg.nickname);
            if (arg.nickname.length <= 0) {
                errorMsg = "ErrorToast3";
                errorType = "ToastTypeError";
            } else if (!cv.StringTools.isClubNameFormat(arg.nickname)) {
                errorMsg = "tips_no_special_words";
                errorType = "ToastTypeWarning";
            } else if (cv.StringTools.isSensitiveWords(arg.nickname)) {
                errorMsg = "tips_no_sensitive_words";
                errorType = "ToastTypeWarning";
            } else if (leng < 4 || leng > 12) {
                errorMsg = "EditBoxNickName1";
                errorType = "ToastTypeWarning";
            }
        }


        if (errorMsg !== null) {
            if (showTips) {
                cv.TT.showMsg(cv.config.getStringData(errorMsg), errorType);
            }
            return true;
        }
        return false;
    }

    // 获取默认图片路径
    getBackgroundBannerImgPath(): string {
        let backGroundImgpath = "client_type/";
        if (cv.config.isOverSeas()) {
            backGroundImgpath += "pkc/"
        }
        else if (cv.config.isThai()) {
            backGroundImgpath += "pkt/"
        } else {
            backGroundImgpath += "pkw/"
        }

        let bannerName = "banner_0";
        if (cv.native.isWideScreen()) {  // 如果是宽屏ipad，使用默认ipad的banner
            bannerName = "banner_1";
        }

        return backGroundImgpath + bannerName;
    }

    /**
     * 判断当前帧指引擎对象实例是否有效
     * @param node 
     * @returns true:表示有效
     */
    isValidNode(cc_obj: any): boolean {
        return cc_obj && cc.isValid(cc_obj, true);
    }

    /**
     * 安全销毁指定节点
     * @param node 
     */
    destroyNode(node: cc.Node) {
        if (this.isValidNode(node)) node.destroy();
    }

    /*
        true: str is JSON string
    */
    public isJSONString(str) {
        if (typeof str === 'string') {
            try {
                if (typeof JSON.parse(str) === 'object') {
                    return true;
                }
            } catch (error) {
                // cc.vv.ConsoleLog.log("isJSONString", error);
            }
        }
      
        return false;
    }

    convertTimeToUTC8(now: Date): Date {
        const utcTime = now.getTime() + now.getTimezoneOffset() * 60 * 1000;  // return UTC 0 time
        const utcPlus8Time = new Date(utcTime + (8 * 60 * 60 * 1000));  // UTC +8 time
        return utcPlus8Time;
    }

    doFadeIn(node: cc.Node, time: number, callback?: Function) {
        node.opacity = 1;
        node.active = true;
        node.runAction(cc.sequence(cc.fadeIn(time), cc.callFunc(() => {
            if (callback) {
                callback();
            }
        })));
    }

    getMTTPopupMsgContextJSON(msg: world_pb.PopupMessageNotice) {
        switch(cv.config.getCurrentLanguage()) {
            case LANGUAGE_TYPE.zh_CN: {
                return msg.json_cn;
            }
            case LANGUAGE_TYPE.en_US: {
                return msg.json_en;
            }
            case LANGUAGE_TYPE.ar_SA: {
                return msg.json_ar;
            }
            case LANGUAGE_TYPE.hi_IN: {
                return msg.json_hi;
            }
            case LANGUAGE_TYPE.th_PH: {
                return msg.json_th;
            }
            case LANGUAGE_TYPE.yn_TH: {
                return msg.json_vn;
            }
            default: {
                return msg.json_en;
            }
        }
    }

    getSetFromLocalStorage(key: string): Set<any> {
        const storedSet = this.GetStringByCCFile(key);
        return storedSet ? new Set(JSON.parse(storedSet)) : new Set();
    }
    
    private _initChipDisplayMode() {
        const cdMode = parseInt(cv.tools.GetStringByCCFile('92_config_chipDisplayMode'));
        if(cdMode && ChipDisplayMode[cdMode]) {
            this._chipDisplayMode = cdMode;
        } else {
            this._chipDisplayMode = ChipDisplayMode.ShowAsChip;
        }
    }

    // Chip display mode getter
    public getChipDisplayMode(): ChipDisplayMode {
        if(!ChipDisplayMode[this._chipDisplayMode]) {
            console.error("undefined chip display mode! return default mode (chip)");
            return ChipDisplayMode.ShowAsChip;
        }
        
        return this._chipDisplayMode;
    }
    
    // Chip display mode setter
    public setChipDisplayMode(mode: ChipDisplayMode) {
        if(!ChipDisplayMode[mode]) {
            console.error("unsupported chip display mode!");
            return;
        }
        if(this._chipDisplayMode === mode){
            console.warn("the same mode and return");
            return;
        }
        
        this._chipDisplayMode = mode;

        // write the mode into local storage
        cv.tools.SaveStringByCCFile('92_config_chipDisplayMode', mode.toString());

        if(mode === ChipDisplayMode.ShowAsBBorAnte){
            this.setShowBBTipsFlag(true);
        }
    }

    // Chip display check BB/Ante mode helper
    public showChipAsBBorAnte(): boolean {
        return this.getChipDisplayMode() === ChipDisplayMode.ShowAsBBorAnte;
    }

    /**
     * Dynamically adds the ShowBBTagCom component to the specified node.
     * If the node already contains the ShowBBTagCom component, it returns the existing component.
     * 
     * @param {cc.Node} node - The Cocos Creator node to check and add the component to.
     * @returns {ShowBBTagCom | null} - The newly added or existing ShowBBTagCom component, or null if the node is invalid.
    */
    public addShowBBTagCom(node: cc.Node, regMsg: boolean = true): ShowBBTagCom | null {
        if (!node) {
            console.error("Node is null or undefined. Cannot add component.");
            return null;
        }

        let component = node.getComponent(ShowBBTagCom);
        if (!component) {
            component = node.addComponent(ShowBBTagCom);
            if(regMsg) {
                component.registerCDModeMessage();
            }
            console.log(`Added ShowBBTagCom to node: ${node.name}`, component);
        } else {
            console.log(`ShowBBTagCom already exists on node: ${node.name}`, component);
        }

        return component;
    }

    getRoomName(idenID:number, gameID, gameMode, roomMode):string{
        const id = idenID.toString().padStart(4,'0');
        switch(gameID){
            case cv.Enum.GameId.Texas:
                switch(roomMode){
                    case world_pb.RoomMode.RoomModeBomb:
                        return gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? "HSB" + id: "HLB" + id;
                    case world_pb.RoomMode.RoomModeLoose:
                        return "LSHL" + id;
                    default:
                        return gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? "HS" + id : "HL" + id;
                }
            case cv.Enum.GameId.Plo:
                return "PLO" + id;
            case cv.Enum.GameId.Squid:
                return "SQ" + id;
            case cv.Enum.GameId.Jackfruit:
                return "JF" + id;
            case cv.Enum.GameId.ZoomTexas:
            case cv.Enum.GameId.ZoomTexasMax:
                return gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? "HSZ" + id : "HLZ" + id;
            case cv.Enum.GameId.Bet:
                return gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? "AS" + id : "AN" + id;
            default:
                console.log("not matching room name:"+gameID + ' ' + gameMode + ' ' + roomMode)
                return "No match";
        }
    }
    
    // e1JsonData: the urls in this data are encrypt and with E1_PREFIX
    public processE1URLJSON(e1JsonData):any {
        try {
            const that = this;
            // Helper function to decode fields with e1Prefix
            const decodeField = (field) => {
                if (typeof field === 'string' && field.startsWith(that._E1_PREFIX)) {
                    const encryptedData = field.substring(that._E1_PREFIX.length);
                    const decrypted = that.decE1URL(encryptedData)
                    return decrypted;
                }
                return field;
            };
    
            // Recursive function to traverse and decode the JSON
            const traverseAndDecode = (obj) => {
                if (Array.isArray(obj)) {
                    return obj.map(traverseAndDecode);
                } else if (obj !== null && typeof obj === 'object') {
                    const result = {};
                    for (const [key, value] of Object.entries(obj)) {
                        result[key] = traverseAndDecode(value);
                    }
                    return result;
                } else {
                    return decodeField(obj);
                }
            };
    
            // Check if it is a valid JSON object
            if (!e1JsonData || typeof e1JsonData !== 'object') {
                throw new Error("Invalid input: Input must be a JSON object.");
            }
    
            // Decode the JSON object
            const decodedData = traverseAndDecode(e1JsonData);
    
            return decodedData;
        } catch (error) {
            console.error("Error decoding e1 URL JSON:", error.message);
            return null;
        }

    }

    decE1URL(encodedURL:string): string {
        const res = aesHandler.DecryptBase64(encodedURL, Server.getInstance().GET_ENCRYPT_KEY());
        return res;
    }


    public getContentStr(str: string): string {
        let key = 'jackfruit_danmu_label_';
        let type = -1;
        if (str.length > 0) {
            const list = str.split(';');
            if (list.length === 4) {
                key += list[0];
                const sublist = list[0].split('_');
                if (cv.Number(sublist[0]) === 0) {
                    if (cv.Number(sublist[1]) === 0) {
                        if (cv.Number(sublist[2]) < 5) {
                            type = 1;
                        } else {
                            type = 0;
                        }
                    } else if (cv.Number(sublist[2]) < 3) {
                        type = 1;
                    } else if (cv.Number(sublist[2]) < 8) {
                        type = 2;
                    } else {
                        type = 0;
                    }
                } else if (cv.Number(sublist[1]) === 0) {
                    if (cv.Number(sublist[2]) < 3) {
                        type = 1;
                    } else {
                        type = 0;
                    }
                } else if (cv.Number(sublist[2]) < 2) {
                    type = 1;
                } else if (cv.Number(sublist[2]) < 9) {
                    type = 2;
                } else {
                    type = 0;
                }
            }
            return this._getContentStrByKey(key, list, type);
        }
        return '';
    }

    /**
     * 合成需要的内容
     * @param key   json的查找key值
     * @param list  解析出来的字符串数组
     * @param type  0 原样输出 1 添加转牌河牌信息 2 添加牌值信息
     */
    _getContentStrByKey(key: string, list: string[], type: number): string {
        const cardnum = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        if (type === -1) {
            return '';
        }
        if (type === 0) {
            return cv.config.getStringData(key);
        }
        let cardstr = '';
        if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
            cardstr = cv.config.getStringData('jackfruit_cardSuit_' + list[2]) + cardnum[cv.Number(list[3])];
        } else {
            cardstr = cardnum[cv.Number(list[3])] + cv.config.getStringData('jackfruit_cardSuit_' + list[2]);
        }
        return cv.StringTools.formatC(cv.config.getStringData(key), cardstr);
    }

    public isShowBBTipsTriggered(): boolean {
        return this._showBBTipsFlag;
    }

    public setShowBBTipsFlag(value: boolean) {
        if(this._showBBTipsFlag === value){
            return;
        }

        const originShowBBTipsTrigger = this._showBBTipsFlag;
        this._showBBTipsFlag = value;

        if(!originShowBBTipsTrigger && this._showBBTipsFlag){
            this.SaveStringByCCFile('local_key_has_show_BBTips' + cv.dataHandler.getUserData().u32Uid, "true");
        }
    }

    public initShowBBTipsFlag() {
        const val = this.GetStringByCCFile('local_key_has_show_BBTips' + cv.dataHandler.getUserData().u32Uid);;
        this._showBBTipsFlag = val === "true";
    }

    // only recommend to use in the same component
    public waitFor(time: number, comp: cc.Component): Promise<void> {
        return new Promise((resolve) => {
            comp.scheduleOnce(() => {
                resolve();
            }, time);
        });
    }
}