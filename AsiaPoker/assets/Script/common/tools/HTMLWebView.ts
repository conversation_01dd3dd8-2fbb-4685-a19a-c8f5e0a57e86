const { ccclass } = cc._decorator;
@ccclass
export class HTMLWebView extends cc.WebView {
    /**
     * web加载
     * @param raw html 或者 url
     * @param isUrlDRequest 是否直接url请求web
     */
    public loadHTMLString(raw: string, isUrlDRequest: boolean = false): void {
        if (isUrlDRequest) {
            this.url = raw;
        }
        else {
            // eslint-disable-next-line dot-notation
            const impl = this['_impl'];
            const document = impl?._iframe?.contentWindow?.document;
            if (document) {
                document.open();
                document.write(raw);
                document.close();
            } else {
                const meta = `<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;" />`;
                impl.loadHTMLString(meta + raw);
            }
        }
    }

}
