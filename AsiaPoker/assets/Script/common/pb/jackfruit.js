"use strict";var $protobuf=require("protobufjs/minimal");var $Reader=$protobuf.Reader,$Writer=$protobuf.Writer,$util=$protobuf.util;var $root=$protobuf.roots["default"]||($protobuf.roots["default"]={});$root.jackfruit_proto=function(){var jackfruit_proto={};jackfruit_proto.ClientType=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Dummy"]=0;values[valuesById[1]="Normal"]=1;values[valuesById[2]="OverSeas"]=2;values[valuesById[3]="H5"]=3;values[valuesById[4]="H5OverSeas"]=4;values[valuesById[5]="H5Web"]=5;values[valuesById[6]="H5WebOverSeas"]=6;values[valuesById[7]="H5VietnamLasted"]=7;values[valuesById[8]="H5WebVietnamLasted"]=8;return values}();jackfruit_proto.CMD=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="CMD_DUMMY"]=0;values[valuesById[12001]="LOGIN_GAME_REQ"]=12001;values[valuesById[12002]="LOGIN_GAME_RESP"]=12002;values[valuesById[12004]="JOIN_ROOM_REQ"]=12004;values[valuesById[12005]="JOIN_ROOM_RESP"]=12005;values[valuesById[12114]="GAME_DATA_SYNC_REQ"]=12114;values[valuesById[12115]="GAME_DATA_SYNC_RESP"]=12115;values[valuesById[12007]="HEART_BEAT_REQ"]=12007;values[valuesById[12008]="HEART_BEAT_RESP"]=12008;values[valuesById[12011]="SIT_DOWN_REQ"]=12011;values[valuesById[12012]="SIT_DOWN_RESP"]=12012;values[valuesById[12013]="SIT_DOWN_NOTIFY"]=12013;values[valuesById[12014]="PLACE_CARD_REQ"]=12014;values[valuesById[12015]="PLACE_CARD_RESP"]=12015;values[valuesById[12017]="PLACE_CARD_OVER_REQ"]=12017;values[valuesById[12018]="PLACE_CARD_OVER_RESP"]=12018;values[valuesById[12019]="PLACE_CARD_OVER_NOTIFY"]=12019;values[valuesById[12021]="STAND_UP_REQ"]=12021;values[valuesById[12022]="STAND_UP_RESP"]=12022;values[valuesById[12023]="STAND_UP_NOTIFY"]=12023;values[valuesById[12024]="READY_REQ"]=12024;values[valuesById[12025]="READY_RESP"]=12025;values[valuesById[12026]="READY_NOTIFY"]=12026;values[valuesById[12027]="GAME_RECORD_REQ"]=12027;values[valuesById[12028]="GAME_RECORD_RESP"]=12028;values[valuesById[12031]="SEND_CHAT_REQ"]=12031;values[valuesById[12032]="SEND_CHAT_RESP"]=12032;values[valuesById[12033]="SEND_CHAT_NOTIFY"]=12033;values[valuesById[12034]="BUY_IN_REQ"]=12034;values[valuesById[12035]="BUY_IN_RESP"]=12035;values[valuesById[12036]="BUY_IN_NOTIFY"]=12036;values[valuesById[12037]="LEAVE_REQ"]=12037;values[valuesById[12038]="LEAVE_RESP"]=12038;values[valuesById[12041]="SITUATION_REQ"]=12041;values[valuesById[12042]="SITUATION_RESP"]=12042;values[valuesById[12044]="ACTION_DELAY_REQ"]=12044;values[valuesById[12045]="ACTION_DELAY_RESP"]=12045;values[valuesById[12046]="ACTION_DELAY_NOTIFY"]=12046;values[valuesById[12047]="SEND_BARRAGE_REQ"]=12047;values[valuesById[12048]="SEND_BARRAGE_RESP"]=12048;values[valuesById[12049]="SEND_BARRAGE_NOTIFY"]=12049;values[valuesById[12051]="MsgId_BarrageCount_REQ"]=12051;values[valuesById[12052]="MsgId_BarrageCount_RESP"]=12052;values[valuesById[12054]="CHANGE_TABLE_REQ"]=12054;values[valuesById[12055]="CHANGE_TABLE_RESP"]=12055;values[valuesById[12057]="SETTLE_REQ"]=12057;values[valuesById[12058]="SETTLE_RESP"]=12058;values[valuesById[12061]="JACKPOT_DATA_REQ"]=12061;values[valuesById[12062]="JACKPOT_DATA_RESP"]=12062;values[valuesById[12064]="JACKPOT_AWARD_LIST_REQ"]=12064;values[valuesById[12065]="JACKPOT_AWARD_LIST_RESP"]=12065;values[valuesById[12103]="GAME_WILL_START_NOTIFY"]=12103;values[valuesById[12106]="DEAL_NOTIFY"]=12106;values[valuesById[12109]="SQUAT_CARDS_NOTIFY"]=12109;values[valuesById[12113]="GAME_ROUND_END_NOTIFY"]=12113;values[valuesById[12116]="DESTROY_ROOM_NOTIFY"]=12116;values[valuesById[12119]="CONFIRM_TO_CONTINUE"]=12119;values[valuesById[12123]="COMMUNITY_CARDS_NOTIFY"]=12123;values[valuesById[12126]="START_PLACE_CARDS"]=12126;values[valuesById[12129]="Show_PLACE_CARDS_NOTIFY"]=12129;values[valuesById[12133]="WAITING_OTHER_PLAYER_NOTIFY"]=12133;values[valuesById[12136]="CAN_OPERATION_NOTIFY"]=12136;values[valuesById[12139]="PLAYER_INFO_SYNC_NOTIFY"]=12139;values[valuesById[12143]="START_MATCH_NOTIFY"]=12143;values[valuesById[12146]="MATCH_RESULT_NOTIFY"]=12146;values[valuesById[12147]="GetGameUUIds_REQ"]=12147;values[valuesById[12148]="GetGameUUIds_RESP"]=12148;values[valuesById[12149]="BRAND_BARRAGE_NOTIFY"]=12149;values[valuesById[12153]="MODIFY_PLACE_CARDS_NOTIFY"]=12153;values[valuesById[10528]="NotDisturb_REQ"]=10528;values[valuesById[10529]="NotDisturb_RESP"]=10529;values[valuesById[10557]="IsEmojiFree_REQ"]=10557;values[valuesById[10558]="IsEmojiFree_RESP"]=10558;values[valuesById[10559]="IsEmojiFree_NOTIFY"]=10559;values[valuesById[10563]="IntimacyUp_NOTIFY"]=10563;values[valuesById[10551]="Like_REQ"]=10551;values[valuesById[10552]="Like_RESP"]=10552;values[valuesById[10553]="Like_NOTIFY"]=10553;values[valuesById[10556]="GoodFriendJoinTable_NOTIFY"]=10556;values[valuesById[10605]="MagicEmoji_Request"]=10605;values[valuesById[10606]="MagicEmoji_Response"]=10606;values[valuesById[10607]="MagicEmoji_Notice"]=10607;values[valuesById[10608]="DynamicConfig_Notice"]=10608;values[valuesById[10609]="MsgId_GetBarrage_Histories_REQ"]=10609;values[valuesById[10610]="MsgId_GetBarrage_Histories_RESP"]=10610;return values}();jackfruit_proto.RoundState=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="RoomStates_DUMMY"]=0;values[valuesById[1]="Free"]=1;values[valuesById[2]="Ready"]=2;values[valuesById[11]="Wait"]=11;values[valuesById[12]="Deal"]=12;values[valuesById[13]="PlaceCards"]=13;values[valuesById[14]="Turn"]=14;values[valuesById[18]="River"]=18;values[valuesById[20]="Settlement"]=20;return values}();jackfruit_proto.PlayerState=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="SeatState_DUMMY"]=0;values[valuesById[1]="SFree"]=1;values[valuesById[2]="SReady"]=2;values[valuesById[8]="SClickReady"]=8;values[valuesById[11]="SWaitPlaceCards"]=11;values[valuesById[13]="SPlaceCards"]=13;values[valuesById[14]="SModifyPlaceCards"]=14;values[valuesById[15]="SConfirmsPlaceCards"]=15;values[valuesById[20]="SWaitResult"]=20;return values}();jackfruit_proto.ErrorCode=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="ErrorCode_DUMMY"]=0;values[valuesById[1]="OK"]=1;values[valuesById[100]="FAILED"]=100;values[valuesById[13e3]="ROOM_WITHOUT_YOU"]=13e3;values[valuesById[13001]="LOW_VERSION"]=13001;values[valuesById[13002]="INVALID_TOKEN"]=13002;values[valuesById[13003]="SERVER_BUSY"]=13003;values[valuesById[13004]="WITHOUT_LOGIN"]=13004;values[valuesById[13005]="ROOM_NOT_MATCH"]=13005;values[valuesById[13006]="ROOM_NOT_EXIST"]=13006;values[valuesById[13007]="ALREADY_IN_OTHER_GAME"]=13007;values[valuesById[13008]="ROOM_PLAYER_LIMIT"]=13008;values[valuesById[13013]="STOP_SERVICE"]=13013;values[valuesById[13018]="TOO_MANY_PEOPLE"]=13018;values[valuesById[13022]="SEAT_ALREADY_BUSY"]=13022;values[valuesById[13023]="NO_ENOUGH_MONEY"]=13023;values[valuesById[13025]="NOT_YET_COMPLETED_PLACE_CARDS"]=13025;values[valuesById[13026]="ALREADY_SIT_DOWN_THIS_SEAT"]=13026;values[valuesById[13027]="ALREADY_SIT_DOWN_Other_SEAT"]=13027;values[valuesById[13028]="SEAT_ID_NOT_EXIST"]=13028;values[valuesById[13029]="NO_PLACE_CARDS"]=13029;values[valuesById[13030]="BAD_REQ_PARAM"]=13030;values[valuesById[13031]="DISALLOWED_OPERATION"]=13031;values[valuesById[13032]="ALREADY_ADD_STAND_UP_LIST"]=13032;values[valuesById[13033]="CAN_NOT_LEAVE_IN_THE_GAME"]=13033;values[valuesById[13034]="Table_Player_Or_Owner_Can_Chat"]=13034;values[valuesById[13035]="Barrage_Sent_Too_Often"]=13035;values[valuesById[13036]="Action_Delay_Exhausted"]=13036;values[valuesById[13037]="Player_Limit_BuyIn"]=13037;values[valuesById[13038]="ALREADY_ADD_LEAVE_LIST"]=13038;values[valuesById[13039]="NOT_ENOUGH_STAKE"]=13039;values[valuesById[13040]="BUY_IN_AMOUNT_INVALID"]=13040;values[valuesById[13041]="CAN_NOT_CHANGE_TABLE"]=13041;values[valuesById[13042]="NOT_SETTLED_YET"]=13042;values[valuesById[13043]="BUY_IN_SEAT_WAS_SNATCHED"]=13043;values[valuesById[13045]="NO_JACKPOT"]=13045;values[valuesById[3]="GameServer_Player_Not_Found"]=3;values[valuesById[1214]="GameServer_Send_Barrage_Too_Fast"]=1214;values[valuesById[22]="GameServer_RoomID_Not_Found"]=22;values[valuesById[1215]="GameServer_Queue_Barrage_Full"]=1215;values[valuesById[1260]="NeedAuthVerify"]=1260;values[valuesById[1261]="WaitAuthRefreshCD"]=1261;values[valuesById[1252]="AlreadyLiked"]=1252;values[valuesById[1253]="Param_Validate"]=1253;values[valuesById[116]="IsEmojiFree"]=116;values[valuesById[31117]="C2CPAYMENT_LIST_GET_ERROR"]=31117;values[valuesById[31118]="C2CPAYMENT_NOT_ALLOW"]=31118;return values}();jackfruit_proto.GameDataSyncReq=function(){function GameDataSyncReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameDataSyncReq.prototype.roomId=0;GameDataSyncReq.create=function create(properties){return new GameDataSyncReq(properties)};GameDataSyncReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);return w};GameDataSyncReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameDataSyncReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GameDataSyncReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;default:r.skipType(t&7);break}}return m};GameDataSyncReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameDataSyncReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}return null};GameDataSyncReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GameDataSyncReq)return d;var m=new $root.jackfruit_proto.GameDataSyncReq;if(d.roomId!=null){m.roomId=d.roomId|0}return m};GameDataSyncReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}return d};GameDataSyncReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameDataSyncReq}();jackfruit_proto.CardLevel=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Dump"]=0;values[valuesById[10]="RoyalFlush"]=10;values[valuesById[9]="StraightFlush"]=9;values[valuesById[8]="FourOfAKind"]=8;values[valuesById[7]="FullHouse"]=7;values[valuesById[6]="Flush"]=6;values[valuesById[5]="StraightI"]=5;values[valuesById[4]="ThreeOfAKind"]=4;values[valuesById[3]="TwoPair"]=3;values[valuesById[2]="OnePair"]=2;values[valuesById[1]="HighCard"]=1;return values}();jackfruit_proto.GameDataSyncResp=function(){function GameDataSyncResp(p){this.delayedOperationPlayIds=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameDataSyncResp.prototype.code=0;GameDataSyncResp.prototype.param=null;GameDataSyncResp.prototype.curState=0;GameDataSyncResp.prototype.cachedNotifyMsg=null;GameDataSyncResp.prototype.fee=null;GameDataSyncResp.prototype.barrageLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSyncResp.prototype.actionDelayCountsFee=0;GameDataSyncResp.prototype.delayedOperationPlayIds=$util.emptyArray;GameDataSyncResp.prototype.canChangeTable=false;GameDataSyncResp.prototype.startMatchTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSyncResp.prototype.matchedSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSyncResp.prototype.jackpotLeftAmount=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSyncResp.prototype.dynamicConfig=null;GameDataSyncResp.create=function create(properties){return new GameDataSyncResp(properties)};GameDataSyncResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.param!=null&&Object.hasOwnProperty.call(m,"param"))$root.jackfruit_proto.RoomParam.encode(m.param,w.uint32(18).fork()).ldelim();if(m.curState!=null&&Object.hasOwnProperty.call(m,"curState"))w.uint32(24).int32(m.curState);if(m.cachedNotifyMsg!=null&&Object.hasOwnProperty.call(m,"cachedNotifyMsg"))$root.jackfruit_proto.GameRoundEndNotify.encode(m.cachedNotifyMsg,w.uint32(34).fork()).ldelim();if(m.fee!=null&&Object.hasOwnProperty.call(m,"fee"))$root.jackfruit_proto.PayMoneyItems.encode(m.fee,w.uint32(42).fork()).ldelim();if(m.barrageLeftSeconds!=null&&Object.hasOwnProperty.call(m,"barrageLeftSeconds"))w.uint32(48).int64(m.barrageLeftSeconds);if(m.actionDelayCountsFee!=null&&Object.hasOwnProperty.call(m,"actionDelayCountsFee"))w.uint32(56).int32(m.actionDelayCountsFee);if(m.delayedOperationPlayIds!=null&&m.delayedOperationPlayIds.length){w.uint32(66).fork();for(var i=0;i<m.delayedOperationPlayIds.length;++i)w.uint32(m.delayedOperationPlayIds[i]);w.ldelim()}if(m.canChangeTable!=null&&Object.hasOwnProperty.call(m,"canChangeTable"))w.uint32(72).bool(m.canChangeTable);if(m.startMatchTimeStamp!=null&&Object.hasOwnProperty.call(m,"startMatchTimeStamp"))w.uint32(80).int64(m.startMatchTimeStamp);if(m.matchedSeconds!=null&&Object.hasOwnProperty.call(m,"matchedSeconds"))w.uint32(88).int64(m.matchedSeconds);if(m.jackpotLeftAmount!=null&&Object.hasOwnProperty.call(m,"jackpotLeftAmount"))w.uint32(96).int64(m.jackpotLeftAmount);if(m.dynamicConfig!=null&&Object.hasOwnProperty.call(m,"dynamicConfig"))$root.jackfruit_proto.DynamicConfig.encode(m.dynamicConfig,w.uint32(106).fork()).ldelim();return w};GameDataSyncResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameDataSyncResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GameDataSyncResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.param=$root.jackfruit_proto.RoomParam.decode(r,r.uint32());break;case 3:m.curState=r.int32();break;case 4:m.cachedNotifyMsg=$root.jackfruit_proto.GameRoundEndNotify.decode(r,r.uint32());break;case 5:m.fee=$root.jackfruit_proto.PayMoneyItems.decode(r,r.uint32());break;case 6:m.barrageLeftSeconds=r.int64();break;case 7:m.actionDelayCountsFee=r.int32();break;case 8:if(!(m.delayedOperationPlayIds&&m.delayedOperationPlayIds.length))m.delayedOperationPlayIds=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.delayedOperationPlayIds.push(r.uint32())}else m.delayedOperationPlayIds.push(r.uint32());break;case 9:m.canChangeTable=r.bool();break;case 10:m.startMatchTimeStamp=r.int64();break;case 11:m.matchedSeconds=r.int64();break;case 12:m.jackpotLeftAmount=r.int64();break;case 13:m.dynamicConfig=$root.jackfruit_proto.DynamicConfig.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};GameDataSyncResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameDataSyncResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.param!=null&&m.hasOwnProperty("param")){{var e=$root.jackfruit_proto.RoomParam.verify(m.param);if(e)return"param."+e}}if(m.curState!=null&&m.hasOwnProperty("curState")){switch(m.curState){default:return"curState: enum value expected";case 0:case 1:case 2:case 11:case 12:case 13:case 14:case 18:case 20:break}}if(m.cachedNotifyMsg!=null&&m.hasOwnProperty("cachedNotifyMsg")){{var e=$root.jackfruit_proto.GameRoundEndNotify.verify(m.cachedNotifyMsg);if(e)return"cachedNotifyMsg."+e}}if(m.fee!=null&&m.hasOwnProperty("fee")){{var e=$root.jackfruit_proto.PayMoneyItems.verify(m.fee);if(e)return"fee."+e}}if(m.barrageLeftSeconds!=null&&m.hasOwnProperty("barrageLeftSeconds")){if(!$util.isInteger(m.barrageLeftSeconds)&&!(m.barrageLeftSeconds&&$util.isInteger(m.barrageLeftSeconds.low)&&$util.isInteger(m.barrageLeftSeconds.high)))return"barrageLeftSeconds: integer|Long expected"}if(m.actionDelayCountsFee!=null&&m.hasOwnProperty("actionDelayCountsFee")){if(!$util.isInteger(m.actionDelayCountsFee))return"actionDelayCountsFee: integer expected"}if(m.delayedOperationPlayIds!=null&&m.hasOwnProperty("delayedOperationPlayIds")){if(!Array.isArray(m.delayedOperationPlayIds))return"delayedOperationPlayIds: array expected";for(var i=0;i<m.delayedOperationPlayIds.length;++i){if(!$util.isInteger(m.delayedOperationPlayIds[i]))return"delayedOperationPlayIds: integer[] expected"}}if(m.canChangeTable!=null&&m.hasOwnProperty("canChangeTable")){if(typeof m.canChangeTable!=="boolean")return"canChangeTable: boolean expected"}if(m.startMatchTimeStamp!=null&&m.hasOwnProperty("startMatchTimeStamp")){if(!$util.isInteger(m.startMatchTimeStamp)&&!(m.startMatchTimeStamp&&$util.isInteger(m.startMatchTimeStamp.low)&&$util.isInteger(m.startMatchTimeStamp.high)))return"startMatchTimeStamp: integer|Long expected"}if(m.matchedSeconds!=null&&m.hasOwnProperty("matchedSeconds")){if(!$util.isInteger(m.matchedSeconds)&&!(m.matchedSeconds&&$util.isInteger(m.matchedSeconds.low)&&$util.isInteger(m.matchedSeconds.high)))return"matchedSeconds: integer|Long expected"}if(m.jackpotLeftAmount!=null&&m.hasOwnProperty("jackpotLeftAmount")){if(!$util.isInteger(m.jackpotLeftAmount)&&!(m.jackpotLeftAmount&&$util.isInteger(m.jackpotLeftAmount.low)&&$util.isInteger(m.jackpotLeftAmount.high)))return"jackpotLeftAmount: integer|Long expected"}if(m.dynamicConfig!=null&&m.hasOwnProperty("dynamicConfig")){{var e=$root.jackfruit_proto.DynamicConfig.verify(m.dynamicConfig);if(e)return"dynamicConfig."+e}}return null};GameDataSyncResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GameDataSyncResp)return d;var m=new $root.jackfruit_proto.GameDataSyncResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.param!=null){if(typeof d.param!=="object")throw TypeError(".jackfruit_proto.GameDataSyncResp.param: object expected");m.param=$root.jackfruit_proto.RoomParam.fromObject(d.param)}switch(d.curState){case"RoomStates_DUMMY":case 0:m.curState=0;break;case"Free":case 1:m.curState=1;break;case"Ready":case 2:m.curState=2;break;case"Wait":case 11:m.curState=11;break;case"Deal":case 12:m.curState=12;break;case"PlaceCards":case 13:m.curState=13;break;case"Turn":case 14:m.curState=14;break;case"River":case 18:m.curState=18;break;case"Settlement":case 20:m.curState=20;break}if(d.cachedNotifyMsg!=null){if(typeof d.cachedNotifyMsg!=="object")throw TypeError(".jackfruit_proto.GameDataSyncResp.cachedNotifyMsg: object expected");m.cachedNotifyMsg=$root.jackfruit_proto.GameRoundEndNotify.fromObject(d.cachedNotifyMsg)}if(d.fee!=null){if(typeof d.fee!=="object")throw TypeError(".jackfruit_proto.GameDataSyncResp.fee: object expected");m.fee=$root.jackfruit_proto.PayMoneyItems.fromObject(d.fee)}if(d.barrageLeftSeconds!=null){if($util.Long)(m.barrageLeftSeconds=$util.Long.fromValue(d.barrageLeftSeconds)).unsigned=false;else if(typeof d.barrageLeftSeconds==="string")m.barrageLeftSeconds=parseInt(d.barrageLeftSeconds,10);else if(typeof d.barrageLeftSeconds==="number")m.barrageLeftSeconds=d.barrageLeftSeconds;else if(typeof d.barrageLeftSeconds==="object")m.barrageLeftSeconds=new $util.LongBits(d.barrageLeftSeconds.low>>>0,d.barrageLeftSeconds.high>>>0).toNumber()}if(d.actionDelayCountsFee!=null){m.actionDelayCountsFee=d.actionDelayCountsFee|0}if(d.delayedOperationPlayIds){if(!Array.isArray(d.delayedOperationPlayIds))throw TypeError(".jackfruit_proto.GameDataSyncResp.delayedOperationPlayIds: array expected");m.delayedOperationPlayIds=[];for(var i=0;i<d.delayedOperationPlayIds.length;++i){m.delayedOperationPlayIds[i]=d.delayedOperationPlayIds[i]>>>0}}if(d.canChangeTable!=null){m.canChangeTable=Boolean(d.canChangeTable)}if(d.startMatchTimeStamp!=null){if($util.Long)(m.startMatchTimeStamp=$util.Long.fromValue(d.startMatchTimeStamp)).unsigned=false;else if(typeof d.startMatchTimeStamp==="string")m.startMatchTimeStamp=parseInt(d.startMatchTimeStamp,10);else if(typeof d.startMatchTimeStamp==="number")m.startMatchTimeStamp=d.startMatchTimeStamp;else if(typeof d.startMatchTimeStamp==="object")m.startMatchTimeStamp=new $util.LongBits(d.startMatchTimeStamp.low>>>0,d.startMatchTimeStamp.high>>>0).toNumber()}if(d.matchedSeconds!=null){if($util.Long)(m.matchedSeconds=$util.Long.fromValue(d.matchedSeconds)).unsigned=false;else if(typeof d.matchedSeconds==="string")m.matchedSeconds=parseInt(d.matchedSeconds,10);else if(typeof d.matchedSeconds==="number")m.matchedSeconds=d.matchedSeconds;else if(typeof d.matchedSeconds==="object")m.matchedSeconds=new $util.LongBits(d.matchedSeconds.low>>>0,d.matchedSeconds.high>>>0).toNumber()}if(d.jackpotLeftAmount!=null){if($util.Long)(m.jackpotLeftAmount=$util.Long.fromValue(d.jackpotLeftAmount)).unsigned=false;else if(typeof d.jackpotLeftAmount==="string")m.jackpotLeftAmount=parseInt(d.jackpotLeftAmount,10);else if(typeof d.jackpotLeftAmount==="number")m.jackpotLeftAmount=d.jackpotLeftAmount;else if(typeof d.jackpotLeftAmount==="object")m.jackpotLeftAmount=new $util.LongBits(d.jackpotLeftAmount.low>>>0,d.jackpotLeftAmount.high>>>0).toNumber()}if(d.dynamicConfig!=null){if(typeof d.dynamicConfig!=="object")throw TypeError(".jackfruit_proto.GameDataSyncResp.dynamicConfig: object expected");m.dynamicConfig=$root.jackfruit_proto.DynamicConfig.fromObject(d.dynamicConfig)}return m};GameDataSyncResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.delayedOperationPlayIds=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.param=null;d.curState=o.enums===String?"RoomStates_DUMMY":0;d.cachedNotifyMsg=null;d.fee=null;if($util.Long){var n=new $util.Long(0,0,false);d.barrageLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.barrageLeftSeconds=o.longs===String?"0":0;d.actionDelayCountsFee=0;d.canChangeTable=false;if($util.Long){var n=new $util.Long(0,0,false);d.startMatchTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.startMatchTimeStamp=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.matchedSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.matchedSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.jackpotLeftAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.jackpotLeftAmount=o.longs===String?"0":0;d.dynamicConfig=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.param!=null&&m.hasOwnProperty("param")){d.param=$root.jackfruit_proto.RoomParam.toObject(m.param,o)}if(m.curState!=null&&m.hasOwnProperty("curState")){d.curState=o.enums===String?$root.jackfruit_proto.RoundState[m.curState]:m.curState}if(m.cachedNotifyMsg!=null&&m.hasOwnProperty("cachedNotifyMsg")){d.cachedNotifyMsg=$root.jackfruit_proto.GameRoundEndNotify.toObject(m.cachedNotifyMsg,o)}if(m.fee!=null&&m.hasOwnProperty("fee")){d.fee=$root.jackfruit_proto.PayMoneyItems.toObject(m.fee,o)}if(m.barrageLeftSeconds!=null&&m.hasOwnProperty("barrageLeftSeconds")){if(typeof m.barrageLeftSeconds==="number")d.barrageLeftSeconds=o.longs===String?String(m.barrageLeftSeconds):m.barrageLeftSeconds;else d.barrageLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.barrageLeftSeconds):o.longs===Number?new $util.LongBits(m.barrageLeftSeconds.low>>>0,m.barrageLeftSeconds.high>>>0).toNumber():m.barrageLeftSeconds}if(m.actionDelayCountsFee!=null&&m.hasOwnProperty("actionDelayCountsFee")){d.actionDelayCountsFee=m.actionDelayCountsFee}if(m.delayedOperationPlayIds&&m.delayedOperationPlayIds.length){d.delayedOperationPlayIds=[];for(var j=0;j<m.delayedOperationPlayIds.length;++j){d.delayedOperationPlayIds[j]=m.delayedOperationPlayIds[j]}}if(m.canChangeTable!=null&&m.hasOwnProperty("canChangeTable")){d.canChangeTable=m.canChangeTable}if(m.startMatchTimeStamp!=null&&m.hasOwnProperty("startMatchTimeStamp")){if(typeof m.startMatchTimeStamp==="number")d.startMatchTimeStamp=o.longs===String?String(m.startMatchTimeStamp):m.startMatchTimeStamp;else d.startMatchTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.startMatchTimeStamp):o.longs===Number?new $util.LongBits(m.startMatchTimeStamp.low>>>0,m.startMatchTimeStamp.high>>>0).toNumber():m.startMatchTimeStamp}if(m.matchedSeconds!=null&&m.hasOwnProperty("matchedSeconds")){if(typeof m.matchedSeconds==="number")d.matchedSeconds=o.longs===String?String(m.matchedSeconds):m.matchedSeconds;else d.matchedSeconds=o.longs===String?$util.Long.prototype.toString.call(m.matchedSeconds):o.longs===Number?new $util.LongBits(m.matchedSeconds.low>>>0,m.matchedSeconds.high>>>0).toNumber():m.matchedSeconds}if(m.jackpotLeftAmount!=null&&m.hasOwnProperty("jackpotLeftAmount")){if(typeof m.jackpotLeftAmount==="number")d.jackpotLeftAmount=o.longs===String?String(m.jackpotLeftAmount):m.jackpotLeftAmount;else d.jackpotLeftAmount=o.longs===String?$util.Long.prototype.toString.call(m.jackpotLeftAmount):o.longs===Number?new $util.LongBits(m.jackpotLeftAmount.low>>>0,m.jackpotLeftAmount.high>>>0).toNumber():m.jackpotLeftAmount}if(m.dynamicConfig!=null&&m.hasOwnProperty("dynamicConfig")){d.dynamicConfig=$root.jackfruit_proto.DynamicConfig.toObject(m.dynamicConfig,o)}return d};GameDataSyncResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameDataSyncResp}();jackfruit_proto.HeartBeatReq=function(){function HeartBeatReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}HeartBeatReq.prototype.uid=0;HeartBeatReq.create=function create(properties){return new HeartBeatReq(properties)};HeartBeatReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);return w};HeartBeatReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};HeartBeatReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.HeartBeatReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;default:r.skipType(t&7);break}}return m};HeartBeatReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};HeartBeatReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}return null};HeartBeatReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.HeartBeatReq)return d;var m=new $root.jackfruit_proto.HeartBeatReq;if(d.uid!=null){m.uid=d.uid>>>0}return m};HeartBeatReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}return d};HeartBeatReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return HeartBeatReq}();jackfruit_proto.HeartBeatResp=function(){function HeartBeatResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}HeartBeatResp.prototype.uid=0;HeartBeatResp.prototype.timestamp=$util.Long?$util.Long.fromBits(0,0,false):0;HeartBeatResp.create=function create(properties){return new HeartBeatResp(properties)};HeartBeatResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.timestamp!=null&&Object.hasOwnProperty.call(m,"timestamp"))w.uint32(16).int64(m.timestamp);return w};HeartBeatResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};HeartBeatResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.HeartBeatResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.timestamp=r.int64();break;default:r.skipType(t&7);break}}return m};HeartBeatResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};HeartBeatResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.timestamp!=null&&m.hasOwnProperty("timestamp")){if(!$util.isInteger(m.timestamp)&&!(m.timestamp&&$util.isInteger(m.timestamp.low)&&$util.isInteger(m.timestamp.high)))return"timestamp: integer|Long expected"}return null};HeartBeatResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.HeartBeatResp)return d;var m=new $root.jackfruit_proto.HeartBeatResp;if(d.uid!=null){m.uid=d.uid>>>0}if(d.timestamp!=null){if($util.Long)(m.timestamp=$util.Long.fromValue(d.timestamp)).unsigned=false;else if(typeof d.timestamp==="string")m.timestamp=parseInt(d.timestamp,10);else if(typeof d.timestamp==="number")m.timestamp=d.timestamp;else if(typeof d.timestamp==="object")m.timestamp=new $util.LongBits(d.timestamp.low>>>0,d.timestamp.high>>>0).toNumber()}return m};HeartBeatResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;if($util.Long){var n=new $util.Long(0,0,false);d.timestamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.timestamp=o.longs===String?"0":0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.timestamp!=null&&m.hasOwnProperty("timestamp")){if(typeof m.timestamp==="number")d.timestamp=o.longs===String?String(m.timestamp):m.timestamp;else d.timestamp=o.longs===String?$util.Long.prototype.toString.call(m.timestamp):o.longs===Number?new $util.LongBits(m.timestamp.low>>>0,m.timestamp.high>>>0).toNumber():m.timestamp}return d};HeartBeatResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return HeartBeatResp}();jackfruit_proto.RoomParam=function(){function RoomParam(p){this.zoneMultiple=[];this.showForClients=[];this.allianceIds=[];this.plats=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomParam.prototype.ownerType=0;RoomParam.prototype.gameMode=0;RoomParam.prototype.playerCountMax=0;RoomParam.prototype.gameName="";RoomParam.prototype.ante=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.manualCreated=0;RoomParam.prototype.minimumAmount=0;RoomParam.prototype.ruleCheckAmount=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.ruleAddToAmount=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.zoneMultiple=$util.emptyArray;RoomParam.prototype.createTime=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.limitPlayers=0;RoomParam.prototype.creatorId=0;RoomParam.prototype.creatorName="";RoomParam.prototype.gameTimeLimit=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.idleSecs=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.ruleCheckScore=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.ruleAddToScore=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.ruleServeScore=$util.Long?$util.Long.fromBits(0,0,false):0;RoomParam.prototype.ownerClubName="";RoomParam.prototype.clubHead="";RoomParam.prototype.isBanVpn=false;RoomParam.prototype.showForClients=$util.emptyArray;RoomParam.prototype.clubId=0;RoomParam.prototype.allianceIds=$util.emptyArray;RoomParam.prototype.ruleSwitchRandomSeat=0;RoomParam.prototype.deskType=0;RoomParam.prototype.autoStartNum=0;RoomParam.prototype.isPrivate=false;RoomParam.prototype.JoinPassword="";RoomParam.prototype.buyInPassword="";RoomParam.prototype.plats=$util.emptyArray;RoomParam.create=function create(properties){return new RoomParam(properties)};RoomParam.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.ownerType!=null&&Object.hasOwnProperty.call(m,"ownerType"))w.uint32(8).int32(m.ownerType);if(m.gameMode!=null&&Object.hasOwnProperty.call(m,"gameMode"))w.uint32(16).int32(m.gameMode);if(m.playerCountMax!=null&&Object.hasOwnProperty.call(m,"playerCountMax"))w.uint32(24).int32(m.playerCountMax);if(m.ante!=null&&Object.hasOwnProperty.call(m,"ante"))w.uint32(32).int64(m.ante);if(m.gameName!=null&&Object.hasOwnProperty.call(m,"gameName"))w.uint32(42).string(m.gameName);if(m.clubId!=null&&Object.hasOwnProperty.call(m,"clubId"))w.uint32(48).uint32(m.clubId);if(m.allianceIds!=null&&m.allianceIds.length){w.uint32(58).fork();for(var i=0;i<m.allianceIds.length;++i)w.uint32(m.allianceIds[i]);w.ldelim()}if(m.ruleSwitchRandomSeat!=null&&Object.hasOwnProperty.call(m,"ruleSwitchRandomSeat"))w.uint32(64).int32(m.ruleSwitchRandomSeat);if(m.manualCreated!=null&&Object.hasOwnProperty.call(m,"manualCreated"))w.uint32(72).int32(m.manualCreated);if(m.minimumAmount!=null&&Object.hasOwnProperty.call(m,"minimumAmount"))w.uint32(80).int32(m.minimumAmount);if(m.deskType!=null&&Object.hasOwnProperty.call(m,"deskType"))w.uint32(88).int32(m.deskType);if(m.ruleAddToAmount!=null&&Object.hasOwnProperty.call(m,"ruleAddToAmount"))w.uint32(96).int64(m.ruleAddToAmount);if(m.zoneMultiple!=null&&m.zoneMultiple.length){w.uint32(106).fork();for(var i=0;i<m.zoneMultiple.length;++i)w.uint32(m.zoneMultiple[i]);w.ldelim()}if(m.createTime!=null&&Object.hasOwnProperty.call(m,"createTime"))w.uint32(112).int64(m.createTime);if(m.autoStartNum!=null&&Object.hasOwnProperty.call(m,"autoStartNum"))w.uint32(120).int32(m.autoStartNum);if(m.isPrivate!=null&&Object.hasOwnProperty.call(m,"isPrivate"))w.uint32(128).bool(m.isPrivate);if(m.creatorId!=null&&Object.hasOwnProperty.call(m,"creatorId"))w.uint32(136).uint32(m.creatorId);if(m.limitPlayers!=null&&Object.hasOwnProperty.call(m,"limitPlayers"))w.uint32(144).uint32(m.limitPlayers);if(m.JoinPassword!=null&&Object.hasOwnProperty.call(m,"JoinPassword"))w.uint32(154).string(m.JoinPassword);if(m.creatorName!=null&&Object.hasOwnProperty.call(m,"creatorName"))w.uint32(162).string(m.creatorName);if(m.gameTimeLimit!=null&&Object.hasOwnProperty.call(m,"gameTimeLimit"))w.uint32(168).int64(m.gameTimeLimit);if(m.ownerClubName!=null&&Object.hasOwnProperty.call(m,"ownerClubName"))w.uint32(178).string(m.ownerClubName);if(m.clubHead!=null&&Object.hasOwnProperty.call(m,"clubHead"))w.uint32(186).string(m.clubHead);if(m.buyInPassword!=null&&Object.hasOwnProperty.call(m,"buyInPassword"))w.uint32(194).string(m.buyInPassword);if(m.ruleCheckAmount!=null&&Object.hasOwnProperty.call(m,"ruleCheckAmount"))w.uint32(200).int64(m.ruleCheckAmount);if(m.idleSecs!=null&&Object.hasOwnProperty.call(m,"idleSecs"))w.uint32(208).int64(m.idleSecs);if(m.ruleCheckScore!=null&&Object.hasOwnProperty.call(m,"ruleCheckScore"))w.uint32(216).int64(m.ruleCheckScore);if(m.ruleAddToScore!=null&&Object.hasOwnProperty.call(m,"ruleAddToScore"))w.uint32(224).int64(m.ruleAddToScore);if(m.ruleServeScore!=null&&Object.hasOwnProperty.call(m,"ruleServeScore"))w.uint32(232).int64(m.ruleServeScore);if(m.plats!=null&&m.plats.length){w.uint32(242).fork();for(var i=0;i<m.plats.length;++i)w.uint32(m.plats[i]);w.ldelim()}if(m.isBanVpn!=null&&Object.hasOwnProperty.call(m,"isBanVpn"))w.uint32(328).bool(m.isBanVpn);if(m.showForClients!=null&&m.showForClients.length){w.uint32(418).fork();for(var i=0;i<m.showForClients.length;++i)w.uint32(m.showForClients[i]);w.ldelim()}return w};RoomParam.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomParam.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.RoomParam;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.ownerType=r.int32();break;case 2:m.gameMode=r.int32();break;case 3:m.playerCountMax=r.int32();break;case 5:m.gameName=r.string();break;case 4:m.ante=r.int64();break;case 9:m.manualCreated=r.int32();break;case 10:m.minimumAmount=r.int32();break;case 25:m.ruleCheckAmount=r.int64();break;case 12:m.ruleAddToAmount=r.int64();break;case 13:if(!(m.zoneMultiple&&m.zoneMultiple.length))m.zoneMultiple=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.zoneMultiple.push(r.uint32())}else m.zoneMultiple.push(r.uint32());break;case 14:m.createTime=r.int64();break;case 18:m.limitPlayers=r.uint32();break;case 17:m.creatorId=r.uint32();break;case 20:m.creatorName=r.string();break;case 21:m.gameTimeLimit=r.int64();break;case 26:m.idleSecs=r.int64();break;case 27:m.ruleCheckScore=r.int64();break;case 28:m.ruleAddToScore=r.int64();break;case 29:m.ruleServeScore=r.int64();break;case 22:m.ownerClubName=r.string();break;case 23:m.clubHead=r.string();break;case 41:m.isBanVpn=r.bool();break;case 52:if(!(m.showForClients&&m.showForClients.length))m.showForClients=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.showForClients.push(r.uint32())}else m.showForClients.push(r.uint32());break;case 6:m.clubId=r.uint32();break;case 7:if(!(m.allianceIds&&m.allianceIds.length))m.allianceIds=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.allianceIds.push(r.uint32())}else m.allianceIds.push(r.uint32());break;case 8:m.ruleSwitchRandomSeat=r.int32();break;case 11:m.deskType=r.int32();break;case 15:m.autoStartNum=r.int32();break;case 16:m.isPrivate=r.bool();break;case 19:m.JoinPassword=r.string();break;case 24:m.buyInPassword=r.string();break;case 30:if(!(m.plats&&m.plats.length))m.plats=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.plats.push(r.uint32())}else m.plats.push(r.uint32());break;default:r.skipType(t&7);break}}return m};RoomParam.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomParam.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.ownerType!=null&&m.hasOwnProperty("ownerType")){if(!$util.isInteger(m.ownerType))return"ownerType: integer expected"}if(m.gameMode!=null&&m.hasOwnProperty("gameMode")){if(!$util.isInteger(m.gameMode))return"gameMode: integer expected"}if(m.playerCountMax!=null&&m.hasOwnProperty("playerCountMax")){if(!$util.isInteger(m.playerCountMax))return"playerCountMax: integer expected"}if(m.gameName!=null&&m.hasOwnProperty("gameName")){if(!$util.isString(m.gameName))return"gameName: string expected"}if(m.ante!=null&&m.hasOwnProperty("ante")){if(!$util.isInteger(m.ante)&&!(m.ante&&$util.isInteger(m.ante.low)&&$util.isInteger(m.ante.high)))return"ante: integer|Long expected"}if(m.manualCreated!=null&&m.hasOwnProperty("manualCreated")){if(!$util.isInteger(m.manualCreated))return"manualCreated: integer expected"}if(m.minimumAmount!=null&&m.hasOwnProperty("minimumAmount")){if(!$util.isInteger(m.minimumAmount))return"minimumAmount: integer expected"}if(m.ruleCheckAmount!=null&&m.hasOwnProperty("ruleCheckAmount")){if(!$util.isInteger(m.ruleCheckAmount)&&!(m.ruleCheckAmount&&$util.isInteger(m.ruleCheckAmount.low)&&$util.isInteger(m.ruleCheckAmount.high)))return"ruleCheckAmount: integer|Long expected"}if(m.ruleAddToAmount!=null&&m.hasOwnProperty("ruleAddToAmount")){if(!$util.isInteger(m.ruleAddToAmount)&&!(m.ruleAddToAmount&&$util.isInteger(m.ruleAddToAmount.low)&&$util.isInteger(m.ruleAddToAmount.high)))return"ruleAddToAmount: integer|Long expected"}if(m.zoneMultiple!=null&&m.hasOwnProperty("zoneMultiple")){if(!Array.isArray(m.zoneMultiple))return"zoneMultiple: array expected";for(var i=0;i<m.zoneMultiple.length;++i){if(!$util.isInteger(m.zoneMultiple[i]))return"zoneMultiple: integer[] expected"}}if(m.createTime!=null&&m.hasOwnProperty("createTime")){if(!$util.isInteger(m.createTime)&&!(m.createTime&&$util.isInteger(m.createTime.low)&&$util.isInteger(m.createTime.high)))return"createTime: integer|Long expected"}if(m.limitPlayers!=null&&m.hasOwnProperty("limitPlayers")){if(!$util.isInteger(m.limitPlayers))return"limitPlayers: integer expected"}if(m.creatorId!=null&&m.hasOwnProperty("creatorId")){if(!$util.isInteger(m.creatorId))return"creatorId: integer expected"}if(m.creatorName!=null&&m.hasOwnProperty("creatorName")){if(!$util.isString(m.creatorName))return"creatorName: string expected"}if(m.gameTimeLimit!=null&&m.hasOwnProperty("gameTimeLimit")){if(!$util.isInteger(m.gameTimeLimit)&&!(m.gameTimeLimit&&$util.isInteger(m.gameTimeLimit.low)&&$util.isInteger(m.gameTimeLimit.high)))return"gameTimeLimit: integer|Long expected"}if(m.idleSecs!=null&&m.hasOwnProperty("idleSecs")){if(!$util.isInteger(m.idleSecs)&&!(m.idleSecs&&$util.isInteger(m.idleSecs.low)&&$util.isInteger(m.idleSecs.high)))return"idleSecs: integer|Long expected"}if(m.ruleCheckScore!=null&&m.hasOwnProperty("ruleCheckScore")){if(!$util.isInteger(m.ruleCheckScore)&&!(m.ruleCheckScore&&$util.isInteger(m.ruleCheckScore.low)&&$util.isInteger(m.ruleCheckScore.high)))return"ruleCheckScore: integer|Long expected"}if(m.ruleAddToScore!=null&&m.hasOwnProperty("ruleAddToScore")){if(!$util.isInteger(m.ruleAddToScore)&&!(m.ruleAddToScore&&$util.isInteger(m.ruleAddToScore.low)&&$util.isInteger(m.ruleAddToScore.high)))return"ruleAddToScore: integer|Long expected"}if(m.ruleServeScore!=null&&m.hasOwnProperty("ruleServeScore")){if(!$util.isInteger(m.ruleServeScore)&&!(m.ruleServeScore&&$util.isInteger(m.ruleServeScore.low)&&$util.isInteger(m.ruleServeScore.high)))return"ruleServeScore: integer|Long expected"}if(m.ownerClubName!=null&&m.hasOwnProperty("ownerClubName")){if(!$util.isString(m.ownerClubName))return"ownerClubName: string expected"}if(m.clubHead!=null&&m.hasOwnProperty("clubHead")){if(!$util.isString(m.clubHead))return"clubHead: string expected"}if(m.isBanVpn!=null&&m.hasOwnProperty("isBanVpn")){if(typeof m.isBanVpn!=="boolean")return"isBanVpn: boolean expected"}if(m.showForClients!=null&&m.hasOwnProperty("showForClients")){if(!Array.isArray(m.showForClients))return"showForClients: array expected";for(var i=0;i<m.showForClients.length;++i){if(!$util.isInteger(m.showForClients[i]))return"showForClients: integer[] expected"}}if(m.clubId!=null&&m.hasOwnProperty("clubId")){if(!$util.isInteger(m.clubId))return"clubId: integer expected"}if(m.allianceIds!=null&&m.hasOwnProperty("allianceIds")){if(!Array.isArray(m.allianceIds))return"allianceIds: array expected";for(var i=0;i<m.allianceIds.length;++i){if(!$util.isInteger(m.allianceIds[i]))return"allianceIds: integer[] expected"}}if(m.ruleSwitchRandomSeat!=null&&m.hasOwnProperty("ruleSwitchRandomSeat")){if(!$util.isInteger(m.ruleSwitchRandomSeat))return"ruleSwitchRandomSeat: integer expected"}if(m.deskType!=null&&m.hasOwnProperty("deskType")){if(!$util.isInteger(m.deskType))return"deskType: integer expected"}if(m.autoStartNum!=null&&m.hasOwnProperty("autoStartNum")){if(!$util.isInteger(m.autoStartNum))return"autoStartNum: integer expected"}if(m.isPrivate!=null&&m.hasOwnProperty("isPrivate")){if(typeof m.isPrivate!=="boolean")return"isPrivate: boolean expected"}if(m.JoinPassword!=null&&m.hasOwnProperty("JoinPassword")){if(!$util.isString(m.JoinPassword))return"JoinPassword: string expected"}if(m.buyInPassword!=null&&m.hasOwnProperty("buyInPassword")){if(!$util.isString(m.buyInPassword))return"buyInPassword: string expected"}if(m.plats!=null&&m.hasOwnProperty("plats")){if(!Array.isArray(m.plats))return"plats: array expected";for(var i=0;i<m.plats.length;++i){if(!$util.isInteger(m.plats[i]))return"plats: integer[] expected"}}return null};RoomParam.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.RoomParam)return d;var m=new $root.jackfruit_proto.RoomParam;if(d.ownerType!=null){m.ownerType=d.ownerType|0}if(d.gameMode!=null){m.gameMode=d.gameMode|0}if(d.playerCountMax!=null){m.playerCountMax=d.playerCountMax|0}if(d.gameName!=null){m.gameName=String(d.gameName)}if(d.ante!=null){if($util.Long)(m.ante=$util.Long.fromValue(d.ante)).unsigned=false;else if(typeof d.ante==="string")m.ante=parseInt(d.ante,10);else if(typeof d.ante==="number")m.ante=d.ante;else if(typeof d.ante==="object")m.ante=new $util.LongBits(d.ante.low>>>0,d.ante.high>>>0).toNumber()}if(d.manualCreated!=null){m.manualCreated=d.manualCreated|0}if(d.minimumAmount!=null){m.minimumAmount=d.minimumAmount|0}if(d.ruleCheckAmount!=null){if($util.Long)(m.ruleCheckAmount=$util.Long.fromValue(d.ruleCheckAmount)).unsigned=false;else if(typeof d.ruleCheckAmount==="string")m.ruleCheckAmount=parseInt(d.ruleCheckAmount,10);else if(typeof d.ruleCheckAmount==="number")m.ruleCheckAmount=d.ruleCheckAmount;else if(typeof d.ruleCheckAmount==="object")m.ruleCheckAmount=new $util.LongBits(d.ruleCheckAmount.low>>>0,d.ruleCheckAmount.high>>>0).toNumber()}if(d.ruleAddToAmount!=null){if($util.Long)(m.ruleAddToAmount=$util.Long.fromValue(d.ruleAddToAmount)).unsigned=false;else if(typeof d.ruleAddToAmount==="string")m.ruleAddToAmount=parseInt(d.ruleAddToAmount,10);else if(typeof d.ruleAddToAmount==="number")m.ruleAddToAmount=d.ruleAddToAmount;else if(typeof d.ruleAddToAmount==="object")m.ruleAddToAmount=new $util.LongBits(d.ruleAddToAmount.low>>>0,d.ruleAddToAmount.high>>>0).toNumber()}if(d.zoneMultiple){if(!Array.isArray(d.zoneMultiple))throw TypeError(".jackfruit_proto.RoomParam.zoneMultiple: array expected");m.zoneMultiple=[];for(var i=0;i<d.zoneMultiple.length;++i){m.zoneMultiple[i]=d.zoneMultiple[i]>>>0}}if(d.createTime!=null){if($util.Long)(m.createTime=$util.Long.fromValue(d.createTime)).unsigned=false;else if(typeof d.createTime==="string")m.createTime=parseInt(d.createTime,10);else if(typeof d.createTime==="number")m.createTime=d.createTime;else if(typeof d.createTime==="object")m.createTime=new $util.LongBits(d.createTime.low>>>0,d.createTime.high>>>0).toNumber()}if(d.limitPlayers!=null){m.limitPlayers=d.limitPlayers>>>0}if(d.creatorId!=null){m.creatorId=d.creatorId>>>0}if(d.creatorName!=null){m.creatorName=String(d.creatorName)}if(d.gameTimeLimit!=null){if($util.Long)(m.gameTimeLimit=$util.Long.fromValue(d.gameTimeLimit)).unsigned=false;else if(typeof d.gameTimeLimit==="string")m.gameTimeLimit=parseInt(d.gameTimeLimit,10);else if(typeof d.gameTimeLimit==="number")m.gameTimeLimit=d.gameTimeLimit;else if(typeof d.gameTimeLimit==="object")m.gameTimeLimit=new $util.LongBits(d.gameTimeLimit.low>>>0,d.gameTimeLimit.high>>>0).toNumber()}if(d.idleSecs!=null){if($util.Long)(m.idleSecs=$util.Long.fromValue(d.idleSecs)).unsigned=false;else if(typeof d.idleSecs==="string")m.idleSecs=parseInt(d.idleSecs,10);else if(typeof d.idleSecs==="number")m.idleSecs=d.idleSecs;else if(typeof d.idleSecs==="object")m.idleSecs=new $util.LongBits(d.idleSecs.low>>>0,d.idleSecs.high>>>0).toNumber()}if(d.ruleCheckScore!=null){if($util.Long)(m.ruleCheckScore=$util.Long.fromValue(d.ruleCheckScore)).unsigned=false;else if(typeof d.ruleCheckScore==="string")m.ruleCheckScore=parseInt(d.ruleCheckScore,10);else if(typeof d.ruleCheckScore==="number")m.ruleCheckScore=d.ruleCheckScore;else if(typeof d.ruleCheckScore==="object")m.ruleCheckScore=new $util.LongBits(d.ruleCheckScore.low>>>0,d.ruleCheckScore.high>>>0).toNumber()}if(d.ruleAddToScore!=null){if($util.Long)(m.ruleAddToScore=$util.Long.fromValue(d.ruleAddToScore)).unsigned=false;else if(typeof d.ruleAddToScore==="string")m.ruleAddToScore=parseInt(d.ruleAddToScore,10);else if(typeof d.ruleAddToScore==="number")m.ruleAddToScore=d.ruleAddToScore;else if(typeof d.ruleAddToScore==="object")m.ruleAddToScore=new $util.LongBits(d.ruleAddToScore.low>>>0,d.ruleAddToScore.high>>>0).toNumber()}if(d.ruleServeScore!=null){if($util.Long)(m.ruleServeScore=$util.Long.fromValue(d.ruleServeScore)).unsigned=false;else if(typeof d.ruleServeScore==="string")m.ruleServeScore=parseInt(d.ruleServeScore,10);else if(typeof d.ruleServeScore==="number")m.ruleServeScore=d.ruleServeScore;else if(typeof d.ruleServeScore==="object")m.ruleServeScore=new $util.LongBits(d.ruleServeScore.low>>>0,d.ruleServeScore.high>>>0).toNumber()}if(d.ownerClubName!=null){m.ownerClubName=String(d.ownerClubName)}if(d.clubHead!=null){m.clubHead=String(d.clubHead)}if(d.isBanVpn!=null){m.isBanVpn=Boolean(d.isBanVpn)}if(d.showForClients){if(!Array.isArray(d.showForClients))throw TypeError(".jackfruit_proto.RoomParam.showForClients: array expected");m.showForClients=[];for(var i=0;i<d.showForClients.length;++i){m.showForClients[i]=d.showForClients[i]>>>0}}if(d.clubId!=null){m.clubId=d.clubId>>>0}if(d.allianceIds){if(!Array.isArray(d.allianceIds))throw TypeError(".jackfruit_proto.RoomParam.allianceIds: array expected");m.allianceIds=[];for(var i=0;i<d.allianceIds.length;++i){m.allianceIds[i]=d.allianceIds[i]>>>0}}if(d.ruleSwitchRandomSeat!=null){m.ruleSwitchRandomSeat=d.ruleSwitchRandomSeat|0}if(d.deskType!=null){m.deskType=d.deskType|0}if(d.autoStartNum!=null){m.autoStartNum=d.autoStartNum|0}if(d.isPrivate!=null){m.isPrivate=Boolean(d.isPrivate)}if(d.JoinPassword!=null){m.JoinPassword=String(d.JoinPassword)}if(d.buyInPassword!=null){m.buyInPassword=String(d.buyInPassword)}if(d.plats){if(!Array.isArray(d.plats))throw TypeError(".jackfruit_proto.RoomParam.plats: array expected");m.plats=[];for(var i=0;i<d.plats.length;++i){m.plats[i]=d.plats[i]>>>0}}return m};RoomParam.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.allianceIds=[];d.zoneMultiple=[];d.plats=[];d.showForClients=[]}if(o.defaults){d.ownerType=0;d.gameMode=0;d.playerCountMax=0;if($util.Long){var n=new $util.Long(0,0,false);d.ante=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.ante=o.longs===String?"0":0;d.gameName="";d.clubId=0;d.ruleSwitchRandomSeat=0;d.manualCreated=0;d.minimumAmount=0;d.deskType=0;if($util.Long){var n=new $util.Long(0,0,false);d.ruleAddToAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.ruleAddToAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.createTime=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.createTime=o.longs===String?"0":0;d.autoStartNum=0;d.isPrivate=false;d.creatorId=0;d.limitPlayers=0;d.JoinPassword="";d.creatorName="";if($util.Long){var n=new $util.Long(0,0,false);d.gameTimeLimit=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.gameTimeLimit=o.longs===String?"0":0;d.ownerClubName="";d.clubHead="";d.buyInPassword="";if($util.Long){var n=new $util.Long(0,0,false);d.ruleCheckAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.ruleCheckAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.idleSecs=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.idleSecs=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.ruleCheckScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.ruleCheckScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.ruleAddToScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.ruleAddToScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.ruleServeScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.ruleServeScore=o.longs===String?"0":0;d.isBanVpn=false}if(m.ownerType!=null&&m.hasOwnProperty("ownerType")){d.ownerType=m.ownerType}if(m.gameMode!=null&&m.hasOwnProperty("gameMode")){d.gameMode=m.gameMode}if(m.playerCountMax!=null&&m.hasOwnProperty("playerCountMax")){d.playerCountMax=m.playerCountMax}if(m.ante!=null&&m.hasOwnProperty("ante")){if(typeof m.ante==="number")d.ante=o.longs===String?String(m.ante):m.ante;else d.ante=o.longs===String?$util.Long.prototype.toString.call(m.ante):o.longs===Number?new $util.LongBits(m.ante.low>>>0,m.ante.high>>>0).toNumber():m.ante}if(m.gameName!=null&&m.hasOwnProperty("gameName")){d.gameName=m.gameName}if(m.clubId!=null&&m.hasOwnProperty("clubId")){d.clubId=m.clubId}if(m.allianceIds&&m.allianceIds.length){d.allianceIds=[];for(var j=0;j<m.allianceIds.length;++j){d.allianceIds[j]=m.allianceIds[j]}}if(m.ruleSwitchRandomSeat!=null&&m.hasOwnProperty("ruleSwitchRandomSeat")){d.ruleSwitchRandomSeat=m.ruleSwitchRandomSeat}if(m.manualCreated!=null&&m.hasOwnProperty("manualCreated")){d.manualCreated=m.manualCreated}if(m.minimumAmount!=null&&m.hasOwnProperty("minimumAmount")){d.minimumAmount=m.minimumAmount}if(m.deskType!=null&&m.hasOwnProperty("deskType")){d.deskType=m.deskType}if(m.ruleAddToAmount!=null&&m.hasOwnProperty("ruleAddToAmount")){if(typeof m.ruleAddToAmount==="number")d.ruleAddToAmount=o.longs===String?String(m.ruleAddToAmount):m.ruleAddToAmount;else d.ruleAddToAmount=o.longs===String?$util.Long.prototype.toString.call(m.ruleAddToAmount):o.longs===Number?new $util.LongBits(m.ruleAddToAmount.low>>>0,m.ruleAddToAmount.high>>>0).toNumber():m.ruleAddToAmount}if(m.zoneMultiple&&m.zoneMultiple.length){d.zoneMultiple=[];for(var j=0;j<m.zoneMultiple.length;++j){d.zoneMultiple[j]=m.zoneMultiple[j]}}if(m.createTime!=null&&m.hasOwnProperty("createTime")){if(typeof m.createTime==="number")d.createTime=o.longs===String?String(m.createTime):m.createTime;else d.createTime=o.longs===String?$util.Long.prototype.toString.call(m.createTime):o.longs===Number?new $util.LongBits(m.createTime.low>>>0,m.createTime.high>>>0).toNumber():m.createTime}if(m.autoStartNum!=null&&m.hasOwnProperty("autoStartNum")){d.autoStartNum=m.autoStartNum}if(m.isPrivate!=null&&m.hasOwnProperty("isPrivate")){d.isPrivate=m.isPrivate}if(m.creatorId!=null&&m.hasOwnProperty("creatorId")){d.creatorId=m.creatorId}if(m.limitPlayers!=null&&m.hasOwnProperty("limitPlayers")){d.limitPlayers=m.limitPlayers}if(m.JoinPassword!=null&&m.hasOwnProperty("JoinPassword")){d.JoinPassword=m.JoinPassword}if(m.creatorName!=null&&m.hasOwnProperty("creatorName")){d.creatorName=m.creatorName}if(m.gameTimeLimit!=null&&m.hasOwnProperty("gameTimeLimit")){if(typeof m.gameTimeLimit==="number")d.gameTimeLimit=o.longs===String?String(m.gameTimeLimit):m.gameTimeLimit;else d.gameTimeLimit=o.longs===String?$util.Long.prototype.toString.call(m.gameTimeLimit):o.longs===Number?new $util.LongBits(m.gameTimeLimit.low>>>0,m.gameTimeLimit.high>>>0).toNumber():m.gameTimeLimit}if(m.ownerClubName!=null&&m.hasOwnProperty("ownerClubName")){d.ownerClubName=m.ownerClubName}if(m.clubHead!=null&&m.hasOwnProperty("clubHead")){d.clubHead=m.clubHead}if(m.buyInPassword!=null&&m.hasOwnProperty("buyInPassword")){d.buyInPassword=m.buyInPassword}if(m.ruleCheckAmount!=null&&m.hasOwnProperty("ruleCheckAmount")){if(typeof m.ruleCheckAmount==="number")d.ruleCheckAmount=o.longs===String?String(m.ruleCheckAmount):m.ruleCheckAmount;else d.ruleCheckAmount=o.longs===String?$util.Long.prototype.toString.call(m.ruleCheckAmount):o.longs===Number?new $util.LongBits(m.ruleCheckAmount.low>>>0,m.ruleCheckAmount.high>>>0).toNumber():m.ruleCheckAmount}if(m.idleSecs!=null&&m.hasOwnProperty("idleSecs")){if(typeof m.idleSecs==="number")d.idleSecs=o.longs===String?String(m.idleSecs):m.idleSecs;else d.idleSecs=o.longs===String?$util.Long.prototype.toString.call(m.idleSecs):o.longs===Number?new $util.LongBits(m.idleSecs.low>>>0,m.idleSecs.high>>>0).toNumber():m.idleSecs}if(m.ruleCheckScore!=null&&m.hasOwnProperty("ruleCheckScore")){if(typeof m.ruleCheckScore==="number")d.ruleCheckScore=o.longs===String?String(m.ruleCheckScore):m.ruleCheckScore;else d.ruleCheckScore=o.longs===String?$util.Long.prototype.toString.call(m.ruleCheckScore):o.longs===Number?new $util.LongBits(m.ruleCheckScore.low>>>0,m.ruleCheckScore.high>>>0).toNumber():m.ruleCheckScore}if(m.ruleAddToScore!=null&&m.hasOwnProperty("ruleAddToScore")){if(typeof m.ruleAddToScore==="number")d.ruleAddToScore=o.longs===String?String(m.ruleAddToScore):m.ruleAddToScore;else d.ruleAddToScore=o.longs===String?$util.Long.prototype.toString.call(m.ruleAddToScore):o.longs===Number?new $util.LongBits(m.ruleAddToScore.low>>>0,m.ruleAddToScore.high>>>0).toNumber():m.ruleAddToScore}if(m.ruleServeScore!=null&&m.hasOwnProperty("ruleServeScore")){if(typeof m.ruleServeScore==="number")d.ruleServeScore=o.longs===String?String(m.ruleServeScore):m.ruleServeScore;else d.ruleServeScore=o.longs===String?$util.Long.prototype.toString.call(m.ruleServeScore):o.longs===Number?new $util.LongBits(m.ruleServeScore.low>>>0,m.ruleServeScore.high>>>0).toNumber():m.ruleServeScore}if(m.plats&&m.plats.length){d.plats=[];for(var j=0;j<m.plats.length;++j){d.plats[j]=m.plats[j]}}if(m.isBanVpn!=null&&m.hasOwnProperty("isBanVpn")){d.isBanVpn=m.isBanVpn}if(m.showForClients&&m.showForClients.length){d.showForClients=[];for(var j=0;j<m.showForClients.length;++j){d.showForClients[j]=m.showForClients[j]}}return d};RoomParam.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomParam}();jackfruit_proto.PlayerInfo=function(){function PlayerInfo(p){this.holeCards=[];this.headCards=[];this.middleCards=[];this.tailCards=[];this.NotDisturbUids=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerInfo.prototype.playerId=0;PlayerInfo.prototype.seatId=0;PlayerInfo.prototype.name="";PlayerInfo.prototype.headUrl="";PlayerInfo.prototype.marks="";PlayerInfo.prototype.gender=0;PlayerInfo.prototype.lastVoice="";PlayerInfo.prototype.amount=0;PlayerInfo.prototype.state=0;PlayerInfo.prototype.holeCards=$util.emptyArray;PlayerInfo.prototype.headCards=$util.emptyArray;PlayerInfo.prototype.middleCards=$util.emptyArray;PlayerInfo.prototype.tailCards=$util.emptyArray;PlayerInfo.prototype.settleScore=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerInfo.prototype.settleAmount=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerInfo.prototype.score=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerInfo.prototype.plat=0;PlayerInfo.prototype.is_online=false;PlayerInfo.prototype.user_join_room_time=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerInfo.prototype.NotDisturbUids=$util.emptyArray;PlayerInfo.prototype.lastChangeVoice=0;PlayerInfo.create=function create(properties){return new PlayerInfo(properties)};PlayerInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(8).uint32(m.playerId);if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(16).int32(m.seatId);if(m.name!=null&&Object.hasOwnProperty.call(m,"name"))w.uint32(26).string(m.name);if(m.headUrl!=null&&Object.hasOwnProperty.call(m,"headUrl"))w.uint32(34).string(m.headUrl);if(m.marks!=null&&Object.hasOwnProperty.call(m,"marks"))w.uint32(42).string(m.marks);if(m.gender!=null&&Object.hasOwnProperty.call(m,"gender"))w.uint32(48).int32(m.gender);if(m.lastVoice!=null&&Object.hasOwnProperty.call(m,"lastVoice"))w.uint32(66).string(m.lastVoice);if(m.amount!=null&&Object.hasOwnProperty.call(m,"amount"))w.uint32(72).uint32(m.amount);if(m.state!=null&&Object.hasOwnProperty.call(m,"state"))w.uint32(80).int32(m.state);if(m.holeCards!=null&&m.holeCards.length){for(var i=0;i<m.holeCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.holeCards[i],w.uint32(90).fork()).ldelim()}if(m.headCards!=null&&m.headCards.length){for(var i=0;i<m.headCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.headCards[i],w.uint32(98).fork()).ldelim()}if(m.middleCards!=null&&m.middleCards.length){for(var i=0;i<m.middleCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.middleCards[i],w.uint32(106).fork()).ldelim()}if(m.tailCards!=null&&m.tailCards.length){for(var i=0;i<m.tailCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.tailCards[i],w.uint32(114).fork()).ldelim()}if(m.settleScore!=null&&Object.hasOwnProperty.call(m,"settleScore"))w.uint32(120).int64(m.settleScore);if(m.settleAmount!=null&&Object.hasOwnProperty.call(m,"settleAmount"))w.uint32(128).int64(m.settleAmount);if(m.score!=null&&Object.hasOwnProperty.call(m,"score"))w.uint32(136).int64(m.score);if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(144).uint32(m.plat);if(m.is_online!=null&&Object.hasOwnProperty.call(m,"is_online"))w.uint32(152).bool(m.is_online);if(m.user_join_room_time!=null&&Object.hasOwnProperty.call(m,"user_join_room_time"))w.uint32(160).int64(m.user_join_room_time);if(m.NotDisturbUids!=null&&m.NotDisturbUids.length){w.uint32(194).fork();for(var i=0;i<m.NotDisturbUids.length;++i)w.uint32(m.NotDisturbUids[i]);w.ldelim()}if(m.lastChangeVoice!=null&&Object.hasOwnProperty.call(m,"lastChangeVoice"))w.uint32(200).int32(m.lastChangeVoice);return w};PlayerInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlayerInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.playerId=r.uint32();break;case 2:m.seatId=r.int32();break;case 3:m.name=r.string();break;case 4:m.headUrl=r.string();break;case 5:m.marks=r.string();break;case 6:m.gender=r.int32();break;case 8:m.lastVoice=r.string();break;case 9:m.amount=r.uint32();break;case 10:m.state=r.int32();break;case 11:if(!(m.holeCards&&m.holeCards.length))m.holeCards=[];m.holeCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 12:if(!(m.headCards&&m.headCards.length))m.headCards=[];m.headCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 13:if(!(m.middleCards&&m.middleCards.length))m.middleCards=[];m.middleCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 14:if(!(m.tailCards&&m.tailCards.length))m.tailCards=[];m.tailCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 15:m.settleScore=r.int64();break;case 16:m.settleAmount=r.int64();break;case 17:m.score=r.int64();break;case 18:m.plat=r.uint32();break;case 19:m.is_online=r.bool();break;case 20:m.user_join_room_time=r.int64();break;case 24:if(!(m.NotDisturbUids&&m.NotDisturbUids.length))m.NotDisturbUids=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.NotDisturbUids.push(r.uint32())}else m.NotDisturbUids.push(r.uint32());break;case 25:m.lastChangeVoice=r.int32();break;default:r.skipType(t&7);break}}return m};PlayerInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}if(m.name!=null&&m.hasOwnProperty("name")){if(!$util.isString(m.name))return"name: string expected"}if(m.headUrl!=null&&m.hasOwnProperty("headUrl")){if(!$util.isString(m.headUrl))return"headUrl: string expected"}if(m.marks!=null&&m.hasOwnProperty("marks")){if(!$util.isString(m.marks))return"marks: string expected"}if(m.gender!=null&&m.hasOwnProperty("gender")){if(!$util.isInteger(m.gender))return"gender: integer expected"}if(m.lastVoice!=null&&m.hasOwnProperty("lastVoice")){if(!$util.isString(m.lastVoice))return"lastVoice: string expected"}if(m.amount!=null&&m.hasOwnProperty("amount")){if(!$util.isInteger(m.amount))return"amount: integer expected"}if(m.state!=null&&m.hasOwnProperty("state")){switch(m.state){default:return"state: enum value expected";case 0:case 1:case 2:case 8:case 11:case 13:case 14:case 15:case 20:break}}if(m.holeCards!=null&&m.hasOwnProperty("holeCards")){if(!Array.isArray(m.holeCards))return"holeCards: array expected";for(var i=0;i<m.holeCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.holeCards[i]);if(e)return"holeCards."+e}}}if(m.headCards!=null&&m.hasOwnProperty("headCards")){if(!Array.isArray(m.headCards))return"headCards: array expected";for(var i=0;i<m.headCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.headCards[i]);if(e)return"headCards."+e}}}if(m.middleCards!=null&&m.hasOwnProperty("middleCards")){if(!Array.isArray(m.middleCards))return"middleCards: array expected";for(var i=0;i<m.middleCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.middleCards[i]);if(e)return"middleCards."+e}}}if(m.tailCards!=null&&m.hasOwnProperty("tailCards")){if(!Array.isArray(m.tailCards))return"tailCards: array expected";for(var i=0;i<m.tailCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.tailCards[i]);if(e)return"tailCards."+e}}}if(m.settleScore!=null&&m.hasOwnProperty("settleScore")){if(!$util.isInteger(m.settleScore)&&!(m.settleScore&&$util.isInteger(m.settleScore.low)&&$util.isInteger(m.settleScore.high)))return"settleScore: integer|Long expected"}if(m.settleAmount!=null&&m.hasOwnProperty("settleAmount")){if(!$util.isInteger(m.settleAmount)&&!(m.settleAmount&&$util.isInteger(m.settleAmount.low)&&$util.isInteger(m.settleAmount.high)))return"settleAmount: integer|Long expected"}if(m.score!=null&&m.hasOwnProperty("score")){if(!$util.isInteger(m.score)&&!(m.score&&$util.isInteger(m.score.low)&&$util.isInteger(m.score.high)))return"score: integer|Long expected"}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}if(m.is_online!=null&&m.hasOwnProperty("is_online")){if(typeof m.is_online!=="boolean")return"is_online: boolean expected"}if(m.user_join_room_time!=null&&m.hasOwnProperty("user_join_room_time")){if(!$util.isInteger(m.user_join_room_time)&&!(m.user_join_room_time&&$util.isInteger(m.user_join_room_time.low)&&$util.isInteger(m.user_join_room_time.high)))return"user_join_room_time: integer|Long expected"}if(m.NotDisturbUids!=null&&m.hasOwnProperty("NotDisturbUids")){if(!Array.isArray(m.NotDisturbUids))return"NotDisturbUids: array expected";for(var i=0;i<m.NotDisturbUids.length;++i){if(!$util.isInteger(m.NotDisturbUids[i]))return"NotDisturbUids: integer[] expected"}}if(m.lastChangeVoice!=null&&m.hasOwnProperty("lastChangeVoice")){if(!$util.isInteger(m.lastChangeVoice))return"lastChangeVoice: integer expected"}return null};PlayerInfo.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlayerInfo)return d;var m=new $root.jackfruit_proto.PlayerInfo;if(d.playerId!=null){m.playerId=d.playerId>>>0}if(d.seatId!=null){m.seatId=d.seatId|0}if(d.name!=null){m.name=String(d.name)}if(d.headUrl!=null){m.headUrl=String(d.headUrl)}if(d.marks!=null){m.marks=String(d.marks)}if(d.gender!=null){m.gender=d.gender|0}if(d.lastVoice!=null){m.lastVoice=String(d.lastVoice)}if(d.amount!=null){m.amount=d.amount>>>0}switch(d.state){case"SeatState_DUMMY":case 0:m.state=0;break;case"SFree":case 1:m.state=1;break;case"SReady":case 2:m.state=2;break;case"SClickReady":case 8:m.state=8;break;case"SWaitPlaceCards":case 11:m.state=11;break;case"SPlaceCards":case 13:m.state=13;break;case"SModifyPlaceCards":case 14:m.state=14;break;case"SConfirmsPlaceCards":case 15:m.state=15;break;case"SWaitResult":case 20:m.state=20;break}if(d.holeCards){if(!Array.isArray(d.holeCards))throw TypeError(".jackfruit_proto.PlayerInfo.holeCards: array expected");m.holeCards=[];for(var i=0;i<d.holeCards.length;++i){if(typeof d.holeCards[i]!=="object")throw TypeError(".jackfruit_proto.PlayerInfo.holeCards: object expected");m.holeCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.holeCards[i])}}if(d.headCards){if(!Array.isArray(d.headCards))throw TypeError(".jackfruit_proto.PlayerInfo.headCards: array expected");m.headCards=[];for(var i=0;i<d.headCards.length;++i){if(typeof d.headCards[i]!=="object")throw TypeError(".jackfruit_proto.PlayerInfo.headCards: object expected");m.headCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.headCards[i])}}if(d.middleCards){if(!Array.isArray(d.middleCards))throw TypeError(".jackfruit_proto.PlayerInfo.middleCards: array expected");m.middleCards=[];for(var i=0;i<d.middleCards.length;++i){if(typeof d.middleCards[i]!=="object")throw TypeError(".jackfruit_proto.PlayerInfo.middleCards: object expected");m.middleCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.middleCards[i])}}if(d.tailCards){if(!Array.isArray(d.tailCards))throw TypeError(".jackfruit_proto.PlayerInfo.tailCards: array expected");m.tailCards=[];for(var i=0;i<d.tailCards.length;++i){if(typeof d.tailCards[i]!=="object")throw TypeError(".jackfruit_proto.PlayerInfo.tailCards: object expected");m.tailCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.tailCards[i])}}if(d.settleScore!=null){if($util.Long)(m.settleScore=$util.Long.fromValue(d.settleScore)).unsigned=false;else if(typeof d.settleScore==="string")m.settleScore=parseInt(d.settleScore,10);else if(typeof d.settleScore==="number")m.settleScore=d.settleScore;else if(typeof d.settleScore==="object")m.settleScore=new $util.LongBits(d.settleScore.low>>>0,d.settleScore.high>>>0).toNumber()}if(d.settleAmount!=null){if($util.Long)(m.settleAmount=$util.Long.fromValue(d.settleAmount)).unsigned=false;else if(typeof d.settleAmount==="string")m.settleAmount=parseInt(d.settleAmount,10);else if(typeof d.settleAmount==="number")m.settleAmount=d.settleAmount;else if(typeof d.settleAmount==="object")m.settleAmount=new $util.LongBits(d.settleAmount.low>>>0,d.settleAmount.high>>>0).toNumber()}if(d.score!=null){if($util.Long)(m.score=$util.Long.fromValue(d.score)).unsigned=false;else if(typeof d.score==="string")m.score=parseInt(d.score,10);else if(typeof d.score==="number")m.score=d.score;else if(typeof d.score==="object")m.score=new $util.LongBits(d.score.low>>>0,d.score.high>>>0).toNumber()}if(d.plat!=null){m.plat=d.plat>>>0}if(d.is_online!=null){m.is_online=Boolean(d.is_online)}if(d.user_join_room_time!=null){if($util.Long)(m.user_join_room_time=$util.Long.fromValue(d.user_join_room_time)).unsigned=false;else if(typeof d.user_join_room_time==="string")m.user_join_room_time=parseInt(d.user_join_room_time,10);else if(typeof d.user_join_room_time==="number")m.user_join_room_time=d.user_join_room_time;else if(typeof d.user_join_room_time==="object")m.user_join_room_time=new $util.LongBits(d.user_join_room_time.low>>>0,d.user_join_room_time.high>>>0).toNumber()}if(d.NotDisturbUids){if(!Array.isArray(d.NotDisturbUids))throw TypeError(".jackfruit_proto.PlayerInfo.NotDisturbUids: array expected");m.NotDisturbUids=[];for(var i=0;i<d.NotDisturbUids.length;++i){m.NotDisturbUids[i]=d.NotDisturbUids[i]>>>0}}if(d.lastChangeVoice!=null){m.lastChangeVoice=d.lastChangeVoice|0}return m};PlayerInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.holeCards=[];d.headCards=[];d.middleCards=[];d.tailCards=[];d.NotDisturbUids=[]}if(o.defaults){d.playerId=0;d.seatId=0;d.name="";d.headUrl="";d.marks="";d.gender=0;d.lastVoice="";d.amount=0;d.state=o.enums===String?"SeatState_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.settleScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.settleScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.settleAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.settleAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.score=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.score=o.longs===String?"0":0;d.plat=0;d.is_online=false;if($util.Long){var n=new $util.Long(0,0,false);d.user_join_room_time=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.user_join_room_time=o.longs===String?"0":0;d.lastChangeVoice=0}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}if(m.name!=null&&m.hasOwnProperty("name")){d.name=m.name}if(m.headUrl!=null&&m.hasOwnProperty("headUrl")){d.headUrl=m.headUrl}if(m.marks!=null&&m.hasOwnProperty("marks")){d.marks=m.marks}if(m.gender!=null&&m.hasOwnProperty("gender")){d.gender=m.gender}if(m.lastVoice!=null&&m.hasOwnProperty("lastVoice")){d.lastVoice=m.lastVoice}if(m.amount!=null&&m.hasOwnProperty("amount")){d.amount=m.amount}if(m.state!=null&&m.hasOwnProperty("state")){d.state=o.enums===String?$root.jackfruit_proto.PlayerState[m.state]:m.state}if(m.holeCards&&m.holeCards.length){d.holeCards=[];for(var j=0;j<m.holeCards.length;++j){d.holeCards[j]=$root.jackfruit_proto.CardItem.toObject(m.holeCards[j],o)}}if(m.headCards&&m.headCards.length){d.headCards=[];for(var j=0;j<m.headCards.length;++j){d.headCards[j]=$root.jackfruit_proto.CardItem.toObject(m.headCards[j],o)}}if(m.middleCards&&m.middleCards.length){d.middleCards=[];for(var j=0;j<m.middleCards.length;++j){d.middleCards[j]=$root.jackfruit_proto.CardItem.toObject(m.middleCards[j],o)}}if(m.tailCards&&m.tailCards.length){d.tailCards=[];for(var j=0;j<m.tailCards.length;++j){d.tailCards[j]=$root.jackfruit_proto.CardItem.toObject(m.tailCards[j],o)}}if(m.settleScore!=null&&m.hasOwnProperty("settleScore")){if(typeof m.settleScore==="number")d.settleScore=o.longs===String?String(m.settleScore):m.settleScore;else d.settleScore=o.longs===String?$util.Long.prototype.toString.call(m.settleScore):o.longs===Number?new $util.LongBits(m.settleScore.low>>>0,m.settleScore.high>>>0).toNumber():m.settleScore}if(m.settleAmount!=null&&m.hasOwnProperty("settleAmount")){if(typeof m.settleAmount==="number")d.settleAmount=o.longs===String?String(m.settleAmount):m.settleAmount;else d.settleAmount=o.longs===String?$util.Long.prototype.toString.call(m.settleAmount):o.longs===Number?new $util.LongBits(m.settleAmount.low>>>0,m.settleAmount.high>>>0).toNumber():m.settleAmount}if(m.score!=null&&m.hasOwnProperty("score")){if(typeof m.score==="number")d.score=o.longs===String?String(m.score):m.score;else d.score=o.longs===String?$util.Long.prototype.toString.call(m.score):o.longs===Number?new $util.LongBits(m.score.low>>>0,m.score.high>>>0).toNumber():m.score}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}if(m.is_online!=null&&m.hasOwnProperty("is_online")){d.is_online=m.is_online}if(m.user_join_room_time!=null&&m.hasOwnProperty("user_join_room_time")){if(typeof m.user_join_room_time==="number")d.user_join_room_time=o.longs===String?String(m.user_join_room_time):m.user_join_room_time;else d.user_join_room_time=o.longs===String?$util.Long.prototype.toString.call(m.user_join_room_time):o.longs===Number?new $util.LongBits(m.user_join_room_time.low>>>0,m.user_join_room_time.high>>>0).toNumber():m.user_join_room_time}if(m.NotDisturbUids&&m.NotDisturbUids.length){d.NotDisturbUids=[];for(var j=0;j<m.NotDisturbUids.length;++j){d.NotDisturbUids[j]=m.NotDisturbUids[j]}}if(m.lastChangeVoice!=null&&m.hasOwnProperty("lastChangeVoice")){d.lastChangeVoice=m.lastChangeVoice}return d};PlayerInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerInfo}();jackfruit_proto.LoginReq=function(){function LoginReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LoginReq.prototype.version="";LoginReq.prototype.token="";LoginReq.prototype.clientType=0;LoginReq.create=function create(properties){return new LoginReq(properties)};LoginReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.version!=null&&Object.hasOwnProperty.call(m,"version"))w.uint32(10).string(m.version);if(m.token!=null&&Object.hasOwnProperty.call(m,"token"))w.uint32(18).string(m.token);if(m.clientType!=null&&Object.hasOwnProperty.call(m,"clientType"))w.uint32(24).int32(m.clientType);return w};LoginReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LoginReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.LoginReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.version=r.string();break;case 2:m.token=r.string();break;case 3:m.clientType=r.int32();break;default:r.skipType(t&7);break}}return m};LoginReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LoginReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.version!=null&&m.hasOwnProperty("version")){if(!$util.isString(m.version))return"version: string expected"}if(m.token!=null&&m.hasOwnProperty("token")){if(!$util.isString(m.token))return"token: string expected"}if(m.clientType!=null&&m.hasOwnProperty("clientType")){switch(m.clientType){default:return"clientType: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:break}}return null};LoginReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.LoginReq)return d;var m=new $root.jackfruit_proto.LoginReq;if(d.version!=null){m.version=String(d.version)}if(d.token!=null){m.token=String(d.token)}switch(d.clientType){case"Dummy":case 0:m.clientType=0;break;case"Normal":case 1:m.clientType=1;break;case"OverSeas":case 2:m.clientType=2;break;case"H5":case 3:m.clientType=3;break;case"H5OverSeas":case 4:m.clientType=4;break;case"H5Web":case 5:m.clientType=5;break;case"H5WebOverSeas":case 6:m.clientType=6;break;case"H5VietnamLasted":case 7:m.clientType=7;break;case"H5WebVietnamLasted":case 8:m.clientType=8;break}return m};LoginReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.version="";d.token="";d.clientType=o.enums===String?"Dummy":0}if(m.version!=null&&m.hasOwnProperty("version")){d.version=m.version}if(m.token!=null&&m.hasOwnProperty("token")){d.token=m.token}if(m.clientType!=null&&m.hasOwnProperty("clientType")){d.clientType=o.enums===String?$root.jackfruit_proto.ClientType[m.clientType]:m.clientType}return d};LoginReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LoginReq}();jackfruit_proto.LoginResp=function(){function LoginResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LoginResp.prototype.code=0;LoginResp.prototype.roomId=0;LoginResp.create=function create(properties){return new LoginResp(properties)};LoginResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(16).uint32(m.roomId);return w};LoginResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LoginResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.LoginResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.roomId=r.uint32();break;default:r.skipType(t&7);break}}return m};LoginResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LoginResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}return null};LoginResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.LoginResp)return d;var m=new $root.jackfruit_proto.LoginResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.roomId!=null){m.roomId=d.roomId>>>0}return m};LoginResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.roomId=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}return d};LoginResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LoginResp}();jackfruit_proto.JoinRoomReq=function(){function JoinRoomReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JoinRoomReq.prototype.roomId=0;JoinRoomReq.create=function create(properties){return new JoinRoomReq(properties)};JoinRoomReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).uint32(m.roomId);return w};JoinRoomReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JoinRoomReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JoinRoomReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.uint32();break;default:r.skipType(t&7);break}}return m};JoinRoomReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JoinRoomReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}return null};JoinRoomReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JoinRoomReq)return d;var m=new $root.jackfruit_proto.JoinRoomReq;if(d.roomId!=null){m.roomId=d.roomId>>>0}return m};JoinRoomReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}return d};JoinRoomReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JoinRoomReq}();jackfruit_proto.JoinRoomResp=function(){function JoinRoomResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JoinRoomResp.prototype.code=0;JoinRoomResp.prototype.roomId=0;JoinRoomResp.prototype.roomuuid=$util.Long?$util.Long.fromBits(0,0,true):0;JoinRoomResp.create=function create(properties){return new JoinRoomResp(properties)};JoinRoomResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(16).uint32(m.roomId);if(m.roomuuid!=null&&Object.hasOwnProperty.call(m,"roomuuid"))w.uint32(24).uint64(m.roomuuid);return w};JoinRoomResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JoinRoomResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JoinRoomResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.roomId=r.uint32();break;case 3:m.roomuuid=r.uint64();break;default:r.skipType(t&7);break}}return m};JoinRoomResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JoinRoomResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(!$util.isInteger(m.roomuuid)&&!(m.roomuuid&&$util.isInteger(m.roomuuid.low)&&$util.isInteger(m.roomuuid.high)))return"roomuuid: integer|Long expected"}return null};JoinRoomResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JoinRoomResp)return d;var m=new $root.jackfruit_proto.JoinRoomResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.roomId!=null){m.roomId=d.roomId>>>0}if(d.roomuuid!=null){if($util.Long)(m.roomuuid=$util.Long.fromValue(d.roomuuid)).unsigned=true;else if(typeof d.roomuuid==="string")m.roomuuid=parseInt(d.roomuuid,10);else if(typeof d.roomuuid==="number")m.roomuuid=d.roomuuid;else if(typeof d.roomuuid==="object")m.roomuuid=new $util.LongBits(d.roomuuid.low>>>0,d.roomuuid.high>>>0).toNumber(true)}return m};JoinRoomResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.roomId=0;if($util.Long){var n=new $util.Long(0,0,true);d.roomuuid=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.roomuuid=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(typeof m.roomuuid==="number")d.roomuuid=o.longs===String?String(m.roomuuid):m.roomuuid;else d.roomuuid=o.longs===String?$util.Long.prototype.toString.call(m.roomuuid):o.longs===Number?new $util.LongBits(m.roomuuid.low>>>0,m.roomuuid.high>>>0).toNumber(true):m.roomuuid}return d};JoinRoomResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JoinRoomResp}();jackfruit_proto.LeaveReq=function(){function LeaveReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LeaveReq.prototype.roomId=0;LeaveReq.create=function create(properties){return new LeaveReq(properties)};LeaveReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);return w};LeaveReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LeaveReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.LeaveReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;default:r.skipType(t&7);break}}return m};LeaveReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LeaveReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}return null};LeaveReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.LeaveReq)return d;var m=new $root.jackfruit_proto.LeaveReq;if(d.roomId!=null){m.roomId=d.roomId|0}return m};LeaveReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}return d};LeaveReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LeaveReq}();jackfruit_proto.LeaveResp=function(){function LeaveResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LeaveResp.prototype.code=0;LeaveResp.create=function create(properties){return new LeaveResp(properties)};LeaveResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};LeaveResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LeaveResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.LeaveResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};LeaveResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LeaveResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}return null};LeaveResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.LeaveResp)return d;var m=new $root.jackfruit_proto.LeaveResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};LeaveResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}return d};LeaveResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LeaveResp}();jackfruit_proto.SitDownReq=function(){function SitDownReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SitDownReq.prototype.roomId=0;SitDownReq.prototype.seatId=0;SitDownReq.create=function create(properties){return new SitDownReq(properties)};SitDownReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(16).int32(m.seatId);return w};SitDownReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SitDownReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SitDownReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:m.seatId=r.int32();break;default:r.skipType(t&7);break}}return m};SitDownReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SitDownReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}return null};SitDownReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SitDownReq)return d;var m=new $root.jackfruit_proto.SitDownReq;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.seatId!=null){m.seatId=d.seatId|0}return m};SitDownReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0;d.seatId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}return d};SitDownReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SitDownReq}();jackfruit_proto.SitDownResp=function(){function SitDownResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SitDownResp.prototype.code=0;SitDownResp.prototype.playerId=0;SitDownResp.prototype.playerName="";SitDownResp.prototype.seatId=0;SitDownResp.prototype.needAmount=$util.Long?$util.Long.fromBits(0,0,false):0;SitDownResp.prototype.amount=$util.Long?$util.Long.fromBits(0,0,false):0;SitDownResp.prototype.needScore=$util.Long?$util.Long.fromBits(0,0,false):0;SitDownResp.prototype.score=$util.Long?$util.Long.fromBits(0,0,false):0;SitDownResp.prototype.authVerifyCD=0;SitDownResp.prototype.roomId=0;SitDownResp.create=function create(properties){return new SitDownResp(properties)};SitDownResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(16).uint32(m.playerId);if(m.playerName!=null&&Object.hasOwnProperty.call(m,"playerName"))w.uint32(26).string(m.playerName);if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(32).int32(m.seatId);if(m.needAmount!=null&&Object.hasOwnProperty.call(m,"needAmount"))w.uint32(40).int64(m.needAmount);if(m.amount!=null&&Object.hasOwnProperty.call(m,"amount"))w.uint32(48).int64(m.amount);if(m.needScore!=null&&Object.hasOwnProperty.call(m,"needScore"))w.uint32(56).int64(m.needScore);if(m.score!=null&&Object.hasOwnProperty.call(m,"score"))w.uint32(64).int64(m.score);if(m.authVerifyCD!=null&&Object.hasOwnProperty.call(m,"authVerifyCD"))w.uint32(72).uint32(m.authVerifyCD);if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(80).uint32(m.roomId);return w};SitDownResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SitDownResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SitDownResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.playerId=r.uint32();break;case 3:m.playerName=r.string();break;case 4:m.seatId=r.int32();break;case 5:m.needAmount=r.int64();break;case 6:m.amount=r.int64();break;case 7:m.needScore=r.int64();break;case 8:m.score=r.int64();break;case 9:m.authVerifyCD=r.uint32();break;case 10:m.roomId=r.uint32();break;default:r.skipType(t&7);break}}return m};SitDownResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SitDownResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}if(m.playerName!=null&&m.hasOwnProperty("playerName")){if(!$util.isString(m.playerName))return"playerName: string expected"}if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}if(m.needAmount!=null&&m.hasOwnProperty("needAmount")){if(!$util.isInteger(m.needAmount)&&!(m.needAmount&&$util.isInteger(m.needAmount.low)&&$util.isInteger(m.needAmount.high)))return"needAmount: integer|Long expected"}if(m.amount!=null&&m.hasOwnProperty("amount")){if(!$util.isInteger(m.amount)&&!(m.amount&&$util.isInteger(m.amount.low)&&$util.isInteger(m.amount.high)))return"amount: integer|Long expected"}if(m.needScore!=null&&m.hasOwnProperty("needScore")){if(!$util.isInteger(m.needScore)&&!(m.needScore&&$util.isInteger(m.needScore.low)&&$util.isInteger(m.needScore.high)))return"needScore: integer|Long expected"}if(m.score!=null&&m.hasOwnProperty("score")){if(!$util.isInteger(m.score)&&!(m.score&&$util.isInteger(m.score.low)&&$util.isInteger(m.score.high)))return"score: integer|Long expected"}if(m.authVerifyCD!=null&&m.hasOwnProperty("authVerifyCD")){if(!$util.isInteger(m.authVerifyCD))return"authVerifyCD: integer expected"}if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}return null};SitDownResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SitDownResp)return d;var m=new $root.jackfruit_proto.SitDownResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.playerId!=null){m.playerId=d.playerId>>>0}if(d.playerName!=null){m.playerName=String(d.playerName)}if(d.seatId!=null){m.seatId=d.seatId|0}if(d.needAmount!=null){if($util.Long)(m.needAmount=$util.Long.fromValue(d.needAmount)).unsigned=false;else if(typeof d.needAmount==="string")m.needAmount=parseInt(d.needAmount,10);else if(typeof d.needAmount==="number")m.needAmount=d.needAmount;else if(typeof d.needAmount==="object")m.needAmount=new $util.LongBits(d.needAmount.low>>>0,d.needAmount.high>>>0).toNumber()}if(d.amount!=null){if($util.Long)(m.amount=$util.Long.fromValue(d.amount)).unsigned=false;else if(typeof d.amount==="string")m.amount=parseInt(d.amount,10);else if(typeof d.amount==="number")m.amount=d.amount;else if(typeof d.amount==="object")m.amount=new $util.LongBits(d.amount.low>>>0,d.amount.high>>>0).toNumber()}if(d.needScore!=null){if($util.Long)(m.needScore=$util.Long.fromValue(d.needScore)).unsigned=false;else if(typeof d.needScore==="string")m.needScore=parseInt(d.needScore,10);else if(typeof d.needScore==="number")m.needScore=d.needScore;else if(typeof d.needScore==="object")m.needScore=new $util.LongBits(d.needScore.low>>>0,d.needScore.high>>>0).toNumber()}if(d.score!=null){if($util.Long)(m.score=$util.Long.fromValue(d.score)).unsigned=false;else if(typeof d.score==="string")m.score=parseInt(d.score,10);else if(typeof d.score==="number")m.score=d.score;else if(typeof d.score==="object")m.score=new $util.LongBits(d.score.low>>>0,d.score.high>>>0).toNumber()}if(d.authVerifyCD!=null){m.authVerifyCD=d.authVerifyCD>>>0}if(d.roomId!=null){m.roomId=d.roomId>>>0}return m};SitDownResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.playerId=0;d.playerName="";d.seatId=0;if($util.Long){var n=new $util.Long(0,0,false);d.needAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.needAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.amount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.amount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.needScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.needScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.score=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.score=o.longs===String?"0":0;d.authVerifyCD=0;d.roomId=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}if(m.playerName!=null&&m.hasOwnProperty("playerName")){d.playerName=m.playerName}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}if(m.needAmount!=null&&m.hasOwnProperty("needAmount")){if(typeof m.needAmount==="number")d.needAmount=o.longs===String?String(m.needAmount):m.needAmount;else d.needAmount=o.longs===String?$util.Long.prototype.toString.call(m.needAmount):o.longs===Number?new $util.LongBits(m.needAmount.low>>>0,m.needAmount.high>>>0).toNumber():m.needAmount}if(m.amount!=null&&m.hasOwnProperty("amount")){if(typeof m.amount==="number")d.amount=o.longs===String?String(m.amount):m.amount;else d.amount=o.longs===String?$util.Long.prototype.toString.call(m.amount):o.longs===Number?new $util.LongBits(m.amount.low>>>0,m.amount.high>>>0).toNumber():m.amount}if(m.needScore!=null&&m.hasOwnProperty("needScore")){if(typeof m.needScore==="number")d.needScore=o.longs===String?String(m.needScore):m.needScore;else d.needScore=o.longs===String?$util.Long.prototype.toString.call(m.needScore):o.longs===Number?new $util.LongBits(m.needScore.low>>>0,m.needScore.high>>>0).toNumber():m.needScore}if(m.score!=null&&m.hasOwnProperty("score")){if(typeof m.score==="number")d.score=o.longs===String?String(m.score):m.score;else d.score=o.longs===String?$util.Long.prototype.toString.call(m.score):o.longs===Number?new $util.LongBits(m.score.low>>>0,m.score.high>>>0).toNumber():m.score}if(m.authVerifyCD!=null&&m.hasOwnProperty("authVerifyCD")){d.authVerifyCD=m.authVerifyCD}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}return d};SitDownResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SitDownResp}();jackfruit_proto.SitDownNotify=function(){function SitDownNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SitDownNotify.prototype.roomId=0;SitDownNotify.prototype.player=null;SitDownNotify.create=function create(properties){return new SitDownNotify(properties)};SitDownNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.player!=null&&Object.hasOwnProperty.call(m,"player"))$root.jackfruit_proto.PlayerInfo.encode(m.player,w.uint32(18).fork()).ldelim();return w};SitDownNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SitDownNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SitDownNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:m.player=$root.jackfruit_proto.PlayerInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};SitDownNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SitDownNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.player!=null&&m.hasOwnProperty("player")){{var e=$root.jackfruit_proto.PlayerInfo.verify(m.player);if(e)return"player."+e}}return null};SitDownNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SitDownNotify)return d;var m=new $root.jackfruit_proto.SitDownNotify;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.player!=null){if(typeof d.player!=="object")throw TypeError(".jackfruit_proto.SitDownNotify.player: object expected");m.player=$root.jackfruit_proto.PlayerInfo.fromObject(d.player)}return m};SitDownNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0;d.player=null}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.player!=null&&m.hasOwnProperty("player")){d.player=$root.jackfruit_proto.PlayerInfo.toObject(m.player,o)}return d};SitDownNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SitDownNotify}();jackfruit_proto.PlaceCardReq=function(){function PlaceCardReq(p){this.headCards=[];this.middleCards=[];this.tailCards=[];this.holeCards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlaceCardReq.prototype.headCards=$util.emptyArray;PlaceCardReq.prototype.middleCards=$util.emptyArray;PlaceCardReq.prototype.tailCards=$util.emptyArray;PlaceCardReq.prototype.holeCards=$util.emptyArray;PlaceCardReq.create=function create(properties){return new PlaceCardReq(properties)};PlaceCardReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.headCards!=null&&m.headCards.length){for(var i=0;i<m.headCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.headCards[i],w.uint32(10).fork()).ldelim()}if(m.middleCards!=null&&m.middleCards.length){for(var i=0;i<m.middleCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.middleCards[i],w.uint32(18).fork()).ldelim()}if(m.tailCards!=null&&m.tailCards.length){for(var i=0;i<m.tailCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.tailCards[i],w.uint32(26).fork()).ldelim()}if(m.holeCards!=null&&m.holeCards.length){for(var i=0;i<m.holeCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.holeCards[i],w.uint32(34).fork()).ldelim()}return w};PlaceCardReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlaceCardReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlaceCardReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.headCards&&m.headCards.length))m.headCards=[];m.headCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 2:if(!(m.middleCards&&m.middleCards.length))m.middleCards=[];m.middleCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 3:if(!(m.tailCards&&m.tailCards.length))m.tailCards=[];m.tailCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 4:if(!(m.holeCards&&m.holeCards.length))m.holeCards=[];m.holeCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};PlaceCardReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlaceCardReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.headCards!=null&&m.hasOwnProperty("headCards")){if(!Array.isArray(m.headCards))return"headCards: array expected";for(var i=0;i<m.headCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.headCards[i]);if(e)return"headCards."+e}}}if(m.middleCards!=null&&m.hasOwnProperty("middleCards")){if(!Array.isArray(m.middleCards))return"middleCards: array expected";for(var i=0;i<m.middleCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.middleCards[i]);if(e)return"middleCards."+e}}}if(m.tailCards!=null&&m.hasOwnProperty("tailCards")){if(!Array.isArray(m.tailCards))return"tailCards: array expected";for(var i=0;i<m.tailCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.tailCards[i]);if(e)return"tailCards."+e}}}if(m.holeCards!=null&&m.hasOwnProperty("holeCards")){if(!Array.isArray(m.holeCards))return"holeCards: array expected";for(var i=0;i<m.holeCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.holeCards[i]);if(e)return"holeCards."+e}}}return null};PlaceCardReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlaceCardReq)return d;var m=new $root.jackfruit_proto.PlaceCardReq;if(d.headCards){if(!Array.isArray(d.headCards))throw TypeError(".jackfruit_proto.PlaceCardReq.headCards: array expected");m.headCards=[];for(var i=0;i<d.headCards.length;++i){if(typeof d.headCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardReq.headCards: object expected");m.headCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.headCards[i])}}if(d.middleCards){if(!Array.isArray(d.middleCards))throw TypeError(".jackfruit_proto.PlaceCardReq.middleCards: array expected");m.middleCards=[];for(var i=0;i<d.middleCards.length;++i){if(typeof d.middleCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardReq.middleCards: object expected");m.middleCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.middleCards[i])}}if(d.tailCards){if(!Array.isArray(d.tailCards))throw TypeError(".jackfruit_proto.PlaceCardReq.tailCards: array expected");m.tailCards=[];for(var i=0;i<d.tailCards.length;++i){if(typeof d.tailCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardReq.tailCards: object expected");m.tailCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.tailCards[i])}}if(d.holeCards){if(!Array.isArray(d.holeCards))throw TypeError(".jackfruit_proto.PlaceCardReq.holeCards: array expected");m.holeCards=[];for(var i=0;i<d.holeCards.length;++i){if(typeof d.holeCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardReq.holeCards: object expected");m.holeCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.holeCards[i])}}return m};PlaceCardReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.headCards=[];d.middleCards=[];d.tailCards=[];d.holeCards=[]}if(m.headCards&&m.headCards.length){d.headCards=[];for(var j=0;j<m.headCards.length;++j){d.headCards[j]=$root.jackfruit_proto.CardItem.toObject(m.headCards[j],o)}}if(m.middleCards&&m.middleCards.length){d.middleCards=[];for(var j=0;j<m.middleCards.length;++j){d.middleCards[j]=$root.jackfruit_proto.CardItem.toObject(m.middleCards[j],o)}}if(m.tailCards&&m.tailCards.length){d.tailCards=[];for(var j=0;j<m.tailCards.length;++j){d.tailCards[j]=$root.jackfruit_proto.CardItem.toObject(m.tailCards[j],o)}}if(m.holeCards&&m.holeCards.length){d.holeCards=[];for(var j=0;j<m.holeCards.length;++j){d.holeCards[j]=$root.jackfruit_proto.CardItem.toObject(m.holeCards[j],o)}}return d};PlaceCardReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlaceCardReq}();jackfruit_proto.PlaceCardResp=function(){function PlaceCardResp(p){this.headCards=[];this.middleCards=[];this.tailCards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlaceCardResp.prototype.code=0;PlaceCardResp.prototype.headCards=$util.emptyArray;PlaceCardResp.prototype.middleCards=$util.emptyArray;PlaceCardResp.prototype.tailCards=$util.emptyArray;PlaceCardResp.create=function create(properties){return new PlaceCardResp(properties)};PlaceCardResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.headCards!=null&&m.headCards.length){for(var i=0;i<m.headCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.headCards[i],w.uint32(18).fork()).ldelim()}if(m.middleCards!=null&&m.middleCards.length){for(var i=0;i<m.middleCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.middleCards[i],w.uint32(26).fork()).ldelim()}if(m.tailCards!=null&&m.tailCards.length){for(var i=0;i<m.tailCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.tailCards[i],w.uint32(34).fork()).ldelim()}return w};PlaceCardResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlaceCardResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlaceCardResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:if(!(m.headCards&&m.headCards.length))m.headCards=[];m.headCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 3:if(!(m.middleCards&&m.middleCards.length))m.middleCards=[];m.middleCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 4:if(!(m.tailCards&&m.tailCards.length))m.tailCards=[];m.tailCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};PlaceCardResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlaceCardResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.headCards!=null&&m.hasOwnProperty("headCards")){if(!Array.isArray(m.headCards))return"headCards: array expected";for(var i=0;i<m.headCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.headCards[i]);if(e)return"headCards."+e}}}if(m.middleCards!=null&&m.hasOwnProperty("middleCards")){if(!Array.isArray(m.middleCards))return"middleCards: array expected";for(var i=0;i<m.middleCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.middleCards[i]);if(e)return"middleCards."+e}}}if(m.tailCards!=null&&m.hasOwnProperty("tailCards")){if(!Array.isArray(m.tailCards))return"tailCards: array expected";for(var i=0;i<m.tailCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.tailCards[i]);if(e)return"tailCards."+e}}}return null};PlaceCardResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlaceCardResp)return d;var m=new $root.jackfruit_proto.PlaceCardResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.headCards){if(!Array.isArray(d.headCards))throw TypeError(".jackfruit_proto.PlaceCardResp.headCards: array expected");m.headCards=[];for(var i=0;i<d.headCards.length;++i){if(typeof d.headCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardResp.headCards: object expected");m.headCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.headCards[i])}}if(d.middleCards){if(!Array.isArray(d.middleCards))throw TypeError(".jackfruit_proto.PlaceCardResp.middleCards: array expected");m.middleCards=[];for(var i=0;i<d.middleCards.length;++i){if(typeof d.middleCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardResp.middleCards: object expected");m.middleCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.middleCards[i])}}if(d.tailCards){if(!Array.isArray(d.tailCards))throw TypeError(".jackfruit_proto.PlaceCardResp.tailCards: array expected");m.tailCards=[];for(var i=0;i<d.tailCards.length;++i){if(typeof d.tailCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardResp.tailCards: object expected");m.tailCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.tailCards[i])}}return m};PlaceCardResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.headCards=[];d.middleCards=[];d.tailCards=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.headCards&&m.headCards.length){d.headCards=[];for(var j=0;j<m.headCards.length;++j){d.headCards[j]=$root.jackfruit_proto.CardItem.toObject(m.headCards[j],o)}}if(m.middleCards&&m.middleCards.length){d.middleCards=[];for(var j=0;j<m.middleCards.length;++j){d.middleCards[j]=$root.jackfruit_proto.CardItem.toObject(m.middleCards[j],o)}}if(m.tailCards&&m.tailCards.length){d.tailCards=[];for(var j=0;j<m.tailCards.length;++j){d.tailCards[j]=$root.jackfruit_proto.CardItem.toObject(m.tailCards[j],o)}}return d};PlaceCardResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlaceCardResp}();jackfruit_proto.PlaceCardOverReq=function(){function PlaceCardOverReq(p){this.headCards=[];this.middleCards=[];this.tailCards=[];this.holeCards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlaceCardOverReq.prototype.headCards=$util.emptyArray;PlaceCardOverReq.prototype.middleCards=$util.emptyArray;PlaceCardOverReq.prototype.tailCards=$util.emptyArray;PlaceCardOverReq.prototype.holeCards=$util.emptyArray;PlaceCardOverReq.create=function create(properties){return new PlaceCardOverReq(properties)};PlaceCardOverReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.headCards!=null&&m.headCards.length){for(var i=0;i<m.headCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.headCards[i],w.uint32(10).fork()).ldelim()}if(m.middleCards!=null&&m.middleCards.length){for(var i=0;i<m.middleCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.middleCards[i],w.uint32(18).fork()).ldelim()}if(m.tailCards!=null&&m.tailCards.length){for(var i=0;i<m.tailCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.tailCards[i],w.uint32(26).fork()).ldelim()}if(m.holeCards!=null&&m.holeCards.length){for(var i=0;i<m.holeCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.holeCards[i],w.uint32(34).fork()).ldelim()}return w};PlaceCardOverReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlaceCardOverReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlaceCardOverReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.headCards&&m.headCards.length))m.headCards=[];m.headCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 2:if(!(m.middleCards&&m.middleCards.length))m.middleCards=[];m.middleCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 3:if(!(m.tailCards&&m.tailCards.length))m.tailCards=[];m.tailCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 4:if(!(m.holeCards&&m.holeCards.length))m.holeCards=[];m.holeCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};PlaceCardOverReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlaceCardOverReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.headCards!=null&&m.hasOwnProperty("headCards")){if(!Array.isArray(m.headCards))return"headCards: array expected";for(var i=0;i<m.headCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.headCards[i]);if(e)return"headCards."+e}}}if(m.middleCards!=null&&m.hasOwnProperty("middleCards")){if(!Array.isArray(m.middleCards))return"middleCards: array expected";for(var i=0;i<m.middleCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.middleCards[i]);if(e)return"middleCards."+e}}}if(m.tailCards!=null&&m.hasOwnProperty("tailCards")){if(!Array.isArray(m.tailCards))return"tailCards: array expected";for(var i=0;i<m.tailCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.tailCards[i]);if(e)return"tailCards."+e}}}if(m.holeCards!=null&&m.hasOwnProperty("holeCards")){if(!Array.isArray(m.holeCards))return"holeCards: array expected";for(var i=0;i<m.holeCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.holeCards[i]);if(e)return"holeCards."+e}}}return null};PlaceCardOverReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlaceCardOverReq)return d;var m=new $root.jackfruit_proto.PlaceCardOverReq;if(d.headCards){if(!Array.isArray(d.headCards))throw TypeError(".jackfruit_proto.PlaceCardOverReq.headCards: array expected");m.headCards=[];for(var i=0;i<d.headCards.length;++i){if(typeof d.headCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardOverReq.headCards: object expected");m.headCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.headCards[i])}}if(d.middleCards){if(!Array.isArray(d.middleCards))throw TypeError(".jackfruit_proto.PlaceCardOverReq.middleCards: array expected");m.middleCards=[];for(var i=0;i<d.middleCards.length;++i){if(typeof d.middleCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardOverReq.middleCards: object expected");m.middleCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.middleCards[i])}}if(d.tailCards){if(!Array.isArray(d.tailCards))throw TypeError(".jackfruit_proto.PlaceCardOverReq.tailCards: array expected");m.tailCards=[];for(var i=0;i<d.tailCards.length;++i){if(typeof d.tailCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardOverReq.tailCards: object expected");m.tailCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.tailCards[i])}}if(d.holeCards){if(!Array.isArray(d.holeCards))throw TypeError(".jackfruit_proto.PlaceCardOverReq.holeCards: array expected");m.holeCards=[];for(var i=0;i<d.holeCards.length;++i){if(typeof d.holeCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardOverReq.holeCards: object expected");m.holeCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.holeCards[i])}}return m};PlaceCardOverReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.headCards=[];d.middleCards=[];d.tailCards=[];d.holeCards=[]}if(m.headCards&&m.headCards.length){d.headCards=[];for(var j=0;j<m.headCards.length;++j){d.headCards[j]=$root.jackfruit_proto.CardItem.toObject(m.headCards[j],o)}}if(m.middleCards&&m.middleCards.length){d.middleCards=[];for(var j=0;j<m.middleCards.length;++j){d.middleCards[j]=$root.jackfruit_proto.CardItem.toObject(m.middleCards[j],o)}}if(m.tailCards&&m.tailCards.length){d.tailCards=[];for(var j=0;j<m.tailCards.length;++j){d.tailCards[j]=$root.jackfruit_proto.CardItem.toObject(m.tailCards[j],o)}}if(m.holeCards&&m.holeCards.length){d.holeCards=[];for(var j=0;j<m.holeCards.length;++j){d.holeCards[j]=$root.jackfruit_proto.CardItem.toObject(m.holeCards[j],o)}}return d};PlaceCardOverReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlaceCardOverReq}();jackfruit_proto.PlaceCardOverResp=function(){function PlaceCardOverResp(p){this.headCards=[];this.middleCards=[];this.tailCards=[];this.holeCards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlaceCardOverResp.prototype.code=0;PlaceCardOverResp.prototype.headCards=$util.emptyArray;PlaceCardOverResp.prototype.middleCards=$util.emptyArray;PlaceCardOverResp.prototype.tailCards=$util.emptyArray;PlaceCardOverResp.prototype.holeCards=$util.emptyArray;PlaceCardOverResp.create=function create(properties){return new PlaceCardOverResp(properties)};PlaceCardOverResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.headCards!=null&&m.headCards.length){for(var i=0;i<m.headCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.headCards[i],w.uint32(18).fork()).ldelim()}if(m.middleCards!=null&&m.middleCards.length){for(var i=0;i<m.middleCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.middleCards[i],w.uint32(26).fork()).ldelim()}if(m.tailCards!=null&&m.tailCards.length){for(var i=0;i<m.tailCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.tailCards[i],w.uint32(34).fork()).ldelim()}if(m.holeCards!=null&&m.holeCards.length){for(var i=0;i<m.holeCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.holeCards[i],w.uint32(42).fork()).ldelim()}return w};PlaceCardOverResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlaceCardOverResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlaceCardOverResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:if(!(m.headCards&&m.headCards.length))m.headCards=[];m.headCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 3:if(!(m.middleCards&&m.middleCards.length))m.middleCards=[];m.middleCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 4:if(!(m.tailCards&&m.tailCards.length))m.tailCards=[];m.tailCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 5:if(!(m.holeCards&&m.holeCards.length))m.holeCards=[];m.holeCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};PlaceCardOverResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlaceCardOverResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.headCards!=null&&m.hasOwnProperty("headCards")){if(!Array.isArray(m.headCards))return"headCards: array expected";for(var i=0;i<m.headCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.headCards[i]);if(e)return"headCards."+e}}}if(m.middleCards!=null&&m.hasOwnProperty("middleCards")){if(!Array.isArray(m.middleCards))return"middleCards: array expected";for(var i=0;i<m.middleCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.middleCards[i]);if(e)return"middleCards."+e}}}if(m.tailCards!=null&&m.hasOwnProperty("tailCards")){if(!Array.isArray(m.tailCards))return"tailCards: array expected";for(var i=0;i<m.tailCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.tailCards[i]);if(e)return"tailCards."+e}}}if(m.holeCards!=null&&m.hasOwnProperty("holeCards")){if(!Array.isArray(m.holeCards))return"holeCards: array expected";for(var i=0;i<m.holeCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.holeCards[i]);if(e)return"holeCards."+e}}}return null};PlaceCardOverResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlaceCardOverResp)return d;var m=new $root.jackfruit_proto.PlaceCardOverResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.headCards){if(!Array.isArray(d.headCards))throw TypeError(".jackfruit_proto.PlaceCardOverResp.headCards: array expected");m.headCards=[];for(var i=0;i<d.headCards.length;++i){if(typeof d.headCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardOverResp.headCards: object expected");m.headCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.headCards[i])}}if(d.middleCards){if(!Array.isArray(d.middleCards))throw TypeError(".jackfruit_proto.PlaceCardOverResp.middleCards: array expected");m.middleCards=[];for(var i=0;i<d.middleCards.length;++i){if(typeof d.middleCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardOverResp.middleCards: object expected");m.middleCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.middleCards[i])}}if(d.tailCards){if(!Array.isArray(d.tailCards))throw TypeError(".jackfruit_proto.PlaceCardOverResp.tailCards: array expected");m.tailCards=[];for(var i=0;i<d.tailCards.length;++i){if(typeof d.tailCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardOverResp.tailCards: object expected");m.tailCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.tailCards[i])}}if(d.holeCards){if(!Array.isArray(d.holeCards))throw TypeError(".jackfruit_proto.PlaceCardOverResp.holeCards: array expected");m.holeCards=[];for(var i=0;i<d.holeCards.length;++i){if(typeof d.holeCards[i]!=="object")throw TypeError(".jackfruit_proto.PlaceCardOverResp.holeCards: object expected");m.holeCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.holeCards[i])}}return m};PlaceCardOverResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.headCards=[];d.middleCards=[];d.tailCards=[];d.holeCards=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.headCards&&m.headCards.length){d.headCards=[];for(var j=0;j<m.headCards.length;++j){d.headCards[j]=$root.jackfruit_proto.CardItem.toObject(m.headCards[j],o)}}if(m.middleCards&&m.middleCards.length){d.middleCards=[];for(var j=0;j<m.middleCards.length;++j){d.middleCards[j]=$root.jackfruit_proto.CardItem.toObject(m.middleCards[j],o)}}if(m.tailCards&&m.tailCards.length){d.tailCards=[];for(var j=0;j<m.tailCards.length;++j){d.tailCards[j]=$root.jackfruit_proto.CardItem.toObject(m.tailCards[j],o)}}if(m.holeCards&&m.holeCards.length){d.holeCards=[];for(var j=0;j<m.holeCards.length;++j){d.holeCards[j]=$root.jackfruit_proto.CardItem.toObject(m.holeCards[j],o)}}return d};PlaceCardOverResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlaceCardOverResp}();jackfruit_proto.PlaceCardOverNotify=function(){function PlaceCardOverNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlaceCardOverNotify.prototype.seatId=0;PlaceCardOverNotify.create=function create(properties){return new PlaceCardOverNotify(properties)};PlaceCardOverNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(8).int32(m.seatId);return w};PlaceCardOverNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlaceCardOverNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlaceCardOverNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.seatId=r.int32();break;default:r.skipType(t&7);break}}return m};PlaceCardOverNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlaceCardOverNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}return null};PlaceCardOverNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlaceCardOverNotify)return d;var m=new $root.jackfruit_proto.PlaceCardOverNotify;if(d.seatId!=null){m.seatId=d.seatId|0}return m};PlaceCardOverNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.seatId=0}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}return d};PlaceCardOverNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlaceCardOverNotify}();jackfruit_proto.StandUpReq=function(){function StandUpReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StandUpReq.prototype.roomId=0;StandUpReq.create=function create(properties){return new StandUpReq(properties)};StandUpReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);return w};StandUpReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StandUpReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.StandUpReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;default:r.skipType(t&7);break}}return m};StandUpReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StandUpReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}return null};StandUpReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.StandUpReq)return d;var m=new $root.jackfruit_proto.StandUpReq;if(d.roomId!=null){m.roomId=d.roomId|0}return m};StandUpReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}return d};StandUpReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StandUpReq}();jackfruit_proto.StandUpResp=function(){function StandUpResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StandUpResp.prototype.code=0;StandUpResp.create=function create(properties){return new StandUpResp(properties)};StandUpResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(16).int32(m.code);return w};StandUpResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StandUpResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.StandUpResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 2:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};StandUpResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StandUpResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}return null};StandUpResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.StandUpResp)return d;var m=new $root.jackfruit_proto.StandUpResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};StandUpResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}return d};StandUpResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StandUpResp}();jackfruit_proto.StandUpNotify=function(){function StandUpNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StandUpNotify.prototype.roomId=0;StandUpNotify.prototype.playerId=0;StandUpNotify.create=function create(properties){return new StandUpNotify(properties)};StandUpNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(16).uint32(m.playerId);return w};StandUpNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StandUpNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.StandUpNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:m.playerId=r.uint32();break;default:r.skipType(t&7);break}}return m};StandUpNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StandUpNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}return null};StandUpNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.StandUpNotify)return d;var m=new $root.jackfruit_proto.StandUpNotify;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.playerId!=null){m.playerId=d.playerId>>>0}return m};StandUpNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0;d.playerId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}return d};StandUpNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StandUpNotify}();jackfruit_proto.GameRecordReq=function(){function GameRecordReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameRecordReq.create=function create(properties){return new GameRecordReq(properties)};GameRecordReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};GameRecordReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameRecordReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GameRecordReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};GameRecordReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameRecordReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};GameRecordReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GameRecordReq)return d;return new $root.jackfruit_proto.GameRecordReq};GameRecordReq.toObject=function toObject(){return{}};GameRecordReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameRecordReq}();jackfruit_proto.GameRecordResp=function(){function GameRecordResp(p){this.gameRecords=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameRecordResp.prototype.code=0;GameRecordResp.prototype.gameRecords=$util.emptyArray;GameRecordResp.create=function create(properties){return new GameRecordResp(properties)};GameRecordResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.gameRecords!=null&&m.gameRecords.length){for(var i=0;i<m.gameRecords.length;++i)$root.jackfruit_proto.GameRecord.encode(m.gameRecords[i],w.uint32(10).fork()).ldelim()}if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(16).int32(m.code);return w};GameRecordResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameRecordResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GameRecordResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 2:m.code=r.int32();break;case 1:if(!(m.gameRecords&&m.gameRecords.length))m.gameRecords=[];m.gameRecords.push($root.jackfruit_proto.GameRecord.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};GameRecordResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameRecordResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.gameRecords!=null&&m.hasOwnProperty("gameRecords")){if(!Array.isArray(m.gameRecords))return"gameRecords: array expected";for(var i=0;i<m.gameRecords.length;++i){{var e=$root.jackfruit_proto.GameRecord.verify(m.gameRecords[i]);if(e)return"gameRecords."+e}}}return null};GameRecordResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GameRecordResp)return d;var m=new $root.jackfruit_proto.GameRecordResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.gameRecords){if(!Array.isArray(d.gameRecords))throw TypeError(".jackfruit_proto.GameRecordResp.gameRecords: array expected");m.gameRecords=[];for(var i=0;i<d.gameRecords.length;++i){if(typeof d.gameRecords[i]!=="object")throw TypeError(".jackfruit_proto.GameRecordResp.gameRecords: object expected");m.gameRecords[i]=$root.jackfruit_proto.GameRecord.fromObject(d.gameRecords[i])}}return m};GameRecordResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.gameRecords=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.gameRecords&&m.gameRecords.length){d.gameRecords=[];for(var j=0;j<m.gameRecords.length;++j){d.gameRecords[j]=$root.jackfruit_proto.GameRecord.toObject(m.gameRecords[j],o)}}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}return d};GameRecordResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameRecordResp}();jackfruit_proto.GameRecord=function(){function GameRecord(p){this.playerSettle=[];this.pubCards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameRecord.prototype.playerSettle=$util.emptyArray;GameRecord.prototype.pubCards=$util.emptyArray;GameRecord.prototype.gameUuid="";GameRecord.prototype.recordTime=$util.Long?$util.Long.fromBits(0,0,false):0;GameRecord.create=function create(properties){return new GameRecord(properties)};GameRecord.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerSettle!=null&&m.playerSettle.length){for(var i=0;i<m.playerSettle.length;++i)$root.jackfruit_proto.PlayerSettle.encode(m.playerSettle[i],w.uint32(10).fork()).ldelim()}if(m.pubCards!=null&&m.pubCards.length){for(var i=0;i<m.pubCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.pubCards[i],w.uint32(18).fork()).ldelim()}if(m.gameUuid!=null&&Object.hasOwnProperty.call(m,"gameUuid"))w.uint32(26).string(m.gameUuid);if(m.recordTime!=null&&Object.hasOwnProperty.call(m,"recordTime"))w.uint32(32).int64(m.recordTime);return w};GameRecord.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameRecord.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GameRecord;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.playerSettle&&m.playerSettle.length))m.playerSettle=[];m.playerSettle.push($root.jackfruit_proto.PlayerSettle.decode(r,r.uint32()));break;case 2:if(!(m.pubCards&&m.pubCards.length))m.pubCards=[];m.pubCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 3:m.gameUuid=r.string();break;case 4:m.recordTime=r.int64();break;default:r.skipType(t&7);break}}return m};GameRecord.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameRecord.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerSettle!=null&&m.hasOwnProperty("playerSettle")){if(!Array.isArray(m.playerSettle))return"playerSettle: array expected";for(var i=0;i<m.playerSettle.length;++i){{var e=$root.jackfruit_proto.PlayerSettle.verify(m.playerSettle[i]);if(e)return"playerSettle."+e}}}if(m.pubCards!=null&&m.hasOwnProperty("pubCards")){if(!Array.isArray(m.pubCards))return"pubCards: array expected";for(var i=0;i<m.pubCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.pubCards[i]);if(e)return"pubCards."+e}}}if(m.gameUuid!=null&&m.hasOwnProperty("gameUuid")){if(!$util.isString(m.gameUuid))return"gameUuid: string expected"}if(m.recordTime!=null&&m.hasOwnProperty("recordTime")){if(!$util.isInteger(m.recordTime)&&!(m.recordTime&&$util.isInteger(m.recordTime.low)&&$util.isInteger(m.recordTime.high)))return"recordTime: integer|Long expected"}return null};GameRecord.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GameRecord)return d;var m=new $root.jackfruit_proto.GameRecord;if(d.playerSettle){if(!Array.isArray(d.playerSettle))throw TypeError(".jackfruit_proto.GameRecord.playerSettle: array expected");m.playerSettle=[];for(var i=0;i<d.playerSettle.length;++i){if(typeof d.playerSettle[i]!=="object")throw TypeError(".jackfruit_proto.GameRecord.playerSettle: object expected");m.playerSettle[i]=$root.jackfruit_proto.PlayerSettle.fromObject(d.playerSettle[i])}}if(d.pubCards){if(!Array.isArray(d.pubCards))throw TypeError(".jackfruit_proto.GameRecord.pubCards: array expected");m.pubCards=[];for(var i=0;i<d.pubCards.length;++i){if(typeof d.pubCards[i]!=="object")throw TypeError(".jackfruit_proto.GameRecord.pubCards: object expected");m.pubCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.pubCards[i])}}if(d.gameUuid!=null){m.gameUuid=String(d.gameUuid)}if(d.recordTime!=null){if($util.Long)(m.recordTime=$util.Long.fromValue(d.recordTime)).unsigned=false;else if(typeof d.recordTime==="string")m.recordTime=parseInt(d.recordTime,10);else if(typeof d.recordTime==="number")m.recordTime=d.recordTime;else if(typeof d.recordTime==="object")m.recordTime=new $util.LongBits(d.recordTime.low>>>0,d.recordTime.high>>>0).toNumber()}return m};GameRecord.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.playerSettle=[];d.pubCards=[]}if(o.defaults){d.gameUuid="";if($util.Long){var n=new $util.Long(0,0,false);d.recordTime=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.recordTime=o.longs===String?"0":0}if(m.playerSettle&&m.playerSettle.length){d.playerSettle=[];for(var j=0;j<m.playerSettle.length;++j){d.playerSettle[j]=$root.jackfruit_proto.PlayerSettle.toObject(m.playerSettle[j],o)}}if(m.pubCards&&m.pubCards.length){d.pubCards=[];for(var j=0;j<m.pubCards.length;++j){d.pubCards[j]=$root.jackfruit_proto.CardItem.toObject(m.pubCards[j],o)}}if(m.gameUuid!=null&&m.hasOwnProperty("gameUuid")){d.gameUuid=m.gameUuid}if(m.recordTime!=null&&m.hasOwnProperty("recordTime")){if(typeof m.recordTime==="number")d.recordTime=o.longs===String?String(m.recordTime):m.recordTime;else d.recordTime=o.longs===String?$util.Long.prototype.toString.call(m.recordTime):o.longs===Number?new $util.LongBits(m.recordTime.low>>>0,m.recordTime.high>>>0).toNumber():m.recordTime}return d};GameRecord.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameRecord}();jackfruit_proto.ActionDelayReq=function(){function ActionDelayReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ActionDelayReq.create=function create(properties){return new ActionDelayReq(properties)};ActionDelayReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};ActionDelayReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ActionDelayReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ActionDelayReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};ActionDelayReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ActionDelayReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};ActionDelayReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ActionDelayReq)return d;return new $root.jackfruit_proto.ActionDelayReq};ActionDelayReq.toObject=function toObject(){return{}};ActionDelayReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ActionDelayReq}();jackfruit_proto.ActionDelayResp=function(){function ActionDelayResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ActionDelayResp.prototype.code=0;ActionDelayResp.prototype.actionDelayCountsFee=$util.Long?$util.Long.fromBits(0,0,false):0;ActionDelayResp.create=function create(properties){return new ActionDelayResp(properties)};ActionDelayResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.actionDelayCountsFee!=null&&Object.hasOwnProperty.call(m,"actionDelayCountsFee"))w.uint32(16).int64(m.actionDelayCountsFee);return w};ActionDelayResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ActionDelayResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ActionDelayResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.actionDelayCountsFee=r.int64();break;default:r.skipType(t&7);break}}return m};ActionDelayResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ActionDelayResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.actionDelayCountsFee!=null&&m.hasOwnProperty("actionDelayCountsFee")){if(!$util.isInteger(m.actionDelayCountsFee)&&!(m.actionDelayCountsFee&&$util.isInteger(m.actionDelayCountsFee.low)&&$util.isInteger(m.actionDelayCountsFee.high)))return"actionDelayCountsFee: integer|Long expected"}return null};ActionDelayResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ActionDelayResp)return d;var m=new $root.jackfruit_proto.ActionDelayResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.actionDelayCountsFee!=null){if($util.Long)(m.actionDelayCountsFee=$util.Long.fromValue(d.actionDelayCountsFee)).unsigned=false;else if(typeof d.actionDelayCountsFee==="string")m.actionDelayCountsFee=parseInt(d.actionDelayCountsFee,10);else if(typeof d.actionDelayCountsFee==="number")m.actionDelayCountsFee=d.actionDelayCountsFee;else if(typeof d.actionDelayCountsFee==="object")m.actionDelayCountsFee=new $util.LongBits(d.actionDelayCountsFee.low>>>0,d.actionDelayCountsFee.high>>>0).toNumber()}return m};ActionDelayResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.actionDelayCountsFee=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.actionDelayCountsFee=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.actionDelayCountsFee!=null&&m.hasOwnProperty("actionDelayCountsFee")){if(typeof m.actionDelayCountsFee==="number")d.actionDelayCountsFee=o.longs===String?String(m.actionDelayCountsFee):m.actionDelayCountsFee;else d.actionDelayCountsFee=o.longs===String?$util.Long.prototype.toString.call(m.actionDelayCountsFee):o.longs===Number?new $util.LongBits(m.actionDelayCountsFee.low>>>0,m.actionDelayCountsFee.high>>>0).toNumber():m.actionDelayCountsFee}return d};ActionDelayResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ActionDelayResp}();jackfruit_proto.ActionDelayNotify=function(){function ActionDelayNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ActionDelayNotify.prototype.playerId=0;ActionDelayNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;ActionDelayNotify.prototype.nextStateStamp=$util.Long?$util.Long.fromBits(0,0,false):0;ActionDelayNotify.create=function create(properties){return new ActionDelayNotify(properties)};ActionDelayNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(8).uint32(m.playerId);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(16).int64(m.leftSeconds);if(m.nextStateStamp!=null&&Object.hasOwnProperty.call(m,"nextStateStamp"))w.uint32(24).int64(m.nextStateStamp);return w};ActionDelayNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ActionDelayNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ActionDelayNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.playerId=r.uint32();break;case 2:m.leftSeconds=r.int64();break;case 3:m.nextStateStamp=r.int64();break;default:r.skipType(t&7);break}}return m};ActionDelayNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ActionDelayNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(!$util.isInteger(m.nextStateStamp)&&!(m.nextStateStamp&&$util.isInteger(m.nextStateStamp.low)&&$util.isInteger(m.nextStateStamp.high)))return"nextStateStamp: integer|Long expected"}return null};ActionDelayNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ActionDelayNotify)return d;var m=new $root.jackfruit_proto.ActionDelayNotify;if(d.playerId!=null){m.playerId=d.playerId>>>0}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.nextStateStamp!=null){if($util.Long)(m.nextStateStamp=$util.Long.fromValue(d.nextStateStamp)).unsigned=false;else if(typeof d.nextStateStamp==="string")m.nextStateStamp=parseInt(d.nextStateStamp,10);else if(typeof d.nextStateStamp==="number")m.nextStateStamp=d.nextStateStamp;else if(typeof d.nextStateStamp==="object")m.nextStateStamp=new $util.LongBits(d.nextStateStamp.low>>>0,d.nextStateStamp.high>>>0).toNumber()}return m};ActionDelayNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.playerId=0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextStateStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextStateStamp=o.longs===String?"0":0}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(typeof m.nextStateStamp==="number")d.nextStateStamp=o.longs===String?String(m.nextStateStamp):m.nextStateStamp;else d.nextStateStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextStateStamp):o.longs===Number?new $util.LongBits(m.nextStateStamp.low>>>0,m.nextStateStamp.high>>>0).toNumber():m.nextStateStamp}return d};ActionDelayNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ActionDelayNotify}();jackfruit_proto.GameWillStartNotify=function(){function GameWillStartNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameWillStartNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameWillStartNotify.prototype.nextStateStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameWillStartNotify.create=function create(properties){return new GameWillStartNotify(properties)};GameWillStartNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(8).int64(m.leftSeconds);if(m.nextStateStamp!=null&&Object.hasOwnProperty.call(m,"nextStateStamp"))w.uint32(16).int64(m.nextStateStamp);return w};GameWillStartNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameWillStartNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GameWillStartNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.leftSeconds=r.int64();break;case 2:m.nextStateStamp=r.int64();break;default:r.skipType(t&7);break}}return m};GameWillStartNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameWillStartNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(!$util.isInteger(m.nextStateStamp)&&!(m.nextStateStamp&&$util.isInteger(m.nextStateStamp.low)&&$util.isInteger(m.nextStateStamp.high)))return"nextStateStamp: integer|Long expected"}return null};GameWillStartNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GameWillStartNotify)return d;var m=new $root.jackfruit_proto.GameWillStartNotify;if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.nextStateStamp!=null){if($util.Long)(m.nextStateStamp=$util.Long.fromValue(d.nextStateStamp)).unsigned=false;else if(typeof d.nextStateStamp==="string")m.nextStateStamp=parseInt(d.nextStateStamp,10);else if(typeof d.nextStateStamp==="number")m.nextStateStamp=d.nextStateStamp;else if(typeof d.nextStateStamp==="object")m.nextStateStamp=new $util.LongBits(d.nextStateStamp.low>>>0,d.nextStateStamp.high>>>0).toNumber()}return m};GameWillStartNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextStateStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextStateStamp=o.longs===String?"0":0}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(typeof m.nextStateStamp==="number")d.nextStateStamp=o.longs===String?String(m.nextStateStamp):m.nextStateStamp;else d.nextStateStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextStateStamp):o.longs===Number?new $util.LongBits(m.nextStateStamp.low>>>0,m.nextStateStamp.high>>>0).toNumber():m.nextStateStamp}return d};GameWillStartNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameWillStartNotify}();jackfruit_proto.DealNotify=function(){function DealNotify(p){this.seatList=[];this.holdCards=[];this.publicCards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DealNotify.prototype.roomId=0;DealNotify.prototype.seatList=$util.emptyArray;DealNotify.prototype.holdCards=$util.emptyArray;DealNotify.prototype.publicCards=$util.emptyArray;DealNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;DealNotify.prototype.nextStateStamp=$util.Long?$util.Long.fromBits(0,0,false):0;DealNotify.prototype.actionDelayCountsFee=$util.Long?$util.Long.fromBits(0,0,false):0;DealNotify.create=function create(properties){return new DealNotify(properties)};DealNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.seatList!=null&&m.seatList.length){w.uint32(18).fork();for(var i=0;i<m.seatList.length;++i)w.int32(m.seatList[i]);w.ldelim()}if(m.holdCards!=null&&m.holdCards.length){for(var i=0;i<m.holdCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.holdCards[i],w.uint32(26).fork()).ldelim()}if(m.publicCards!=null&&m.publicCards.length){for(var i=0;i<m.publicCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.publicCards[i],w.uint32(34).fork()).ldelim()}if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(40).int64(m.leftSeconds);if(m.nextStateStamp!=null&&Object.hasOwnProperty.call(m,"nextStateStamp"))w.uint32(48).int64(m.nextStateStamp);if(m.actionDelayCountsFee!=null&&Object.hasOwnProperty.call(m,"actionDelayCountsFee"))w.uint32(56).int64(m.actionDelayCountsFee);return w};DealNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DealNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.DealNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:if(!(m.seatList&&m.seatList.length))m.seatList=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.seatList.push(r.int32())}else m.seatList.push(r.int32());break;case 3:if(!(m.holdCards&&m.holdCards.length))m.holdCards=[];m.holdCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 4:if(!(m.publicCards&&m.publicCards.length))m.publicCards=[];m.publicCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 5:m.leftSeconds=r.int64();break;case 6:m.nextStateStamp=r.int64();break;case 7:m.actionDelayCountsFee=r.int64();break;default:r.skipType(t&7);break}}return m};DealNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DealNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.seatList!=null&&m.hasOwnProperty("seatList")){if(!Array.isArray(m.seatList))return"seatList: array expected";for(var i=0;i<m.seatList.length;++i){if(!$util.isInteger(m.seatList[i]))return"seatList: integer[] expected"}}if(m.holdCards!=null&&m.hasOwnProperty("holdCards")){if(!Array.isArray(m.holdCards))return"holdCards: array expected";for(var i=0;i<m.holdCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.holdCards[i]);if(e)return"holdCards."+e}}}if(m.publicCards!=null&&m.hasOwnProperty("publicCards")){if(!Array.isArray(m.publicCards))return"publicCards: array expected";for(var i=0;i<m.publicCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.publicCards[i]);if(e)return"publicCards."+e}}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(!$util.isInteger(m.nextStateStamp)&&!(m.nextStateStamp&&$util.isInteger(m.nextStateStamp.low)&&$util.isInteger(m.nextStateStamp.high)))return"nextStateStamp: integer|Long expected"}if(m.actionDelayCountsFee!=null&&m.hasOwnProperty("actionDelayCountsFee")){if(!$util.isInteger(m.actionDelayCountsFee)&&!(m.actionDelayCountsFee&&$util.isInteger(m.actionDelayCountsFee.low)&&$util.isInteger(m.actionDelayCountsFee.high)))return"actionDelayCountsFee: integer|Long expected"}return null};DealNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.DealNotify)return d;var m=new $root.jackfruit_proto.DealNotify;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.seatList){if(!Array.isArray(d.seatList))throw TypeError(".jackfruit_proto.DealNotify.seatList: array expected");m.seatList=[];for(var i=0;i<d.seatList.length;++i){m.seatList[i]=d.seatList[i]|0}}if(d.holdCards){if(!Array.isArray(d.holdCards))throw TypeError(".jackfruit_proto.DealNotify.holdCards: array expected");m.holdCards=[];for(var i=0;i<d.holdCards.length;++i){if(typeof d.holdCards[i]!=="object")throw TypeError(".jackfruit_proto.DealNotify.holdCards: object expected");m.holdCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.holdCards[i])}}if(d.publicCards){if(!Array.isArray(d.publicCards))throw TypeError(".jackfruit_proto.DealNotify.publicCards: array expected");m.publicCards=[];for(var i=0;i<d.publicCards.length;++i){if(typeof d.publicCards[i]!=="object")throw TypeError(".jackfruit_proto.DealNotify.publicCards: object expected");m.publicCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.publicCards[i])}}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.nextStateStamp!=null){if($util.Long)(m.nextStateStamp=$util.Long.fromValue(d.nextStateStamp)).unsigned=false;else if(typeof d.nextStateStamp==="string")m.nextStateStamp=parseInt(d.nextStateStamp,10);else if(typeof d.nextStateStamp==="number")m.nextStateStamp=d.nextStateStamp;else if(typeof d.nextStateStamp==="object")m.nextStateStamp=new $util.LongBits(d.nextStateStamp.low>>>0,d.nextStateStamp.high>>>0).toNumber()}if(d.actionDelayCountsFee!=null){if($util.Long)(m.actionDelayCountsFee=$util.Long.fromValue(d.actionDelayCountsFee)).unsigned=false;else if(typeof d.actionDelayCountsFee==="string")m.actionDelayCountsFee=parseInt(d.actionDelayCountsFee,10);else if(typeof d.actionDelayCountsFee==="number")m.actionDelayCountsFee=d.actionDelayCountsFee;else if(typeof d.actionDelayCountsFee==="object")m.actionDelayCountsFee=new $util.LongBits(d.actionDelayCountsFee.low>>>0,d.actionDelayCountsFee.high>>>0).toNumber()}return m};DealNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.seatList=[];d.holdCards=[];d.publicCards=[]}if(o.defaults){d.roomId=0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextStateStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextStateStamp=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.actionDelayCountsFee=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.actionDelayCountsFee=o.longs===String?"0":0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.seatList&&m.seatList.length){d.seatList=[];for(var j=0;j<m.seatList.length;++j){d.seatList[j]=m.seatList[j]}}if(m.holdCards&&m.holdCards.length){d.holdCards=[];for(var j=0;j<m.holdCards.length;++j){d.holdCards[j]=$root.jackfruit_proto.CardItem.toObject(m.holdCards[j],o)}}if(m.publicCards&&m.publicCards.length){d.publicCards=[];for(var j=0;j<m.publicCards.length;++j){d.publicCards[j]=$root.jackfruit_proto.CardItem.toObject(m.publicCards[j],o)}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(typeof m.nextStateStamp==="number")d.nextStateStamp=o.longs===String?String(m.nextStateStamp):m.nextStateStamp;else d.nextStateStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextStateStamp):o.longs===Number?new $util.LongBits(m.nextStateStamp.low>>>0,m.nextStateStamp.high>>>0).toNumber():m.nextStateStamp}if(m.actionDelayCountsFee!=null&&m.hasOwnProperty("actionDelayCountsFee")){if(typeof m.actionDelayCountsFee==="number")d.actionDelayCountsFee=o.longs===String?String(m.actionDelayCountsFee):m.actionDelayCountsFee;else d.actionDelayCountsFee=o.longs===String?$util.Long.prototype.toString.call(m.actionDelayCountsFee):o.longs===Number?new $util.LongBits(m.actionDelayCountsFee.low>>>0,m.actionDelayCountsFee.high>>>0).toNumber():m.actionDelayCountsFee}return d};DealNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DealNotify}();jackfruit_proto.StartPlaceCardsNotify=function(){function StartPlaceCardsNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StartPlaceCardsNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;StartPlaceCardsNotify.prototype.nextStateStamp=$util.Long?$util.Long.fromBits(0,0,false):0;StartPlaceCardsNotify.create=function create(properties){return new StartPlaceCardsNotify(properties)};StartPlaceCardsNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(8).int64(m.leftSeconds);if(m.nextStateStamp!=null&&Object.hasOwnProperty.call(m,"nextStateStamp"))w.uint32(16).int64(m.nextStateStamp);return w};StartPlaceCardsNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StartPlaceCardsNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.StartPlaceCardsNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.leftSeconds=r.int64();break;case 2:m.nextStateStamp=r.int64();break;default:r.skipType(t&7);break}}return m};StartPlaceCardsNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StartPlaceCardsNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(!$util.isInteger(m.nextStateStamp)&&!(m.nextStateStamp&&$util.isInteger(m.nextStateStamp.low)&&$util.isInteger(m.nextStateStamp.high)))return"nextStateStamp: integer|Long expected"}return null};StartPlaceCardsNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.StartPlaceCardsNotify)return d;var m=new $root.jackfruit_proto.StartPlaceCardsNotify;if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.nextStateStamp!=null){if($util.Long)(m.nextStateStamp=$util.Long.fromValue(d.nextStateStamp)).unsigned=false;else if(typeof d.nextStateStamp==="string")m.nextStateStamp=parseInt(d.nextStateStamp,10);else if(typeof d.nextStateStamp==="number")m.nextStateStamp=d.nextStateStamp;else if(typeof d.nextStateStamp==="object")m.nextStateStamp=new $util.LongBits(d.nextStateStamp.low>>>0,d.nextStateStamp.high>>>0).toNumber()}return m};StartPlaceCardsNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextStateStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextStateStamp=o.longs===String?"0":0}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(typeof m.nextStateStamp==="number")d.nextStateStamp=o.longs===String?String(m.nextStateStamp):m.nextStateStamp;else d.nextStateStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextStateStamp):o.longs===Number?new $util.LongBits(m.nextStateStamp.low>>>0,m.nextStateStamp.high>>>0).toNumber():m.nextStateStamp}return d};StartPlaceCardsNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StartPlaceCardsNotify}();jackfruit_proto.CommunityCardsNotify=function(){function CommunityCardsNotify(p){this.publicCards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CommunityCardsNotify.prototype.roomId=0;CommunityCardsNotify.prototype.publicCards=$util.emptyArray;CommunityCardsNotify.prototype.roundState=0;CommunityCardsNotify.create=function create(properties){return new CommunityCardsNotify(properties)};CommunityCardsNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.publicCards!=null&&m.publicCards.length){for(var i=0;i<m.publicCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.publicCards[i],w.uint32(18).fork()).ldelim()}if(m.roundState!=null&&Object.hasOwnProperty.call(m,"roundState"))w.uint32(24).int32(m.roundState);return w};CommunityCardsNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CommunityCardsNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.CommunityCardsNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:if(!(m.publicCards&&m.publicCards.length))m.publicCards=[];m.publicCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 3:m.roundState=r.int32();break;default:r.skipType(t&7);break}}return m};CommunityCardsNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CommunityCardsNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.publicCards!=null&&m.hasOwnProperty("publicCards")){if(!Array.isArray(m.publicCards))return"publicCards: array expected";for(var i=0;i<m.publicCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.publicCards[i]);if(e)return"publicCards."+e}}}if(m.roundState!=null&&m.hasOwnProperty("roundState")){switch(m.roundState){default:return"roundState: enum value expected";case 0:case 1:case 2:case 11:case 12:case 13:case 14:case 18:case 20:break}}return null};CommunityCardsNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.CommunityCardsNotify)return d;var m=new $root.jackfruit_proto.CommunityCardsNotify;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.publicCards){if(!Array.isArray(d.publicCards))throw TypeError(".jackfruit_proto.CommunityCardsNotify.publicCards: array expected");m.publicCards=[];for(var i=0;i<d.publicCards.length;++i){if(typeof d.publicCards[i]!=="object")throw TypeError(".jackfruit_proto.CommunityCardsNotify.publicCards: object expected");m.publicCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.publicCards[i])}}switch(d.roundState){case"RoomStates_DUMMY":case 0:m.roundState=0;break;case"Free":case 1:m.roundState=1;break;case"Ready":case 2:m.roundState=2;break;case"Wait":case 11:m.roundState=11;break;case"Deal":case 12:m.roundState=12;break;case"PlaceCards":case 13:m.roundState=13;break;case"Turn":case 14:m.roundState=14;break;case"River":case 18:m.roundState=18;break;case"Settlement":case 20:m.roundState=20;break}return m};CommunityCardsNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.publicCards=[]}if(o.defaults){d.roomId=0;d.roundState=o.enums===String?"RoomStates_DUMMY":0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.publicCards&&m.publicCards.length){d.publicCards=[];for(var j=0;j<m.publicCards.length;++j){d.publicCards[j]=$root.jackfruit_proto.CardItem.toObject(m.publicCards[j],o)}}if(m.roundState!=null&&m.hasOwnProperty("roundState")){d.roundState=o.enums===String?$root.jackfruit_proto.RoundState[m.roundState]:m.roundState}return d};CommunityCardsNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CommunityCardsNotify}();jackfruit_proto.GameRoundEndNotify=function(){function GameRoundEndNotify(p){this.playerSettle=[];this.pubCards=[];this.jackpotAwards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameRoundEndNotify.prototype.playerSettle=$util.emptyArray;GameRoundEndNotify.prototype.stopWorld=0;GameRoundEndNotify.prototype.pubCards=$util.emptyArray;GameRoundEndNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.nextStateStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.settleType=0;GameRoundEndNotify.prototype.onlyWinAmount=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.jackpotLeftAmount=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.jackpotAwards=$util.emptyArray;GameRoundEndNotify.prototype.game_uuid_js="";GameRoundEndNotify.create=function create(properties){return new GameRoundEndNotify(properties)};GameRoundEndNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerSettle!=null&&m.playerSettle.length){for(var i=0;i<m.playerSettle.length;++i)$root.jackfruit_proto.PlayerSettle.encode(m.playerSettle[i],w.uint32(10).fork()).ldelim()}if(m.stopWorld!=null&&Object.hasOwnProperty.call(m,"stopWorld"))w.uint32(16).int32(m.stopWorld);if(m.pubCards!=null&&m.pubCards.length){for(var i=0;i<m.pubCards.length;++i)$root.jackfruit_proto.CardItem.encode(m.pubCards[i],w.uint32(26).fork()).ldelim()}if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(32).int64(m.leftSeconds);if(m.nextStateStamp!=null&&Object.hasOwnProperty.call(m,"nextStateStamp"))w.uint32(40).int64(m.nextStateStamp);if(m.settleType!=null&&Object.hasOwnProperty.call(m,"settleType"))w.uint32(48).int32(m.settleType);if(m.onlyWinAmount!=null&&Object.hasOwnProperty.call(m,"onlyWinAmount"))w.uint32(56).int64(m.onlyWinAmount);if(m.jackpotLeftAmount!=null&&Object.hasOwnProperty.call(m,"jackpotLeftAmount"))w.uint32(64).int64(m.jackpotLeftAmount);if(m.jackpotAwards!=null&&m.jackpotAwards.length){for(var i=0;i<m.jackpotAwards.length;++i)$root.jackfruit_proto.JackpotAwardInfo.encode(m.jackpotAwards[i],w.uint32(74).fork()).ldelim()}if(m.game_uuid_js!=null&&Object.hasOwnProperty.call(m,"game_uuid_js"))w.uint32(82).string(m.game_uuid_js);return w};GameRoundEndNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameRoundEndNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GameRoundEndNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.playerSettle&&m.playerSettle.length))m.playerSettle=[];m.playerSettle.push($root.jackfruit_proto.PlayerSettle.decode(r,r.uint32()));break;case 2:m.stopWorld=r.int32();break;case 3:if(!(m.pubCards&&m.pubCards.length))m.pubCards=[];m.pubCards.push($root.jackfruit_proto.CardItem.decode(r,r.uint32()));break;case 4:m.leftSeconds=r.int64();break;case 5:m.nextStateStamp=r.int64();break;case 6:m.settleType=r.int32();break;case 7:m.onlyWinAmount=r.int64();break;case 8:m.jackpotLeftAmount=r.int64();break;case 9:if(!(m.jackpotAwards&&m.jackpotAwards.length))m.jackpotAwards=[];m.jackpotAwards.push($root.jackfruit_proto.JackpotAwardInfo.decode(r,r.uint32()));break;case 10:m.game_uuid_js=r.string();break;default:r.skipType(t&7);break}}return m};GameRoundEndNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameRoundEndNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerSettle!=null&&m.hasOwnProperty("playerSettle")){if(!Array.isArray(m.playerSettle))return"playerSettle: array expected";for(var i=0;i<m.playerSettle.length;++i){{var e=$root.jackfruit_proto.PlayerSettle.verify(m.playerSettle[i]);if(e)return"playerSettle."+e}}}if(m.stopWorld!=null&&m.hasOwnProperty("stopWorld")){if(!$util.isInteger(m.stopWorld))return"stopWorld: integer expected"}if(m.pubCards!=null&&m.hasOwnProperty("pubCards")){if(!Array.isArray(m.pubCards))return"pubCards: array expected";for(var i=0;i<m.pubCards.length;++i){{var e=$root.jackfruit_proto.CardItem.verify(m.pubCards[i]);if(e)return"pubCards."+e}}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(!$util.isInteger(m.nextStateStamp)&&!(m.nextStateStamp&&$util.isInteger(m.nextStateStamp.low)&&$util.isInteger(m.nextStateStamp.high)))return"nextStateStamp: integer|Long expected"}if(m.settleType!=null&&m.hasOwnProperty("settleType")){if(!$util.isInteger(m.settleType))return"settleType: integer expected"}if(m.onlyWinAmount!=null&&m.hasOwnProperty("onlyWinAmount")){if(!$util.isInteger(m.onlyWinAmount)&&!(m.onlyWinAmount&&$util.isInteger(m.onlyWinAmount.low)&&$util.isInteger(m.onlyWinAmount.high)))return"onlyWinAmount: integer|Long expected"}if(m.jackpotLeftAmount!=null&&m.hasOwnProperty("jackpotLeftAmount")){if(!$util.isInteger(m.jackpotLeftAmount)&&!(m.jackpotLeftAmount&&$util.isInteger(m.jackpotLeftAmount.low)&&$util.isInteger(m.jackpotLeftAmount.high)))return"jackpotLeftAmount: integer|Long expected"}if(m.jackpotAwards!=null&&m.hasOwnProperty("jackpotAwards")){if(!Array.isArray(m.jackpotAwards))return"jackpotAwards: array expected";for(var i=0;i<m.jackpotAwards.length;++i){{var e=$root.jackfruit_proto.JackpotAwardInfo.verify(m.jackpotAwards[i]);if(e)return"jackpotAwards."+e}}}if(m.game_uuid_js!=null&&m.hasOwnProperty("game_uuid_js")){if(!$util.isString(m.game_uuid_js))return"game_uuid_js: string expected"}return null};GameRoundEndNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GameRoundEndNotify)return d;var m=new $root.jackfruit_proto.GameRoundEndNotify;if(d.playerSettle){if(!Array.isArray(d.playerSettle))throw TypeError(".jackfruit_proto.GameRoundEndNotify.playerSettle: array expected");m.playerSettle=[];for(var i=0;i<d.playerSettle.length;++i){if(typeof d.playerSettle[i]!=="object")throw TypeError(".jackfruit_proto.GameRoundEndNotify.playerSettle: object expected");m.playerSettle[i]=$root.jackfruit_proto.PlayerSettle.fromObject(d.playerSettle[i])}}if(d.stopWorld!=null){m.stopWorld=d.stopWorld|0}if(d.pubCards){if(!Array.isArray(d.pubCards))throw TypeError(".jackfruit_proto.GameRoundEndNotify.pubCards: array expected");m.pubCards=[];for(var i=0;i<d.pubCards.length;++i){if(typeof d.pubCards[i]!=="object")throw TypeError(".jackfruit_proto.GameRoundEndNotify.pubCards: object expected");m.pubCards[i]=$root.jackfruit_proto.CardItem.fromObject(d.pubCards[i])}}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.nextStateStamp!=null){if($util.Long)(m.nextStateStamp=$util.Long.fromValue(d.nextStateStamp)).unsigned=false;else if(typeof d.nextStateStamp==="string")m.nextStateStamp=parseInt(d.nextStateStamp,10);else if(typeof d.nextStateStamp==="number")m.nextStateStamp=d.nextStateStamp;else if(typeof d.nextStateStamp==="object")m.nextStateStamp=new $util.LongBits(d.nextStateStamp.low>>>0,d.nextStateStamp.high>>>0).toNumber()}if(d.settleType!=null){m.settleType=d.settleType|0}if(d.onlyWinAmount!=null){if($util.Long)(m.onlyWinAmount=$util.Long.fromValue(d.onlyWinAmount)).unsigned=false;else if(typeof d.onlyWinAmount==="string")m.onlyWinAmount=parseInt(d.onlyWinAmount,10);else if(typeof d.onlyWinAmount==="number")m.onlyWinAmount=d.onlyWinAmount;else if(typeof d.onlyWinAmount==="object")m.onlyWinAmount=new $util.LongBits(d.onlyWinAmount.low>>>0,d.onlyWinAmount.high>>>0).toNumber()}if(d.jackpotLeftAmount!=null){if($util.Long)(m.jackpotLeftAmount=$util.Long.fromValue(d.jackpotLeftAmount)).unsigned=false;else if(typeof d.jackpotLeftAmount==="string")m.jackpotLeftAmount=parseInt(d.jackpotLeftAmount,10);else if(typeof d.jackpotLeftAmount==="number")m.jackpotLeftAmount=d.jackpotLeftAmount;else if(typeof d.jackpotLeftAmount==="object")m.jackpotLeftAmount=new $util.LongBits(d.jackpotLeftAmount.low>>>0,d.jackpotLeftAmount.high>>>0).toNumber()}if(d.jackpotAwards){if(!Array.isArray(d.jackpotAwards))throw TypeError(".jackfruit_proto.GameRoundEndNotify.jackpotAwards: array expected");m.jackpotAwards=[];for(var i=0;i<d.jackpotAwards.length;++i){if(typeof d.jackpotAwards[i]!=="object")throw TypeError(".jackfruit_proto.GameRoundEndNotify.jackpotAwards: object expected");m.jackpotAwards[i]=$root.jackfruit_proto.JackpotAwardInfo.fromObject(d.jackpotAwards[i])}}if(d.game_uuid_js!=null){m.game_uuid_js=String(d.game_uuid_js)}return m};GameRoundEndNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.playerSettle=[];d.pubCards=[];d.jackpotAwards=[]}if(o.defaults){d.stopWorld=0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextStateStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextStateStamp=o.longs===String?"0":0;d.settleType=0;if($util.Long){var n=new $util.Long(0,0,false);d.onlyWinAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.onlyWinAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.jackpotLeftAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.jackpotLeftAmount=o.longs===String?"0":0;d.game_uuid_js=""}if(m.playerSettle&&m.playerSettle.length){d.playerSettle=[];for(var j=0;j<m.playerSettle.length;++j){d.playerSettle[j]=$root.jackfruit_proto.PlayerSettle.toObject(m.playerSettle[j],o)}}if(m.stopWorld!=null&&m.hasOwnProperty("stopWorld")){d.stopWorld=m.stopWorld}if(m.pubCards&&m.pubCards.length){d.pubCards=[];for(var j=0;j<m.pubCards.length;++j){d.pubCards[j]=$root.jackfruit_proto.CardItem.toObject(m.pubCards[j],o)}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(typeof m.nextStateStamp==="number")d.nextStateStamp=o.longs===String?String(m.nextStateStamp):m.nextStateStamp;else d.nextStateStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextStateStamp):o.longs===Number?new $util.LongBits(m.nextStateStamp.low>>>0,m.nextStateStamp.high>>>0).toNumber():m.nextStateStamp}if(m.settleType!=null&&m.hasOwnProperty("settleType")){d.settleType=m.settleType}if(m.onlyWinAmount!=null&&m.hasOwnProperty("onlyWinAmount")){if(typeof m.onlyWinAmount==="number")d.onlyWinAmount=o.longs===String?String(m.onlyWinAmount):m.onlyWinAmount;else d.onlyWinAmount=o.longs===String?$util.Long.prototype.toString.call(m.onlyWinAmount):o.longs===Number?new $util.LongBits(m.onlyWinAmount.low>>>0,m.onlyWinAmount.high>>>0).toNumber():m.onlyWinAmount}if(m.jackpotLeftAmount!=null&&m.hasOwnProperty("jackpotLeftAmount")){if(typeof m.jackpotLeftAmount==="number")d.jackpotLeftAmount=o.longs===String?String(m.jackpotLeftAmount):m.jackpotLeftAmount;else d.jackpotLeftAmount=o.longs===String?$util.Long.prototype.toString.call(m.jackpotLeftAmount):o.longs===Number?new $util.LongBits(m.jackpotLeftAmount.low>>>0,m.jackpotLeftAmount.high>>>0).toNumber():m.jackpotLeftAmount}if(m.jackpotAwards&&m.jackpotAwards.length){d.jackpotAwards=[];for(var j=0;j<m.jackpotAwards.length;++j){d.jackpotAwards[j]=$root.jackfruit_proto.JackpotAwardInfo.toObject(m.jackpotAwards[j],o)}}if(m.game_uuid_js!=null&&m.hasOwnProperty("game_uuid_js")){d.game_uuid_js=m.game_uuid_js}return d};GameRoundEndNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameRoundEndNotify}();jackfruit_proto.ConfirmToContinueNotify=function(){function ConfirmToContinueNotify(p){this.playerId=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ConfirmToContinueNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;ConfirmToContinueNotify.prototype.nextStateStamp=$util.Long?$util.Long.fromBits(0,0,false):0;ConfirmToContinueNotify.prototype.playerId=$util.emptyArray;ConfirmToContinueNotify.create=function create(properties){return new ConfirmToContinueNotify(properties)};ConfirmToContinueNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(8).int64(m.leftSeconds);if(m.nextStateStamp!=null&&Object.hasOwnProperty.call(m,"nextStateStamp"))w.uint32(16).int64(m.nextStateStamp);if(m.playerId!=null&&m.playerId.length){w.uint32(26).fork();for(var i=0;i<m.playerId.length;++i)w.uint32(m.playerId[i]);w.ldelim()}return w};ConfirmToContinueNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ConfirmToContinueNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ConfirmToContinueNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.leftSeconds=r.int64();break;case 2:m.nextStateStamp=r.int64();break;case 3:if(!(m.playerId&&m.playerId.length))m.playerId=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.playerId.push(r.uint32())}else m.playerId.push(r.uint32());break;default:r.skipType(t&7);break}}return m};ConfirmToContinueNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ConfirmToContinueNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(!$util.isInteger(m.nextStateStamp)&&!(m.nextStateStamp&&$util.isInteger(m.nextStateStamp.low)&&$util.isInteger(m.nextStateStamp.high)))return"nextStateStamp: integer|Long expected"}if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!Array.isArray(m.playerId))return"playerId: array expected";for(var i=0;i<m.playerId.length;++i){if(!$util.isInteger(m.playerId[i]))return"playerId: integer[] expected"}}return null};ConfirmToContinueNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ConfirmToContinueNotify)return d;var m=new $root.jackfruit_proto.ConfirmToContinueNotify;if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.nextStateStamp!=null){if($util.Long)(m.nextStateStamp=$util.Long.fromValue(d.nextStateStamp)).unsigned=false;else if(typeof d.nextStateStamp==="string")m.nextStateStamp=parseInt(d.nextStateStamp,10);else if(typeof d.nextStateStamp==="number")m.nextStateStamp=d.nextStateStamp;else if(typeof d.nextStateStamp==="object")m.nextStateStamp=new $util.LongBits(d.nextStateStamp.low>>>0,d.nextStateStamp.high>>>0).toNumber()}if(d.playerId){if(!Array.isArray(d.playerId))throw TypeError(".jackfruit_proto.ConfirmToContinueNotify.playerId: array expected");m.playerId=[];for(var i=0;i<d.playerId.length;++i){m.playerId[i]=d.playerId[i]>>>0}}return m};ConfirmToContinueNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.playerId=[]}if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextStateStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextStateStamp=o.longs===String?"0":0}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.nextStateStamp!=null&&m.hasOwnProperty("nextStateStamp")){if(typeof m.nextStateStamp==="number")d.nextStateStamp=o.longs===String?String(m.nextStateStamp):m.nextStateStamp;else d.nextStateStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextStateStamp):o.longs===Number?new $util.LongBits(m.nextStateStamp.low>>>0,m.nextStateStamp.high>>>0).toNumber():m.nextStateStamp}if(m.playerId&&m.playerId.length){d.playerId=[];for(var j=0;j<m.playerId.length;++j){d.playerId[j]=m.playerId[j]}}return d};ConfirmToContinueNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ConfirmToContinueNotify}();jackfruit_proto.CardItem=function(){function CardItem(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CardItem.prototype.number=0;CardItem.prototype.suit=0;CardItem.create=function create(properties){return new CardItem(properties)};CardItem.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.number!=null&&Object.hasOwnProperty.call(m,"number"))w.uint32(8).int32(m.number);if(m.suit!=null&&Object.hasOwnProperty.call(m,"suit"))w.uint32(16).int32(m.suit);return w};CardItem.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CardItem.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.CardItem;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.number=r.int32();break;case 2:m.suit=r.int32();break;default:r.skipType(t&7);break}}return m};CardItem.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CardItem.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.number!=null&&m.hasOwnProperty("number")){if(!$util.isInteger(m.number))return"number: integer expected"}if(m.suit!=null&&m.hasOwnProperty("suit")){if(!$util.isInteger(m.suit))return"suit: integer expected"}return null};CardItem.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.CardItem)return d;var m=new $root.jackfruit_proto.CardItem;if(d.number!=null){m.number=d.number|0}if(d.suit!=null){m.suit=d.suit|0}return m};CardItem.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.number=0;d.suit=0}if(m.number!=null&&m.hasOwnProperty("number")){d.number=m.number}if(m.suit!=null&&m.hasOwnProperty("suit")){d.suit=m.suit}return d};CardItem.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CardItem}();jackfruit_proto.PlaceResult=function(){function PlaceResult(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlaceResult.prototype.score=0;PlaceResult.prototype.result=0;PlaceResult.prototype.level=0;PlaceResult.prototype.zoneMultiple=0;PlaceResult.prototype.levelScore=0;PlaceResult.create=function create(properties){return new PlaceResult(properties)};PlaceResult.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.score!=null&&Object.hasOwnProperty.call(m,"score"))w.uint32(16).int32(m.score);if(m.result!=null&&Object.hasOwnProperty.call(m,"result"))w.uint32(24).int32(m.result);if(m.level!=null&&Object.hasOwnProperty.call(m,"level"))w.uint32(32).int32(m.level);if(m.zoneMultiple!=null&&Object.hasOwnProperty.call(m,"zoneMultiple"))w.uint32(40).int32(m.zoneMultiple);if(m.levelScore!=null&&Object.hasOwnProperty.call(m,"levelScore"))w.uint32(48).int32(m.levelScore);return w};PlaceResult.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlaceResult.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlaceResult;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 2:m.score=r.int32();break;case 3:m.result=r.int32();break;case 4:m.level=r.int32();break;case 5:m.zoneMultiple=r.int32();break;case 6:m.levelScore=r.int32();break;default:r.skipType(t&7);break}}return m};PlaceResult.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlaceResult.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.score!=null&&m.hasOwnProperty("score")){if(!$util.isInteger(m.score))return"score: integer expected"}if(m.result!=null&&m.hasOwnProperty("result")){if(!$util.isInteger(m.result))return"result: integer expected"}if(m.level!=null&&m.hasOwnProperty("level")){switch(m.level){default:return"level: enum value expected";case 0:case 10:case 9:case 8:case 7:case 6:case 5:case 4:case 3:case 2:case 1:break}}if(m.zoneMultiple!=null&&m.hasOwnProperty("zoneMultiple")){if(!$util.isInteger(m.zoneMultiple))return"zoneMultiple: integer expected"}if(m.levelScore!=null&&m.hasOwnProperty("levelScore")){if(!$util.isInteger(m.levelScore))return"levelScore: integer expected"}return null};PlaceResult.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlaceResult)return d;var m=new $root.jackfruit_proto.PlaceResult;if(d.score!=null){m.score=d.score|0}if(d.result!=null){m.result=d.result|0}switch(d.level){case"Dump":case 0:m.level=0;break;case"RoyalFlush":case 10:m.level=10;break;case"StraightFlush":case 9:m.level=9;break;case"FourOfAKind":case 8:m.level=8;break;case"FullHouse":case 7:m.level=7;break;case"Flush":case 6:m.level=6;break;case"StraightI":case 5:m.level=5;break;case"ThreeOfAKind":case 4:m.level=4;break;case"TwoPair":case 3:m.level=3;break;case"OnePair":case 2:m.level=2;break;case"HighCard":case 1:m.level=1;break}if(d.zoneMultiple!=null){m.zoneMultiple=d.zoneMultiple|0}if(d.levelScore!=null){m.levelScore=d.levelScore|0}return m};PlaceResult.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.score=0;d.result=0;d.level=o.enums===String?"Dump":0;d.zoneMultiple=0;d.levelScore=0}if(m.score!=null&&m.hasOwnProperty("score")){d.score=m.score}if(m.result!=null&&m.hasOwnProperty("result")){d.result=m.result}if(m.level!=null&&m.hasOwnProperty("level")){d.level=o.enums===String?$root.jackfruit_proto.CardLevel[m.level]:m.level}if(m.zoneMultiple!=null&&m.hasOwnProperty("zoneMultiple")){d.zoneMultiple=m.zoneMultiple}if(m.levelScore!=null&&m.hasOwnProperty("levelScore")){d.levelScore=m.levelScore}return d};PlaceResult.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlaceResult}();jackfruit_proto.PlayerSettle=function(){function PlayerSettle(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerSettle.prototype.player=null;PlayerSettle.prototype.headResult=null;PlayerSettle.prototype.middleResult=null;PlayerSettle.prototype.tailResult=null;PlayerSettle.prototype.repeatWining=0;PlayerSettle.prototype.winAllAward=0;PlayerSettle.prototype.totalScore=0;PlayerSettle.prototype.winAmount=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerSettle.prototype.winScore=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerSettle.create=function create(properties){return new PlayerSettle(properties)};PlayerSettle.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.player!=null&&Object.hasOwnProperty.call(m,"player"))$root.jackfruit_proto.PlayerInfo.encode(m.player,w.uint32(10).fork()).ldelim();if(m.headResult!=null&&Object.hasOwnProperty.call(m,"headResult"))$root.jackfruit_proto.PlaceResult.encode(m.headResult,w.uint32(50).fork()).ldelim();if(m.middleResult!=null&&Object.hasOwnProperty.call(m,"middleResult"))$root.jackfruit_proto.PlaceResult.encode(m.middleResult,w.uint32(58).fork()).ldelim();if(m.tailResult!=null&&Object.hasOwnProperty.call(m,"tailResult"))$root.jackfruit_proto.PlaceResult.encode(m.tailResult,w.uint32(66).fork()).ldelim();if(m.repeatWining!=null&&Object.hasOwnProperty.call(m,"repeatWining"))w.uint32(72).int32(m.repeatWining);if(m.winAllAward!=null&&Object.hasOwnProperty.call(m,"winAllAward"))w.uint32(80).uint32(m.winAllAward);if(m.totalScore!=null&&Object.hasOwnProperty.call(m,"totalScore"))w.uint32(120).int32(m.totalScore);if(m.winAmount!=null&&Object.hasOwnProperty.call(m,"winAmount"))w.uint32(128).int64(m.winAmount);if(m.winScore!=null&&Object.hasOwnProperty.call(m,"winScore"))w.uint32(144).int64(m.winScore);return w};PlayerSettle.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerSettle.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlayerSettle;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.player=$root.jackfruit_proto.PlayerInfo.decode(r,r.uint32());break;case 6:m.headResult=$root.jackfruit_proto.PlaceResult.decode(r,r.uint32());break;case 7:m.middleResult=$root.jackfruit_proto.PlaceResult.decode(r,r.uint32());break;case 8:m.tailResult=$root.jackfruit_proto.PlaceResult.decode(r,r.uint32());break;case 9:m.repeatWining=r.int32();break;case 10:m.winAllAward=r.uint32();break;case 15:m.totalScore=r.int32();break;case 16:m.winAmount=r.int64();break;case 18:m.winScore=r.int64();break;default:r.skipType(t&7);break}}return m};PlayerSettle.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerSettle.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.player!=null&&m.hasOwnProperty("player")){{var e=$root.jackfruit_proto.PlayerInfo.verify(m.player);if(e)return"player."+e}}if(m.headResult!=null&&m.hasOwnProperty("headResult")){{var e=$root.jackfruit_proto.PlaceResult.verify(m.headResult);if(e)return"headResult."+e}}if(m.middleResult!=null&&m.hasOwnProperty("middleResult")){{var e=$root.jackfruit_proto.PlaceResult.verify(m.middleResult);if(e)return"middleResult."+e}}if(m.tailResult!=null&&m.hasOwnProperty("tailResult")){{var e=$root.jackfruit_proto.PlaceResult.verify(m.tailResult);if(e)return"tailResult."+e}}if(m.repeatWining!=null&&m.hasOwnProperty("repeatWining")){if(!$util.isInteger(m.repeatWining))return"repeatWining: integer expected"}if(m.winAllAward!=null&&m.hasOwnProperty("winAllAward")){if(!$util.isInteger(m.winAllAward))return"winAllAward: integer expected"}if(m.totalScore!=null&&m.hasOwnProperty("totalScore")){if(!$util.isInteger(m.totalScore))return"totalScore: integer expected"}if(m.winAmount!=null&&m.hasOwnProperty("winAmount")){if(!$util.isInteger(m.winAmount)&&!(m.winAmount&&$util.isInteger(m.winAmount.low)&&$util.isInteger(m.winAmount.high)))return"winAmount: integer|Long expected"}if(m.winScore!=null&&m.hasOwnProperty("winScore")){if(!$util.isInteger(m.winScore)&&!(m.winScore&&$util.isInteger(m.winScore.low)&&$util.isInteger(m.winScore.high)))return"winScore: integer|Long expected"}return null};PlayerSettle.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlayerSettle)return d;var m=new $root.jackfruit_proto.PlayerSettle;if(d.player!=null){if(typeof d.player!=="object")throw TypeError(".jackfruit_proto.PlayerSettle.player: object expected");m.player=$root.jackfruit_proto.PlayerInfo.fromObject(d.player)}if(d.headResult!=null){if(typeof d.headResult!=="object")throw TypeError(".jackfruit_proto.PlayerSettle.headResult: object expected");m.headResult=$root.jackfruit_proto.PlaceResult.fromObject(d.headResult)}if(d.middleResult!=null){if(typeof d.middleResult!=="object")throw TypeError(".jackfruit_proto.PlayerSettle.middleResult: object expected");m.middleResult=$root.jackfruit_proto.PlaceResult.fromObject(d.middleResult)}if(d.tailResult!=null){if(typeof d.tailResult!=="object")throw TypeError(".jackfruit_proto.PlayerSettle.tailResult: object expected");m.tailResult=$root.jackfruit_proto.PlaceResult.fromObject(d.tailResult)}if(d.repeatWining!=null){m.repeatWining=d.repeatWining|0}if(d.winAllAward!=null){m.winAllAward=d.winAllAward>>>0}if(d.totalScore!=null){m.totalScore=d.totalScore|0}if(d.winAmount!=null){if($util.Long)(m.winAmount=$util.Long.fromValue(d.winAmount)).unsigned=false;else if(typeof d.winAmount==="string")m.winAmount=parseInt(d.winAmount,10);else if(typeof d.winAmount==="number")m.winAmount=d.winAmount;else if(typeof d.winAmount==="object")m.winAmount=new $util.LongBits(d.winAmount.low>>>0,d.winAmount.high>>>0).toNumber()}if(d.winScore!=null){if($util.Long)(m.winScore=$util.Long.fromValue(d.winScore)).unsigned=false;else if(typeof d.winScore==="string")m.winScore=parseInt(d.winScore,10);else if(typeof d.winScore==="number")m.winScore=d.winScore;else if(typeof d.winScore==="object")m.winScore=new $util.LongBits(d.winScore.low>>>0,d.winScore.high>>>0).toNumber()}return m};PlayerSettle.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.player=null;d.headResult=null;d.middleResult=null;d.tailResult=null;d.repeatWining=0;d.winAllAward=0;d.totalScore=0;if($util.Long){var n=new $util.Long(0,0,false);d.winAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.winAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.winScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.winScore=o.longs===String?"0":0}if(m.player!=null&&m.hasOwnProperty("player")){d.player=$root.jackfruit_proto.PlayerInfo.toObject(m.player,o)}if(m.headResult!=null&&m.hasOwnProperty("headResult")){d.headResult=$root.jackfruit_proto.PlaceResult.toObject(m.headResult,o)}if(m.middleResult!=null&&m.hasOwnProperty("middleResult")){d.middleResult=$root.jackfruit_proto.PlaceResult.toObject(m.middleResult,o)}if(m.tailResult!=null&&m.hasOwnProperty("tailResult")){d.tailResult=$root.jackfruit_proto.PlaceResult.toObject(m.tailResult,o)}if(m.repeatWining!=null&&m.hasOwnProperty("repeatWining")){d.repeatWining=m.repeatWining}if(m.winAllAward!=null&&m.hasOwnProperty("winAllAward")){d.winAllAward=m.winAllAward}if(m.totalScore!=null&&m.hasOwnProperty("totalScore")){d.totalScore=m.totalScore}if(m.winAmount!=null&&m.hasOwnProperty("winAmount")){if(typeof m.winAmount==="number")d.winAmount=o.longs===String?String(m.winAmount):m.winAmount;else d.winAmount=o.longs===String?$util.Long.prototype.toString.call(m.winAmount):o.longs===Number?new $util.LongBits(m.winAmount.low>>>0,m.winAmount.high>>>0).toNumber():m.winAmount}if(m.winScore!=null&&m.hasOwnProperty("winScore")){if(typeof m.winScore==="number")d.winScore=o.longs===String?String(m.winScore):m.winScore;else d.winScore=o.longs===String?$util.Long.prototype.toString.call(m.winScore):o.longs===Number?new $util.LongBits(m.winScore.low>>>0,m.winScore.high>>>0).toNumber():m.winScore}return d};PlayerSettle.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerSettle}();jackfruit_proto.ChatType=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Enum_Emoji"]=0;values[valuesById[1]="Enum_Voice"]=1;values[valuesById[2]="Enum_Emoji_Interactive"]=2;values[valuesById[3]="Enum_Barrage"]=3;return values}();jackfruit_proto.EmojiType=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Attack"]=0;values[valuesById[1]="Welcome"]=1;values[valuesById[2]="InterActiveNormal"]=2;return values}();jackfruit_proto.SendChatReq=function(){function SendChatReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SendChatReq.prototype.roomId=0;SendChatReq.prototype.cType=0;SendChatReq.prototype.content="";SendChatReq.prototype.change_voice=0;SendChatReq.prototype.emoji_type=0;SendChatReq.prototype.useItemId=0;SendChatReq.create=function create(properties){return new SendChatReq(properties)};SendChatReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.cType!=null&&Object.hasOwnProperty.call(m,"cType"))w.uint32(16).int32(m.cType);if(m.content!=null&&Object.hasOwnProperty.call(m,"content"))w.uint32(26).string(m.content);if(m.change_voice!=null&&Object.hasOwnProperty.call(m,"change_voice"))w.uint32(32).int32(m.change_voice);if(m.emoji_type!=null&&Object.hasOwnProperty.call(m,"emoji_type"))w.uint32(40).int32(m.emoji_type);if(m.useItemId!=null&&Object.hasOwnProperty.call(m,"useItemId"))w.uint32(48).uint32(m.useItemId);return w};SendChatReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SendChatReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SendChatReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:m.cType=r.int32();break;case 3:m.content=r.string();break;case 4:m.change_voice=r.int32();break;case 5:m.emoji_type=r.int32();break;case 6:m.useItemId=r.uint32();break;default:r.skipType(t&7);break}}return m};SendChatReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SendChatReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.cType!=null&&m.hasOwnProperty("cType")){switch(m.cType){default:return"cType: enum value expected";case 0:case 1:case 2:case 3:break}}if(m.content!=null&&m.hasOwnProperty("content")){if(!$util.isString(m.content))return"content: string expected"}if(m.change_voice!=null&&m.hasOwnProperty("change_voice")){if(!$util.isInteger(m.change_voice))return"change_voice: integer expected"}if(m.emoji_type!=null&&m.hasOwnProperty("emoji_type")){switch(m.emoji_type){default:return"emoji_type: enum value expected";case 0:case 1:case 2:break}}if(m.useItemId!=null&&m.hasOwnProperty("useItemId")){if(!$util.isInteger(m.useItemId))return"useItemId: integer expected"}return null};SendChatReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SendChatReq)return d;var m=new $root.jackfruit_proto.SendChatReq;if(d.roomId!=null){m.roomId=d.roomId|0}switch(d.cType){case"Enum_Emoji":case 0:m.cType=0;break;case"Enum_Voice":case 1:m.cType=1;break;case"Enum_Emoji_Interactive":case 2:m.cType=2;break;case"Enum_Barrage":case 3:m.cType=3;break}if(d.content!=null){m.content=String(d.content)}if(d.change_voice!=null){m.change_voice=d.change_voice|0}switch(d.emoji_type){case"Attack":case 0:m.emoji_type=0;break;case"Welcome":case 1:m.emoji_type=1;break;case"InterActiveNormal":case 2:m.emoji_type=2;break}if(d.useItemId!=null){m.useItemId=d.useItemId>>>0}return m};SendChatReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0;d.cType=o.enums===String?"Enum_Emoji":0;d.content="";d.change_voice=0;d.emoji_type=o.enums===String?"Attack":0;d.useItemId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.cType!=null&&m.hasOwnProperty("cType")){d.cType=o.enums===String?$root.jackfruit_proto.ChatType[m.cType]:m.cType}if(m.content!=null&&m.hasOwnProperty("content")){d.content=m.content}if(m.change_voice!=null&&m.hasOwnProperty("change_voice")){d.change_voice=m.change_voice}if(m.emoji_type!=null&&m.hasOwnProperty("emoji_type")){d.emoji_type=o.enums===String?$root.jackfruit_proto.EmojiType[m.emoji_type]:m.emoji_type}if(m.useItemId!=null&&m.hasOwnProperty("useItemId")){d.useItemId=m.useItemId}return d};SendChatReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SendChatReq}();jackfruit_proto.SendChatResp=function(){function SendChatResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SendChatResp.prototype.code=0;SendChatResp.prototype.nextFee=null;SendChatResp.prototype.barrageLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;SendChatResp.create=function create(properties){return new SendChatResp(properties)};SendChatResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.nextFee!=null&&Object.hasOwnProperty.call(m,"nextFee"))$root.jackfruit_proto.PayMoneyItems.encode(m.nextFee,w.uint32(18).fork()).ldelim();if(m.barrageLeftSeconds!=null&&Object.hasOwnProperty.call(m,"barrageLeftSeconds"))w.uint32(24).int64(m.barrageLeftSeconds);return w};SendChatResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SendChatResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SendChatResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.nextFee=$root.jackfruit_proto.PayMoneyItems.decode(r,r.uint32());break;case 3:m.barrageLeftSeconds=r.int64();break;default:r.skipType(t&7);break}}return m};SendChatResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SendChatResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.nextFee!=null&&m.hasOwnProperty("nextFee")){{var e=$root.jackfruit_proto.PayMoneyItems.verify(m.nextFee);if(e)return"nextFee."+e}}if(m.barrageLeftSeconds!=null&&m.hasOwnProperty("barrageLeftSeconds")){if(!$util.isInteger(m.barrageLeftSeconds)&&!(m.barrageLeftSeconds&&$util.isInteger(m.barrageLeftSeconds.low)&&$util.isInteger(m.barrageLeftSeconds.high)))return"barrageLeftSeconds: integer|Long expected"}return null};SendChatResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SendChatResp)return d;var m=new $root.jackfruit_proto.SendChatResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.nextFee!=null){if(typeof d.nextFee!=="object")throw TypeError(".jackfruit_proto.SendChatResp.nextFee: object expected");m.nextFee=$root.jackfruit_proto.PayMoneyItems.fromObject(d.nextFee)}if(d.barrageLeftSeconds!=null){if($util.Long)(m.barrageLeftSeconds=$util.Long.fromValue(d.barrageLeftSeconds)).unsigned=false;else if(typeof d.barrageLeftSeconds==="string")m.barrageLeftSeconds=parseInt(d.barrageLeftSeconds,10);else if(typeof d.barrageLeftSeconds==="number")m.barrageLeftSeconds=d.barrageLeftSeconds;else if(typeof d.barrageLeftSeconds==="object")m.barrageLeftSeconds=new $util.LongBits(d.barrageLeftSeconds.low>>>0,d.barrageLeftSeconds.high>>>0).toNumber()}return m};SendChatResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.nextFee=null;if($util.Long){var n=new $util.Long(0,0,false);d.barrageLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.barrageLeftSeconds=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.nextFee!=null&&m.hasOwnProperty("nextFee")){d.nextFee=$root.jackfruit_proto.PayMoneyItems.toObject(m.nextFee,o)}if(m.barrageLeftSeconds!=null&&m.hasOwnProperty("barrageLeftSeconds")){if(typeof m.barrageLeftSeconds==="number")d.barrageLeftSeconds=o.longs===String?String(m.barrageLeftSeconds):m.barrageLeftSeconds;else d.barrageLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.barrageLeftSeconds):o.longs===Number?new $util.LongBits(m.barrageLeftSeconds.low>>>0,m.barrageLeftSeconds.high>>>0).toNumber():m.barrageLeftSeconds}return d};SendChatResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SendChatResp}();jackfruit_proto.PayMoneyItems=function(){function PayMoneyItems(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PayMoneyItems.prototype.emotionFee=0;PayMoneyItems.prototype.emotionFee2=0;PayMoneyItems.prototype.magicEmojiFee=0;PayMoneyItems.create=function create(properties){return new PayMoneyItems(properties)};PayMoneyItems.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.emotionFee!=null&&Object.hasOwnProperty.call(m,"emotionFee"))w.uint32(8).int32(m.emotionFee);if(m.emotionFee2!=null&&Object.hasOwnProperty.call(m,"emotionFee2"))w.uint32(16).int32(m.emotionFee2);if(m.magicEmojiFee!=null&&Object.hasOwnProperty.call(m,"magicEmojiFee"))w.uint32(24).int32(m.magicEmojiFee);return w};PayMoneyItems.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PayMoneyItems.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PayMoneyItems;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.emotionFee=r.int32();break;case 2:m.emotionFee2=r.int32();break;case 3:m.magicEmojiFee=r.int32();break;default:r.skipType(t&7);break}}return m};PayMoneyItems.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PayMoneyItems.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.emotionFee!=null&&m.hasOwnProperty("emotionFee")){if(!$util.isInteger(m.emotionFee))return"emotionFee: integer expected"}if(m.emotionFee2!=null&&m.hasOwnProperty("emotionFee2")){if(!$util.isInteger(m.emotionFee2))return"emotionFee2: integer expected"}if(m.magicEmojiFee!=null&&m.hasOwnProperty("magicEmojiFee")){if(!$util.isInteger(m.magicEmojiFee))return"magicEmojiFee: integer expected"}return null};PayMoneyItems.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PayMoneyItems)return d;var m=new $root.jackfruit_proto.PayMoneyItems;if(d.emotionFee!=null){m.emotionFee=d.emotionFee|0}if(d.emotionFee2!=null){m.emotionFee2=d.emotionFee2|0}if(d.magicEmojiFee!=null){m.magicEmojiFee=d.magicEmojiFee|0}return m};PayMoneyItems.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.emotionFee=0;d.emotionFee2=0;d.magicEmojiFee=0}if(m.emotionFee!=null&&m.hasOwnProperty("emotionFee")){d.emotionFee=m.emotionFee}if(m.emotionFee2!=null&&m.hasOwnProperty("emotionFee2")){d.emotionFee2=m.emotionFee2}if(m.magicEmojiFee!=null&&m.hasOwnProperty("magicEmojiFee")){d.magicEmojiFee=m.magicEmojiFee}return d};PayMoneyItems.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PayMoneyItems}();jackfruit_proto.SendChatNotify=function(){function SendChatNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SendChatNotify.prototype.roomId=0;SendChatNotify.prototype.cType=0;SendChatNotify.prototype.content="";SendChatNotify.prototype.playerId=0;SendChatNotify.prototype.seatId=0;SendChatNotify.prototype.change_voice=0;SendChatNotify.prototype.emoji_type=0;SendChatNotify.create=function create(properties){return new SendChatNotify(properties)};SendChatNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.cType!=null&&Object.hasOwnProperty.call(m,"cType"))w.uint32(16).int32(m.cType);if(m.content!=null&&Object.hasOwnProperty.call(m,"content"))w.uint32(26).string(m.content);if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(32).uint32(m.playerId);if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(40).int32(m.seatId);if(m.change_voice!=null&&Object.hasOwnProperty.call(m,"change_voice"))w.uint32(48).int32(m.change_voice);if(m.emoji_type!=null&&Object.hasOwnProperty.call(m,"emoji_type"))w.uint32(56).int32(m.emoji_type);return w};SendChatNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SendChatNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SendChatNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:m.cType=r.int32();break;case 3:m.content=r.string();break;case 4:m.playerId=r.uint32();break;case 5:m.seatId=r.int32();break;case 6:m.change_voice=r.int32();break;case 7:m.emoji_type=r.int32();break;default:r.skipType(t&7);break}}return m};SendChatNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SendChatNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.cType!=null&&m.hasOwnProperty("cType")){switch(m.cType){default:return"cType: enum value expected";case 0:case 1:case 2:case 3:break}}if(m.content!=null&&m.hasOwnProperty("content")){if(!$util.isString(m.content))return"content: string expected"}if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}if(m.change_voice!=null&&m.hasOwnProperty("change_voice")){if(!$util.isInteger(m.change_voice))return"change_voice: integer expected"}if(m.emoji_type!=null&&m.hasOwnProperty("emoji_type")){switch(m.emoji_type){default:return"emoji_type: enum value expected";case 0:case 1:case 2:break}}return null};SendChatNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SendChatNotify)return d;var m=new $root.jackfruit_proto.SendChatNotify;if(d.roomId!=null){m.roomId=d.roomId|0}switch(d.cType){case"Enum_Emoji":case 0:m.cType=0;break;case"Enum_Voice":case 1:m.cType=1;break;case"Enum_Emoji_Interactive":case 2:m.cType=2;break;case"Enum_Barrage":case 3:m.cType=3;break}if(d.content!=null){m.content=String(d.content)}if(d.playerId!=null){m.playerId=d.playerId>>>0}if(d.seatId!=null){m.seatId=d.seatId|0}if(d.change_voice!=null){m.change_voice=d.change_voice|0}switch(d.emoji_type){case"Attack":case 0:m.emoji_type=0;break;case"Welcome":case 1:m.emoji_type=1;break;case"InterActiveNormal":case 2:m.emoji_type=2;break}return m};SendChatNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0;d.cType=o.enums===String?"Enum_Emoji":0;d.content="";d.playerId=0;d.seatId=0;d.change_voice=0;d.emoji_type=o.enums===String?"Attack":0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.cType!=null&&m.hasOwnProperty("cType")){d.cType=o.enums===String?$root.jackfruit_proto.ChatType[m.cType]:m.cType}if(m.content!=null&&m.hasOwnProperty("content")){d.content=m.content}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}if(m.change_voice!=null&&m.hasOwnProperty("change_voice")){d.change_voice=m.change_voice}if(m.emoji_type!=null&&m.hasOwnProperty("emoji_type")){d.emoji_type=o.enums===String?$root.jackfruit_proto.EmojiType[m.emoji_type]:m.emoji_type}return d};SendChatNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SendChatNotify}();jackfruit_proto.ReadyReq=function(){function ReadyReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ReadyReq.create=function create(properties){return new ReadyReq(properties)};ReadyReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};ReadyReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ReadyReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ReadyReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};ReadyReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ReadyReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};ReadyReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ReadyReq)return d;return new $root.jackfruit_proto.ReadyReq};ReadyReq.toObject=function toObject(){return{}};ReadyReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ReadyReq}();jackfruit_proto.ReadyResp=function(){function ReadyResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ReadyResp.prototype.code=0;ReadyResp.create=function create(properties){return new ReadyResp(properties)};ReadyResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};ReadyResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ReadyResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ReadyResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};ReadyResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ReadyResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}return null};ReadyResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ReadyResp)return d;var m=new $root.jackfruit_proto.ReadyResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};ReadyResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}return d};ReadyResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ReadyResp}();jackfruit_proto.ReadyNotify=function(){function ReadyNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ReadyNotify.prototype.seatId=0;ReadyNotify.prototype.playerId=0;ReadyNotify.create=function create(properties){return new ReadyNotify(properties)};ReadyNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(8).int32(m.seatId);if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(16).uint32(m.playerId);return w};ReadyNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ReadyNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ReadyNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.seatId=r.int32();break;case 2:m.playerId=r.uint32();break;default:r.skipType(t&7);break}}return m};ReadyNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ReadyNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}return null};ReadyNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ReadyNotify)return d;var m=new $root.jackfruit_proto.ReadyNotify;if(d.seatId!=null){m.seatId=d.seatId|0}if(d.playerId!=null){m.playerId=d.playerId>>>0}return m};ReadyNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.seatId=0;d.playerId=0}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}return d};ReadyNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ReadyNotify}();jackfruit_proto.BuyInReq=function(){function BuyInReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BuyInReq.prototype.amount=$util.Long?$util.Long.fromBits(0,0,false):0;BuyInReq.prototype.seatId=0;BuyInReq.prototype.afterSeat=false;BuyInReq.prototype.score=$util.Long?$util.Long.fromBits(0,0,false):0;BuyInReq.create=function create(properties){return new BuyInReq(properties)};BuyInReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.amount!=null&&Object.hasOwnProperty.call(m,"amount"))w.uint32(8).int64(m.amount);if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(16).int32(m.seatId);if(m.afterSeat!=null&&Object.hasOwnProperty.call(m,"afterSeat"))w.uint32(24).bool(m.afterSeat);if(m.score!=null&&Object.hasOwnProperty.call(m,"score"))w.uint32(32).int64(m.score);return w};BuyInReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BuyInReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BuyInReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.amount=r.int64();break;case 2:m.seatId=r.int32();break;case 3:m.afterSeat=r.bool();break;case 4:m.score=r.int64();break;default:r.skipType(t&7);break}}return m};BuyInReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BuyInReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.amount!=null&&m.hasOwnProperty("amount")){if(!$util.isInteger(m.amount)&&!(m.amount&&$util.isInteger(m.amount.low)&&$util.isInteger(m.amount.high)))return"amount: integer|Long expected"}if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}if(m.afterSeat!=null&&m.hasOwnProperty("afterSeat")){if(typeof m.afterSeat!=="boolean")return"afterSeat: boolean expected"}if(m.score!=null&&m.hasOwnProperty("score")){if(!$util.isInteger(m.score)&&!(m.score&&$util.isInteger(m.score.low)&&$util.isInteger(m.score.high)))return"score: integer|Long expected"}return null};BuyInReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BuyInReq)return d;var m=new $root.jackfruit_proto.BuyInReq;if(d.amount!=null){if($util.Long)(m.amount=$util.Long.fromValue(d.amount)).unsigned=false;else if(typeof d.amount==="string")m.amount=parseInt(d.amount,10);else if(typeof d.amount==="number")m.amount=d.amount;else if(typeof d.amount==="object")m.amount=new $util.LongBits(d.amount.low>>>0,d.amount.high>>>0).toNumber()}if(d.seatId!=null){m.seatId=d.seatId|0}if(d.afterSeat!=null){m.afterSeat=Boolean(d.afterSeat)}if(d.score!=null){if($util.Long)(m.score=$util.Long.fromValue(d.score)).unsigned=false;else if(typeof d.score==="string")m.score=parseInt(d.score,10);else if(typeof d.score==="number")m.score=d.score;else if(typeof d.score==="object")m.score=new $util.LongBits(d.score.low>>>0,d.score.high>>>0).toNumber()}return m};BuyInReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.amount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.amount=o.longs===String?"0":0;d.seatId=0;d.afterSeat=false;if($util.Long){var n=new $util.Long(0,0,false);d.score=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.score=o.longs===String?"0":0}if(m.amount!=null&&m.hasOwnProperty("amount")){if(typeof m.amount==="number")d.amount=o.longs===String?String(m.amount):m.amount;else d.amount=o.longs===String?$util.Long.prototype.toString.call(m.amount):o.longs===Number?new $util.LongBits(m.amount.low>>>0,m.amount.high>>>0).toNumber():m.amount}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}if(m.afterSeat!=null&&m.hasOwnProperty("afterSeat")){d.afterSeat=m.afterSeat}if(m.score!=null&&m.hasOwnProperty("score")){if(typeof m.score==="number")d.score=o.longs===String?String(m.score):m.score;else d.score=o.longs===String?$util.Long.prototype.toString.call(m.score):o.longs===Number?new $util.LongBits(m.score.low>>>0,m.score.high>>>0).toNumber():m.score}return d};BuyInReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BuyInReq}();jackfruit_proto.BuyInResp=function(){function BuyInResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BuyInResp.prototype.code=0;BuyInResp.prototype.bill=null;BuyInResp.create=function create(properties){return new BuyInResp(properties)};BuyInResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.bill!=null&&Object.hasOwnProperty.call(m,"bill"))$root.jackfruit_proto.BillInfo.encode(m.bill,w.uint32(18).fork()).ldelim();return w};BuyInResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BuyInResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BuyInResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.bill=$root.jackfruit_proto.BillInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};BuyInResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BuyInResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.bill!=null&&m.hasOwnProperty("bill")){{var e=$root.jackfruit_proto.BillInfo.verify(m.bill);if(e)return"bill."+e}}return null};BuyInResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BuyInResp)return d;var m=new $root.jackfruit_proto.BuyInResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.bill!=null){if(typeof d.bill!=="object")throw TypeError(".jackfruit_proto.BuyInResp.bill: object expected");m.bill=$root.jackfruit_proto.BillInfo.fromObject(d.bill)}return m};BuyInResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.bill=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.bill!=null&&m.hasOwnProperty("bill")){d.bill=$root.jackfruit_proto.BillInfo.toObject(m.bill,o)}return d};BuyInResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BuyInResp}();jackfruit_proto.BillInfo=function(){function BillInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BillInfo.prototype.BillNo="";BillInfo.prototype.time=$util.Long?$util.Long.fromBits(0,0,false):0;BillInfo.create=function create(properties){return new BillInfo(properties)};BillInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.BillNo!=null&&Object.hasOwnProperty.call(m,"BillNo"))w.uint32(10).string(m.BillNo);if(m.time!=null&&Object.hasOwnProperty.call(m,"time"))w.uint32(16).int64(m.time);return w};BillInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BillInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BillInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.BillNo=r.string();break;case 2:m.time=r.int64();break;default:r.skipType(t&7);break}}return m};BillInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BillInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.BillNo!=null&&m.hasOwnProperty("BillNo")){if(!$util.isString(m.BillNo))return"BillNo: string expected"}if(m.time!=null&&m.hasOwnProperty("time")){if(!$util.isInteger(m.time)&&!(m.time&&$util.isInteger(m.time.low)&&$util.isInteger(m.time.high)))return"time: integer|Long expected"}return null};BillInfo.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BillInfo)return d;var m=new $root.jackfruit_proto.BillInfo;if(d.BillNo!=null){m.BillNo=String(d.BillNo)}if(d.time!=null){if($util.Long)(m.time=$util.Long.fromValue(d.time)).unsigned=false;else if(typeof d.time==="string")m.time=parseInt(d.time,10);else if(typeof d.time==="number")m.time=d.time;else if(typeof d.time==="object")m.time=new $util.LongBits(d.time.low>>>0,d.time.high>>>0).toNumber()}return m};BillInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.BillNo="";if($util.Long){var n=new $util.Long(0,0,false);d.time=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.time=o.longs===String?"0":0}if(m.BillNo!=null&&m.hasOwnProperty("BillNo")){d.BillNo=m.BillNo}if(m.time!=null&&m.hasOwnProperty("time")){if(typeof m.time==="number")d.time=o.longs===String?String(m.time):m.time;else d.time=o.longs===String?$util.Long.prototype.toString.call(m.time):o.longs===Number?new $util.LongBits(m.time.low>>>0,m.time.high>>>0).toNumber():m.time}return d};BillInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BillInfo}();jackfruit_proto.BuyInNotify=function(){function BuyInNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BuyInNotify.prototype.seatId=0;BuyInNotify.prototype.playerId=0;BuyInNotify.prototype.playerName="";BuyInNotify.prototype.buyInAmount=$util.Long?$util.Long.fromBits(0,0,false):0;BuyInNotify.prototype.amount=$util.Long?$util.Long.fromBits(0,0,false):0;BuyInNotify.prototype.isAuto=false;BuyInNotify.prototype.roomId=0;BuyInNotify.prototype.score=$util.Long?$util.Long.fromBits(0,0,false):0;BuyInNotify.prototype.buyInScore=$util.Long?$util.Long.fromBits(0,0,false):0;BuyInNotify.create=function create(properties){return new BuyInNotify(properties)};BuyInNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(8).int32(m.seatId);if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(16).uint32(m.playerId);if(m.playerName!=null&&Object.hasOwnProperty.call(m,"playerName"))w.uint32(26).string(m.playerName);if(m.buyInAmount!=null&&Object.hasOwnProperty.call(m,"buyInAmount"))w.uint32(32).int64(m.buyInAmount);if(m.amount!=null&&Object.hasOwnProperty.call(m,"amount"))w.uint32(40).int64(m.amount);if(m.isAuto!=null&&Object.hasOwnProperty.call(m,"isAuto"))w.uint32(48).bool(m.isAuto);if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(56).int32(m.roomId);if(m.score!=null&&Object.hasOwnProperty.call(m,"score"))w.uint32(64).int64(m.score);if(m.buyInScore!=null&&Object.hasOwnProperty.call(m,"buyInScore"))w.uint32(72).int64(m.buyInScore);return w};BuyInNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BuyInNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BuyInNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.seatId=r.int32();break;case 2:m.playerId=r.uint32();break;case 3:m.playerName=r.string();break;case 4:m.buyInAmount=r.int64();break;case 5:m.amount=r.int64();break;case 6:m.isAuto=r.bool();break;case 7:m.roomId=r.int32();break;case 8:m.score=r.int64();break;case 9:m.buyInScore=r.int64();break;default:r.skipType(t&7);break}}return m};BuyInNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BuyInNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}if(m.playerName!=null&&m.hasOwnProperty("playerName")){if(!$util.isString(m.playerName))return"playerName: string expected"}if(m.buyInAmount!=null&&m.hasOwnProperty("buyInAmount")){if(!$util.isInteger(m.buyInAmount)&&!(m.buyInAmount&&$util.isInteger(m.buyInAmount.low)&&$util.isInteger(m.buyInAmount.high)))return"buyInAmount: integer|Long expected"}if(m.amount!=null&&m.hasOwnProperty("amount")){if(!$util.isInteger(m.amount)&&!(m.amount&&$util.isInteger(m.amount.low)&&$util.isInteger(m.amount.high)))return"amount: integer|Long expected"}if(m.isAuto!=null&&m.hasOwnProperty("isAuto")){if(typeof m.isAuto!=="boolean")return"isAuto: boolean expected"}if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.score!=null&&m.hasOwnProperty("score")){if(!$util.isInteger(m.score)&&!(m.score&&$util.isInteger(m.score.low)&&$util.isInteger(m.score.high)))return"score: integer|Long expected"}if(m.buyInScore!=null&&m.hasOwnProperty("buyInScore")){if(!$util.isInteger(m.buyInScore)&&!(m.buyInScore&&$util.isInteger(m.buyInScore.low)&&$util.isInteger(m.buyInScore.high)))return"buyInScore: integer|Long expected"}return null};BuyInNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BuyInNotify)return d;var m=new $root.jackfruit_proto.BuyInNotify;if(d.seatId!=null){m.seatId=d.seatId|0}if(d.playerId!=null){m.playerId=d.playerId>>>0}if(d.playerName!=null){m.playerName=String(d.playerName)}if(d.buyInAmount!=null){if($util.Long)(m.buyInAmount=$util.Long.fromValue(d.buyInAmount)).unsigned=false;else if(typeof d.buyInAmount==="string")m.buyInAmount=parseInt(d.buyInAmount,10);else if(typeof d.buyInAmount==="number")m.buyInAmount=d.buyInAmount;else if(typeof d.buyInAmount==="object")m.buyInAmount=new $util.LongBits(d.buyInAmount.low>>>0,d.buyInAmount.high>>>0).toNumber()}if(d.amount!=null){if($util.Long)(m.amount=$util.Long.fromValue(d.amount)).unsigned=false;else if(typeof d.amount==="string")m.amount=parseInt(d.amount,10);else if(typeof d.amount==="number")m.amount=d.amount;else if(typeof d.amount==="object")m.amount=new $util.LongBits(d.amount.low>>>0,d.amount.high>>>0).toNumber()}if(d.isAuto!=null){m.isAuto=Boolean(d.isAuto)}if(d.roomId!=null){m.roomId=d.roomId|0}if(d.score!=null){if($util.Long)(m.score=$util.Long.fromValue(d.score)).unsigned=false;else if(typeof d.score==="string")m.score=parseInt(d.score,10);else if(typeof d.score==="number")m.score=d.score;else if(typeof d.score==="object")m.score=new $util.LongBits(d.score.low>>>0,d.score.high>>>0).toNumber()}if(d.buyInScore!=null){if($util.Long)(m.buyInScore=$util.Long.fromValue(d.buyInScore)).unsigned=false;else if(typeof d.buyInScore==="string")m.buyInScore=parseInt(d.buyInScore,10);else if(typeof d.buyInScore==="number")m.buyInScore=d.buyInScore;else if(typeof d.buyInScore==="object")m.buyInScore=new $util.LongBits(d.buyInScore.low>>>0,d.buyInScore.high>>>0).toNumber()}return m};BuyInNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.seatId=0;d.playerId=0;d.playerName="";if($util.Long){var n=new $util.Long(0,0,false);d.buyInAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.buyInAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.amount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.amount=o.longs===String?"0":0;d.isAuto=false;d.roomId=0;if($util.Long){var n=new $util.Long(0,0,false);d.score=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.score=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.buyInScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.buyInScore=o.longs===String?"0":0}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}if(m.playerName!=null&&m.hasOwnProperty("playerName")){d.playerName=m.playerName}if(m.buyInAmount!=null&&m.hasOwnProperty("buyInAmount")){if(typeof m.buyInAmount==="number")d.buyInAmount=o.longs===String?String(m.buyInAmount):m.buyInAmount;else d.buyInAmount=o.longs===String?$util.Long.prototype.toString.call(m.buyInAmount):o.longs===Number?new $util.LongBits(m.buyInAmount.low>>>0,m.buyInAmount.high>>>0).toNumber():m.buyInAmount}if(m.amount!=null&&m.hasOwnProperty("amount")){if(typeof m.amount==="number")d.amount=o.longs===String?String(m.amount):m.amount;else d.amount=o.longs===String?$util.Long.prototype.toString.call(m.amount):o.longs===Number?new $util.LongBits(m.amount.low>>>0,m.amount.high>>>0).toNumber():m.amount}if(m.isAuto!=null&&m.hasOwnProperty("isAuto")){d.isAuto=m.isAuto}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.score!=null&&m.hasOwnProperty("score")){if(typeof m.score==="number")d.score=o.longs===String?String(m.score):m.score;else d.score=o.longs===String?$util.Long.prototype.toString.call(m.score):o.longs===Number?new $util.LongBits(m.score.low>>>0,m.score.high>>>0).toNumber():m.score}if(m.buyInScore!=null&&m.hasOwnProperty("buyInScore")){if(typeof m.buyInScore==="number")d.buyInScore=o.longs===String?String(m.buyInScore):m.buyInScore;else d.buyInScore=o.longs===String?$util.Long.prototype.toString.call(m.buyInScore):o.longs===Number?new $util.LongBits(m.buyInScore.low>>>0,m.buyInScore.high>>>0).toNumber():m.buyInScore}return d};BuyInNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BuyInNotify}();jackfruit_proto.SituationReq=function(){function SituationReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SituationReq.prototype.roomId=0;SituationReq.create=function create(properties){return new SituationReq(properties)};SituationReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);return w};SituationReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SituationReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SituationReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;default:r.skipType(t&7);break}}return m};SituationReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SituationReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}return null};SituationReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SituationReq)return d;var m=new $root.jackfruit_proto.SituationReq;if(d.roomId!=null){m.roomId=d.roomId|0}return m};SituationReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}return d};SituationReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SituationReq}();jackfruit_proto.PlayerBuyInInfo=function(){function PlayerBuyInInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerBuyInInfo.prototype.playerName="";PlayerBuyInInfo.prototype.playerId=0;PlayerBuyInInfo.prototype.totalBuyIn=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerBuyInInfo.prototype.currRecord=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerBuyInInfo.prototype.totalBuyInScore=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerBuyInInfo.prototype.currRecordScore=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerBuyInInfo.prototype.handCount=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerBuyInInfo.create=function create(properties){return new PlayerBuyInInfo(properties)};PlayerBuyInInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerName!=null&&Object.hasOwnProperty.call(m,"playerName"))w.uint32(10).string(m.playerName);if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(16).uint32(m.playerId);if(m.totalBuyIn!=null&&Object.hasOwnProperty.call(m,"totalBuyIn"))w.uint32(24).int64(m.totalBuyIn);if(m.currRecord!=null&&Object.hasOwnProperty.call(m,"currRecord"))w.uint32(32).int64(m.currRecord);if(m.totalBuyInScore!=null&&Object.hasOwnProperty.call(m,"totalBuyInScore"))w.uint32(40).int64(m.totalBuyInScore);if(m.currRecordScore!=null&&Object.hasOwnProperty.call(m,"currRecordScore"))w.uint32(48).int64(m.currRecordScore);if(m.handCount!=null&&Object.hasOwnProperty.call(m,"handCount"))w.uint32(56).int64(m.handCount);return w};PlayerBuyInInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerBuyInInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlayerBuyInInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.playerName=r.string();break;case 2:m.playerId=r.uint32();break;case 3:m.totalBuyIn=r.int64();break;case 4:m.currRecord=r.int64();break;case 5:m.totalBuyInScore=r.int64();break;case 6:m.currRecordScore=r.int64();break;case 7:m.handCount=r.int64();break;default:r.skipType(t&7);break}}return m};PlayerBuyInInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerBuyInInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerName!=null&&m.hasOwnProperty("playerName")){if(!$util.isString(m.playerName))return"playerName: string expected"}if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}if(m.totalBuyIn!=null&&m.hasOwnProperty("totalBuyIn")){if(!$util.isInteger(m.totalBuyIn)&&!(m.totalBuyIn&&$util.isInteger(m.totalBuyIn.low)&&$util.isInteger(m.totalBuyIn.high)))return"totalBuyIn: integer|Long expected"}if(m.currRecord!=null&&m.hasOwnProperty("currRecord")){if(!$util.isInteger(m.currRecord)&&!(m.currRecord&&$util.isInteger(m.currRecord.low)&&$util.isInteger(m.currRecord.high)))return"currRecord: integer|Long expected"}if(m.totalBuyInScore!=null&&m.hasOwnProperty("totalBuyInScore")){if(!$util.isInteger(m.totalBuyInScore)&&!(m.totalBuyInScore&&$util.isInteger(m.totalBuyInScore.low)&&$util.isInteger(m.totalBuyInScore.high)))return"totalBuyInScore: integer|Long expected"}if(m.currRecordScore!=null&&m.hasOwnProperty("currRecordScore")){if(!$util.isInteger(m.currRecordScore)&&!(m.currRecordScore&&$util.isInteger(m.currRecordScore.low)&&$util.isInteger(m.currRecordScore.high)))return"currRecordScore: integer|Long expected"}if(m.handCount!=null&&m.hasOwnProperty("handCount")){if(!$util.isInteger(m.handCount)&&!(m.handCount&&$util.isInteger(m.handCount.low)&&$util.isInteger(m.handCount.high)))return"handCount: integer|Long expected"}return null};PlayerBuyInInfo.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlayerBuyInInfo)return d;var m=new $root.jackfruit_proto.PlayerBuyInInfo;if(d.playerName!=null){m.playerName=String(d.playerName)}if(d.playerId!=null){m.playerId=d.playerId>>>0}if(d.totalBuyIn!=null){if($util.Long)(m.totalBuyIn=$util.Long.fromValue(d.totalBuyIn)).unsigned=false;else if(typeof d.totalBuyIn==="string")m.totalBuyIn=parseInt(d.totalBuyIn,10);else if(typeof d.totalBuyIn==="number")m.totalBuyIn=d.totalBuyIn;else if(typeof d.totalBuyIn==="object")m.totalBuyIn=new $util.LongBits(d.totalBuyIn.low>>>0,d.totalBuyIn.high>>>0).toNumber()}if(d.currRecord!=null){if($util.Long)(m.currRecord=$util.Long.fromValue(d.currRecord)).unsigned=false;else if(typeof d.currRecord==="string")m.currRecord=parseInt(d.currRecord,10);else if(typeof d.currRecord==="number")m.currRecord=d.currRecord;else if(typeof d.currRecord==="object")m.currRecord=new $util.LongBits(d.currRecord.low>>>0,d.currRecord.high>>>0).toNumber()}if(d.totalBuyInScore!=null){if($util.Long)(m.totalBuyInScore=$util.Long.fromValue(d.totalBuyInScore)).unsigned=false;else if(typeof d.totalBuyInScore==="string")m.totalBuyInScore=parseInt(d.totalBuyInScore,10);else if(typeof d.totalBuyInScore==="number")m.totalBuyInScore=d.totalBuyInScore;else if(typeof d.totalBuyInScore==="object")m.totalBuyInScore=new $util.LongBits(d.totalBuyInScore.low>>>0,d.totalBuyInScore.high>>>0).toNumber()}if(d.currRecordScore!=null){if($util.Long)(m.currRecordScore=$util.Long.fromValue(d.currRecordScore)).unsigned=false;else if(typeof d.currRecordScore==="string")m.currRecordScore=parseInt(d.currRecordScore,10);else if(typeof d.currRecordScore==="number")m.currRecordScore=d.currRecordScore;else if(typeof d.currRecordScore==="object")m.currRecordScore=new $util.LongBits(d.currRecordScore.low>>>0,d.currRecordScore.high>>>0).toNumber()}if(d.handCount!=null){if($util.Long)(m.handCount=$util.Long.fromValue(d.handCount)).unsigned=false;else if(typeof d.handCount==="string")m.handCount=parseInt(d.handCount,10);else if(typeof d.handCount==="number")m.handCount=d.handCount;else if(typeof d.handCount==="object")m.handCount=new $util.LongBits(d.handCount.low>>>0,d.handCount.high>>>0).toNumber()}return m};PlayerBuyInInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.playerName="";d.playerId=0;if($util.Long){var n=new $util.Long(0,0,false);d.totalBuyIn=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBuyIn=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.currRecord=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.currRecord=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.totalBuyInScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBuyInScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.currRecordScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.currRecordScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.handCount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.handCount=o.longs===String?"0":0}if(m.playerName!=null&&m.hasOwnProperty("playerName")){d.playerName=m.playerName}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}if(m.totalBuyIn!=null&&m.hasOwnProperty("totalBuyIn")){if(typeof m.totalBuyIn==="number")d.totalBuyIn=o.longs===String?String(m.totalBuyIn):m.totalBuyIn;else d.totalBuyIn=o.longs===String?$util.Long.prototype.toString.call(m.totalBuyIn):o.longs===Number?new $util.LongBits(m.totalBuyIn.low>>>0,m.totalBuyIn.high>>>0).toNumber():m.totalBuyIn}if(m.currRecord!=null&&m.hasOwnProperty("currRecord")){if(typeof m.currRecord==="number")d.currRecord=o.longs===String?String(m.currRecord):m.currRecord;else d.currRecord=o.longs===String?$util.Long.prototype.toString.call(m.currRecord):o.longs===Number?new $util.LongBits(m.currRecord.low>>>0,m.currRecord.high>>>0).toNumber():m.currRecord}if(m.totalBuyInScore!=null&&m.hasOwnProperty("totalBuyInScore")){if(typeof m.totalBuyInScore==="number")d.totalBuyInScore=o.longs===String?String(m.totalBuyInScore):m.totalBuyInScore;else d.totalBuyInScore=o.longs===String?$util.Long.prototype.toString.call(m.totalBuyInScore):o.longs===Number?new $util.LongBits(m.totalBuyInScore.low>>>0,m.totalBuyInScore.high>>>0).toNumber():m.totalBuyInScore}if(m.currRecordScore!=null&&m.hasOwnProperty("currRecordScore")){if(typeof m.currRecordScore==="number")d.currRecordScore=o.longs===String?String(m.currRecordScore):m.currRecordScore;else d.currRecordScore=o.longs===String?$util.Long.prototype.toString.call(m.currRecordScore):o.longs===Number?new $util.LongBits(m.currRecordScore.low>>>0,m.currRecordScore.high>>>0).toNumber():m.currRecordScore}if(m.handCount!=null&&m.hasOwnProperty("handCount")){if(typeof m.handCount==="number")d.handCount=o.longs===String?String(m.handCount):m.handCount;else d.handCount=o.longs===String?$util.Long.prototype.toString.call(m.handCount):o.longs===Number?new $util.LongBits(m.handCount.low>>>0,m.handCount.high>>>0).toNumber():m.handCount}return d};PlayerBuyInInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerBuyInInfo}();jackfruit_proto.SituationResp=function(){function SituationResp(p){this.observerList=[];this.byStanderList=[];this.playerBuyInInfo=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SituationResp.prototype.code=0;SituationResp.prototype.observerList=$util.emptyArray;SituationResp.prototype.byStanderList=$util.emptyArray;SituationResp.prototype.leftTime=0;SituationResp.prototype.roomStartTime=0;SituationResp.prototype.playerBuyInInfo=$util.emptyArray;SituationResp.prototype.roomId=0;SituationResp.prototype.observer_info=null;SituationResp.create=function create(properties){return new SituationResp(properties)};SituationResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.observerList!=null&&m.observerList.length){for(var i=0;i<m.observerList.length;++i)$root.jackfruit_proto.PlayerInfo.encode(m.observerList[i],w.uint32(18).fork()).ldelim()}if(m.byStanderList!=null&&m.byStanderList.length){w.uint32(26).fork();for(var i=0;i<m.byStanderList.length;++i)w.int32(m.byStanderList[i]);w.ldelim()}if(m.leftTime!=null&&Object.hasOwnProperty.call(m,"leftTime"))w.uint32(32).int32(m.leftTime);if(m.roomStartTime!=null&&Object.hasOwnProperty.call(m,"roomStartTime"))w.uint32(40).int32(m.roomStartTime);if(m.playerBuyInInfo!=null&&m.playerBuyInInfo.length){for(var i=0;i<m.playerBuyInInfo.length;++i)$root.jackfruit_proto.PlayerBuyInInfo.encode(m.playerBuyInInfo[i],w.uint32(50).fork()).ldelim()}if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(56).int32(m.roomId);if(m.observer_info!=null&&Object.hasOwnProperty.call(m,"observer_info"))$root.jackfruit_proto.ObserverDetails.encode(m.observer_info,w.uint32(66).fork()).ldelim();return w};SituationResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SituationResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SituationResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:if(!(m.observerList&&m.observerList.length))m.observerList=[];m.observerList.push($root.jackfruit_proto.PlayerInfo.decode(r,r.uint32()));break;case 3:if(!(m.byStanderList&&m.byStanderList.length))m.byStanderList=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.byStanderList.push(r.int32())}else m.byStanderList.push(r.int32());break;case 4:m.leftTime=r.int32();break;case 5:m.roomStartTime=r.int32();break;case 6:if(!(m.playerBuyInInfo&&m.playerBuyInInfo.length))m.playerBuyInInfo=[];m.playerBuyInInfo.push($root.jackfruit_proto.PlayerBuyInInfo.decode(r,r.uint32()));break;case 7:m.roomId=r.int32();break;case 8:m.observer_info=$root.jackfruit_proto.ObserverDetails.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};SituationResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SituationResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.observerList!=null&&m.hasOwnProperty("observerList")){if(!Array.isArray(m.observerList))return"observerList: array expected";for(var i=0;i<m.observerList.length;++i){{var e=$root.jackfruit_proto.PlayerInfo.verify(m.observerList[i]);if(e)return"observerList."+e}}}if(m.byStanderList!=null&&m.hasOwnProperty("byStanderList")){if(!Array.isArray(m.byStanderList))return"byStanderList: array expected";for(var i=0;i<m.byStanderList.length;++i){if(!$util.isInteger(m.byStanderList[i]))return"byStanderList: integer[] expected"}}if(m.leftTime!=null&&m.hasOwnProperty("leftTime")){if(!$util.isInteger(m.leftTime))return"leftTime: integer expected"}if(m.roomStartTime!=null&&m.hasOwnProperty("roomStartTime")){if(!$util.isInteger(m.roomStartTime))return"roomStartTime: integer expected"}if(m.playerBuyInInfo!=null&&m.hasOwnProperty("playerBuyInInfo")){if(!Array.isArray(m.playerBuyInInfo))return"playerBuyInInfo: array expected";for(var i=0;i<m.playerBuyInInfo.length;++i){{var e=$root.jackfruit_proto.PlayerBuyInInfo.verify(m.playerBuyInInfo[i]);if(e)return"playerBuyInInfo."+e}}}if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.observer_info!=null&&m.hasOwnProperty("observer_info")){{var e=$root.jackfruit_proto.ObserverDetails.verify(m.observer_info);if(e)return"observer_info."+e}}return null};SituationResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SituationResp)return d;var m=new $root.jackfruit_proto.SituationResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.observerList){if(!Array.isArray(d.observerList))throw TypeError(".jackfruit_proto.SituationResp.observerList: array expected");m.observerList=[];for(var i=0;i<d.observerList.length;++i){if(typeof d.observerList[i]!=="object")throw TypeError(".jackfruit_proto.SituationResp.observerList: object expected");m.observerList[i]=$root.jackfruit_proto.PlayerInfo.fromObject(d.observerList[i])}}if(d.byStanderList){if(!Array.isArray(d.byStanderList))throw TypeError(".jackfruit_proto.SituationResp.byStanderList: array expected");m.byStanderList=[];for(var i=0;i<d.byStanderList.length;++i){m.byStanderList[i]=d.byStanderList[i]|0}}if(d.leftTime!=null){m.leftTime=d.leftTime|0}if(d.roomStartTime!=null){m.roomStartTime=d.roomStartTime|0}if(d.playerBuyInInfo){if(!Array.isArray(d.playerBuyInInfo))throw TypeError(".jackfruit_proto.SituationResp.playerBuyInInfo: array expected");m.playerBuyInInfo=[];for(var i=0;i<d.playerBuyInInfo.length;++i){if(typeof d.playerBuyInInfo[i]!=="object")throw TypeError(".jackfruit_proto.SituationResp.playerBuyInInfo: object expected");m.playerBuyInInfo[i]=$root.jackfruit_proto.PlayerBuyInInfo.fromObject(d.playerBuyInInfo[i])}}if(d.roomId!=null){m.roomId=d.roomId|0}if(d.observer_info!=null){if(typeof d.observer_info!=="object")throw TypeError(".jackfruit_proto.SituationResp.observer_info: object expected");m.observer_info=$root.jackfruit_proto.ObserverDetails.fromObject(d.observer_info)}return m};SituationResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.observerList=[];d.byStanderList=[];d.playerBuyInInfo=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.leftTime=0;d.roomStartTime=0;d.roomId=0;d.observer_info=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.observerList&&m.observerList.length){d.observerList=[];for(var j=0;j<m.observerList.length;++j){d.observerList[j]=$root.jackfruit_proto.PlayerInfo.toObject(m.observerList[j],o)}}if(m.byStanderList&&m.byStanderList.length){d.byStanderList=[];for(var j=0;j<m.byStanderList.length;++j){d.byStanderList[j]=m.byStanderList[j]}}if(m.leftTime!=null&&m.hasOwnProperty("leftTime")){d.leftTime=m.leftTime}if(m.roomStartTime!=null&&m.hasOwnProperty("roomStartTime")){d.roomStartTime=m.roomStartTime}if(m.playerBuyInInfo&&m.playerBuyInInfo.length){d.playerBuyInInfo=[];for(var j=0;j<m.playerBuyInInfo.length;++j){d.playerBuyInInfo[j]=$root.jackfruit_proto.PlayerBuyInInfo.toObject(m.playerBuyInInfo[j],o)}}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.observer_info!=null&&m.hasOwnProperty("observer_info")){d.observer_info=$root.jackfruit_proto.ObserverDetails.toObject(m.observer_info,o)}return d};SituationResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SituationResp}();jackfruit_proto.ObserverDetails=function(){function ObserverDetails(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ObserverDetails.prototype.online_count=0;ObserverDetails.prototype.total_count=0;ObserverDetails.create=function create(properties){return new ObserverDetails(properties)};ObserverDetails.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.online_count!=null&&Object.hasOwnProperty.call(m,"online_count"))w.uint32(8).int32(m.online_count);if(m.total_count!=null&&Object.hasOwnProperty.call(m,"total_count"))w.uint32(16).int32(m.total_count);return w};ObserverDetails.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ObserverDetails.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ObserverDetails;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.online_count=r.int32();break;case 2:m.total_count=r.int32();break;default:r.skipType(t&7);break}}return m};ObserverDetails.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ObserverDetails.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.online_count!=null&&m.hasOwnProperty("online_count")){if(!$util.isInteger(m.online_count))return"online_count: integer expected"}if(m.total_count!=null&&m.hasOwnProperty("total_count")){if(!$util.isInteger(m.total_count))return"total_count: integer expected"}return null};ObserverDetails.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ObserverDetails)return d;var m=new $root.jackfruit_proto.ObserverDetails;if(d.online_count!=null){m.online_count=d.online_count|0}if(d.total_count!=null){m.total_count=d.total_count|0}return m};ObserverDetails.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.online_count=0;d.total_count=0}if(m.online_count!=null&&m.hasOwnProperty("online_count")){d.online_count=m.online_count}if(m.total_count!=null&&m.hasOwnProperty("total_count")){d.total_count=m.total_count}return d};ObserverDetails.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ObserverDetails}();jackfruit_proto.PlayerTotalSettle=function(){function PlayerTotalSettle(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerTotalSettle.prototype.playerName="";PlayerTotalSettle.prototype.playerHead="";PlayerTotalSettle.prototype.totalBuyIn=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerTotalSettle.prototype.playerHandCount=0;PlayerTotalSettle.prototype.playerSettle=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerTotalSettle.prototype.playerId=0;PlayerTotalSettle.prototype.totalBuyInScore=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerTotalSettle.prototype.playerSettleScore=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerTotalSettle.prototype.plat=0;PlayerTotalSettle.create=function create(properties){return new PlayerTotalSettle(properties)};PlayerTotalSettle.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerName!=null&&Object.hasOwnProperty.call(m,"playerName"))w.uint32(10).string(m.playerName);if(m.playerHead!=null&&Object.hasOwnProperty.call(m,"playerHead"))w.uint32(18).string(m.playerHead);if(m.totalBuyIn!=null&&Object.hasOwnProperty.call(m,"totalBuyIn"))w.uint32(24).int64(m.totalBuyIn);if(m.playerHandCount!=null&&Object.hasOwnProperty.call(m,"playerHandCount"))w.uint32(32).int32(m.playerHandCount);if(m.playerSettle!=null&&Object.hasOwnProperty.call(m,"playerSettle"))w.uint32(40).int64(m.playerSettle);if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(48).uint32(m.playerId);if(m.totalBuyInScore!=null&&Object.hasOwnProperty.call(m,"totalBuyInScore"))w.uint32(56).int64(m.totalBuyInScore);if(m.playerSettleScore!=null&&Object.hasOwnProperty.call(m,"playerSettleScore"))w.uint32(64).int64(m.playerSettleScore);if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(72).uint32(m.plat);return w};PlayerTotalSettle.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerTotalSettle.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlayerTotalSettle;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.playerName=r.string();break;case 2:m.playerHead=r.string();break;case 3:m.totalBuyIn=r.int64();break;case 4:m.playerHandCount=r.int32();break;case 5:m.playerSettle=r.int64();break;case 6:m.playerId=r.uint32();break;case 7:m.totalBuyInScore=r.int64();break;case 8:m.playerSettleScore=r.int64();break;case 9:m.plat=r.uint32();break;default:r.skipType(t&7);break}}return m};PlayerTotalSettle.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerTotalSettle.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerName!=null&&m.hasOwnProperty("playerName")){if(!$util.isString(m.playerName))return"playerName: string expected"}if(m.playerHead!=null&&m.hasOwnProperty("playerHead")){if(!$util.isString(m.playerHead))return"playerHead: string expected"}if(m.totalBuyIn!=null&&m.hasOwnProperty("totalBuyIn")){if(!$util.isInteger(m.totalBuyIn)&&!(m.totalBuyIn&&$util.isInteger(m.totalBuyIn.low)&&$util.isInteger(m.totalBuyIn.high)))return"totalBuyIn: integer|Long expected"}if(m.playerHandCount!=null&&m.hasOwnProperty("playerHandCount")){if(!$util.isInteger(m.playerHandCount))return"playerHandCount: integer expected"}if(m.playerSettle!=null&&m.hasOwnProperty("playerSettle")){if(!$util.isInteger(m.playerSettle)&&!(m.playerSettle&&$util.isInteger(m.playerSettle.low)&&$util.isInteger(m.playerSettle.high)))return"playerSettle: integer|Long expected"}if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}if(m.totalBuyInScore!=null&&m.hasOwnProperty("totalBuyInScore")){if(!$util.isInteger(m.totalBuyInScore)&&!(m.totalBuyInScore&&$util.isInteger(m.totalBuyInScore.low)&&$util.isInteger(m.totalBuyInScore.high)))return"totalBuyInScore: integer|Long expected"}if(m.playerSettleScore!=null&&m.hasOwnProperty("playerSettleScore")){if(!$util.isInteger(m.playerSettleScore)&&!(m.playerSettleScore&&$util.isInteger(m.playerSettleScore.low)&&$util.isInteger(m.playerSettleScore.high)))return"playerSettleScore: integer|Long expected"}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}return null};PlayerTotalSettle.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlayerTotalSettle)return d;var m=new $root.jackfruit_proto.PlayerTotalSettle;if(d.playerName!=null){m.playerName=String(d.playerName)}if(d.playerHead!=null){m.playerHead=String(d.playerHead)}if(d.totalBuyIn!=null){if($util.Long)(m.totalBuyIn=$util.Long.fromValue(d.totalBuyIn)).unsigned=false;else if(typeof d.totalBuyIn==="string")m.totalBuyIn=parseInt(d.totalBuyIn,10);else if(typeof d.totalBuyIn==="number")m.totalBuyIn=d.totalBuyIn;else if(typeof d.totalBuyIn==="object")m.totalBuyIn=new $util.LongBits(d.totalBuyIn.low>>>0,d.totalBuyIn.high>>>0).toNumber()}if(d.playerHandCount!=null){m.playerHandCount=d.playerHandCount|0}if(d.playerSettle!=null){if($util.Long)(m.playerSettle=$util.Long.fromValue(d.playerSettle)).unsigned=false;else if(typeof d.playerSettle==="string")m.playerSettle=parseInt(d.playerSettle,10);else if(typeof d.playerSettle==="number")m.playerSettle=d.playerSettle;else if(typeof d.playerSettle==="object")m.playerSettle=new $util.LongBits(d.playerSettle.low>>>0,d.playerSettle.high>>>0).toNumber()}if(d.playerId!=null){m.playerId=d.playerId>>>0}if(d.totalBuyInScore!=null){if($util.Long)(m.totalBuyInScore=$util.Long.fromValue(d.totalBuyInScore)).unsigned=false;else if(typeof d.totalBuyInScore==="string")m.totalBuyInScore=parseInt(d.totalBuyInScore,10);else if(typeof d.totalBuyInScore==="number")m.totalBuyInScore=d.totalBuyInScore;else if(typeof d.totalBuyInScore==="object")m.totalBuyInScore=new $util.LongBits(d.totalBuyInScore.low>>>0,d.totalBuyInScore.high>>>0).toNumber()}if(d.playerSettleScore!=null){if($util.Long)(m.playerSettleScore=$util.Long.fromValue(d.playerSettleScore)).unsigned=false;else if(typeof d.playerSettleScore==="string")m.playerSettleScore=parseInt(d.playerSettleScore,10);else if(typeof d.playerSettleScore==="number")m.playerSettleScore=d.playerSettleScore;else if(typeof d.playerSettleScore==="object")m.playerSettleScore=new $util.LongBits(d.playerSettleScore.low>>>0,d.playerSettleScore.high>>>0).toNumber()}if(d.plat!=null){m.plat=d.plat>>>0}return m};PlayerTotalSettle.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.playerName="";d.playerHead="";if($util.Long){var n=new $util.Long(0,0,false);d.totalBuyIn=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBuyIn=o.longs===String?"0":0;d.playerHandCount=0;if($util.Long){var n=new $util.Long(0,0,false);d.playerSettle=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.playerSettle=o.longs===String?"0":0;d.playerId=0;if($util.Long){var n=new $util.Long(0,0,false);d.totalBuyInScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBuyInScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.playerSettleScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.playerSettleScore=o.longs===String?"0":0;d.plat=0}if(m.playerName!=null&&m.hasOwnProperty("playerName")){d.playerName=m.playerName}if(m.playerHead!=null&&m.hasOwnProperty("playerHead")){d.playerHead=m.playerHead}if(m.totalBuyIn!=null&&m.hasOwnProperty("totalBuyIn")){if(typeof m.totalBuyIn==="number")d.totalBuyIn=o.longs===String?String(m.totalBuyIn):m.totalBuyIn;else d.totalBuyIn=o.longs===String?$util.Long.prototype.toString.call(m.totalBuyIn):o.longs===Number?new $util.LongBits(m.totalBuyIn.low>>>0,m.totalBuyIn.high>>>0).toNumber():m.totalBuyIn}if(m.playerHandCount!=null&&m.hasOwnProperty("playerHandCount")){d.playerHandCount=m.playerHandCount}if(m.playerSettle!=null&&m.hasOwnProperty("playerSettle")){if(typeof m.playerSettle==="number")d.playerSettle=o.longs===String?String(m.playerSettle):m.playerSettle;else d.playerSettle=o.longs===String?$util.Long.prototype.toString.call(m.playerSettle):o.longs===Number?new $util.LongBits(m.playerSettle.low>>>0,m.playerSettle.high>>>0).toNumber():m.playerSettle}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}if(m.totalBuyInScore!=null&&m.hasOwnProperty("totalBuyInScore")){if(typeof m.totalBuyInScore==="number")d.totalBuyInScore=o.longs===String?String(m.totalBuyInScore):m.totalBuyInScore;else d.totalBuyInScore=o.longs===String?$util.Long.prototype.toString.call(m.totalBuyInScore):o.longs===Number?new $util.LongBits(m.totalBuyInScore.low>>>0,m.totalBuyInScore.high>>>0).toNumber():m.totalBuyInScore}if(m.playerSettleScore!=null&&m.hasOwnProperty("playerSettleScore")){if(typeof m.playerSettleScore==="number")d.playerSettleScore=o.longs===String?String(m.playerSettleScore):m.playerSettleScore;else d.playerSettleScore=o.longs===String?$util.Long.prototype.toString.call(m.playerSettleScore):o.longs===Number?new $util.LongBits(m.playerSettleScore.low>>>0,m.playerSettleScore.high>>>0).toNumber():m.playerSettleScore}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}return d};PlayerTotalSettle.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerTotalSettle}();jackfruit_proto.DestroyRoom=function(){function DestroyRoom(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DestroyRoom.prototype.roomId=0;DestroyRoom.create=function create(properties){return new DestroyRoom(properties)};DestroyRoom.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);return w};DestroyRoom.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DestroyRoom.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.DestroyRoom;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;default:r.skipType(t&7);break}}return m};DestroyRoom.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DestroyRoom.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}return null};DestroyRoom.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.DestroyRoom)return d;var m=new $root.jackfruit_proto.DestroyRoom;if(d.roomId!=null){m.roomId=d.roomId|0}return m};DestroyRoom.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}return d};DestroyRoom.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DestroyRoom}();jackfruit_proto.DestroyRoomResp=function(){function DestroyRoomResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DestroyRoomResp.prototype.code=0;DestroyRoomResp.create=function create(properties){return new DestroyRoomResp(properties)};DestroyRoomResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};DestroyRoomResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DestroyRoomResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.DestroyRoomResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};DestroyRoomResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DestroyRoomResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}return null};DestroyRoomResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.DestroyRoomResp)return d;var m=new $root.jackfruit_proto.DestroyRoomResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};DestroyRoomResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}return d};DestroyRoomResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DestroyRoomResp}();jackfruit_proto.DestroyRoomNotify=function(){function DestroyRoomNotify(p){this.playerTotalSettle=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DestroyRoomNotify.prototype.roomId=0;DestroyRoomNotify.prototype.createTime=0;DestroyRoomNotify.prototype.timeLimit=0;DestroyRoomNotify.prototype.ownerName="";DestroyRoomNotify.prototype.gameHand=0;DestroyRoomNotify.prototype.totalBuyIn=$util.Long?$util.Long.fromBits(0,0,false):0;DestroyRoomNotify.prototype.roomUuid=$util.Long?$util.Long.fromBits(0,0,true):0;DestroyRoomNotify.prototype.roomName="";DestroyRoomNotify.prototype.roomUuidStr="";DestroyRoomNotify.prototype.playerTotalSettle=$util.emptyArray;DestroyRoomNotify.prototype.reason=0;DestroyRoomNotify.prototype.totalBuyInScore=$util.Long?$util.Long.fromBits(0,0,false):0;DestroyRoomNotify.create=function create(properties){return new DestroyRoomNotify(properties)};DestroyRoomNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.createTime!=null&&Object.hasOwnProperty.call(m,"createTime"))w.uint32(16).uint32(m.createTime);if(m.timeLimit!=null&&Object.hasOwnProperty.call(m,"timeLimit"))w.uint32(24).int32(m.timeLimit);if(m.ownerName!=null&&Object.hasOwnProperty.call(m,"ownerName"))w.uint32(34).string(m.ownerName);if(m.gameHand!=null&&Object.hasOwnProperty.call(m,"gameHand"))w.uint32(40).int32(m.gameHand);if(m.totalBuyIn!=null&&Object.hasOwnProperty.call(m,"totalBuyIn"))w.uint32(48).int64(m.totalBuyIn);if(m.roomUuid!=null&&Object.hasOwnProperty.call(m,"roomUuid"))w.uint32(56).uint64(m.roomUuid);if(m.roomName!=null&&Object.hasOwnProperty.call(m,"roomName"))w.uint32(66).string(m.roomName);if(m.roomUuidStr!=null&&Object.hasOwnProperty.call(m,"roomUuidStr"))w.uint32(74).string(m.roomUuidStr);if(m.playerTotalSettle!=null&&m.playerTotalSettle.length){for(var i=0;i<m.playerTotalSettle.length;++i)$root.jackfruit_proto.PlayerTotalSettle.encode(m.playerTotalSettle[i],w.uint32(82).fork()).ldelim()}if(m.reason!=null&&Object.hasOwnProperty.call(m,"reason"))w.uint32(88).int32(m.reason);if(m.totalBuyInScore!=null&&Object.hasOwnProperty.call(m,"totalBuyInScore"))w.uint32(96).int64(m.totalBuyInScore);return w};DestroyRoomNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DestroyRoomNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.DestroyRoomNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:m.createTime=r.uint32();break;case 3:m.timeLimit=r.int32();break;case 4:m.ownerName=r.string();break;case 5:m.gameHand=r.int32();break;case 6:m.totalBuyIn=r.int64();break;case 7:m.roomUuid=r.uint64();break;case 8:m.roomName=r.string();break;case 9:m.roomUuidStr=r.string();break;case 10:if(!(m.playerTotalSettle&&m.playerTotalSettle.length))m.playerTotalSettle=[];m.playerTotalSettle.push($root.jackfruit_proto.PlayerTotalSettle.decode(r,r.uint32()));break;case 11:m.reason=r.int32();break;case 12:m.totalBuyInScore=r.int64();break;default:r.skipType(t&7);break}}return m};DestroyRoomNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DestroyRoomNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.createTime!=null&&m.hasOwnProperty("createTime")){if(!$util.isInteger(m.createTime))return"createTime: integer expected"}if(m.timeLimit!=null&&m.hasOwnProperty("timeLimit")){if(!$util.isInteger(m.timeLimit))return"timeLimit: integer expected"}if(m.ownerName!=null&&m.hasOwnProperty("ownerName")){if(!$util.isString(m.ownerName))return"ownerName: string expected"}if(m.gameHand!=null&&m.hasOwnProperty("gameHand")){if(!$util.isInteger(m.gameHand))return"gameHand: integer expected"}if(m.totalBuyIn!=null&&m.hasOwnProperty("totalBuyIn")){if(!$util.isInteger(m.totalBuyIn)&&!(m.totalBuyIn&&$util.isInteger(m.totalBuyIn.low)&&$util.isInteger(m.totalBuyIn.high)))return"totalBuyIn: integer|Long expected"}if(m.roomUuid!=null&&m.hasOwnProperty("roomUuid")){if(!$util.isInteger(m.roomUuid)&&!(m.roomUuid&&$util.isInteger(m.roomUuid.low)&&$util.isInteger(m.roomUuid.high)))return"roomUuid: integer|Long expected"}if(m.roomName!=null&&m.hasOwnProperty("roomName")){if(!$util.isString(m.roomName))return"roomName: string expected"}if(m.roomUuidStr!=null&&m.hasOwnProperty("roomUuidStr")){if(!$util.isString(m.roomUuidStr))return"roomUuidStr: string expected"}if(m.playerTotalSettle!=null&&m.hasOwnProperty("playerTotalSettle")){if(!Array.isArray(m.playerTotalSettle))return"playerTotalSettle: array expected";for(var i=0;i<m.playerTotalSettle.length;++i){{var e=$root.jackfruit_proto.PlayerTotalSettle.verify(m.playerTotalSettle[i]);if(e)return"playerTotalSettle."+e}}}if(m.reason!=null&&m.hasOwnProperty("reason")){if(!$util.isInteger(m.reason))return"reason: integer expected"}if(m.totalBuyInScore!=null&&m.hasOwnProperty("totalBuyInScore")){if(!$util.isInteger(m.totalBuyInScore)&&!(m.totalBuyInScore&&$util.isInteger(m.totalBuyInScore.low)&&$util.isInteger(m.totalBuyInScore.high)))return"totalBuyInScore: integer|Long expected"}return null};DestroyRoomNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.DestroyRoomNotify)return d;var m=new $root.jackfruit_proto.DestroyRoomNotify;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.createTime!=null){m.createTime=d.createTime>>>0}if(d.timeLimit!=null){m.timeLimit=d.timeLimit|0}if(d.ownerName!=null){m.ownerName=String(d.ownerName)}if(d.gameHand!=null){m.gameHand=d.gameHand|0}if(d.totalBuyIn!=null){if($util.Long)(m.totalBuyIn=$util.Long.fromValue(d.totalBuyIn)).unsigned=false;else if(typeof d.totalBuyIn==="string")m.totalBuyIn=parseInt(d.totalBuyIn,10);else if(typeof d.totalBuyIn==="number")m.totalBuyIn=d.totalBuyIn;else if(typeof d.totalBuyIn==="object")m.totalBuyIn=new $util.LongBits(d.totalBuyIn.low>>>0,d.totalBuyIn.high>>>0).toNumber()}if(d.roomUuid!=null){if($util.Long)(m.roomUuid=$util.Long.fromValue(d.roomUuid)).unsigned=true;else if(typeof d.roomUuid==="string")m.roomUuid=parseInt(d.roomUuid,10);else if(typeof d.roomUuid==="number")m.roomUuid=d.roomUuid;else if(typeof d.roomUuid==="object")m.roomUuid=new $util.LongBits(d.roomUuid.low>>>0,d.roomUuid.high>>>0).toNumber(true)}if(d.roomName!=null){m.roomName=String(d.roomName)}if(d.roomUuidStr!=null){m.roomUuidStr=String(d.roomUuidStr)}if(d.playerTotalSettle){if(!Array.isArray(d.playerTotalSettle))throw TypeError(".jackfruit_proto.DestroyRoomNotify.playerTotalSettle: array expected");m.playerTotalSettle=[];for(var i=0;i<d.playerTotalSettle.length;++i){if(typeof d.playerTotalSettle[i]!=="object")throw TypeError(".jackfruit_proto.DestroyRoomNotify.playerTotalSettle: object expected");m.playerTotalSettle[i]=$root.jackfruit_proto.PlayerTotalSettle.fromObject(d.playerTotalSettle[i])}}if(d.reason!=null){m.reason=d.reason|0}if(d.totalBuyInScore!=null){if($util.Long)(m.totalBuyInScore=$util.Long.fromValue(d.totalBuyInScore)).unsigned=false;else if(typeof d.totalBuyInScore==="string")m.totalBuyInScore=parseInt(d.totalBuyInScore,10);else if(typeof d.totalBuyInScore==="number")m.totalBuyInScore=d.totalBuyInScore;else if(typeof d.totalBuyInScore==="object")m.totalBuyInScore=new $util.LongBits(d.totalBuyInScore.low>>>0,d.totalBuyInScore.high>>>0).toNumber()}return m};DestroyRoomNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.playerTotalSettle=[]}if(o.defaults){d.roomId=0;d.createTime=0;d.timeLimit=0;d.ownerName="";d.gameHand=0;if($util.Long){var n=new $util.Long(0,0,false);d.totalBuyIn=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBuyIn=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.roomUuid=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.roomUuid=o.longs===String?"0":0;d.roomName="";d.roomUuidStr="";d.reason=0;if($util.Long){var n=new $util.Long(0,0,false);d.totalBuyInScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBuyInScore=o.longs===String?"0":0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.createTime!=null&&m.hasOwnProperty("createTime")){d.createTime=m.createTime}if(m.timeLimit!=null&&m.hasOwnProperty("timeLimit")){d.timeLimit=m.timeLimit}if(m.ownerName!=null&&m.hasOwnProperty("ownerName")){d.ownerName=m.ownerName}if(m.gameHand!=null&&m.hasOwnProperty("gameHand")){d.gameHand=m.gameHand}if(m.totalBuyIn!=null&&m.hasOwnProperty("totalBuyIn")){if(typeof m.totalBuyIn==="number")d.totalBuyIn=o.longs===String?String(m.totalBuyIn):m.totalBuyIn;else d.totalBuyIn=o.longs===String?$util.Long.prototype.toString.call(m.totalBuyIn):o.longs===Number?new $util.LongBits(m.totalBuyIn.low>>>0,m.totalBuyIn.high>>>0).toNumber():m.totalBuyIn}if(m.roomUuid!=null&&m.hasOwnProperty("roomUuid")){if(typeof m.roomUuid==="number")d.roomUuid=o.longs===String?String(m.roomUuid):m.roomUuid;else d.roomUuid=o.longs===String?$util.Long.prototype.toString.call(m.roomUuid):o.longs===Number?new $util.LongBits(m.roomUuid.low>>>0,m.roomUuid.high>>>0).toNumber(true):m.roomUuid}if(m.roomName!=null&&m.hasOwnProperty("roomName")){d.roomName=m.roomName}if(m.roomUuidStr!=null&&m.hasOwnProperty("roomUuidStr")){d.roomUuidStr=m.roomUuidStr}if(m.playerTotalSettle&&m.playerTotalSettle.length){d.playerTotalSettle=[];for(var j=0;j<m.playerTotalSettle.length;++j){d.playerTotalSettle[j]=$root.jackfruit_proto.PlayerTotalSettle.toObject(m.playerTotalSettle[j],o)}}if(m.reason!=null&&m.hasOwnProperty("reason")){d.reason=m.reason}if(m.totalBuyInScore!=null&&m.hasOwnProperty("totalBuyInScore")){if(typeof m.totalBuyInScore==="number")d.totalBuyInScore=o.longs===String?String(m.totalBuyInScore):m.totalBuyInScore;else d.totalBuyInScore=o.longs===String?$util.Long.prototype.toString.call(m.totalBuyInScore):o.longs===Number?new $util.LongBits(m.totalBuyInScore.low>>>0,m.totalBuyInScore.high>>>0).toNumber():m.totalBuyInScore}return d};DestroyRoomNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DestroyRoomNotify}();jackfruit_proto.BarrageType=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Enum_System"]=0;values[valuesById[1]="Enum_Custom"]=1;values[valuesById[2]="Enum_CardType"]=2;values[valuesById[3]="Enum_Liked"]=3;values[valuesById[6]="Enum_Profile_Liked"]=6;return values}();jackfruit_proto.RequestSendBarrage=function(){function RequestSendBarrage(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RequestSendBarrage.prototype.roomid=0;RequestSendBarrage.prototype.ctype=0;RequestSendBarrage.prototype.content="";RequestSendBarrage.prototype.is_copy=false;RequestSendBarrage.prototype.thump_up_status=0;RequestSendBarrage.create=function create(properties){return new RequestSendBarrage(properties)};RequestSendBarrage.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).int32(m.roomid);if(m.ctype!=null&&Object.hasOwnProperty.call(m,"ctype"))w.uint32(16).int32(m.ctype);if(m.content!=null&&Object.hasOwnProperty.call(m,"content"))w.uint32(26).string(m.content);if(m.is_copy!=null&&Object.hasOwnProperty.call(m,"is_copy"))w.uint32(32).bool(m.is_copy);if(m.thump_up_status!=null&&Object.hasOwnProperty.call(m,"thump_up_status"))w.uint32(40).int32(m.thump_up_status);return w};RequestSendBarrage.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RequestSendBarrage.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.RequestSendBarrage;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.int32();break;case 2:m.ctype=r.int32();break;case 3:m.content=r.string();break;case 4:m.is_copy=r.bool();break;case 5:m.thump_up_status=r.int32();break;default:r.skipType(t&7);break}}return m};RequestSendBarrage.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RequestSendBarrage.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.ctype!=null&&m.hasOwnProperty("ctype")){switch(m.ctype){default:return"ctype: enum value expected";case 0:case 1:case 2:case 3:case 6:break}}if(m.content!=null&&m.hasOwnProperty("content")){if(!$util.isString(m.content))return"content: string expected"}if(m.is_copy!=null&&m.hasOwnProperty("is_copy")){if(typeof m.is_copy!=="boolean")return"is_copy: boolean expected"}if(m.thump_up_status!=null&&m.hasOwnProperty("thump_up_status")){if(!$util.isInteger(m.thump_up_status))return"thump_up_status: integer expected"}return null};RequestSendBarrage.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.RequestSendBarrage)return d;var m=new $root.jackfruit_proto.RequestSendBarrage;if(d.roomid!=null){m.roomid=d.roomid|0}switch(d.ctype){case"Enum_System":case 0:m.ctype=0;break;case"Enum_Custom":case 1:m.ctype=1;break;case"Enum_CardType":case 2:m.ctype=2;break;case"Enum_Liked":case 3:m.ctype=3;break;case"Enum_Profile_Liked":case 6:m.ctype=6;break}if(d.content!=null){m.content=String(d.content)}if(d.is_copy!=null){m.is_copy=Boolean(d.is_copy)}if(d.thump_up_status!=null){m.thump_up_status=d.thump_up_status|0}return m};RequestSendBarrage.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomid=0;d.ctype=o.enums===String?"Enum_System":0;d.content="";d.is_copy=false;d.thump_up_status=0}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.ctype!=null&&m.hasOwnProperty("ctype")){d.ctype=o.enums===String?$root.jackfruit_proto.BarrageType[m.ctype]:m.ctype}if(m.content!=null&&m.hasOwnProperty("content")){d.content=m.content}if(m.is_copy!=null&&m.hasOwnProperty("is_copy")){d.is_copy=m.is_copy}if(m.thump_up_status!=null&&m.hasOwnProperty("thump_up_status")){d.thump_up_status=m.thump_up_status}return d};RequestSendBarrage.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RequestSendBarrage}();jackfruit_proto.ResponseSendBarrage=function(){function ResponseSendBarrage(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ResponseSendBarrage.prototype.error=0;ResponseSendBarrage.prototype.barrageId=0;ResponseSendBarrage.prototype.useCount=$util.Long?$util.Long.fromBits(0,0,true):0;ResponseSendBarrage.prototype.lastSendTime=$util.Long?$util.Long.fromBits(0,0,false):0;ResponseSendBarrage.create=function create(properties){return new ResponseSendBarrage(properties)};ResponseSendBarrage.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.error!=null&&Object.hasOwnProperty.call(m,"error"))w.uint32(8).int32(m.error);if(m.barrageId!=null&&Object.hasOwnProperty.call(m,"barrageId"))w.uint32(16).uint32(m.barrageId);if(m.useCount!=null&&Object.hasOwnProperty.call(m,"useCount"))w.uint32(24).uint64(m.useCount);if(m.lastSendTime!=null&&Object.hasOwnProperty.call(m,"lastSendTime"))w.uint32(32).int64(m.lastSendTime);return w};ResponseSendBarrage.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ResponseSendBarrage.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ResponseSendBarrage;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.error=r.int32();break;case 2:m.barrageId=r.uint32();break;case 3:m.useCount=r.uint64();break;case 4:m.lastSendTime=r.int64();break;default:r.skipType(t&7);break}}return m};ResponseSendBarrage.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ResponseSendBarrage.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.error!=null&&m.hasOwnProperty("error")){if(!$util.isInteger(m.error))return"error: integer expected"}if(m.barrageId!=null&&m.hasOwnProperty("barrageId")){if(!$util.isInteger(m.barrageId))return"barrageId: integer expected"}if(m.useCount!=null&&m.hasOwnProperty("useCount")){if(!$util.isInteger(m.useCount)&&!(m.useCount&&$util.isInteger(m.useCount.low)&&$util.isInteger(m.useCount.high)))return"useCount: integer|Long expected"}if(m.lastSendTime!=null&&m.hasOwnProperty("lastSendTime")){if(!$util.isInteger(m.lastSendTime)&&!(m.lastSendTime&&$util.isInteger(m.lastSendTime.low)&&$util.isInteger(m.lastSendTime.high)))return"lastSendTime: integer|Long expected"}return null};ResponseSendBarrage.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ResponseSendBarrage)return d;var m=new $root.jackfruit_proto.ResponseSendBarrage;if(d.error!=null){m.error=d.error|0}if(d.barrageId!=null){m.barrageId=d.barrageId>>>0}if(d.useCount!=null){if($util.Long)(m.useCount=$util.Long.fromValue(d.useCount)).unsigned=true;else if(typeof d.useCount==="string")m.useCount=parseInt(d.useCount,10);else if(typeof d.useCount==="number")m.useCount=d.useCount;else if(typeof d.useCount==="object")m.useCount=new $util.LongBits(d.useCount.low>>>0,d.useCount.high>>>0).toNumber(true)}if(d.lastSendTime!=null){if($util.Long)(m.lastSendTime=$util.Long.fromValue(d.lastSendTime)).unsigned=false;else if(typeof d.lastSendTime==="string")m.lastSendTime=parseInt(d.lastSendTime,10);else if(typeof d.lastSendTime==="number")m.lastSendTime=d.lastSendTime;else if(typeof d.lastSendTime==="object")m.lastSendTime=new $util.LongBits(d.lastSendTime.low>>>0,d.lastSendTime.high>>>0).toNumber()}return m};ResponseSendBarrage.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.error=0;d.barrageId=0;if($util.Long){var n=new $util.Long(0,0,true);d.useCount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.useCount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.lastSendTime=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.lastSendTime=o.longs===String?"0":0}if(m.error!=null&&m.hasOwnProperty("error")){d.error=m.error}if(m.barrageId!=null&&m.hasOwnProperty("barrageId")){d.barrageId=m.barrageId}if(m.useCount!=null&&m.hasOwnProperty("useCount")){if(typeof m.useCount==="number")d.useCount=o.longs===String?String(m.useCount):m.useCount;else d.useCount=o.longs===String?$util.Long.prototype.toString.call(m.useCount):o.longs===Number?new $util.LongBits(m.useCount.low>>>0,m.useCount.high>>>0).toNumber(true):m.useCount}if(m.lastSendTime!=null&&m.hasOwnProperty("lastSendTime")){if(typeof m.lastSendTime==="number")d.lastSendTime=o.longs===String?String(m.lastSendTime):m.lastSendTime;else d.lastSendTime=o.longs===String?$util.Long.prototype.toString.call(m.lastSendTime):o.longs===Number?new $util.LongBits(m.lastSendTime.low>>>0,m.lastSendTime.high>>>0).toNumber():m.lastSendTime}return d};ResponseSendBarrage.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ResponseSendBarrage}();jackfruit_proto.NoticeSendBarrage=function(){function NoticeSendBarrage(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}NoticeSendBarrage.prototype.roomid=0;NoticeSendBarrage.prototype.ctype=0;NoticeSendBarrage.prototype.content="";NoticeSendBarrage.prototype.playerid=0;NoticeSendBarrage.prototype.nickname="";NoticeSendBarrage.prototype.avatar="";NoticeSendBarrage.prototype.send_time=$util.Long?$util.Long.fromBits(0,0,false):0;NoticeSendBarrage.prototype.thump_up_status=0;NoticeSendBarrage.prototype.liked_nickname="";NoticeSendBarrage.prototype.liked_playerid=0;NoticeSendBarrage.prototype.liked_avatar="";NoticeSendBarrage.prototype.plat=0;NoticeSendBarrage.create=function create(properties){return new NoticeSendBarrage(properties)};NoticeSendBarrage.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).int32(m.roomid);if(m.ctype!=null&&Object.hasOwnProperty.call(m,"ctype"))w.uint32(16).int32(m.ctype);if(m.content!=null&&Object.hasOwnProperty.call(m,"content"))w.uint32(26).string(m.content);if(m.playerid!=null&&Object.hasOwnProperty.call(m,"playerid"))w.uint32(32).uint32(m.playerid);if(m.nickname!=null&&Object.hasOwnProperty.call(m,"nickname"))w.uint32(42).string(m.nickname);if(m.avatar!=null&&Object.hasOwnProperty.call(m,"avatar"))w.uint32(50).string(m.avatar);if(m.send_time!=null&&Object.hasOwnProperty.call(m,"send_time"))w.uint32(56).int64(m.send_time);if(m.thump_up_status!=null&&Object.hasOwnProperty.call(m,"thump_up_status"))w.uint32(64).int32(m.thump_up_status);if(m.liked_nickname!=null&&Object.hasOwnProperty.call(m,"liked_nickname"))w.uint32(90).string(m.liked_nickname);if(m.liked_playerid!=null&&Object.hasOwnProperty.call(m,"liked_playerid"))w.uint32(96).uint32(m.liked_playerid);if(m.liked_avatar!=null&&Object.hasOwnProperty.call(m,"liked_avatar"))w.uint32(106).string(m.liked_avatar);if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(112).uint32(m.plat);return w};NoticeSendBarrage.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};NoticeSendBarrage.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.NoticeSendBarrage;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.int32();break;case 2:m.ctype=r.int32();break;case 3:m.content=r.string();break;case 4:m.playerid=r.uint32();break;case 5:m.nickname=r.string();break;case 6:m.avatar=r.string();break;case 7:m.send_time=r.int64();break;case 8:m.thump_up_status=r.int32();break;case 11:m.liked_nickname=r.string();break;case 12:m.liked_playerid=r.uint32();break;case 13:m.liked_avatar=r.string();break;case 14:m.plat=r.uint32();break;default:r.skipType(t&7);break}}return m};NoticeSendBarrage.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};NoticeSendBarrage.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.ctype!=null&&m.hasOwnProperty("ctype")){switch(m.ctype){default:return"ctype: enum value expected";case 0:case 1:case 2:case 3:case 6:break}}if(m.content!=null&&m.hasOwnProperty("content")){if(!$util.isString(m.content))return"content: string expected"}if(m.playerid!=null&&m.hasOwnProperty("playerid")){if(!$util.isInteger(m.playerid))return"playerid: integer expected"}if(m.nickname!=null&&m.hasOwnProperty("nickname")){if(!$util.isString(m.nickname))return"nickname: string expected"}if(m.avatar!=null&&m.hasOwnProperty("avatar")){if(!$util.isString(m.avatar))return"avatar: string expected"}if(m.send_time!=null&&m.hasOwnProperty("send_time")){if(!$util.isInteger(m.send_time)&&!(m.send_time&&$util.isInteger(m.send_time.low)&&$util.isInteger(m.send_time.high)))return"send_time: integer|Long expected"}if(m.thump_up_status!=null&&m.hasOwnProperty("thump_up_status")){if(!$util.isInteger(m.thump_up_status))return"thump_up_status: integer expected"}if(m.liked_nickname!=null&&m.hasOwnProperty("liked_nickname")){if(!$util.isString(m.liked_nickname))return"liked_nickname: string expected"}if(m.liked_playerid!=null&&m.hasOwnProperty("liked_playerid")){if(!$util.isInteger(m.liked_playerid))return"liked_playerid: integer expected"}if(m.liked_avatar!=null&&m.hasOwnProperty("liked_avatar")){if(!$util.isString(m.liked_avatar))return"liked_avatar: string expected"}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}return null};NoticeSendBarrage.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.NoticeSendBarrage)return d;var m=new $root.jackfruit_proto.NoticeSendBarrage;if(d.roomid!=null){m.roomid=d.roomid|0}switch(d.ctype){case"Enum_System":case 0:m.ctype=0;break;case"Enum_Custom":case 1:m.ctype=1;break;case"Enum_CardType":case 2:m.ctype=2;break;case"Enum_Liked":case 3:m.ctype=3;break;case"Enum_Profile_Liked":case 6:m.ctype=6;break}if(d.content!=null){m.content=String(d.content)}if(d.playerid!=null){m.playerid=d.playerid>>>0}if(d.nickname!=null){m.nickname=String(d.nickname)}if(d.avatar!=null){m.avatar=String(d.avatar)}if(d.send_time!=null){if($util.Long)(m.send_time=$util.Long.fromValue(d.send_time)).unsigned=false;else if(typeof d.send_time==="string")m.send_time=parseInt(d.send_time,10);else if(typeof d.send_time==="number")m.send_time=d.send_time;else if(typeof d.send_time==="object")m.send_time=new $util.LongBits(d.send_time.low>>>0,d.send_time.high>>>0).toNumber()}if(d.thump_up_status!=null){m.thump_up_status=d.thump_up_status|0}if(d.liked_nickname!=null){m.liked_nickname=String(d.liked_nickname)}if(d.liked_playerid!=null){m.liked_playerid=d.liked_playerid>>>0}if(d.liked_avatar!=null){m.liked_avatar=String(d.liked_avatar)}if(d.plat!=null){m.plat=d.plat>>>0}return m};NoticeSendBarrage.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomid=0;d.ctype=o.enums===String?"Enum_System":0;d.content="";d.playerid=0;d.nickname="";d.avatar="";if($util.Long){var n=new $util.Long(0,0,false);d.send_time=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.send_time=o.longs===String?"0":0;d.thump_up_status=0;d.liked_nickname="";d.liked_playerid=0;d.liked_avatar="";d.plat=0}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.ctype!=null&&m.hasOwnProperty("ctype")){d.ctype=o.enums===String?$root.jackfruit_proto.BarrageType[m.ctype]:m.ctype}if(m.content!=null&&m.hasOwnProperty("content")){d.content=m.content}if(m.playerid!=null&&m.hasOwnProperty("playerid")){d.playerid=m.playerid}if(m.nickname!=null&&m.hasOwnProperty("nickname")){d.nickname=m.nickname}if(m.avatar!=null&&m.hasOwnProperty("avatar")){d.avatar=m.avatar}if(m.send_time!=null&&m.hasOwnProperty("send_time")){if(typeof m.send_time==="number")d.send_time=o.longs===String?String(m.send_time):m.send_time;else d.send_time=o.longs===String?$util.Long.prototype.toString.call(m.send_time):o.longs===Number?new $util.LongBits(m.send_time.low>>>0,m.send_time.high>>>0).toNumber():m.send_time}if(m.thump_up_status!=null&&m.hasOwnProperty("thump_up_status")){d.thump_up_status=m.thump_up_status}if(m.liked_nickname!=null&&m.hasOwnProperty("liked_nickname")){d.liked_nickname=m.liked_nickname}if(m.liked_playerid!=null&&m.hasOwnProperty("liked_playerid")){d.liked_playerid=m.liked_playerid}if(m.liked_avatar!=null&&m.hasOwnProperty("liked_avatar")){d.liked_avatar=m.liked_avatar}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}return d};NoticeSendBarrage.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return NoticeSendBarrage}();jackfruit_proto.BarrageCountReq=function(){function BarrageCountReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BarrageCountReq.create=function create(properties){return new BarrageCountReq(properties)};BarrageCountReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};BarrageCountReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BarrageCountReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BarrageCountReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};BarrageCountReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BarrageCountReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};BarrageCountReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BarrageCountReq)return d;return new $root.jackfruit_proto.BarrageCountReq};BarrageCountReq.toObject=function toObject(){return{}};BarrageCountReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BarrageCountReq}();jackfruit_proto.BarrageCountRsp=function(){function BarrageCountRsp(p){this.Infos=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BarrageCountRsp.prototype.error=0;BarrageCountRsp.prototype.Infos=$util.emptyArray;BarrageCountRsp.create=function create(properties){return new BarrageCountRsp(properties)};BarrageCountRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.error!=null&&Object.hasOwnProperty.call(m,"error"))w.uint32(8).int32(m.error);if(m.Infos!=null&&m.Infos.length){for(var i=0;i<m.Infos.length;++i)$root.jackfruit_proto.BarrageCount.encode(m.Infos[i],w.uint32(18).fork()).ldelim()}return w};BarrageCountRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BarrageCountRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BarrageCountRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.error=r.int32();break;case 2:if(!(m.Infos&&m.Infos.length))m.Infos=[];m.Infos.push($root.jackfruit_proto.BarrageCount.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};BarrageCountRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BarrageCountRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.error!=null&&m.hasOwnProperty("error")){switch(m.error){default:return"error: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.Infos!=null&&m.hasOwnProperty("Infos")){if(!Array.isArray(m.Infos))return"Infos: array expected";for(var i=0;i<m.Infos.length;++i){{var e=$root.jackfruit_proto.BarrageCount.verify(m.Infos[i]);if(e)return"Infos."+e}}}return null};BarrageCountRsp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BarrageCountRsp)return d;var m=new $root.jackfruit_proto.BarrageCountRsp;switch(d.error){case"ErrorCode_DUMMY":case 0:m.error=0;break;case"OK":case 1:m.error=1;break;case"FAILED":case 100:m.error=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.error=13e3;break;case"LOW_VERSION":case 13001:m.error=13001;break;case"INVALID_TOKEN":case 13002:m.error=13002;break;case"SERVER_BUSY":case 13003:m.error=13003;break;case"WITHOUT_LOGIN":case 13004:m.error=13004;break;case"ROOM_NOT_MATCH":case 13005:m.error=13005;break;case"ROOM_NOT_EXIST":case 13006:m.error=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.error=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.error=13008;break;case"STOP_SERVICE":case 13013:m.error=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.error=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.error=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.error=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.error=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.error=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.error=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.error=13028;break;case"NO_PLACE_CARDS":case 13029:m.error=13029;break;case"BAD_REQ_PARAM":case 13030:m.error=13030;break;case"DISALLOWED_OPERATION":case 13031:m.error=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.error=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.error=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.error=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.error=13035;break;case"Action_Delay_Exhausted":case 13036:m.error=13036;break;case"Player_Limit_BuyIn":case 13037:m.error=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.error=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.error=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.error=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.error=13041;break;case"NOT_SETTLED_YET":case 13042:m.error=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.error=13043;break;case"NO_JACKPOT":case 13045:m.error=13045;break;case"GameServer_Player_Not_Found":case 3:m.error=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.error=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.error=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.error=1215;break;case"NeedAuthVerify":case 1260:m.error=1260;break;case"WaitAuthRefreshCD":case 1261:m.error=1261;break;case"AlreadyLiked":case 1252:m.error=1252;break;case"Param_Validate":case 1253:m.error=1253;break;case"IsEmojiFree":case 116:m.error=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.error=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.error=31118;break}if(d.Infos){if(!Array.isArray(d.Infos))throw TypeError(".jackfruit_proto.BarrageCountRsp.Infos: array expected");m.Infos=[];for(var i=0;i<d.Infos.length;++i){if(typeof d.Infos[i]!=="object")throw TypeError(".jackfruit_proto.BarrageCountRsp.Infos: object expected");m.Infos[i]=$root.jackfruit_proto.BarrageCount.fromObject(d.Infos[i])}}return m};BarrageCountRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.Infos=[]}if(o.defaults){d.error=o.enums===String?"ErrorCode_DUMMY":0}if(m.error!=null&&m.hasOwnProperty("error")){d.error=o.enums===String?$root.jackfruit_proto.ErrorCode[m.error]:m.error}if(m.Infos&&m.Infos.length){d.Infos=[];for(var j=0;j<m.Infos.length;++j){d.Infos[j]=$root.jackfruit_proto.BarrageCount.toObject(m.Infos[j],o)}}return d};BarrageCountRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BarrageCountRsp}();jackfruit_proto.BarrageCount=function(){function BarrageCount(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BarrageCount.prototype.BarrageId=0;BarrageCount.prototype.UseCount=$util.Long?$util.Long.fromBits(0,0,true):0;BarrageCount.prototype.lastSendTime=$util.Long?$util.Long.fromBits(0,0,false):0;BarrageCount.create=function create(properties){return new BarrageCount(properties)};BarrageCount.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.BarrageId!=null&&Object.hasOwnProperty.call(m,"BarrageId"))w.uint32(8).uint32(m.BarrageId);if(m.UseCount!=null&&Object.hasOwnProperty.call(m,"UseCount"))w.uint32(16).uint64(m.UseCount);if(m.lastSendTime!=null&&Object.hasOwnProperty.call(m,"lastSendTime"))w.uint32(24).int64(m.lastSendTime);return w};BarrageCount.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BarrageCount.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BarrageCount;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.BarrageId=r.uint32();break;case 2:m.UseCount=r.uint64();break;case 3:m.lastSendTime=r.int64();break;default:r.skipType(t&7);break}}return m};BarrageCount.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BarrageCount.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.BarrageId!=null&&m.hasOwnProperty("BarrageId")){if(!$util.isInteger(m.BarrageId))return"BarrageId: integer expected"}if(m.UseCount!=null&&m.hasOwnProperty("UseCount")){if(!$util.isInteger(m.UseCount)&&!(m.UseCount&&$util.isInteger(m.UseCount.low)&&$util.isInteger(m.UseCount.high)))return"UseCount: integer|Long expected"}if(m.lastSendTime!=null&&m.hasOwnProperty("lastSendTime")){if(!$util.isInteger(m.lastSendTime)&&!(m.lastSendTime&&$util.isInteger(m.lastSendTime.low)&&$util.isInteger(m.lastSendTime.high)))return"lastSendTime: integer|Long expected"}return null};BarrageCount.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BarrageCount)return d;var m=new $root.jackfruit_proto.BarrageCount;if(d.BarrageId!=null){m.BarrageId=d.BarrageId>>>0}if(d.UseCount!=null){if($util.Long)(m.UseCount=$util.Long.fromValue(d.UseCount)).unsigned=true;else if(typeof d.UseCount==="string")m.UseCount=parseInt(d.UseCount,10);else if(typeof d.UseCount==="number")m.UseCount=d.UseCount;else if(typeof d.UseCount==="object")m.UseCount=new $util.LongBits(d.UseCount.low>>>0,d.UseCount.high>>>0).toNumber(true)}if(d.lastSendTime!=null){if($util.Long)(m.lastSendTime=$util.Long.fromValue(d.lastSendTime)).unsigned=false;else if(typeof d.lastSendTime==="string")m.lastSendTime=parseInt(d.lastSendTime,10);else if(typeof d.lastSendTime==="number")m.lastSendTime=d.lastSendTime;else if(typeof d.lastSendTime==="object")m.lastSendTime=new $util.LongBits(d.lastSendTime.low>>>0,d.lastSendTime.high>>>0).toNumber()}return m};BarrageCount.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.BarrageId=0;if($util.Long){var n=new $util.Long(0,0,true);d.UseCount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.UseCount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.lastSendTime=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.lastSendTime=o.longs===String?"0":0}if(m.BarrageId!=null&&m.hasOwnProperty("BarrageId")){d.BarrageId=m.BarrageId}if(m.UseCount!=null&&m.hasOwnProperty("UseCount")){if(typeof m.UseCount==="number")d.UseCount=o.longs===String?String(m.UseCount):m.UseCount;else d.UseCount=o.longs===String?$util.Long.prototype.toString.call(m.UseCount):o.longs===Number?new $util.LongBits(m.UseCount.low>>>0,m.UseCount.high>>>0).toNumber(true):m.UseCount}if(m.lastSendTime!=null&&m.hasOwnProperty("lastSendTime")){if(typeof m.lastSendTime==="number")d.lastSendTime=o.longs===String?String(m.lastSendTime):m.lastSendTime;else d.lastSendTime=o.longs===String?$util.Long.prototype.toString.call(m.lastSendTime):o.longs===Number?new $util.LongBits(m.lastSendTime.low>>>0,m.lastSendTime.high>>>0).toNumber():m.lastSendTime}return d};BarrageCount.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BarrageCount}();jackfruit_proto.ShowPlaceCardsNotify=function(){function ShowPlaceCardsNotify(p){this.player=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ShowPlaceCardsNotify.prototype.player=$util.emptyArray;ShowPlaceCardsNotify.create=function create(properties){return new ShowPlaceCardsNotify(properties)};ShowPlaceCardsNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.player!=null&&m.player.length){for(var i=0;i<m.player.length;++i)$root.jackfruit_proto.PlayerInfo.encode(m.player[i],w.uint32(10).fork()).ldelim()}return w};ShowPlaceCardsNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ShowPlaceCardsNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ShowPlaceCardsNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.player&&m.player.length))m.player=[];m.player.push($root.jackfruit_proto.PlayerInfo.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};ShowPlaceCardsNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ShowPlaceCardsNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.player!=null&&m.hasOwnProperty("player")){if(!Array.isArray(m.player))return"player: array expected";for(var i=0;i<m.player.length;++i){{var e=$root.jackfruit_proto.PlayerInfo.verify(m.player[i]);if(e)return"player."+e}}}return null};ShowPlaceCardsNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ShowPlaceCardsNotify)return d;var m=new $root.jackfruit_proto.ShowPlaceCardsNotify;if(d.player){if(!Array.isArray(d.player))throw TypeError(".jackfruit_proto.ShowPlaceCardsNotify.player: array expected");m.player=[];for(var i=0;i<d.player.length;++i){if(typeof d.player[i]!=="object")throw TypeError(".jackfruit_proto.ShowPlaceCardsNotify.player: object expected");m.player[i]=$root.jackfruit_proto.PlayerInfo.fromObject(d.player[i])}}return m};ShowPlaceCardsNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.player=[]}if(m.player&&m.player.length){d.player=[];for(var j=0;j<m.player.length;++j){d.player[j]=$root.jackfruit_proto.PlayerInfo.toObject(m.player[j],o)}}return d};ShowPlaceCardsNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ShowPlaceCardsNotify}();jackfruit_proto.WaitingOtherPlayerNotify=function(){function WaitingOtherPlayerNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}WaitingOtherPlayerNotify.create=function create(properties){return new WaitingOtherPlayerNotify(properties)};WaitingOtherPlayerNotify.encode=function encode(m,w){if(!w)w=$Writer.create();return w};WaitingOtherPlayerNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};WaitingOtherPlayerNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.WaitingOtherPlayerNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};WaitingOtherPlayerNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};WaitingOtherPlayerNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};WaitingOtherPlayerNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.WaitingOtherPlayerNotify)return d;return new $root.jackfruit_proto.WaitingOtherPlayerNotify};WaitingOtherPlayerNotify.toObject=function toObject(){return{}};WaitingOtherPlayerNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return WaitingOtherPlayerNotify}();jackfruit_proto.CanOperationNotify=function(){function CanOperationNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CanOperationNotify.prototype.changeTable=false;CanOperationNotify.create=function create(properties){return new CanOperationNotify(properties)};CanOperationNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.changeTable!=null&&Object.hasOwnProperty.call(m,"changeTable"))w.uint32(8).bool(m.changeTable);return w};CanOperationNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CanOperationNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.CanOperationNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.changeTable=r.bool();break;default:r.skipType(t&7);break}}return m};CanOperationNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CanOperationNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.changeTable!=null&&m.hasOwnProperty("changeTable")){if(typeof m.changeTable!=="boolean")return"changeTable: boolean expected"}return null};CanOperationNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.CanOperationNotify)return d;var m=new $root.jackfruit_proto.CanOperationNotify;if(d.changeTable!=null){m.changeTable=Boolean(d.changeTable)}return m};CanOperationNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.changeTable=false}if(m.changeTable!=null&&m.hasOwnProperty("changeTable")){d.changeTable=m.changeTable}return d};CanOperationNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CanOperationNotify}();jackfruit_proto.ChangeTableReq=function(){function ChangeTableReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ChangeTableReq.create=function create(properties){return new ChangeTableReq(properties)};ChangeTableReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};ChangeTableReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ChangeTableReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ChangeTableReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};ChangeTableReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ChangeTableReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};ChangeTableReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ChangeTableReq)return d;return new $root.jackfruit_proto.ChangeTableReq};ChangeTableReq.toObject=function toObject(){return{}};ChangeTableReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ChangeTableReq}();jackfruit_proto.ChangeTableResp=function(){function ChangeTableResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ChangeTableResp.prototype.code=0;ChangeTableResp.prototype.authVerifyCD=0;ChangeTableResp.create=function create(properties){return new ChangeTableResp(properties)};ChangeTableResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.authVerifyCD!=null&&Object.hasOwnProperty.call(m,"authVerifyCD"))w.uint32(16).uint32(m.authVerifyCD);return w};ChangeTableResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ChangeTableResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ChangeTableResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.authVerifyCD=r.uint32();break;default:r.skipType(t&7);break}}return m};ChangeTableResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ChangeTableResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.authVerifyCD!=null&&m.hasOwnProperty("authVerifyCD")){if(!$util.isInteger(m.authVerifyCD))return"authVerifyCD: integer expected"}return null};ChangeTableResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ChangeTableResp)return d;var m=new $root.jackfruit_proto.ChangeTableResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.authVerifyCD!=null){m.authVerifyCD=d.authVerifyCD>>>0}return m};ChangeTableResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.authVerifyCD=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.authVerifyCD!=null&&m.hasOwnProperty("authVerifyCD")){d.authVerifyCD=m.authVerifyCD}return d};ChangeTableResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ChangeTableResp}();jackfruit_proto.SettleReq=function(){function SettleReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SettleReq.create=function create(properties){return new SettleReq(properties)};SettleReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};SettleReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SettleReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SettleReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};SettleReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SettleReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};SettleReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SettleReq)return d;return new $root.jackfruit_proto.SettleReq};SettleReq.toObject=function toObject(){return{}};SettleReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SettleReq}();jackfruit_proto.SettleResp=function(){function SettleResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SettleResp.prototype.code=0;SettleResp.prototype.settleScore=$util.Long?$util.Long.fromBits(0,0,false):0;SettleResp.prototype.settleAmount=$util.Long?$util.Long.fromBits(0,0,false):0;SettleResp.create=function create(properties){return new SettleResp(properties)};SettleResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.settleScore!=null&&Object.hasOwnProperty.call(m,"settleScore"))w.uint32(16).int64(m.settleScore);if(m.settleAmount!=null&&Object.hasOwnProperty.call(m,"settleAmount"))w.uint32(24).int64(m.settleAmount);return w};SettleResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SettleResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.SettleResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.settleScore=r.int64();break;case 3:m.settleAmount=r.int64();break;default:r.skipType(t&7);break}}return m};SettleResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SettleResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.settleScore!=null&&m.hasOwnProperty("settleScore")){if(!$util.isInteger(m.settleScore)&&!(m.settleScore&&$util.isInteger(m.settleScore.low)&&$util.isInteger(m.settleScore.high)))return"settleScore: integer|Long expected"}if(m.settleAmount!=null&&m.hasOwnProperty("settleAmount")){if(!$util.isInteger(m.settleAmount)&&!(m.settleAmount&&$util.isInteger(m.settleAmount.low)&&$util.isInteger(m.settleAmount.high)))return"settleAmount: integer|Long expected"}return null};SettleResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.SettleResp)return d;var m=new $root.jackfruit_proto.SettleResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.settleScore!=null){if($util.Long)(m.settleScore=$util.Long.fromValue(d.settleScore)).unsigned=false;else if(typeof d.settleScore==="string")m.settleScore=parseInt(d.settleScore,10);else if(typeof d.settleScore==="number")m.settleScore=d.settleScore;else if(typeof d.settleScore==="object")m.settleScore=new $util.LongBits(d.settleScore.low>>>0,d.settleScore.high>>>0).toNumber()}if(d.settleAmount!=null){if($util.Long)(m.settleAmount=$util.Long.fromValue(d.settleAmount)).unsigned=false;else if(typeof d.settleAmount==="string")m.settleAmount=parseInt(d.settleAmount,10);else if(typeof d.settleAmount==="number")m.settleAmount=d.settleAmount;else if(typeof d.settleAmount==="object")m.settleAmount=new $util.LongBits(d.settleAmount.low>>>0,d.settleAmount.high>>>0).toNumber()}return m};SettleResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.settleScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.settleScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.settleAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.settleAmount=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.settleScore!=null&&m.hasOwnProperty("settleScore")){if(typeof m.settleScore==="number")d.settleScore=o.longs===String?String(m.settleScore):m.settleScore;else d.settleScore=o.longs===String?$util.Long.prototype.toString.call(m.settleScore):o.longs===Number?new $util.LongBits(m.settleScore.low>>>0,m.settleScore.high>>>0).toNumber():m.settleScore}if(m.settleAmount!=null&&m.hasOwnProperty("settleAmount")){if(typeof m.settleAmount==="number")d.settleAmount=o.longs===String?String(m.settleAmount):m.settleAmount;else d.settleAmount=o.longs===String?$util.Long.prototype.toString.call(m.settleAmount):o.longs===Number?new $util.LongBits(m.settleAmount.low>>>0,m.settleAmount.high>>>0).toNumber():m.settleAmount}return d};SettleResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SettleResp}();jackfruit_proto.PlayerInfoSyncNotify=function(){function PlayerInfoSyncNotify(p){this.playerList=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerInfoSyncNotify.prototype.playerList=$util.emptyArray;PlayerInfoSyncNotify.create=function create(properties){return new PlayerInfoSyncNotify(properties)};PlayerInfoSyncNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerList!=null&&m.playerList.length){for(var i=0;i<m.playerList.length;++i)$root.jackfruit_proto.PlayerInfo.encode(m.playerList[i],w.uint32(10).fork()).ldelim()}return w};PlayerInfoSyncNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerInfoSyncNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.PlayerInfoSyncNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.playerList&&m.playerList.length))m.playerList=[];m.playerList.push($root.jackfruit_proto.PlayerInfo.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};PlayerInfoSyncNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerInfoSyncNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerList!=null&&m.hasOwnProperty("playerList")){if(!Array.isArray(m.playerList))return"playerList: array expected";for(var i=0;i<m.playerList.length;++i){{var e=$root.jackfruit_proto.PlayerInfo.verify(m.playerList[i]);if(e)return"playerList."+e}}}return null};PlayerInfoSyncNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.PlayerInfoSyncNotify)return d;var m=new $root.jackfruit_proto.PlayerInfoSyncNotify;if(d.playerList){if(!Array.isArray(d.playerList))throw TypeError(".jackfruit_proto.PlayerInfoSyncNotify.playerList: array expected");m.playerList=[];for(var i=0;i<d.playerList.length;++i){if(typeof d.playerList[i]!=="object")throw TypeError(".jackfruit_proto.PlayerInfoSyncNotify.playerList: object expected");m.playerList[i]=$root.jackfruit_proto.PlayerInfo.fromObject(d.playerList[i])}}return m};PlayerInfoSyncNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.playerList=[]}if(m.playerList&&m.playerList.length){d.playerList=[];for(var j=0;j<m.playerList.length;++j){d.playerList[j]=$root.jackfruit_proto.PlayerInfo.toObject(m.playerList[j],o)}}return d};PlayerInfoSyncNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerInfoSyncNotify}();jackfruit_proto.StartMatchNotify=function(){function StartMatchNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StartMatchNotify.prototype.startTime=$util.Long?$util.Long.fromBits(0,0,false):0;StartMatchNotify.create=function create(properties){return new StartMatchNotify(properties)};StartMatchNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.startTime!=null&&Object.hasOwnProperty.call(m,"startTime"))w.uint32(8).int64(m.startTime);return w};StartMatchNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StartMatchNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.StartMatchNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.startTime=r.int64();break;default:r.skipType(t&7);break}}return m};StartMatchNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StartMatchNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.startTime!=null&&m.hasOwnProperty("startTime")){if(!$util.isInteger(m.startTime)&&!(m.startTime&&$util.isInteger(m.startTime.low)&&$util.isInteger(m.startTime.high)))return"startTime: integer|Long expected"}return null};StartMatchNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.StartMatchNotify)return d;var m=new $root.jackfruit_proto.StartMatchNotify;if(d.startTime!=null){if($util.Long)(m.startTime=$util.Long.fromValue(d.startTime)).unsigned=false;else if(typeof d.startTime==="string")m.startTime=parseInt(d.startTime,10);else if(typeof d.startTime==="number")m.startTime=d.startTime;else if(typeof d.startTime==="object")m.startTime=new $util.LongBits(d.startTime.low>>>0,d.startTime.high>>>0).toNumber()}return m};StartMatchNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.startTime=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.startTime=o.longs===String?"0":0}if(m.startTime!=null&&m.hasOwnProperty("startTime")){if(typeof m.startTime==="number")d.startTime=o.longs===String?String(m.startTime):m.startTime;else d.startTime=o.longs===String?$util.Long.prototype.toString.call(m.startTime):o.longs===Number?new $util.LongBits(m.startTime.low>>>0,m.startTime.high>>>0).toNumber():m.startTime}return d};StartMatchNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StartMatchNotify}();jackfruit_proto.MatchResultNotify=function(){function MatchResultNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}MatchResultNotify.prototype.roomId=0;MatchResultNotify.prototype.result=0;MatchResultNotify.create=function create(properties){return new MatchResultNotify(properties)};MatchResultNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.result!=null&&Object.hasOwnProperty.call(m,"result"))w.uint32(16).int32(m.result);return w};MatchResultNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};MatchResultNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.MatchResultNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:m.result=r.int32();break;default:r.skipType(t&7);break}}return m};MatchResultNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};MatchResultNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.result!=null&&m.hasOwnProperty("result")){if(!$util.isInteger(m.result))return"result: integer expected"}return null};MatchResultNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.MatchResultNotify)return d;var m=new $root.jackfruit_proto.MatchResultNotify;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.result!=null){m.result=d.result|0}return m};MatchResultNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0;d.result=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.result!=null&&m.hasOwnProperty("result")){d.result=m.result}return d};MatchResultNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return MatchResultNotify}();jackfruit_proto.GetGameUUIdsReq=function(){function GetGameUUIdsReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GetGameUUIdsReq.create=function create(properties){return new GetGameUUIdsReq(properties)};GetGameUUIdsReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};GetGameUUIdsReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GetGameUUIdsReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GetGameUUIdsReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};GetGameUUIdsReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GetGameUUIdsReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};GetGameUUIdsReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GetGameUUIdsReq)return d;return new $root.jackfruit_proto.GetGameUUIdsReq};GetGameUUIdsReq.toObject=function toObject(){return{}};GetGameUUIdsReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GetGameUUIdsReq}();jackfruit_proto.GetGameUUIdsResp=function(){function GetGameUUIdsResp(p){this.list=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GetGameUUIdsResp.prototype.code=0;GetGameUUIdsResp.prototype.Total=$util.Long?$util.Long.fromBits(0,0,false):0;GetGameUUIdsResp.prototype.Page=$util.Long?$util.Long.fromBits(0,0,false):0;GetGameUUIdsResp.prototype.list=$util.emptyArray;GetGameUUIdsResp.create=function create(properties){return new GetGameUUIdsResp(properties)};GetGameUUIdsResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.Total!=null&&Object.hasOwnProperty.call(m,"Total"))w.uint32(16).int64(m.Total);if(m.Page!=null&&Object.hasOwnProperty.call(m,"Page"))w.uint32(24).int64(m.Page);if(m.list!=null&&m.list.length){for(var i=0;i<m.list.length;++i)$root.jackfruit_proto.JsStringGameUUid.encode(m.list[i],w.uint32(34).fork()).ldelim()}return w};GetGameUUIdsResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GetGameUUIdsResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GetGameUUIdsResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.Total=r.int64();break;case 3:m.Page=r.int64();break;case 4:if(!(m.list&&m.list.length))m.list=[];m.list.push($root.jackfruit_proto.JsStringGameUUid.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};GetGameUUIdsResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GetGameUUIdsResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.Total!=null&&m.hasOwnProperty("Total")){if(!$util.isInteger(m.Total)&&!(m.Total&&$util.isInteger(m.Total.low)&&$util.isInteger(m.Total.high)))return"Total: integer|Long expected"}if(m.Page!=null&&m.hasOwnProperty("Page")){if(!$util.isInteger(m.Page)&&!(m.Page&&$util.isInteger(m.Page.low)&&$util.isInteger(m.Page.high)))return"Page: integer|Long expected"}if(m.list!=null&&m.hasOwnProperty("list")){if(!Array.isArray(m.list))return"list: array expected";for(var i=0;i<m.list.length;++i){{var e=$root.jackfruit_proto.JsStringGameUUid.verify(m.list[i]);if(e)return"list."+e}}}return null};GetGameUUIdsResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GetGameUUIdsResp)return d;var m=new $root.jackfruit_proto.GetGameUUIdsResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.Total!=null){if($util.Long)(m.Total=$util.Long.fromValue(d.Total)).unsigned=false;else if(typeof d.Total==="string")m.Total=parseInt(d.Total,10);else if(typeof d.Total==="number")m.Total=d.Total;else if(typeof d.Total==="object")m.Total=new $util.LongBits(d.Total.low>>>0,d.Total.high>>>0).toNumber()}if(d.Page!=null){if($util.Long)(m.Page=$util.Long.fromValue(d.Page)).unsigned=false;else if(typeof d.Page==="string")m.Page=parseInt(d.Page,10);else if(typeof d.Page==="number")m.Page=d.Page;else if(typeof d.Page==="object")m.Page=new $util.LongBits(d.Page.low>>>0,d.Page.high>>>0).toNumber()}if(d.list){if(!Array.isArray(d.list))throw TypeError(".jackfruit_proto.GetGameUUIdsResp.list: array expected");m.list=[];for(var i=0;i<d.list.length;++i){if(typeof d.list[i]!=="object")throw TypeError(".jackfruit_proto.GetGameUUIdsResp.list: object expected");m.list[i]=$root.jackfruit_proto.JsStringGameUUid.fromObject(d.list[i])}}return m};GetGameUUIdsResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.list=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.Total=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.Total=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.Page=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.Page=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.Total!=null&&m.hasOwnProperty("Total")){if(typeof m.Total==="number")d.Total=o.longs===String?String(m.Total):m.Total;else d.Total=o.longs===String?$util.Long.prototype.toString.call(m.Total):o.longs===Number?new $util.LongBits(m.Total.low>>>0,m.Total.high>>>0).toNumber():m.Total}if(m.Page!=null&&m.hasOwnProperty("Page")){if(typeof m.Page==="number")d.Page=o.longs===String?String(m.Page):m.Page;else d.Page=o.longs===String?$util.Long.prototype.toString.call(m.Page):o.longs===Number?new $util.LongBits(m.Page.low>>>0,m.Page.high>>>0).toNumber():m.Page}if(m.list&&m.list.length){d.list=[];for(var j=0;j<m.list.length;++j){d.list[j]=$root.jackfruit_proto.JsStringGameUUid.toObject(m.list[j],o)}}return d};GetGameUUIdsResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GetGameUUIdsResp}();jackfruit_proto.JsStringGameUUid=function(){function JsStringGameUUid(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JsStringGameUUid.prototype.game_uuid_js="";JsStringGameUUid.create=function create(properties){return new JsStringGameUUid(properties)};JsStringGameUUid.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.game_uuid_js!=null&&Object.hasOwnProperty.call(m,"game_uuid_js"))w.uint32(10).string(m.game_uuid_js);return w};JsStringGameUUid.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JsStringGameUUid.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JsStringGameUUid;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.game_uuid_js=r.string();break;default:r.skipType(t&7);break}}return m};JsStringGameUUid.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JsStringGameUUid.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.game_uuid_js!=null&&m.hasOwnProperty("game_uuid_js")){if(!$util.isString(m.game_uuid_js))return"game_uuid_js: string expected"}return null};JsStringGameUUid.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JsStringGameUUid)return d;var m=new $root.jackfruit_proto.JsStringGameUUid;if(d.game_uuid_js!=null){m.game_uuid_js=String(d.game_uuid_js)}return m};JsStringGameUUid.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.game_uuid_js=""}if(m.game_uuid_js!=null&&m.hasOwnProperty("game_uuid_js")){d.game_uuid_js=m.game_uuid_js}return d};JsStringGameUUid.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JsStringGameUUid}();jackfruit_proto.ModifyPlaceCardsNotify=function(){function ModifyPlaceCardsNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ModifyPlaceCardsNotify.prototype.roomId=0;ModifyPlaceCardsNotify.prototype.seatId=0;ModifyPlaceCardsNotify.create=function create(properties){return new ModifyPlaceCardsNotify(properties)};ModifyPlaceCardsNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.seatId!=null&&Object.hasOwnProperty.call(m,"seatId"))w.uint32(16).int32(m.seatId);return w};ModifyPlaceCardsNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ModifyPlaceCardsNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ModifyPlaceCardsNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:m.seatId=r.int32();break;default:r.skipType(t&7);break}}return m};ModifyPlaceCardsNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ModifyPlaceCardsNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.seatId!=null&&m.hasOwnProperty("seatId")){if(!$util.isInteger(m.seatId))return"seatId: integer expected"}return null};ModifyPlaceCardsNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ModifyPlaceCardsNotify)return d;var m=new $root.jackfruit_proto.ModifyPlaceCardsNotify;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.seatId!=null){m.seatId=d.seatId|0}return m};ModifyPlaceCardsNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomId=0;d.seatId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.seatId!=null&&m.hasOwnProperty("seatId")){d.seatId=m.seatId}return d};ModifyPlaceCardsNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ModifyPlaceCardsNotify}();jackfruit_proto.BrandBarrageType=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="BrandBarrageType_DUMMY"]=0;values[valuesById[1]="LIKE"]=1;values[valuesById[2]="DESPISE"]=2;return values}();jackfruit_proto.BrandBarrageNotify=function(){function BrandBarrageNotify(p){this.infos=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BrandBarrageNotify.prototype.roomId=0;BrandBarrageNotify.prototype.infos=$util.emptyArray;BrandBarrageNotify.create=function create(properties){return new BrandBarrageNotify(properties)};BrandBarrageNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomId!=null&&Object.hasOwnProperty.call(m,"roomId"))w.uint32(8).int32(m.roomId);if(m.infos!=null&&m.infos.length){for(var i=0;i<m.infos.length;++i)$root.jackfruit_proto.BrandBarrageInfo.encode(m.infos[i],w.uint32(18).fork()).ldelim()}return w};BrandBarrageNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BrandBarrageNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BrandBarrageNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomId=r.int32();break;case 2:if(!(m.infos&&m.infos.length))m.infos=[];m.infos.push($root.jackfruit_proto.BrandBarrageInfo.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};BrandBarrageNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BrandBarrageNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomId!=null&&m.hasOwnProperty("roomId")){if(!$util.isInteger(m.roomId))return"roomId: integer expected"}if(m.infos!=null&&m.hasOwnProperty("infos")){if(!Array.isArray(m.infos))return"infos: array expected";for(var i=0;i<m.infos.length;++i){{var e=$root.jackfruit_proto.BrandBarrageInfo.verify(m.infos[i]);if(e)return"infos."+e}}}return null};BrandBarrageNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BrandBarrageNotify)return d;var m=new $root.jackfruit_proto.BrandBarrageNotify;if(d.roomId!=null){m.roomId=d.roomId|0}if(d.infos){if(!Array.isArray(d.infos))throw TypeError(".jackfruit_proto.BrandBarrageNotify.infos: array expected");m.infos=[];for(var i=0;i<d.infos.length;++i){if(typeof d.infos[i]!=="object")throw TypeError(".jackfruit_proto.BrandBarrageNotify.infos: object expected");m.infos[i]=$root.jackfruit_proto.BrandBarrageInfo.fromObject(d.infos[i])}}return m};BrandBarrageNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.infos=[]}if(o.defaults){d.roomId=0}if(m.roomId!=null&&m.hasOwnProperty("roomId")){d.roomId=m.roomId}if(m.infos&&m.infos.length){d.infos=[];for(var j=0;j<m.infos.length;++j){d.infos[j]=$root.jackfruit_proto.BrandBarrageInfo.toObject(m.infos[j],o)}}return d};BrandBarrageNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BrandBarrageNotify}();jackfruit_proto.BrandBarrageInfo=function(){function BrandBarrageInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BrandBarrageInfo.prototype.card=null;BrandBarrageInfo.prototype.type=0;BrandBarrageInfo.prototype.index=0;BrandBarrageInfo.prototype.hasReverse=false;BrandBarrageInfo.create=function create(properties){return new BrandBarrageInfo(properties)};BrandBarrageInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.card!=null&&Object.hasOwnProperty.call(m,"card"))$root.jackfruit_proto.CardItem.encode(m.card,w.uint32(10).fork()).ldelim();if(m.type!=null&&Object.hasOwnProperty.call(m,"type"))w.uint32(16).int32(m.type);if(m.index!=null&&Object.hasOwnProperty.call(m,"index"))w.uint32(24).int32(m.index);if(m.hasReverse!=null&&Object.hasOwnProperty.call(m,"hasReverse"))w.uint32(32).bool(m.hasReverse);return w};BrandBarrageInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BrandBarrageInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.BrandBarrageInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.card=$root.jackfruit_proto.CardItem.decode(r,r.uint32());break;case 2:m.type=r.int32();break;case 3:m.index=r.int32();break;case 4:m.hasReverse=r.bool();break;default:r.skipType(t&7);break}}return m};BrandBarrageInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BrandBarrageInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.card!=null&&m.hasOwnProperty("card")){{var e=$root.jackfruit_proto.CardItem.verify(m.card);if(e)return"card."+e}}if(m.type!=null&&m.hasOwnProperty("type")){switch(m.type){default:return"type: enum value expected";case 0:case 1:case 2:break}}if(m.index!=null&&m.hasOwnProperty("index")){if(!$util.isInteger(m.index))return"index: integer expected"}if(m.hasReverse!=null&&m.hasOwnProperty("hasReverse")){if(typeof m.hasReverse!=="boolean")return"hasReverse: boolean expected"}return null};BrandBarrageInfo.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.BrandBarrageInfo)return d;var m=new $root.jackfruit_proto.BrandBarrageInfo;if(d.card!=null){if(typeof d.card!=="object")throw TypeError(".jackfruit_proto.BrandBarrageInfo.card: object expected");m.card=$root.jackfruit_proto.CardItem.fromObject(d.card)}switch(d.type){case"BrandBarrageType_DUMMY":case 0:m.type=0;break;case"LIKE":case 1:m.type=1;break;case"DESPISE":case 2:m.type=2;break}if(d.index!=null){m.index=d.index|0}if(d.hasReverse!=null){m.hasReverse=Boolean(d.hasReverse)}return m};BrandBarrageInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.card=null;d.type=o.enums===String?"BrandBarrageType_DUMMY":0;d.index=0;d.hasReverse=false}if(m.card!=null&&m.hasOwnProperty("card")){d.card=$root.jackfruit_proto.CardItem.toObject(m.card,o)}if(m.type!=null&&m.hasOwnProperty("type")){d.type=o.enums===String?$root.jackfruit_proto.BrandBarrageType[m.type]:m.type}if(m.index!=null&&m.hasOwnProperty("index")){d.index=m.index}if(m.hasReverse!=null&&m.hasOwnProperty("hasReverse")){d.hasReverse=m.hasReverse}return d};BrandBarrageInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BrandBarrageInfo}();jackfruit_proto.JackpotDataReq=function(){function JackpotDataReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotDataReq.prototype.ante=$util.Long?$util.Long.fromBits(0,0,false):0;JackpotDataReq.create=function create(properties){return new JackpotDataReq(properties)};JackpotDataReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.ante!=null&&Object.hasOwnProperty.call(m,"ante"))w.uint32(8).int64(m.ante);return w};JackpotDataReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotDataReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JackpotDataReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.ante=r.int64();break;default:r.skipType(t&7);break}}return m};JackpotDataReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotDataReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.ante!=null&&m.hasOwnProperty("ante")){if(!$util.isInteger(m.ante)&&!(m.ante&&$util.isInteger(m.ante.low)&&$util.isInteger(m.ante.high)))return"ante: integer|Long expected"}return null};JackpotDataReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JackpotDataReq)return d;var m=new $root.jackfruit_proto.JackpotDataReq;if(d.ante!=null){if($util.Long)(m.ante=$util.Long.fromValue(d.ante)).unsigned=false;else if(typeof d.ante==="string")m.ante=parseInt(d.ante,10);else if(typeof d.ante==="number")m.ante=d.ante;else if(typeof d.ante==="object")m.ante=new $util.LongBits(d.ante.low>>>0,d.ante.high>>>0).toNumber()}return m};JackpotDataReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.ante=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.ante=o.longs===String?"0":0}if(m.ante!=null&&m.hasOwnProperty("ante")){if(typeof m.ante==="number")d.ante=o.longs===String?String(m.ante):m.ante;else d.ante=o.longs===String?$util.Long.prototype.toString.call(m.ante):o.longs===Number?new $util.LongBits(m.ante.low>>>0,m.ante.high>>>0).toNumber():m.ante}return d};JackpotDataReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotDataReq}();jackfruit_proto.JackpotDataResp=function(){function JackpotDataResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotDataResp.prototype.code=0;JackpotDataResp.prototype.data=null;JackpotDataResp.create=function create(properties){return new JackpotDataResp(properties)};JackpotDataResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.data!=null&&Object.hasOwnProperty.call(m,"data"))$root.jackfruit_proto.JackpotDataInfo.encode(m.data,w.uint32(18).fork()).ldelim();return w};JackpotDataResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotDataResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JackpotDataResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.data=$root.jackfruit_proto.JackpotDataInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};JackpotDataResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotDataResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.data!=null&&m.hasOwnProperty("data")){{var e=$root.jackfruit_proto.JackpotDataInfo.verify(m.data);if(e)return"data."+e}}return null};JackpotDataResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JackpotDataResp)return d;var m=new $root.jackfruit_proto.JackpotDataResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.data!=null){if(typeof d.data!=="object")throw TypeError(".jackfruit_proto.JackpotDataResp.data: object expected");m.data=$root.jackfruit_proto.JackpotDataInfo.fromObject(d.data)}return m};JackpotDataResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.data=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.data!=null&&m.hasOwnProperty("data")){d.data=$root.jackfruit_proto.JackpotDataInfo.toObject(m.data,o)}return d};JackpotDataResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotDataResp}();jackfruit_proto.JackpotDataInfo=function(){function JackpotDataInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotDataInfo.prototype.leftAmount=$util.Long?$util.Long.fromBits(0,0,false):0;JackpotDataInfo.prototype.boundaryScore=$util.Long?$util.Long.fromBits(0,0,false):0;JackpotDataInfo.prototype.contrScore=$util.Long?$util.Long.fromBits(0,0,false):0;JackpotDataInfo.prototype.huangTongPer=0;JackpotDataInfo.prototype.siTiaoPer=0;JackpotDataInfo.prototype.tongHuaShunPer=0;JackpotDataInfo.create=function create(properties){return new JackpotDataInfo(properties)};JackpotDataInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.leftAmount!=null&&Object.hasOwnProperty.call(m,"leftAmount"))w.uint32(8).int64(m.leftAmount);if(m.boundaryScore!=null&&Object.hasOwnProperty.call(m,"boundaryScore"))w.uint32(16).int64(m.boundaryScore);if(m.contrScore!=null&&Object.hasOwnProperty.call(m,"contrScore"))w.uint32(24).int64(m.contrScore);if(m.huangTongPer!=null&&Object.hasOwnProperty.call(m,"huangTongPer"))w.uint32(32).uint32(m.huangTongPer);if(m.siTiaoPer!=null&&Object.hasOwnProperty.call(m,"siTiaoPer"))w.uint32(40).uint32(m.siTiaoPer);if(m.tongHuaShunPer!=null&&Object.hasOwnProperty.call(m,"tongHuaShunPer"))w.uint32(48).uint32(m.tongHuaShunPer);return w};JackpotDataInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotDataInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JackpotDataInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.leftAmount=r.int64();break;case 2:m.boundaryScore=r.int64();break;case 3:m.contrScore=r.int64();break;case 4:m.huangTongPer=r.uint32();break;case 5:m.siTiaoPer=r.uint32();break;case 6:m.tongHuaShunPer=r.uint32();break;default:r.skipType(t&7);break}}return m};JackpotDataInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotDataInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.leftAmount!=null&&m.hasOwnProperty("leftAmount")){if(!$util.isInteger(m.leftAmount)&&!(m.leftAmount&&$util.isInteger(m.leftAmount.low)&&$util.isInteger(m.leftAmount.high)))return"leftAmount: integer|Long expected"}if(m.boundaryScore!=null&&m.hasOwnProperty("boundaryScore")){if(!$util.isInteger(m.boundaryScore)&&!(m.boundaryScore&&$util.isInteger(m.boundaryScore.low)&&$util.isInteger(m.boundaryScore.high)))return"boundaryScore: integer|Long expected"}if(m.contrScore!=null&&m.hasOwnProperty("contrScore")){if(!$util.isInteger(m.contrScore)&&!(m.contrScore&&$util.isInteger(m.contrScore.low)&&$util.isInteger(m.contrScore.high)))return"contrScore: integer|Long expected"}if(m.huangTongPer!=null&&m.hasOwnProperty("huangTongPer")){if(!$util.isInteger(m.huangTongPer))return"huangTongPer: integer expected"}if(m.siTiaoPer!=null&&m.hasOwnProperty("siTiaoPer")){if(!$util.isInteger(m.siTiaoPer))return"siTiaoPer: integer expected"}if(m.tongHuaShunPer!=null&&m.hasOwnProperty("tongHuaShunPer")){if(!$util.isInteger(m.tongHuaShunPer))return"tongHuaShunPer: integer expected"}return null};JackpotDataInfo.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JackpotDataInfo)return d;var m=new $root.jackfruit_proto.JackpotDataInfo;if(d.leftAmount!=null){if($util.Long)(m.leftAmount=$util.Long.fromValue(d.leftAmount)).unsigned=false;else if(typeof d.leftAmount==="string")m.leftAmount=parseInt(d.leftAmount,10);else if(typeof d.leftAmount==="number")m.leftAmount=d.leftAmount;else if(typeof d.leftAmount==="object")m.leftAmount=new $util.LongBits(d.leftAmount.low>>>0,d.leftAmount.high>>>0).toNumber()}if(d.boundaryScore!=null){if($util.Long)(m.boundaryScore=$util.Long.fromValue(d.boundaryScore)).unsigned=false;else if(typeof d.boundaryScore==="string")m.boundaryScore=parseInt(d.boundaryScore,10);else if(typeof d.boundaryScore==="number")m.boundaryScore=d.boundaryScore;else if(typeof d.boundaryScore==="object")m.boundaryScore=new $util.LongBits(d.boundaryScore.low>>>0,d.boundaryScore.high>>>0).toNumber()}if(d.contrScore!=null){if($util.Long)(m.contrScore=$util.Long.fromValue(d.contrScore)).unsigned=false;else if(typeof d.contrScore==="string")m.contrScore=parseInt(d.contrScore,10);else if(typeof d.contrScore==="number")m.contrScore=d.contrScore;else if(typeof d.contrScore==="object")m.contrScore=new $util.LongBits(d.contrScore.low>>>0,d.contrScore.high>>>0).toNumber()}if(d.huangTongPer!=null){m.huangTongPer=d.huangTongPer>>>0}if(d.siTiaoPer!=null){m.siTiaoPer=d.siTiaoPer>>>0}if(d.tongHuaShunPer!=null){m.tongHuaShunPer=d.tongHuaShunPer>>>0}return m};JackpotDataInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.leftAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.boundaryScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.boundaryScore=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.contrScore=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.contrScore=o.longs===String?"0":0;d.huangTongPer=0;d.siTiaoPer=0;d.tongHuaShunPer=0}if(m.leftAmount!=null&&m.hasOwnProperty("leftAmount")){if(typeof m.leftAmount==="number")d.leftAmount=o.longs===String?String(m.leftAmount):m.leftAmount;else d.leftAmount=o.longs===String?$util.Long.prototype.toString.call(m.leftAmount):o.longs===Number?new $util.LongBits(m.leftAmount.low>>>0,m.leftAmount.high>>>0).toNumber():m.leftAmount}if(m.boundaryScore!=null&&m.hasOwnProperty("boundaryScore")){if(typeof m.boundaryScore==="number")d.boundaryScore=o.longs===String?String(m.boundaryScore):m.boundaryScore;else d.boundaryScore=o.longs===String?$util.Long.prototype.toString.call(m.boundaryScore):o.longs===Number?new $util.LongBits(m.boundaryScore.low>>>0,m.boundaryScore.high>>>0).toNumber():m.boundaryScore}if(m.contrScore!=null&&m.hasOwnProperty("contrScore")){if(typeof m.contrScore==="number")d.contrScore=o.longs===String?String(m.contrScore):m.contrScore;else d.contrScore=o.longs===String?$util.Long.prototype.toString.call(m.contrScore):o.longs===Number?new $util.LongBits(m.contrScore.low>>>0,m.contrScore.high>>>0).toNumber():m.contrScore}if(m.huangTongPer!=null&&m.hasOwnProperty("huangTongPer")){d.huangTongPer=m.huangTongPer}if(m.siTiaoPer!=null&&m.hasOwnProperty("siTiaoPer")){d.siTiaoPer=m.siTiaoPer}if(m.tongHuaShunPer!=null&&m.hasOwnProperty("tongHuaShunPer")){d.tongHuaShunPer=m.tongHuaShunPer}return d};JackpotDataInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotDataInfo}();jackfruit_proto.JackpotAwardListReq=function(){function JackpotAwardListReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotAwardListReq.create=function create(properties){return new JackpotAwardListReq(properties)};JackpotAwardListReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};JackpotAwardListReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotAwardListReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JackpotAwardListReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};JackpotAwardListReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotAwardListReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};JackpotAwardListReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JackpotAwardListReq)return d;return new $root.jackfruit_proto.JackpotAwardListReq};JackpotAwardListReq.toObject=function toObject(){return{}};JackpotAwardListReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotAwardListReq}();jackfruit_proto.JackpotAwardListResp=function(){function JackpotAwardListResp(p){this.lastData=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotAwardListResp.prototype.code=0;JackpotAwardListResp.prototype.luckyOne=null;JackpotAwardListResp.prototype.lastData=$util.emptyArray;JackpotAwardListResp.create=function create(properties){return new JackpotAwardListResp(properties)};JackpotAwardListResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.luckyOne!=null&&Object.hasOwnProperty.call(m,"luckyOne"))$root.jackfruit_proto.JackpotAwardInfo.encode(m.luckyOne,w.uint32(18).fork()).ldelim();if(m.lastData!=null&&m.lastData.length){for(var i=0;i<m.lastData.length;++i)$root.jackfruit_proto.JackpotAwardInfo.encode(m.lastData[i],w.uint32(26).fork()).ldelim()}return w};JackpotAwardListResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotAwardListResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JackpotAwardListResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.luckyOne=$root.jackfruit_proto.JackpotAwardInfo.decode(r,r.uint32());break;case 3:if(!(m.lastData&&m.lastData.length))m.lastData=[];m.lastData.push($root.jackfruit_proto.JackpotAwardInfo.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};JackpotAwardListResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotAwardListResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.luckyOne!=null&&m.hasOwnProperty("luckyOne")){{var e=$root.jackfruit_proto.JackpotAwardInfo.verify(m.luckyOne);if(e)return"luckyOne."+e}}if(m.lastData!=null&&m.hasOwnProperty("lastData")){if(!Array.isArray(m.lastData))return"lastData: array expected";for(var i=0;i<m.lastData.length;++i){{var e=$root.jackfruit_proto.JackpotAwardInfo.verify(m.lastData[i]);if(e)return"lastData."+e}}}return null};JackpotAwardListResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JackpotAwardListResp)return d;var m=new $root.jackfruit_proto.JackpotAwardListResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.luckyOne!=null){if(typeof d.luckyOne!=="object")throw TypeError(".jackfruit_proto.JackpotAwardListResp.luckyOne: object expected");m.luckyOne=$root.jackfruit_proto.JackpotAwardInfo.fromObject(d.luckyOne)}if(d.lastData){if(!Array.isArray(d.lastData))throw TypeError(".jackfruit_proto.JackpotAwardListResp.lastData: array expected");m.lastData=[];for(var i=0;i<d.lastData.length;++i){if(typeof d.lastData[i]!=="object")throw TypeError(".jackfruit_proto.JackpotAwardListResp.lastData: object expected");m.lastData[i]=$root.jackfruit_proto.JackpotAwardInfo.fromObject(d.lastData[i])}}return m};JackpotAwardListResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.lastData=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.luckyOne=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.luckyOne!=null&&m.hasOwnProperty("luckyOne")){d.luckyOne=$root.jackfruit_proto.JackpotAwardInfo.toObject(m.luckyOne,o)}if(m.lastData&&m.lastData.length){d.lastData=[];for(var j=0;j<m.lastData.length;++j){d.lastData[j]=$root.jackfruit_proto.JackpotAwardInfo.toObject(m.lastData[j],o)}}return d};JackpotAwardListResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotAwardListResp}();jackfruit_proto.JackpotAwardInfo=function(){function JackpotAwardInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotAwardInfo.prototype.playerId=0;JackpotAwardInfo.prototype.level=0;JackpotAwardInfo.prototype.awardAmount=$util.Long?$util.Long.fromBits(0,0,false):0;JackpotAwardInfo.prototype.awardTime=$util.Long?$util.Long.fromBits(0,0,false):0;JackpotAwardInfo.prototype.playerName="";JackpotAwardInfo.prototype.avatar="";JackpotAwardInfo.prototype.game_uuid=$util.Long?$util.Long.fromBits(0,0,true):0;JackpotAwardInfo.prototype.platform=0;JackpotAwardInfo.create=function create(properties){return new JackpotAwardInfo(properties)};JackpotAwardInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerId!=null&&Object.hasOwnProperty.call(m,"playerId"))w.uint32(8).uint32(m.playerId);if(m.level!=null&&Object.hasOwnProperty.call(m,"level"))w.uint32(16).int32(m.level);if(m.awardAmount!=null&&Object.hasOwnProperty.call(m,"awardAmount"))w.uint32(24).int64(m.awardAmount);if(m.awardTime!=null&&Object.hasOwnProperty.call(m,"awardTime"))w.uint32(32).int64(m.awardTime);if(m.playerName!=null&&Object.hasOwnProperty.call(m,"playerName"))w.uint32(42).string(m.playerName);if(m.avatar!=null&&Object.hasOwnProperty.call(m,"avatar"))w.uint32(50).string(m.avatar);if(m.game_uuid!=null&&Object.hasOwnProperty.call(m,"game_uuid"))w.uint32(56).uint64(m.game_uuid);if(m.platform!=null&&Object.hasOwnProperty.call(m,"platform"))w.uint32(64).int32(m.platform);return w};JackpotAwardInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotAwardInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.JackpotAwardInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.playerId=r.uint32();break;case 2:m.level=r.int32();break;case 3:m.awardAmount=r.int64();break;case 4:m.awardTime=r.int64();break;case 5:m.playerName=r.string();break;case 6:m.avatar=r.string();break;case 7:m.game_uuid=r.uint64();break;case 8:m.platform=r.int32();break;default:r.skipType(t&7);break}}return m};JackpotAwardInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotAwardInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerId!=null&&m.hasOwnProperty("playerId")){if(!$util.isInteger(m.playerId))return"playerId: integer expected"}if(m.level!=null&&m.hasOwnProperty("level")){switch(m.level){default:return"level: enum value expected";case 0:case 10:case 9:case 8:case 7:case 6:case 5:case 4:case 3:case 2:case 1:break}}if(m.awardAmount!=null&&m.hasOwnProperty("awardAmount")){if(!$util.isInteger(m.awardAmount)&&!(m.awardAmount&&$util.isInteger(m.awardAmount.low)&&$util.isInteger(m.awardAmount.high)))return"awardAmount: integer|Long expected"}if(m.awardTime!=null&&m.hasOwnProperty("awardTime")){if(!$util.isInteger(m.awardTime)&&!(m.awardTime&&$util.isInteger(m.awardTime.low)&&$util.isInteger(m.awardTime.high)))return"awardTime: integer|Long expected"}if(m.playerName!=null&&m.hasOwnProperty("playerName")){if(!$util.isString(m.playerName))return"playerName: string expected"}if(m.avatar!=null&&m.hasOwnProperty("avatar")){if(!$util.isString(m.avatar))return"avatar: string expected"}if(m.game_uuid!=null&&m.hasOwnProperty("game_uuid")){if(!$util.isInteger(m.game_uuid)&&!(m.game_uuid&&$util.isInteger(m.game_uuid.low)&&$util.isInteger(m.game_uuid.high)))return"game_uuid: integer|Long expected"}if(m.platform!=null&&m.hasOwnProperty("platform")){if(!$util.isInteger(m.platform))return"platform: integer expected"}return null};JackpotAwardInfo.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.JackpotAwardInfo)return d;var m=new $root.jackfruit_proto.JackpotAwardInfo;if(d.playerId!=null){m.playerId=d.playerId>>>0}switch(d.level){case"Dump":case 0:m.level=0;break;case"RoyalFlush":case 10:m.level=10;break;case"StraightFlush":case 9:m.level=9;break;case"FourOfAKind":case 8:m.level=8;break;case"FullHouse":case 7:m.level=7;break;case"Flush":case 6:m.level=6;break;case"StraightI":case 5:m.level=5;break;case"ThreeOfAKind":case 4:m.level=4;break;case"TwoPair":case 3:m.level=3;break;case"OnePair":case 2:m.level=2;break;case"HighCard":case 1:m.level=1;break}if(d.awardAmount!=null){if($util.Long)(m.awardAmount=$util.Long.fromValue(d.awardAmount)).unsigned=false;else if(typeof d.awardAmount==="string")m.awardAmount=parseInt(d.awardAmount,10);else if(typeof d.awardAmount==="number")m.awardAmount=d.awardAmount;else if(typeof d.awardAmount==="object")m.awardAmount=new $util.LongBits(d.awardAmount.low>>>0,d.awardAmount.high>>>0).toNumber()}if(d.awardTime!=null){if($util.Long)(m.awardTime=$util.Long.fromValue(d.awardTime)).unsigned=false;else if(typeof d.awardTime==="string")m.awardTime=parseInt(d.awardTime,10);else if(typeof d.awardTime==="number")m.awardTime=d.awardTime;else if(typeof d.awardTime==="object")m.awardTime=new $util.LongBits(d.awardTime.low>>>0,d.awardTime.high>>>0).toNumber()}if(d.playerName!=null){m.playerName=String(d.playerName)}if(d.avatar!=null){m.avatar=String(d.avatar)}if(d.game_uuid!=null){if($util.Long)(m.game_uuid=$util.Long.fromValue(d.game_uuid)).unsigned=true;else if(typeof d.game_uuid==="string")m.game_uuid=parseInt(d.game_uuid,10);else if(typeof d.game_uuid==="number")m.game_uuid=d.game_uuid;else if(typeof d.game_uuid==="object")m.game_uuid=new $util.LongBits(d.game_uuid.low>>>0,d.game_uuid.high>>>0).toNumber(true)}if(d.platform!=null){m.platform=d.platform|0}return m};JackpotAwardInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.playerId=0;d.level=o.enums===String?"Dump":0;if($util.Long){var n=new $util.Long(0,0,false);d.awardAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.awardAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.awardTime=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.awardTime=o.longs===String?"0":0;d.playerName="";d.avatar="";if($util.Long){var n=new $util.Long(0,0,true);d.game_uuid=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.game_uuid=o.longs===String?"0":0;d.platform=0}if(m.playerId!=null&&m.hasOwnProperty("playerId")){d.playerId=m.playerId}if(m.level!=null&&m.hasOwnProperty("level")){d.level=o.enums===String?$root.jackfruit_proto.CardLevel[m.level]:m.level}if(m.awardAmount!=null&&m.hasOwnProperty("awardAmount")){if(typeof m.awardAmount==="number")d.awardAmount=o.longs===String?String(m.awardAmount):m.awardAmount;else d.awardAmount=o.longs===String?$util.Long.prototype.toString.call(m.awardAmount):o.longs===Number?new $util.LongBits(m.awardAmount.low>>>0,m.awardAmount.high>>>0).toNumber():m.awardAmount}if(m.awardTime!=null&&m.hasOwnProperty("awardTime")){if(typeof m.awardTime==="number")d.awardTime=o.longs===String?String(m.awardTime):m.awardTime;else d.awardTime=o.longs===String?$util.Long.prototype.toString.call(m.awardTime):o.longs===Number?new $util.LongBits(m.awardTime.low>>>0,m.awardTime.high>>>0).toNumber():m.awardTime}if(m.playerName!=null&&m.hasOwnProperty("playerName")){d.playerName=m.playerName}if(m.avatar!=null&&m.hasOwnProperty("avatar")){d.avatar=m.avatar}if(m.game_uuid!=null&&m.hasOwnProperty("game_uuid")){if(typeof m.game_uuid==="number")d.game_uuid=o.longs===String?String(m.game_uuid):m.game_uuid;else d.game_uuid=o.longs===String?$util.Long.prototype.toString.call(m.game_uuid):o.longs===Number?new $util.LongBits(m.game_uuid.low>>>0,m.game_uuid.high>>>0).toNumber(true):m.game_uuid}if(m.platform!=null&&m.hasOwnProperty("platform")){d.platform=m.platform}return d};JackpotAwardInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotAwardInfo}();jackfruit_proto.NotDisturbReq=function(){function NotDisturbReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}NotDisturbReq.prototype.operate=0;NotDisturbReq.prototype.whoId=0;NotDisturbReq.create=function create(properties){return new NotDisturbReq(properties)};NotDisturbReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.operate!=null&&Object.hasOwnProperty.call(m,"operate"))w.uint32(8).uint32(m.operate);if(m.whoId!=null&&Object.hasOwnProperty.call(m,"whoId"))w.uint32(16).uint32(m.whoId);return w};NotDisturbReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};NotDisturbReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.NotDisturbReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.operate=r.uint32();break;case 2:m.whoId=r.uint32();break;default:r.skipType(t&7);break}}return m};NotDisturbReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};NotDisturbReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.operate!=null&&m.hasOwnProperty("operate")){if(!$util.isInteger(m.operate))return"operate: integer expected"}if(m.whoId!=null&&m.hasOwnProperty("whoId")){if(!$util.isInteger(m.whoId))return"whoId: integer expected"}return null};NotDisturbReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.NotDisturbReq)return d;var m=new $root.jackfruit_proto.NotDisturbReq;if(d.operate!=null){m.operate=d.operate>>>0}if(d.whoId!=null){m.whoId=d.whoId>>>0}return m};NotDisturbReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.operate=0;d.whoId=0}if(m.operate!=null&&m.hasOwnProperty("operate")){d.operate=m.operate}if(m.whoId!=null&&m.hasOwnProperty("whoId")){d.whoId=m.whoId}return d};NotDisturbReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return NotDisturbReq}();jackfruit_proto.NotDisturbResp=function(){function NotDisturbResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}NotDisturbResp.prototype.code=0;NotDisturbResp.prototype.operate=0;NotDisturbResp.prototype.whoId=0;NotDisturbResp.create=function create(properties){return new NotDisturbResp(properties)};NotDisturbResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.operate!=null&&Object.hasOwnProperty.call(m,"operate"))w.uint32(16).uint32(m.operate);if(m.whoId!=null&&Object.hasOwnProperty.call(m,"whoId"))w.uint32(24).uint32(m.whoId);return w};NotDisturbResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};NotDisturbResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.NotDisturbResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.operate=r.uint32();break;case 3:m.whoId=r.uint32();break;default:r.skipType(t&7);break}}return m};NotDisturbResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};NotDisturbResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}if(m.operate!=null&&m.hasOwnProperty("operate")){if(!$util.isInteger(m.operate))return"operate: integer expected"}if(m.whoId!=null&&m.hasOwnProperty("whoId")){if(!$util.isInteger(m.whoId))return"whoId: integer expected"}return null};NotDisturbResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.NotDisturbResp)return d;var m=new $root.jackfruit_proto.NotDisturbResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.operate!=null){m.operate=d.operate>>>0}if(d.whoId!=null){m.whoId=d.whoId>>>0}return m};NotDisturbResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.operate=0;d.whoId=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}if(m.operate!=null&&m.hasOwnProperty("operate")){d.operate=m.operate}if(m.whoId!=null&&m.hasOwnProperty("whoId")){d.whoId=m.whoId}return d};NotDisturbResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return NotDisturbResp}();jackfruit_proto.IsEmojiFreeReq=function(){function IsEmojiFreeReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}IsEmojiFreeReq.prototype.type=0;IsEmojiFreeReq.create=function create(properties){return new IsEmojiFreeReq(properties)};IsEmojiFreeReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.type!=null&&Object.hasOwnProperty.call(m,"type"))w.uint32(8).int32(m.type);return w};IsEmojiFreeReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};IsEmojiFreeReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.IsEmojiFreeReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.type=r.int32();break;default:r.skipType(t&7);break}}return m};IsEmojiFreeReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};IsEmojiFreeReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.type!=null&&m.hasOwnProperty("type")){switch(m.type){default:return"type: enum value expected";case 0:case 1:case 2:break}}return null};IsEmojiFreeReq.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.IsEmojiFreeReq)return d;var m=new $root.jackfruit_proto.IsEmojiFreeReq;switch(d.type){case"Attack":case 0:m.type=0;break;case"Welcome":case 1:m.type=1;break;case"InterActiveNormal":case 2:m.type=2;break}return m};IsEmojiFreeReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.type=o.enums===String?"Attack":0}if(m.type!=null&&m.hasOwnProperty("type")){d.type=o.enums===String?$root.jackfruit_proto.EmojiType[m.type]:m.type}return d};IsEmojiFreeReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return IsEmojiFreeReq}();jackfruit_proto.IsEmojiFreeResp=function(){function IsEmojiFreeResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}IsEmojiFreeResp.prototype.code=0;IsEmojiFreeResp.create=function create(properties){return new IsEmojiFreeResp(properties)};IsEmojiFreeResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};IsEmojiFreeResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};IsEmojiFreeResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.IsEmojiFreeResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};IsEmojiFreeResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};IsEmojiFreeResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 100:case 13e3:case 13001:case 13002:case 13003:case 13004:case 13005:case 13006:case 13007:case 13008:case 13013:case 13018:case 13022:case 13023:case 13025:case 13026:case 13027:case 13028:case 13029:case 13030:case 13031:case 13032:case 13033:case 13034:case 13035:case 13036:case 13037:case 13038:case 13039:case 13040:case 13041:case 13042:case 13043:case 13045:case 3:case 1214:case 22:case 1215:case 1260:case 1261:case 1252:case 1253:case 116:case 31117:case 31118:break}}return null};IsEmojiFreeResp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.IsEmojiFreeResp)return d;var m=new $root.jackfruit_proto.IsEmojiFreeResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"FAILED":case 100:m.code=100;break;case"ROOM_WITHOUT_YOU":case 13e3:m.code=13e3;break;case"LOW_VERSION":case 13001:m.code=13001;break;case"INVALID_TOKEN":case 13002:m.code=13002;break;case"SERVER_BUSY":case 13003:m.code=13003;break;case"WITHOUT_LOGIN":case 13004:m.code=13004;break;case"ROOM_NOT_MATCH":case 13005:m.code=13005;break;case"ROOM_NOT_EXIST":case 13006:m.code=13006;break;case"ALREADY_IN_OTHER_GAME":case 13007:m.code=13007;break;case"ROOM_PLAYER_LIMIT":case 13008:m.code=13008;break;case"STOP_SERVICE":case 13013:m.code=13013;break;case"TOO_MANY_PEOPLE":case 13018:m.code=13018;break;case"SEAT_ALREADY_BUSY":case 13022:m.code=13022;break;case"NO_ENOUGH_MONEY":case 13023:m.code=13023;break;case"NOT_YET_COMPLETED_PLACE_CARDS":case 13025:m.code=13025;break;case"ALREADY_SIT_DOWN_THIS_SEAT":case 13026:m.code=13026;break;case"ALREADY_SIT_DOWN_Other_SEAT":case 13027:m.code=13027;break;case"SEAT_ID_NOT_EXIST":case 13028:m.code=13028;break;case"NO_PLACE_CARDS":case 13029:m.code=13029;break;case"BAD_REQ_PARAM":case 13030:m.code=13030;break;case"DISALLOWED_OPERATION":case 13031:m.code=13031;break;case"ALREADY_ADD_STAND_UP_LIST":case 13032:m.code=13032;break;case"CAN_NOT_LEAVE_IN_THE_GAME":case 13033:m.code=13033;break;case"Table_Player_Or_Owner_Can_Chat":case 13034:m.code=13034;break;case"Barrage_Sent_Too_Often":case 13035:m.code=13035;break;case"Action_Delay_Exhausted":case 13036:m.code=13036;break;case"Player_Limit_BuyIn":case 13037:m.code=13037;break;case"ALREADY_ADD_LEAVE_LIST":case 13038:m.code=13038;break;case"NOT_ENOUGH_STAKE":case 13039:m.code=13039;break;case"BUY_IN_AMOUNT_INVALID":case 13040:m.code=13040;break;case"CAN_NOT_CHANGE_TABLE":case 13041:m.code=13041;break;case"NOT_SETTLED_YET":case 13042:m.code=13042;break;case"BUY_IN_SEAT_WAS_SNATCHED":case 13043:m.code=13043;break;case"NO_JACKPOT":case 13045:m.code=13045;break;case"GameServer_Player_Not_Found":case 3:m.code=3;break;case"GameServer_Send_Barrage_Too_Fast":case 1214:m.code=1214;break;case"GameServer_RoomID_Not_Found":case 22:m.code=22;break;case"GameServer_Queue_Barrage_Full":case 1215:m.code=1215;break;case"NeedAuthVerify":case 1260:m.code=1260;break;case"WaitAuthRefreshCD":case 1261:m.code=1261;break;case"AlreadyLiked":case 1252:m.code=1252;break;case"Param_Validate":case 1253:m.code=1253;break;case"IsEmojiFree":case 116:m.code=116;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};IsEmojiFreeResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.jackfruit_proto.ErrorCode[m.code]:m.code}return d};IsEmojiFreeResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return IsEmojiFreeResp}();jackfruit_proto.IsEmojiFreeNotify=function(){function IsEmojiFreeNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}IsEmojiFreeNotify.prototype.type=0;IsEmojiFreeNotify.prototype.is_free=false;IsEmojiFreeNotify.create=function create(properties){return new IsEmojiFreeNotify(properties)};IsEmojiFreeNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.type!=null&&Object.hasOwnProperty.call(m,"type"))w.uint32(8).int32(m.type);if(m.is_free!=null&&Object.hasOwnProperty.call(m,"is_free"))w.uint32(16).bool(m.is_free);return w};IsEmojiFreeNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};IsEmojiFreeNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.IsEmojiFreeNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.type=r.int32();break;case 2:m.is_free=r.bool();break;default:r.skipType(t&7);break}}return m};IsEmojiFreeNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};IsEmojiFreeNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.type!=null&&m.hasOwnProperty("type")){switch(m.type){default:return"type: enum value expected";case 0:case 1:case 2:break}}if(m.is_free!=null&&m.hasOwnProperty("is_free")){if(typeof m.is_free!=="boolean")return"is_free: boolean expected"}return null};IsEmojiFreeNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.IsEmojiFreeNotify)return d;var m=new $root.jackfruit_proto.IsEmojiFreeNotify;switch(d.type){case"Attack":case 0:m.type=0;break;case"Welcome":case 1:m.type=1;break;case"InterActiveNormal":case 2:m.type=2;break}if(d.is_free!=null){m.is_free=Boolean(d.is_free)}return m};IsEmojiFreeNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.type=o.enums===String?"Attack":0;d.is_free=false}if(m.type!=null&&m.hasOwnProperty("type")){d.type=o.enums===String?$root.jackfruit_proto.EmojiType[m.type]:m.type}if(m.is_free!=null&&m.hasOwnProperty("is_free")){d.is_free=m.is_free}return d};IsEmojiFreeNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return IsEmojiFreeNotify}();jackfruit_proto.NoticeIntimacyUp=function(){function NoticeIntimacyUp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}NoticeIntimacyUp.prototype.playerid=0;NoticeIntimacyUp.prototype.nickname="";NoticeIntimacyUp.prototype.intimacy=0;NoticeIntimacyUp.prototype.way=0;NoticeIntimacyUp.create=function create(properties){return new NoticeIntimacyUp(properties)};NoticeIntimacyUp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerid!=null&&Object.hasOwnProperty.call(m,"playerid"))w.uint32(8).uint32(m.playerid);if(m.nickname!=null&&Object.hasOwnProperty.call(m,"nickname"))w.uint32(18).string(m.nickname);if(m.intimacy!=null&&Object.hasOwnProperty.call(m,"intimacy"))w.uint32(24).int32(m.intimacy);if(m.way!=null&&Object.hasOwnProperty.call(m,"way"))w.uint32(32).int32(m.way);return w};NoticeIntimacyUp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};NoticeIntimacyUp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.NoticeIntimacyUp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.playerid=r.uint32();break;case 2:m.nickname=r.string();break;case 3:m.intimacy=r.int32();break;case 4:m.way=r.int32();break;default:r.skipType(t&7);break}}return m};NoticeIntimacyUp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};NoticeIntimacyUp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerid!=null&&m.hasOwnProperty("playerid")){if(!$util.isInteger(m.playerid))return"playerid: integer expected"}if(m.nickname!=null&&m.hasOwnProperty("nickname")){if(!$util.isString(m.nickname))return"nickname: string expected"}if(m.intimacy!=null&&m.hasOwnProperty("intimacy")){if(!$util.isInteger(m.intimacy))return"intimacy: integer expected"}if(m.way!=null&&m.hasOwnProperty("way")){if(!$util.isInteger(m.way))return"way: integer expected"}return null};NoticeIntimacyUp.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.NoticeIntimacyUp)return d;var m=new $root.jackfruit_proto.NoticeIntimacyUp;if(d.playerid!=null){m.playerid=d.playerid>>>0}if(d.nickname!=null){m.nickname=String(d.nickname)}if(d.intimacy!=null){m.intimacy=d.intimacy|0}if(d.way!=null){m.way=d.way|0}return m};NoticeIntimacyUp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.playerid=0;d.nickname="";d.intimacy=0;d.way=0}if(m.playerid!=null&&m.hasOwnProperty("playerid")){d.playerid=m.playerid}if(m.nickname!=null&&m.hasOwnProperty("nickname")){d.nickname=m.nickname}if(m.intimacy!=null&&m.hasOwnProperty("intimacy")){d.intimacy=m.intimacy}if(m.way!=null&&m.hasOwnProperty("way")){d.way=m.way}return d};NoticeIntimacyUp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return NoticeIntimacyUp}();jackfruit_proto.LikeRequest=function(){function LikeRequest(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LikeRequest.prototype.likeUid=0;LikeRequest.prototype.content="";LikeRequest.prototype.ctype=0;LikeRequest.create=function create(properties){return new LikeRequest(properties)};LikeRequest.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.likeUid!=null&&Object.hasOwnProperty.call(m,"likeUid"))w.uint32(8).uint32(m.likeUid);if(m.content!=null&&Object.hasOwnProperty.call(m,"content"))w.uint32(18).string(m.content);if(m.ctype!=null&&Object.hasOwnProperty.call(m,"ctype"))w.uint32(24).int32(m.ctype);return w};LikeRequest.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LikeRequest.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.LikeRequest;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.likeUid=r.uint32();break;case 2:m.content=r.string();break;case 3:m.ctype=r.int32();break;default:r.skipType(t&7);break}}return m};LikeRequest.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LikeRequest.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.likeUid!=null&&m.hasOwnProperty("likeUid")){if(!$util.isInteger(m.likeUid))return"likeUid: integer expected"}if(m.content!=null&&m.hasOwnProperty("content")){if(!$util.isString(m.content))return"content: string expected"}if(m.ctype!=null&&m.hasOwnProperty("ctype")){switch(m.ctype){default:return"ctype: enum value expected";case 0:case 1:case 2:case 3:case 6:break}}return null};LikeRequest.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.LikeRequest)return d;var m=new $root.jackfruit_proto.LikeRequest;if(d.likeUid!=null){m.likeUid=d.likeUid>>>0}if(d.content!=null){m.content=String(d.content)}switch(d.ctype){case"Enum_System":case 0:m.ctype=0;break;case"Enum_Custom":case 1:m.ctype=1;break;case"Enum_CardType":case 2:m.ctype=2;break;case"Enum_Liked":case 3:m.ctype=3;break;case"Enum_Profile_Liked":case 6:m.ctype=6;break}return m};LikeRequest.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.likeUid=0;d.content="";d.ctype=o.enums===String?"Enum_System":0}if(m.likeUid!=null&&m.hasOwnProperty("likeUid")){d.likeUid=m.likeUid}if(m.content!=null&&m.hasOwnProperty("content")){d.content=m.content}if(m.ctype!=null&&m.hasOwnProperty("ctype")){d.ctype=o.enums===String?$root.jackfruit_proto.BarrageType[m.ctype]:m.ctype}return d};LikeRequest.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LikeRequest}();jackfruit_proto.LikeResponse=function(){function LikeResponse(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LikeResponse.prototype.error=0;LikeResponse.prototype.likeUid=0;LikeResponse.prototype.likedCount=0;LikeResponse.create=function create(properties){return new LikeResponse(properties)};LikeResponse.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.error!=null&&Object.hasOwnProperty.call(m,"error"))w.uint32(8).int32(m.error);if(m.likeUid!=null&&Object.hasOwnProperty.call(m,"likeUid"))w.uint32(16).uint32(m.likeUid);if(m.likedCount!=null&&Object.hasOwnProperty.call(m,"likedCount"))w.uint32(24).uint32(m.likedCount);return w};LikeResponse.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LikeResponse.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.LikeResponse;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.error=r.int32();break;case 2:m.likeUid=r.uint32();break;case 3:m.likedCount=r.uint32();break;default:r.skipType(t&7);break}}return m};LikeResponse.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LikeResponse.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.error!=null&&m.hasOwnProperty("error")){if(!$util.isInteger(m.error))return"error: integer expected"}if(m.likeUid!=null&&m.hasOwnProperty("likeUid")){if(!$util.isInteger(m.likeUid))return"likeUid: integer expected"}if(m.likedCount!=null&&m.hasOwnProperty("likedCount")){if(!$util.isInteger(m.likedCount))return"likedCount: integer expected"}return null};LikeResponse.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.LikeResponse)return d;var m=new $root.jackfruit_proto.LikeResponse;if(d.error!=null){m.error=d.error|0}if(d.likeUid!=null){m.likeUid=d.likeUid>>>0}if(d.likedCount!=null){m.likedCount=d.likedCount>>>0}return m};LikeResponse.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.error=0;d.likeUid=0;d.likedCount=0}if(m.error!=null&&m.hasOwnProperty("error")){d.error=m.error}if(m.likeUid!=null&&m.hasOwnProperty("likeUid")){d.likeUid=m.likeUid}if(m.likedCount!=null&&m.hasOwnProperty("likedCount")){d.likedCount=m.likedCount}return d};LikeResponse.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LikeResponse}();jackfruit_proto.LikeNotice=function(){function LikeNotice(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LikeNotice.prototype.playerid=0;LikeNotice.prototype.nickname="";LikeNotice.prototype.notice=null;LikeNotice.create=function create(properties){return new LikeNotice(properties)};LikeNotice.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerid!=null&&Object.hasOwnProperty.call(m,"playerid"))w.uint32(32).uint32(m.playerid);if(m.nickname!=null&&Object.hasOwnProperty.call(m,"nickname"))w.uint32(42).string(m.nickname);if(m.notice!=null&&Object.hasOwnProperty.call(m,"notice"))$root.jackfruit_proto.NoticeSendBarrage.encode(m.notice,w.uint32(50).fork()).ldelim();return w};LikeNotice.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LikeNotice.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.LikeNotice;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 4:m.playerid=r.uint32();break;case 5:m.nickname=r.string();break;case 6:m.notice=$root.jackfruit_proto.NoticeSendBarrage.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};LikeNotice.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LikeNotice.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerid!=null&&m.hasOwnProperty("playerid")){if(!$util.isInteger(m.playerid))return"playerid: integer expected"}if(m.nickname!=null&&m.hasOwnProperty("nickname")){if(!$util.isString(m.nickname))return"nickname: string expected"}if(m.notice!=null&&m.hasOwnProperty("notice")){{var e=$root.jackfruit_proto.NoticeSendBarrage.verify(m.notice);if(e)return"notice."+e}}return null};LikeNotice.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.LikeNotice)return d;var m=new $root.jackfruit_proto.LikeNotice;if(d.playerid!=null){m.playerid=d.playerid>>>0}if(d.nickname!=null){m.nickname=String(d.nickname)}if(d.notice!=null){if(typeof d.notice!=="object")throw TypeError(".jackfruit_proto.LikeNotice.notice: object expected");m.notice=$root.jackfruit_proto.NoticeSendBarrage.fromObject(d.notice)}return m};LikeNotice.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.playerid=0;d.nickname="";d.notice=null}if(m.playerid!=null&&m.hasOwnProperty("playerid")){d.playerid=m.playerid}if(m.nickname!=null&&m.hasOwnProperty("nickname")){d.nickname=m.nickname}if(m.notice!=null&&m.hasOwnProperty("notice")){d.notice=$root.jackfruit_proto.NoticeSendBarrage.toObject(m.notice,o)}return d};LikeNotice.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LikeNotice}();jackfruit_proto.GoodFriendJoinTableNotify=function(){function GoodFriendJoinTableNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GoodFriendJoinTableNotify.prototype.playerid=0;GoodFriendJoinTableNotify.prototype.nickname="";GoodFriendJoinTableNotify.prototype.intimacy=0;GoodFriendJoinTableNotify.prototype.seatid=0;GoodFriendJoinTableNotify.create=function create(properties){return new GoodFriendJoinTableNotify(properties)};GoodFriendJoinTableNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerid!=null&&Object.hasOwnProperty.call(m,"playerid"))w.uint32(8).uint32(m.playerid);if(m.nickname!=null&&Object.hasOwnProperty.call(m,"nickname"))w.uint32(18).string(m.nickname);if(m.intimacy!=null&&Object.hasOwnProperty.call(m,"intimacy"))w.uint32(24).int32(m.intimacy);if(m.seatid!=null&&Object.hasOwnProperty.call(m,"seatid"))w.uint32(32).int32(m.seatid);return w};GoodFriendJoinTableNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GoodFriendJoinTableNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.GoodFriendJoinTableNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.playerid=r.uint32();break;case 2:m.nickname=r.string();break;case 3:m.intimacy=r.int32();break;case 4:m.seatid=r.int32();break;default:r.skipType(t&7);break}}return m};GoodFriendJoinTableNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GoodFriendJoinTableNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerid!=null&&m.hasOwnProperty("playerid")){if(!$util.isInteger(m.playerid))return"playerid: integer expected"}if(m.nickname!=null&&m.hasOwnProperty("nickname")){if(!$util.isString(m.nickname))return"nickname: string expected"}if(m.intimacy!=null&&m.hasOwnProperty("intimacy")){if(!$util.isInteger(m.intimacy))return"intimacy: integer expected"}if(m.seatid!=null&&m.hasOwnProperty("seatid")){if(!$util.isInteger(m.seatid))return"seatid: integer expected"}return null};GoodFriendJoinTableNotify.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.GoodFriendJoinTableNotify)return d;var m=new $root.jackfruit_proto.GoodFriendJoinTableNotify;if(d.playerid!=null){m.playerid=d.playerid>>>0}if(d.nickname!=null){m.nickname=String(d.nickname)}if(d.intimacy!=null){m.intimacy=d.intimacy|0}if(d.seatid!=null){m.seatid=d.seatid|0}return m};GoodFriendJoinTableNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.playerid=0;d.nickname="";d.intimacy=0;d.seatid=0}if(m.playerid!=null&&m.hasOwnProperty("playerid")){d.playerid=m.playerid}if(m.nickname!=null&&m.hasOwnProperty("nickname")){d.nickname=m.nickname}if(m.intimacy!=null&&m.hasOwnProperty("intimacy")){d.intimacy=m.intimacy}if(m.seatid!=null&&m.hasOwnProperty("seatid")){d.seatid=m.seatid}return d};GoodFriendJoinTableNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GoodFriendJoinTableNotify}();jackfruit_proto.MagicEmojiRequest=function(){function MagicEmojiRequest(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}MagicEmojiRequest.prototype.roomid=0;MagicEmojiRequest.prototype.type=0;MagicEmojiRequest.create=function create(properties){return new MagicEmojiRequest(properties)};MagicEmojiRequest.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).int32(m.roomid);if(m.type!=null&&Object.hasOwnProperty.call(m,"type"))w.uint32(16).int32(m.type);return w};MagicEmojiRequest.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};MagicEmojiRequest.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.MagicEmojiRequest;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.int32();break;case 2:m.type=r.int32();break;default:r.skipType(t&7);break}}return m};MagicEmojiRequest.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};MagicEmojiRequest.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.type!=null&&m.hasOwnProperty("type")){if(!$util.isInteger(m.type))return"type: integer expected"}return null};MagicEmojiRequest.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.MagicEmojiRequest)return d;var m=new $root.jackfruit_proto.MagicEmojiRequest;if(d.roomid!=null){m.roomid=d.roomid|0}if(d.type!=null){m.type=d.type|0}return m};MagicEmojiRequest.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomid=0;d.type=0}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.type!=null&&m.hasOwnProperty("type")){d.type=m.type}return d};MagicEmojiRequest.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return MagicEmojiRequest}();jackfruit_proto.MagicEmojiResponse=function(){function MagicEmojiResponse(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}MagicEmojiResponse.prototype.error=0;MagicEmojiResponse.create=function create(properties){return new MagicEmojiResponse(properties)};MagicEmojiResponse.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.error!=null&&Object.hasOwnProperty.call(m,"error"))w.uint32(8).int32(m.error);return w};MagicEmojiResponse.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};MagicEmojiResponse.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.MagicEmojiResponse;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.error=r.int32();break;default:r.skipType(t&7);break}}return m};MagicEmojiResponse.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};MagicEmojiResponse.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.error!=null&&m.hasOwnProperty("error")){if(!$util.isInteger(m.error))return"error: integer expected"}return null};MagicEmojiResponse.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.MagicEmojiResponse)return d;var m=new $root.jackfruit_proto.MagicEmojiResponse;if(d.error!=null){m.error=d.error|0}return m};MagicEmojiResponse.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.error=0}if(m.error!=null&&m.hasOwnProperty("error")){d.error=m.error}return d};MagicEmojiResponse.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return MagicEmojiResponse}();jackfruit_proto.MagicEmojiNotice=function(){function MagicEmojiNotice(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}MagicEmojiNotice.prototype.roomid=0;MagicEmojiNotice.prototype.playerid=0;MagicEmojiNotice.prototype.type=0;MagicEmojiNotice.create=function create(properties){return new MagicEmojiNotice(properties)};MagicEmojiNotice.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).int32(m.roomid);if(m.playerid!=null&&Object.hasOwnProperty.call(m,"playerid"))w.uint32(16).uint32(m.playerid);if(m.type!=null&&Object.hasOwnProperty.call(m,"type"))w.uint32(24).int32(m.type);return w};MagicEmojiNotice.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};MagicEmojiNotice.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.MagicEmojiNotice;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.int32();break;case 2:m.playerid=r.uint32();break;case 3:m.type=r.int32();break;default:r.skipType(t&7);break}}return m};MagicEmojiNotice.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};MagicEmojiNotice.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.playerid!=null&&m.hasOwnProperty("playerid")){if(!$util.isInteger(m.playerid))return"playerid: integer expected"}if(m.type!=null&&m.hasOwnProperty("type")){if(!$util.isInteger(m.type))return"type: integer expected"}return null};MagicEmojiNotice.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.MagicEmojiNotice)return d;var m=new $root.jackfruit_proto.MagicEmojiNotice;if(d.roomid!=null){m.roomid=d.roomid|0}if(d.playerid!=null){m.playerid=d.playerid>>>0}if(d.type!=null){m.type=d.type|0}return m};MagicEmojiNotice.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomid=0;d.playerid=0;d.type=0}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.playerid!=null&&m.hasOwnProperty("playerid")){d.playerid=m.playerid}if(m.type!=null&&m.hasOwnProperty("type")){d.type=m.type}return d};MagicEmojiNotice.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return MagicEmojiNotice}();jackfruit_proto.DynamicConfig=function(){function DynamicConfig(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DynamicConfig.prototype.magicEmojiEnable=false;DynamicConfig.prototype.magicEmojiDuration=$util.Long?$util.Long.fromBits(0,0,false):0;DynamicConfig.create=function create(properties){return new DynamicConfig(properties)};DynamicConfig.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.magicEmojiEnable!=null&&Object.hasOwnProperty.call(m,"magicEmojiEnable"))w.uint32(8).bool(m.magicEmojiEnable);if(m.magicEmojiDuration!=null&&Object.hasOwnProperty.call(m,"magicEmojiDuration"))w.uint32(16).int64(m.magicEmojiDuration);return w};DynamicConfig.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DynamicConfig.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.DynamicConfig;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.magicEmojiEnable=r.bool();break;case 2:m.magicEmojiDuration=r.int64();break;default:r.skipType(t&7);break}}return m};DynamicConfig.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DynamicConfig.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.magicEmojiEnable!=null&&m.hasOwnProperty("magicEmojiEnable")){if(typeof m.magicEmojiEnable!=="boolean")return"magicEmojiEnable: boolean expected"}if(m.magicEmojiDuration!=null&&m.hasOwnProperty("magicEmojiDuration")){if(!$util.isInteger(m.magicEmojiDuration)&&!(m.magicEmojiDuration&&$util.isInteger(m.magicEmojiDuration.low)&&$util.isInteger(m.magicEmojiDuration.high)))return"magicEmojiDuration: integer|Long expected"}return null};DynamicConfig.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.DynamicConfig)return d;var m=new $root.jackfruit_proto.DynamicConfig;if(d.magicEmojiEnable!=null){m.magicEmojiEnable=Boolean(d.magicEmojiEnable)}if(d.magicEmojiDuration!=null){if($util.Long)(m.magicEmojiDuration=$util.Long.fromValue(d.magicEmojiDuration)).unsigned=false;else if(typeof d.magicEmojiDuration==="string")m.magicEmojiDuration=parseInt(d.magicEmojiDuration,10);else if(typeof d.magicEmojiDuration==="number")m.magicEmojiDuration=d.magicEmojiDuration;else if(typeof d.magicEmojiDuration==="object")m.magicEmojiDuration=new $util.LongBits(d.magicEmojiDuration.low>>>0,d.magicEmojiDuration.high>>>0).toNumber()}return m};DynamicConfig.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.magicEmojiEnable=false;if($util.Long){var n=new $util.Long(0,0,false);d.magicEmojiDuration=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.magicEmojiDuration=o.longs===String?"0":0}if(m.magicEmojiEnable!=null&&m.hasOwnProperty("magicEmojiEnable")){d.magicEmojiEnable=m.magicEmojiEnable}if(m.magicEmojiDuration!=null&&m.hasOwnProperty("magicEmojiDuration")){if(typeof m.magicEmojiDuration==="number")d.magicEmojiDuration=o.longs===String?String(m.magicEmojiDuration):m.magicEmojiDuration;else d.magicEmojiDuration=o.longs===String?$util.Long.prototype.toString.call(m.magicEmojiDuration):o.longs===Number?new $util.LongBits(m.magicEmojiDuration.low>>>0,m.magicEmojiDuration.high>>>0).toNumber():m.magicEmojiDuration}return d};DynamicConfig.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DynamicConfig}();jackfruit_proto.RequestBarrageHistories=function(){function RequestBarrageHistories(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RequestBarrageHistories.prototype.roomid=0;RequestBarrageHistories.create=function create(properties){return new RequestBarrageHistories(properties)};RequestBarrageHistories.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).int32(m.roomid);return w};RequestBarrageHistories.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RequestBarrageHistories.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.RequestBarrageHistories;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.int32();break;default:r.skipType(t&7);break}}return m};RequestBarrageHistories.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RequestBarrageHistories.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}return null};RequestBarrageHistories.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.RequestBarrageHistories)return d;var m=new $root.jackfruit_proto.RequestBarrageHistories;if(d.roomid!=null){m.roomid=d.roomid|0}return m};RequestBarrageHistories.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomid=0}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}return d};RequestBarrageHistories.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RequestBarrageHistories}();jackfruit_proto.ResponseBarrageHistories=function(){function ResponseBarrageHistories(p){this.barrages=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ResponseBarrageHistories.prototype.error=0;ResponseBarrageHistories.prototype.barrages=$util.emptyArray;ResponseBarrageHistories.prototype.tableBarrageMessage=null;ResponseBarrageHistories.create=function create(properties){return new ResponseBarrageHistories(properties)};ResponseBarrageHistories.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.error!=null&&Object.hasOwnProperty.call(m,"error"))w.uint32(8).int32(m.error);if(m.barrages!=null&&m.barrages.length){for(var i=0;i<m.barrages.length;++i)$root.jackfruit_proto.Barrage.encode(m.barrages[i],w.uint32(18).fork()).ldelim()}if(m.tableBarrageMessage!=null&&Object.hasOwnProperty.call(m,"tableBarrageMessage"))$root.jackfruit_proto.TableBarrageMessage.encode(m.tableBarrageMessage,w.uint32(26).fork()).ldelim();return w};ResponseBarrageHistories.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ResponseBarrageHistories.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.ResponseBarrageHistories;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.error=r.int32();break;case 2:if(!(m.barrages&&m.barrages.length))m.barrages=[];m.barrages.push($root.jackfruit_proto.Barrage.decode(r,r.uint32()));break;case 3:m.tableBarrageMessage=$root.jackfruit_proto.TableBarrageMessage.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};ResponseBarrageHistories.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ResponseBarrageHistories.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.error!=null&&m.hasOwnProperty("error")){if(!$util.isInteger(m.error))return"error: integer expected"}if(m.barrages!=null&&m.hasOwnProperty("barrages")){if(!Array.isArray(m.barrages))return"barrages: array expected";for(var i=0;i<m.barrages.length;++i){{var e=$root.jackfruit_proto.Barrage.verify(m.barrages[i]);if(e)return"barrages."+e}}}if(m.tableBarrageMessage!=null&&m.hasOwnProperty("tableBarrageMessage")){{var e=$root.jackfruit_proto.TableBarrageMessage.verify(m.tableBarrageMessage);if(e)return"tableBarrageMessage."+e}}return null};ResponseBarrageHistories.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.ResponseBarrageHistories)return d;var m=new $root.jackfruit_proto.ResponseBarrageHistories;if(d.error!=null){m.error=d.error|0}if(d.barrages){if(!Array.isArray(d.barrages))throw TypeError(".jackfruit_proto.ResponseBarrageHistories.barrages: array expected");m.barrages=[];for(var i=0;i<d.barrages.length;++i){if(typeof d.barrages[i]!=="object")throw TypeError(".jackfruit_proto.ResponseBarrageHistories.barrages: object expected");m.barrages[i]=$root.jackfruit_proto.Barrage.fromObject(d.barrages[i])}}if(d.tableBarrageMessage!=null){if(typeof d.tableBarrageMessage!=="object")throw TypeError(".jackfruit_proto.ResponseBarrageHistories.tableBarrageMessage: object expected");m.tableBarrageMessage=$root.jackfruit_proto.TableBarrageMessage.fromObject(d.tableBarrageMessage)}return m};ResponseBarrageHistories.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.barrages=[]}if(o.defaults){d.error=0;d.tableBarrageMessage=null}if(m.error!=null&&m.hasOwnProperty("error")){d.error=m.error}if(m.barrages&&m.barrages.length){d.barrages=[];for(var j=0;j<m.barrages.length;++j){d.barrages[j]=$root.jackfruit_proto.Barrage.toObject(m.barrages[j],o)}}if(m.tableBarrageMessage!=null&&m.hasOwnProperty("tableBarrageMessage")){d.tableBarrageMessage=$root.jackfruit_proto.TableBarrageMessage.toObject(m.tableBarrageMessage,o)}return d};ResponseBarrageHistories.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ResponseBarrageHistories}();jackfruit_proto.TableBarrageMessage=function(){function TableBarrageMessage(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TableBarrageMessage.prototype.send_time=$util.Long?$util.Long.fromBits(0,0,false):0;TableBarrageMessage.prototype.content=null;TableBarrageMessage.create=function create(properties){return new TableBarrageMessage(properties)};TableBarrageMessage.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.send_time!=null&&Object.hasOwnProperty.call(m,"send_time"))w.uint32(8).int64(m.send_time);if(m.content!=null&&Object.hasOwnProperty.call(m,"content"))$root.jackfruit_proto.LocalizedContent.encode(m.content,w.uint32(18).fork()).ldelim();return w};TableBarrageMessage.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TableBarrageMessage.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.TableBarrageMessage;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.send_time=r.int64();break;case 2:m.content=$root.jackfruit_proto.LocalizedContent.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};TableBarrageMessage.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TableBarrageMessage.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.send_time!=null&&m.hasOwnProperty("send_time")){if(!$util.isInteger(m.send_time)&&!(m.send_time&&$util.isInteger(m.send_time.low)&&$util.isInteger(m.send_time.high)))return"send_time: integer|Long expected"}if(m.content!=null&&m.hasOwnProperty("content")){{var e=$root.jackfruit_proto.LocalizedContent.verify(m.content);if(e)return"content."+e}}return null};TableBarrageMessage.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.TableBarrageMessage)return d;var m=new $root.jackfruit_proto.TableBarrageMessage;if(d.send_time!=null){if($util.Long)(m.send_time=$util.Long.fromValue(d.send_time)).unsigned=false;else if(typeof d.send_time==="string")m.send_time=parseInt(d.send_time,10);else if(typeof d.send_time==="number")m.send_time=d.send_time;else if(typeof d.send_time==="object")m.send_time=new $util.LongBits(d.send_time.low>>>0,d.send_time.high>>>0).toNumber()}if(d.content!=null){if(typeof d.content!=="object")throw TypeError(".jackfruit_proto.TableBarrageMessage.content: object expected");m.content=$root.jackfruit_proto.LocalizedContent.fromObject(d.content)}return m};TableBarrageMessage.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.send_time=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.send_time=o.longs===String?"0":0;d.content=null}if(m.send_time!=null&&m.hasOwnProperty("send_time")){if(typeof m.send_time==="number")d.send_time=o.longs===String?String(m.send_time):m.send_time;else d.send_time=o.longs===String?$util.Long.prototype.toString.call(m.send_time):o.longs===Number?new $util.LongBits(m.send_time.low>>>0,m.send_time.high>>>0).toNumber():m.send_time}if(m.content!=null&&m.hasOwnProperty("content")){d.content=$root.jackfruit_proto.LocalizedContent.toObject(m.content,o)}return d};TableBarrageMessage.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TableBarrageMessage}();jackfruit_proto.LocalizedContent=function(){function LocalizedContent(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LocalizedContent.prototype.zh_CN="";LocalizedContent.prototype.en_US="";LocalizedContent.prototype.yn_TH="";LocalizedContent.prototype.th_PH="";LocalizedContent.prototype.ar_SA="";LocalizedContent.prototype.hi_IN="";LocalizedContent.create=function create(properties){return new LocalizedContent(properties)};LocalizedContent.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.zh_CN!=null&&Object.hasOwnProperty.call(m,"zh_CN"))w.uint32(10).string(m.zh_CN);if(m.en_US!=null&&Object.hasOwnProperty.call(m,"en_US"))w.uint32(18).string(m.en_US);if(m.yn_TH!=null&&Object.hasOwnProperty.call(m,"yn_TH"))w.uint32(26).string(m.yn_TH);if(m.th_PH!=null&&Object.hasOwnProperty.call(m,"th_PH"))w.uint32(34).string(m.th_PH);if(m.ar_SA!=null&&Object.hasOwnProperty.call(m,"ar_SA"))w.uint32(42).string(m.ar_SA);if(m.hi_IN!=null&&Object.hasOwnProperty.call(m,"hi_IN"))w.uint32(50).string(m.hi_IN);return w};LocalizedContent.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LocalizedContent.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.LocalizedContent;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.zh_CN=r.string();break;case 2:m.en_US=r.string();break;case 3:m.yn_TH=r.string();break;case 4:m.th_PH=r.string();break;case 5:m.ar_SA=r.string();break;case 6:m.hi_IN=r.string();break;default:r.skipType(t&7);break}}return m};LocalizedContent.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LocalizedContent.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.zh_CN!=null&&m.hasOwnProperty("zh_CN")){if(!$util.isString(m.zh_CN))return"zh_CN: string expected"}if(m.en_US!=null&&m.hasOwnProperty("en_US")){if(!$util.isString(m.en_US))return"en_US: string expected"}if(m.yn_TH!=null&&m.hasOwnProperty("yn_TH")){if(!$util.isString(m.yn_TH))return"yn_TH: string expected"}if(m.th_PH!=null&&m.hasOwnProperty("th_PH")){if(!$util.isString(m.th_PH))return"th_PH: string expected"}if(m.ar_SA!=null&&m.hasOwnProperty("ar_SA")){if(!$util.isString(m.ar_SA))return"ar_SA: string expected"}if(m.hi_IN!=null&&m.hasOwnProperty("hi_IN")){if(!$util.isString(m.hi_IN))return"hi_IN: string expected"}return null};LocalizedContent.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.LocalizedContent)return d;var m=new $root.jackfruit_proto.LocalizedContent;if(d.zh_CN!=null){m.zh_CN=String(d.zh_CN)}if(d.en_US!=null){m.en_US=String(d.en_US)}if(d.yn_TH!=null){m.yn_TH=String(d.yn_TH)}if(d.th_PH!=null){m.th_PH=String(d.th_PH)}if(d.ar_SA!=null){m.ar_SA=String(d.ar_SA)}if(d.hi_IN!=null){m.hi_IN=String(d.hi_IN)}return m};LocalizedContent.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.zh_CN="";d.en_US="";d.yn_TH="";d.th_PH="";d.ar_SA="";d.hi_IN=""}if(m.zh_CN!=null&&m.hasOwnProperty("zh_CN")){d.zh_CN=m.zh_CN}if(m.en_US!=null&&m.hasOwnProperty("en_US")){d.en_US=m.en_US}if(m.yn_TH!=null&&m.hasOwnProperty("yn_TH")){d.yn_TH=m.yn_TH}if(m.th_PH!=null&&m.hasOwnProperty("th_PH")){d.th_PH=m.th_PH}if(m.ar_SA!=null&&m.hasOwnProperty("ar_SA")){d.ar_SA=m.ar_SA}if(m.hi_IN!=null&&m.hasOwnProperty("hi_IN")){d.hi_IN=m.hi_IN}return d};LocalizedContent.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LocalizedContent}();jackfruit_proto.Barrage=function(){function Barrage(p){this.at_uid_list=[];this.at_list=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}Barrage.prototype.roomid=0;Barrage.prototype.playerid=0;Barrage.prototype.nickname="";Barrage.prototype.avatar="";Barrage.prototype.content="";Barrage.prototype.ctype=0;Barrage.prototype.send_time=$util.Long?$util.Long.fromBits(0,0,false):0;Barrage.prototype.at_uid_list=$util.emptyArray;Barrage.prototype.thump_up_status=0;Barrage.prototype.at_list=$util.emptyArray;Barrage.prototype.plat=0;Barrage.create=function create(properties){return new Barrage(properties)};Barrage.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).int32(m.roomid);if(m.playerid!=null&&Object.hasOwnProperty.call(m,"playerid"))w.uint32(16).uint32(m.playerid);if(m.nickname!=null&&Object.hasOwnProperty.call(m,"nickname"))w.uint32(26).string(m.nickname);if(m.avatar!=null&&Object.hasOwnProperty.call(m,"avatar"))w.uint32(34).string(m.avatar);if(m.content!=null&&Object.hasOwnProperty.call(m,"content"))w.uint32(42).string(m.content);if(m.ctype!=null&&Object.hasOwnProperty.call(m,"ctype"))w.uint32(48).int32(m.ctype);if(m.send_time!=null&&Object.hasOwnProperty.call(m,"send_time"))w.uint32(56).int64(m.send_time);if(m.at_uid_list!=null&&m.at_uid_list.length){w.uint32(66).fork();for(var i=0;i<m.at_uid_list.length;++i)w.uint32(m.at_uid_list[i]);w.ldelim()}if(m.thump_up_status!=null&&Object.hasOwnProperty.call(m,"thump_up_status"))w.uint32(72).int32(m.thump_up_status);if(m.at_list!=null&&m.at_list.length){for(var i=0;i<m.at_list.length;++i)w.uint32(82).string(m.at_list[i])}if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(88).uint32(m.plat);return w};Barrage.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};Barrage.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.jackfruit_proto.Barrage;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.int32();break;case 2:m.playerid=r.uint32();break;case 3:m.nickname=r.string();break;case 4:m.avatar=r.string();break;case 5:m.content=r.string();break;case 6:m.ctype=r.int32();break;case 7:m.send_time=r.int64();break;case 8:if(!(m.at_uid_list&&m.at_uid_list.length))m.at_uid_list=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.at_uid_list.push(r.uint32())}else m.at_uid_list.push(r.uint32());break;case 9:m.thump_up_status=r.int32();break;case 10:if(!(m.at_list&&m.at_list.length))m.at_list=[];m.at_list.push(r.string());break;case 11:m.plat=r.uint32();break;default:r.skipType(t&7);break}}return m};Barrage.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};Barrage.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.playerid!=null&&m.hasOwnProperty("playerid")){if(!$util.isInteger(m.playerid))return"playerid: integer expected"}if(m.nickname!=null&&m.hasOwnProperty("nickname")){if(!$util.isString(m.nickname))return"nickname: string expected"}if(m.avatar!=null&&m.hasOwnProperty("avatar")){if(!$util.isString(m.avatar))return"avatar: string expected"}if(m.content!=null&&m.hasOwnProperty("content")){if(!$util.isString(m.content))return"content: string expected"}if(m.ctype!=null&&m.hasOwnProperty("ctype")){switch(m.ctype){default:return"ctype: enum value expected";case 0:case 1:case 2:case 3:case 6:break}}if(m.send_time!=null&&m.hasOwnProperty("send_time")){if(!$util.isInteger(m.send_time)&&!(m.send_time&&$util.isInteger(m.send_time.low)&&$util.isInteger(m.send_time.high)))return"send_time: integer|Long expected"}if(m.at_uid_list!=null&&m.hasOwnProperty("at_uid_list")){if(!Array.isArray(m.at_uid_list))return"at_uid_list: array expected";for(var i=0;i<m.at_uid_list.length;++i){if(!$util.isInteger(m.at_uid_list[i]))return"at_uid_list: integer[] expected"}}if(m.thump_up_status!=null&&m.hasOwnProperty("thump_up_status")){if(!$util.isInteger(m.thump_up_status))return"thump_up_status: integer expected"}if(m.at_list!=null&&m.hasOwnProperty("at_list")){if(!Array.isArray(m.at_list))return"at_list: array expected";for(var i=0;i<m.at_list.length;++i){if(!$util.isString(m.at_list[i]))return"at_list: string[] expected"}}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}return null};Barrage.fromObject=function fromObject(d){if(d instanceof $root.jackfruit_proto.Barrage)return d;var m=new $root.jackfruit_proto.Barrage;if(d.roomid!=null){m.roomid=d.roomid|0}if(d.playerid!=null){m.playerid=d.playerid>>>0}if(d.nickname!=null){m.nickname=String(d.nickname)}if(d.avatar!=null){m.avatar=String(d.avatar)}if(d.content!=null){m.content=String(d.content)}switch(d.ctype){case"Enum_System":case 0:m.ctype=0;break;case"Enum_Custom":case 1:m.ctype=1;break;case"Enum_CardType":case 2:m.ctype=2;break;case"Enum_Liked":case 3:m.ctype=3;break;case"Enum_Profile_Liked":case 6:m.ctype=6;break}if(d.send_time!=null){if($util.Long)(m.send_time=$util.Long.fromValue(d.send_time)).unsigned=false;else if(typeof d.send_time==="string")m.send_time=parseInt(d.send_time,10);else if(typeof d.send_time==="number")m.send_time=d.send_time;else if(typeof d.send_time==="object")m.send_time=new $util.LongBits(d.send_time.low>>>0,d.send_time.high>>>0).toNumber()}if(d.at_uid_list){if(!Array.isArray(d.at_uid_list))throw TypeError(".jackfruit_proto.Barrage.at_uid_list: array expected");m.at_uid_list=[];for(var i=0;i<d.at_uid_list.length;++i){m.at_uid_list[i]=d.at_uid_list[i]>>>0}}if(d.thump_up_status!=null){m.thump_up_status=d.thump_up_status|0}if(d.at_list){if(!Array.isArray(d.at_list))throw TypeError(".jackfruit_proto.Barrage.at_list: array expected");m.at_list=[];for(var i=0;i<d.at_list.length;++i){m.at_list[i]=String(d.at_list[i])}}if(d.plat!=null){m.plat=d.plat>>>0}return m};Barrage.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.at_uid_list=[];d.at_list=[]}if(o.defaults){d.roomid=0;d.playerid=0;d.nickname="";d.avatar="";d.content="";d.ctype=o.enums===String?"Enum_System":0;if($util.Long){var n=new $util.Long(0,0,false);d.send_time=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.send_time=o.longs===String?"0":0;d.thump_up_status=0;d.plat=0}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.playerid!=null&&m.hasOwnProperty("playerid")){d.playerid=m.playerid}if(m.nickname!=null&&m.hasOwnProperty("nickname")){d.nickname=m.nickname}if(m.avatar!=null&&m.hasOwnProperty("avatar")){d.avatar=m.avatar}if(m.content!=null&&m.hasOwnProperty("content")){d.content=m.content}if(m.ctype!=null&&m.hasOwnProperty("ctype")){d.ctype=o.enums===String?$root.jackfruit_proto.BarrageType[m.ctype]:m.ctype}if(m.send_time!=null&&m.hasOwnProperty("send_time")){if(typeof m.send_time==="number")d.send_time=o.longs===String?String(m.send_time):m.send_time;else d.send_time=o.longs===String?$util.Long.prototype.toString.call(m.send_time):o.longs===Number?new $util.LongBits(m.send_time.low>>>0,m.send_time.high>>>0).toNumber():m.send_time}if(m.at_uid_list&&m.at_uid_list.length){d.at_uid_list=[];for(var j=0;j<m.at_uid_list.length;++j){d.at_uid_list[j]=m.at_uid_list[j]}}if(m.thump_up_status!=null&&m.hasOwnProperty("thump_up_status")){d.thump_up_status=m.thump_up_status}if(m.at_list&&m.at_list.length){d.at_list=[];for(var j=0;j<m.at_list.length;++j){d.at_list[j]=m.at_list[j]}}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}return d};Barrage.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return Barrage}();return jackfruit_proto}();module.exports=$root;