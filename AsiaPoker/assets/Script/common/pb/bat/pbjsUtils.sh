echo Generate start...
pbName="cowboy" # proto fileName eg: cowboy
pbNameList=(cowboy jackfruit pokermaster) # proto fileName list
isTransAllOfProto=true # whether to generate js or ts for all proto files
if [ "$isTransAllOfProto" = false ]
then 
  pbjs -t static-module -w commonjs -o --no-beautify --keep-case --es6 ../$pbName.js ../../../../resources/zh_CN/pb/$pbName.proto
  pbts -o ../$pbName.d.ts ../$pbName.js
  uglifyjs ../$pbName.js -o ../$pbName.js
else
  for name in "${pbNameList[@]}"
  do
    pbjs -t static-module -w commonjs -o --no-beautify --keep-case --es6 ../$name.js ../../../../resources/zh_CN/pb/$name.proto
    pbts -o ../$name.d.ts ../$name.js
    uglifyjs ../$name.js -o ../$name.js
  done
fi
echo Generate success!