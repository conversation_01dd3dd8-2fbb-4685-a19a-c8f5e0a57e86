"use strict";var $protobuf=require("protobufjs/minimal");var $Reader=$protobuf.Reader,$Writer=$protobuf.Writer,$util=$protobuf.util;var $root=$protobuf.roots["default"]||($protobuf.roots["default"]={});$root.pokermaster_proto=function(){var pokermaster_proto={};pokermaster_proto.RoomLevel=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="RoomLevel_DUMMY"]=0;values[valuesById[1]="Small"]=1;values[valuesById[2]="Middle"]=2;values[valuesById[3]="Big"]=3;return values}();pokermaster_proto.RoleName=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="RoleName_DUMMY"]=0;values[valuesById[1]="Fisher"]=1;values[valuesById[2]="Shark"]=2;return values}();pokermaster_proto.RoundState=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="RoundState_DUMMY"]=0;values[valuesById[1]="GAME_PENDING"]=1;values[valuesById[2]="NEW_ROUND"]=2;values[valuesById[3]="SHOW_ODDS"]=3;values[valuesById[4]="BET"]=4;values[valuesById[5]="STOP_BET"]=5;values[valuesById[6]="WAIT_NEXT_ROUND"]=6;values[valuesById[7]="READY_GAME"]=7;return values}();pokermaster_proto.BetZoneOption=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="BetZoneOption_DUMMY"]=0;values[valuesById[100]="WIN_BEGIN"]=100;values[valuesById[101]="FISHER_WIN"]=101;values[valuesById[102]="SHARK_WIN"]=102;values[valuesById[103]="EQUAL"]=103;values[valuesById[199]="WIN_END"]=199;values[valuesById[300]="FIVE_BEGIN"]=300;values[valuesById[301]="FIVE_NONE_1DUI"]=301;values[valuesById[302]="FIVE_2DUI"]=302;values[valuesById[303]="FIVE_SAN_SHUN_TONG"]=303;values[valuesById[304]="FIVE_GOURD"]=304;values[valuesById[305]="FIVE_KING_TONG_HUA_SHUN_4"]=305;values[valuesById[399]="FIVE_END"]=399;return values}();pokermaster_proto.GameMode=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="DUMMY"]=0;values[valuesById[1]="NOAML"]=1;values[valuesById[3]="SHORT"]=3;return values}();pokermaster_proto.CMD=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="CMD_DUMMY"]=0;values[valuesById[3e4]="LOGIN_GAME_REQ"]=3e4;values[valuesById[30001]="LOGIN_GAME_RESP"]=30001;values[valuesById[30004]="HEART_BEAT_REQ"]=30004;values[valuesById[30005]="HEART_BEAT_RESP"]=30005;values[valuesById[30007]="JOIN_ROOM_REQ"]=30007;values[valuesById[30008]="JOIN_ROOM_RESP"]=30008;values[valuesById[30009]="GAME_LIST_REQ"]=30009;values[valuesById[30010]="GAME_LIST_RESP"]=30010;values[valuesById[30011]="GAME_DATA_SYN"]=30011;values[valuesById[30012]="DEAL_NOTIFY"]=30012;values[valuesById[30013]="BET_REQ"]=30013;values[valuesById[30014]="BET_RESP"]=30014;values[valuesById[30015]="BET_NOTIFY"]=30015;values[valuesById[30016]="GAME_ROUND_END_NOTIFY"]=30016;values[valuesById[30018]="LEAVE_ROOM_REQ"]=30018;values[valuesById[30019]="LEAVE_ROOM_RESP"]=30019;values[valuesById[30020]="LEAVE_ROOM_NOTIFY"]=30020;values[valuesById[30022]="CONN_CLOSE_REQ"]=30022;values[valuesById[30023]="ROOM_TREND_REQ"]=30023;values[valuesById[30024]="ROOM_TREND_RSP"]=30024;values[valuesById[30025]="ROOM_TREND_NOTICE"]=30025;values[valuesById[30026]="START_BET_NOTIFY"]=30026;values[valuesById[30029]="AUTO_BET_REQ"]=30029;values[valuesById[30030]="AUTO_BET_RESP"]=30030;values[valuesById[30031]="AUTO_BET_NOTIFY"]=30031;values[valuesById[30032]="PLAYER_LIST_REQ"]=30032;values[valuesById[30033]="PLAYER_LIST_RESP"]=30033;values[valuesById[30036]="MERGE_AUTO_BET_NOTIFY"]=30036;values[valuesById[30037]="KICK_NOTIFY"]=30037;values[valuesById[30038]="ROOM_TREND_ROAD_REQ"]=30038;values[valuesById[30039]="ROOM_TREND_ROAD_RSP"]=30039;values[valuesById[30040]="ROOM_TREND_ROAD_NOTICE"]=30040;values[valuesById[30041]="AUTO_OPEN_ROADS_REQ"]=30041;values[valuesById[30042]="AUTO_OPEN_ROADS_RSP"]=30042;values[valuesById[30044]="SET_GAME_OPTION_REQ"]=30044;values[valuesById[30045]="SET_GAME_OPTION_RSP"]=30045;values[valuesById[30047]="START_SETTLEMENT_NOTIFY"]=30047;values[valuesById[30050]="ADVANCE_AUTO_BET_REQ"]=30050;values[valuesById[30051]="ADVANCE_AUTO_BET_RSP"]=30051;values[valuesById[30052]="CANCEL_ADVANCE_AUTO_BET_REQ"]=30052;values[valuesById[30053]="CANCEL_ADVANCE_AUTO_BET_RSP"]=30053;values[valuesById[30054]="ADVANCE_AUTO_BET_SET_REQ"]=30054;values[valuesById[30055]="ADVANCE_AUTO_BET_SET_RSP"]=30055;values[valuesById[30056]="SHOW_ODDS_NOTIFY"]=30056;values[valuesById[30057]="STOP_BET_NOTIFY"]=30057;values[valuesById[30058]="BET_REVIEW_REQ"]=30058;values[valuesById[30059]="BET_REVIEW_RSP"]=30059;values[valuesById[30060]="READY_GAME_NOTIFY"]=30060;values[valuesById[30061]="USER_POINTS_CHANGE_NOTICE"]=30061;values[valuesById[30062]="ADVANCE_AUTO_BET_ADD_REQ"]=30062;values[valuesById[30063]="ADVANCE_AUTO_BET_ADD_RSP"]=30063;values[valuesById[30066]="LEFT_GAME_COIN_NOTIFY"]=30066;return values}();pokermaster_proto.ErrorCode=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="ErrorCode_DUMMY"]=0;values[valuesById[1]="OK"]=1;values[valuesById[51e3]="ROOM_WITHOUT_YOU"]=51e3;values[valuesById[51001]="LOW_VERSION"]=51001;values[valuesById[51002]="INVALID_TOKEN"]=51002;values[valuesById[51003]="SERVER_BUSY"]=51003;values[valuesById[51004]="WITHOUT_LOGIN"]=51004;values[valuesById[51005]="ROOM_NOT_MATCH"]=51005;values[valuesById[51006]="ROOM_NOT_EXIST"]=51006;values[valuesById[51007]="BET_EXCEED_LIMIT"]=51007;values[valuesById[51008]="ROOM_PLAYER_LIMIT"]=51008;values[valuesById[51009]="NO_BET"]=51009;values[valuesById[51010]="BET_AMOUNT_NOT_MATCH"]=51010;values[valuesById[51011]="NO_MONEY"]=51011;values[valuesById[51012]="BET_BAD_PARAM"]=51012;values[valuesById[51013]="STOP_SERVICE"]=51013;values[valuesById[51014]="NOT_BET_WHEN_AUTO_BET"]=51014;values[valuesById[51015]="BET_TOO_SMALL"]=51015;values[valuesById[51016]="BET_COUNT_LIMIT"]=51016;values[valuesById[51017]="AUTO_BET_LIMIT"]=51017;values[valuesById[51018]="TOO_MANY_PEOPLE"]=51018;values[valuesById[51019]="BAD_REQ_PARAM"]=51019;values[valuesById[51020]="NO_SET_ADVANCE_AUTO_BET"]=51020;values[valuesById[51021]="AUTO_BET_COUNT_LIMIT"]=51021;values[valuesById[51022]="AUTO_BET_NO_MONEY"]=51022;values[valuesById[51023]="AUTO_BET_EXCEED_LIMIT"]=51023;values[valuesById[51027]="REACH_LIMIT_BET"]=51027;values[valuesById[51024]="INNER_ERROR"]=51024;values[valuesById[51025]="ROOM_SYSTEM_FORCE_CLOSED"]=51025;values[valuesById[51026]="IN_CALM_DOWN"]=51026;values[valuesById[31117]="C2CPAYMENT_LIST_GET_ERROR"]=31117;values[valuesById[31118]="C2CPAYMENT_NOT_ALLOW"]=31118;return values}();pokermaster_proto.ClientType=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Dummy"]=0;values[valuesById[1]="Normal"]=1;values[valuesById[2]="OverSeas"]=2;values[valuesById[3]="H5"]=3;values[valuesById[4]="H5OverSeas"]=4;values[valuesById[5]="H5Web"]=5;values[valuesById[6]="H5WebOverSeas"]=6;values[valuesById[7]="H5VietnamLasted"]=7;values[valuesById[8]="H5WebVietnamLasted"]=8;values[valuesById[9]="H5CowboyWeb"]=9;values[valuesById[10]="H5Thailand"]=10;values[valuesById[11]="H5WebThailand"]=11;values[valuesById[12]="H5Arab"]=12;values[valuesById[13]="H5Hindi"]=13;values[valuesById[14]="H5Mempoker"]=14;values[valuesById[15]="PC"]=15;values[valuesById[16]="WPTG"]=16;return values}();pokermaster_proto.Kick=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Kick_DUMMY"]=0;values[valuesById[1]="IDLE_LONG_TIME"]=1;values[valuesById[2]="Stop_World"]=2;return values}();pokermaster_proto.AutoBetLevel=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Level_Normal"]=0;values[valuesById[1]="Level_Advance"]=1;return values}();pokermaster_proto.StartSettlementNotify=function(){function StartSettlementNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StartSettlementNotify.create=function create(properties){return new StartSettlementNotify(properties)};StartSettlementNotify.encode=function encode(m,w){if(!w)w=$Writer.create();return w};StartSettlementNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StartSettlementNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.StartSettlementNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};StartSettlementNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StartSettlementNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};StartSettlementNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.StartSettlementNotify)return d;return new $root.pokermaster_proto.StartSettlementNotify};StartSettlementNotify.toObject=function toObject(){return{}};StartSettlementNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StartSettlementNotify}();pokermaster_proto.CardItem=function(){function CardItem(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CardItem.prototype.number=0;CardItem.prototype.suit=0;CardItem.create=function create(properties){return new CardItem(properties)};CardItem.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.number!=null&&Object.hasOwnProperty.call(m,"number"))w.uint32(8).int32(m.number);if(m.suit!=null&&Object.hasOwnProperty.call(m,"suit"))w.uint32(16).int32(m.suit);return w};CardItem.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CardItem.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.CardItem;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.number=r.int32();break;case 2:m.suit=r.int32();break;default:r.skipType(t&7);break}}return m};CardItem.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CardItem.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.number!=null&&m.hasOwnProperty("number")){if(!$util.isInteger(m.number))return"number: integer expected"}if(m.suit!=null&&m.hasOwnProperty("suit")){if(!$util.isInteger(m.suit))return"suit: integer expected"}return null};CardItem.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.CardItem)return d;var m=new $root.pokermaster_proto.CardItem;if(d.number!=null){m.number=d.number|0}if(d.suit!=null){m.suit=d.suit|0}return m};CardItem.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.number=0;d.suit=0}if(m.number!=null&&m.hasOwnProperty("number")){d.number=m.number}if(m.suit!=null&&m.hasOwnProperty("suit")){d.suit=m.suit}return d};CardItem.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CardItem}();pokermaster_proto.HeartBeatReq=function(){function HeartBeatReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}HeartBeatReq.prototype.uid=0;HeartBeatReq.create=function create(properties){return new HeartBeatReq(properties)};HeartBeatReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);return w};HeartBeatReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};HeartBeatReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.HeartBeatReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;default:r.skipType(t&7);break}}return m};HeartBeatReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};HeartBeatReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}return null};HeartBeatReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.HeartBeatReq)return d;var m=new $root.pokermaster_proto.HeartBeatReq;if(d.uid!=null){m.uid=d.uid>>>0}return m};HeartBeatReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}return d};HeartBeatReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return HeartBeatReq}();pokermaster_proto.HeartBeatResp=function(){function HeartBeatResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}HeartBeatResp.prototype.uid=0;HeartBeatResp.prototype.timestamp=$util.Long?$util.Long.fromBits(0,0,false):0;HeartBeatResp.create=function create(properties){return new HeartBeatResp(properties)};HeartBeatResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.timestamp!=null&&Object.hasOwnProperty.call(m,"timestamp"))w.uint32(16).int64(m.timestamp);return w};HeartBeatResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};HeartBeatResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.HeartBeatResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.timestamp=r.int64();break;default:r.skipType(t&7);break}}return m};HeartBeatResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};HeartBeatResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.timestamp!=null&&m.hasOwnProperty("timestamp")){if(!$util.isInteger(m.timestamp)&&!(m.timestamp&&$util.isInteger(m.timestamp.low)&&$util.isInteger(m.timestamp.high)))return"timestamp: integer|Long expected"}return null};HeartBeatResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.HeartBeatResp)return d;var m=new $root.pokermaster_proto.HeartBeatResp;if(d.uid!=null){m.uid=d.uid>>>0}if(d.timestamp!=null){if($util.Long)(m.timestamp=$util.Long.fromValue(d.timestamp)).unsigned=false;else if(typeof d.timestamp==="string")m.timestamp=parseInt(d.timestamp,10);else if(typeof d.timestamp==="number")m.timestamp=d.timestamp;else if(typeof d.timestamp==="object")m.timestamp=new $util.LongBits(d.timestamp.low>>>0,d.timestamp.high>>>0).toNumber()}return m};HeartBeatResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;if($util.Long){var n=new $util.Long(0,0,false);d.timestamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.timestamp=o.longs===String?"0":0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.timestamp!=null&&m.hasOwnProperty("timestamp")){if(typeof m.timestamp==="number")d.timestamp=o.longs===String?String(m.timestamp):m.timestamp;else d.timestamp=o.longs===String?$util.Long.prototype.toString.call(m.timestamp):o.longs===Number?new $util.LongBits(m.timestamp.low>>>0,m.timestamp.high>>>0).toNumber():m.timestamp}return d};HeartBeatResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return HeartBeatResp}();pokermaster_proto.LoginReq=function(){function LoginReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LoginReq.prototype.version="";LoginReq.prototype.token="";LoginReq.prototype.client_type=0;LoginReq.create=function create(properties){return new LoginReq(properties)};LoginReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.version!=null&&Object.hasOwnProperty.call(m,"version"))w.uint32(10).string(m.version);if(m.token!=null&&Object.hasOwnProperty.call(m,"token"))w.uint32(18).string(m.token);if(m.client_type!=null&&Object.hasOwnProperty.call(m,"client_type"))w.uint32(24).int32(m.client_type);return w};LoginReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LoginReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.LoginReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.version=r.string();break;case 2:m.token=r.string();break;case 3:m.client_type=r.int32();break;default:r.skipType(t&7);break}}return m};LoginReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LoginReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.version!=null&&m.hasOwnProperty("version")){if(!$util.isString(m.version))return"version: string expected"}if(m.token!=null&&m.hasOwnProperty("token")){if(!$util.isString(m.token))return"token: string expected"}if(m.client_type!=null&&m.hasOwnProperty("client_type")){switch(m.client_type){default:return"client_type: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:case 16:break}}return null};LoginReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.LoginReq)return d;var m=new $root.pokermaster_proto.LoginReq;if(d.version!=null){m.version=String(d.version)}if(d.token!=null){m.token=String(d.token)}switch(d.client_type){case"Dummy":case 0:m.client_type=0;break;case"Normal":case 1:m.client_type=1;break;case"OverSeas":case 2:m.client_type=2;break;case"H5":case 3:m.client_type=3;break;case"H5OverSeas":case 4:m.client_type=4;break;case"H5Web":case 5:m.client_type=5;break;case"H5WebOverSeas":case 6:m.client_type=6;break;case"H5VietnamLasted":case 7:m.client_type=7;break;case"H5WebVietnamLasted":case 8:m.client_type=8;break;case"H5CowboyWeb":case 9:m.client_type=9;break;case"H5Thailand":case 10:m.client_type=10;break;case"H5WebThailand":case 11:m.client_type=11;break;case"H5Arab":case 12:m.client_type=12;break;case"H5Hindi":case 13:m.client_type=13;break;case"H5Mempoker":case 14:m.client_type=14;break;case"PC":case 15:m.client_type=15;break;case"WPTG":case 16:m.client_type=16;break}return m};LoginReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.version="";d.token="";d.client_type=o.enums===String?"Dummy":0}if(m.version!=null&&m.hasOwnProperty("version")){d.version=m.version}if(m.token!=null&&m.hasOwnProperty("token")){d.token=m.token}if(m.client_type!=null&&m.hasOwnProperty("client_type")){d.client_type=o.enums===String?$root.pokermaster_proto.ClientType[m.client_type]:m.client_type}return d};LoginReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LoginReq}();pokermaster_proto.LoginResp=function(){function LoginResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LoginResp.prototype.code=0;LoginResp.prototype.roomid=0;LoginResp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;LoginResp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;LoginResp.create=function create(properties){return new LoginResp(properties)};LoginResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(16).uint32(m.roomid);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);return w};LoginResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LoginResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.LoginResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.roomid=r.uint32();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;default:r.skipType(t&7);break}}return m};LoginResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LoginResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}return null};LoginResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.LoginResp)return d;var m=new $root.pokermaster_proto.LoginResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.roomid!=null){m.roomid=d.roomid>>>0}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}return m};LoginResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.roomid=0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}return d};LoginResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LoginResp}();pokermaster_proto.JoinRoomReq=function(){function JoinRoomReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JoinRoomReq.prototype.roomid=0;JoinRoomReq.create=function create(properties){return new JoinRoomReq(properties)};JoinRoomReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).uint32(m.roomid);return w};JoinRoomReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JoinRoomReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.JoinRoomReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.uint32();break;default:r.skipType(t&7);break}}return m};JoinRoomReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JoinRoomReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}return null};JoinRoomReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.JoinRoomReq)return d;var m=new $root.pokermaster_proto.JoinRoomReq;if(d.roomid!=null){m.roomid=d.roomid>>>0}return m};JoinRoomReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomid=0}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}return d};JoinRoomReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JoinRoomReq}();pokermaster_proto.JoinRoomResp=function(){function JoinRoomResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JoinRoomResp.prototype.code=0;JoinRoomResp.prototype.roomid=0;JoinRoomResp.prototype.roomuuid=$util.Long?$util.Long.fromBits(0,0,true):0;JoinRoomResp.create=function create(properties){return new JoinRoomResp(properties)};JoinRoomResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(16).uint32(m.roomid);if(m.roomuuid!=null&&Object.hasOwnProperty.call(m,"roomuuid"))w.uint32(24).uint64(m.roomuuid);return w};JoinRoomResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JoinRoomResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.JoinRoomResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.roomid=r.uint32();break;case 3:m.roomuuid=r.uint64();break;default:r.skipType(t&7);break}}return m};JoinRoomResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JoinRoomResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(!$util.isInteger(m.roomuuid)&&!(m.roomuuid&&$util.isInteger(m.roomuuid.low)&&$util.isInteger(m.roomuuid.high)))return"roomuuid: integer|Long expected"}return null};JoinRoomResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.JoinRoomResp)return d;var m=new $root.pokermaster_proto.JoinRoomResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.roomid!=null){m.roomid=d.roomid>>>0}if(d.roomuuid!=null){if($util.Long)(m.roomuuid=$util.Long.fromValue(d.roomuuid)).unsigned=true;else if(typeof d.roomuuid==="string")m.roomuuid=parseInt(d.roomuuid,10);else if(typeof d.roomuuid==="number")m.roomuuid=d.roomuuid;else if(typeof d.roomuuid==="object")m.roomuuid=new $util.LongBits(d.roomuuid.low>>>0,d.roomuuid.high>>>0).toNumber(true)}return m};JoinRoomResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.roomid=0;if($util.Long){var n=new $util.Long(0,0,true);d.roomuuid=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.roomuuid=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(typeof m.roomuuid==="number")d.roomuuid=o.longs===String?String(m.roomuuid):m.roomuuid;else d.roomuuid=o.longs===String?$util.Long.prototype.toString.call(m.roomuuid):o.longs===Number?new $util.LongBits(m.roomuuid.low>>>0,m.roomuuid.high>>>0).toNumber(true):m.roomuuid}return d};JoinRoomResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JoinRoomResp}();pokermaster_proto.RoomParam=function(){function RoomParam(p){this.amountLevel=[];this.pictureCn=[];this.pictureEn=[];this.pictureThai=[];this.totalAmountLevel=[];this.pictureVn=[];this.optionLimit=[];this.ruleByLanguage=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomParam.prototype.roomid=0;RoomParam.prototype.amountLevel=$util.emptyArray;RoomParam.prototype.limitPlayers=0;RoomParam.prototype.deskType=0;RoomParam.prototype.smallBet=0;RoomParam.prototype.pictureCn=$util.emptyArray;RoomParam.prototype.pictureEn=$util.emptyArray;RoomParam.prototype.pictureThai=$util.emptyArray;RoomParam.prototype.totalAmountLevel=$util.emptyArray;RoomParam.prototype.pictureVn=$util.emptyArray;RoomParam.prototype.gameMode=0;RoomParam.prototype.optionLimit=$util.emptyArray;RoomParam.prototype.ruleByLanguage=$util.emptyArray;RoomParam.prototype.langVersion=0;RoomParam.prototype.rulePic="";RoomParam.create=function create(properties){return new RoomParam(properties)};RoomParam.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).uint32(m.roomid);if(m.amountLevel!=null&&m.amountLevel.length){w.uint32(18).fork();for(var i=0;i<m.amountLevel.length;++i)w.uint64(m.amountLevel[i]);w.ldelim()}if(m.limitPlayers!=null&&Object.hasOwnProperty.call(m,"limitPlayers"))w.uint32(24).uint32(m.limitPlayers);if(m.deskType!=null&&Object.hasOwnProperty.call(m,"deskType"))w.uint32(32).uint32(m.deskType);if(m.smallBet!=null&&Object.hasOwnProperty.call(m,"smallBet"))w.uint32(40).uint32(m.smallBet);if(m.pictureCn!=null&&m.pictureCn.length){for(var i=0;i<m.pictureCn.length;++i)w.uint32(50).string(m.pictureCn[i])}if(m.pictureEn!=null&&m.pictureEn.length){for(var i=0;i<m.pictureEn.length;++i)w.uint32(58).string(m.pictureEn[i])}if(m.pictureThai!=null&&m.pictureThai.length){for(var i=0;i<m.pictureThai.length;++i)w.uint32(66).string(m.pictureThai[i])}if(m.totalAmountLevel!=null&&m.totalAmountLevel.length){w.uint32(74).fork();for(var i=0;i<m.totalAmountLevel.length;++i)w.uint64(m.totalAmountLevel[i]);w.ldelim()}if(m.pictureVn!=null&&m.pictureVn.length){for(var i=0;i<m.pictureVn.length;++i)w.uint32(82).string(m.pictureVn[i])}if(m.gameMode!=null&&Object.hasOwnProperty.call(m,"gameMode"))w.uint32(88).int32(m.gameMode);if(m.optionLimit!=null&&m.optionLimit.length){for(var i=0;i<m.optionLimit.length;++i)$root.pokermaster_proto.OddsOptionLimit.encode(m.optionLimit[i],w.uint32(98).fork()).ldelim()}if(m.ruleByLanguage!=null&&m.ruleByLanguage.length){for(var i=0;i<m.ruleByLanguage.length;++i)$root.pokermaster_proto.LanguageItem.encode(m.ruleByLanguage[i],w.uint32(106).fork()).ldelim()}if(m.langVersion!=null&&Object.hasOwnProperty.call(m,"langVersion"))w.uint32(112).int32(m.langVersion);if(m.rulePic!=null&&Object.hasOwnProperty.call(m,"rulePic"))w.uint32(122).string(m.rulePic);return w};RoomParam.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomParam.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.RoomParam;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.uint32();break;case 2:if(!(m.amountLevel&&m.amountLevel.length))m.amountLevel=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.amountLevel.push(r.uint64())}else m.amountLevel.push(r.uint64());break;case 3:m.limitPlayers=r.uint32();break;case 4:m.deskType=r.uint32();break;case 5:m.smallBet=r.uint32();break;case 6:if(!(m.pictureCn&&m.pictureCn.length))m.pictureCn=[];m.pictureCn.push(r.string());break;case 7:if(!(m.pictureEn&&m.pictureEn.length))m.pictureEn=[];m.pictureEn.push(r.string());break;case 8:if(!(m.pictureThai&&m.pictureThai.length))m.pictureThai=[];m.pictureThai.push(r.string());break;case 9:if(!(m.totalAmountLevel&&m.totalAmountLevel.length))m.totalAmountLevel=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.totalAmountLevel.push(r.uint64())}else m.totalAmountLevel.push(r.uint64());break;case 10:if(!(m.pictureVn&&m.pictureVn.length))m.pictureVn=[];m.pictureVn.push(r.string());break;case 11:m.gameMode=r.int32();break;case 12:if(!(m.optionLimit&&m.optionLimit.length))m.optionLimit=[];m.optionLimit.push($root.pokermaster_proto.OddsOptionLimit.decode(r,r.uint32()));break;case 13:if(!(m.ruleByLanguage&&m.ruleByLanguage.length))m.ruleByLanguage=[];m.ruleByLanguage.push($root.pokermaster_proto.LanguageItem.decode(r,r.uint32()));break;case 14:m.langVersion=r.int32();break;case 15:m.rulePic=r.string();break;default:r.skipType(t&7);break}}return m};RoomParam.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomParam.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.amountLevel!=null&&m.hasOwnProperty("amountLevel")){if(!Array.isArray(m.amountLevel))return"amountLevel: array expected";for(var i=0;i<m.amountLevel.length;++i){if(!$util.isInteger(m.amountLevel[i])&&!(m.amountLevel[i]&&$util.isInteger(m.amountLevel[i].low)&&$util.isInteger(m.amountLevel[i].high)))return"amountLevel: integer|Long[] expected"}}if(m.limitPlayers!=null&&m.hasOwnProperty("limitPlayers")){if(!$util.isInteger(m.limitPlayers))return"limitPlayers: integer expected"}if(m.deskType!=null&&m.hasOwnProperty("deskType")){if(!$util.isInteger(m.deskType))return"deskType: integer expected"}if(m.smallBet!=null&&m.hasOwnProperty("smallBet")){if(!$util.isInteger(m.smallBet))return"smallBet: integer expected"}if(m.pictureCn!=null&&m.hasOwnProperty("pictureCn")){if(!Array.isArray(m.pictureCn))return"pictureCn: array expected";for(var i=0;i<m.pictureCn.length;++i){if(!$util.isString(m.pictureCn[i]))return"pictureCn: string[] expected"}}if(m.pictureEn!=null&&m.hasOwnProperty("pictureEn")){if(!Array.isArray(m.pictureEn))return"pictureEn: array expected";for(var i=0;i<m.pictureEn.length;++i){if(!$util.isString(m.pictureEn[i]))return"pictureEn: string[] expected"}}if(m.pictureThai!=null&&m.hasOwnProperty("pictureThai")){if(!Array.isArray(m.pictureThai))return"pictureThai: array expected";for(var i=0;i<m.pictureThai.length;++i){if(!$util.isString(m.pictureThai[i]))return"pictureThai: string[] expected"}}if(m.totalAmountLevel!=null&&m.hasOwnProperty("totalAmountLevel")){if(!Array.isArray(m.totalAmountLevel))return"totalAmountLevel: array expected";for(var i=0;i<m.totalAmountLevel.length;++i){if(!$util.isInteger(m.totalAmountLevel[i])&&!(m.totalAmountLevel[i]&&$util.isInteger(m.totalAmountLevel[i].low)&&$util.isInteger(m.totalAmountLevel[i].high)))return"totalAmountLevel: integer|Long[] expected"}}if(m.pictureVn!=null&&m.hasOwnProperty("pictureVn")){if(!Array.isArray(m.pictureVn))return"pictureVn: array expected";for(var i=0;i<m.pictureVn.length;++i){if(!$util.isString(m.pictureVn[i]))return"pictureVn: string[] expected"}}if(m.gameMode!=null&&m.hasOwnProperty("gameMode")){if(!$util.isInteger(m.gameMode))return"gameMode: integer expected"}if(m.optionLimit!=null&&m.hasOwnProperty("optionLimit")){if(!Array.isArray(m.optionLimit))return"optionLimit: array expected";for(var i=0;i<m.optionLimit.length;++i){{var e=$root.pokermaster_proto.OddsOptionLimit.verify(m.optionLimit[i]);if(e)return"optionLimit."+e}}}if(m.ruleByLanguage!=null&&m.hasOwnProperty("ruleByLanguage")){if(!Array.isArray(m.ruleByLanguage))return"ruleByLanguage: array expected";for(var i=0;i<m.ruleByLanguage.length;++i){{var e=$root.pokermaster_proto.LanguageItem.verify(m.ruleByLanguage[i]);if(e)return"ruleByLanguage."+e}}}if(m.langVersion!=null&&m.hasOwnProperty("langVersion")){if(!$util.isInteger(m.langVersion))return"langVersion: integer expected"}if(m.rulePic!=null&&m.hasOwnProperty("rulePic")){if(!$util.isString(m.rulePic))return"rulePic: string expected"}return null};RoomParam.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.RoomParam)return d;var m=new $root.pokermaster_proto.RoomParam;if(d.roomid!=null){m.roomid=d.roomid>>>0}if(d.amountLevel){if(!Array.isArray(d.amountLevel))throw TypeError(".pokermaster_proto.RoomParam.amountLevel: array expected");m.amountLevel=[];for(var i=0;i<d.amountLevel.length;++i){if($util.Long)(m.amountLevel[i]=$util.Long.fromValue(d.amountLevel[i])).unsigned=true;else if(typeof d.amountLevel[i]==="string")m.amountLevel[i]=parseInt(d.amountLevel[i],10);else if(typeof d.amountLevel[i]==="number")m.amountLevel[i]=d.amountLevel[i];else if(typeof d.amountLevel[i]==="object")m.amountLevel[i]=new $util.LongBits(d.amountLevel[i].low>>>0,d.amountLevel[i].high>>>0).toNumber(true)}}if(d.limitPlayers!=null){m.limitPlayers=d.limitPlayers>>>0}if(d.deskType!=null){m.deskType=d.deskType>>>0}if(d.smallBet!=null){m.smallBet=d.smallBet>>>0}if(d.pictureCn){if(!Array.isArray(d.pictureCn))throw TypeError(".pokermaster_proto.RoomParam.pictureCn: array expected");m.pictureCn=[];for(var i=0;i<d.pictureCn.length;++i){m.pictureCn[i]=String(d.pictureCn[i])}}if(d.pictureEn){if(!Array.isArray(d.pictureEn))throw TypeError(".pokermaster_proto.RoomParam.pictureEn: array expected");m.pictureEn=[];for(var i=0;i<d.pictureEn.length;++i){m.pictureEn[i]=String(d.pictureEn[i])}}if(d.pictureThai){if(!Array.isArray(d.pictureThai))throw TypeError(".pokermaster_proto.RoomParam.pictureThai: array expected");m.pictureThai=[];for(var i=0;i<d.pictureThai.length;++i){m.pictureThai[i]=String(d.pictureThai[i])}}if(d.totalAmountLevel){if(!Array.isArray(d.totalAmountLevel))throw TypeError(".pokermaster_proto.RoomParam.totalAmountLevel: array expected");m.totalAmountLevel=[];for(var i=0;i<d.totalAmountLevel.length;++i){if($util.Long)(m.totalAmountLevel[i]=$util.Long.fromValue(d.totalAmountLevel[i])).unsigned=true;else if(typeof d.totalAmountLevel[i]==="string")m.totalAmountLevel[i]=parseInt(d.totalAmountLevel[i],10);else if(typeof d.totalAmountLevel[i]==="number")m.totalAmountLevel[i]=d.totalAmountLevel[i];else if(typeof d.totalAmountLevel[i]==="object")m.totalAmountLevel[i]=new $util.LongBits(d.totalAmountLevel[i].low>>>0,d.totalAmountLevel[i].high>>>0).toNumber(true)}}if(d.pictureVn){if(!Array.isArray(d.pictureVn))throw TypeError(".pokermaster_proto.RoomParam.pictureVn: array expected");m.pictureVn=[];for(var i=0;i<d.pictureVn.length;++i){m.pictureVn[i]=String(d.pictureVn[i])}}if(d.gameMode!=null){m.gameMode=d.gameMode|0}if(d.optionLimit){if(!Array.isArray(d.optionLimit))throw TypeError(".pokermaster_proto.RoomParam.optionLimit: array expected");m.optionLimit=[];for(var i=0;i<d.optionLimit.length;++i){if(typeof d.optionLimit[i]!=="object")throw TypeError(".pokermaster_proto.RoomParam.optionLimit: object expected");m.optionLimit[i]=$root.pokermaster_proto.OddsOptionLimit.fromObject(d.optionLimit[i])}}if(d.ruleByLanguage){if(!Array.isArray(d.ruleByLanguage))throw TypeError(".pokermaster_proto.RoomParam.ruleByLanguage: array expected");m.ruleByLanguage=[];for(var i=0;i<d.ruleByLanguage.length;++i){if(typeof d.ruleByLanguage[i]!=="object")throw TypeError(".pokermaster_proto.RoomParam.ruleByLanguage: object expected");m.ruleByLanguage[i]=$root.pokermaster_proto.LanguageItem.fromObject(d.ruleByLanguage[i])}}if(d.langVersion!=null){m.langVersion=d.langVersion|0}if(d.rulePic!=null){m.rulePic=String(d.rulePic)}return m};RoomParam.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.amountLevel=[];d.pictureCn=[];d.pictureEn=[];d.pictureThai=[];d.totalAmountLevel=[];d.pictureVn=[];d.optionLimit=[];d.ruleByLanguage=[]}if(o.defaults){d.roomid=0;d.limitPlayers=0;d.deskType=0;d.smallBet=0;d.gameMode=0;d.langVersion=0;d.rulePic=""}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.amountLevel&&m.amountLevel.length){d.amountLevel=[];for(var j=0;j<m.amountLevel.length;++j){if(typeof m.amountLevel[j]==="number")d.amountLevel[j]=o.longs===String?String(m.amountLevel[j]):m.amountLevel[j];else d.amountLevel[j]=o.longs===String?$util.Long.prototype.toString.call(m.amountLevel[j]):o.longs===Number?new $util.LongBits(m.amountLevel[j].low>>>0,m.amountLevel[j].high>>>0).toNumber(true):m.amountLevel[j]}}if(m.limitPlayers!=null&&m.hasOwnProperty("limitPlayers")){d.limitPlayers=m.limitPlayers}if(m.deskType!=null&&m.hasOwnProperty("deskType")){d.deskType=m.deskType}if(m.smallBet!=null&&m.hasOwnProperty("smallBet")){d.smallBet=m.smallBet}if(m.pictureCn&&m.pictureCn.length){d.pictureCn=[];for(var j=0;j<m.pictureCn.length;++j){d.pictureCn[j]=m.pictureCn[j]}}if(m.pictureEn&&m.pictureEn.length){d.pictureEn=[];for(var j=0;j<m.pictureEn.length;++j){d.pictureEn[j]=m.pictureEn[j]}}if(m.pictureThai&&m.pictureThai.length){d.pictureThai=[];for(var j=0;j<m.pictureThai.length;++j){d.pictureThai[j]=m.pictureThai[j]}}if(m.totalAmountLevel&&m.totalAmountLevel.length){d.totalAmountLevel=[];for(var j=0;j<m.totalAmountLevel.length;++j){if(typeof m.totalAmountLevel[j]==="number")d.totalAmountLevel[j]=o.longs===String?String(m.totalAmountLevel[j]):m.totalAmountLevel[j];else d.totalAmountLevel[j]=o.longs===String?$util.Long.prototype.toString.call(m.totalAmountLevel[j]):o.longs===Number?new $util.LongBits(m.totalAmountLevel[j].low>>>0,m.totalAmountLevel[j].high>>>0).toNumber(true):m.totalAmountLevel[j]}}if(m.pictureVn&&m.pictureVn.length){d.pictureVn=[];for(var j=0;j<m.pictureVn.length;++j){d.pictureVn[j]=m.pictureVn[j]}}if(m.gameMode!=null&&m.hasOwnProperty("gameMode")){d.gameMode=m.gameMode}if(m.optionLimit&&m.optionLimit.length){d.optionLimit=[];for(var j=0;j<m.optionLimit.length;++j){d.optionLimit[j]=$root.pokermaster_proto.OddsOptionLimit.toObject(m.optionLimit[j],o)}}if(m.ruleByLanguage&&m.ruleByLanguage.length){d.ruleByLanguage=[];for(var j=0;j<m.ruleByLanguage.length;++j){d.ruleByLanguage[j]=$root.pokermaster_proto.LanguageItem.toObject(m.ruleByLanguage[j],o)}}if(m.langVersion!=null&&m.hasOwnProperty("langVersion")){d.langVersion=m.langVersion}if(m.rulePic!=null&&m.hasOwnProperty("rulePic")){d.rulePic=m.rulePic}return d};RoomParam.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomParam}();pokermaster_proto.LanguageItem=function(){function LanguageItem(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LanguageItem.prototype.lang="";LanguageItem.prototype.value="";LanguageItem.prototype.plat=0;LanguageItem.create=function create(properties){return new LanguageItem(properties)};LanguageItem.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.lang!=null&&Object.hasOwnProperty.call(m,"lang"))w.uint32(10).string(m.lang);if(m.value!=null&&Object.hasOwnProperty.call(m,"value"))w.uint32(18).string(m.value);if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(24).uint32(m.plat);return w};LanguageItem.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LanguageItem.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.LanguageItem;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.lang=r.string();break;case 2:m.value=r.string();break;case 3:m.plat=r.uint32();break;default:r.skipType(t&7);break}}return m};LanguageItem.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LanguageItem.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.lang!=null&&m.hasOwnProperty("lang")){if(!$util.isString(m.lang))return"lang: string expected"}if(m.value!=null&&m.hasOwnProperty("value")){if(!$util.isString(m.value))return"value: string expected"}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}return null};LanguageItem.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.LanguageItem)return d;var m=new $root.pokermaster_proto.LanguageItem;if(d.lang!=null){m.lang=String(d.lang)}if(d.value!=null){m.value=String(d.value)}if(d.plat!=null){m.plat=d.plat>>>0}return m};LanguageItem.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.lang="";d.value="";d.plat=0}if(m.lang!=null&&m.hasOwnProperty("lang")){d.lang=m.lang}if(m.value!=null&&m.hasOwnProperty("value")){d.value=m.value}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}return d};LanguageItem.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LanguageItem}();pokermaster_proto.OddsOptionLimit=function(){function OddsOptionLimit(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OddsOptionLimit.prototype.minOdds=$util.Long?$util.Long.fromBits(0,0,false):0;OddsOptionLimit.prototype.maxOdds=$util.Long?$util.Long.fromBits(0,0,false):0;OddsOptionLimit.prototype.limitRed=$util.Long?$util.Long.fromBits(0,0,false):0;OddsOptionLimit.create=function create(properties){return new OddsOptionLimit(properties)};OddsOptionLimit.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.minOdds!=null&&Object.hasOwnProperty.call(m,"minOdds"))w.uint32(8).int64(m.minOdds);if(m.maxOdds!=null&&Object.hasOwnProperty.call(m,"maxOdds"))w.uint32(16).int64(m.maxOdds);if(m.limitRed!=null&&Object.hasOwnProperty.call(m,"limitRed"))w.uint32(24).int64(m.limitRed);return w};OddsOptionLimit.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OddsOptionLimit.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.OddsOptionLimit;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.minOdds=r.int64();break;case 2:m.maxOdds=r.int64();break;case 3:m.limitRed=r.int64();break;default:r.skipType(t&7);break}}return m};OddsOptionLimit.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OddsOptionLimit.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.minOdds!=null&&m.hasOwnProperty("minOdds")){if(!$util.isInteger(m.minOdds)&&!(m.minOdds&&$util.isInteger(m.minOdds.low)&&$util.isInteger(m.minOdds.high)))return"minOdds: integer|Long expected"}if(m.maxOdds!=null&&m.hasOwnProperty("maxOdds")){if(!$util.isInteger(m.maxOdds)&&!(m.maxOdds&&$util.isInteger(m.maxOdds.low)&&$util.isInteger(m.maxOdds.high)))return"maxOdds: integer|Long expected"}if(m.limitRed!=null&&m.hasOwnProperty("limitRed")){if(!$util.isInteger(m.limitRed)&&!(m.limitRed&&$util.isInteger(m.limitRed.low)&&$util.isInteger(m.limitRed.high)))return"limitRed: integer|Long expected"}return null};OddsOptionLimit.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.OddsOptionLimit)return d;var m=new $root.pokermaster_proto.OddsOptionLimit;if(d.minOdds!=null){if($util.Long)(m.minOdds=$util.Long.fromValue(d.minOdds)).unsigned=false;else if(typeof d.minOdds==="string")m.minOdds=parseInt(d.minOdds,10);else if(typeof d.minOdds==="number")m.minOdds=d.minOdds;else if(typeof d.minOdds==="object")m.minOdds=new $util.LongBits(d.minOdds.low>>>0,d.minOdds.high>>>0).toNumber()}if(d.maxOdds!=null){if($util.Long)(m.maxOdds=$util.Long.fromValue(d.maxOdds)).unsigned=false;else if(typeof d.maxOdds==="string")m.maxOdds=parseInt(d.maxOdds,10);else if(typeof d.maxOdds==="number")m.maxOdds=d.maxOdds;else if(typeof d.maxOdds==="object")m.maxOdds=new $util.LongBits(d.maxOdds.low>>>0,d.maxOdds.high>>>0).toNumber()}if(d.limitRed!=null){if($util.Long)(m.limitRed=$util.Long.fromValue(d.limitRed)).unsigned=false;else if(typeof d.limitRed==="string")m.limitRed=parseInt(d.limitRed,10);else if(typeof d.limitRed==="number")m.limitRed=d.limitRed;else if(typeof d.limitRed==="object")m.limitRed=new $util.LongBits(d.limitRed.low>>>0,d.limitRed.high>>>0).toNumber()}return m};OddsOptionLimit.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.minOdds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.minOdds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.maxOdds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.maxOdds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.limitRed=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.limitRed=o.longs===String?"0":0}if(m.minOdds!=null&&m.hasOwnProperty("minOdds")){if(typeof m.minOdds==="number")d.minOdds=o.longs===String?String(m.minOdds):m.minOdds;else d.minOdds=o.longs===String?$util.Long.prototype.toString.call(m.minOdds):o.longs===Number?new $util.LongBits(m.minOdds.low>>>0,m.minOdds.high>>>0).toNumber():m.minOdds}if(m.maxOdds!=null&&m.hasOwnProperty("maxOdds")){if(typeof m.maxOdds==="number")d.maxOdds=o.longs===String?String(m.maxOdds):m.maxOdds;else d.maxOdds=o.longs===String?$util.Long.prototype.toString.call(m.maxOdds):o.longs===Number?new $util.LongBits(m.maxOdds.low>>>0,m.maxOdds.high>>>0).toNumber():m.maxOdds}if(m.limitRed!=null&&m.hasOwnProperty("limitRed")){if(typeof m.limitRed==="number")d.limitRed=o.longs===String?String(m.limitRed):m.limitRed;else d.limitRed=o.longs===String?$util.Long.prototype.toString.call(m.limitRed):o.longs===Number?new $util.LongBits(m.limitRed.low>>>0,m.limitRed.high>>>0).toNumber():m.limitRed}return d};OddsOptionLimit.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OddsOptionLimit}();pokermaster_proto.ZoneLimit=function(){function ZoneLimit(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ZoneLimit.prototype.limit=$util.Long?$util.Long.fromBits(0,0,true):0;ZoneLimit.create=function create(properties){return new ZoneLimit(properties)};ZoneLimit.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.limit!=null&&Object.hasOwnProperty.call(m,"limit"))w.uint32(8).uint64(m.limit);return w};ZoneLimit.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ZoneLimit.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.ZoneLimit;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.limit=r.uint64();break;default:r.skipType(t&7);break}}return m};ZoneLimit.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ZoneLimit.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.limit!=null&&m.hasOwnProperty("limit")){if(!$util.isInteger(m.limit)&&!(m.limit&&$util.isInteger(m.limit.low)&&$util.isInteger(m.limit.high)))return"limit: integer|Long expected"}return null};ZoneLimit.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.ZoneLimit)return d;var m=new $root.pokermaster_proto.ZoneLimit;if(d.limit!=null){if($util.Long)(m.limit=$util.Long.fromValue(d.limit)).unsigned=true;else if(typeof d.limit==="string")m.limit=parseInt(d.limit,10);else if(typeof d.limit==="number")m.limit=d.limit;else if(typeof d.limit==="object")m.limit=new $util.LongBits(d.limit.low>>>0,d.limit.high>>>0).toNumber(true)}return m};ZoneLimit.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,true);d.limit=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.limit=o.longs===String?"0":0}if(m.limit!=null&&m.hasOwnProperty("limit")){if(typeof m.limit==="number")d.limit=o.longs===String?String(m.limit):m.limit;else d.limit=o.longs===String?$util.Long.prototype.toString.call(m.limit):o.longs===Number?new $util.LongBits(m.limit.low>>>0,m.limit.high>>>0).toNumber(true):m.limit}return d};ZoneLimit.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ZoneLimit}();pokermaster_proto.OddsDetail=function(){function OddsDetail(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OddsDetail.prototype.option=0;OddsDetail.prototype.odds=$util.Long?$util.Long.fromBits(0,0,true):0;OddsDetail.create=function create(properties){return new OddsDetail(properties)};OddsDetail.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.odds!=null&&Object.hasOwnProperty.call(m,"odds"))w.uint32(16).uint64(m.odds);return w};OddsDetail.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OddsDetail.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.OddsDetail;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.odds=r.uint64();break;default:r.skipType(t&7);break}}return m};OddsDetail.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OddsDetail.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.odds!=null&&m.hasOwnProperty("odds")){if(!$util.isInteger(m.odds)&&!(m.odds&&$util.isInteger(m.odds.low)&&$util.isInteger(m.odds.high)))return"odds: integer|Long expected"}return null};OddsDetail.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.OddsDetail)return d;var m=new $root.pokermaster_proto.OddsDetail;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"WIN_BEGIN":case 100:m.option=100;break;case"FISHER_WIN":case 101:m.option=101;break;case"SHARK_WIN":case 102:m.option=102;break;case"EQUAL":case 103:m.option=103;break;case"WIN_END":case 199:m.option=199;break;case"FIVE_BEGIN":case 300:m.option=300;break;case"FIVE_NONE_1DUI":case 301:m.option=301;break;case"FIVE_2DUI":case 302:m.option=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.option=303;break;case"FIVE_GOURD":case 304:m.option=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.option=305;break;case"FIVE_END":case 399:m.option=399;break}if(d.odds!=null){if($util.Long)(m.odds=$util.Long.fromValue(d.odds)).unsigned=true;else if(typeof d.odds==="string")m.odds=parseInt(d.odds,10);else if(typeof d.odds==="number")m.odds=d.odds;else if(typeof d.odds==="object")m.odds=new $util.LongBits(d.odds.low>>>0,d.odds.high>>>0).toNumber(true)}return m};OddsDetail.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.odds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.odds=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.option]:m.option}if(m.odds!=null&&m.hasOwnProperty("odds")){if(typeof m.odds==="number")d.odds=o.longs===String?String(m.odds):m.odds;else d.odds=o.longs===String?$util.Long.prototype.toString.call(m.odds):o.longs===Number?new $util.LongBits(m.odds.low>>>0,m.odds.high>>>0).toNumber(true):m.odds}return d};OddsDetail.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OddsDetail}();pokermaster_proto.GameListReq=function(){function GameListReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameListReq.create=function create(properties){return new GameListReq(properties)};GameListReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};GameListReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameListReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.GameListReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};GameListReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameListReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};GameListReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.GameListReq)return d;return new $root.pokermaster_proto.GameListReq};GameListReq.toObject=function toObject(){return{}};GameListReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameListReq}();pokermaster_proto.GameListResp=function(){function GameListResp(p){this.gameList=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameListResp.prototype.gameList=$util.emptyArray;GameListResp.create=function create(properties){return new GameListResp(properties)};GameListResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.gameList!=null&&m.gameList.length){for(var i=0;i<m.gameList.length;++i)$root.pokermaster_proto.GameSnapShot.encode(m.gameList[i],w.uint32(10).fork()).ldelim()}return w};GameListResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameListResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.GameListResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.gameList&&m.gameList.length))m.gameList=[];m.gameList.push($root.pokermaster_proto.GameSnapShot.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};GameListResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameListResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.gameList!=null&&m.hasOwnProperty("gameList")){if(!Array.isArray(m.gameList))return"gameList: array expected";for(var i=0;i<m.gameList.length;++i){{var e=$root.pokermaster_proto.GameSnapShot.verify(m.gameList[i]);if(e)return"gameList."+e}}}return null};GameListResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.GameListResp)return d;var m=new $root.pokermaster_proto.GameListResp;if(d.gameList){if(!Array.isArray(d.gameList))throw TypeError(".pokermaster_proto.GameListResp.gameList: array expected");m.gameList=[];for(var i=0;i<d.gameList.length;++i){if(typeof d.gameList[i]!=="object")throw TypeError(".pokermaster_proto.GameListResp.gameList: object expected");m.gameList[i]=$root.pokermaster_proto.GameSnapShot.fromObject(d.gameList[i])}}return m};GameListResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.gameList=[]}if(m.gameList&&m.gameList.length){d.gameList=[];for(var j=0;j<m.gameList.length;++j){d.gameList[j]=$root.pokermaster_proto.GameSnapShot.toObject(m.gameList[j],o)}}return d};GameListResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameListResp}();pokermaster_proto.GameSnapShot=function(){function GameSnapShot(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameSnapShot.prototype.param=null;GameSnapShot.prototype.playerNum=0;GameSnapShot.create=function create(properties){return new GameSnapShot(properties)};GameSnapShot.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.param!=null&&Object.hasOwnProperty.call(m,"param"))$root.pokermaster_proto.RoomParam.encode(m.param,w.uint32(10).fork()).ldelim();if(m.playerNum!=null&&Object.hasOwnProperty.call(m,"playerNum"))w.uint32(16).int32(m.playerNum);return w};GameSnapShot.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameSnapShot.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.GameSnapShot;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.param=$root.pokermaster_proto.RoomParam.decode(r,r.uint32());break;case 2:m.playerNum=r.int32();break;default:r.skipType(t&7);break}}return m};GameSnapShot.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameSnapShot.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.param!=null&&m.hasOwnProperty("param")){{var e=$root.pokermaster_proto.RoomParam.verify(m.param);if(e)return"param."+e}}if(m.playerNum!=null&&m.hasOwnProperty("playerNum")){if(!$util.isInteger(m.playerNum))return"playerNum: integer expected"}return null};GameSnapShot.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.GameSnapShot)return d;var m=new $root.pokermaster_proto.GameSnapShot;if(d.param!=null){if(typeof d.param!=="object")throw TypeError(".pokermaster_proto.GameSnapShot.param: object expected");m.param=$root.pokermaster_proto.RoomParam.fromObject(d.param)}if(d.playerNum!=null){m.playerNum=d.playerNum|0}return m};GameSnapShot.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.param=null;d.playerNum=0}if(m.param!=null&&m.hasOwnProperty("param")){d.param=$root.pokermaster_proto.RoomParam.toObject(m.param,o)}if(m.playerNum!=null&&m.hasOwnProperty("playerNum")){d.playerNum=m.playerNum}return d};GameSnapShot.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameSnapShot}();pokermaster_proto.GameDataSynNotify=function(){function GameDataSynNotify(p){this.optionInfo=[];this.lastResult=[];this.players=[];this.publicCards=[];this.optionResults=[];this.betCoinOption=[];this.AutoBetCountList=[];this.fisherHoleCards=[];this.sharkHoleCards=[];this.oddsOp=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameDataSynNotify.prototype.param=null;GameDataSynNotify.prototype.optionInfo=$util.emptyArray;GameDataSynNotify.prototype.lastResult=$util.emptyArray;GameDataSynNotify.prototype.curState=0;GameDataSynNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.players=$util.emptyArray;GameDataSynNotify.prototype.publicCards=$util.emptyArray;GameDataSynNotify.prototype.canAuto=false;GameDataSynNotify.prototype.cachedNotifyMsg=null;GameDataSynNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.openRoads=false;GameDataSynNotify.prototype.optionResults=$util.emptyArray;GameDataSynNotify.prototype.betCoinOption=$util.emptyArray;GameDataSynNotify.prototype.autoLevel=0;GameDataSynNotify.prototype.usedAutoBetCount=0;GameDataSynNotify.prototype.selectAutoBetCount=0;GameDataSynNotify.prototype.AutoBetCountList=$util.emptyArray;GameDataSynNotify.prototype.canAdvanceAuto=false;GameDataSynNotify.prototype.BetButtonLimitAmount=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.fisherHoleCards=$util.emptyArray;GameDataSynNotify.prototype.sharkHoleCards=$util.emptyArray;GameDataSynNotify.prototype.squintMsg=null;GameDataSynNotify.prototype.fortune=null;GameDataSynNotify.prototype.oddsOp=$util.emptyArray;GameDataSynNotify.prototype.whoIsLeader=0;GameDataSynNotify.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.reachLimitBet=false;GameDataSynNotify.create=function create(properties){return new GameDataSynNotify(properties)};GameDataSynNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.param!=null&&Object.hasOwnProperty.call(m,"param"))$root.pokermaster_proto.RoomParam.encode(m.param,w.uint32(10).fork()).ldelim();if(m.optionInfo!=null&&m.optionInfo.length){for(var i=0;i<m.optionInfo.length;++i)$root.pokermaster_proto.BetOptionInfo.encode(m.optionInfo[i],w.uint32(18).fork()).ldelim()}if(m.lastResult!=null&&m.lastResult.length){w.uint32(26).fork();for(var i=0;i<m.lastResult.length;++i)w.int32(m.lastResult[i]);w.ldelim()}if(m.curState!=null&&Object.hasOwnProperty.call(m,"curState"))w.uint32(32).int32(m.curState);if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(40).int64(m.nextRoundEndStamp);if(m.players!=null&&m.players.length){for(var i=0;i<m.players.length;++i)$root.pokermaster_proto.GamePlayer.encode(m.players[i],w.uint32(50).fork()).ldelim()}if(m.publicCards!=null&&m.publicCards.length){for(var i=0;i<m.publicCards.length;++i)$root.pokermaster_proto.CardItem.encode(m.publicCards[i],w.uint32(58).fork()).ldelim()}if(m.canAuto!=null&&Object.hasOwnProperty.call(m,"canAuto"))w.uint32(64).bool(m.canAuto);if(m.cachedNotifyMsg!=null&&Object.hasOwnProperty.call(m,"cachedNotifyMsg"))$root.pokermaster_proto.GameRoundEndNotify.encode(m.cachedNotifyMsg,w.uint32(74).fork()).ldelim();if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(80).int64(m.leftSeconds);if(m.openRoads!=null&&Object.hasOwnProperty.call(m,"openRoads"))w.uint32(88).bool(m.openRoads);if(m.optionResults!=null&&m.optionResults.length){for(var i=0;i<m.optionResults.length;++i)$root.pokermaster_proto.OptionResults.encode(m.optionResults[i],w.uint32(98).fork()).ldelim()}if(m.betCoinOption!=null&&m.betCoinOption.length){w.uint32(106).fork();for(var i=0;i<m.betCoinOption.length;++i)w.uint64(m.betCoinOption[i]);w.ldelim()}if(m.autoLevel!=null&&Object.hasOwnProperty.call(m,"autoLevel"))w.uint32(112).int32(m.autoLevel);if(m.usedAutoBetCount!=null&&Object.hasOwnProperty.call(m,"usedAutoBetCount"))w.uint32(120).int32(m.usedAutoBetCount);if(m.selectAutoBetCount!=null&&Object.hasOwnProperty.call(m,"selectAutoBetCount"))w.uint32(128).int32(m.selectAutoBetCount);if(m.AutoBetCountList!=null&&m.AutoBetCountList.length){w.uint32(138).fork();for(var i=0;i<m.AutoBetCountList.length;++i)w.int32(m.AutoBetCountList[i]);w.ldelim()}if(m.canAdvanceAuto!=null&&Object.hasOwnProperty.call(m,"canAdvanceAuto"))w.uint32(144).bool(m.canAdvanceAuto);if(m.BetButtonLimitAmount!=null&&Object.hasOwnProperty.call(m,"BetButtonLimitAmount"))w.uint32(152).int64(m.BetButtonLimitAmount);if(m.fisherHoleCards!=null&&m.fisherHoleCards.length){for(var i=0;i<m.fisherHoleCards.length;++i)$root.pokermaster_proto.CardItem.encode(m.fisherHoleCards[i],w.uint32(162).fork()).ldelim()}if(m.sharkHoleCards!=null&&m.sharkHoleCards.length){for(var i=0;i<m.sharkHoleCards.length;++i)$root.pokermaster_proto.CardItem.encode(m.sharkHoleCards[i],w.uint32(170).fork()).ldelim()}if(m.squintMsg!=null&&Object.hasOwnProperty.call(m,"squintMsg"))$root.pokermaster_proto.StopBetNotify.encode(m.squintMsg,w.uint32(186).fork()).ldelim();if(m.fortune!=null&&Object.hasOwnProperty.call(m,"fortune"))$root.pokermaster_proto.PlayerFortune.encode(m.fortune,w.uint32(194).fork()).ldelim();if(m.oddsOp!=null&&m.oddsOp.length){for(var i=0;i<m.oddsOp.length;++i)$root.pokermaster_proto.BetOptionsOdds.encode(m.oddsOp[i],w.uint32(202).fork()).ldelim()}if(m.whoIsLeader!=null&&Object.hasOwnProperty.call(m,"whoIsLeader"))w.uint32(208).int32(m.whoIsLeader);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(232).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(240).int64(m.CalmDownDeadLineTimeStamp);if(m.reachLimitBet!=null&&Object.hasOwnProperty.call(m,"reachLimitBet"))w.uint32(248).bool(m.reachLimitBet);return w};GameDataSynNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameDataSynNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.GameDataSynNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.param=$root.pokermaster_proto.RoomParam.decode(r,r.uint32());break;case 2:if(!(m.optionInfo&&m.optionInfo.length))m.optionInfo=[];m.optionInfo.push($root.pokermaster_proto.BetOptionInfo.decode(r,r.uint32()));break;case 3:if(!(m.lastResult&&m.lastResult.length))m.lastResult=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.lastResult.push(r.int32())}else m.lastResult.push(r.int32());break;case 4:m.curState=r.int32();break;case 5:m.nextRoundEndStamp=r.int64();break;case 6:if(!(m.players&&m.players.length))m.players=[];m.players.push($root.pokermaster_proto.GamePlayer.decode(r,r.uint32()));break;case 7:if(!(m.publicCards&&m.publicCards.length))m.publicCards=[];m.publicCards.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;case 8:m.canAuto=r.bool();break;case 9:m.cachedNotifyMsg=$root.pokermaster_proto.GameRoundEndNotify.decode(r,r.uint32());break;case 10:m.leftSeconds=r.int64();break;case 11:m.openRoads=r.bool();break;case 12:if(!(m.optionResults&&m.optionResults.length))m.optionResults=[];m.optionResults.push($root.pokermaster_proto.OptionResults.decode(r,r.uint32()));break;case 13:if(!(m.betCoinOption&&m.betCoinOption.length))m.betCoinOption=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.betCoinOption.push(r.uint64())}else m.betCoinOption.push(r.uint64());break;case 14:m.autoLevel=r.int32();break;case 15:m.usedAutoBetCount=r.int32();break;case 16:m.selectAutoBetCount=r.int32();break;case 17:if(!(m.AutoBetCountList&&m.AutoBetCountList.length))m.AutoBetCountList=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.AutoBetCountList.push(r.int32())}else m.AutoBetCountList.push(r.int32());break;case 18:m.canAdvanceAuto=r.bool();break;case 19:m.BetButtonLimitAmount=r.int64();break;case 20:if(!(m.fisherHoleCards&&m.fisherHoleCards.length))m.fisherHoleCards=[];m.fisherHoleCards.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;case 21:if(!(m.sharkHoleCards&&m.sharkHoleCards.length))m.sharkHoleCards=[];m.sharkHoleCards.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;case 23:m.squintMsg=$root.pokermaster_proto.StopBetNotify.decode(r,r.uint32());break;case 24:m.fortune=$root.pokermaster_proto.PlayerFortune.decode(r,r.uint32());break;case 25:if(!(m.oddsOp&&m.oddsOp.length))m.oddsOp=[];m.oddsOp.push($root.pokermaster_proto.BetOptionsOdds.decode(r,r.uint32()));break;case 26:m.whoIsLeader=r.int32();break;case 29:m.CalmDownLeftSeconds=r.int64();break;case 30:m.CalmDownDeadLineTimeStamp=r.int64();break;case 31:m.reachLimitBet=r.bool();break;default:r.skipType(t&7);break}}return m};GameDataSynNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameDataSynNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.param!=null&&m.hasOwnProperty("param")){{var e=$root.pokermaster_proto.RoomParam.verify(m.param);if(e)return"param."+e}}if(m.optionInfo!=null&&m.hasOwnProperty("optionInfo")){if(!Array.isArray(m.optionInfo))return"optionInfo: array expected";for(var i=0;i<m.optionInfo.length;++i){{var e=$root.pokermaster_proto.BetOptionInfo.verify(m.optionInfo[i]);if(e)return"optionInfo."+e}}}if(m.lastResult!=null&&m.hasOwnProperty("lastResult")){if(!Array.isArray(m.lastResult))return"lastResult: array expected";for(var i=0;i<m.lastResult.length;++i){switch(m.lastResult[i]){default:return"lastResult: enum value[] expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}}if(m.curState!=null&&m.hasOwnProperty("curState")){switch(m.curState){default:return"curState: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:break}}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.players!=null&&m.hasOwnProperty("players")){if(!Array.isArray(m.players))return"players: array expected";for(var i=0;i<m.players.length;++i){{var e=$root.pokermaster_proto.GamePlayer.verify(m.players[i]);if(e)return"players."+e}}}if(m.publicCards!=null&&m.hasOwnProperty("publicCards")){if(!Array.isArray(m.publicCards))return"publicCards: array expected";for(var i=0;i<m.publicCards.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.publicCards[i]);if(e)return"publicCards."+e}}}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){if(typeof m.canAuto!=="boolean")return"canAuto: boolean expected"}if(m.cachedNotifyMsg!=null&&m.hasOwnProperty("cachedNotifyMsg")){{var e=$root.pokermaster_proto.GameRoundEndNotify.verify(m.cachedNotifyMsg);if(e)return"cachedNotifyMsg."+e}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.openRoads!=null&&m.hasOwnProperty("openRoads")){if(typeof m.openRoads!=="boolean")return"openRoads: boolean expected"}if(m.optionResults!=null&&m.hasOwnProperty("optionResults")){if(!Array.isArray(m.optionResults))return"optionResults: array expected";for(var i=0;i<m.optionResults.length;++i){{var e=$root.pokermaster_proto.OptionResults.verify(m.optionResults[i]);if(e)return"optionResults."+e}}}if(m.betCoinOption!=null&&m.hasOwnProperty("betCoinOption")){if(!Array.isArray(m.betCoinOption))return"betCoinOption: array expected";for(var i=0;i<m.betCoinOption.length;++i){if(!$util.isInteger(m.betCoinOption[i])&&!(m.betCoinOption[i]&&$util.isInteger(m.betCoinOption[i].low)&&$util.isInteger(m.betCoinOption[i].high)))return"betCoinOption: integer|Long[] expected"}}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){switch(m.autoLevel){default:return"autoLevel: enum value expected";case 0:case 1:break}}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){if(!$util.isInteger(m.usedAutoBetCount))return"usedAutoBetCount: integer expected"}if(m.selectAutoBetCount!=null&&m.hasOwnProperty("selectAutoBetCount")){if(!$util.isInteger(m.selectAutoBetCount))return"selectAutoBetCount: integer expected"}if(m.AutoBetCountList!=null&&m.hasOwnProperty("AutoBetCountList")){if(!Array.isArray(m.AutoBetCountList))return"AutoBetCountList: array expected";for(var i=0;i<m.AutoBetCountList.length;++i){if(!$util.isInteger(m.AutoBetCountList[i]))return"AutoBetCountList: integer[] expected"}}if(m.canAdvanceAuto!=null&&m.hasOwnProperty("canAdvanceAuto")){if(typeof m.canAdvanceAuto!=="boolean")return"canAdvanceAuto: boolean expected"}if(m.BetButtonLimitAmount!=null&&m.hasOwnProperty("BetButtonLimitAmount")){if(!$util.isInteger(m.BetButtonLimitAmount)&&!(m.BetButtonLimitAmount&&$util.isInteger(m.BetButtonLimitAmount.low)&&$util.isInteger(m.BetButtonLimitAmount.high)))return"BetButtonLimitAmount: integer|Long expected"}if(m.fisherHoleCards!=null&&m.hasOwnProperty("fisherHoleCards")){if(!Array.isArray(m.fisherHoleCards))return"fisherHoleCards: array expected";for(var i=0;i<m.fisherHoleCards.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.fisherHoleCards[i]);if(e)return"fisherHoleCards."+e}}}if(m.sharkHoleCards!=null&&m.hasOwnProperty("sharkHoleCards")){if(!Array.isArray(m.sharkHoleCards))return"sharkHoleCards: array expected";for(var i=0;i<m.sharkHoleCards.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.sharkHoleCards[i]);if(e)return"sharkHoleCards."+e}}}if(m.squintMsg!=null&&m.hasOwnProperty("squintMsg")){{var e=$root.pokermaster_proto.StopBetNotify.verify(m.squintMsg);if(e)return"squintMsg."+e}}if(m.fortune!=null&&m.hasOwnProperty("fortune")){{var e=$root.pokermaster_proto.PlayerFortune.verify(m.fortune);if(e)return"fortune."+e}}if(m.oddsOp!=null&&m.hasOwnProperty("oddsOp")){if(!Array.isArray(m.oddsOp))return"oddsOp: array expected";for(var i=0;i<m.oddsOp.length;++i){{var e=$root.pokermaster_proto.BetOptionsOdds.verify(m.oddsOp[i]);if(e)return"oddsOp."+e}}}if(m.whoIsLeader!=null&&m.hasOwnProperty("whoIsLeader")){if(!$util.isInteger(m.whoIsLeader))return"whoIsLeader: integer expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}if(m.reachLimitBet!=null&&m.hasOwnProperty("reachLimitBet")){if(typeof m.reachLimitBet!=="boolean")return"reachLimitBet: boolean expected"}return null};GameDataSynNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.GameDataSynNotify)return d;var m=new $root.pokermaster_proto.GameDataSynNotify;if(d.param!=null){if(typeof d.param!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.param: object expected");m.param=$root.pokermaster_proto.RoomParam.fromObject(d.param)}if(d.optionInfo){if(!Array.isArray(d.optionInfo))throw TypeError(".pokermaster_proto.GameDataSynNotify.optionInfo: array expected");m.optionInfo=[];for(var i=0;i<d.optionInfo.length;++i){if(typeof d.optionInfo[i]!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.optionInfo: object expected");m.optionInfo[i]=$root.pokermaster_proto.BetOptionInfo.fromObject(d.optionInfo[i])}}if(d.lastResult){if(!Array.isArray(d.lastResult))throw TypeError(".pokermaster_proto.GameDataSynNotify.lastResult: array expected");m.lastResult=[];for(var i=0;i<d.lastResult.length;++i){switch(d.lastResult[i]){default:case"BetZoneOption_DUMMY":case 0:m.lastResult[i]=0;break;case"WIN_BEGIN":case 100:m.lastResult[i]=100;break;case"FISHER_WIN":case 101:m.lastResult[i]=101;break;case"SHARK_WIN":case 102:m.lastResult[i]=102;break;case"EQUAL":case 103:m.lastResult[i]=103;break;case"WIN_END":case 199:m.lastResult[i]=199;break;case"FIVE_BEGIN":case 300:m.lastResult[i]=300;break;case"FIVE_NONE_1DUI":case 301:m.lastResult[i]=301;break;case"FIVE_2DUI":case 302:m.lastResult[i]=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.lastResult[i]=303;break;case"FIVE_GOURD":case 304:m.lastResult[i]=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.lastResult[i]=305;break;case"FIVE_END":case 399:m.lastResult[i]=399;break}}}switch(d.curState){case"RoundState_DUMMY":case 0:m.curState=0;break;case"GAME_PENDING":case 1:m.curState=1;break;case"NEW_ROUND":case 2:m.curState=2;break;case"SHOW_ODDS":case 3:m.curState=3;break;case"BET":case 4:m.curState=4;break;case"STOP_BET":case 5:m.curState=5;break;case"WAIT_NEXT_ROUND":case 6:m.curState=6;break;case"READY_GAME":case 7:m.curState=7;break}if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.players){if(!Array.isArray(d.players))throw TypeError(".pokermaster_proto.GameDataSynNotify.players: array expected");m.players=[];for(var i=0;i<d.players.length;++i){if(typeof d.players[i]!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.players: object expected");m.players[i]=$root.pokermaster_proto.GamePlayer.fromObject(d.players[i])}}if(d.publicCards){if(!Array.isArray(d.publicCards))throw TypeError(".pokermaster_proto.GameDataSynNotify.publicCards: array expected");m.publicCards=[];for(var i=0;i<d.publicCards.length;++i){if(typeof d.publicCards[i]!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.publicCards: object expected");m.publicCards[i]=$root.pokermaster_proto.CardItem.fromObject(d.publicCards[i])}}if(d.canAuto!=null){m.canAuto=Boolean(d.canAuto)}if(d.cachedNotifyMsg!=null){if(typeof d.cachedNotifyMsg!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.cachedNotifyMsg: object expected");m.cachedNotifyMsg=$root.pokermaster_proto.GameRoundEndNotify.fromObject(d.cachedNotifyMsg)}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.openRoads!=null){m.openRoads=Boolean(d.openRoads)}if(d.optionResults){if(!Array.isArray(d.optionResults))throw TypeError(".pokermaster_proto.GameDataSynNotify.optionResults: array expected");m.optionResults=[];for(var i=0;i<d.optionResults.length;++i){if(typeof d.optionResults[i]!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.optionResults: object expected");m.optionResults[i]=$root.pokermaster_proto.OptionResults.fromObject(d.optionResults[i])}}if(d.betCoinOption){if(!Array.isArray(d.betCoinOption))throw TypeError(".pokermaster_proto.GameDataSynNotify.betCoinOption: array expected");m.betCoinOption=[];for(var i=0;i<d.betCoinOption.length;++i){if($util.Long)(m.betCoinOption[i]=$util.Long.fromValue(d.betCoinOption[i])).unsigned=true;else if(typeof d.betCoinOption[i]==="string")m.betCoinOption[i]=parseInt(d.betCoinOption[i],10);else if(typeof d.betCoinOption[i]==="number")m.betCoinOption[i]=d.betCoinOption[i];else if(typeof d.betCoinOption[i]==="object")m.betCoinOption[i]=new $util.LongBits(d.betCoinOption[i].low>>>0,d.betCoinOption[i].high>>>0).toNumber(true)}}switch(d.autoLevel){case"Level_Normal":case 0:m.autoLevel=0;break;case"Level_Advance":case 1:m.autoLevel=1;break}if(d.usedAutoBetCount!=null){m.usedAutoBetCount=d.usedAutoBetCount|0}if(d.selectAutoBetCount!=null){m.selectAutoBetCount=d.selectAutoBetCount|0}if(d.AutoBetCountList){if(!Array.isArray(d.AutoBetCountList))throw TypeError(".pokermaster_proto.GameDataSynNotify.AutoBetCountList: array expected");m.AutoBetCountList=[];for(var i=0;i<d.AutoBetCountList.length;++i){m.AutoBetCountList[i]=d.AutoBetCountList[i]|0}}if(d.canAdvanceAuto!=null){m.canAdvanceAuto=Boolean(d.canAdvanceAuto)}if(d.BetButtonLimitAmount!=null){if($util.Long)(m.BetButtonLimitAmount=$util.Long.fromValue(d.BetButtonLimitAmount)).unsigned=false;else if(typeof d.BetButtonLimitAmount==="string")m.BetButtonLimitAmount=parseInt(d.BetButtonLimitAmount,10);else if(typeof d.BetButtonLimitAmount==="number")m.BetButtonLimitAmount=d.BetButtonLimitAmount;else if(typeof d.BetButtonLimitAmount==="object")m.BetButtonLimitAmount=new $util.LongBits(d.BetButtonLimitAmount.low>>>0,d.BetButtonLimitAmount.high>>>0).toNumber()}if(d.fisherHoleCards){if(!Array.isArray(d.fisherHoleCards))throw TypeError(".pokermaster_proto.GameDataSynNotify.fisherHoleCards: array expected");m.fisherHoleCards=[];for(var i=0;i<d.fisherHoleCards.length;++i){if(typeof d.fisherHoleCards[i]!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.fisherHoleCards: object expected");m.fisherHoleCards[i]=$root.pokermaster_proto.CardItem.fromObject(d.fisherHoleCards[i])}}if(d.sharkHoleCards){if(!Array.isArray(d.sharkHoleCards))throw TypeError(".pokermaster_proto.GameDataSynNotify.sharkHoleCards: array expected");m.sharkHoleCards=[];for(var i=0;i<d.sharkHoleCards.length;++i){if(typeof d.sharkHoleCards[i]!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.sharkHoleCards: object expected");m.sharkHoleCards[i]=$root.pokermaster_proto.CardItem.fromObject(d.sharkHoleCards[i])}}if(d.squintMsg!=null){if(typeof d.squintMsg!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.squintMsg: object expected");m.squintMsg=$root.pokermaster_proto.StopBetNotify.fromObject(d.squintMsg)}if(d.fortune!=null){if(typeof d.fortune!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.fortune: object expected");m.fortune=$root.pokermaster_proto.PlayerFortune.fromObject(d.fortune)}if(d.oddsOp){if(!Array.isArray(d.oddsOp))throw TypeError(".pokermaster_proto.GameDataSynNotify.oddsOp: array expected");m.oddsOp=[];for(var i=0;i<d.oddsOp.length;++i){if(typeof d.oddsOp[i]!=="object")throw TypeError(".pokermaster_proto.GameDataSynNotify.oddsOp: object expected");m.oddsOp[i]=$root.pokermaster_proto.BetOptionsOdds.fromObject(d.oddsOp[i])}}if(d.whoIsLeader!=null){m.whoIsLeader=d.whoIsLeader|0}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}if(d.reachLimitBet!=null){m.reachLimitBet=Boolean(d.reachLimitBet)}return m};GameDataSynNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.optionInfo=[];d.lastResult=[];d.players=[];d.publicCards=[];d.optionResults=[];d.betCoinOption=[];d.AutoBetCountList=[];d.fisherHoleCards=[];d.sharkHoleCards=[];d.oddsOp=[]}if(o.defaults){d.param=null;d.curState=o.enums===String?"RoundState_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;d.canAuto=false;d.cachedNotifyMsg=null;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;d.openRoads=false;d.autoLevel=o.enums===String?"Level_Normal":0;d.usedAutoBetCount=0;d.selectAutoBetCount=0;d.canAdvanceAuto=false;if($util.Long){var n=new $util.Long(0,0,false);d.BetButtonLimitAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.BetButtonLimitAmount=o.longs===String?"0":0;d.squintMsg=null;d.fortune=null;d.whoIsLeader=0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0;d.reachLimitBet=false}if(m.param!=null&&m.hasOwnProperty("param")){d.param=$root.pokermaster_proto.RoomParam.toObject(m.param,o)}if(m.optionInfo&&m.optionInfo.length){d.optionInfo=[];for(var j=0;j<m.optionInfo.length;++j){d.optionInfo[j]=$root.pokermaster_proto.BetOptionInfo.toObject(m.optionInfo[j],o)}}if(m.lastResult&&m.lastResult.length){d.lastResult=[];for(var j=0;j<m.lastResult.length;++j){d.lastResult[j]=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.lastResult[j]]:m.lastResult[j]}}if(m.curState!=null&&m.hasOwnProperty("curState")){d.curState=o.enums===String?$root.pokermaster_proto.RoundState[m.curState]:m.curState}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.players&&m.players.length){d.players=[];for(var j=0;j<m.players.length;++j){d.players[j]=$root.pokermaster_proto.GamePlayer.toObject(m.players[j],o)}}if(m.publicCards&&m.publicCards.length){d.publicCards=[];for(var j=0;j<m.publicCards.length;++j){d.publicCards[j]=$root.pokermaster_proto.CardItem.toObject(m.publicCards[j],o)}}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){d.canAuto=m.canAuto}if(m.cachedNotifyMsg!=null&&m.hasOwnProperty("cachedNotifyMsg")){d.cachedNotifyMsg=$root.pokermaster_proto.GameRoundEndNotify.toObject(m.cachedNotifyMsg,o)}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.openRoads!=null&&m.hasOwnProperty("openRoads")){d.openRoads=m.openRoads}if(m.optionResults&&m.optionResults.length){d.optionResults=[];for(var j=0;j<m.optionResults.length;++j){d.optionResults[j]=$root.pokermaster_proto.OptionResults.toObject(m.optionResults[j],o)}}if(m.betCoinOption&&m.betCoinOption.length){d.betCoinOption=[];for(var j=0;j<m.betCoinOption.length;++j){if(typeof m.betCoinOption[j]==="number")d.betCoinOption[j]=o.longs===String?String(m.betCoinOption[j]):m.betCoinOption[j];else d.betCoinOption[j]=o.longs===String?$util.Long.prototype.toString.call(m.betCoinOption[j]):o.longs===Number?new $util.LongBits(m.betCoinOption[j].low>>>0,m.betCoinOption[j].high>>>0).toNumber(true):m.betCoinOption[j]}}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){d.autoLevel=o.enums===String?$root.pokermaster_proto.AutoBetLevel[m.autoLevel]:m.autoLevel}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){d.usedAutoBetCount=m.usedAutoBetCount}if(m.selectAutoBetCount!=null&&m.hasOwnProperty("selectAutoBetCount")){d.selectAutoBetCount=m.selectAutoBetCount}if(m.AutoBetCountList&&m.AutoBetCountList.length){d.AutoBetCountList=[];for(var j=0;j<m.AutoBetCountList.length;++j){d.AutoBetCountList[j]=m.AutoBetCountList[j]}}if(m.canAdvanceAuto!=null&&m.hasOwnProperty("canAdvanceAuto")){d.canAdvanceAuto=m.canAdvanceAuto}if(m.BetButtonLimitAmount!=null&&m.hasOwnProperty("BetButtonLimitAmount")){if(typeof m.BetButtonLimitAmount==="number")d.BetButtonLimitAmount=o.longs===String?String(m.BetButtonLimitAmount):m.BetButtonLimitAmount;else d.BetButtonLimitAmount=o.longs===String?$util.Long.prototype.toString.call(m.BetButtonLimitAmount):o.longs===Number?new $util.LongBits(m.BetButtonLimitAmount.low>>>0,m.BetButtonLimitAmount.high>>>0).toNumber():m.BetButtonLimitAmount}if(m.fisherHoleCards&&m.fisherHoleCards.length){d.fisherHoleCards=[];for(var j=0;j<m.fisherHoleCards.length;++j){d.fisherHoleCards[j]=$root.pokermaster_proto.CardItem.toObject(m.fisherHoleCards[j],o)}}if(m.sharkHoleCards&&m.sharkHoleCards.length){d.sharkHoleCards=[];for(var j=0;j<m.sharkHoleCards.length;++j){d.sharkHoleCards[j]=$root.pokermaster_proto.CardItem.toObject(m.sharkHoleCards[j],o)}}if(m.squintMsg!=null&&m.hasOwnProperty("squintMsg")){d.squintMsg=$root.pokermaster_proto.StopBetNotify.toObject(m.squintMsg,o)}if(m.fortune!=null&&m.hasOwnProperty("fortune")){d.fortune=$root.pokermaster_proto.PlayerFortune.toObject(m.fortune,o)}if(m.oddsOp&&m.oddsOp.length){d.oddsOp=[];for(var j=0;j<m.oddsOp.length;++j){d.oddsOp[j]=$root.pokermaster_proto.BetOptionsOdds.toObject(m.oddsOp[j],o)}}if(m.whoIsLeader!=null&&m.hasOwnProperty("whoIsLeader")){d.whoIsLeader=m.whoIsLeader}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}if(m.reachLimitBet!=null&&m.hasOwnProperty("reachLimitBet")){d.reachLimitBet=m.reachLimitBet}return d};GameDataSynNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameDataSynNotify}();pokermaster_proto.BetOptionInfo=function(){function BetOptionInfo(p){this.amount=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetOptionInfo.prototype.option=0;BetOptionInfo.prototype.selfBet=$util.Long?$util.Long.fromBits(0,0,true):0;BetOptionInfo.prototype.totalBet=$util.Long?$util.Long.fromBits(0,0,true):0;BetOptionInfo.prototype.amount=$util.emptyArray;BetOptionInfo.prototype.odds=$util.Long?$util.Long.fromBits(0,0,false):0;BetOptionInfo.prototype.limitRed=$util.Long?$util.Long.fromBits(0,0,true):0;BetOptionInfo.create=function create(properties){return new BetOptionInfo(properties)};BetOptionInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.selfBet!=null&&Object.hasOwnProperty.call(m,"selfBet"))w.uint32(16).uint64(m.selfBet);if(m.totalBet!=null&&Object.hasOwnProperty.call(m,"totalBet"))w.uint32(24).uint64(m.totalBet);if(m.amount!=null&&m.amount.length){w.uint32(34).fork();for(var i=0;i<m.amount.length;++i)w.uint64(m.amount[i]);w.ldelim()}if(m.odds!=null&&Object.hasOwnProperty.call(m,"odds"))w.uint32(40).int64(m.odds);if(m.limitRed!=null&&Object.hasOwnProperty.call(m,"limitRed"))w.uint32(48).uint64(m.limitRed);return w};BetOptionInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetOptionInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetOptionInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.selfBet=r.uint64();break;case 3:m.totalBet=r.uint64();break;case 4:if(!(m.amount&&m.amount.length))m.amount=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.amount.push(r.uint64())}else m.amount.push(r.uint64());break;case 5:m.odds=r.int64();break;case 6:m.limitRed=r.uint64();break;default:r.skipType(t&7);break}}return m};BetOptionInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetOptionInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.selfBet!=null&&m.hasOwnProperty("selfBet")){if(!$util.isInteger(m.selfBet)&&!(m.selfBet&&$util.isInteger(m.selfBet.low)&&$util.isInteger(m.selfBet.high)))return"selfBet: integer|Long expected"}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(!$util.isInteger(m.totalBet)&&!(m.totalBet&&$util.isInteger(m.totalBet.low)&&$util.isInteger(m.totalBet.high)))return"totalBet: integer|Long expected"}if(m.amount!=null&&m.hasOwnProperty("amount")){if(!Array.isArray(m.amount))return"amount: array expected";for(var i=0;i<m.amount.length;++i){if(!$util.isInteger(m.amount[i])&&!(m.amount[i]&&$util.isInteger(m.amount[i].low)&&$util.isInteger(m.amount[i].high)))return"amount: integer|Long[] expected"}}if(m.odds!=null&&m.hasOwnProperty("odds")){if(!$util.isInteger(m.odds)&&!(m.odds&&$util.isInteger(m.odds.low)&&$util.isInteger(m.odds.high)))return"odds: integer|Long expected"}if(m.limitRed!=null&&m.hasOwnProperty("limitRed")){if(!$util.isInteger(m.limitRed)&&!(m.limitRed&&$util.isInteger(m.limitRed.low)&&$util.isInteger(m.limitRed.high)))return"limitRed: integer|Long expected"}return null};BetOptionInfo.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetOptionInfo)return d;var m=new $root.pokermaster_proto.BetOptionInfo;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"WIN_BEGIN":case 100:m.option=100;break;case"FISHER_WIN":case 101:m.option=101;break;case"SHARK_WIN":case 102:m.option=102;break;case"EQUAL":case 103:m.option=103;break;case"WIN_END":case 199:m.option=199;break;case"FIVE_BEGIN":case 300:m.option=300;break;case"FIVE_NONE_1DUI":case 301:m.option=301;break;case"FIVE_2DUI":case 302:m.option=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.option=303;break;case"FIVE_GOURD":case 304:m.option=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.option=305;break;case"FIVE_END":case 399:m.option=399;break}if(d.selfBet!=null){if($util.Long)(m.selfBet=$util.Long.fromValue(d.selfBet)).unsigned=true;else if(typeof d.selfBet==="string")m.selfBet=parseInt(d.selfBet,10);else if(typeof d.selfBet==="number")m.selfBet=d.selfBet;else if(typeof d.selfBet==="object")m.selfBet=new $util.LongBits(d.selfBet.low>>>0,d.selfBet.high>>>0).toNumber(true)}if(d.totalBet!=null){if($util.Long)(m.totalBet=$util.Long.fromValue(d.totalBet)).unsigned=true;else if(typeof d.totalBet==="string")m.totalBet=parseInt(d.totalBet,10);else if(typeof d.totalBet==="number")m.totalBet=d.totalBet;else if(typeof d.totalBet==="object")m.totalBet=new $util.LongBits(d.totalBet.low>>>0,d.totalBet.high>>>0).toNumber(true)}if(d.amount){if(!Array.isArray(d.amount))throw TypeError(".pokermaster_proto.BetOptionInfo.amount: array expected");m.amount=[];for(var i=0;i<d.amount.length;++i){if($util.Long)(m.amount[i]=$util.Long.fromValue(d.amount[i])).unsigned=true;else if(typeof d.amount[i]==="string")m.amount[i]=parseInt(d.amount[i],10);else if(typeof d.amount[i]==="number")m.amount[i]=d.amount[i];else if(typeof d.amount[i]==="object")m.amount[i]=new $util.LongBits(d.amount[i].low>>>0,d.amount[i].high>>>0).toNumber(true)}}if(d.odds!=null){if($util.Long)(m.odds=$util.Long.fromValue(d.odds)).unsigned=false;else if(typeof d.odds==="string")m.odds=parseInt(d.odds,10);else if(typeof d.odds==="number")m.odds=d.odds;else if(typeof d.odds==="object")m.odds=new $util.LongBits(d.odds.low>>>0,d.odds.high>>>0).toNumber()}if(d.limitRed!=null){if($util.Long)(m.limitRed=$util.Long.fromValue(d.limitRed)).unsigned=true;else if(typeof d.limitRed==="string")m.limitRed=parseInt(d.limitRed,10);else if(typeof d.limitRed==="number")m.limitRed=d.limitRed;else if(typeof d.limitRed==="object")m.limitRed=new $util.LongBits(d.limitRed.low>>>0,d.limitRed.high>>>0).toNumber(true)}return m};BetOptionInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.amount=[]}if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.selfBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.selfBet=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.totalBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBet=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.odds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.odds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.limitRed=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.limitRed=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.option]:m.option}if(m.selfBet!=null&&m.hasOwnProperty("selfBet")){if(typeof m.selfBet==="number")d.selfBet=o.longs===String?String(m.selfBet):m.selfBet;else d.selfBet=o.longs===String?$util.Long.prototype.toString.call(m.selfBet):o.longs===Number?new $util.LongBits(m.selfBet.low>>>0,m.selfBet.high>>>0).toNumber(true):m.selfBet}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(typeof m.totalBet==="number")d.totalBet=o.longs===String?String(m.totalBet):m.totalBet;else d.totalBet=o.longs===String?$util.Long.prototype.toString.call(m.totalBet):o.longs===Number?new $util.LongBits(m.totalBet.low>>>0,m.totalBet.high>>>0).toNumber(true):m.totalBet}if(m.amount&&m.amount.length){d.amount=[];for(var j=0;j<m.amount.length;++j){if(typeof m.amount[j]==="number")d.amount[j]=o.longs===String?String(m.amount[j]):m.amount[j];else d.amount[j]=o.longs===String?$util.Long.prototype.toString.call(m.amount[j]):o.longs===Number?new $util.LongBits(m.amount[j].low>>>0,m.amount[j].high>>>0).toNumber(true):m.amount[j]}}if(m.odds!=null&&m.hasOwnProperty("odds")){if(typeof m.odds==="number")d.odds=o.longs===String?String(m.odds):m.odds;else d.odds=o.longs===String?$util.Long.prototype.toString.call(m.odds):o.longs===Number?new $util.LongBits(m.odds.low>>>0,m.odds.high>>>0).toNumber():m.odds}if(m.limitRed!=null&&m.hasOwnProperty("limitRed")){if(typeof m.limitRed==="number")d.limitRed=o.longs===String?String(m.limitRed):m.limitRed;else d.limitRed=o.longs===String?$util.Long.prototype.toString.call(m.limitRed):o.longs===Number?new $util.LongBits(m.limitRed.low>>>0,m.limitRed.high>>>0).toNumber(true):m.limitRed}return d};BetOptionInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetOptionInfo}();pokermaster_proto.DealNotify=function(){function DealNotify(p){this.players=[];this.lastResult=[];this.playerHoleCard=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DealNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;DealNotify.prototype.players=$util.emptyArray;DealNotify.prototype.param=null;DealNotify.prototype.changed=false;DealNotify.prototype.lastResult=$util.emptyArray;DealNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;DealNotify.prototype.canAuto=false;DealNotify.prototype.playerHoleCard=$util.emptyArray;DealNotify.create=function create(properties){return new DealNotify(properties)};DealNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(8).int64(m.nextRoundEndStamp);if(m.players!=null&&m.players.length){for(var i=0;i<m.players.length;++i)$root.pokermaster_proto.GamePlayer.encode(m.players[i],w.uint32(18).fork()).ldelim()}if(m.param!=null&&Object.hasOwnProperty.call(m,"param"))$root.pokermaster_proto.RoomParam.encode(m.param,w.uint32(26).fork()).ldelim();if(m.changed!=null&&Object.hasOwnProperty.call(m,"changed"))w.uint32(32).bool(m.changed);if(m.lastResult!=null&&m.lastResult.length){w.uint32(42).fork();for(var i=0;i<m.lastResult.length;++i)w.int32(m.lastResult[i]);w.ldelim()}if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(48).int64(m.leftSeconds);if(m.canAuto!=null&&Object.hasOwnProperty.call(m,"canAuto"))w.uint32(56).bool(m.canAuto);if(m.playerHoleCard!=null&&m.playerHoleCard.length){for(var i=0;i<m.playerHoleCard.length;++i)$root.pokermaster_proto.PlayerHoleCard.encode(m.playerHoleCard[i],w.uint32(66).fork()).ldelim()}return w};DealNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DealNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.DealNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.nextRoundEndStamp=r.int64();break;case 2:if(!(m.players&&m.players.length))m.players=[];m.players.push($root.pokermaster_proto.GamePlayer.decode(r,r.uint32()));break;case 3:m.param=$root.pokermaster_proto.RoomParam.decode(r,r.uint32());break;case 4:m.changed=r.bool();break;case 5:if(!(m.lastResult&&m.lastResult.length))m.lastResult=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.lastResult.push(r.int32())}else m.lastResult.push(r.int32());break;case 6:m.leftSeconds=r.int64();break;case 7:m.canAuto=r.bool();break;case 8:if(!(m.playerHoleCard&&m.playerHoleCard.length))m.playerHoleCard=[];m.playerHoleCard.push($root.pokermaster_proto.PlayerHoleCard.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};DealNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DealNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.players!=null&&m.hasOwnProperty("players")){if(!Array.isArray(m.players))return"players: array expected";for(var i=0;i<m.players.length;++i){{var e=$root.pokermaster_proto.GamePlayer.verify(m.players[i]);if(e)return"players."+e}}}if(m.param!=null&&m.hasOwnProperty("param")){{var e=$root.pokermaster_proto.RoomParam.verify(m.param);if(e)return"param."+e}}if(m.changed!=null&&m.hasOwnProperty("changed")){if(typeof m.changed!=="boolean")return"changed: boolean expected"}if(m.lastResult!=null&&m.hasOwnProperty("lastResult")){if(!Array.isArray(m.lastResult))return"lastResult: array expected";for(var i=0;i<m.lastResult.length;++i){switch(m.lastResult[i]){default:return"lastResult: enum value[] expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){if(typeof m.canAuto!=="boolean")return"canAuto: boolean expected"}if(m.playerHoleCard!=null&&m.hasOwnProperty("playerHoleCard")){if(!Array.isArray(m.playerHoleCard))return"playerHoleCard: array expected";for(var i=0;i<m.playerHoleCard.length;++i){{var e=$root.pokermaster_proto.PlayerHoleCard.verify(m.playerHoleCard[i]);if(e)return"playerHoleCard."+e}}}return null};DealNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.DealNotify)return d;var m=new $root.pokermaster_proto.DealNotify;if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.players){if(!Array.isArray(d.players))throw TypeError(".pokermaster_proto.DealNotify.players: array expected");m.players=[];for(var i=0;i<d.players.length;++i){if(typeof d.players[i]!=="object")throw TypeError(".pokermaster_proto.DealNotify.players: object expected");m.players[i]=$root.pokermaster_proto.GamePlayer.fromObject(d.players[i])}}if(d.param!=null){if(typeof d.param!=="object")throw TypeError(".pokermaster_proto.DealNotify.param: object expected");m.param=$root.pokermaster_proto.RoomParam.fromObject(d.param)}if(d.changed!=null){m.changed=Boolean(d.changed)}if(d.lastResult){if(!Array.isArray(d.lastResult))throw TypeError(".pokermaster_proto.DealNotify.lastResult: array expected");m.lastResult=[];for(var i=0;i<d.lastResult.length;++i){switch(d.lastResult[i]){default:case"BetZoneOption_DUMMY":case 0:m.lastResult[i]=0;break;case"WIN_BEGIN":case 100:m.lastResult[i]=100;break;case"FISHER_WIN":case 101:m.lastResult[i]=101;break;case"SHARK_WIN":case 102:m.lastResult[i]=102;break;case"EQUAL":case 103:m.lastResult[i]=103;break;case"WIN_END":case 199:m.lastResult[i]=199;break;case"FIVE_BEGIN":case 300:m.lastResult[i]=300;break;case"FIVE_NONE_1DUI":case 301:m.lastResult[i]=301;break;case"FIVE_2DUI":case 302:m.lastResult[i]=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.lastResult[i]=303;break;case"FIVE_GOURD":case 304:m.lastResult[i]=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.lastResult[i]=305;break;case"FIVE_END":case 399:m.lastResult[i]=399;break}}}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.canAuto!=null){m.canAuto=Boolean(d.canAuto)}if(d.playerHoleCard){if(!Array.isArray(d.playerHoleCard))throw TypeError(".pokermaster_proto.DealNotify.playerHoleCard: array expected");m.playerHoleCard=[];for(var i=0;i<d.playerHoleCard.length;++i){if(typeof d.playerHoleCard[i]!=="object")throw TypeError(".pokermaster_proto.DealNotify.playerHoleCard: object expected");m.playerHoleCard[i]=$root.pokermaster_proto.PlayerHoleCard.fromObject(d.playerHoleCard[i])}}return m};DealNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.players=[];d.lastResult=[];d.playerHoleCard=[]}if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;d.param=null;d.changed=false;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;d.canAuto=false}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.players&&m.players.length){d.players=[];for(var j=0;j<m.players.length;++j){d.players[j]=$root.pokermaster_proto.GamePlayer.toObject(m.players[j],o)}}if(m.param!=null&&m.hasOwnProperty("param")){d.param=$root.pokermaster_proto.RoomParam.toObject(m.param,o)}if(m.changed!=null&&m.hasOwnProperty("changed")){d.changed=m.changed}if(m.lastResult&&m.lastResult.length){d.lastResult=[];for(var j=0;j<m.lastResult.length;++j){d.lastResult[j]=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.lastResult[j]]:m.lastResult[j]}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){d.canAuto=m.canAuto}if(m.playerHoleCard&&m.playerHoleCard.length){d.playerHoleCard=[];for(var j=0;j<m.playerHoleCard.length;++j){d.playerHoleCard[j]=$root.pokermaster_proto.PlayerHoleCard.toObject(m.playerHoleCard[j],o)}}return d};DealNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DealNotify}();pokermaster_proto.BetReq=function(){function BetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetReq.prototype.detail=null;BetReq.create=function create(properties){return new BetReq(properties)};BetReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.detail!=null&&Object.hasOwnProperty.call(m,"detail"))$root.pokermaster_proto.BetDetail.encode(m.detail,w.uint32(10).fork()).ldelim();return w};BetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.detail=$root.pokermaster_proto.BetDetail.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};BetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.detail!=null&&m.hasOwnProperty("detail")){{var e=$root.pokermaster_proto.BetDetail.verify(m.detail);if(e)return"detail."+e}}return null};BetReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetReq)return d;var m=new $root.pokermaster_proto.BetReq;if(d.detail!=null){if(typeof d.detail!=="object")throw TypeError(".pokermaster_proto.BetReq.detail: object expected");m.detail=$root.pokermaster_proto.BetDetail.fromObject(d.detail)}return m};BetReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.detail=null}if(m.detail!=null&&m.hasOwnProperty("detail")){d.detail=$root.pokermaster_proto.BetDetail.toObject(m.detail,o)}return d};BetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetReq}();pokermaster_proto.BillInfo=function(){function BillInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BillInfo.prototype.BillNo="";BillInfo.prototype.time=$util.Long?$util.Long.fromBits(0,0,false):0;BillInfo.create=function create(properties){return new BillInfo(properties)};BillInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.BillNo!=null&&Object.hasOwnProperty.call(m,"BillNo"))w.uint32(10).string(m.BillNo);if(m.time!=null&&Object.hasOwnProperty.call(m,"time"))w.uint32(16).int64(m.time);return w};BillInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BillInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BillInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.BillNo=r.string();break;case 2:m.time=r.int64();break;default:r.skipType(t&7);break}}return m};BillInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BillInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.BillNo!=null&&m.hasOwnProperty("BillNo")){if(!$util.isString(m.BillNo))return"BillNo: string expected"}if(m.time!=null&&m.hasOwnProperty("time")){if(!$util.isInteger(m.time)&&!(m.time&&$util.isInteger(m.time.low)&&$util.isInteger(m.time.high)))return"time: integer|Long expected"}return null};BillInfo.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BillInfo)return d;var m=new $root.pokermaster_proto.BillInfo;if(d.BillNo!=null){m.BillNo=String(d.BillNo)}if(d.time!=null){if($util.Long)(m.time=$util.Long.fromValue(d.time)).unsigned=false;else if(typeof d.time==="string")m.time=parseInt(d.time,10);else if(typeof d.time==="number")m.time=d.time;else if(typeof d.time==="object")m.time=new $util.LongBits(d.time.low>>>0,d.time.high>>>0).toNumber()}return m};BillInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.BillNo="";if($util.Long){var n=new $util.Long(0,0,false);d.time=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.time=o.longs===String?"0":0}if(m.BillNo!=null&&m.hasOwnProperty("BillNo")){d.BillNo=m.BillNo}if(m.time!=null&&m.hasOwnProperty("time")){if(typeof m.time==="number")d.time=o.longs===String?String(m.time):m.time;else d.time=o.longs===String?$util.Long.prototype.toString.call(m.time):o.longs===Number?new $util.LongBits(m.time.low>>>0,m.time.high>>>0).toNumber():m.time}return d};BillInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BillInfo}();pokermaster_proto.BetResp=function(){function BetResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetResp.prototype.code=0;BetResp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;BetResp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;BetResp.prototype.bill=null;BetResp.create=function create(properties){return new BetResp(properties)};BetResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);if(m.bill!=null&&Object.hasOwnProperty.call(m,"bill"))$root.pokermaster_proto.BillInfo.encode(m.bill,w.uint32(42).fork()).ldelim();return w};BetResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;case 5:m.bill=$root.pokermaster_proto.BillInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};BetResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}if(m.bill!=null&&m.hasOwnProperty("bill")){{var e=$root.pokermaster_proto.BillInfo.verify(m.bill);if(e)return"bill."+e}}return null};BetResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetResp)return d;var m=new $root.pokermaster_proto.BetResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}if(d.bill!=null){if(typeof d.bill!=="object")throw TypeError(".pokermaster_proto.BetResp.bill: object expected");m.bill=$root.pokermaster_proto.BillInfo.fromObject(d.bill)}return m};BetResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0;d.bill=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}if(m.bill!=null&&m.hasOwnProperty("bill")){d.bill=$root.pokermaster_proto.BillInfo.toObject(m.bill,o)}return d};BetResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetResp}();pokermaster_proto.BetNotify=function(){function BetNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetNotify.prototype.uid=0;BetNotify.prototype.detail=null;BetNotify.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,true):0;BetNotify.prototype.selfBet=$util.Long?$util.Long.fromBits(0,0,true):0;BetNotify.prototype.totalBet=$util.Long?$util.Long.fromBits(0,0,true):0;BetNotify.prototype.curUsdt=$util.Long?$util.Long.fromBits(0,0,true):0;BetNotify.create=function create(properties){return new BetNotify(properties)};BetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.detail!=null&&Object.hasOwnProperty.call(m,"detail"))$root.pokermaster_proto.BetDetail.encode(m.detail,w.uint32(18).fork()).ldelim();if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(24).uint64(m.curCoin);if(m.selfBet!=null&&Object.hasOwnProperty.call(m,"selfBet"))w.uint32(32).uint64(m.selfBet);if(m.totalBet!=null&&Object.hasOwnProperty.call(m,"totalBet"))w.uint32(40).uint64(m.totalBet);if(m.curUsdt!=null&&Object.hasOwnProperty.call(m,"curUsdt"))w.uint32(48).uint64(m.curUsdt);return w};BetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.detail=$root.pokermaster_proto.BetDetail.decode(r,r.uint32());break;case 3:m.curCoin=r.uint64();break;case 4:m.selfBet=r.uint64();break;case 5:m.totalBet=r.uint64();break;case 6:m.curUsdt=r.uint64();break;default:r.skipType(t&7);break}}return m};BetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.detail!=null&&m.hasOwnProperty("detail")){{var e=$root.pokermaster_proto.BetDetail.verify(m.detail);if(e)return"detail."+e}}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}if(m.selfBet!=null&&m.hasOwnProperty("selfBet")){if(!$util.isInteger(m.selfBet)&&!(m.selfBet&&$util.isInteger(m.selfBet.low)&&$util.isInteger(m.selfBet.high)))return"selfBet: integer|Long expected"}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(!$util.isInteger(m.totalBet)&&!(m.totalBet&&$util.isInteger(m.totalBet.low)&&$util.isInteger(m.totalBet.high)))return"totalBet: integer|Long expected"}if(m.curUsdt!=null&&m.hasOwnProperty("curUsdt")){if(!$util.isInteger(m.curUsdt)&&!(m.curUsdt&&$util.isInteger(m.curUsdt.low)&&$util.isInteger(m.curUsdt.high)))return"curUsdt: integer|Long expected"}return null};BetNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetNotify)return d;var m=new $root.pokermaster_proto.BetNotify;if(d.uid!=null){m.uid=d.uid>>>0}if(d.detail!=null){if(typeof d.detail!=="object")throw TypeError(".pokermaster_proto.BetNotify.detail: object expected");m.detail=$root.pokermaster_proto.BetDetail.fromObject(d.detail)}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=true;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber(true)}if(d.selfBet!=null){if($util.Long)(m.selfBet=$util.Long.fromValue(d.selfBet)).unsigned=true;else if(typeof d.selfBet==="string")m.selfBet=parseInt(d.selfBet,10);else if(typeof d.selfBet==="number")m.selfBet=d.selfBet;else if(typeof d.selfBet==="object")m.selfBet=new $util.LongBits(d.selfBet.low>>>0,d.selfBet.high>>>0).toNumber(true)}if(d.totalBet!=null){if($util.Long)(m.totalBet=$util.Long.fromValue(d.totalBet)).unsigned=true;else if(typeof d.totalBet==="string")m.totalBet=parseInt(d.totalBet,10);else if(typeof d.totalBet==="number")m.totalBet=d.totalBet;else if(typeof d.totalBet==="object")m.totalBet=new $util.LongBits(d.totalBet.low>>>0,d.totalBet.high>>>0).toNumber(true)}if(d.curUsdt!=null){if($util.Long)(m.curUsdt=$util.Long.fromValue(d.curUsdt)).unsigned=true;else if(typeof d.curUsdt==="string")m.curUsdt=parseInt(d.curUsdt,10);else if(typeof d.curUsdt==="number")m.curUsdt=d.curUsdt;else if(typeof d.curUsdt==="object")m.curUsdt=new $util.LongBits(d.curUsdt.low>>>0,d.curUsdt.high>>>0).toNumber(true)}return m};BetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;d.detail=null;if($util.Long){var n=new $util.Long(0,0,true);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.selfBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.selfBet=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.totalBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBet=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.curUsdt=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curUsdt=o.longs===String?"0":0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.detail!=null&&m.hasOwnProperty("detail")){d.detail=$root.pokermaster_proto.BetDetail.toObject(m.detail,o)}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber(true):m.curCoin}if(m.selfBet!=null&&m.hasOwnProperty("selfBet")){if(typeof m.selfBet==="number")d.selfBet=o.longs===String?String(m.selfBet):m.selfBet;else d.selfBet=o.longs===String?$util.Long.prototype.toString.call(m.selfBet):o.longs===Number?new $util.LongBits(m.selfBet.low>>>0,m.selfBet.high>>>0).toNumber(true):m.selfBet}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(typeof m.totalBet==="number")d.totalBet=o.longs===String?String(m.totalBet):m.totalBet;else d.totalBet=o.longs===String?$util.Long.prototype.toString.call(m.totalBet):o.longs===Number?new $util.LongBits(m.totalBet.low>>>0,m.totalBet.high>>>0).toNumber(true):m.totalBet}if(m.curUsdt!=null&&m.hasOwnProperty("curUsdt")){if(typeof m.curUsdt==="number")d.curUsdt=o.longs===String?String(m.curUsdt):m.curUsdt;else d.curUsdt=o.longs===String?$util.Long.prototype.toString.call(m.curUsdt):o.longs===Number?new $util.LongBits(m.curUsdt.low>>>0,m.curUsdt.high>>>0).toNumber(true):m.curUsdt}return d};BetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetNotify}();pokermaster_proto.MergeAutoBetNotify=function(){function MergeAutoBetNotify(p){this.notify=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}MergeAutoBetNotify.prototype.notify=$util.emptyArray;MergeAutoBetNotify.create=function create(properties){return new MergeAutoBetNotify(properties)};MergeAutoBetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.notify!=null&&m.notify.length){for(var i=0;i<m.notify.length;++i)$root.pokermaster_proto.BetNotify.encode(m.notify[i],w.uint32(10).fork()).ldelim()}return w};MergeAutoBetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};MergeAutoBetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.MergeAutoBetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.notify&&m.notify.length))m.notify=[];m.notify.push($root.pokermaster_proto.BetNotify.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};MergeAutoBetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};MergeAutoBetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.notify!=null&&m.hasOwnProperty("notify")){if(!Array.isArray(m.notify))return"notify: array expected";for(var i=0;i<m.notify.length;++i){{var e=$root.pokermaster_proto.BetNotify.verify(m.notify[i]);if(e)return"notify."+e}}}return null};MergeAutoBetNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.MergeAutoBetNotify)return d;var m=new $root.pokermaster_proto.MergeAutoBetNotify;if(d.notify){if(!Array.isArray(d.notify))throw TypeError(".pokermaster_proto.MergeAutoBetNotify.notify: array expected");m.notify=[];for(var i=0;i<d.notify.length;++i){if(typeof d.notify[i]!=="object")throw TypeError(".pokermaster_proto.MergeAutoBetNotify.notify: object expected");m.notify[i]=$root.pokermaster_proto.BetNotify.fromObject(d.notify[i])}}return m};MergeAutoBetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.notify=[]}if(m.notify&&m.notify.length){d.notify=[];for(var j=0;j<m.notify.length;++j){d.notify[j]=$root.pokermaster_proto.BetNotify.toObject(m.notify[j],o)}}return d};MergeAutoBetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return MergeAutoBetNotify}();pokermaster_proto.BetDetail=function(){function BetDetail(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetDetail.prototype.option=0;BetDetail.prototype.betAmount=$util.Long?$util.Long.fromBits(0,0,true):0;BetDetail.prototype.auto=false;BetDetail.prototype.is_shot=false;BetDetail.prototype.win_amt=$util.Long?$util.Long.fromBits(0,0,false):0;BetDetail.prototype.odds=$util.Long?$util.Long.fromBits(0,0,false):0;BetDetail.prototype.betGameCoin=$util.Long?$util.Long.fromBits(0,0,false):0;BetDetail.create=function create(properties){return new BetDetail(properties)};BetDetail.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.betAmount!=null&&Object.hasOwnProperty.call(m,"betAmount"))w.uint32(16).uint64(m.betAmount);if(m.auto!=null&&Object.hasOwnProperty.call(m,"auto"))w.uint32(24).bool(m.auto);if(m.is_shot!=null&&Object.hasOwnProperty.call(m,"is_shot"))w.uint32(32).bool(m.is_shot);if(m.win_amt!=null&&Object.hasOwnProperty.call(m,"win_amt"))w.uint32(40).int64(m.win_amt);if(m.odds!=null&&Object.hasOwnProperty.call(m,"odds"))w.uint32(48).int64(m.odds);if(m.betGameCoin!=null&&Object.hasOwnProperty.call(m,"betGameCoin"))w.uint32(56).int64(m.betGameCoin);return w};BetDetail.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetDetail.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetDetail;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.betAmount=r.uint64();break;case 3:m.auto=r.bool();break;case 4:m.is_shot=r.bool();break;case 5:m.win_amt=r.int64();break;case 6:m.odds=r.int64();break;case 7:m.betGameCoin=r.int64();break;default:r.skipType(t&7);break}}return m};BetDetail.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetDetail.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.betAmount!=null&&m.hasOwnProperty("betAmount")){if(!$util.isInteger(m.betAmount)&&!(m.betAmount&&$util.isInteger(m.betAmount.low)&&$util.isInteger(m.betAmount.high)))return"betAmount: integer|Long expected"}if(m.auto!=null&&m.hasOwnProperty("auto")){if(typeof m.auto!=="boolean")return"auto: boolean expected"}if(m.is_shot!=null&&m.hasOwnProperty("is_shot")){if(typeof m.is_shot!=="boolean")return"is_shot: boolean expected"}if(m.win_amt!=null&&m.hasOwnProperty("win_amt")){if(!$util.isInteger(m.win_amt)&&!(m.win_amt&&$util.isInteger(m.win_amt.low)&&$util.isInteger(m.win_amt.high)))return"win_amt: integer|Long expected"}if(m.odds!=null&&m.hasOwnProperty("odds")){if(!$util.isInteger(m.odds)&&!(m.odds&&$util.isInteger(m.odds.low)&&$util.isInteger(m.odds.high)))return"odds: integer|Long expected"}if(m.betGameCoin!=null&&m.hasOwnProperty("betGameCoin")){if(!$util.isInteger(m.betGameCoin)&&!(m.betGameCoin&&$util.isInteger(m.betGameCoin.low)&&$util.isInteger(m.betGameCoin.high)))return"betGameCoin: integer|Long expected"}return null};BetDetail.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetDetail)return d;var m=new $root.pokermaster_proto.BetDetail;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"WIN_BEGIN":case 100:m.option=100;break;case"FISHER_WIN":case 101:m.option=101;break;case"SHARK_WIN":case 102:m.option=102;break;case"EQUAL":case 103:m.option=103;break;case"WIN_END":case 199:m.option=199;break;case"FIVE_BEGIN":case 300:m.option=300;break;case"FIVE_NONE_1DUI":case 301:m.option=301;break;case"FIVE_2DUI":case 302:m.option=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.option=303;break;case"FIVE_GOURD":case 304:m.option=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.option=305;break;case"FIVE_END":case 399:m.option=399;break}if(d.betAmount!=null){if($util.Long)(m.betAmount=$util.Long.fromValue(d.betAmount)).unsigned=true;else if(typeof d.betAmount==="string")m.betAmount=parseInt(d.betAmount,10);else if(typeof d.betAmount==="number")m.betAmount=d.betAmount;else if(typeof d.betAmount==="object")m.betAmount=new $util.LongBits(d.betAmount.low>>>0,d.betAmount.high>>>0).toNumber(true)}if(d.auto!=null){m.auto=Boolean(d.auto)}if(d.is_shot!=null){m.is_shot=Boolean(d.is_shot)}if(d.win_amt!=null){if($util.Long)(m.win_amt=$util.Long.fromValue(d.win_amt)).unsigned=false;else if(typeof d.win_amt==="string")m.win_amt=parseInt(d.win_amt,10);else if(typeof d.win_amt==="number")m.win_amt=d.win_amt;else if(typeof d.win_amt==="object")m.win_amt=new $util.LongBits(d.win_amt.low>>>0,d.win_amt.high>>>0).toNumber()}if(d.odds!=null){if($util.Long)(m.odds=$util.Long.fromValue(d.odds)).unsigned=false;else if(typeof d.odds==="string")m.odds=parseInt(d.odds,10);else if(typeof d.odds==="number")m.odds=d.odds;else if(typeof d.odds==="object")m.odds=new $util.LongBits(d.odds.low>>>0,d.odds.high>>>0).toNumber()}if(d.betGameCoin!=null){if($util.Long)(m.betGameCoin=$util.Long.fromValue(d.betGameCoin)).unsigned=false;else if(typeof d.betGameCoin==="string")m.betGameCoin=parseInt(d.betGameCoin,10);else if(typeof d.betGameCoin==="number")m.betGameCoin=d.betGameCoin;else if(typeof d.betGameCoin==="object")m.betGameCoin=new $util.LongBits(d.betGameCoin.low>>>0,d.betGameCoin.high>>>0).toNumber()}return m};BetDetail.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.betAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.betAmount=o.longs===String?"0":0;d.auto=false;d.is_shot=false;if($util.Long){var n=new $util.Long(0,0,false);d.win_amt=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.win_amt=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.odds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.odds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.betGameCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.betGameCoin=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.option]:m.option}if(m.betAmount!=null&&m.hasOwnProperty("betAmount")){if(typeof m.betAmount==="number")d.betAmount=o.longs===String?String(m.betAmount):m.betAmount;else d.betAmount=o.longs===String?$util.Long.prototype.toString.call(m.betAmount):o.longs===Number?new $util.LongBits(m.betAmount.low>>>0,m.betAmount.high>>>0).toNumber(true):m.betAmount}if(m.auto!=null&&m.hasOwnProperty("auto")){d.auto=m.auto}if(m.is_shot!=null&&m.hasOwnProperty("is_shot")){d.is_shot=m.is_shot}if(m.win_amt!=null&&m.hasOwnProperty("win_amt")){if(typeof m.win_amt==="number")d.win_amt=o.longs===String?String(m.win_amt):m.win_amt;else d.win_amt=o.longs===String?$util.Long.prototype.toString.call(m.win_amt):o.longs===Number?new $util.LongBits(m.win_amt.low>>>0,m.win_amt.high>>>0).toNumber():m.win_amt}if(m.odds!=null&&m.hasOwnProperty("odds")){if(typeof m.odds==="number")d.odds=o.longs===String?String(m.odds):m.odds;else d.odds=o.longs===String?$util.Long.prototype.toString.call(m.odds):o.longs===Number?new $util.LongBits(m.odds.low>>>0,m.odds.high>>>0).toNumber():m.odds}if(m.betGameCoin!=null&&m.hasOwnProperty("betGameCoin")){if(typeof m.betGameCoin==="number")d.betGameCoin=o.longs===String?String(m.betGameCoin):m.betGameCoin;else d.betGameCoin=o.longs===String?$util.Long.prototype.toString.call(m.betGameCoin):o.longs===Number?new $util.LongBits(m.betGameCoin.low>>>0,m.betGameCoin.high>>>0).toNumber():m.betGameCoin}return d};BetDetail.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetDetail}();pokermaster_proto.GameRoundEndNotify=function(){function GameRoundEndNotify(p){this.playerSettle=[];this.optionResult=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameRoundEndNotify.prototype.playerSettle=$util.emptyArray;GameRoundEndNotify.prototype.roundResult=null;GameRoundEndNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.stopWorld=0;GameRoundEndNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.otherPlayers=null;GameRoundEndNotify.prototype.openRoads=false;GameRoundEndNotify.prototype.optionResult=$util.emptyArray;GameRoundEndNotify.prototype.fortune=null;GameRoundEndNotify.prototype.idle_roomid=0;GameRoundEndNotify.prototype.change_points=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.create=function create(properties){return new GameRoundEndNotify(properties)};GameRoundEndNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerSettle!=null&&m.playerSettle.length){for(var i=0;i<m.playerSettle.length;++i)$root.pokermaster_proto.PlayerSettle.encode(m.playerSettle[i],w.uint32(10).fork()).ldelim()}if(m.roundResult!=null&&Object.hasOwnProperty.call(m,"roundResult"))$root.pokermaster_proto.RoundResult.encode(m.roundResult,w.uint32(18).fork()).ldelim();if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(24).int64(m.nextRoundEndStamp);if(m.stopWorld!=null&&Object.hasOwnProperty.call(m,"stopWorld"))w.uint32(32).int32(m.stopWorld);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(40).int64(m.leftSeconds);if(m.otherPlayers!=null&&Object.hasOwnProperty.call(m,"otherPlayers"))$root.pokermaster_proto.PlayerSettle.encode(m.otherPlayers,w.uint32(50).fork()).ldelim();if(m.openRoads!=null&&Object.hasOwnProperty.call(m,"openRoads"))w.uint32(56).bool(m.openRoads);if(m.optionResult!=null&&m.optionResult.length){for(var i=0;i<m.optionResult.length;++i)$root.pokermaster_proto.OptionResult.encode(m.optionResult[i],w.uint32(66).fork()).ldelim()}if(m.fortune!=null&&Object.hasOwnProperty.call(m,"fortune"))$root.pokermaster_proto.PlayerFortune.encode(m.fortune,w.uint32(74).fork()).ldelim();if(m.idle_roomid!=null&&Object.hasOwnProperty.call(m,"idle_roomid"))w.uint32(80).uint32(m.idle_roomid);if(m.change_points!=null&&Object.hasOwnProperty.call(m,"change_points"))w.uint32(88).int64(m.change_points);return w};GameRoundEndNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameRoundEndNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.GameRoundEndNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.playerSettle&&m.playerSettle.length))m.playerSettle=[];m.playerSettle.push($root.pokermaster_proto.PlayerSettle.decode(r,r.uint32()));break;case 2:m.roundResult=$root.pokermaster_proto.RoundResult.decode(r,r.uint32());break;case 3:m.nextRoundEndStamp=r.int64();break;case 4:m.stopWorld=r.int32();break;case 5:m.leftSeconds=r.int64();break;case 6:m.otherPlayers=$root.pokermaster_proto.PlayerSettle.decode(r,r.uint32());break;case 7:m.openRoads=r.bool();break;case 8:if(!(m.optionResult&&m.optionResult.length))m.optionResult=[];m.optionResult.push($root.pokermaster_proto.OptionResult.decode(r,r.uint32()));break;case 9:m.fortune=$root.pokermaster_proto.PlayerFortune.decode(r,r.uint32());break;case 10:m.idle_roomid=r.uint32();break;case 11:m.change_points=r.int64();break;default:r.skipType(t&7);break}}return m};GameRoundEndNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameRoundEndNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerSettle!=null&&m.hasOwnProperty("playerSettle")){if(!Array.isArray(m.playerSettle))return"playerSettle: array expected";for(var i=0;i<m.playerSettle.length;++i){{var e=$root.pokermaster_proto.PlayerSettle.verify(m.playerSettle[i]);if(e)return"playerSettle."+e}}}if(m.roundResult!=null&&m.hasOwnProperty("roundResult")){{var e=$root.pokermaster_proto.RoundResult.verify(m.roundResult);if(e)return"roundResult."+e}}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.stopWorld!=null&&m.hasOwnProperty("stopWorld")){if(!$util.isInteger(m.stopWorld))return"stopWorld: integer expected"}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.otherPlayers!=null&&m.hasOwnProperty("otherPlayers")){{var e=$root.pokermaster_proto.PlayerSettle.verify(m.otherPlayers);if(e)return"otherPlayers."+e}}if(m.openRoads!=null&&m.hasOwnProperty("openRoads")){if(typeof m.openRoads!=="boolean")return"openRoads: boolean expected"}if(m.optionResult!=null&&m.hasOwnProperty("optionResult")){if(!Array.isArray(m.optionResult))return"optionResult: array expected";for(var i=0;i<m.optionResult.length;++i){{var e=$root.pokermaster_proto.OptionResult.verify(m.optionResult[i]);if(e)return"optionResult."+e}}}if(m.fortune!=null&&m.hasOwnProperty("fortune")){{var e=$root.pokermaster_proto.PlayerFortune.verify(m.fortune);if(e)return"fortune."+e}}if(m.idle_roomid!=null&&m.hasOwnProperty("idle_roomid")){if(!$util.isInteger(m.idle_roomid))return"idle_roomid: integer expected"}if(m.change_points!=null&&m.hasOwnProperty("change_points")){if(!$util.isInteger(m.change_points)&&!(m.change_points&&$util.isInteger(m.change_points.low)&&$util.isInteger(m.change_points.high)))return"change_points: integer|Long expected"}return null};GameRoundEndNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.GameRoundEndNotify)return d;var m=new $root.pokermaster_proto.GameRoundEndNotify;if(d.playerSettle){if(!Array.isArray(d.playerSettle))throw TypeError(".pokermaster_proto.GameRoundEndNotify.playerSettle: array expected");m.playerSettle=[];for(var i=0;i<d.playerSettle.length;++i){if(typeof d.playerSettle[i]!=="object")throw TypeError(".pokermaster_proto.GameRoundEndNotify.playerSettle: object expected");m.playerSettle[i]=$root.pokermaster_proto.PlayerSettle.fromObject(d.playerSettle[i])}}if(d.roundResult!=null){if(typeof d.roundResult!=="object")throw TypeError(".pokermaster_proto.GameRoundEndNotify.roundResult: object expected");m.roundResult=$root.pokermaster_proto.RoundResult.fromObject(d.roundResult)}if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.stopWorld!=null){m.stopWorld=d.stopWorld|0}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.otherPlayers!=null){if(typeof d.otherPlayers!=="object")throw TypeError(".pokermaster_proto.GameRoundEndNotify.otherPlayers: object expected");m.otherPlayers=$root.pokermaster_proto.PlayerSettle.fromObject(d.otherPlayers)}if(d.openRoads!=null){m.openRoads=Boolean(d.openRoads)}if(d.optionResult){if(!Array.isArray(d.optionResult))throw TypeError(".pokermaster_proto.GameRoundEndNotify.optionResult: array expected");m.optionResult=[];for(var i=0;i<d.optionResult.length;++i){if(typeof d.optionResult[i]!=="object")throw TypeError(".pokermaster_proto.GameRoundEndNotify.optionResult: object expected");m.optionResult[i]=$root.pokermaster_proto.OptionResult.fromObject(d.optionResult[i])}}if(d.fortune!=null){if(typeof d.fortune!=="object")throw TypeError(".pokermaster_proto.GameRoundEndNotify.fortune: object expected");m.fortune=$root.pokermaster_proto.PlayerFortune.fromObject(d.fortune)}if(d.idle_roomid!=null){m.idle_roomid=d.idle_roomid>>>0}if(d.change_points!=null){if($util.Long)(m.change_points=$util.Long.fromValue(d.change_points)).unsigned=false;else if(typeof d.change_points==="string")m.change_points=parseInt(d.change_points,10);else if(typeof d.change_points==="number")m.change_points=d.change_points;else if(typeof d.change_points==="object")m.change_points=new $util.LongBits(d.change_points.low>>>0,d.change_points.high>>>0).toNumber()}return m};GameRoundEndNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.playerSettle=[];d.optionResult=[]}if(o.defaults){d.roundResult=null;if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;d.stopWorld=0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;d.otherPlayers=null;d.openRoads=false;d.fortune=null;d.idle_roomid=0;if($util.Long){var n=new $util.Long(0,0,false);d.change_points=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.change_points=o.longs===String?"0":0}if(m.playerSettle&&m.playerSettle.length){d.playerSettle=[];for(var j=0;j<m.playerSettle.length;++j){d.playerSettle[j]=$root.pokermaster_proto.PlayerSettle.toObject(m.playerSettle[j],o)}}if(m.roundResult!=null&&m.hasOwnProperty("roundResult")){d.roundResult=$root.pokermaster_proto.RoundResult.toObject(m.roundResult,o)}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.stopWorld!=null&&m.hasOwnProperty("stopWorld")){d.stopWorld=m.stopWorld}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.otherPlayers!=null&&m.hasOwnProperty("otherPlayers")){d.otherPlayers=$root.pokermaster_proto.PlayerSettle.toObject(m.otherPlayers,o)}if(m.openRoads!=null&&m.hasOwnProperty("openRoads")){d.openRoads=m.openRoads}if(m.optionResult&&m.optionResult.length){d.optionResult=[];for(var j=0;j<m.optionResult.length;++j){d.optionResult[j]=$root.pokermaster_proto.OptionResult.toObject(m.optionResult[j],o)}}if(m.fortune!=null&&m.hasOwnProperty("fortune")){d.fortune=$root.pokermaster_proto.PlayerFortune.toObject(m.fortune,o)}if(m.idle_roomid!=null&&m.hasOwnProperty("idle_roomid")){d.idle_roomid=m.idle_roomid}if(m.change_points!=null&&m.hasOwnProperty("change_points")){if(typeof m.change_points==="number")d.change_points=o.longs===String?String(m.change_points):m.change_points;else d.change_points=o.longs===String?$util.Long.prototype.toString.call(m.change_points):o.longs===Number?new $util.LongBits(m.change_points.low>>>0,m.change_points.high>>>0).toNumber():m.change_points}return d};GameRoundEndNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameRoundEndNotify}();pokermaster_proto.OptionResult=function(){function OptionResult(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OptionResult.prototype.option=0;OptionResult.prototype.result=0;OptionResult.prototype.loseHand=0;OptionResult.create=function create(properties){return new OptionResult(properties)};OptionResult.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.result!=null&&Object.hasOwnProperty.call(m,"result"))w.uint32(16).int32(m.result);if(m.loseHand!=null&&Object.hasOwnProperty.call(m,"loseHand"))w.uint32(24).int32(m.loseHand);return w};OptionResult.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OptionResult.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.OptionResult;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.result=r.int32();break;case 3:m.loseHand=r.int32();break;default:r.skipType(t&7);break}}return m};OptionResult.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OptionResult.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.result!=null&&m.hasOwnProperty("result")){if(!$util.isInteger(m.result))return"result: integer expected"}if(m.loseHand!=null&&m.hasOwnProperty("loseHand")){if(!$util.isInteger(m.loseHand))return"loseHand: integer expected"}return null};OptionResult.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.OptionResult)return d;var m=new $root.pokermaster_proto.OptionResult;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"WIN_BEGIN":case 100:m.option=100;break;case"FISHER_WIN":case 101:m.option=101;break;case"SHARK_WIN":case 102:m.option=102;break;case"EQUAL":case 103:m.option=103;break;case"WIN_END":case 199:m.option=199;break;case"FIVE_BEGIN":case 300:m.option=300;break;case"FIVE_NONE_1DUI":case 301:m.option=301;break;case"FIVE_2DUI":case 302:m.option=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.option=303;break;case"FIVE_GOURD":case 304:m.option=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.option=305;break;case"FIVE_END":case 399:m.option=399;break}if(d.result!=null){m.result=d.result|0}if(d.loseHand!=null){m.loseHand=d.loseHand|0}return m};OptionResult.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;d.result=0;d.loseHand=0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.option]:m.option}if(m.result!=null&&m.hasOwnProperty("result")){d.result=m.result}if(m.loseHand!=null&&m.hasOwnProperty("loseHand")){d.loseHand=m.loseHand}return d};OptionResult.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OptionResult}();pokermaster_proto.OptionResults=function(){function OptionResults(p){this.results=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OptionResults.prototype.option=0;OptionResults.prototype.results=$util.emptyArray;OptionResults.prototype.loseHand=0;OptionResults.create=function create(properties){return new OptionResults(properties)};OptionResults.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.results!=null&&m.results.length){w.uint32(18).fork();for(var i=0;i<m.results.length;++i)w.int32(m.results[i]);w.ldelim()}if(m.loseHand!=null&&Object.hasOwnProperty.call(m,"loseHand"))w.uint32(24).int32(m.loseHand);return w};OptionResults.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OptionResults.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.OptionResults;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:if(!(m.results&&m.results.length))m.results=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.results.push(r.int32())}else m.results.push(r.int32());break;case 3:m.loseHand=r.int32();break;default:r.skipType(t&7);break}}return m};OptionResults.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OptionResults.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.results!=null&&m.hasOwnProperty("results")){if(!Array.isArray(m.results))return"results: array expected";for(var i=0;i<m.results.length;++i){if(!$util.isInteger(m.results[i]))return"results: integer[] expected"}}if(m.loseHand!=null&&m.hasOwnProperty("loseHand")){if(!$util.isInteger(m.loseHand))return"loseHand: integer expected"}return null};OptionResults.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.OptionResults)return d;var m=new $root.pokermaster_proto.OptionResults;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"WIN_BEGIN":case 100:m.option=100;break;case"FISHER_WIN":case 101:m.option=101;break;case"SHARK_WIN":case 102:m.option=102;break;case"EQUAL":case 103:m.option=103;break;case"WIN_END":case 199:m.option=199;break;case"FIVE_BEGIN":case 300:m.option=300;break;case"FIVE_NONE_1DUI":case 301:m.option=301;break;case"FIVE_2DUI":case 302:m.option=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.option=303;break;case"FIVE_GOURD":case 304:m.option=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.option=305;break;case"FIVE_END":case 399:m.option=399;break}if(d.results){if(!Array.isArray(d.results))throw TypeError(".pokermaster_proto.OptionResults.results: array expected");m.results=[];for(var i=0;i<d.results.length;++i){m.results[i]=d.results[i]|0}}if(d.loseHand!=null){m.loseHand=d.loseHand|0}return m};OptionResults.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.results=[]}if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;d.loseHand=0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.option]:m.option}if(m.results&&m.results.length){d.results=[];for(var j=0;j<m.results.length;++j){d.results[j]=m.results[j]}}if(m.loseHand!=null&&m.hasOwnProperty("loseHand")){d.loseHand=m.loseHand}return d};OptionResults.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OptionResults}();pokermaster_proto.HandLevel=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="HAND_DUMMY"]=0;values[valuesById[1]="HAND_NONE"]=1;values[valuesById[2]="HAND_DUI"]=2;values[valuesById[3]="HAND_DUI_TWO"]=3;values[valuesById[4]="HAND_SANJO"]=4;values[valuesById[5]="HAND_SHUN"]=5;values[valuesById[6]="HAND_TONG"]=6;values[valuesById[7]="HAND_HULU"]=7;values[valuesById[8]="HAND_SIJO"]=8;values[valuesById[9]="HAND_TONG_SHUN"]=9;values[valuesById[10]="HAND_KING"]=10;return values}();pokermaster_proto.RoundResult=function(){function RoundResult(p){this.Cards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoundResult.prototype.winOp=0;RoundResult.prototype.fisherLevel=0;RoundResult.prototype.sharkLevel=0;RoundResult.prototype.Cards=$util.emptyArray;RoundResult.create=function create(properties){return new RoundResult(properties)};RoundResult.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.winOp!=null&&Object.hasOwnProperty.call(m,"winOp"))w.uint32(8).int32(m.winOp);if(m.fisherLevel!=null&&Object.hasOwnProperty.call(m,"fisherLevel"))w.uint32(16).int32(m.fisherLevel);if(m.sharkLevel!=null&&Object.hasOwnProperty.call(m,"sharkLevel"))w.uint32(24).int32(m.sharkLevel);if(m.Cards!=null&&m.Cards.length){for(var i=0;i<m.Cards.length;++i)$root.pokermaster_proto.CardItem.encode(m.Cards[i],w.uint32(34).fork()).ldelim()}return w};RoundResult.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoundResult.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.RoundResult;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.winOp=r.int32();break;case 2:m.fisherLevel=r.int32();break;case 3:m.sharkLevel=r.int32();break;case 4:if(!(m.Cards&&m.Cards.length))m.Cards=[];m.Cards.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};RoundResult.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoundResult.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.winOp!=null&&m.hasOwnProperty("winOp")){switch(m.winOp){default:return"winOp: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.fisherLevel!=null&&m.hasOwnProperty("fisherLevel")){if(!$util.isInteger(m.fisherLevel))return"fisherLevel: integer expected"}if(m.sharkLevel!=null&&m.hasOwnProperty("sharkLevel")){if(!$util.isInteger(m.sharkLevel))return"sharkLevel: integer expected"}if(m.Cards!=null&&m.hasOwnProperty("Cards")){if(!Array.isArray(m.Cards))return"Cards: array expected";for(var i=0;i<m.Cards.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.Cards[i]);if(e)return"Cards."+e}}}return null};RoundResult.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.RoundResult)return d;var m=new $root.pokermaster_proto.RoundResult;switch(d.winOp){case"BetZoneOption_DUMMY":case 0:m.winOp=0;break;case"WIN_BEGIN":case 100:m.winOp=100;break;case"FISHER_WIN":case 101:m.winOp=101;break;case"SHARK_WIN":case 102:m.winOp=102;break;case"EQUAL":case 103:m.winOp=103;break;case"WIN_END":case 199:m.winOp=199;break;case"FIVE_BEGIN":case 300:m.winOp=300;break;case"FIVE_NONE_1DUI":case 301:m.winOp=301;break;case"FIVE_2DUI":case 302:m.winOp=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.winOp=303;break;case"FIVE_GOURD":case 304:m.winOp=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.winOp=305;break;case"FIVE_END":case 399:m.winOp=399;break}if(d.fisherLevel!=null){m.fisherLevel=d.fisherLevel|0}if(d.sharkLevel!=null){m.sharkLevel=d.sharkLevel|0}if(d.Cards){if(!Array.isArray(d.Cards))throw TypeError(".pokermaster_proto.RoundResult.Cards: array expected");m.Cards=[];for(var i=0;i<d.Cards.length;++i){if(typeof d.Cards[i]!=="object")throw TypeError(".pokermaster_proto.RoundResult.Cards: object expected");m.Cards[i]=$root.pokermaster_proto.CardItem.fromObject(d.Cards[i])}}return m};RoundResult.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.Cards=[]}if(o.defaults){d.winOp=o.enums===String?"BetZoneOption_DUMMY":0;d.fisherLevel=0;d.sharkLevel=0}if(m.winOp!=null&&m.hasOwnProperty("winOp")){d.winOp=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.winOp]:m.winOp}if(m.fisherLevel!=null&&m.hasOwnProperty("fisherLevel")){d.fisherLevel=m.fisherLevel}if(m.sharkLevel!=null&&m.hasOwnProperty("sharkLevel")){d.sharkLevel=m.sharkLevel}if(m.Cards&&m.Cards.length){d.Cards=[];for(var j=0;j<m.Cards.length;++j){d.Cards[j]=$root.pokermaster_proto.CardItem.toObject(m.Cards[j],o)}}return d};RoundResult.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoundResult}();pokermaster_proto.PlayerSettle=function(){function PlayerSettle(p){this.settle=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerSettle.prototype.uid=0;PlayerSettle.prototype.settle=$util.emptyArray;PlayerSettle.prototype.totalWinAmount=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerSettle.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerSettle.prototype.keepWinCount=0;PlayerSettle.create=function create(properties){return new PlayerSettle(properties)};PlayerSettle.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.settle!=null&&m.settle.length){for(var i=0;i<m.settle.length;++i)$root.pokermaster_proto.ZoneSettleDetail.encode(m.settle[i],w.uint32(18).fork()).ldelim()}if(m.totalWinAmount!=null&&Object.hasOwnProperty.call(m,"totalWinAmount"))w.uint32(24).int64(m.totalWinAmount);if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(32).int64(m.curCoin);if(m.keepWinCount!=null&&Object.hasOwnProperty.call(m,"keepWinCount"))w.uint32(40).int32(m.keepWinCount);return w};PlayerSettle.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerSettle.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.PlayerSettle;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:if(!(m.settle&&m.settle.length))m.settle=[];m.settle.push($root.pokermaster_proto.ZoneSettleDetail.decode(r,r.uint32()));break;case 3:m.totalWinAmount=r.int64();break;case 4:m.curCoin=r.int64();break;case 5:m.keepWinCount=r.int32();break;default:r.skipType(t&7);break}}return m};PlayerSettle.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerSettle.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.settle!=null&&m.hasOwnProperty("settle")){if(!Array.isArray(m.settle))return"settle: array expected";for(var i=0;i<m.settle.length;++i){{var e=$root.pokermaster_proto.ZoneSettleDetail.verify(m.settle[i]);if(e)return"settle."+e}}}if(m.totalWinAmount!=null&&m.hasOwnProperty("totalWinAmount")){if(!$util.isInteger(m.totalWinAmount)&&!(m.totalWinAmount&&$util.isInteger(m.totalWinAmount.low)&&$util.isInteger(m.totalWinAmount.high)))return"totalWinAmount: integer|Long expected"}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}if(m.keepWinCount!=null&&m.hasOwnProperty("keepWinCount")){if(!$util.isInteger(m.keepWinCount))return"keepWinCount: integer expected"}return null};PlayerSettle.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.PlayerSettle)return d;var m=new $root.pokermaster_proto.PlayerSettle;if(d.uid!=null){m.uid=d.uid>>>0}if(d.settle){if(!Array.isArray(d.settle))throw TypeError(".pokermaster_proto.PlayerSettle.settle: array expected");m.settle=[];for(var i=0;i<d.settle.length;++i){if(typeof d.settle[i]!=="object")throw TypeError(".pokermaster_proto.PlayerSettle.settle: object expected");m.settle[i]=$root.pokermaster_proto.ZoneSettleDetail.fromObject(d.settle[i])}}if(d.totalWinAmount!=null){if($util.Long)(m.totalWinAmount=$util.Long.fromValue(d.totalWinAmount)).unsigned=false;else if(typeof d.totalWinAmount==="string")m.totalWinAmount=parseInt(d.totalWinAmount,10);else if(typeof d.totalWinAmount==="number")m.totalWinAmount=d.totalWinAmount;else if(typeof d.totalWinAmount==="object")m.totalWinAmount=new $util.LongBits(d.totalWinAmount.low>>>0,d.totalWinAmount.high>>>0).toNumber()}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=false;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber()}if(d.keepWinCount!=null){m.keepWinCount=d.keepWinCount|0}return m};PlayerSettle.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.settle=[]}if(o.defaults){d.uid=0;if($util.Long){var n=new $util.Long(0,0,false);d.totalWinAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalWinAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0;d.keepWinCount=0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.settle&&m.settle.length){d.settle=[];for(var j=0;j<m.settle.length;++j){d.settle[j]=$root.pokermaster_proto.ZoneSettleDetail.toObject(m.settle[j],o)}}if(m.totalWinAmount!=null&&m.hasOwnProperty("totalWinAmount")){if(typeof m.totalWinAmount==="number")d.totalWinAmount=o.longs===String?String(m.totalWinAmount):m.totalWinAmount;else d.totalWinAmount=o.longs===String?$util.Long.prototype.toString.call(m.totalWinAmount):o.longs===Number?new $util.LongBits(m.totalWinAmount.low>>>0,m.totalWinAmount.high>>>0).toNumber():m.totalWinAmount}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber():m.curCoin}if(m.keepWinCount!=null&&m.hasOwnProperty("keepWinCount")){d.keepWinCount=m.keepWinCount}return d};PlayerSettle.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerSettle}();pokermaster_proto.ZoneSettleDetail=function(){function ZoneSettleDetail(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ZoneSettleDetail.prototype.option=0;ZoneSettleDetail.prototype.betAmount=$util.Long?$util.Long.fromBits(0,0,true):0;ZoneSettleDetail.prototype.winAmount=$util.Long?$util.Long.fromBits(0,0,false):0;ZoneSettleDetail.prototype.isAuto=0;ZoneSettleDetail.prototype.betGameCoin=$util.Long?$util.Long.fromBits(0,0,false):0;ZoneSettleDetail.create=function create(properties){return new ZoneSettleDetail(properties)};ZoneSettleDetail.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.betAmount!=null&&Object.hasOwnProperty.call(m,"betAmount"))w.uint32(16).uint64(m.betAmount);if(m.winAmount!=null&&Object.hasOwnProperty.call(m,"winAmount"))w.uint32(24).int64(m.winAmount);if(m.isAuto!=null&&Object.hasOwnProperty.call(m,"isAuto"))w.uint32(32).int32(m.isAuto);if(m.betGameCoin!=null&&Object.hasOwnProperty.call(m,"betGameCoin"))w.uint32(40).int64(m.betGameCoin);return w};ZoneSettleDetail.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ZoneSettleDetail.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.ZoneSettleDetail;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.betAmount=r.uint64();break;case 3:m.winAmount=r.int64();break;case 4:m.isAuto=r.int32();break;case 5:m.betGameCoin=r.int64();break;default:r.skipType(t&7);break}}return m};ZoneSettleDetail.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ZoneSettleDetail.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.betAmount!=null&&m.hasOwnProperty("betAmount")){if(!$util.isInteger(m.betAmount)&&!(m.betAmount&&$util.isInteger(m.betAmount.low)&&$util.isInteger(m.betAmount.high)))return"betAmount: integer|Long expected"}if(m.winAmount!=null&&m.hasOwnProperty("winAmount")){if(!$util.isInteger(m.winAmount)&&!(m.winAmount&&$util.isInteger(m.winAmount.low)&&$util.isInteger(m.winAmount.high)))return"winAmount: integer|Long expected"}if(m.isAuto!=null&&m.hasOwnProperty("isAuto")){if(!$util.isInteger(m.isAuto))return"isAuto: integer expected"}if(m.betGameCoin!=null&&m.hasOwnProperty("betGameCoin")){if(!$util.isInteger(m.betGameCoin)&&!(m.betGameCoin&&$util.isInteger(m.betGameCoin.low)&&$util.isInteger(m.betGameCoin.high)))return"betGameCoin: integer|Long expected"}return null};ZoneSettleDetail.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.ZoneSettleDetail)return d;var m=new $root.pokermaster_proto.ZoneSettleDetail;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"WIN_BEGIN":case 100:m.option=100;break;case"FISHER_WIN":case 101:m.option=101;break;case"SHARK_WIN":case 102:m.option=102;break;case"EQUAL":case 103:m.option=103;break;case"WIN_END":case 199:m.option=199;break;case"FIVE_BEGIN":case 300:m.option=300;break;case"FIVE_NONE_1DUI":case 301:m.option=301;break;case"FIVE_2DUI":case 302:m.option=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.option=303;break;case"FIVE_GOURD":case 304:m.option=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.option=305;break;case"FIVE_END":case 399:m.option=399;break}if(d.betAmount!=null){if($util.Long)(m.betAmount=$util.Long.fromValue(d.betAmount)).unsigned=true;else if(typeof d.betAmount==="string")m.betAmount=parseInt(d.betAmount,10);else if(typeof d.betAmount==="number")m.betAmount=d.betAmount;else if(typeof d.betAmount==="object")m.betAmount=new $util.LongBits(d.betAmount.low>>>0,d.betAmount.high>>>0).toNumber(true)}if(d.winAmount!=null){if($util.Long)(m.winAmount=$util.Long.fromValue(d.winAmount)).unsigned=false;else if(typeof d.winAmount==="string")m.winAmount=parseInt(d.winAmount,10);else if(typeof d.winAmount==="number")m.winAmount=d.winAmount;else if(typeof d.winAmount==="object")m.winAmount=new $util.LongBits(d.winAmount.low>>>0,d.winAmount.high>>>0).toNumber()}if(d.isAuto!=null){m.isAuto=d.isAuto|0}if(d.betGameCoin!=null){if($util.Long)(m.betGameCoin=$util.Long.fromValue(d.betGameCoin)).unsigned=false;else if(typeof d.betGameCoin==="string")m.betGameCoin=parseInt(d.betGameCoin,10);else if(typeof d.betGameCoin==="number")m.betGameCoin=d.betGameCoin;else if(typeof d.betGameCoin==="object")m.betGameCoin=new $util.LongBits(d.betGameCoin.low>>>0,d.betGameCoin.high>>>0).toNumber()}return m};ZoneSettleDetail.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.betAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.betAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.winAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.winAmount=o.longs===String?"0":0;d.isAuto=0;if($util.Long){var n=new $util.Long(0,0,false);d.betGameCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.betGameCoin=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.option]:m.option}if(m.betAmount!=null&&m.hasOwnProperty("betAmount")){if(typeof m.betAmount==="number")d.betAmount=o.longs===String?String(m.betAmount):m.betAmount;else d.betAmount=o.longs===String?$util.Long.prototype.toString.call(m.betAmount):o.longs===Number?new $util.LongBits(m.betAmount.low>>>0,m.betAmount.high>>>0).toNumber(true):m.betAmount}if(m.winAmount!=null&&m.hasOwnProperty("winAmount")){if(typeof m.winAmount==="number")d.winAmount=o.longs===String?String(m.winAmount):m.winAmount;else d.winAmount=o.longs===String?$util.Long.prototype.toString.call(m.winAmount):o.longs===Number?new $util.LongBits(m.winAmount.low>>>0,m.winAmount.high>>>0).toNumber():m.winAmount}if(m.isAuto!=null&&m.hasOwnProperty("isAuto")){d.isAuto=m.isAuto}if(m.betGameCoin!=null&&m.hasOwnProperty("betGameCoin")){if(typeof m.betGameCoin==="number")d.betGameCoin=o.longs===String?String(m.betGameCoin):m.betGameCoin;else d.betGameCoin=o.longs===String?$util.Long.prototype.toString.call(m.betGameCoin):o.longs===Number?new $util.LongBits(m.betGameCoin.low>>>0,m.betGameCoin.high>>>0).toNumber():m.betGameCoin}return d};ZoneSettleDetail.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ZoneSettleDetail}();pokermaster_proto.PlayerHoleCard=function(){function PlayerHoleCard(p){this.Cards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerHoleCard.prototype.name=0;PlayerHoleCard.prototype.Cards=$util.emptyArray;PlayerHoleCard.create=function create(properties){return new PlayerHoleCard(properties)};PlayerHoleCard.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.name!=null&&Object.hasOwnProperty.call(m,"name"))w.uint32(8).int32(m.name);if(m.Cards!=null&&m.Cards.length){for(var i=0;i<m.Cards.length;++i)$root.pokermaster_proto.CardItem.encode(m.Cards[i],w.uint32(18).fork()).ldelim()}return w};PlayerHoleCard.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerHoleCard.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.PlayerHoleCard;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.name=r.int32();break;case 2:if(!(m.Cards&&m.Cards.length))m.Cards=[];m.Cards.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};PlayerHoleCard.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerHoleCard.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.name!=null&&m.hasOwnProperty("name")){switch(m.name){default:return"name: enum value expected";case 0:case 1:case 2:break}}if(m.Cards!=null&&m.hasOwnProperty("Cards")){if(!Array.isArray(m.Cards))return"Cards: array expected";for(var i=0;i<m.Cards.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.Cards[i]);if(e)return"Cards."+e}}}return null};PlayerHoleCard.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.PlayerHoleCard)return d;var m=new $root.pokermaster_proto.PlayerHoleCard;switch(d.name){case"RoleName_DUMMY":case 0:m.name=0;break;case"Fisher":case 1:m.name=1;break;case"Shark":case 2:m.name=2;break}if(d.Cards){if(!Array.isArray(d.Cards))throw TypeError(".pokermaster_proto.PlayerHoleCard.Cards: array expected");m.Cards=[];for(var i=0;i<d.Cards.length;++i){if(typeof d.Cards[i]!=="object")throw TypeError(".pokermaster_proto.PlayerHoleCard.Cards: object expected");m.Cards[i]=$root.pokermaster_proto.CardItem.fromObject(d.Cards[i])}}return m};PlayerHoleCard.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.Cards=[]}if(o.defaults){d.name=o.enums===String?"RoleName_DUMMY":0}if(m.name!=null&&m.hasOwnProperty("name")){d.name=o.enums===String?$root.pokermaster_proto.RoleName[m.name]:m.name}if(m.Cards&&m.Cards.length){d.Cards=[];for(var j=0;j<m.Cards.length;++j){d.Cards[j]=$root.pokermaster_proto.CardItem.toObject(m.Cards[j],o)}}return d};PlayerHoleCard.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerHoleCard}();pokermaster_proto.PlayerFortune=function(){function PlayerFortune(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerFortune.prototype.fisherFortune=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerFortune.prototype.sharkFortune=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerFortune.prototype.whoWin=0;PlayerFortune.create=function create(properties){return new PlayerFortune(properties)};PlayerFortune.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.fisherFortune!=null&&Object.hasOwnProperty.call(m,"fisherFortune"))w.uint32(8).int64(m.fisherFortune);if(m.sharkFortune!=null&&Object.hasOwnProperty.call(m,"sharkFortune"))w.uint32(16).int64(m.sharkFortune);if(m.whoWin!=null&&Object.hasOwnProperty.call(m,"whoWin"))w.uint32(24).int32(m.whoWin);return w};PlayerFortune.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerFortune.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.PlayerFortune;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.fisherFortune=r.int64();break;case 2:m.sharkFortune=r.int64();break;case 3:m.whoWin=r.int32();break;default:r.skipType(t&7);break}}return m};PlayerFortune.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerFortune.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.fisherFortune!=null&&m.hasOwnProperty("fisherFortune")){if(!$util.isInteger(m.fisherFortune)&&!(m.fisherFortune&&$util.isInteger(m.fisherFortune.low)&&$util.isInteger(m.fisherFortune.high)))return"fisherFortune: integer|Long expected"}if(m.sharkFortune!=null&&m.hasOwnProperty("sharkFortune")){if(!$util.isInteger(m.sharkFortune)&&!(m.sharkFortune&&$util.isInteger(m.sharkFortune.low)&&$util.isInteger(m.sharkFortune.high)))return"sharkFortune: integer|Long expected"}if(m.whoWin!=null&&m.hasOwnProperty("whoWin")){switch(m.whoWin){default:return"whoWin: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}return null};PlayerFortune.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.PlayerFortune)return d;var m=new $root.pokermaster_proto.PlayerFortune;if(d.fisherFortune!=null){if($util.Long)(m.fisherFortune=$util.Long.fromValue(d.fisherFortune)).unsigned=false;else if(typeof d.fisherFortune==="string")m.fisherFortune=parseInt(d.fisherFortune,10);else if(typeof d.fisherFortune==="number")m.fisherFortune=d.fisherFortune;else if(typeof d.fisherFortune==="object")m.fisherFortune=new $util.LongBits(d.fisherFortune.low>>>0,d.fisherFortune.high>>>0).toNumber()}if(d.sharkFortune!=null){if($util.Long)(m.sharkFortune=$util.Long.fromValue(d.sharkFortune)).unsigned=false;else if(typeof d.sharkFortune==="string")m.sharkFortune=parseInt(d.sharkFortune,10);else if(typeof d.sharkFortune==="number")m.sharkFortune=d.sharkFortune;else if(typeof d.sharkFortune==="object")m.sharkFortune=new $util.LongBits(d.sharkFortune.low>>>0,d.sharkFortune.high>>>0).toNumber()}switch(d.whoWin){case"BetZoneOption_DUMMY":case 0:m.whoWin=0;break;case"WIN_BEGIN":case 100:m.whoWin=100;break;case"FISHER_WIN":case 101:m.whoWin=101;break;case"SHARK_WIN":case 102:m.whoWin=102;break;case"EQUAL":case 103:m.whoWin=103;break;case"WIN_END":case 199:m.whoWin=199;break;case"FIVE_BEGIN":case 300:m.whoWin=300;break;case"FIVE_NONE_1DUI":case 301:m.whoWin=301;break;case"FIVE_2DUI":case 302:m.whoWin=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.whoWin=303;break;case"FIVE_GOURD":case 304:m.whoWin=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.whoWin=305;break;case"FIVE_END":case 399:m.whoWin=399;break}return m};PlayerFortune.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.fisherFortune=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.fisherFortune=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.sharkFortune=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.sharkFortune=o.longs===String?"0":0;d.whoWin=o.enums===String?"BetZoneOption_DUMMY":0}if(m.fisherFortune!=null&&m.hasOwnProperty("fisherFortune")){if(typeof m.fisherFortune==="number")d.fisherFortune=o.longs===String?String(m.fisherFortune):m.fisherFortune;else d.fisherFortune=o.longs===String?$util.Long.prototype.toString.call(m.fisherFortune):o.longs===Number?new $util.LongBits(m.fisherFortune.low>>>0,m.fisherFortune.high>>>0).toNumber():m.fisherFortune}if(m.sharkFortune!=null&&m.hasOwnProperty("sharkFortune")){if(typeof m.sharkFortune==="number")d.sharkFortune=o.longs===String?String(m.sharkFortune):m.sharkFortune;else d.sharkFortune=o.longs===String?$util.Long.prototype.toString.call(m.sharkFortune):o.longs===Number?new $util.LongBits(m.sharkFortune.low>>>0,m.sharkFortune.high>>>0).toNumber():m.sharkFortune}if(m.whoWin!=null&&m.hasOwnProperty("whoWin")){d.whoWin=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.whoWin]:m.whoWin}return d};PlayerFortune.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerFortune}();pokermaster_proto.ConnClosed=function(){function ConnClosed(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ConnClosed.prototype.Reason=0;ConnClosed.create=function create(properties){return new ConnClosed(properties)};ConnClosed.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.Reason!=null&&Object.hasOwnProperty.call(m,"Reason"))w.uint32(8).int32(m.Reason);return w};ConnClosed.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ConnClosed.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.ConnClosed;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.Reason=r.int32();break;default:r.skipType(t&7);break}}return m};ConnClosed.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ConnClosed.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.Reason!=null&&m.hasOwnProperty("Reason")){if(!$util.isInteger(m.Reason))return"Reason: integer expected"}return null};ConnClosed.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.ConnClosed)return d;var m=new $root.pokermaster_proto.ConnClosed;if(d.Reason!=null){m.Reason=d.Reason|0}return m};ConnClosed.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.Reason=0}if(m.Reason!=null&&m.hasOwnProperty("Reason")){d.Reason=m.Reason}return d};ConnClosed.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ConnClosed}();pokermaster_proto.LeaveRoomReq=function(){function LeaveRoomReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LeaveRoomReq.create=function create(properties){return new LeaveRoomReq(properties)};LeaveRoomReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};LeaveRoomReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LeaveRoomReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.LeaveRoomReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};LeaveRoomReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LeaveRoomReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};LeaveRoomReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.LeaveRoomReq)return d;return new $root.pokermaster_proto.LeaveRoomReq};LeaveRoomReq.toObject=function toObject(){return{}};LeaveRoomReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LeaveRoomReq}();pokermaster_proto.LeaveRoomResp=function(){function LeaveRoomResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LeaveRoomResp.prototype.code=0;LeaveRoomResp.create=function create(properties){return new LeaveRoomResp(properties)};LeaveRoomResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};LeaveRoomResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LeaveRoomResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.LeaveRoomResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};LeaveRoomResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LeaveRoomResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}return null};LeaveRoomResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.LeaveRoomResp)return d;var m=new $root.pokermaster_proto.LeaveRoomResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};LeaveRoomResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}return d};LeaveRoomResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LeaveRoomResp}();pokermaster_proto.StartBetNotify=function(){function StartBetNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StartBetNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;StartBetNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;StartBetNotify.create=function create(properties){return new StartBetNotify(properties)};StartBetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(8).int64(m.nextRoundEndStamp);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(16).int64(m.leftSeconds);return w};StartBetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StartBetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.StartBetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.nextRoundEndStamp=r.int64();break;case 2:m.leftSeconds=r.int64();break;default:r.skipType(t&7);break}}return m};StartBetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StartBetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}return null};StartBetNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.StartBetNotify)return d;var m=new $root.pokermaster_proto.StartBetNotify;if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}return m};StartBetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}return d};StartBetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StartBetNotify}();pokermaster_proto.ShowOddsNotify=function(){function ShowOddsNotify(p){this.option_odds=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ShowOddsNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;ShowOddsNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;ShowOddsNotify.prototype.option_odds=$util.emptyArray;ShowOddsNotify.prototype.whoIsLeader=0;ShowOddsNotify.create=function create(properties){return new ShowOddsNotify(properties)};ShowOddsNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(8).int64(m.nextRoundEndStamp);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(16).int64(m.leftSeconds);if(m.option_odds!=null&&m.option_odds.length){for(var i=0;i<m.option_odds.length;++i)$root.pokermaster_proto.BetOptionsOdds.encode(m.option_odds[i],w.uint32(26).fork()).ldelim()}if(m.whoIsLeader!=null&&Object.hasOwnProperty.call(m,"whoIsLeader"))w.uint32(32).int32(m.whoIsLeader);return w};ShowOddsNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ShowOddsNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.ShowOddsNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.nextRoundEndStamp=r.int64();break;case 2:m.leftSeconds=r.int64();break;case 3:if(!(m.option_odds&&m.option_odds.length))m.option_odds=[];m.option_odds.push($root.pokermaster_proto.BetOptionsOdds.decode(r,r.uint32()));break;case 4:m.whoIsLeader=r.int32();break;default:r.skipType(t&7);break}}return m};ShowOddsNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ShowOddsNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.option_odds!=null&&m.hasOwnProperty("option_odds")){if(!Array.isArray(m.option_odds))return"option_odds: array expected";for(var i=0;i<m.option_odds.length;++i){{var e=$root.pokermaster_proto.BetOptionsOdds.verify(m.option_odds[i]);if(e)return"option_odds."+e}}}if(m.whoIsLeader!=null&&m.hasOwnProperty("whoIsLeader")){if(!$util.isInteger(m.whoIsLeader))return"whoIsLeader: integer expected"}return null};ShowOddsNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.ShowOddsNotify)return d;var m=new $root.pokermaster_proto.ShowOddsNotify;if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.option_odds){if(!Array.isArray(d.option_odds))throw TypeError(".pokermaster_proto.ShowOddsNotify.option_odds: array expected");m.option_odds=[];for(var i=0;i<d.option_odds.length;++i){if(typeof d.option_odds[i]!=="object")throw TypeError(".pokermaster_proto.ShowOddsNotify.option_odds: object expected");m.option_odds[i]=$root.pokermaster_proto.BetOptionsOdds.fromObject(d.option_odds[i])}}if(d.whoIsLeader!=null){m.whoIsLeader=d.whoIsLeader|0}return m};ShowOddsNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.option_odds=[]}if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;d.whoIsLeader=0}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.option_odds&&m.option_odds.length){d.option_odds=[];for(var j=0;j<m.option_odds.length;++j){d.option_odds[j]=$root.pokermaster_proto.BetOptionsOdds.toObject(m.option_odds[j],o)}}if(m.whoIsLeader!=null&&m.hasOwnProperty("whoIsLeader")){d.whoIsLeader=m.whoIsLeader}return d};ShowOddsNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ShowOddsNotify}();pokermaster_proto.TrendData=function(){function TrendData(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TrendData.prototype.win=0;TrendData.prototype.win_patterns=0;TrendData.prototype.hand_num=0;TrendData.create=function create(properties){return new TrendData(properties)};TrendData.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.win!=null&&Object.hasOwnProperty.call(m,"win"))w.uint32(8).int32(m.win);if(m.win_patterns!=null&&Object.hasOwnProperty.call(m,"win_patterns"))w.uint32(16).uint32(m.win_patterns);if(m.hand_num!=null&&Object.hasOwnProperty.call(m,"hand_num"))w.uint32(24).int32(m.hand_num);return w};TrendData.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TrendData.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.TrendData;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.win=r.int32();break;case 2:m.win_patterns=r.uint32();break;case 3:m.hand_num=r.int32();break;default:r.skipType(t&7);break}}return m};TrendData.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TrendData.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.win!=null&&m.hasOwnProperty("win")){switch(m.win){default:return"win: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.win_patterns!=null&&m.hasOwnProperty("win_patterns")){if(!$util.isInteger(m.win_patterns))return"win_patterns: integer expected"}if(m.hand_num!=null&&m.hasOwnProperty("hand_num")){if(!$util.isInteger(m.hand_num))return"hand_num: integer expected"}return null};TrendData.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.TrendData)return d;var m=new $root.pokermaster_proto.TrendData;switch(d.win){case"BetZoneOption_DUMMY":case 0:m.win=0;break;case"WIN_BEGIN":case 100:m.win=100;break;case"FISHER_WIN":case 101:m.win=101;break;case"SHARK_WIN":case 102:m.win=102;break;case"EQUAL":case 103:m.win=103;break;case"WIN_END":case 199:m.win=199;break;case"FIVE_BEGIN":case 300:m.win=300;break;case"FIVE_NONE_1DUI":case 301:m.win=301;break;case"FIVE_2DUI":case 302:m.win=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.win=303;break;case"FIVE_GOURD":case 304:m.win=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.win=305;break;case"FIVE_END":case 399:m.win=399;break}if(d.win_patterns!=null){m.win_patterns=d.win_patterns>>>0}if(d.hand_num!=null){m.hand_num=d.hand_num|0}return m};TrendData.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.win=o.enums===String?"BetZoneOption_DUMMY":0;d.win_patterns=0;d.hand_num=0}if(m.win!=null&&m.hasOwnProperty("win")){d.win=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.win]:m.win}if(m.win_patterns!=null&&m.hasOwnProperty("win_patterns")){d.win_patterns=m.win_patterns}if(m.hand_num!=null&&m.hasOwnProperty("hand_num")){d.hand_num=m.hand_num}return d};TrendData.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TrendData}();pokermaster_proto.RoomTrendRoadReq=function(){function RoomTrendRoadReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendRoadReq.prototype.roomuuid=$util.Long?$util.Long.fromBits(0,0,true):0;RoomTrendRoadReq.create=function create(properties){return new RoomTrendRoadReq(properties)};RoomTrendRoadReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomuuid!=null&&Object.hasOwnProperty.call(m,"roomuuid"))w.uint32(8).uint64(m.roomuuid);return w};RoomTrendRoadReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendRoadReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.RoomTrendRoadReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomuuid=r.uint64();break;default:r.skipType(t&7);break}}return m};RoomTrendRoadReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendRoadReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(!$util.isInteger(m.roomuuid)&&!(m.roomuuid&&$util.isInteger(m.roomuuid.low)&&$util.isInteger(m.roomuuid.high)))return"roomuuid: integer|Long expected"}return null};RoomTrendRoadReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.RoomTrendRoadReq)return d;var m=new $root.pokermaster_proto.RoomTrendRoadReq;if(d.roomuuid!=null){if($util.Long)(m.roomuuid=$util.Long.fromValue(d.roomuuid)).unsigned=true;else if(typeof d.roomuuid==="string")m.roomuuid=parseInt(d.roomuuid,10);else if(typeof d.roomuuid==="number")m.roomuuid=d.roomuuid;else if(typeof d.roomuuid==="object")m.roomuuid=new $util.LongBits(d.roomuuid.low>>>0,d.roomuuid.high>>>0).toNumber(true)}return m};RoomTrendRoadReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,true);d.roomuuid=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.roomuuid=o.longs===String?"0":0}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(typeof m.roomuuid==="number")d.roomuuid=o.longs===String?String(m.roomuuid):m.roomuuid;else d.roomuuid=o.longs===String?$util.Long.prototype.toString.call(m.roomuuid):o.longs===Number?new $util.LongBits(m.roomuuid.low>>>0,m.roomuuid.high>>>0).toNumber(true):m.roomuuid}return d};RoomTrendRoadReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendRoadReq}();pokermaster_proto.RoomTrendRoadRsp=function(){function RoomTrendRoadRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendRoadRsp.prototype.code=0;RoomTrendRoadRsp.create=function create(properties){return new RoomTrendRoadRsp(properties)};RoomTrendRoadRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};RoomTrendRoadRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendRoadRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.RoomTrendRoadRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};RoomTrendRoadRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendRoadRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}return null};RoomTrendRoadRsp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.RoomTrendRoadRsp)return d;var m=new $root.pokermaster_proto.RoomTrendRoadRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};RoomTrendRoadRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}return d};RoomTrendRoadRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendRoadRsp}();pokermaster_proto.RoomTrendRoadNotice=function(){function RoomTrendRoadNotice(p){this.road=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendRoadNotice.prototype.road=$util.emptyArray;RoomTrendRoadNotice.create=function create(properties){return new RoomTrendRoadNotice(properties)};RoomTrendRoadNotice.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.road!=null&&m.road.length){for(var i=0;i<m.road.length;++i)$root.pokermaster_proto.TrendRoad.encode(m.road[i],w.uint32(10).fork()).ldelim()}return w};RoomTrendRoadNotice.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendRoadNotice.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.RoomTrendRoadNotice;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.road&&m.road.length))m.road=[];m.road.push($root.pokermaster_proto.TrendRoad.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};RoomTrendRoadNotice.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendRoadNotice.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.road!=null&&m.hasOwnProperty("road")){if(!Array.isArray(m.road))return"road: array expected";for(var i=0;i<m.road.length;++i){{var e=$root.pokermaster_proto.TrendRoad.verify(m.road[i]);if(e)return"road."+e}}}return null};RoomTrendRoadNotice.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.RoomTrendRoadNotice)return d;var m=new $root.pokermaster_proto.RoomTrendRoadNotice;if(d.road){if(!Array.isArray(d.road))throw TypeError(".pokermaster_proto.RoomTrendRoadNotice.road: array expected");m.road=[];for(var i=0;i<d.road.length;++i){if(typeof d.road[i]!=="object")throw TypeError(".pokermaster_proto.RoomTrendRoadNotice.road: object expected");m.road[i]=$root.pokermaster_proto.TrendRoad.fromObject(d.road[i])}}return m};RoomTrendRoadNotice.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.road=[]}if(m.road&&m.road.length){d.road=[];for(var j=0;j<m.road.length;++j){d.road[j]=$root.pokermaster_proto.TrendRoad.toObject(m.road[j],o)}}return d};RoomTrendRoadNotice.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendRoadNotice}();pokermaster_proto.TrendRoad=function(){function TrendRoad(p){this.road_row=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TrendRoad.prototype.road_row=$util.emptyArray;TrendRoad.create=function create(properties){return new TrendRoad(properties)};TrendRoad.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.road_row!=null&&m.road_row.length){for(var i=0;i<m.road_row.length;++i)$root.pokermaster_proto.TrendRoadInfo.encode(m.road_row[i],w.uint32(10).fork()).ldelim()}return w};TrendRoad.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TrendRoad.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.TrendRoad;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.road_row&&m.road_row.length))m.road_row=[];m.road_row.push($root.pokermaster_proto.TrendRoadInfo.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};TrendRoad.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TrendRoad.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.road_row!=null&&m.hasOwnProperty("road_row")){if(!Array.isArray(m.road_row))return"road_row: array expected";for(var i=0;i<m.road_row.length;++i){{var e=$root.pokermaster_proto.TrendRoadInfo.verify(m.road_row[i]);if(e)return"road_row."+e}}}return null};TrendRoad.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.TrendRoad)return d;var m=new $root.pokermaster_proto.TrendRoad;if(d.road_row){if(!Array.isArray(d.road_row))throw TypeError(".pokermaster_proto.TrendRoad.road_row: array expected");m.road_row=[];for(var i=0;i<d.road_row.length;++i){if(typeof d.road_row[i]!=="object")throw TypeError(".pokermaster_proto.TrendRoad.road_row: object expected");m.road_row[i]=$root.pokermaster_proto.TrendRoadInfo.fromObject(d.road_row[i])}}return m};TrendRoad.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.road_row=[]}if(m.road_row&&m.road_row.length){d.road_row=[];for(var j=0;j<m.road_row.length;++j){d.road_row[j]=$root.pokermaster_proto.TrendRoadInfo.toObject(m.road_row[j],o)}}return d};TrendRoad.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TrendRoad}();pokermaster_proto.TrendRoadInfo=function(){function TrendRoadInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TrendRoadInfo.prototype.win="";TrendRoadInfo.prototype.eqc=0;TrendRoadInfo.create=function create(properties){return new TrendRoadInfo(properties)};TrendRoadInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.win!=null&&Object.hasOwnProperty.call(m,"win"))w.uint32(10).string(m.win);if(m.eqc!=null&&Object.hasOwnProperty.call(m,"eqc"))w.uint32(16).int32(m.eqc);return w};TrendRoadInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TrendRoadInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.TrendRoadInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.win=r.string();break;case 2:m.eqc=r.int32();break;default:r.skipType(t&7);break}}return m};TrendRoadInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TrendRoadInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.win!=null&&m.hasOwnProperty("win")){if(!$util.isString(m.win))return"win: string expected"}if(m.eqc!=null&&m.hasOwnProperty("eqc")){if(!$util.isInteger(m.eqc))return"eqc: integer expected"}return null};TrendRoadInfo.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.TrendRoadInfo)return d;var m=new $root.pokermaster_proto.TrendRoadInfo;if(d.win!=null){m.win=String(d.win)}if(d.eqc!=null){m.eqc=d.eqc|0}return m};TrendRoadInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.win="";d.eqc=0}if(m.win!=null&&m.hasOwnProperty("win")){d.win=m.win}if(m.eqc!=null&&m.hasOwnProperty("eqc")){d.eqc=m.eqc}return d};TrendRoadInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TrendRoadInfo}();pokermaster_proto.DailyStat=function(){function DailyStat(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DailyStat.prototype.betzone_type=0;DailyStat.prototype.count=0;DailyStat.prototype.win_pattern=0;DailyStat.create=function create(properties){return new DailyStat(properties)};DailyStat.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.betzone_type!=null&&Object.hasOwnProperty.call(m,"betzone_type"))w.uint32(8).int32(m.betzone_type);if(m.count!=null&&Object.hasOwnProperty.call(m,"count"))w.uint32(16).uint32(m.count);if(m.win_pattern!=null&&Object.hasOwnProperty.call(m,"win_pattern"))w.uint32(24).uint32(m.win_pattern);return w};DailyStat.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DailyStat.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.DailyStat;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.betzone_type=r.int32();break;case 2:m.count=r.uint32();break;case 3:m.win_pattern=r.uint32();break;default:r.skipType(t&7);break}}return m};DailyStat.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DailyStat.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.betzone_type!=null&&m.hasOwnProperty("betzone_type")){switch(m.betzone_type){default:return"betzone_type: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.count!=null&&m.hasOwnProperty("count")){if(!$util.isInteger(m.count))return"count: integer expected"}if(m.win_pattern!=null&&m.hasOwnProperty("win_pattern")){if(!$util.isInteger(m.win_pattern))return"win_pattern: integer expected"}return null};DailyStat.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.DailyStat)return d;var m=new $root.pokermaster_proto.DailyStat;switch(d.betzone_type){case"BetZoneOption_DUMMY":case 0:m.betzone_type=0;break;case"WIN_BEGIN":case 100:m.betzone_type=100;break;case"FISHER_WIN":case 101:m.betzone_type=101;break;case"SHARK_WIN":case 102:m.betzone_type=102;break;case"EQUAL":case 103:m.betzone_type=103;break;case"WIN_END":case 199:m.betzone_type=199;break;case"FIVE_BEGIN":case 300:m.betzone_type=300;break;case"FIVE_NONE_1DUI":case 301:m.betzone_type=301;break;case"FIVE_2DUI":case 302:m.betzone_type=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.betzone_type=303;break;case"FIVE_GOURD":case 304:m.betzone_type=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.betzone_type=305;break;case"FIVE_END":case 399:m.betzone_type=399;break}if(d.count!=null){m.count=d.count>>>0}if(d.win_pattern!=null){m.win_pattern=d.win_pattern>>>0}return m};DailyStat.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.betzone_type=o.enums===String?"BetZoneOption_DUMMY":0;d.count=0;d.win_pattern=0}if(m.betzone_type!=null&&m.hasOwnProperty("betzone_type")){d.betzone_type=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.betzone_type]:m.betzone_type}if(m.count!=null&&m.hasOwnProperty("count")){d.count=m.count}if(m.win_pattern!=null&&m.hasOwnProperty("win_pattern")){d.win_pattern=m.win_pattern}return d};DailyStat.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DailyStat}();pokermaster_proto.RoomTrendReq=function(){function RoomTrendReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendReq.prototype.roomuuid=$util.Long?$util.Long.fromBits(0,0,true):0;RoomTrendReq.create=function create(properties){return new RoomTrendReq(properties)};RoomTrendReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomuuid!=null&&Object.hasOwnProperty.call(m,"roomuuid"))w.uint32(8).uint64(m.roomuuid);return w};RoomTrendReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.RoomTrendReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomuuid=r.uint64();break;default:r.skipType(t&7);break}}return m};RoomTrendReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(!$util.isInteger(m.roomuuid)&&!(m.roomuuid&&$util.isInteger(m.roomuuid.low)&&$util.isInteger(m.roomuuid.high)))return"roomuuid: integer|Long expected"}return null};RoomTrendReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.RoomTrendReq)return d;var m=new $root.pokermaster_proto.RoomTrendReq;if(d.roomuuid!=null){if($util.Long)(m.roomuuid=$util.Long.fromValue(d.roomuuid)).unsigned=true;else if(typeof d.roomuuid==="string")m.roomuuid=parseInt(d.roomuuid,10);else if(typeof d.roomuuid==="number")m.roomuuid=d.roomuuid;else if(typeof d.roomuuid==="object")m.roomuuid=new $util.LongBits(d.roomuuid.low>>>0,d.roomuuid.high>>>0).toNumber(true)}return m};RoomTrendReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,true);d.roomuuid=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.roomuuid=o.longs===String?"0":0}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(typeof m.roomuuid==="number")d.roomuuid=o.longs===String?String(m.roomuuid):m.roomuuid;else d.roomuuid=o.longs===String?$util.Long.prototype.toString.call(m.roomuuid):o.longs===Number?new $util.LongBits(m.roomuuid.low>>>0,m.roomuuid.high>>>0).toNumber(true):m.roomuuid}return d};RoomTrendReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendReq}();pokermaster_proto.RoomTrendRsp=function(){function RoomTrendRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendRsp.prototype.code=0;RoomTrendRsp.create=function create(properties){return new RoomTrendRsp(properties)};RoomTrendRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};RoomTrendRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.RoomTrendRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};RoomTrendRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}return null};RoomTrendRsp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.RoomTrendRsp)return d;var m=new $root.pokermaster_proto.RoomTrendRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};RoomTrendRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}return d};RoomTrendRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendRsp}();pokermaster_proto.RoomTrendNotice=function(){function RoomTrendNotice(p){this.trend=[];this.road=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendNotice.prototype.roomuuid=$util.Long?$util.Long.fromBits(0,0,true):0;RoomTrendNotice.prototype.trend=$util.emptyArray;RoomTrendNotice.prototype.fortune=null;RoomTrendNotice.prototype.road=$util.emptyArray;RoomTrendNotice.prototype.lastRow=0;RoomTrendNotice.prototype.lastCol=0;RoomTrendNotice.create=function create(properties){return new RoomTrendNotice(properties)};RoomTrendNotice.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomuuid!=null&&Object.hasOwnProperty.call(m,"roomuuid"))w.uint32(8).uint64(m.roomuuid);if(m.trend!=null&&m.trend.length){for(var i=0;i<m.trend.length;++i)$root.pokermaster_proto.TrendData.encode(m.trend[i],w.uint32(18).fork()).ldelim()}if(m.fortune!=null&&Object.hasOwnProperty.call(m,"fortune"))$root.pokermaster_proto.PlayerFortune.encode(m.fortune,w.uint32(26).fork()).ldelim();if(m.road!=null&&m.road.length){for(var i=0;i<m.road.length;++i)$root.pokermaster_proto.TrendRoad.encode(m.road[i],w.uint32(34).fork()).ldelim()}if(m.lastRow!=null&&Object.hasOwnProperty.call(m,"lastRow"))w.uint32(40).int32(m.lastRow);if(m.lastCol!=null&&Object.hasOwnProperty.call(m,"lastCol"))w.uint32(48).int32(m.lastCol);return w};RoomTrendNotice.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendNotice.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.RoomTrendNotice;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomuuid=r.uint64();break;case 2:if(!(m.trend&&m.trend.length))m.trend=[];m.trend.push($root.pokermaster_proto.TrendData.decode(r,r.uint32()));break;case 3:m.fortune=$root.pokermaster_proto.PlayerFortune.decode(r,r.uint32());break;case 4:if(!(m.road&&m.road.length))m.road=[];m.road.push($root.pokermaster_proto.TrendRoad.decode(r,r.uint32()));break;case 5:m.lastRow=r.int32();break;case 6:m.lastCol=r.int32();break;default:r.skipType(t&7);break}}return m};RoomTrendNotice.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendNotice.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(!$util.isInteger(m.roomuuid)&&!(m.roomuuid&&$util.isInteger(m.roomuuid.low)&&$util.isInteger(m.roomuuid.high)))return"roomuuid: integer|Long expected"}if(m.trend!=null&&m.hasOwnProperty("trend")){if(!Array.isArray(m.trend))return"trend: array expected";for(var i=0;i<m.trend.length;++i){{var e=$root.pokermaster_proto.TrendData.verify(m.trend[i]);if(e)return"trend."+e}}}if(m.fortune!=null&&m.hasOwnProperty("fortune")){{var e=$root.pokermaster_proto.PlayerFortune.verify(m.fortune);if(e)return"fortune."+e}}if(m.road!=null&&m.hasOwnProperty("road")){if(!Array.isArray(m.road))return"road: array expected";for(var i=0;i<m.road.length;++i){{var e=$root.pokermaster_proto.TrendRoad.verify(m.road[i]);if(e)return"road."+e}}}if(m.lastRow!=null&&m.hasOwnProperty("lastRow")){if(!$util.isInteger(m.lastRow))return"lastRow: integer expected"}if(m.lastCol!=null&&m.hasOwnProperty("lastCol")){if(!$util.isInteger(m.lastCol))return"lastCol: integer expected"}return null};RoomTrendNotice.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.RoomTrendNotice)return d;var m=new $root.pokermaster_proto.RoomTrendNotice;if(d.roomuuid!=null){if($util.Long)(m.roomuuid=$util.Long.fromValue(d.roomuuid)).unsigned=true;else if(typeof d.roomuuid==="string")m.roomuuid=parseInt(d.roomuuid,10);else if(typeof d.roomuuid==="number")m.roomuuid=d.roomuuid;else if(typeof d.roomuuid==="object")m.roomuuid=new $util.LongBits(d.roomuuid.low>>>0,d.roomuuid.high>>>0).toNumber(true)}if(d.trend){if(!Array.isArray(d.trend))throw TypeError(".pokermaster_proto.RoomTrendNotice.trend: array expected");m.trend=[];for(var i=0;i<d.trend.length;++i){if(typeof d.trend[i]!=="object")throw TypeError(".pokermaster_proto.RoomTrendNotice.trend: object expected");m.trend[i]=$root.pokermaster_proto.TrendData.fromObject(d.trend[i])}}if(d.fortune!=null){if(typeof d.fortune!=="object")throw TypeError(".pokermaster_proto.RoomTrendNotice.fortune: object expected");m.fortune=$root.pokermaster_proto.PlayerFortune.fromObject(d.fortune)}if(d.road){if(!Array.isArray(d.road))throw TypeError(".pokermaster_proto.RoomTrendNotice.road: array expected");m.road=[];for(var i=0;i<d.road.length;++i){if(typeof d.road[i]!=="object")throw TypeError(".pokermaster_proto.RoomTrendNotice.road: object expected");m.road[i]=$root.pokermaster_proto.TrendRoad.fromObject(d.road[i])}}if(d.lastRow!=null){m.lastRow=d.lastRow|0}if(d.lastCol!=null){m.lastCol=d.lastCol|0}return m};RoomTrendNotice.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.trend=[];d.road=[]}if(o.defaults){if($util.Long){var n=new $util.Long(0,0,true);d.roomuuid=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.roomuuid=o.longs===String?"0":0;d.fortune=null;d.lastRow=0;d.lastCol=0}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(typeof m.roomuuid==="number")d.roomuuid=o.longs===String?String(m.roomuuid):m.roomuuid;else d.roomuuid=o.longs===String?$util.Long.prototype.toString.call(m.roomuuid):o.longs===Number?new $util.LongBits(m.roomuuid.low>>>0,m.roomuuid.high>>>0).toNumber(true):m.roomuuid}if(m.trend&&m.trend.length){d.trend=[];for(var j=0;j<m.trend.length;++j){d.trend[j]=$root.pokermaster_proto.TrendData.toObject(m.trend[j],o)}}if(m.fortune!=null&&m.hasOwnProperty("fortune")){d.fortune=$root.pokermaster_proto.PlayerFortune.toObject(m.fortune,o)}if(m.road&&m.road.length){d.road=[];for(var j=0;j<m.road.length;++j){d.road[j]=$root.pokermaster_proto.TrendRoad.toObject(m.road[j],o)}}if(m.lastRow!=null&&m.hasOwnProperty("lastRow")){d.lastRow=m.lastRow}if(m.lastCol!=null&&m.hasOwnProperty("lastCol")){d.lastCol=m.lastCol}return d};RoomTrendNotice.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendNotice}();pokermaster_proto.AutoBetReq=function(){function AutoBetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AutoBetReq.create=function create(properties){return new AutoBetReq(properties)};AutoBetReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};AutoBetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AutoBetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AutoBetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};AutoBetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AutoBetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};AutoBetReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AutoBetReq)return d;return new $root.pokermaster_proto.AutoBetReq};AutoBetReq.toObject=function toObject(){return{}};AutoBetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AutoBetReq}();pokermaster_proto.AutoBetResp=function(){function AutoBetResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AutoBetResp.prototype.code=0;AutoBetResp.prototype.canAuto=false;AutoBetResp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;AutoBetResp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;AutoBetResp.prototype.bill=null;AutoBetResp.create=function create(properties){return new AutoBetResp(properties)};AutoBetResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.canAuto!=null&&Object.hasOwnProperty.call(m,"canAuto"))w.uint32(16).bool(m.canAuto);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);if(m.bill!=null&&Object.hasOwnProperty.call(m,"bill"))$root.pokermaster_proto.BillInfo.encode(m.bill,w.uint32(42).fork()).ldelim();return w};AutoBetResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AutoBetResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AutoBetResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.canAuto=r.bool();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;case 5:m.bill=$root.pokermaster_proto.BillInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};AutoBetResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AutoBetResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){if(typeof m.canAuto!=="boolean")return"canAuto: boolean expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}if(m.bill!=null&&m.hasOwnProperty("bill")){{var e=$root.pokermaster_proto.BillInfo.verify(m.bill);if(e)return"bill."+e}}return null};AutoBetResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AutoBetResp)return d;var m=new $root.pokermaster_proto.AutoBetResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.canAuto!=null){m.canAuto=Boolean(d.canAuto)}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}if(d.bill!=null){if(typeof d.bill!=="object")throw TypeError(".pokermaster_proto.AutoBetResp.bill: object expected");m.bill=$root.pokermaster_proto.BillInfo.fromObject(d.bill)}return m};AutoBetResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.canAuto=false;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0;d.bill=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){d.canAuto=m.canAuto}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}if(m.bill!=null&&m.hasOwnProperty("bill")){d.bill=$root.pokermaster_proto.BillInfo.toObject(m.bill,o)}return d};AutoBetResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AutoBetResp}();pokermaster_proto.AutoBetNotify=function(){function AutoBetNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AutoBetNotify.prototype.open=false;AutoBetNotify.create=function create(properties){return new AutoBetNotify(properties)};AutoBetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.open!=null&&Object.hasOwnProperty.call(m,"open"))w.uint32(16).bool(m.open);return w};AutoBetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AutoBetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AutoBetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 2:m.open=r.bool();break;default:r.skipType(t&7);break}}return m};AutoBetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AutoBetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.open!=null&&m.hasOwnProperty("open")){if(typeof m.open!=="boolean")return"open: boolean expected"}return null};AutoBetNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AutoBetNotify)return d;var m=new $root.pokermaster_proto.AutoBetNotify;if(d.open!=null){m.open=Boolean(d.open)}return m};AutoBetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.open=false}if(m.open!=null&&m.hasOwnProperty("open")){d.open=m.open}return d};AutoBetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AutoBetNotify}();pokermaster_proto.PlayerListReq=function(){function PlayerListReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerListReq.create=function create(properties){return new PlayerListReq(properties)};PlayerListReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};PlayerListReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerListReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.PlayerListReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};PlayerListReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerListReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};PlayerListReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.PlayerListReq)return d;return new $root.pokermaster_proto.PlayerListReq};PlayerListReq.toObject=function toObject(){return{}};PlayerListReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerListReq}();pokermaster_proto.PlayerListResp=function(){function PlayerListResp(p){this.gamePlayers=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerListResp.prototype.code=0;PlayerListResp.prototype.gamePlayers=$util.emptyArray;PlayerListResp.prototype.self=null;PlayerListResp.prototype.playerNum=0;PlayerListResp.create=function create(properties){return new PlayerListResp(properties)};PlayerListResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.gamePlayers!=null&&m.gamePlayers.length){for(var i=0;i<m.gamePlayers.length;++i)$root.pokermaster_proto.GamePlayer.encode(m.gamePlayers[i],w.uint32(18).fork()).ldelim()}if(m.self!=null&&Object.hasOwnProperty.call(m,"self"))$root.pokermaster_proto.GamePlayer.encode(m.self,w.uint32(26).fork()).ldelim();if(m.playerNum!=null&&Object.hasOwnProperty.call(m,"playerNum"))w.uint32(40).uint32(m.playerNum);return w};PlayerListResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerListResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.PlayerListResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:if(!(m.gamePlayers&&m.gamePlayers.length))m.gamePlayers=[];m.gamePlayers.push($root.pokermaster_proto.GamePlayer.decode(r,r.uint32()));break;case 3:m.self=$root.pokermaster_proto.GamePlayer.decode(r,r.uint32());break;case 5:m.playerNum=r.uint32();break;default:r.skipType(t&7);break}}return m};PlayerListResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerListResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.gamePlayers!=null&&m.hasOwnProperty("gamePlayers")){if(!Array.isArray(m.gamePlayers))return"gamePlayers: array expected";for(var i=0;i<m.gamePlayers.length;++i){{var e=$root.pokermaster_proto.GamePlayer.verify(m.gamePlayers[i]);if(e)return"gamePlayers."+e}}}if(m.self!=null&&m.hasOwnProperty("self")){{var e=$root.pokermaster_proto.GamePlayer.verify(m.self);if(e)return"self."+e}}if(m.playerNum!=null&&m.hasOwnProperty("playerNum")){if(!$util.isInteger(m.playerNum))return"playerNum: integer expected"}return null};PlayerListResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.PlayerListResp)return d;var m=new $root.pokermaster_proto.PlayerListResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.gamePlayers){if(!Array.isArray(d.gamePlayers))throw TypeError(".pokermaster_proto.PlayerListResp.gamePlayers: array expected");m.gamePlayers=[];for(var i=0;i<d.gamePlayers.length;++i){if(typeof d.gamePlayers[i]!=="object")throw TypeError(".pokermaster_proto.PlayerListResp.gamePlayers: object expected");m.gamePlayers[i]=$root.pokermaster_proto.GamePlayer.fromObject(d.gamePlayers[i])}}if(d.self!=null){if(typeof d.self!=="object")throw TypeError(".pokermaster_proto.PlayerListResp.self: object expected");m.self=$root.pokermaster_proto.GamePlayer.fromObject(d.self)}if(d.playerNum!=null){m.playerNum=d.playerNum>>>0}return m};PlayerListResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.gamePlayers=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.self=null;d.playerNum=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.gamePlayers&&m.gamePlayers.length){d.gamePlayers=[];for(var j=0;j<m.gamePlayers.length;++j){d.gamePlayers[j]=$root.pokermaster_proto.GamePlayer.toObject(m.gamePlayers[j],o)}}if(m.self!=null&&m.hasOwnProperty("self")){d.self=$root.pokermaster_proto.GamePlayer.toObject(m.self,o)}if(m.playerNum!=null&&m.hasOwnProperty("playerNum")){d.playerNum=m.playerNum}return d};PlayerListResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerListResp}();pokermaster_proto.GamePlayer=function(){function GamePlayer(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GamePlayer.prototype.uid=0;GamePlayer.prototype.name="";GamePlayer.prototype.head="";GamePlayer.prototype.totalBetAmount=$util.Long?$util.Long.fromBits(0,0,true):0;GamePlayer.prototype.winCount=0;GamePlayer.prototype.rank=0;GamePlayer.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,true):0;GamePlayer.prototype.keepWinCount=0;GamePlayer.prototype.curUsdt=$util.Long?$util.Long.fromBits(0,0,true):0;GamePlayer.prototype.plat=0;GamePlayer.create=function create(properties){return new GamePlayer(properties)};GamePlayer.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.name!=null&&Object.hasOwnProperty.call(m,"name"))w.uint32(18).string(m.name);if(m.head!=null&&Object.hasOwnProperty.call(m,"head"))w.uint32(26).string(m.head);if(m.totalBetAmount!=null&&Object.hasOwnProperty.call(m,"totalBetAmount"))w.uint32(32).uint64(m.totalBetAmount);if(m.winCount!=null&&Object.hasOwnProperty.call(m,"winCount"))w.uint32(40).uint32(m.winCount);if(m.rank!=null&&Object.hasOwnProperty.call(m,"rank"))w.uint32(48).uint32(m.rank);if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(56).uint64(m.curCoin);if(m.keepWinCount!=null&&Object.hasOwnProperty.call(m,"keepWinCount"))w.uint32(64).int32(m.keepWinCount);if(m.curUsdt!=null&&Object.hasOwnProperty.call(m,"curUsdt"))w.uint32(72).uint64(m.curUsdt);if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(80).uint32(m.plat);return w};GamePlayer.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GamePlayer.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.GamePlayer;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.name=r.string();break;case 3:m.head=r.string();break;case 4:m.totalBetAmount=r.uint64();break;case 5:m.winCount=r.uint32();break;case 6:m.rank=r.uint32();break;case 7:m.curCoin=r.uint64();break;case 8:m.keepWinCount=r.int32();break;case 9:m.curUsdt=r.uint64();break;case 10:m.plat=r.uint32();break;default:r.skipType(t&7);break}}return m};GamePlayer.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GamePlayer.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.name!=null&&m.hasOwnProperty("name")){if(!$util.isString(m.name))return"name: string expected"}if(m.head!=null&&m.hasOwnProperty("head")){if(!$util.isString(m.head))return"head: string expected"}if(m.totalBetAmount!=null&&m.hasOwnProperty("totalBetAmount")){if(!$util.isInteger(m.totalBetAmount)&&!(m.totalBetAmount&&$util.isInteger(m.totalBetAmount.low)&&$util.isInteger(m.totalBetAmount.high)))return"totalBetAmount: integer|Long expected"}if(m.winCount!=null&&m.hasOwnProperty("winCount")){if(!$util.isInteger(m.winCount))return"winCount: integer expected"}if(m.rank!=null&&m.hasOwnProperty("rank")){if(!$util.isInteger(m.rank))return"rank: integer expected"}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}if(m.keepWinCount!=null&&m.hasOwnProperty("keepWinCount")){if(!$util.isInteger(m.keepWinCount))return"keepWinCount: integer expected"}if(m.curUsdt!=null&&m.hasOwnProperty("curUsdt")){if(!$util.isInteger(m.curUsdt)&&!(m.curUsdt&&$util.isInteger(m.curUsdt.low)&&$util.isInteger(m.curUsdt.high)))return"curUsdt: integer|Long expected"}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}return null};GamePlayer.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.GamePlayer)return d;var m=new $root.pokermaster_proto.GamePlayer;if(d.uid!=null){m.uid=d.uid>>>0}if(d.name!=null){m.name=String(d.name)}if(d.head!=null){m.head=String(d.head)}if(d.totalBetAmount!=null){if($util.Long)(m.totalBetAmount=$util.Long.fromValue(d.totalBetAmount)).unsigned=true;else if(typeof d.totalBetAmount==="string")m.totalBetAmount=parseInt(d.totalBetAmount,10);else if(typeof d.totalBetAmount==="number")m.totalBetAmount=d.totalBetAmount;else if(typeof d.totalBetAmount==="object")m.totalBetAmount=new $util.LongBits(d.totalBetAmount.low>>>0,d.totalBetAmount.high>>>0).toNumber(true)}if(d.winCount!=null){m.winCount=d.winCount>>>0}if(d.rank!=null){m.rank=d.rank>>>0}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=true;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber(true)}if(d.keepWinCount!=null){m.keepWinCount=d.keepWinCount|0}if(d.curUsdt!=null){if($util.Long)(m.curUsdt=$util.Long.fromValue(d.curUsdt)).unsigned=true;else if(typeof d.curUsdt==="string")m.curUsdt=parseInt(d.curUsdt,10);else if(typeof d.curUsdt==="number")m.curUsdt=d.curUsdt;else if(typeof d.curUsdt==="object")m.curUsdt=new $util.LongBits(d.curUsdt.low>>>0,d.curUsdt.high>>>0).toNumber(true)}if(d.plat!=null){m.plat=d.plat>>>0}return m};GamePlayer.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;d.name="";d.head="";if($util.Long){var n=new $util.Long(0,0,true);d.totalBetAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBetAmount=o.longs===String?"0":0;d.winCount=0;d.rank=0;if($util.Long){var n=new $util.Long(0,0,true);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0;d.keepWinCount=0;if($util.Long){var n=new $util.Long(0,0,true);d.curUsdt=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curUsdt=o.longs===String?"0":0;d.plat=0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.name!=null&&m.hasOwnProperty("name")){d.name=m.name}if(m.head!=null&&m.hasOwnProperty("head")){d.head=m.head}if(m.totalBetAmount!=null&&m.hasOwnProperty("totalBetAmount")){if(typeof m.totalBetAmount==="number")d.totalBetAmount=o.longs===String?String(m.totalBetAmount):m.totalBetAmount;else d.totalBetAmount=o.longs===String?$util.Long.prototype.toString.call(m.totalBetAmount):o.longs===Number?new $util.LongBits(m.totalBetAmount.low>>>0,m.totalBetAmount.high>>>0).toNumber(true):m.totalBetAmount}if(m.winCount!=null&&m.hasOwnProperty("winCount")){d.winCount=m.winCount}if(m.rank!=null&&m.hasOwnProperty("rank")){d.rank=m.rank}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber(true):m.curCoin}if(m.keepWinCount!=null&&m.hasOwnProperty("keepWinCount")){d.keepWinCount=m.keepWinCount}if(m.curUsdt!=null&&m.hasOwnProperty("curUsdt")){if(typeof m.curUsdt==="number")d.curUsdt=o.longs===String?String(m.curUsdt):m.curUsdt;else d.curUsdt=o.longs===String?$util.Long.prototype.toString.call(m.curUsdt):o.longs===Number?new $util.LongBits(m.curUsdt.low>>>0,m.curUsdt.high>>>0).toNumber(true):m.curUsdt}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}return d};GamePlayer.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GamePlayer}();pokermaster_proto.KickNotify=function(){function KickNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}KickNotify.prototype.kickType=0;KickNotify.prototype.desc="";KickNotify.prototype.idle_roomid=0;KickNotify.create=function create(properties){return new KickNotify(properties)};KickNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.kickType!=null&&Object.hasOwnProperty.call(m,"kickType"))w.uint32(8).int32(m.kickType);if(m.desc!=null&&Object.hasOwnProperty.call(m,"desc"))w.uint32(18).string(m.desc);if(m.idle_roomid!=null&&Object.hasOwnProperty.call(m,"idle_roomid"))w.uint32(24).uint32(m.idle_roomid);return w};KickNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};KickNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.KickNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.kickType=r.int32();break;case 2:m.desc=r.string();break;case 3:m.idle_roomid=r.uint32();break;default:r.skipType(t&7);break}}return m};KickNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};KickNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.kickType!=null&&m.hasOwnProperty("kickType")){switch(m.kickType){default:return"kickType: enum value expected";case 0:case 1:case 2:break}}if(m.desc!=null&&m.hasOwnProperty("desc")){if(!$util.isString(m.desc))return"desc: string expected"}if(m.idle_roomid!=null&&m.hasOwnProperty("idle_roomid")){if(!$util.isInteger(m.idle_roomid))return"idle_roomid: integer expected"}return null};KickNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.KickNotify)return d;var m=new $root.pokermaster_proto.KickNotify;switch(d.kickType){case"Kick_DUMMY":case 0:m.kickType=0;break;case"IDLE_LONG_TIME":case 1:m.kickType=1;break;case"Stop_World":case 2:m.kickType=2;break}if(d.desc!=null){m.desc=String(d.desc)}if(d.idle_roomid!=null){m.idle_roomid=d.idle_roomid>>>0}return m};KickNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.kickType=o.enums===String?"Kick_DUMMY":0;d.desc="";d.idle_roomid=0}if(m.kickType!=null&&m.hasOwnProperty("kickType")){d.kickType=o.enums===String?$root.pokermaster_proto.Kick[m.kickType]:m.kickType}if(m.desc!=null&&m.hasOwnProperty("desc")){d.desc=m.desc}if(m.idle_roomid!=null&&m.hasOwnProperty("idle_roomid")){d.idle_roomid=m.idle_roomid}return d};KickNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return KickNotify}();pokermaster_proto.AutoOpenRoadsReq=function(){function AutoOpenRoadsReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AutoOpenRoadsReq.prototype.open=false;AutoOpenRoadsReq.create=function create(properties){return new AutoOpenRoadsReq(properties)};AutoOpenRoadsReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.open!=null&&Object.hasOwnProperty.call(m,"open"))w.uint32(8).bool(m.open);return w};AutoOpenRoadsReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AutoOpenRoadsReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AutoOpenRoadsReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.open=r.bool();break;default:r.skipType(t&7);break}}return m};AutoOpenRoadsReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AutoOpenRoadsReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.open!=null&&m.hasOwnProperty("open")){if(typeof m.open!=="boolean")return"open: boolean expected"}return null};AutoOpenRoadsReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AutoOpenRoadsReq)return d;var m=new $root.pokermaster_proto.AutoOpenRoadsReq;if(d.open!=null){m.open=Boolean(d.open)}return m};AutoOpenRoadsReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.open=false}if(m.open!=null&&m.hasOwnProperty("open")){d.open=m.open}return d};AutoOpenRoadsReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AutoOpenRoadsReq}();pokermaster_proto.AutoOpenRoadsResp=function(){function AutoOpenRoadsResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AutoOpenRoadsResp.prototype.code=0;AutoOpenRoadsResp.prototype.open=false;AutoOpenRoadsResp.create=function create(properties){return new AutoOpenRoadsResp(properties)};AutoOpenRoadsResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.open!=null&&Object.hasOwnProperty.call(m,"open"))w.uint32(16).bool(m.open);return w};AutoOpenRoadsResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AutoOpenRoadsResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AutoOpenRoadsResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.open=r.bool();break;default:r.skipType(t&7);break}}return m};AutoOpenRoadsResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AutoOpenRoadsResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.open!=null&&m.hasOwnProperty("open")){if(typeof m.open!=="boolean")return"open: boolean expected"}return null};AutoOpenRoadsResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AutoOpenRoadsResp)return d;var m=new $root.pokermaster_proto.AutoOpenRoadsResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.open!=null){m.open=Boolean(d.open)}return m};AutoOpenRoadsResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.open=false}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.open!=null&&m.hasOwnProperty("open")){d.open=m.open}return d};AutoOpenRoadsResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AutoOpenRoadsResp}();pokermaster_proto.SetGameOptionReq=function(){function SetGameOptionReq(p){this.betCoinOption=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SetGameOptionReq.prototype.autoLevel=0;SetGameOptionReq.prototype.betCoinOption=$util.emptyArray;SetGameOptionReq.create=function create(properties){return new SetGameOptionReq(properties)};SetGameOptionReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.autoLevel!=null&&Object.hasOwnProperty.call(m,"autoLevel"))w.uint32(8).int32(m.autoLevel);if(m.betCoinOption!=null&&m.betCoinOption.length){w.uint32(18).fork();for(var i=0;i<m.betCoinOption.length;++i)w.uint64(m.betCoinOption[i]);w.ldelim()}return w};SetGameOptionReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SetGameOptionReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.SetGameOptionReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.autoLevel=r.int32();break;case 2:if(!(m.betCoinOption&&m.betCoinOption.length))m.betCoinOption=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.betCoinOption.push(r.uint64())}else m.betCoinOption.push(r.uint64());break;default:r.skipType(t&7);break}}return m};SetGameOptionReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SetGameOptionReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){switch(m.autoLevel){default:return"autoLevel: enum value expected";case 0:case 1:break}}if(m.betCoinOption!=null&&m.hasOwnProperty("betCoinOption")){if(!Array.isArray(m.betCoinOption))return"betCoinOption: array expected";for(var i=0;i<m.betCoinOption.length;++i){if(!$util.isInteger(m.betCoinOption[i])&&!(m.betCoinOption[i]&&$util.isInteger(m.betCoinOption[i].low)&&$util.isInteger(m.betCoinOption[i].high)))return"betCoinOption: integer|Long[] expected"}}return null};SetGameOptionReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.SetGameOptionReq)return d;var m=new $root.pokermaster_proto.SetGameOptionReq;switch(d.autoLevel){case"Level_Normal":case 0:m.autoLevel=0;break;case"Level_Advance":case 1:m.autoLevel=1;break}if(d.betCoinOption){if(!Array.isArray(d.betCoinOption))throw TypeError(".pokermaster_proto.SetGameOptionReq.betCoinOption: array expected");m.betCoinOption=[];for(var i=0;i<d.betCoinOption.length;++i){if($util.Long)(m.betCoinOption[i]=$util.Long.fromValue(d.betCoinOption[i])).unsigned=true;else if(typeof d.betCoinOption[i]==="string")m.betCoinOption[i]=parseInt(d.betCoinOption[i],10);else if(typeof d.betCoinOption[i]==="number")m.betCoinOption[i]=d.betCoinOption[i];else if(typeof d.betCoinOption[i]==="object")m.betCoinOption[i]=new $util.LongBits(d.betCoinOption[i].low>>>0,d.betCoinOption[i].high>>>0).toNumber(true)}}return m};SetGameOptionReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.betCoinOption=[]}if(o.defaults){d.autoLevel=o.enums===String?"Level_Normal":0}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){d.autoLevel=o.enums===String?$root.pokermaster_proto.AutoBetLevel[m.autoLevel]:m.autoLevel}if(m.betCoinOption&&m.betCoinOption.length){d.betCoinOption=[];for(var j=0;j<m.betCoinOption.length;++j){if(typeof m.betCoinOption[j]==="number")d.betCoinOption[j]=o.longs===String?String(m.betCoinOption[j]):m.betCoinOption[j];else d.betCoinOption[j]=o.longs===String?$util.Long.prototype.toString.call(m.betCoinOption[j]):o.longs===Number?new $util.LongBits(m.betCoinOption[j].low>>>0,m.betCoinOption[j].high>>>0).toNumber(true):m.betCoinOption[j]}}return d};SetGameOptionReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SetGameOptionReq}();pokermaster_proto.SetGameOptionResp=function(){function SetGameOptionResp(p){this.betCoinOption=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SetGameOptionResp.prototype.code=0;SetGameOptionResp.prototype.autoLevel=0;SetGameOptionResp.prototype.betCoinOption=$util.emptyArray;SetGameOptionResp.create=function create(properties){return new SetGameOptionResp(properties)};SetGameOptionResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.autoLevel!=null&&Object.hasOwnProperty.call(m,"autoLevel"))w.uint32(16).int32(m.autoLevel);if(m.betCoinOption!=null&&m.betCoinOption.length){w.uint32(26).fork();for(var i=0;i<m.betCoinOption.length;++i)w.uint64(m.betCoinOption[i]);w.ldelim()}return w};SetGameOptionResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SetGameOptionResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.SetGameOptionResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.autoLevel=r.int32();break;case 3:if(!(m.betCoinOption&&m.betCoinOption.length))m.betCoinOption=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.betCoinOption.push(r.uint64())}else m.betCoinOption.push(r.uint64());break;default:r.skipType(t&7);break}}return m};SetGameOptionResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SetGameOptionResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){switch(m.autoLevel){default:return"autoLevel: enum value expected";case 0:case 1:break}}if(m.betCoinOption!=null&&m.hasOwnProperty("betCoinOption")){if(!Array.isArray(m.betCoinOption))return"betCoinOption: array expected";for(var i=0;i<m.betCoinOption.length;++i){if(!$util.isInteger(m.betCoinOption[i])&&!(m.betCoinOption[i]&&$util.isInteger(m.betCoinOption[i].low)&&$util.isInteger(m.betCoinOption[i].high)))return"betCoinOption: integer|Long[] expected"}}return null};SetGameOptionResp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.SetGameOptionResp)return d;var m=new $root.pokermaster_proto.SetGameOptionResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}switch(d.autoLevel){case"Level_Normal":case 0:m.autoLevel=0;break;case"Level_Advance":case 1:m.autoLevel=1;break}if(d.betCoinOption){if(!Array.isArray(d.betCoinOption))throw TypeError(".pokermaster_proto.SetGameOptionResp.betCoinOption: array expected");m.betCoinOption=[];for(var i=0;i<d.betCoinOption.length;++i){if($util.Long)(m.betCoinOption[i]=$util.Long.fromValue(d.betCoinOption[i])).unsigned=true;else if(typeof d.betCoinOption[i]==="string")m.betCoinOption[i]=parseInt(d.betCoinOption[i],10);else if(typeof d.betCoinOption[i]==="number")m.betCoinOption[i]=d.betCoinOption[i];else if(typeof d.betCoinOption[i]==="object")m.betCoinOption[i]=new $util.LongBits(d.betCoinOption[i].low>>>0,d.betCoinOption[i].high>>>0).toNumber(true)}}return m};SetGameOptionResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.betCoinOption=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.autoLevel=o.enums===String?"Level_Normal":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){d.autoLevel=o.enums===String?$root.pokermaster_proto.AutoBetLevel[m.autoLevel]:m.autoLevel}if(m.betCoinOption&&m.betCoinOption.length){d.betCoinOption=[];for(var j=0;j<m.betCoinOption.length;++j){if(typeof m.betCoinOption[j]==="number")d.betCoinOption[j]=o.longs===String?String(m.betCoinOption[j]):m.betCoinOption[j];else d.betCoinOption[j]=o.longs===String?$util.Long.prototype.toString.call(m.betCoinOption[j]):o.longs===Number?new $util.LongBits(m.betCoinOption[j].low>>>0,m.betCoinOption[j].high>>>0).toNumber(true):m.betCoinOption[j]}}return d};SetGameOptionResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SetGameOptionResp}();pokermaster_proto.AdvanceAutoBetReq=function(){function AdvanceAutoBetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetReq.create=function create(properties){return new AdvanceAutoBetReq(properties)};AdvanceAutoBetReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};AdvanceAutoBetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AdvanceAutoBetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};AdvanceAutoBetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};AdvanceAutoBetReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AdvanceAutoBetReq)return d;return new $root.pokermaster_proto.AdvanceAutoBetReq};AdvanceAutoBetReq.toObject=function toObject(){return{}};AdvanceAutoBetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetReq}();pokermaster_proto.AdvanceAutoBetRsp=function(){function AdvanceAutoBetRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetRsp.prototype.code=0;AdvanceAutoBetRsp.prototype.usedAutoBetCount=0;AdvanceAutoBetRsp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;AdvanceAutoBetRsp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;AdvanceAutoBetRsp.prototype.bill=null;AdvanceAutoBetRsp.create=function create(properties){return new AdvanceAutoBetRsp(properties)};AdvanceAutoBetRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.usedAutoBetCount!=null&&Object.hasOwnProperty.call(m,"usedAutoBetCount"))w.uint32(16).int32(m.usedAutoBetCount);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);if(m.bill!=null&&Object.hasOwnProperty.call(m,"bill"))$root.pokermaster_proto.BillInfo.encode(m.bill,w.uint32(42).fork()).ldelim();return w};AdvanceAutoBetRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AdvanceAutoBetRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.usedAutoBetCount=r.int32();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;case 5:m.bill=$root.pokermaster_proto.BillInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){if(!$util.isInteger(m.usedAutoBetCount))return"usedAutoBetCount: integer expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}if(m.bill!=null&&m.hasOwnProperty("bill")){{var e=$root.pokermaster_proto.BillInfo.verify(m.bill);if(e)return"bill."+e}}return null};AdvanceAutoBetRsp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AdvanceAutoBetRsp)return d;var m=new $root.pokermaster_proto.AdvanceAutoBetRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.usedAutoBetCount!=null){m.usedAutoBetCount=d.usedAutoBetCount|0}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}if(d.bill!=null){if(typeof d.bill!=="object")throw TypeError(".pokermaster_proto.AdvanceAutoBetRsp.bill: object expected");m.bill=$root.pokermaster_proto.BillInfo.fromObject(d.bill)}return m};AdvanceAutoBetRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.usedAutoBetCount=0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0;d.bill=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){d.usedAutoBetCount=m.usedAutoBetCount}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}if(m.bill!=null&&m.hasOwnProperty("bill")){d.bill=$root.pokermaster_proto.BillInfo.toObject(m.bill,o)}return d};AdvanceAutoBetRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetRsp}();pokermaster_proto.CancelAdvanceAutoBetReq=function(){function CancelAdvanceAutoBetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CancelAdvanceAutoBetReq.create=function create(properties){return new CancelAdvanceAutoBetReq(properties)};CancelAdvanceAutoBetReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};CancelAdvanceAutoBetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CancelAdvanceAutoBetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.CancelAdvanceAutoBetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};CancelAdvanceAutoBetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CancelAdvanceAutoBetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};CancelAdvanceAutoBetReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.CancelAdvanceAutoBetReq)return d;return new $root.pokermaster_proto.CancelAdvanceAutoBetReq};CancelAdvanceAutoBetReq.toObject=function toObject(){return{}};CancelAdvanceAutoBetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CancelAdvanceAutoBetReq}();pokermaster_proto.CancelAdvanceAutoBetRsp=function(){function CancelAdvanceAutoBetRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CancelAdvanceAutoBetRsp.prototype.code=0;CancelAdvanceAutoBetRsp.prototype.is_manual=false;CancelAdvanceAutoBetRsp.create=function create(properties){return new CancelAdvanceAutoBetRsp(properties)};CancelAdvanceAutoBetRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.is_manual!=null&&Object.hasOwnProperty.call(m,"is_manual"))w.uint32(16).bool(m.is_manual);return w};CancelAdvanceAutoBetRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CancelAdvanceAutoBetRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.CancelAdvanceAutoBetRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.is_manual=r.bool();break;default:r.skipType(t&7);break}}return m};CancelAdvanceAutoBetRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CancelAdvanceAutoBetRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.is_manual!=null&&m.hasOwnProperty("is_manual")){if(typeof m.is_manual!=="boolean")return"is_manual: boolean expected"}return null};CancelAdvanceAutoBetRsp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.CancelAdvanceAutoBetRsp)return d;var m=new $root.pokermaster_proto.CancelAdvanceAutoBetRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.is_manual!=null){m.is_manual=Boolean(d.is_manual)}return m};CancelAdvanceAutoBetRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.is_manual=false}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.is_manual!=null&&m.hasOwnProperty("is_manual")){d.is_manual=m.is_manual}return d};CancelAdvanceAutoBetRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CancelAdvanceAutoBetRsp}();pokermaster_proto.AdvanceAutoBetSetReq=function(){function AdvanceAutoBetSetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetSetReq.prototype.count=0;AdvanceAutoBetSetReq.create=function create(properties){return new AdvanceAutoBetSetReq(properties)};AdvanceAutoBetSetReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.count!=null&&Object.hasOwnProperty.call(m,"count"))w.uint32(8).int32(m.count);return w};AdvanceAutoBetSetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetSetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AdvanceAutoBetSetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.count=r.int32();break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetSetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetSetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.count!=null&&m.hasOwnProperty("count")){if(!$util.isInteger(m.count))return"count: integer expected"}return null};AdvanceAutoBetSetReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AdvanceAutoBetSetReq)return d;var m=new $root.pokermaster_proto.AdvanceAutoBetSetReq;if(d.count!=null){m.count=d.count|0}return m};AdvanceAutoBetSetReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.count=0}if(m.count!=null&&m.hasOwnProperty("count")){d.count=m.count}return d};AdvanceAutoBetSetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetSetReq}();pokermaster_proto.AdvanceAutoBetSetRsp=function(){function AdvanceAutoBetSetRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetSetRsp.prototype.code=0;AdvanceAutoBetSetRsp.prototype.count=0;AdvanceAutoBetSetRsp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;AdvanceAutoBetSetRsp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;AdvanceAutoBetSetRsp.create=function create(properties){return new AdvanceAutoBetSetRsp(properties)};AdvanceAutoBetSetRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.count!=null&&Object.hasOwnProperty.call(m,"count"))w.uint32(16).int32(m.count);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);return w};AdvanceAutoBetSetRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetSetRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AdvanceAutoBetSetRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.count=r.int32();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetSetRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetSetRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.count!=null&&m.hasOwnProperty("count")){if(!$util.isInteger(m.count))return"count: integer expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}return null};AdvanceAutoBetSetRsp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AdvanceAutoBetSetRsp)return d;var m=new $root.pokermaster_proto.AdvanceAutoBetSetRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.count!=null){m.count=d.count|0}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}return m};AdvanceAutoBetSetRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.count=0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.count!=null&&m.hasOwnProperty("count")){d.count=m.count}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}return d};AdvanceAutoBetSetRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetSetRsp}();pokermaster_proto.StopBetNotify=function(){function StopBetNotify(p){this.cards=[];this.fisherOuts=[];this.sharkOuts=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StopBetNotify.prototype.cards=$util.emptyArray;StopBetNotify.prototype.canSquint=false;StopBetNotify.prototype.whoIsLeader=0;StopBetNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;StopBetNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;StopBetNotify.prototype.skipRound=false;StopBetNotify.prototype.fisherOuts=$util.emptyArray;StopBetNotify.prototype.sharkOuts=$util.emptyArray;StopBetNotify.prototype.fisherLevel=$util.Long?$util.Long.fromBits(0,0,false):0;StopBetNotify.prototype.sharkLevel=$util.Long?$util.Long.fromBits(0,0,false):0;StopBetNotify.create=function create(properties){return new StopBetNotify(properties)};StopBetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.cards!=null&&m.cards.length){for(var i=0;i<m.cards.length;++i)$root.pokermaster_proto.CardItem.encode(m.cards[i],w.uint32(10).fork()).ldelim()}if(m.canSquint!=null&&Object.hasOwnProperty.call(m,"canSquint"))w.uint32(16).bool(m.canSquint);if(m.whoIsLeader!=null&&Object.hasOwnProperty.call(m,"whoIsLeader"))w.uint32(24).int32(m.whoIsLeader);if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(32).int64(m.nextRoundEndStamp);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(40).int64(m.leftSeconds);if(m.skipRound!=null&&Object.hasOwnProperty.call(m,"skipRound"))w.uint32(48).bool(m.skipRound);if(m.fisherOuts!=null&&m.fisherOuts.length){for(var i=0;i<m.fisherOuts.length;++i)$root.pokermaster_proto.OutItem.encode(m.fisherOuts[i],w.uint32(58).fork()).ldelim()}if(m.sharkOuts!=null&&m.sharkOuts.length){for(var i=0;i<m.sharkOuts.length;++i)$root.pokermaster_proto.OutItem.encode(m.sharkOuts[i],w.uint32(66).fork()).ldelim()}if(m.fisherLevel!=null&&Object.hasOwnProperty.call(m,"fisherLevel"))w.uint32(72).int64(m.fisherLevel);if(m.sharkLevel!=null&&Object.hasOwnProperty.call(m,"sharkLevel"))w.uint32(80).int64(m.sharkLevel);return w};StopBetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StopBetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.StopBetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.cards&&m.cards.length))m.cards=[];m.cards.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;case 2:m.canSquint=r.bool();break;case 3:m.whoIsLeader=r.int32();break;case 4:m.nextRoundEndStamp=r.int64();break;case 5:m.leftSeconds=r.int64();break;case 6:m.skipRound=r.bool();break;case 7:if(!(m.fisherOuts&&m.fisherOuts.length))m.fisherOuts=[];m.fisherOuts.push($root.pokermaster_proto.OutItem.decode(r,r.uint32()));break;case 8:if(!(m.sharkOuts&&m.sharkOuts.length))m.sharkOuts=[];m.sharkOuts.push($root.pokermaster_proto.OutItem.decode(r,r.uint32()));break;case 9:m.fisherLevel=r.int64();break;case 10:m.sharkLevel=r.int64();break;default:r.skipType(t&7);break}}return m};StopBetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StopBetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.cards!=null&&m.hasOwnProperty("cards")){if(!Array.isArray(m.cards))return"cards: array expected";for(var i=0;i<m.cards.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.cards[i]);if(e)return"cards."+e}}}if(m.canSquint!=null&&m.hasOwnProperty("canSquint")){if(typeof m.canSquint!=="boolean")return"canSquint: boolean expected"}if(m.whoIsLeader!=null&&m.hasOwnProperty("whoIsLeader")){if(!$util.isInteger(m.whoIsLeader))return"whoIsLeader: integer expected"}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.skipRound!=null&&m.hasOwnProperty("skipRound")){if(typeof m.skipRound!=="boolean")return"skipRound: boolean expected"}if(m.fisherOuts!=null&&m.hasOwnProperty("fisherOuts")){if(!Array.isArray(m.fisherOuts))return"fisherOuts: array expected";for(var i=0;i<m.fisherOuts.length;++i){{var e=$root.pokermaster_proto.OutItem.verify(m.fisherOuts[i]);if(e)return"fisherOuts."+e}}}if(m.sharkOuts!=null&&m.hasOwnProperty("sharkOuts")){if(!Array.isArray(m.sharkOuts))return"sharkOuts: array expected";for(var i=0;i<m.sharkOuts.length;++i){{var e=$root.pokermaster_proto.OutItem.verify(m.sharkOuts[i]);if(e)return"sharkOuts."+e}}}if(m.fisherLevel!=null&&m.hasOwnProperty("fisherLevel")){if(!$util.isInteger(m.fisherLevel)&&!(m.fisherLevel&&$util.isInteger(m.fisherLevel.low)&&$util.isInteger(m.fisherLevel.high)))return"fisherLevel: integer|Long expected"}if(m.sharkLevel!=null&&m.hasOwnProperty("sharkLevel")){if(!$util.isInteger(m.sharkLevel)&&!(m.sharkLevel&&$util.isInteger(m.sharkLevel.low)&&$util.isInteger(m.sharkLevel.high)))return"sharkLevel: integer|Long expected"}return null};StopBetNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.StopBetNotify)return d;var m=new $root.pokermaster_proto.StopBetNotify;if(d.cards){if(!Array.isArray(d.cards))throw TypeError(".pokermaster_proto.StopBetNotify.cards: array expected");m.cards=[];for(var i=0;i<d.cards.length;++i){if(typeof d.cards[i]!=="object")throw TypeError(".pokermaster_proto.StopBetNotify.cards: object expected");m.cards[i]=$root.pokermaster_proto.CardItem.fromObject(d.cards[i])}}if(d.canSquint!=null){m.canSquint=Boolean(d.canSquint)}if(d.whoIsLeader!=null){m.whoIsLeader=d.whoIsLeader|0}if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.skipRound!=null){m.skipRound=Boolean(d.skipRound)}if(d.fisherOuts){if(!Array.isArray(d.fisherOuts))throw TypeError(".pokermaster_proto.StopBetNotify.fisherOuts: array expected");m.fisherOuts=[];for(var i=0;i<d.fisherOuts.length;++i){if(typeof d.fisherOuts[i]!=="object")throw TypeError(".pokermaster_proto.StopBetNotify.fisherOuts: object expected");m.fisherOuts[i]=$root.pokermaster_proto.OutItem.fromObject(d.fisherOuts[i])}}if(d.sharkOuts){if(!Array.isArray(d.sharkOuts))throw TypeError(".pokermaster_proto.StopBetNotify.sharkOuts: array expected");m.sharkOuts=[];for(var i=0;i<d.sharkOuts.length;++i){if(typeof d.sharkOuts[i]!=="object")throw TypeError(".pokermaster_proto.StopBetNotify.sharkOuts: object expected");m.sharkOuts[i]=$root.pokermaster_proto.OutItem.fromObject(d.sharkOuts[i])}}if(d.fisherLevel!=null){if($util.Long)(m.fisherLevel=$util.Long.fromValue(d.fisherLevel)).unsigned=false;else if(typeof d.fisherLevel==="string")m.fisherLevel=parseInt(d.fisherLevel,10);else if(typeof d.fisherLevel==="number")m.fisherLevel=d.fisherLevel;else if(typeof d.fisherLevel==="object")m.fisherLevel=new $util.LongBits(d.fisherLevel.low>>>0,d.fisherLevel.high>>>0).toNumber()}if(d.sharkLevel!=null){if($util.Long)(m.sharkLevel=$util.Long.fromValue(d.sharkLevel)).unsigned=false;else if(typeof d.sharkLevel==="string")m.sharkLevel=parseInt(d.sharkLevel,10);else if(typeof d.sharkLevel==="number")m.sharkLevel=d.sharkLevel;else if(typeof d.sharkLevel==="object")m.sharkLevel=new $util.LongBits(d.sharkLevel.low>>>0,d.sharkLevel.high>>>0).toNumber()}return m};StopBetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.cards=[];d.fisherOuts=[];d.sharkOuts=[]}if(o.defaults){d.canSquint=false;d.whoIsLeader=0;if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;d.skipRound=false;if($util.Long){var n=new $util.Long(0,0,false);d.fisherLevel=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.fisherLevel=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.sharkLevel=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.sharkLevel=o.longs===String?"0":0}if(m.cards&&m.cards.length){d.cards=[];for(var j=0;j<m.cards.length;++j){d.cards[j]=$root.pokermaster_proto.CardItem.toObject(m.cards[j],o)}}if(m.canSquint!=null&&m.hasOwnProperty("canSquint")){d.canSquint=m.canSquint}if(m.whoIsLeader!=null&&m.hasOwnProperty("whoIsLeader")){d.whoIsLeader=m.whoIsLeader}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.skipRound!=null&&m.hasOwnProperty("skipRound")){d.skipRound=m.skipRound}if(m.fisherOuts&&m.fisherOuts.length){d.fisherOuts=[];for(var j=0;j<m.fisherOuts.length;++j){d.fisherOuts[j]=$root.pokermaster_proto.OutItem.toObject(m.fisherOuts[j],o)}}if(m.sharkOuts&&m.sharkOuts.length){d.sharkOuts=[];for(var j=0;j<m.sharkOuts.length;++j){d.sharkOuts[j]=$root.pokermaster_proto.OutItem.toObject(m.sharkOuts[j],o)}}if(m.fisherLevel!=null&&m.hasOwnProperty("fisherLevel")){if(typeof m.fisherLevel==="number")d.fisherLevel=o.longs===String?String(m.fisherLevel):m.fisherLevel;else d.fisherLevel=o.longs===String?$util.Long.prototype.toString.call(m.fisherLevel):o.longs===Number?new $util.LongBits(m.fisherLevel.low>>>0,m.fisherLevel.high>>>0).toNumber():m.fisherLevel}if(m.sharkLevel!=null&&m.hasOwnProperty("sharkLevel")){if(typeof m.sharkLevel==="number")d.sharkLevel=o.longs===String?String(m.sharkLevel):m.sharkLevel;else d.sharkLevel=o.longs===String?$util.Long.prototype.toString.call(m.sharkLevel):o.longs===Number?new $util.LongBits(m.sharkLevel.low>>>0,m.sharkLevel.high>>>0).toNumber():m.sharkLevel}return d};StopBetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StopBetNotify}();pokermaster_proto.OutItem=function(){function OutItem(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OutItem.prototype.OutsId=0;OutItem.prototype.card=null;OutItem.create=function create(properties){return new OutItem(properties)};OutItem.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.OutsId!=null&&Object.hasOwnProperty.call(m,"OutsId"))w.uint32(8).int32(m.OutsId);if(m.card!=null&&Object.hasOwnProperty.call(m,"card"))$root.pokermaster_proto.CardItem.encode(m.card,w.uint32(18).fork()).ldelim();return w};OutItem.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OutItem.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.OutItem;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.OutsId=r.int32();break;case 2:m.card=$root.pokermaster_proto.CardItem.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};OutItem.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OutItem.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.OutsId!=null&&m.hasOwnProperty("OutsId")){if(!$util.isInteger(m.OutsId))return"OutsId: integer expected"}if(m.card!=null&&m.hasOwnProperty("card")){{var e=$root.pokermaster_proto.CardItem.verify(m.card);if(e)return"card."+e}}return null};OutItem.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.OutItem)return d;var m=new $root.pokermaster_proto.OutItem;if(d.OutsId!=null){m.OutsId=d.OutsId|0}if(d.card!=null){if(typeof d.card!=="object")throw TypeError(".pokermaster_proto.OutItem.card: object expected");m.card=$root.pokermaster_proto.CardItem.fromObject(d.card)}return m};OutItem.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.OutsId=0;d.card=null}if(m.OutsId!=null&&m.hasOwnProperty("OutsId")){d.OutsId=m.OutsId}if(m.card!=null&&m.hasOwnProperty("card")){d.card=$root.pokermaster_proto.CardItem.toObject(m.card,o)}return d};OutItem.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OutItem}();pokermaster_proto.BetOptionsOdds=function(){function BetOptionsOdds(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetOptionsOdds.prototype.option=0;BetOptionsOdds.prototype.odds=$util.Long?$util.Long.fromBits(0,0,false):0;BetOptionsOdds.prototype.limitRed=$util.Long?$util.Long.fromBits(0,0,true):0;BetOptionsOdds.prototype.winRate=0;BetOptionsOdds.create=function create(properties){return new BetOptionsOdds(properties)};BetOptionsOdds.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.odds!=null&&Object.hasOwnProperty.call(m,"odds"))w.uint32(16).int64(m.odds);if(m.limitRed!=null&&Object.hasOwnProperty.call(m,"limitRed"))w.uint32(24).uint64(m.limitRed);if(m.winRate!=null&&Object.hasOwnProperty.call(m,"winRate"))w.uint32(33).double(m.winRate);return w};BetOptionsOdds.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetOptionsOdds.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetOptionsOdds;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.odds=r.int64();break;case 3:m.limitRed=r.uint64();break;case 4:m.winRate=r.double();break;default:r.skipType(t&7);break}}return m};BetOptionsOdds.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetOptionsOdds.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}if(m.odds!=null&&m.hasOwnProperty("odds")){if(!$util.isInteger(m.odds)&&!(m.odds&&$util.isInteger(m.odds.low)&&$util.isInteger(m.odds.high)))return"odds: integer|Long expected"}if(m.limitRed!=null&&m.hasOwnProperty("limitRed")){if(!$util.isInteger(m.limitRed)&&!(m.limitRed&&$util.isInteger(m.limitRed.low)&&$util.isInteger(m.limitRed.high)))return"limitRed: integer|Long expected"}if(m.winRate!=null&&m.hasOwnProperty("winRate")){if(typeof m.winRate!=="number")return"winRate: number expected"}return null};BetOptionsOdds.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetOptionsOdds)return d;var m=new $root.pokermaster_proto.BetOptionsOdds;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"WIN_BEGIN":case 100:m.option=100;break;case"FISHER_WIN":case 101:m.option=101;break;case"SHARK_WIN":case 102:m.option=102;break;case"EQUAL":case 103:m.option=103;break;case"WIN_END":case 199:m.option=199;break;case"FIVE_BEGIN":case 300:m.option=300;break;case"FIVE_NONE_1DUI":case 301:m.option=301;break;case"FIVE_2DUI":case 302:m.option=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.option=303;break;case"FIVE_GOURD":case 304:m.option=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.option=305;break;case"FIVE_END":case 399:m.option=399;break}if(d.odds!=null){if($util.Long)(m.odds=$util.Long.fromValue(d.odds)).unsigned=false;else if(typeof d.odds==="string")m.odds=parseInt(d.odds,10);else if(typeof d.odds==="number")m.odds=d.odds;else if(typeof d.odds==="object")m.odds=new $util.LongBits(d.odds.low>>>0,d.odds.high>>>0).toNumber()}if(d.limitRed!=null){if($util.Long)(m.limitRed=$util.Long.fromValue(d.limitRed)).unsigned=true;else if(typeof d.limitRed==="string")m.limitRed=parseInt(d.limitRed,10);else if(typeof d.limitRed==="number")m.limitRed=d.limitRed;else if(typeof d.limitRed==="object")m.limitRed=new $util.LongBits(d.limitRed.low>>>0,d.limitRed.high>>>0).toNumber(true)}if(d.winRate!=null){m.winRate=Number(d.winRate)}return m};BetOptionsOdds.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.odds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.odds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.limitRed=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.limitRed=o.longs===String?"0":0;d.winRate=0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.option]:m.option}if(m.odds!=null&&m.hasOwnProperty("odds")){if(typeof m.odds==="number")d.odds=o.longs===String?String(m.odds):m.odds;else d.odds=o.longs===String?$util.Long.prototype.toString.call(m.odds):o.longs===Number?new $util.LongBits(m.odds.low>>>0,m.odds.high>>>0).toNumber():m.odds}if(m.limitRed!=null&&m.hasOwnProperty("limitRed")){if(typeof m.limitRed==="number")d.limitRed=o.longs===String?String(m.limitRed):m.limitRed;else d.limitRed=o.longs===String?$util.Long.prototype.toString.call(m.limitRed):o.longs===Number?new $util.LongBits(m.limitRed.low>>>0,m.limitRed.high>>>0).toNumber(true):m.limitRed}if(m.winRate!=null&&m.hasOwnProperty("winRate")){d.winRate=o.json&&!isFinite(m.winRate)?String(m.winRate):m.winRate}return d};BetOptionsOdds.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetOptionsOdds}();pokermaster_proto.BetReviewReq=function(){function BetReviewReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetReviewReq.create=function create(properties){return new BetReviewReq(properties)};BetReviewReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};BetReviewReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetReviewReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetReviewReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};BetReviewReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetReviewReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};BetReviewReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetReviewReq)return d;return new $root.pokermaster_proto.BetReviewReq};BetReviewReq.toObject=function toObject(){return{}};BetReviewReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetReviewReq}();pokermaster_proto.BetReviewRsp=function(){function BetReviewRsp(p){this.reviewed=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetReviewRsp.prototype.code=0;BetReviewRsp.prototype.reviewed=$util.emptyArray;BetReviewRsp.create=function create(properties){return new BetReviewRsp(properties)};BetReviewRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.reviewed!=null&&m.reviewed.length){for(var i=0;i<m.reviewed.length;++i)$root.pokermaster_proto.BetReview.encode(m.reviewed[i],w.uint32(18).fork()).ldelim()}return w};BetReviewRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetReviewRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetReviewRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:if(!(m.reviewed&&m.reviewed.length))m.reviewed=[];m.reviewed.push($root.pokermaster_proto.BetReview.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};BetReviewRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetReviewRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.reviewed!=null&&m.hasOwnProperty("reviewed")){if(!Array.isArray(m.reviewed))return"reviewed: array expected";for(var i=0;i<m.reviewed.length;++i){{var e=$root.pokermaster_proto.BetReview.verify(m.reviewed[i]);if(e)return"reviewed."+e}}}return null};BetReviewRsp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetReviewRsp)return d;var m=new $root.pokermaster_proto.BetReviewRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.reviewed){if(!Array.isArray(d.reviewed))throw TypeError(".pokermaster_proto.BetReviewRsp.reviewed: array expected");m.reviewed=[];for(var i=0;i<d.reviewed.length;++i){if(typeof d.reviewed[i]!=="object")throw TypeError(".pokermaster_proto.BetReviewRsp.reviewed: object expected");m.reviewed[i]=$root.pokermaster_proto.BetReview.fromObject(d.reviewed[i])}}return m};BetReviewRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.reviewed=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.reviewed&&m.reviewed.length){d.reviewed=[];for(var j=0;j<m.reviewed.length;++j){d.reviewed[j]=$root.pokermaster_proto.BetReview.toObject(m.reviewed[j],o)}}return d};BetReviewRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetReviewRsp}();pokermaster_proto.BetReview=function(){function BetReview(p){this.fisherCard=[];this.sharkCard=[];this.pubCard=[];this.detail=[];this.winOps=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetReview.prototype.totalBet=$util.Long?$util.Long.fromBits(0,0,false):0;BetReview.prototype.totalWin=$util.Long?$util.Long.fromBits(0,0,false):0;BetReview.prototype.fisherCard=$util.emptyArray;BetReview.prototype.sharkCard=$util.emptyArray;BetReview.prototype.pubCard=$util.emptyArray;BetReview.prototype.detail=$util.emptyArray;BetReview.prototype.winOps=$util.emptyArray;BetReview.prototype.level=$util.Long?$util.Long.fromBits(0,0,false):0;BetReview.create=function create(properties){return new BetReview(properties)};BetReview.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.totalBet!=null&&Object.hasOwnProperty.call(m,"totalBet"))w.uint32(8).int64(m.totalBet);if(m.totalWin!=null&&Object.hasOwnProperty.call(m,"totalWin"))w.uint32(16).int64(m.totalWin);if(m.fisherCard!=null&&m.fisherCard.length){for(var i=0;i<m.fisherCard.length;++i)$root.pokermaster_proto.CardItem.encode(m.fisherCard[i],w.uint32(26).fork()).ldelim()}if(m.sharkCard!=null&&m.sharkCard.length){for(var i=0;i<m.sharkCard.length;++i)$root.pokermaster_proto.CardItem.encode(m.sharkCard[i],w.uint32(34).fork()).ldelim()}if(m.pubCard!=null&&m.pubCard.length){for(var i=0;i<m.pubCard.length;++i)$root.pokermaster_proto.CardItem.encode(m.pubCard[i],w.uint32(42).fork()).ldelim()}if(m.detail!=null&&m.detail.length){for(var i=0;i<m.detail.length;++i)$root.pokermaster_proto.BetDetail.encode(m.detail[i],w.uint32(50).fork()).ldelim()}if(m.winOps!=null&&m.winOps.length){w.uint32(58).fork();for(var i=0;i<m.winOps.length;++i)w.int32(m.winOps[i]);w.ldelim()}if(m.level!=null&&Object.hasOwnProperty.call(m,"level"))w.uint32(64).int64(m.level);return w};BetReview.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetReview.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.BetReview;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.totalBet=r.int64();break;case 2:m.totalWin=r.int64();break;case 3:if(!(m.fisherCard&&m.fisherCard.length))m.fisherCard=[];m.fisherCard.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;case 4:if(!(m.sharkCard&&m.sharkCard.length))m.sharkCard=[];m.sharkCard.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;case 5:if(!(m.pubCard&&m.pubCard.length))m.pubCard=[];m.pubCard.push($root.pokermaster_proto.CardItem.decode(r,r.uint32()));break;case 6:if(!(m.detail&&m.detail.length))m.detail=[];m.detail.push($root.pokermaster_proto.BetDetail.decode(r,r.uint32()));break;case 7:if(!(m.winOps&&m.winOps.length))m.winOps=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.winOps.push(r.int32())}else m.winOps.push(r.int32());break;case 8:m.level=r.int64();break;default:r.skipType(t&7);break}}return m};BetReview.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetReview.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(!$util.isInteger(m.totalBet)&&!(m.totalBet&&$util.isInteger(m.totalBet.low)&&$util.isInteger(m.totalBet.high)))return"totalBet: integer|Long expected"}if(m.totalWin!=null&&m.hasOwnProperty("totalWin")){if(!$util.isInteger(m.totalWin)&&!(m.totalWin&&$util.isInteger(m.totalWin.low)&&$util.isInteger(m.totalWin.high)))return"totalWin: integer|Long expected"}if(m.fisherCard!=null&&m.hasOwnProperty("fisherCard")){if(!Array.isArray(m.fisherCard))return"fisherCard: array expected";for(var i=0;i<m.fisherCard.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.fisherCard[i]);if(e)return"fisherCard."+e}}}if(m.sharkCard!=null&&m.hasOwnProperty("sharkCard")){if(!Array.isArray(m.sharkCard))return"sharkCard: array expected";for(var i=0;i<m.sharkCard.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.sharkCard[i]);if(e)return"sharkCard."+e}}}if(m.pubCard!=null&&m.hasOwnProperty("pubCard")){if(!Array.isArray(m.pubCard))return"pubCard: array expected";for(var i=0;i<m.pubCard.length;++i){{var e=$root.pokermaster_proto.CardItem.verify(m.pubCard[i]);if(e)return"pubCard."+e}}}if(m.detail!=null&&m.hasOwnProperty("detail")){if(!Array.isArray(m.detail))return"detail: array expected";for(var i=0;i<m.detail.length;++i){{var e=$root.pokermaster_proto.BetDetail.verify(m.detail[i]);if(e)return"detail."+e}}}if(m.winOps!=null&&m.hasOwnProperty("winOps")){if(!Array.isArray(m.winOps))return"winOps: array expected";for(var i=0;i<m.winOps.length;++i){switch(m.winOps[i]){default:return"winOps: enum value[] expected";case 0:case 100:case 101:case 102:case 103:case 199:case 300:case 301:case 302:case 303:case 304:case 305:case 399:break}}}if(m.level!=null&&m.hasOwnProperty("level")){if(!$util.isInteger(m.level)&&!(m.level&&$util.isInteger(m.level.low)&&$util.isInteger(m.level.high)))return"level: integer|Long expected"}return null};BetReview.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.BetReview)return d;var m=new $root.pokermaster_proto.BetReview;if(d.totalBet!=null){if($util.Long)(m.totalBet=$util.Long.fromValue(d.totalBet)).unsigned=false;else if(typeof d.totalBet==="string")m.totalBet=parseInt(d.totalBet,10);else if(typeof d.totalBet==="number")m.totalBet=d.totalBet;else if(typeof d.totalBet==="object")m.totalBet=new $util.LongBits(d.totalBet.low>>>0,d.totalBet.high>>>0).toNumber()}if(d.totalWin!=null){if($util.Long)(m.totalWin=$util.Long.fromValue(d.totalWin)).unsigned=false;else if(typeof d.totalWin==="string")m.totalWin=parseInt(d.totalWin,10);else if(typeof d.totalWin==="number")m.totalWin=d.totalWin;else if(typeof d.totalWin==="object")m.totalWin=new $util.LongBits(d.totalWin.low>>>0,d.totalWin.high>>>0).toNumber()}if(d.fisherCard){if(!Array.isArray(d.fisherCard))throw TypeError(".pokermaster_proto.BetReview.fisherCard: array expected");m.fisherCard=[];for(var i=0;i<d.fisherCard.length;++i){if(typeof d.fisherCard[i]!=="object")throw TypeError(".pokermaster_proto.BetReview.fisherCard: object expected");m.fisherCard[i]=$root.pokermaster_proto.CardItem.fromObject(d.fisherCard[i])}}if(d.sharkCard){if(!Array.isArray(d.sharkCard))throw TypeError(".pokermaster_proto.BetReview.sharkCard: array expected");m.sharkCard=[];for(var i=0;i<d.sharkCard.length;++i){if(typeof d.sharkCard[i]!=="object")throw TypeError(".pokermaster_proto.BetReview.sharkCard: object expected");m.sharkCard[i]=$root.pokermaster_proto.CardItem.fromObject(d.sharkCard[i])}}if(d.pubCard){if(!Array.isArray(d.pubCard))throw TypeError(".pokermaster_proto.BetReview.pubCard: array expected");m.pubCard=[];for(var i=0;i<d.pubCard.length;++i){if(typeof d.pubCard[i]!=="object")throw TypeError(".pokermaster_proto.BetReview.pubCard: object expected");m.pubCard[i]=$root.pokermaster_proto.CardItem.fromObject(d.pubCard[i])}}if(d.detail){if(!Array.isArray(d.detail))throw TypeError(".pokermaster_proto.BetReview.detail: array expected");m.detail=[];for(var i=0;i<d.detail.length;++i){if(typeof d.detail[i]!=="object")throw TypeError(".pokermaster_proto.BetReview.detail: object expected");m.detail[i]=$root.pokermaster_proto.BetDetail.fromObject(d.detail[i])}}if(d.winOps){if(!Array.isArray(d.winOps))throw TypeError(".pokermaster_proto.BetReview.winOps: array expected");m.winOps=[];for(var i=0;i<d.winOps.length;++i){switch(d.winOps[i]){default:case"BetZoneOption_DUMMY":case 0:m.winOps[i]=0;break;case"WIN_BEGIN":case 100:m.winOps[i]=100;break;case"FISHER_WIN":case 101:m.winOps[i]=101;break;case"SHARK_WIN":case 102:m.winOps[i]=102;break;case"EQUAL":case 103:m.winOps[i]=103;break;case"WIN_END":case 199:m.winOps[i]=199;break;case"FIVE_BEGIN":case 300:m.winOps[i]=300;break;case"FIVE_NONE_1DUI":case 301:m.winOps[i]=301;break;case"FIVE_2DUI":case 302:m.winOps[i]=302;break;case"FIVE_SAN_SHUN_TONG":case 303:m.winOps[i]=303;break;case"FIVE_GOURD":case 304:m.winOps[i]=304;break;case"FIVE_KING_TONG_HUA_SHUN_4":case 305:m.winOps[i]=305;break;case"FIVE_END":case 399:m.winOps[i]=399;break}}}if(d.level!=null){if($util.Long)(m.level=$util.Long.fromValue(d.level)).unsigned=false;else if(typeof d.level==="string")m.level=parseInt(d.level,10);else if(typeof d.level==="number")m.level=d.level;else if(typeof d.level==="object")m.level=new $util.LongBits(d.level.low>>>0,d.level.high>>>0).toNumber()}return m};BetReview.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.fisherCard=[];d.sharkCard=[];d.pubCard=[];d.detail=[];d.winOps=[]}if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.totalBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBet=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.totalWin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalWin=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.level=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.level=o.longs===String?"0":0}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(typeof m.totalBet==="number")d.totalBet=o.longs===String?String(m.totalBet):m.totalBet;else d.totalBet=o.longs===String?$util.Long.prototype.toString.call(m.totalBet):o.longs===Number?new $util.LongBits(m.totalBet.low>>>0,m.totalBet.high>>>0).toNumber():m.totalBet}if(m.totalWin!=null&&m.hasOwnProperty("totalWin")){if(typeof m.totalWin==="number")d.totalWin=o.longs===String?String(m.totalWin):m.totalWin;else d.totalWin=o.longs===String?$util.Long.prototype.toString.call(m.totalWin):o.longs===Number?new $util.LongBits(m.totalWin.low>>>0,m.totalWin.high>>>0).toNumber():m.totalWin}if(m.fisherCard&&m.fisherCard.length){d.fisherCard=[];for(var j=0;j<m.fisherCard.length;++j){d.fisherCard[j]=$root.pokermaster_proto.CardItem.toObject(m.fisherCard[j],o)}}if(m.sharkCard&&m.sharkCard.length){d.sharkCard=[];for(var j=0;j<m.sharkCard.length;++j){d.sharkCard[j]=$root.pokermaster_proto.CardItem.toObject(m.sharkCard[j],o)}}if(m.pubCard&&m.pubCard.length){d.pubCard=[];for(var j=0;j<m.pubCard.length;++j){d.pubCard[j]=$root.pokermaster_proto.CardItem.toObject(m.pubCard[j],o)}}if(m.detail&&m.detail.length){d.detail=[];for(var j=0;j<m.detail.length;++j){d.detail[j]=$root.pokermaster_proto.BetDetail.toObject(m.detail[j],o)}}if(m.winOps&&m.winOps.length){d.winOps=[];for(var j=0;j<m.winOps.length;++j){d.winOps[j]=o.enums===String?$root.pokermaster_proto.BetZoneOption[m.winOps[j]]:m.winOps[j]}}if(m.level!=null&&m.hasOwnProperty("level")){if(typeof m.level==="number")d.level=o.longs===String?String(m.level):m.level;else d.level=o.longs===String?$util.Long.prototype.toString.call(m.level):o.longs===Number?new $util.LongBits(m.level.low>>>0,m.level.high>>>0).toNumber():m.level}return d};BetReview.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetReview}();pokermaster_proto.ReadyGameNotify=function(){function ReadyGameNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ReadyGameNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;ReadyGameNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;ReadyGameNotify.create=function create(properties){return new ReadyGameNotify(properties)};ReadyGameNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(8).int64(m.nextRoundEndStamp);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(16).int64(m.leftSeconds);return w};ReadyGameNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ReadyGameNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.ReadyGameNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.nextRoundEndStamp=r.int64();break;case 2:m.leftSeconds=r.int64();break;default:r.skipType(t&7);break}}return m};ReadyGameNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ReadyGameNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}return null};ReadyGameNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.ReadyGameNotify)return d;var m=new $root.pokermaster_proto.ReadyGameNotify;if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}return m};ReadyGameNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}return d};ReadyGameNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ReadyGameNotify}();pokermaster_proto.UserPointsChangeNotice=function(){function UserPointsChangeNotice(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}UserPointsChangeNotice.prototype.change_points=$util.Long?$util.Long.fromBits(0,0,false):0;UserPointsChangeNotice.create=function create(properties){return new UserPointsChangeNotice(properties)};UserPointsChangeNotice.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.change_points!=null&&Object.hasOwnProperty.call(m,"change_points"))w.uint32(8).int64(m.change_points);return w};UserPointsChangeNotice.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};UserPointsChangeNotice.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.UserPointsChangeNotice;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.change_points=r.int64();break;default:r.skipType(t&7);break}}return m};UserPointsChangeNotice.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};UserPointsChangeNotice.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.change_points!=null&&m.hasOwnProperty("change_points")){if(!$util.isInteger(m.change_points)&&!(m.change_points&&$util.isInteger(m.change_points.low)&&$util.isInteger(m.change_points.high)))return"change_points: integer|Long expected"}return null};UserPointsChangeNotice.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.UserPointsChangeNotice)return d;var m=new $root.pokermaster_proto.UserPointsChangeNotice;if(d.change_points!=null){if($util.Long)(m.change_points=$util.Long.fromValue(d.change_points)).unsigned=false;else if(typeof d.change_points==="string")m.change_points=parseInt(d.change_points,10);else if(typeof d.change_points==="number")m.change_points=d.change_points;else if(typeof d.change_points==="object")m.change_points=new $util.LongBits(d.change_points.low>>>0,d.change_points.high>>>0).toNumber()}return m};UserPointsChangeNotice.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.change_points=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.change_points=o.longs===String?"0":0}if(m.change_points!=null&&m.hasOwnProperty("change_points")){if(typeof m.change_points==="number")d.change_points=o.longs===String?String(m.change_points):m.change_points;else d.change_points=o.longs===String?$util.Long.prototype.toString.call(m.change_points):o.longs===Number?new $util.LongBits(m.change_points.low>>>0,m.change_points.high>>>0).toNumber():m.change_points}return d};UserPointsChangeNotice.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return UserPointsChangeNotice}();pokermaster_proto.AdvanceAutoBetAddReq=function(){function AdvanceAutoBetAddReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetAddReq.prototype.count=0;AdvanceAutoBetAddReq.create=function create(properties){return new AdvanceAutoBetAddReq(properties)};AdvanceAutoBetAddReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.count!=null&&Object.hasOwnProperty.call(m,"count"))w.uint32(8).int32(m.count);return w};AdvanceAutoBetAddReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetAddReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AdvanceAutoBetAddReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.count=r.int32();break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetAddReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetAddReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.count!=null&&m.hasOwnProperty("count")){if(!$util.isInteger(m.count))return"count: integer expected"}return null};AdvanceAutoBetAddReq.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AdvanceAutoBetAddReq)return d;var m=new $root.pokermaster_proto.AdvanceAutoBetAddReq;if(d.count!=null){m.count=d.count|0}return m};AdvanceAutoBetAddReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.count=0}if(m.count!=null&&m.hasOwnProperty("count")){d.count=m.count}return d};AdvanceAutoBetAddReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetAddReq}();pokermaster_proto.AdvanceAutoBetAddRsp=function(){function AdvanceAutoBetAddRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetAddRsp.prototype.code=0;AdvanceAutoBetAddRsp.prototype.autoBetCount=0;AdvanceAutoBetAddRsp.prototype.usedAutoBetCount=0;AdvanceAutoBetAddRsp.prototype.numberHandAdded=0;AdvanceAutoBetAddRsp.create=function create(properties){return new AdvanceAutoBetAddRsp(properties)};AdvanceAutoBetAddRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.autoBetCount!=null&&Object.hasOwnProperty.call(m,"autoBetCount"))w.uint32(16).int32(m.autoBetCount);if(m.usedAutoBetCount!=null&&Object.hasOwnProperty.call(m,"usedAutoBetCount"))w.uint32(24).int32(m.usedAutoBetCount);if(m.numberHandAdded!=null&&Object.hasOwnProperty.call(m,"numberHandAdded"))w.uint32(32).int32(m.numberHandAdded);return w};AdvanceAutoBetAddRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetAddRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.AdvanceAutoBetAddRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.autoBetCount=r.int32();break;case 3:m.usedAutoBetCount=r.int32();break;case 4:m.numberHandAdded=r.int32();break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetAddRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetAddRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 51e3:case 51001:case 51002:case 51003:case 51004:case 51005:case 51006:case 51007:case 51008:case 51009:case 51010:case 51011:case 51012:case 51013:case 51014:case 51015:case 51016:case 51017:case 51018:case 51019:case 51020:case 51021:case 51022:case 51023:case 51027:case 51024:case 51025:case 51026:case 31117:case 31118:break}}if(m.autoBetCount!=null&&m.hasOwnProperty("autoBetCount")){if(!$util.isInteger(m.autoBetCount))return"autoBetCount: integer expected"}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){if(!$util.isInteger(m.usedAutoBetCount))return"usedAutoBetCount: integer expected"}if(m.numberHandAdded!=null&&m.hasOwnProperty("numberHandAdded")){if(!$util.isInteger(m.numberHandAdded))return"numberHandAdded: integer expected"}return null};AdvanceAutoBetAddRsp.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.AdvanceAutoBetAddRsp)return d;var m=new $root.pokermaster_proto.AdvanceAutoBetAddRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 51e3:m.code=51e3;break;case"LOW_VERSION":case 51001:m.code=51001;break;case"INVALID_TOKEN":case 51002:m.code=51002;break;case"SERVER_BUSY":case 51003:m.code=51003;break;case"WITHOUT_LOGIN":case 51004:m.code=51004;break;case"ROOM_NOT_MATCH":case 51005:m.code=51005;break;case"ROOM_NOT_EXIST":case 51006:m.code=51006;break;case"BET_EXCEED_LIMIT":case 51007:m.code=51007;break;case"ROOM_PLAYER_LIMIT":case 51008:m.code=51008;break;case"NO_BET":case 51009:m.code=51009;break;case"BET_AMOUNT_NOT_MATCH":case 51010:m.code=51010;break;case"NO_MONEY":case 51011:m.code=51011;break;case"BET_BAD_PARAM":case 51012:m.code=51012;break;case"STOP_SERVICE":case 51013:m.code=51013;break;case"NOT_BET_WHEN_AUTO_BET":case 51014:m.code=51014;break;case"BET_TOO_SMALL":case 51015:m.code=51015;break;case"BET_COUNT_LIMIT":case 51016:m.code=51016;break;case"AUTO_BET_LIMIT":case 51017:m.code=51017;break;case"TOO_MANY_PEOPLE":case 51018:m.code=51018;break;case"BAD_REQ_PARAM":case 51019:m.code=51019;break;case"NO_SET_ADVANCE_AUTO_BET":case 51020:m.code=51020;break;case"AUTO_BET_COUNT_LIMIT":case 51021:m.code=51021;break;case"AUTO_BET_NO_MONEY":case 51022:m.code=51022;break;case"AUTO_BET_EXCEED_LIMIT":case 51023:m.code=51023;break;case"REACH_LIMIT_BET":case 51027:m.code=51027;break;case"INNER_ERROR":case 51024:m.code=51024;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 51025:m.code=51025;break;case"IN_CALM_DOWN":case 51026:m.code=51026;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.autoBetCount!=null){m.autoBetCount=d.autoBetCount|0}if(d.usedAutoBetCount!=null){m.usedAutoBetCount=d.usedAutoBetCount|0}if(d.numberHandAdded!=null){m.numberHandAdded=d.numberHandAdded|0}return m};AdvanceAutoBetAddRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.autoBetCount=0;d.usedAutoBetCount=0;d.numberHandAdded=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.pokermaster_proto.ErrorCode[m.code]:m.code}if(m.autoBetCount!=null&&m.hasOwnProperty("autoBetCount")){d.autoBetCount=m.autoBetCount}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){d.usedAutoBetCount=m.usedAutoBetCount}if(m.numberHandAdded!=null&&m.hasOwnProperty("numberHandAdded")){d.numberHandAdded=m.numberHandAdded}return d};AdvanceAutoBetAddRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetAddRsp}();pokermaster_proto.LeftGameCoinNotify=function(){function LeftGameCoinNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LeftGameCoinNotify.prototype.cur_game_coin=$util.Long?$util.Long.fromBits(0,0,false):0;LeftGameCoinNotify.prototype.lost_game_coin=$util.Long?$util.Long.fromBits(0,0,false):0;LeftGameCoinNotify.create=function create(properties){return new LeftGameCoinNotify(properties)};LeftGameCoinNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.cur_game_coin!=null&&Object.hasOwnProperty.call(m,"cur_game_coin"))w.uint32(8).int64(m.cur_game_coin);if(m.lost_game_coin!=null&&Object.hasOwnProperty.call(m,"lost_game_coin"))w.uint32(16).int64(m.lost_game_coin);return w};LeftGameCoinNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LeftGameCoinNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.pokermaster_proto.LeftGameCoinNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.cur_game_coin=r.int64();break;case 2:m.lost_game_coin=r.int64();break;default:r.skipType(t&7);break}}return m};LeftGameCoinNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LeftGameCoinNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.cur_game_coin!=null&&m.hasOwnProperty("cur_game_coin")){if(!$util.isInteger(m.cur_game_coin)&&!(m.cur_game_coin&&$util.isInteger(m.cur_game_coin.low)&&$util.isInteger(m.cur_game_coin.high)))return"cur_game_coin: integer|Long expected"}if(m.lost_game_coin!=null&&m.hasOwnProperty("lost_game_coin")){if(!$util.isInteger(m.lost_game_coin)&&!(m.lost_game_coin&&$util.isInteger(m.lost_game_coin.low)&&$util.isInteger(m.lost_game_coin.high)))return"lost_game_coin: integer|Long expected"}return null};LeftGameCoinNotify.fromObject=function fromObject(d){if(d instanceof $root.pokermaster_proto.LeftGameCoinNotify)return d;var m=new $root.pokermaster_proto.LeftGameCoinNotify;if(d.cur_game_coin!=null){if($util.Long)(m.cur_game_coin=$util.Long.fromValue(d.cur_game_coin)).unsigned=false;else if(typeof d.cur_game_coin==="string")m.cur_game_coin=parseInt(d.cur_game_coin,10);else if(typeof d.cur_game_coin==="number")m.cur_game_coin=d.cur_game_coin;else if(typeof d.cur_game_coin==="object")m.cur_game_coin=new $util.LongBits(d.cur_game_coin.low>>>0,d.cur_game_coin.high>>>0).toNumber()}if(d.lost_game_coin!=null){if($util.Long)(m.lost_game_coin=$util.Long.fromValue(d.lost_game_coin)).unsigned=false;else if(typeof d.lost_game_coin==="string")m.lost_game_coin=parseInt(d.lost_game_coin,10);else if(typeof d.lost_game_coin==="number")m.lost_game_coin=d.lost_game_coin;else if(typeof d.lost_game_coin==="object")m.lost_game_coin=new $util.LongBits(d.lost_game_coin.low>>>0,d.lost_game_coin.high>>>0).toNumber()}return m};LeftGameCoinNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.cur_game_coin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.cur_game_coin=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.lost_game_coin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.lost_game_coin=o.longs===String?"0":0}if(m.cur_game_coin!=null&&m.hasOwnProperty("cur_game_coin")){if(typeof m.cur_game_coin==="number")d.cur_game_coin=o.longs===String?String(m.cur_game_coin):m.cur_game_coin;else d.cur_game_coin=o.longs===String?$util.Long.prototype.toString.call(m.cur_game_coin):o.longs===Number?new $util.LongBits(m.cur_game_coin.low>>>0,m.cur_game_coin.high>>>0).toNumber():m.cur_game_coin}if(m.lost_game_coin!=null&&m.hasOwnProperty("lost_game_coin")){if(typeof m.lost_game_coin==="number")d.lost_game_coin=o.longs===String?String(m.lost_game_coin):m.lost_game_coin;else d.lost_game_coin=o.longs===String?$util.Long.prototype.toString.call(m.lost_game_coin):o.longs===Number?new $util.LongBits(m.lost_game_coin.low>>>0,m.lost_game_coin.high>>>0).toNumber():m.lost_game_coin}return d};LeftGameCoinNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LeftGameCoinNotify}();return pokermaster_proto}();module.exports=$root;