"use strict";var $protobuf=require("protobufjs/minimal");var $Reader=$protobuf.Reader,$Writer=$protobuf.Writer,$util=$protobuf.util;var $root=$protobuf.roots["default"]||($protobuf.roots["default"]={});$root.humanboy_proto=function(){var humanboy_proto={};humanboy_proto.RoomLevel=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="RoomLevel_DUMMY"]=0;values[valuesById[1]="Small"]=1;values[valuesById[2]="Middle"]=2;values[valuesById[3]="Big"]=3;return values}();humanboy_proto.KickApplyDealerReason=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="K_NULL"]=0;values[valuesById[1]="K_NoMoney"]=1;values[valuesById[2]="K_SUPPLY"]=2;values[valuesById[3]="K_OFFLINE"]=3;values[valuesById[4]="K_LEAVE"]=4;return values}();humanboy_proto.RoundState=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="RoundState_DUMMY"]=0;values[valuesById[1]="GAME_PENDING"]=1;values[valuesById[2]="NEW_ROUND"]=2;values[valuesById[3]="BET"]=3;values[valuesById[4]="WAIT_NEXT_ROUND"]=4;values[valuesById[5]="WAIT_NEXT_ROUND2"]=5;return values}();humanboy_proto.BetZoneOption=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="BetZoneOption_DUMMY"]=0;values[valuesById[1]="HOST"]=1;values[valuesById[2]="POS1"]=2;values[valuesById[3]="POS2"]=3;values[valuesById[4]="POS3"]=4;values[valuesById[5]="POS4"]=5;values[valuesById[100]="POS_LUCK"]=100;values[valuesById[101]="POS_LUCK_1"]=101;values[valuesById[102]="POS_LUCK_2"]=102;values[valuesById[103]="POS_LUCK_3"]=103;values[valuesById[104]="POS_LUCK_4"]=104;values[valuesById[105]="POS_LUCK_5"]=105;values[valuesById[106]="POS_LUCK_6"]=106;return values}();humanboy_proto.CMD=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="CMD_DUMMY"]=0;values[valuesById[4e4]="LOGIN_GAME_REQ"]=4e4;values[valuesById[40001]="LOGIN_GAME_RESP"]=40001;values[valuesById[40004]="HEART_BEAT_REQ"]=40004;values[valuesById[40005]="HEART_BEAT_RESP"]=40005;values[valuesById[40007]="JOIN_ROOM_REQ"]=40007;values[valuesById[40008]="JOIN_ROOM_RESP"]=40008;values[valuesById[40009]="GAME_LIST_REQ"]=40009;values[valuesById[40010]="GAME_LIST_RESP"]=40010;values[valuesById[40011]="GAME_DATA_SYN"]=40011;values[valuesById[40012]="DEAL_NOTIFY"]=40012;values[valuesById[40013]="BET_REQ"]=40013;values[valuesById[40014]="BET_RESP"]=40014;values[valuesById[40015]="BET_NOTIFY"]=40015;values[valuesById[40016]="GAME_ROUND_END_NOTIFY"]=40016;values[valuesById[40018]="LEAVE_ROOM_REQ"]=40018;values[valuesById[40019]="LEAVE_ROOM_RESP"]=40019;values[valuesById[40020]="LEAVE_ROOM_NOTIFY"]=40020;values[valuesById[40022]="CONN_CLOSE_REQ"]=40022;values[valuesById[40023]="ROOM_TREND_REQ"]=40023;values[valuesById[40024]="ROOM_TREND_RSP"]=40024;values[valuesById[40025]="ROOM_TREND_NOTICE"]=40025;values[valuesById[40026]="START_BET_NOTIFY"]=40026;values[valuesById[40029]="AUTO_BET_REQ"]=40029;values[valuesById[40030]="AUTO_BET_RESP"]=40030;values[valuesById[40031]="AUTO_BET_NOTIFY"]=40031;values[valuesById[40032]="PLAYER_LIST_REQ"]=40032;values[valuesById[40033]="PLAYER_LIST_RESP"]=40033;values[valuesById[40036]="MERGE_AUTO_BET_NOTIFY"]=40036;values[valuesById[40037]="KICK_NOTIFY"]=40037;values[valuesById[40038]="DOWN_DEALER_REQ"]=40038;values[valuesById[40039]="DOWN_DEALER_RSP"]=40039;values[valuesById[40040]="DOWN_DEALER_NOTIFY"]=40040;values[valuesById[40041]="UP_DEALER_REQ"]=40041;values[valuesById[40042]="UP_DEALER_RSP"]=40042;values[valuesById[40043]="UP_DEALER_NOTIFY"]=40043;values[valuesById[40044]="CANCEL_WAIT_REQ"]=40044;values[valuesById[40045]="CANCEL_WAIT_RSP"]=40045;values[valuesById[40047]="DEALER_LIST_REQ"]=40047;values[valuesById[40048]="DEALER_LIST_RSP"]=40048;values[valuesById[40050]="GET_BUY_STOCK_NUM_REQ"]=40050;values[valuesById[40051]="GET_BUY_STOCK_NUM_RSP"]=40051;values[valuesById[40052]="JACKPOT_DATA_REQ"]=40052;values[valuesById[40053]="JACKPOT_DATA_RSP"]=40053;values[valuesById[40055]="JACKPOT_AWARD_LIST_REQ"]=40055;values[valuesById[40056]="JACKPOT_AWARD_LIST_RSP"]=40056;values[valuesById[40063]="GAME_WILL_START_NOTIFY"]=40063;values[valuesById[40066]="KICK_DEALER_APPLY_NOTIFY"]=40066;values[valuesById[40067]="UPDATE_DEALER_LIST_REQ"]=40067;values[valuesById[40068]="UPDATE_DEALER_LIST_RSP"]=40068;values[valuesById[40070]="SET_GAME_OPTION_REQ"]=40070;values[valuesById[40071]="SET_GAME_OPTION_RSP"]=40071;values[valuesById[40073]="START_SETTLEMENT_NOTIFY"]=40073;values[valuesById[40074]="SEND_EXPRESSION_REQ"]=40074;values[valuesById[40075]="SEND_EXPRESSION_RSP"]=40075;values[valuesById[40076]="SEND_EXPRESSION_NOTIFY"]=40076;values[valuesById[40080]="ADVANCE_AUTO_BET_REQ"]=40080;values[valuesById[40081]="ADVANCE_AUTO_BET_RSP"]=40081;values[valuesById[40082]="CANCEL_ADVANCE_AUTO_BET_REQ"]=40082;values[valuesById[40083]="CANCEL_ADVANCE_AUTO_BET_RSP"]=40083;values[valuesById[40084]="ADVANCE_AUTO_BET_SET_REQ"]=40084;values[valuesById[40085]="ADVANCE_AUTO_BET_SET_RSP"]=40085;values[valuesById[40086]="USER_POINTS_CHANGE_NOTICE"]=40086;values[valuesById[40087]="ADVANCE_AUTO_BET_ADD_REQ"]=40087;values[valuesById[40088]="ADVANCE_AUTO_BET_ADD_RSP"]=40088;values[valuesById[40089]="LEFT_GAME_COIN_NOTIFY"]=40089;return values}();humanboy_proto.ErrorCode=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="ErrorCode_DUMMY"]=0;values[valuesById[1]="OK"]=1;values[valuesById[41e3]="ROOM_WITHOUT_YOU"]=41e3;values[valuesById[41001]="LOW_VERSION"]=41001;values[valuesById[41002]="INVALID_TOKEN"]=41002;values[valuesById[41003]="SERVER_BUSY"]=41003;values[valuesById[41004]="WITHOUT_LOGIN"]=41004;values[valuesById[41005]="ROOM_NOT_MATCH"]=41005;values[valuesById[41006]="ROOM_NOT_EXIST"]=41006;values[valuesById[41007]="BET_EXCEED_LIMIT"]=41007;values[valuesById[41008]="ROOM_PLAYER_LIMIT"]=41008;values[valuesById[41009]="NO_BET"]=41009;values[valuesById[41010]="BET_AMOUNT_NOT_MATCH"]=41010;values[valuesById[41011]="NO_MONEY"]=41011;values[valuesById[41012]="BET_BAD_PARAM"]=41012;values[valuesById[41013]="STOP_SERVICE"]=41013;values[valuesById[41014]="NOT_BET_WHEN_AUTO_BET"]=41014;values[valuesById[41015]="BET_TOO_SMALL"]=41015;values[valuesById[41016]="BET_COUNT_LIMIT"]=41016;values[valuesById[41017]="AUTO_BET_LIMIT"]=41017;values[valuesById[41018]="TOO_MANY_PEOPLE"]=41018;values[valuesById[41019]="NO_UP_DEALER"]=41019;values[valuesById[41020]="STOCK_NUM_EXCEED"]=41020;values[valuesById[41021]="NO_MONEY_TO_DEALER"]=41021;values[valuesById[41022]="NOT_A_DEALER"]=41022;values[valuesById[41023]="NOT_IN_APPLY"]=41023;values[valuesById[41024]="DEALER_NO_BET"]=41024;values[valuesById[41025]="BAD_REQ_PARAM"]=41025;values[valuesById[41026]="NO_SET_ADVANCE_AUTO_BET"]=41026;values[valuesById[41027]="AUTO_BET_COUNT_LIMIT"]=41027;values[valuesById[41028]="AUTO_BET_NO_MONEY"]=41028;values[valuesById[41029]="AUTO_BET_EXCEED_LIMIT"]=41029;values[valuesById[41034]="REACH_LIMIT_BET"]=41034;values[valuesById[41030]="ROOM_SYSTEM_FORCE_CLOSED"]=41030;values[valuesById[41031]="CAN_NOT_LEAVE_IN_BETTING"]=41031;values[valuesById[41032]="CAN_NOT_LEAVE_IN_DEALER"]=41032;values[valuesById[41033]="IN_CALM_DOWN"]=41033;values[valuesById[31117]="C2CPAYMENT_LIST_GET_ERROR"]=31117;values[valuesById[31118]="C2CPAYMENT_NOT_ALLOW"]=31118;return values}();humanboy_proto.DownDealerReason=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="DownDummy"]=0;values[valuesById[1]="NoMoney"]=1;values[valuesById[2]="LongTime"]=2;values[valuesById[3]="Leave"]=3;values[valuesById[4]="Offline"]=4;values[valuesById[5]="CalmDown"]=5;return values}();humanboy_proto.CardResult=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="CardResult_Dummy"]=0;values[valuesById[1]="GAO_PAI"]=1;values[valuesById[2]="YI_DUI"]=2;values[valuesById[3]="LIAN_DUI"]=3;values[valuesById[4]="SAN_TIAO"]=4;values[valuesById[5]="SHUN_ZI"]=5;values[valuesById[6]="TONG_HUA"]=6;values[valuesById[7]="HU_LU"]=7;values[valuesById[8]="SI_TIAO"]=8;values[valuesById[9]="TONG_HUA_SHUN"]=9;values[valuesById[10]="HUANG_TONG"]=10;return values}();humanboy_proto.Kick=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Kick_DUMMY"]=0;values[valuesById[1]="IDLE_LONG_TIME"]=1;values[valuesById[2]="Stop_World"]=2;return values}();humanboy_proto.AutoBetLevel=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Level_Normal"]=0;values[valuesById[1]="Level_Advance"]=1;return values}();humanboy_proto.ClientType=function(){var valuesById={},values=Object.create(valuesById);values[valuesById[0]="Dummy"]=0;values[valuesById[1]="Normal"]=1;values[valuesById[2]="OverSeas"]=2;values[valuesById[3]="H5"]=3;values[valuesById[4]="H5OverSeas"]=4;values[valuesById[5]="H5Web"]=5;values[valuesById[6]="H5WebOverSeas"]=6;values[valuesById[7]="H5VietnamLasted"]=7;values[valuesById[8]="H5WebVietnamLasted"]=8;values[valuesById[9]="H5CowboyWeb"]=9;values[valuesById[10]="H5Thailand"]=10;values[valuesById[11]="H5WebThailand"]=11;values[valuesById[12]="H5Arab"]=12;values[valuesById[13]="H5Hindi"]=13;values[valuesById[14]="H5Mempoker"]=14;values[valuesById[15]="PC"]=15;values[valuesById[16]="WPTG"]=16;return values}();humanboy_proto.CardItem=function(){function CardItem(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CardItem.prototype.number=0;CardItem.prototype.suit=0;CardItem.create=function create(properties){return new CardItem(properties)};CardItem.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.number!=null&&Object.hasOwnProperty.call(m,"number"))w.uint32(8).int32(m.number);if(m.suit!=null&&Object.hasOwnProperty.call(m,"suit"))w.uint32(16).int32(m.suit);return w};CardItem.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CardItem.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.CardItem;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.number=r.int32();break;case 2:m.suit=r.int32();break;default:r.skipType(t&7);break}}return m};CardItem.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CardItem.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.number!=null&&m.hasOwnProperty("number")){if(!$util.isInteger(m.number))return"number: integer expected"}if(m.suit!=null&&m.hasOwnProperty("suit")){if(!$util.isInteger(m.suit))return"suit: integer expected"}return null};CardItem.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.CardItem)return d;var m=new $root.humanboy_proto.CardItem;if(d.number!=null){m.number=d.number|0}if(d.suit!=null){m.suit=d.suit|0}return m};CardItem.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.number=0;d.suit=0}if(m.number!=null&&m.hasOwnProperty("number")){d.number=m.number}if(m.suit!=null&&m.hasOwnProperty("suit")){d.suit=m.suit}return d};CardItem.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CardItem}();humanboy_proto.HeartBeatReq=function(){function HeartBeatReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}HeartBeatReq.prototype.uid=0;HeartBeatReq.create=function create(properties){return new HeartBeatReq(properties)};HeartBeatReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);return w};HeartBeatReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};HeartBeatReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.HeartBeatReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;default:r.skipType(t&7);break}}return m};HeartBeatReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};HeartBeatReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}return null};HeartBeatReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.HeartBeatReq)return d;var m=new $root.humanboy_proto.HeartBeatReq;if(d.uid!=null){m.uid=d.uid>>>0}return m};HeartBeatReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}return d};HeartBeatReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return HeartBeatReq}();humanboy_proto.HeartBeatResp=function(){function HeartBeatResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}HeartBeatResp.prototype.uid=0;HeartBeatResp.prototype.timestamp=$util.Long?$util.Long.fromBits(0,0,false):0;HeartBeatResp.create=function create(properties){return new HeartBeatResp(properties)};HeartBeatResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.timestamp!=null&&Object.hasOwnProperty.call(m,"timestamp"))w.uint32(16).int64(m.timestamp);return w};HeartBeatResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};HeartBeatResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.HeartBeatResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.timestamp=r.int64();break;default:r.skipType(t&7);break}}return m};HeartBeatResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};HeartBeatResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.timestamp!=null&&m.hasOwnProperty("timestamp")){if(!$util.isInteger(m.timestamp)&&!(m.timestamp&&$util.isInteger(m.timestamp.low)&&$util.isInteger(m.timestamp.high)))return"timestamp: integer|Long expected"}return null};HeartBeatResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.HeartBeatResp)return d;var m=new $root.humanboy_proto.HeartBeatResp;if(d.uid!=null){m.uid=d.uid>>>0}if(d.timestamp!=null){if($util.Long)(m.timestamp=$util.Long.fromValue(d.timestamp)).unsigned=false;else if(typeof d.timestamp==="string")m.timestamp=parseInt(d.timestamp,10);else if(typeof d.timestamp==="number")m.timestamp=d.timestamp;else if(typeof d.timestamp==="object")m.timestamp=new $util.LongBits(d.timestamp.low>>>0,d.timestamp.high>>>0).toNumber()}return m};HeartBeatResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;if($util.Long){var n=new $util.Long(0,0,false);d.timestamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.timestamp=o.longs===String?"0":0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.timestamp!=null&&m.hasOwnProperty("timestamp")){if(typeof m.timestamp==="number")d.timestamp=o.longs===String?String(m.timestamp):m.timestamp;else d.timestamp=o.longs===String?$util.Long.prototype.toString.call(m.timestamp):o.longs===Number?new $util.LongBits(m.timestamp.low>>>0,m.timestamp.high>>>0).toNumber():m.timestamp}return d};HeartBeatResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return HeartBeatResp}();humanboy_proto.LoginReq=function(){function LoginReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LoginReq.prototype.version="";LoginReq.prototype.token="";LoginReq.prototype.client_type=0;LoginReq.create=function create(properties){return new LoginReq(properties)};LoginReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.version!=null&&Object.hasOwnProperty.call(m,"version"))w.uint32(10).string(m.version);if(m.token!=null&&Object.hasOwnProperty.call(m,"token"))w.uint32(18).string(m.token);if(m.client_type!=null&&Object.hasOwnProperty.call(m,"client_type"))w.uint32(24).int32(m.client_type);return w};LoginReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LoginReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.LoginReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.version=r.string();break;case 2:m.token=r.string();break;case 3:m.client_type=r.int32();break;default:r.skipType(t&7);break}}return m};LoginReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LoginReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.version!=null&&m.hasOwnProperty("version")){if(!$util.isString(m.version))return"version: string expected"}if(m.token!=null&&m.hasOwnProperty("token")){if(!$util.isString(m.token))return"token: string expected"}if(m.client_type!=null&&m.hasOwnProperty("client_type")){switch(m.client_type){default:return"client_type: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:case 16:break}}return null};LoginReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.LoginReq)return d;var m=new $root.humanboy_proto.LoginReq;if(d.version!=null){m.version=String(d.version)}if(d.token!=null){m.token=String(d.token)}switch(d.client_type){case"Dummy":case 0:m.client_type=0;break;case"Normal":case 1:m.client_type=1;break;case"OverSeas":case 2:m.client_type=2;break;case"H5":case 3:m.client_type=3;break;case"H5OverSeas":case 4:m.client_type=4;break;case"H5Web":case 5:m.client_type=5;break;case"H5WebOverSeas":case 6:m.client_type=6;break;case"H5VietnamLasted":case 7:m.client_type=7;break;case"H5WebVietnamLasted":case 8:m.client_type=8;break;case"H5CowboyWeb":case 9:m.client_type=9;break;case"H5Thailand":case 10:m.client_type=10;break;case"H5WebThailand":case 11:m.client_type=11;break;case"H5Arab":case 12:m.client_type=12;break;case"H5Hindi":case 13:m.client_type=13;break;case"H5Mempoker":case 14:m.client_type=14;break;case"PC":case 15:m.client_type=15;break;case"WPTG":case 16:m.client_type=16;break}return m};LoginReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.version="";d.token="";d.client_type=o.enums===String?"Dummy":0}if(m.version!=null&&m.hasOwnProperty("version")){d.version=m.version}if(m.token!=null&&m.hasOwnProperty("token")){d.token=m.token}if(m.client_type!=null&&m.hasOwnProperty("client_type")){d.client_type=o.enums===String?$root.humanboy_proto.ClientType[m.client_type]:m.client_type}return d};LoginReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LoginReq}();humanboy_proto.LoginResp=function(){function LoginResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LoginResp.prototype.code=0;LoginResp.prototype.roomid=0;LoginResp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;LoginResp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;LoginResp.create=function create(properties){return new LoginResp(properties)};LoginResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(16).uint32(m.roomid);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);return w};LoginResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LoginResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.LoginResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.roomid=r.uint32();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;default:r.skipType(t&7);break}}return m};LoginResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LoginResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}return null};LoginResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.LoginResp)return d;var m=new $root.humanboy_proto.LoginResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.roomid!=null){m.roomid=d.roomid>>>0}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}return m};LoginResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.roomid=0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}return d};LoginResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LoginResp}();humanboy_proto.JoinRoomReq=function(){function JoinRoomReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JoinRoomReq.prototype.roomid=0;JoinRoomReq.create=function create(properties){return new JoinRoomReq(properties)};JoinRoomReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).uint32(m.roomid);return w};JoinRoomReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JoinRoomReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.JoinRoomReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.uint32();break;default:r.skipType(t&7);break}}return m};JoinRoomReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JoinRoomReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}return null};JoinRoomReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.JoinRoomReq)return d;var m=new $root.humanboy_proto.JoinRoomReq;if(d.roomid!=null){m.roomid=d.roomid>>>0}return m};JoinRoomReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomid=0}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}return d};JoinRoomReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JoinRoomReq}();humanboy_proto.JoinRoomResp=function(){function JoinRoomResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JoinRoomResp.prototype.code=0;JoinRoomResp.prototype.roomid=0;JoinRoomResp.prototype.roomuuid=$util.Long?$util.Long.fromBits(0,0,true):0;JoinRoomResp.create=function create(properties){return new JoinRoomResp(properties)};JoinRoomResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(16).uint32(m.roomid);if(m.roomuuid!=null&&Object.hasOwnProperty.call(m,"roomuuid"))w.uint32(24).uint64(m.roomuuid);return w};JoinRoomResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JoinRoomResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.JoinRoomResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.roomid=r.uint32();break;case 3:m.roomuuid=r.uint64();break;default:r.skipType(t&7);break}}return m};JoinRoomResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JoinRoomResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(!$util.isInteger(m.roomuuid)&&!(m.roomuuid&&$util.isInteger(m.roomuuid.low)&&$util.isInteger(m.roomuuid.high)))return"roomuuid: integer|Long expected"}return null};JoinRoomResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.JoinRoomResp)return d;var m=new $root.humanboy_proto.JoinRoomResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.roomid!=null){m.roomid=d.roomid>>>0}if(d.roomuuid!=null){if($util.Long)(m.roomuuid=$util.Long.fromValue(d.roomuuid)).unsigned=true;else if(typeof d.roomuuid==="string")m.roomuuid=parseInt(d.roomuuid,10);else if(typeof d.roomuuid==="number")m.roomuuid=d.roomuuid;else if(typeof d.roomuuid==="object")m.roomuuid=new $util.LongBits(d.roomuuid.low>>>0,d.roomuuid.high>>>0).toNumber(true)}return m};JoinRoomResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.roomid=0;if($util.Long){var n=new $util.Long(0,0,true);d.roomuuid=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.roomuuid=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.roomuuid!=null&&m.hasOwnProperty("roomuuid")){if(typeof m.roomuuid==="number")d.roomuuid=o.longs===String?String(m.roomuuid):m.roomuuid;else d.roomuuid=o.longs===String?$util.Long.prototype.toString.call(m.roomuuid):o.longs===Number?new $util.LongBits(m.roomuuid.low>>>0,m.roomuuid.high>>>0).toNumber(true):m.roomuuid}return d};JoinRoomResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JoinRoomResp}();humanboy_proto.RoomParam=function(){function RoomParam(p){this.amountLevel=[];this.oddsDetail=[];this.pictureCn=[];this.pictureEn=[];this.totalAmountLevel=[];this.pictureVn=[];this.ruleByLanguage=[];this.toWpkRulePic={};if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomParam.prototype.roomid=0;RoomParam.prototype.amountLevel=$util.emptyArray;RoomParam.prototype.oddsDetail=$util.emptyArray;RoomParam.prototype.limitPlayers=0;RoomParam.prototype.deskType=0;RoomParam.prototype.smallBet=0;RoomParam.prototype.pictureCn=$util.emptyArray;RoomParam.prototype.pictureEn=$util.emptyArray;RoomParam.prototype.upDealerMoney=$util.Long?$util.Long.fromBits(0,0,true):0;RoomParam.prototype.dealerCount=0;RoomParam.prototype.singleMaxStock=0;RoomParam.prototype.downDealerMoney=$util.Long?$util.Long.fromBits(0,0,true):0;RoomParam.prototype.moneyPerStock=$util.Long?$util.Long.fromBits(0,0,true):0;RoomParam.prototype.totalStockNum=0;RoomParam.prototype.shareLimitAmount=$util.Long?$util.Long.fromBits(0,0,true):0;RoomParam.prototype.stdJackpotBet=$util.Long?$util.Long.fromBits(0,0,true):0;RoomParam.prototype.version=0;RoomParam.prototype.totalAmountLevel=$util.emptyArray;RoomParam.prototype.pictureVn=$util.emptyArray;RoomParam.prototype.ruleByLanguage=$util.emptyArray;RoomParam.prototype.langVersion=0;RoomParam.prototype.rulePic="";RoomParam.prototype.toWpkRulePic=$util.emptyObject;RoomParam.create=function create(properties){return new RoomParam(properties)};RoomParam.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomid!=null&&Object.hasOwnProperty.call(m,"roomid"))w.uint32(8).uint32(m.roomid);if(m.amountLevel!=null&&m.amountLevel.length){w.uint32(18).fork();for(var i=0;i<m.amountLevel.length;++i)w.uint64(m.amountLevel[i]);w.ldelim()}if(m.oddsDetail!=null&&m.oddsDetail.length){for(var i=0;i<m.oddsDetail.length;++i)$root.humanboy_proto.OddsDetail.encode(m.oddsDetail[i],w.uint32(26).fork()).ldelim()}if(m.limitPlayers!=null&&Object.hasOwnProperty.call(m,"limitPlayers"))w.uint32(40).uint32(m.limitPlayers);if(m.deskType!=null&&Object.hasOwnProperty.call(m,"deskType"))w.uint32(48).uint32(m.deskType);if(m.smallBet!=null&&Object.hasOwnProperty.call(m,"smallBet"))w.uint32(56).uint32(m.smallBet);if(m.pictureCn!=null&&m.pictureCn.length){for(var i=0;i<m.pictureCn.length;++i)w.uint32(66).string(m.pictureCn[i])}if(m.pictureEn!=null&&m.pictureEn.length){for(var i=0;i<m.pictureEn.length;++i)w.uint32(74).string(m.pictureEn[i])}if(m.upDealerMoney!=null&&Object.hasOwnProperty.call(m,"upDealerMoney"))w.uint32(80).uint64(m.upDealerMoney);if(m.dealerCount!=null&&Object.hasOwnProperty.call(m,"dealerCount"))w.uint32(96).uint32(m.dealerCount);if(m.singleMaxStock!=null&&Object.hasOwnProperty.call(m,"singleMaxStock"))w.uint32(104).uint32(m.singleMaxStock);if(m.downDealerMoney!=null&&Object.hasOwnProperty.call(m,"downDealerMoney"))w.uint32(112).uint64(m.downDealerMoney);if(m.moneyPerStock!=null&&Object.hasOwnProperty.call(m,"moneyPerStock"))w.uint32(120).uint64(m.moneyPerStock);if(m.totalStockNum!=null&&Object.hasOwnProperty.call(m,"totalStockNum"))w.uint32(128).uint32(m.totalStockNum);if(m.shareLimitAmount!=null&&Object.hasOwnProperty.call(m,"shareLimitAmount"))w.uint32(136).uint64(m.shareLimitAmount);if(m.stdJackpotBet!=null&&Object.hasOwnProperty.call(m,"stdJackpotBet"))w.uint32(144).uint64(m.stdJackpotBet);if(m.version!=null&&Object.hasOwnProperty.call(m,"version"))w.uint32(152).uint32(m.version);if(m.totalAmountLevel!=null&&m.totalAmountLevel.length){w.uint32(162).fork();for(var i=0;i<m.totalAmountLevel.length;++i)w.uint64(m.totalAmountLevel[i]);w.ldelim()}if(m.pictureVn!=null&&m.pictureVn.length){for(var i=0;i<m.pictureVn.length;++i)w.uint32(170).string(m.pictureVn[i])}if(m.ruleByLanguage!=null&&m.ruleByLanguage.length){for(var i=0;i<m.ruleByLanguage.length;++i)$root.humanboy_proto.LanguageItem.encode(m.ruleByLanguage[i],w.uint32(178).fork()).ldelim()}if(m.langVersion!=null&&Object.hasOwnProperty.call(m,"langVersion"))w.uint32(184).int32(m.langVersion);if(m.rulePic!=null&&Object.hasOwnProperty.call(m,"rulePic"))w.uint32(194).string(m.rulePic);if(m.toWpkRulePic!=null&&Object.hasOwnProperty.call(m,"toWpkRulePic")){for(var ks=Object.keys(m.toWpkRulePic),i=0;i<ks.length;++i){w.uint32(202).fork().uint32(10).string(ks[i]).uint32(18).string(m.toWpkRulePic[ks[i]]).ldelim()}}return w};RoomParam.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomParam.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.RoomParam,k;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomid=r.uint32();break;case 2:if(!(m.amountLevel&&m.amountLevel.length))m.amountLevel=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.amountLevel.push(r.uint64())}else m.amountLevel.push(r.uint64());break;case 3:if(!(m.oddsDetail&&m.oddsDetail.length))m.oddsDetail=[];m.oddsDetail.push($root.humanboy_proto.OddsDetail.decode(r,r.uint32()));break;case 5:m.limitPlayers=r.uint32();break;case 6:m.deskType=r.uint32();break;case 7:m.smallBet=r.uint32();break;case 8:if(!(m.pictureCn&&m.pictureCn.length))m.pictureCn=[];m.pictureCn.push(r.string());break;case 9:if(!(m.pictureEn&&m.pictureEn.length))m.pictureEn=[];m.pictureEn.push(r.string());break;case 10:m.upDealerMoney=r.uint64();break;case 12:m.dealerCount=r.uint32();break;case 13:m.singleMaxStock=r.uint32();break;case 14:m.downDealerMoney=r.uint64();break;case 15:m.moneyPerStock=r.uint64();break;case 16:m.totalStockNum=r.uint32();break;case 17:m.shareLimitAmount=r.uint64();break;case 18:m.stdJackpotBet=r.uint64();break;case 19:m.version=r.uint32();break;case 20:if(!(m.totalAmountLevel&&m.totalAmountLevel.length))m.totalAmountLevel=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.totalAmountLevel.push(r.uint64())}else m.totalAmountLevel.push(r.uint64());break;case 21:if(!(m.pictureVn&&m.pictureVn.length))m.pictureVn=[];m.pictureVn.push(r.string());break;case 22:if(!(m.ruleByLanguage&&m.ruleByLanguage.length))m.ruleByLanguage=[];m.ruleByLanguage.push($root.humanboy_proto.LanguageItem.decode(r,r.uint32()));break;case 23:m.langVersion=r.int32();break;case 24:m.rulePic=r.string();break;case 25:r.skip().pos++;if(m.toWpkRulePic===$util.emptyObject)m.toWpkRulePic={};k=r.string();r.pos++;m.toWpkRulePic[k]=r.string();break;default:r.skipType(t&7);break}}return m};RoomParam.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomParam.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomid!=null&&m.hasOwnProperty("roomid")){if(!$util.isInteger(m.roomid))return"roomid: integer expected"}if(m.amountLevel!=null&&m.hasOwnProperty("amountLevel")){if(!Array.isArray(m.amountLevel))return"amountLevel: array expected";for(var i=0;i<m.amountLevel.length;++i){if(!$util.isInteger(m.amountLevel[i])&&!(m.amountLevel[i]&&$util.isInteger(m.amountLevel[i].low)&&$util.isInteger(m.amountLevel[i].high)))return"amountLevel: integer|Long[] expected"}}if(m.oddsDetail!=null&&m.hasOwnProperty("oddsDetail")){if(!Array.isArray(m.oddsDetail))return"oddsDetail: array expected";for(var i=0;i<m.oddsDetail.length;++i){{var e=$root.humanboy_proto.OddsDetail.verify(m.oddsDetail[i]);if(e)return"oddsDetail."+e}}}if(m.limitPlayers!=null&&m.hasOwnProperty("limitPlayers")){if(!$util.isInteger(m.limitPlayers))return"limitPlayers: integer expected"}if(m.deskType!=null&&m.hasOwnProperty("deskType")){if(!$util.isInteger(m.deskType))return"deskType: integer expected"}if(m.smallBet!=null&&m.hasOwnProperty("smallBet")){if(!$util.isInteger(m.smallBet))return"smallBet: integer expected"}if(m.pictureCn!=null&&m.hasOwnProperty("pictureCn")){if(!Array.isArray(m.pictureCn))return"pictureCn: array expected";for(var i=0;i<m.pictureCn.length;++i){if(!$util.isString(m.pictureCn[i]))return"pictureCn: string[] expected"}}if(m.pictureEn!=null&&m.hasOwnProperty("pictureEn")){if(!Array.isArray(m.pictureEn))return"pictureEn: array expected";for(var i=0;i<m.pictureEn.length;++i){if(!$util.isString(m.pictureEn[i]))return"pictureEn: string[] expected"}}if(m.upDealerMoney!=null&&m.hasOwnProperty("upDealerMoney")){if(!$util.isInteger(m.upDealerMoney)&&!(m.upDealerMoney&&$util.isInteger(m.upDealerMoney.low)&&$util.isInteger(m.upDealerMoney.high)))return"upDealerMoney: integer|Long expected"}if(m.dealerCount!=null&&m.hasOwnProperty("dealerCount")){if(!$util.isInteger(m.dealerCount))return"dealerCount: integer expected"}if(m.singleMaxStock!=null&&m.hasOwnProperty("singleMaxStock")){if(!$util.isInteger(m.singleMaxStock))return"singleMaxStock: integer expected"}if(m.downDealerMoney!=null&&m.hasOwnProperty("downDealerMoney")){if(!$util.isInteger(m.downDealerMoney)&&!(m.downDealerMoney&&$util.isInteger(m.downDealerMoney.low)&&$util.isInteger(m.downDealerMoney.high)))return"downDealerMoney: integer|Long expected"}if(m.moneyPerStock!=null&&m.hasOwnProperty("moneyPerStock")){if(!$util.isInteger(m.moneyPerStock)&&!(m.moneyPerStock&&$util.isInteger(m.moneyPerStock.low)&&$util.isInteger(m.moneyPerStock.high)))return"moneyPerStock: integer|Long expected"}if(m.totalStockNum!=null&&m.hasOwnProperty("totalStockNum")){if(!$util.isInteger(m.totalStockNum))return"totalStockNum: integer expected"}if(m.shareLimitAmount!=null&&m.hasOwnProperty("shareLimitAmount")){if(!$util.isInteger(m.shareLimitAmount)&&!(m.shareLimitAmount&&$util.isInteger(m.shareLimitAmount.low)&&$util.isInteger(m.shareLimitAmount.high)))return"shareLimitAmount: integer|Long expected"}if(m.stdJackpotBet!=null&&m.hasOwnProperty("stdJackpotBet")){if(!$util.isInteger(m.stdJackpotBet)&&!(m.stdJackpotBet&&$util.isInteger(m.stdJackpotBet.low)&&$util.isInteger(m.stdJackpotBet.high)))return"stdJackpotBet: integer|Long expected"}if(m.version!=null&&m.hasOwnProperty("version")){if(!$util.isInteger(m.version))return"version: integer expected"}if(m.totalAmountLevel!=null&&m.hasOwnProperty("totalAmountLevel")){if(!Array.isArray(m.totalAmountLevel))return"totalAmountLevel: array expected";for(var i=0;i<m.totalAmountLevel.length;++i){if(!$util.isInteger(m.totalAmountLevel[i])&&!(m.totalAmountLevel[i]&&$util.isInteger(m.totalAmountLevel[i].low)&&$util.isInteger(m.totalAmountLevel[i].high)))return"totalAmountLevel: integer|Long[] expected"}}if(m.pictureVn!=null&&m.hasOwnProperty("pictureVn")){if(!Array.isArray(m.pictureVn))return"pictureVn: array expected";for(var i=0;i<m.pictureVn.length;++i){if(!$util.isString(m.pictureVn[i]))return"pictureVn: string[] expected"}}if(m.ruleByLanguage!=null&&m.hasOwnProperty("ruleByLanguage")){if(!Array.isArray(m.ruleByLanguage))return"ruleByLanguage: array expected";for(var i=0;i<m.ruleByLanguage.length;++i){{var e=$root.humanboy_proto.LanguageItem.verify(m.ruleByLanguage[i]);if(e)return"ruleByLanguage."+e}}}if(m.langVersion!=null&&m.hasOwnProperty("langVersion")){if(!$util.isInteger(m.langVersion))return"langVersion: integer expected"}if(m.rulePic!=null&&m.hasOwnProperty("rulePic")){if(!$util.isString(m.rulePic))return"rulePic: string expected"}if(m.toWpkRulePic!=null&&m.hasOwnProperty("toWpkRulePic")){if(!$util.isObject(m.toWpkRulePic))return"toWpkRulePic: object expected";var k=Object.keys(m.toWpkRulePic);for(var i=0;i<k.length;++i){if(!$util.isString(m.toWpkRulePic[k[i]]))return"toWpkRulePic: string{k:string} expected"}}return null};RoomParam.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.RoomParam)return d;var m=new $root.humanboy_proto.RoomParam;if(d.roomid!=null){m.roomid=d.roomid>>>0}if(d.amountLevel){if(!Array.isArray(d.amountLevel))throw TypeError(".humanboy_proto.RoomParam.amountLevel: array expected");m.amountLevel=[];for(var i=0;i<d.amountLevel.length;++i){if($util.Long)(m.amountLevel[i]=$util.Long.fromValue(d.amountLevel[i])).unsigned=true;else if(typeof d.amountLevel[i]==="string")m.amountLevel[i]=parseInt(d.amountLevel[i],10);else if(typeof d.amountLevel[i]==="number")m.amountLevel[i]=d.amountLevel[i];else if(typeof d.amountLevel[i]==="object")m.amountLevel[i]=new $util.LongBits(d.amountLevel[i].low>>>0,d.amountLevel[i].high>>>0).toNumber(true)}}if(d.oddsDetail){if(!Array.isArray(d.oddsDetail))throw TypeError(".humanboy_proto.RoomParam.oddsDetail: array expected");m.oddsDetail=[];for(var i=0;i<d.oddsDetail.length;++i){if(typeof d.oddsDetail[i]!=="object")throw TypeError(".humanboy_proto.RoomParam.oddsDetail: object expected");m.oddsDetail[i]=$root.humanboy_proto.OddsDetail.fromObject(d.oddsDetail[i])}}if(d.limitPlayers!=null){m.limitPlayers=d.limitPlayers>>>0}if(d.deskType!=null){m.deskType=d.deskType>>>0}if(d.smallBet!=null){m.smallBet=d.smallBet>>>0}if(d.pictureCn){if(!Array.isArray(d.pictureCn))throw TypeError(".humanboy_proto.RoomParam.pictureCn: array expected");m.pictureCn=[];for(var i=0;i<d.pictureCn.length;++i){m.pictureCn[i]=String(d.pictureCn[i])}}if(d.pictureEn){if(!Array.isArray(d.pictureEn))throw TypeError(".humanboy_proto.RoomParam.pictureEn: array expected");m.pictureEn=[];for(var i=0;i<d.pictureEn.length;++i){m.pictureEn[i]=String(d.pictureEn[i])}}if(d.upDealerMoney!=null){if($util.Long)(m.upDealerMoney=$util.Long.fromValue(d.upDealerMoney)).unsigned=true;else if(typeof d.upDealerMoney==="string")m.upDealerMoney=parseInt(d.upDealerMoney,10);else if(typeof d.upDealerMoney==="number")m.upDealerMoney=d.upDealerMoney;else if(typeof d.upDealerMoney==="object")m.upDealerMoney=new $util.LongBits(d.upDealerMoney.low>>>0,d.upDealerMoney.high>>>0).toNumber(true)}if(d.dealerCount!=null){m.dealerCount=d.dealerCount>>>0}if(d.singleMaxStock!=null){m.singleMaxStock=d.singleMaxStock>>>0}if(d.downDealerMoney!=null){if($util.Long)(m.downDealerMoney=$util.Long.fromValue(d.downDealerMoney)).unsigned=true;else if(typeof d.downDealerMoney==="string")m.downDealerMoney=parseInt(d.downDealerMoney,10);else if(typeof d.downDealerMoney==="number")m.downDealerMoney=d.downDealerMoney;else if(typeof d.downDealerMoney==="object")m.downDealerMoney=new $util.LongBits(d.downDealerMoney.low>>>0,d.downDealerMoney.high>>>0).toNumber(true)}if(d.moneyPerStock!=null){if($util.Long)(m.moneyPerStock=$util.Long.fromValue(d.moneyPerStock)).unsigned=true;else if(typeof d.moneyPerStock==="string")m.moneyPerStock=parseInt(d.moneyPerStock,10);else if(typeof d.moneyPerStock==="number")m.moneyPerStock=d.moneyPerStock;else if(typeof d.moneyPerStock==="object")m.moneyPerStock=new $util.LongBits(d.moneyPerStock.low>>>0,d.moneyPerStock.high>>>0).toNumber(true)}if(d.totalStockNum!=null){m.totalStockNum=d.totalStockNum>>>0}if(d.shareLimitAmount!=null){if($util.Long)(m.shareLimitAmount=$util.Long.fromValue(d.shareLimitAmount)).unsigned=true;else if(typeof d.shareLimitAmount==="string")m.shareLimitAmount=parseInt(d.shareLimitAmount,10);else if(typeof d.shareLimitAmount==="number")m.shareLimitAmount=d.shareLimitAmount;else if(typeof d.shareLimitAmount==="object")m.shareLimitAmount=new $util.LongBits(d.shareLimitAmount.low>>>0,d.shareLimitAmount.high>>>0).toNumber(true)}if(d.stdJackpotBet!=null){if($util.Long)(m.stdJackpotBet=$util.Long.fromValue(d.stdJackpotBet)).unsigned=true;else if(typeof d.stdJackpotBet==="string")m.stdJackpotBet=parseInt(d.stdJackpotBet,10);else if(typeof d.stdJackpotBet==="number")m.stdJackpotBet=d.stdJackpotBet;else if(typeof d.stdJackpotBet==="object")m.stdJackpotBet=new $util.LongBits(d.stdJackpotBet.low>>>0,d.stdJackpotBet.high>>>0).toNumber(true)}if(d.version!=null){m.version=d.version>>>0}if(d.totalAmountLevel){if(!Array.isArray(d.totalAmountLevel))throw TypeError(".humanboy_proto.RoomParam.totalAmountLevel: array expected");m.totalAmountLevel=[];for(var i=0;i<d.totalAmountLevel.length;++i){if($util.Long)(m.totalAmountLevel[i]=$util.Long.fromValue(d.totalAmountLevel[i])).unsigned=true;else if(typeof d.totalAmountLevel[i]==="string")m.totalAmountLevel[i]=parseInt(d.totalAmountLevel[i],10);else if(typeof d.totalAmountLevel[i]==="number")m.totalAmountLevel[i]=d.totalAmountLevel[i];else if(typeof d.totalAmountLevel[i]==="object")m.totalAmountLevel[i]=new $util.LongBits(d.totalAmountLevel[i].low>>>0,d.totalAmountLevel[i].high>>>0).toNumber(true)}}if(d.pictureVn){if(!Array.isArray(d.pictureVn))throw TypeError(".humanboy_proto.RoomParam.pictureVn: array expected");m.pictureVn=[];for(var i=0;i<d.pictureVn.length;++i){m.pictureVn[i]=String(d.pictureVn[i])}}if(d.ruleByLanguage){if(!Array.isArray(d.ruleByLanguage))throw TypeError(".humanboy_proto.RoomParam.ruleByLanguage: array expected");m.ruleByLanguage=[];for(var i=0;i<d.ruleByLanguage.length;++i){if(typeof d.ruleByLanguage[i]!=="object")throw TypeError(".humanboy_proto.RoomParam.ruleByLanguage: object expected");m.ruleByLanguage[i]=$root.humanboy_proto.LanguageItem.fromObject(d.ruleByLanguage[i])}}if(d.langVersion!=null){m.langVersion=d.langVersion|0}if(d.rulePic!=null){m.rulePic=String(d.rulePic)}if(d.toWpkRulePic){if(typeof d.toWpkRulePic!=="object")throw TypeError(".humanboy_proto.RoomParam.toWpkRulePic: object expected");m.toWpkRulePic={};for(var ks=Object.keys(d.toWpkRulePic),i=0;i<ks.length;++i){m.toWpkRulePic[ks[i]]=String(d.toWpkRulePic[ks[i]])}}return m};RoomParam.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.amountLevel=[];d.oddsDetail=[];d.pictureCn=[];d.pictureEn=[];d.totalAmountLevel=[];d.pictureVn=[];d.ruleByLanguage=[]}if(o.objects||o.defaults){d.toWpkRulePic={}}if(o.defaults){d.roomid=0;d.limitPlayers=0;d.deskType=0;d.smallBet=0;if($util.Long){var n=new $util.Long(0,0,true);d.upDealerMoney=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.upDealerMoney=o.longs===String?"0":0;d.dealerCount=0;d.singleMaxStock=0;if($util.Long){var n=new $util.Long(0,0,true);d.downDealerMoney=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.downDealerMoney=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.moneyPerStock=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.moneyPerStock=o.longs===String?"0":0;d.totalStockNum=0;if($util.Long){var n=new $util.Long(0,0,true);d.shareLimitAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.shareLimitAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.stdJackpotBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.stdJackpotBet=o.longs===String?"0":0;d.version=0;d.langVersion=0;d.rulePic=""}if(m.roomid!=null&&m.hasOwnProperty("roomid")){d.roomid=m.roomid}if(m.amountLevel&&m.amountLevel.length){d.amountLevel=[];for(var j=0;j<m.amountLevel.length;++j){if(typeof m.amountLevel[j]==="number")d.amountLevel[j]=o.longs===String?String(m.amountLevel[j]):m.amountLevel[j];else d.amountLevel[j]=o.longs===String?$util.Long.prototype.toString.call(m.amountLevel[j]):o.longs===Number?new $util.LongBits(m.amountLevel[j].low>>>0,m.amountLevel[j].high>>>0).toNumber(true):m.amountLevel[j]}}if(m.oddsDetail&&m.oddsDetail.length){d.oddsDetail=[];for(var j=0;j<m.oddsDetail.length;++j){d.oddsDetail[j]=$root.humanboy_proto.OddsDetail.toObject(m.oddsDetail[j],o)}}if(m.limitPlayers!=null&&m.hasOwnProperty("limitPlayers")){d.limitPlayers=m.limitPlayers}if(m.deskType!=null&&m.hasOwnProperty("deskType")){d.deskType=m.deskType}if(m.smallBet!=null&&m.hasOwnProperty("smallBet")){d.smallBet=m.smallBet}if(m.pictureCn&&m.pictureCn.length){d.pictureCn=[];for(var j=0;j<m.pictureCn.length;++j){d.pictureCn[j]=m.pictureCn[j]}}if(m.pictureEn&&m.pictureEn.length){d.pictureEn=[];for(var j=0;j<m.pictureEn.length;++j){d.pictureEn[j]=m.pictureEn[j]}}if(m.upDealerMoney!=null&&m.hasOwnProperty("upDealerMoney")){if(typeof m.upDealerMoney==="number")d.upDealerMoney=o.longs===String?String(m.upDealerMoney):m.upDealerMoney;else d.upDealerMoney=o.longs===String?$util.Long.prototype.toString.call(m.upDealerMoney):o.longs===Number?new $util.LongBits(m.upDealerMoney.low>>>0,m.upDealerMoney.high>>>0).toNumber(true):m.upDealerMoney}if(m.dealerCount!=null&&m.hasOwnProperty("dealerCount")){d.dealerCount=m.dealerCount}if(m.singleMaxStock!=null&&m.hasOwnProperty("singleMaxStock")){d.singleMaxStock=m.singleMaxStock}if(m.downDealerMoney!=null&&m.hasOwnProperty("downDealerMoney")){if(typeof m.downDealerMoney==="number")d.downDealerMoney=o.longs===String?String(m.downDealerMoney):m.downDealerMoney;else d.downDealerMoney=o.longs===String?$util.Long.prototype.toString.call(m.downDealerMoney):o.longs===Number?new $util.LongBits(m.downDealerMoney.low>>>0,m.downDealerMoney.high>>>0).toNumber(true):m.downDealerMoney}if(m.moneyPerStock!=null&&m.hasOwnProperty("moneyPerStock")){if(typeof m.moneyPerStock==="number")d.moneyPerStock=o.longs===String?String(m.moneyPerStock):m.moneyPerStock;else d.moneyPerStock=o.longs===String?$util.Long.prototype.toString.call(m.moneyPerStock):o.longs===Number?new $util.LongBits(m.moneyPerStock.low>>>0,m.moneyPerStock.high>>>0).toNumber(true):m.moneyPerStock}if(m.totalStockNum!=null&&m.hasOwnProperty("totalStockNum")){d.totalStockNum=m.totalStockNum}if(m.shareLimitAmount!=null&&m.hasOwnProperty("shareLimitAmount")){if(typeof m.shareLimitAmount==="number")d.shareLimitAmount=o.longs===String?String(m.shareLimitAmount):m.shareLimitAmount;else d.shareLimitAmount=o.longs===String?$util.Long.prototype.toString.call(m.shareLimitAmount):o.longs===Number?new $util.LongBits(m.shareLimitAmount.low>>>0,m.shareLimitAmount.high>>>0).toNumber(true):m.shareLimitAmount}if(m.stdJackpotBet!=null&&m.hasOwnProperty("stdJackpotBet")){if(typeof m.stdJackpotBet==="number")d.stdJackpotBet=o.longs===String?String(m.stdJackpotBet):m.stdJackpotBet;else d.stdJackpotBet=o.longs===String?$util.Long.prototype.toString.call(m.stdJackpotBet):o.longs===Number?new $util.LongBits(m.stdJackpotBet.low>>>0,m.stdJackpotBet.high>>>0).toNumber(true):m.stdJackpotBet}if(m.version!=null&&m.hasOwnProperty("version")){d.version=m.version}if(m.totalAmountLevel&&m.totalAmountLevel.length){d.totalAmountLevel=[];for(var j=0;j<m.totalAmountLevel.length;++j){if(typeof m.totalAmountLevel[j]==="number")d.totalAmountLevel[j]=o.longs===String?String(m.totalAmountLevel[j]):m.totalAmountLevel[j];else d.totalAmountLevel[j]=o.longs===String?$util.Long.prototype.toString.call(m.totalAmountLevel[j]):o.longs===Number?new $util.LongBits(m.totalAmountLevel[j].low>>>0,m.totalAmountLevel[j].high>>>0).toNumber(true):m.totalAmountLevel[j]}}if(m.pictureVn&&m.pictureVn.length){d.pictureVn=[];for(var j=0;j<m.pictureVn.length;++j){d.pictureVn[j]=m.pictureVn[j]}}if(m.ruleByLanguage&&m.ruleByLanguage.length){d.ruleByLanguage=[];for(var j=0;j<m.ruleByLanguage.length;++j){d.ruleByLanguage[j]=$root.humanboy_proto.LanguageItem.toObject(m.ruleByLanguage[j],o)}}if(m.langVersion!=null&&m.hasOwnProperty("langVersion")){d.langVersion=m.langVersion}if(m.rulePic!=null&&m.hasOwnProperty("rulePic")){d.rulePic=m.rulePic}var ks2;if(m.toWpkRulePic&&(ks2=Object.keys(m.toWpkRulePic)).length){d.toWpkRulePic={};for(var j=0;j<ks2.length;++j){d.toWpkRulePic[ks2[j]]=m.toWpkRulePic[ks2[j]]}}return d};RoomParam.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomParam}();humanboy_proto.LanguageItem=function(){function LanguageItem(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LanguageItem.prototype.lang="";LanguageItem.prototype.value="";LanguageItem.prototype.plat=0;LanguageItem.create=function create(properties){return new LanguageItem(properties)};LanguageItem.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.lang!=null&&Object.hasOwnProperty.call(m,"lang"))w.uint32(10).string(m.lang);if(m.value!=null&&Object.hasOwnProperty.call(m,"value"))w.uint32(18).string(m.value);if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(24).uint32(m.plat);return w};LanguageItem.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LanguageItem.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.LanguageItem;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.lang=r.string();break;case 2:m.value=r.string();break;case 3:m.plat=r.uint32();break;default:r.skipType(t&7);break}}return m};LanguageItem.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LanguageItem.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.lang!=null&&m.hasOwnProperty("lang")){if(!$util.isString(m.lang))return"lang: string expected"}if(m.value!=null&&m.hasOwnProperty("value")){if(!$util.isString(m.value))return"value: string expected"}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}return null};LanguageItem.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.LanguageItem)return d;var m=new $root.humanboy_proto.LanguageItem;if(d.lang!=null){m.lang=String(d.lang)}if(d.value!=null){m.value=String(d.value)}if(d.plat!=null){m.plat=d.plat>>>0}return m};LanguageItem.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.lang="";d.value="";d.plat=0}if(m.lang!=null&&m.hasOwnProperty("lang")){d.lang=m.lang}if(m.value!=null&&m.hasOwnProperty("value")){d.value=m.value}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}return d};LanguageItem.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LanguageItem}();humanboy_proto.OddsDetail=function(){function OddsDetail(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OddsDetail.prototype.option=0;OddsDetail.prototype.odds=$util.Long?$util.Long.fromBits(0,0,true):0;OddsDetail.prototype.limit=$util.Long?$util.Long.fromBits(0,0,true):0;OddsDetail.create=function create(properties){return new OddsDetail(properties)};OddsDetail.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(16).int32(m.option);if(m.odds!=null&&Object.hasOwnProperty.call(m,"odds"))w.uint32(24).uint64(m.odds);if(m.limit!=null&&Object.hasOwnProperty.call(m,"limit"))w.uint32(32).uint64(m.limit);return w};OddsDetail.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OddsDetail.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.OddsDetail;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 2:m.option=r.int32();break;case 3:m.odds=r.uint64();break;case 4:m.limit=r.uint64();break;default:r.skipType(t&7);break}}return m};OddsDetail.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OddsDetail.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.odds!=null&&m.hasOwnProperty("odds")){if(!$util.isInteger(m.odds)&&!(m.odds&&$util.isInteger(m.odds.low)&&$util.isInteger(m.odds.high)))return"odds: integer|Long expected"}if(m.limit!=null&&m.hasOwnProperty("limit")){if(!$util.isInteger(m.limit)&&!(m.limit&&$util.isInteger(m.limit.low)&&$util.isInteger(m.limit.high)))return"limit: integer|Long expected"}return null};OddsDetail.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.OddsDetail)return d;var m=new $root.humanboy_proto.OddsDetail;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.odds!=null){if($util.Long)(m.odds=$util.Long.fromValue(d.odds)).unsigned=true;else if(typeof d.odds==="string")m.odds=parseInt(d.odds,10);else if(typeof d.odds==="number")m.odds=d.odds;else if(typeof d.odds==="object")m.odds=new $util.LongBits(d.odds.low>>>0,d.odds.high>>>0).toNumber(true)}if(d.limit!=null){if($util.Long)(m.limit=$util.Long.fromValue(d.limit)).unsigned=true;else if(typeof d.limit==="string")m.limit=parseInt(d.limit,10);else if(typeof d.limit==="number")m.limit=d.limit;else if(typeof d.limit==="object")m.limit=new $util.LongBits(d.limit.low>>>0,d.limit.high>>>0).toNumber(true)}return m};OddsDetail.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.odds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.odds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.limit=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.limit=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.odds!=null&&m.hasOwnProperty("odds")){if(typeof m.odds==="number")d.odds=o.longs===String?String(m.odds):m.odds;else d.odds=o.longs===String?$util.Long.prototype.toString.call(m.odds):o.longs===Number?new $util.LongBits(m.odds.low>>>0,m.odds.high>>>0).toNumber(true):m.odds}if(m.limit!=null&&m.hasOwnProperty("limit")){if(typeof m.limit==="number")d.limit=o.longs===String?String(m.limit):m.limit;else d.limit=o.longs===String?$util.Long.prototype.toString.call(m.limit):o.longs===Number?new $util.LongBits(m.limit.low>>>0,m.limit.high>>>0).toNumber(true):m.limit}return d};OddsDetail.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OddsDetail}();humanboy_proto.OptionLimit=function(){function OptionLimit(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OptionLimit.prototype.option=0;OptionLimit.prototype.limitAmount=$util.Long?$util.Long.fromBits(0,0,true):0;OptionLimit.create=function create(properties){return new OptionLimit(properties)};OptionLimit.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.limitAmount!=null&&Object.hasOwnProperty.call(m,"limitAmount"))w.uint32(16).uint64(m.limitAmount);return w};OptionLimit.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OptionLimit.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.OptionLimit;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.limitAmount=r.uint64();break;default:r.skipType(t&7);break}}return m};OptionLimit.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OptionLimit.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.limitAmount!=null&&m.hasOwnProperty("limitAmount")){if(!$util.isInteger(m.limitAmount)&&!(m.limitAmount&&$util.isInteger(m.limitAmount.low)&&$util.isInteger(m.limitAmount.high)))return"limitAmount: integer|Long expected"}return null};OptionLimit.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.OptionLimit)return d;var m=new $root.humanboy_proto.OptionLimit;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.limitAmount!=null){if($util.Long)(m.limitAmount=$util.Long.fromValue(d.limitAmount)).unsigned=true;else if(typeof d.limitAmount==="string")m.limitAmount=parseInt(d.limitAmount,10);else if(typeof d.limitAmount==="number")m.limitAmount=d.limitAmount;else if(typeof d.limitAmount==="object")m.limitAmount=new $util.LongBits(d.limitAmount.low>>>0,d.limitAmount.high>>>0).toNumber(true)}return m};OptionLimit.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.limitAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.limitAmount=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.limitAmount!=null&&m.hasOwnProperty("limitAmount")){if(typeof m.limitAmount==="number")d.limitAmount=o.longs===String?String(m.limitAmount):m.limitAmount;else d.limitAmount=o.longs===String?$util.Long.prototype.toString.call(m.limitAmount):o.longs===Number?new $util.LongBits(m.limitAmount.low>>>0,m.limitAmount.high>>>0).toNumber(true):m.limitAmount}return d};OptionLimit.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OptionLimit}();humanboy_proto.GameListReq=function(){function GameListReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameListReq.create=function create(properties){return new GameListReq(properties)};GameListReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};GameListReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameListReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GameListReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};GameListReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameListReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};GameListReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GameListReq)return d;return new $root.humanboy_proto.GameListReq};GameListReq.toObject=function toObject(){return{}};GameListReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameListReq}();humanboy_proto.StartSettlementNotify=function(){function StartSettlementNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StartSettlementNotify.create=function create(properties){return new StartSettlementNotify(properties)};StartSettlementNotify.encode=function encode(m,w){if(!w)w=$Writer.create();return w};StartSettlementNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StartSettlementNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.StartSettlementNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};StartSettlementNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StartSettlementNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};StartSettlementNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.StartSettlementNotify)return d;return new $root.humanboy_proto.StartSettlementNotify};StartSettlementNotify.toObject=function toObject(){return{}};StartSettlementNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StartSettlementNotify}();humanboy_proto.GameListResp=function(){function GameListResp(p){this.gameList=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameListResp.prototype.gameList=$util.emptyArray;GameListResp.create=function create(properties){return new GameListResp(properties)};GameListResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.gameList!=null&&m.gameList.length){for(var i=0;i<m.gameList.length;++i)$root.humanboy_proto.GameSnapShot.encode(m.gameList[i],w.uint32(10).fork()).ldelim()}return w};GameListResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameListResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GameListResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.gameList&&m.gameList.length))m.gameList=[];m.gameList.push($root.humanboy_proto.GameSnapShot.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};GameListResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameListResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.gameList!=null&&m.hasOwnProperty("gameList")){if(!Array.isArray(m.gameList))return"gameList: array expected";for(var i=0;i<m.gameList.length;++i){{var e=$root.humanboy_proto.GameSnapShot.verify(m.gameList[i]);if(e)return"gameList."+e}}}return null};GameListResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GameListResp)return d;var m=new $root.humanboy_proto.GameListResp;if(d.gameList){if(!Array.isArray(d.gameList))throw TypeError(".humanboy_proto.GameListResp.gameList: array expected");m.gameList=[];for(var i=0;i<d.gameList.length;++i){if(typeof d.gameList[i]!=="object")throw TypeError(".humanboy_proto.GameListResp.gameList: object expected");m.gameList[i]=$root.humanboy_proto.GameSnapShot.fromObject(d.gameList[i])}}return m};GameListResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.gameList=[]}if(m.gameList&&m.gameList.length){d.gameList=[];for(var j=0;j<m.gameList.length;++j){d.gameList[j]=$root.humanboy_proto.GameSnapShot.toObject(m.gameList[j],o)}}return d};GameListResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameListResp}();humanboy_proto.GameSnapShot=function(){function GameSnapShot(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameSnapShot.prototype.param=null;GameSnapShot.prototype.playerNum=0;GameSnapShot.create=function create(properties){return new GameSnapShot(properties)};GameSnapShot.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.param!=null&&Object.hasOwnProperty.call(m,"param"))$root.humanboy_proto.RoomParam.encode(m.param,w.uint32(10).fork()).ldelim();if(m.playerNum!=null&&Object.hasOwnProperty.call(m,"playerNum"))w.uint32(16).int32(m.playerNum);return w};GameSnapShot.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameSnapShot.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GameSnapShot;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.param=$root.humanboy_proto.RoomParam.decode(r,r.uint32());break;case 2:m.playerNum=r.int32();break;default:r.skipType(t&7);break}}return m};GameSnapShot.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameSnapShot.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.param!=null&&m.hasOwnProperty("param")){{var e=$root.humanboy_proto.RoomParam.verify(m.param);if(e)return"param."+e}}if(m.playerNum!=null&&m.hasOwnProperty("playerNum")){if(!$util.isInteger(m.playerNum))return"playerNum: integer expected"}return null};GameSnapShot.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GameSnapShot)return d;var m=new $root.humanboy_proto.GameSnapShot;if(d.param!=null){if(typeof d.param!=="object")throw TypeError(".humanboy_proto.GameSnapShot.param: object expected");m.param=$root.humanboy_proto.RoomParam.fromObject(d.param)}if(d.playerNum!=null){m.playerNum=d.playerNum|0}return m};GameSnapShot.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.param=null;d.playerNum=0}if(m.param!=null&&m.hasOwnProperty("param")){d.param=$root.humanboy_proto.RoomParam.toObject(m.param,o)}if(m.playerNum!=null&&m.hasOwnProperty("playerNum")){d.playerNum=m.playerNum}return d};GameSnapShot.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameSnapShot}();humanboy_proto.GameDataSynNotify=function(){function GameDataSynNotify(p){this.optionInfo=[];this.lastResult=[];this.players=[];this.dealer=[];this.optionResults=[];this.betCoinOption=[];this.items=[];this.AutoBetCountList=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameDataSynNotify.prototype.param=null;GameDataSynNotify.prototype.optionInfo=$util.emptyArray;GameDataSynNotify.prototype.lastResult=$util.emptyArray;GameDataSynNotify.prototype.curState=0;GameDataSynNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.players=$util.emptyArray;GameDataSynNotify.prototype.canAuto=false;GameDataSynNotify.prototype.cachedNotifyMsg=null;GameDataSynNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.dealer=$util.emptyArray;GameDataSynNotify.prototype.onDealerList=0;GameDataSynNotify.prototype.jackpotLeftMoney=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.beDealerNum=0;GameDataSynNotify.prototype.optionResults=$util.emptyArray;GameDataSynNotify.prototype.isWaitDownDealer=false;GameDataSynNotify.prototype.surplusStockNum=0;GameDataSynNotify.prototype.occupyStockNum=0;GameDataSynNotify.prototype.showMiddleUpDealerBtn=false;GameDataSynNotify.prototype.totalStockNum=0;GameDataSynNotify.prototype.betCoinOption=$util.emptyArray;GameDataSynNotify.prototype.autoLevel=0;GameDataSynNotify.prototype.items=$util.emptyArray;GameDataSynNotify.prototype.usedAutoBetCount=0;GameDataSynNotify.prototype.selectAutoBetCount=0;GameDataSynNotify.prototype.AutoBetCountList=$util.emptyArray;GameDataSynNotify.prototype.canAdvanceAuto=false;GameDataSynNotify.prototype.BetButtonLimitAmount=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameDataSynNotify.prototype.reachLimitBet=false;GameDataSynNotify.create=function create(properties){return new GameDataSynNotify(properties)};GameDataSynNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.param!=null&&Object.hasOwnProperty.call(m,"param"))$root.humanboy_proto.RoomParam.encode(m.param,w.uint32(10).fork()).ldelim();if(m.optionInfo!=null&&m.optionInfo.length){for(var i=0;i<m.optionInfo.length;++i)$root.humanboy_proto.BetOptionInfo.encode(m.optionInfo[i],w.uint32(18).fork()).ldelim()}if(m.lastResult!=null&&m.lastResult.length){w.uint32(26).fork();for(var i=0;i<m.lastResult.length;++i)w.int32(m.lastResult[i]);w.ldelim()}if(m.curState!=null&&Object.hasOwnProperty.call(m,"curState"))w.uint32(32).int32(m.curState);if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(40).int64(m.nextRoundEndStamp);if(m.players!=null&&m.players.length){for(var i=0;i<m.players.length;++i)$root.humanboy_proto.GamePlayer.encode(m.players[i],w.uint32(50).fork()).ldelim()}if(m.canAuto!=null&&Object.hasOwnProperty.call(m,"canAuto"))w.uint32(64).bool(m.canAuto);if(m.cachedNotifyMsg!=null&&Object.hasOwnProperty.call(m,"cachedNotifyMsg"))$root.humanboy_proto.GameRoundEndNotify.encode(m.cachedNotifyMsg,w.uint32(74).fork()).ldelim();if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(80).int64(m.leftSeconds);if(m.dealer!=null&&m.dealer.length){for(var i=0;i<m.dealer.length;++i)$root.humanboy_proto.DealerPlayerInfo.encode(m.dealer[i],w.uint32(90).fork()).ldelim()}if(m.onDealerList!=null&&Object.hasOwnProperty.call(m,"onDealerList"))w.uint32(96).uint32(m.onDealerList);if(m.jackpotLeftMoney!=null&&Object.hasOwnProperty.call(m,"jackpotLeftMoney"))w.uint32(104).int64(m.jackpotLeftMoney);if(m.beDealerNum!=null&&Object.hasOwnProperty.call(m,"beDealerNum"))w.uint32(112).int32(m.beDealerNum);if(m.optionResults!=null&&m.optionResults.length){for(var i=0;i<m.optionResults.length;++i)$root.humanboy_proto.OptionResults.encode(m.optionResults[i],w.uint32(122).fork()).ldelim()}if(m.isWaitDownDealer!=null&&Object.hasOwnProperty.call(m,"isWaitDownDealer"))w.uint32(128).bool(m.isWaitDownDealer);if(m.surplusStockNum!=null&&Object.hasOwnProperty.call(m,"surplusStockNum"))w.uint32(136).uint32(m.surplusStockNum);if(m.occupyStockNum!=null&&Object.hasOwnProperty.call(m,"occupyStockNum"))w.uint32(144).uint32(m.occupyStockNum);if(m.showMiddleUpDealerBtn!=null&&Object.hasOwnProperty.call(m,"showMiddleUpDealerBtn"))w.uint32(152).bool(m.showMiddleUpDealerBtn);if(m.totalStockNum!=null&&Object.hasOwnProperty.call(m,"totalStockNum"))w.uint32(160).uint32(m.totalStockNum);if(m.betCoinOption!=null&&m.betCoinOption.length){w.uint32(170).fork();for(var i=0;i<m.betCoinOption.length;++i)w.uint64(m.betCoinOption[i]);w.ldelim()}if(m.autoLevel!=null&&Object.hasOwnProperty.call(m,"autoLevel"))w.uint32(176).int32(m.autoLevel);if(m.items!=null&&m.items.length){for(var i=0;i<m.items.length;++i)$root.humanboy_proto.FeeItems.encode(m.items[i],w.uint32(186).fork()).ldelim()}if(m.usedAutoBetCount!=null&&Object.hasOwnProperty.call(m,"usedAutoBetCount"))w.uint32(192).int32(m.usedAutoBetCount);if(m.selectAutoBetCount!=null&&Object.hasOwnProperty.call(m,"selectAutoBetCount"))w.uint32(200).int32(m.selectAutoBetCount);if(m.AutoBetCountList!=null&&m.AutoBetCountList.length){w.uint32(210).fork();for(var i=0;i<m.AutoBetCountList.length;++i)w.int32(m.AutoBetCountList[i]);w.ldelim()}if(m.canAdvanceAuto!=null&&Object.hasOwnProperty.call(m,"canAdvanceAuto"))w.uint32(216).bool(m.canAdvanceAuto);if(m.BetButtonLimitAmount!=null&&Object.hasOwnProperty.call(m,"BetButtonLimitAmount"))w.uint32(224).int64(m.BetButtonLimitAmount);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(232).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(240).int64(m.CalmDownDeadLineTimeStamp);if(m.reachLimitBet!=null&&Object.hasOwnProperty.call(m,"reachLimitBet"))w.uint32(248).bool(m.reachLimitBet);return w};GameDataSynNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameDataSynNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GameDataSynNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.param=$root.humanboy_proto.RoomParam.decode(r,r.uint32());break;case 2:if(!(m.optionInfo&&m.optionInfo.length))m.optionInfo=[];m.optionInfo.push($root.humanboy_proto.BetOptionInfo.decode(r,r.uint32()));break;case 3:if(!(m.lastResult&&m.lastResult.length))m.lastResult=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.lastResult.push(r.int32())}else m.lastResult.push(r.int32());break;case 4:m.curState=r.int32();break;case 5:m.nextRoundEndStamp=r.int64();break;case 6:if(!(m.players&&m.players.length))m.players=[];m.players.push($root.humanboy_proto.GamePlayer.decode(r,r.uint32()));break;case 8:m.canAuto=r.bool();break;case 9:m.cachedNotifyMsg=$root.humanboy_proto.GameRoundEndNotify.decode(r,r.uint32());break;case 10:m.leftSeconds=r.int64();break;case 11:if(!(m.dealer&&m.dealer.length))m.dealer=[];m.dealer.push($root.humanboy_proto.DealerPlayerInfo.decode(r,r.uint32()));break;case 12:m.onDealerList=r.uint32();break;case 13:m.jackpotLeftMoney=r.int64();break;case 14:m.beDealerNum=r.int32();break;case 15:if(!(m.optionResults&&m.optionResults.length))m.optionResults=[];m.optionResults.push($root.humanboy_proto.OptionResults.decode(r,r.uint32()));break;case 16:m.isWaitDownDealer=r.bool();break;case 17:m.surplusStockNum=r.uint32();break;case 18:m.occupyStockNum=r.uint32();break;case 19:m.showMiddleUpDealerBtn=r.bool();break;case 20:m.totalStockNum=r.uint32();break;case 21:if(!(m.betCoinOption&&m.betCoinOption.length))m.betCoinOption=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.betCoinOption.push(r.uint64())}else m.betCoinOption.push(r.uint64());break;case 22:m.autoLevel=r.int32();break;case 23:if(!(m.items&&m.items.length))m.items=[];m.items.push($root.humanboy_proto.FeeItems.decode(r,r.uint32()));break;case 24:m.usedAutoBetCount=r.int32();break;case 25:m.selectAutoBetCount=r.int32();break;case 26:if(!(m.AutoBetCountList&&m.AutoBetCountList.length))m.AutoBetCountList=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.AutoBetCountList.push(r.int32())}else m.AutoBetCountList.push(r.int32());break;case 27:m.canAdvanceAuto=r.bool();break;case 28:m.BetButtonLimitAmount=r.int64();break;case 29:m.CalmDownLeftSeconds=r.int64();break;case 30:m.CalmDownDeadLineTimeStamp=r.int64();break;case 31:m.reachLimitBet=r.bool();break;default:r.skipType(t&7);break}}return m};GameDataSynNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameDataSynNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.param!=null&&m.hasOwnProperty("param")){{var e=$root.humanboy_proto.RoomParam.verify(m.param);if(e)return"param."+e}}if(m.optionInfo!=null&&m.hasOwnProperty("optionInfo")){if(!Array.isArray(m.optionInfo))return"optionInfo: array expected";for(var i=0;i<m.optionInfo.length;++i){{var e=$root.humanboy_proto.BetOptionInfo.verify(m.optionInfo[i]);if(e)return"optionInfo."+e}}}if(m.lastResult!=null&&m.hasOwnProperty("lastResult")){if(!Array.isArray(m.lastResult))return"lastResult: array expected";for(var i=0;i<m.lastResult.length;++i){if(!$util.isInteger(m.lastResult[i]))return"lastResult: integer[] expected"}}if(m.curState!=null&&m.hasOwnProperty("curState")){switch(m.curState){default:return"curState: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:break}}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.players!=null&&m.hasOwnProperty("players")){if(!Array.isArray(m.players))return"players: array expected";for(var i=0;i<m.players.length;++i){{var e=$root.humanboy_proto.GamePlayer.verify(m.players[i]);if(e)return"players."+e}}}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){if(typeof m.canAuto!=="boolean")return"canAuto: boolean expected"}if(m.cachedNotifyMsg!=null&&m.hasOwnProperty("cachedNotifyMsg")){{var e=$root.humanboy_proto.GameRoundEndNotify.verify(m.cachedNotifyMsg);if(e)return"cachedNotifyMsg."+e}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.dealer!=null&&m.hasOwnProperty("dealer")){if(!Array.isArray(m.dealer))return"dealer: array expected";for(var i=0;i<m.dealer.length;++i){{var e=$root.humanboy_proto.DealerPlayerInfo.verify(m.dealer[i]);if(e)return"dealer."+e}}}if(m.onDealerList!=null&&m.hasOwnProperty("onDealerList")){if(!$util.isInteger(m.onDealerList))return"onDealerList: integer expected"}if(m.jackpotLeftMoney!=null&&m.hasOwnProperty("jackpotLeftMoney")){if(!$util.isInteger(m.jackpotLeftMoney)&&!(m.jackpotLeftMoney&&$util.isInteger(m.jackpotLeftMoney.low)&&$util.isInteger(m.jackpotLeftMoney.high)))return"jackpotLeftMoney: integer|Long expected"}if(m.beDealerNum!=null&&m.hasOwnProperty("beDealerNum")){if(!$util.isInteger(m.beDealerNum))return"beDealerNum: integer expected"}if(m.optionResults!=null&&m.hasOwnProperty("optionResults")){if(!Array.isArray(m.optionResults))return"optionResults: array expected";for(var i=0;i<m.optionResults.length;++i){{var e=$root.humanboy_proto.OptionResults.verify(m.optionResults[i]);if(e)return"optionResults."+e}}}if(m.isWaitDownDealer!=null&&m.hasOwnProperty("isWaitDownDealer")){if(typeof m.isWaitDownDealer!=="boolean")return"isWaitDownDealer: boolean expected"}if(m.surplusStockNum!=null&&m.hasOwnProperty("surplusStockNum")){if(!$util.isInteger(m.surplusStockNum))return"surplusStockNum: integer expected"}if(m.occupyStockNum!=null&&m.hasOwnProperty("occupyStockNum")){if(!$util.isInteger(m.occupyStockNum))return"occupyStockNum: integer expected"}if(m.showMiddleUpDealerBtn!=null&&m.hasOwnProperty("showMiddleUpDealerBtn")){if(typeof m.showMiddleUpDealerBtn!=="boolean")return"showMiddleUpDealerBtn: boolean expected"}if(m.totalStockNum!=null&&m.hasOwnProperty("totalStockNum")){if(!$util.isInteger(m.totalStockNum))return"totalStockNum: integer expected"}if(m.betCoinOption!=null&&m.hasOwnProperty("betCoinOption")){if(!Array.isArray(m.betCoinOption))return"betCoinOption: array expected";for(var i=0;i<m.betCoinOption.length;++i){if(!$util.isInteger(m.betCoinOption[i])&&!(m.betCoinOption[i]&&$util.isInteger(m.betCoinOption[i].low)&&$util.isInteger(m.betCoinOption[i].high)))return"betCoinOption: integer|Long[] expected"}}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){switch(m.autoLevel){default:return"autoLevel: enum value expected";case 0:case 1:break}}if(m.items!=null&&m.hasOwnProperty("items")){if(!Array.isArray(m.items))return"items: array expected";for(var i=0;i<m.items.length;++i){{var e=$root.humanboy_proto.FeeItems.verify(m.items[i]);if(e)return"items."+e}}}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){if(!$util.isInteger(m.usedAutoBetCount))return"usedAutoBetCount: integer expected"}if(m.selectAutoBetCount!=null&&m.hasOwnProperty("selectAutoBetCount")){if(!$util.isInteger(m.selectAutoBetCount))return"selectAutoBetCount: integer expected"}if(m.AutoBetCountList!=null&&m.hasOwnProperty("AutoBetCountList")){if(!Array.isArray(m.AutoBetCountList))return"AutoBetCountList: array expected";for(var i=0;i<m.AutoBetCountList.length;++i){if(!$util.isInteger(m.AutoBetCountList[i]))return"AutoBetCountList: integer[] expected"}}if(m.canAdvanceAuto!=null&&m.hasOwnProperty("canAdvanceAuto")){if(typeof m.canAdvanceAuto!=="boolean")return"canAdvanceAuto: boolean expected"}if(m.BetButtonLimitAmount!=null&&m.hasOwnProperty("BetButtonLimitAmount")){if(!$util.isInteger(m.BetButtonLimitAmount)&&!(m.BetButtonLimitAmount&&$util.isInteger(m.BetButtonLimitAmount.low)&&$util.isInteger(m.BetButtonLimitAmount.high)))return"BetButtonLimitAmount: integer|Long expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}if(m.reachLimitBet!=null&&m.hasOwnProperty("reachLimitBet")){if(typeof m.reachLimitBet!=="boolean")return"reachLimitBet: boolean expected"}return null};GameDataSynNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GameDataSynNotify)return d;var m=new $root.humanboy_proto.GameDataSynNotify;if(d.param!=null){if(typeof d.param!=="object")throw TypeError(".humanboy_proto.GameDataSynNotify.param: object expected");m.param=$root.humanboy_proto.RoomParam.fromObject(d.param)}if(d.optionInfo){if(!Array.isArray(d.optionInfo))throw TypeError(".humanboy_proto.GameDataSynNotify.optionInfo: array expected");m.optionInfo=[];for(var i=0;i<d.optionInfo.length;++i){if(typeof d.optionInfo[i]!=="object")throw TypeError(".humanboy_proto.GameDataSynNotify.optionInfo: object expected");m.optionInfo[i]=$root.humanboy_proto.BetOptionInfo.fromObject(d.optionInfo[i])}}if(d.lastResult){if(!Array.isArray(d.lastResult))throw TypeError(".humanboy_proto.GameDataSynNotify.lastResult: array expected");m.lastResult=[];for(var i=0;i<d.lastResult.length;++i){m.lastResult[i]=d.lastResult[i]|0}}switch(d.curState){case"RoundState_DUMMY":case 0:m.curState=0;break;case"GAME_PENDING":case 1:m.curState=1;break;case"NEW_ROUND":case 2:m.curState=2;break;case"BET":case 3:m.curState=3;break;case"WAIT_NEXT_ROUND":case 4:m.curState=4;break;case"WAIT_NEXT_ROUND2":case 5:m.curState=5;break}if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.players){if(!Array.isArray(d.players))throw TypeError(".humanboy_proto.GameDataSynNotify.players: array expected");m.players=[];for(var i=0;i<d.players.length;++i){if(typeof d.players[i]!=="object")throw TypeError(".humanboy_proto.GameDataSynNotify.players: object expected");m.players[i]=$root.humanboy_proto.GamePlayer.fromObject(d.players[i])}}if(d.canAuto!=null){m.canAuto=Boolean(d.canAuto)}if(d.cachedNotifyMsg!=null){if(typeof d.cachedNotifyMsg!=="object")throw TypeError(".humanboy_proto.GameDataSynNotify.cachedNotifyMsg: object expected");m.cachedNotifyMsg=$root.humanboy_proto.GameRoundEndNotify.fromObject(d.cachedNotifyMsg)}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.dealer){if(!Array.isArray(d.dealer))throw TypeError(".humanboy_proto.GameDataSynNotify.dealer: array expected");m.dealer=[];for(var i=0;i<d.dealer.length;++i){if(typeof d.dealer[i]!=="object")throw TypeError(".humanboy_proto.GameDataSynNotify.dealer: object expected");m.dealer[i]=$root.humanboy_proto.DealerPlayerInfo.fromObject(d.dealer[i])}}if(d.onDealerList!=null){m.onDealerList=d.onDealerList>>>0}if(d.jackpotLeftMoney!=null){if($util.Long)(m.jackpotLeftMoney=$util.Long.fromValue(d.jackpotLeftMoney)).unsigned=false;else if(typeof d.jackpotLeftMoney==="string")m.jackpotLeftMoney=parseInt(d.jackpotLeftMoney,10);else if(typeof d.jackpotLeftMoney==="number")m.jackpotLeftMoney=d.jackpotLeftMoney;else if(typeof d.jackpotLeftMoney==="object")m.jackpotLeftMoney=new $util.LongBits(d.jackpotLeftMoney.low>>>0,d.jackpotLeftMoney.high>>>0).toNumber()}if(d.beDealerNum!=null){m.beDealerNum=d.beDealerNum|0}if(d.optionResults){if(!Array.isArray(d.optionResults))throw TypeError(".humanboy_proto.GameDataSynNotify.optionResults: array expected");m.optionResults=[];for(var i=0;i<d.optionResults.length;++i){if(typeof d.optionResults[i]!=="object")throw TypeError(".humanboy_proto.GameDataSynNotify.optionResults: object expected");m.optionResults[i]=$root.humanboy_proto.OptionResults.fromObject(d.optionResults[i])}}if(d.isWaitDownDealer!=null){m.isWaitDownDealer=Boolean(d.isWaitDownDealer)}if(d.surplusStockNum!=null){m.surplusStockNum=d.surplusStockNum>>>0}if(d.occupyStockNum!=null){m.occupyStockNum=d.occupyStockNum>>>0}if(d.showMiddleUpDealerBtn!=null){m.showMiddleUpDealerBtn=Boolean(d.showMiddleUpDealerBtn)}if(d.totalStockNum!=null){m.totalStockNum=d.totalStockNum>>>0}if(d.betCoinOption){if(!Array.isArray(d.betCoinOption))throw TypeError(".humanboy_proto.GameDataSynNotify.betCoinOption: array expected");m.betCoinOption=[];for(var i=0;i<d.betCoinOption.length;++i){if($util.Long)(m.betCoinOption[i]=$util.Long.fromValue(d.betCoinOption[i])).unsigned=true;else if(typeof d.betCoinOption[i]==="string")m.betCoinOption[i]=parseInt(d.betCoinOption[i],10);else if(typeof d.betCoinOption[i]==="number")m.betCoinOption[i]=d.betCoinOption[i];else if(typeof d.betCoinOption[i]==="object")m.betCoinOption[i]=new $util.LongBits(d.betCoinOption[i].low>>>0,d.betCoinOption[i].high>>>0).toNumber(true)}}switch(d.autoLevel){case"Level_Normal":case 0:m.autoLevel=0;break;case"Level_Advance":case 1:m.autoLevel=1;break}if(d.items){if(!Array.isArray(d.items))throw TypeError(".humanboy_proto.GameDataSynNotify.items: array expected");m.items=[];for(var i=0;i<d.items.length;++i){if(typeof d.items[i]!=="object")throw TypeError(".humanboy_proto.GameDataSynNotify.items: object expected");m.items[i]=$root.humanboy_proto.FeeItems.fromObject(d.items[i])}}if(d.usedAutoBetCount!=null){m.usedAutoBetCount=d.usedAutoBetCount|0}if(d.selectAutoBetCount!=null){m.selectAutoBetCount=d.selectAutoBetCount|0}if(d.AutoBetCountList){if(!Array.isArray(d.AutoBetCountList))throw TypeError(".humanboy_proto.GameDataSynNotify.AutoBetCountList: array expected");m.AutoBetCountList=[];for(var i=0;i<d.AutoBetCountList.length;++i){m.AutoBetCountList[i]=d.AutoBetCountList[i]|0}}if(d.canAdvanceAuto!=null){m.canAdvanceAuto=Boolean(d.canAdvanceAuto)}if(d.BetButtonLimitAmount!=null){if($util.Long)(m.BetButtonLimitAmount=$util.Long.fromValue(d.BetButtonLimitAmount)).unsigned=false;else if(typeof d.BetButtonLimitAmount==="string")m.BetButtonLimitAmount=parseInt(d.BetButtonLimitAmount,10);else if(typeof d.BetButtonLimitAmount==="number")m.BetButtonLimitAmount=d.BetButtonLimitAmount;else if(typeof d.BetButtonLimitAmount==="object")m.BetButtonLimitAmount=new $util.LongBits(d.BetButtonLimitAmount.low>>>0,d.BetButtonLimitAmount.high>>>0).toNumber()}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}if(d.reachLimitBet!=null){m.reachLimitBet=Boolean(d.reachLimitBet)}return m};GameDataSynNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.optionInfo=[];d.lastResult=[];d.players=[];d.dealer=[];d.optionResults=[];d.betCoinOption=[];d.items=[];d.AutoBetCountList=[]}if(o.defaults){d.param=null;d.curState=o.enums===String?"RoundState_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;d.canAuto=false;d.cachedNotifyMsg=null;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;d.onDealerList=0;if($util.Long){var n=new $util.Long(0,0,false);d.jackpotLeftMoney=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.jackpotLeftMoney=o.longs===String?"0":0;d.beDealerNum=0;d.isWaitDownDealer=false;d.surplusStockNum=0;d.occupyStockNum=0;d.showMiddleUpDealerBtn=false;d.totalStockNum=0;d.autoLevel=o.enums===String?"Level_Normal":0;d.usedAutoBetCount=0;d.selectAutoBetCount=0;d.canAdvanceAuto=false;if($util.Long){var n=new $util.Long(0,0,false);d.BetButtonLimitAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.BetButtonLimitAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0;d.reachLimitBet=false}if(m.param!=null&&m.hasOwnProperty("param")){d.param=$root.humanboy_proto.RoomParam.toObject(m.param,o)}if(m.optionInfo&&m.optionInfo.length){d.optionInfo=[];for(var j=0;j<m.optionInfo.length;++j){d.optionInfo[j]=$root.humanboy_proto.BetOptionInfo.toObject(m.optionInfo[j],o)}}if(m.lastResult&&m.lastResult.length){d.lastResult=[];for(var j=0;j<m.lastResult.length;++j){d.lastResult[j]=m.lastResult[j]}}if(m.curState!=null&&m.hasOwnProperty("curState")){d.curState=o.enums===String?$root.humanboy_proto.RoundState[m.curState]:m.curState}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.players&&m.players.length){d.players=[];for(var j=0;j<m.players.length;++j){d.players[j]=$root.humanboy_proto.GamePlayer.toObject(m.players[j],o)}}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){d.canAuto=m.canAuto}if(m.cachedNotifyMsg!=null&&m.hasOwnProperty("cachedNotifyMsg")){d.cachedNotifyMsg=$root.humanboy_proto.GameRoundEndNotify.toObject(m.cachedNotifyMsg,o)}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.dealer&&m.dealer.length){d.dealer=[];for(var j=0;j<m.dealer.length;++j){d.dealer[j]=$root.humanboy_proto.DealerPlayerInfo.toObject(m.dealer[j],o)}}if(m.onDealerList!=null&&m.hasOwnProperty("onDealerList")){d.onDealerList=m.onDealerList}if(m.jackpotLeftMoney!=null&&m.hasOwnProperty("jackpotLeftMoney")){if(typeof m.jackpotLeftMoney==="number")d.jackpotLeftMoney=o.longs===String?String(m.jackpotLeftMoney):m.jackpotLeftMoney;else d.jackpotLeftMoney=o.longs===String?$util.Long.prototype.toString.call(m.jackpotLeftMoney):o.longs===Number?new $util.LongBits(m.jackpotLeftMoney.low>>>0,m.jackpotLeftMoney.high>>>0).toNumber():m.jackpotLeftMoney}if(m.beDealerNum!=null&&m.hasOwnProperty("beDealerNum")){d.beDealerNum=m.beDealerNum}if(m.optionResults&&m.optionResults.length){d.optionResults=[];for(var j=0;j<m.optionResults.length;++j){d.optionResults[j]=$root.humanboy_proto.OptionResults.toObject(m.optionResults[j],o)}}if(m.isWaitDownDealer!=null&&m.hasOwnProperty("isWaitDownDealer")){d.isWaitDownDealer=m.isWaitDownDealer}if(m.surplusStockNum!=null&&m.hasOwnProperty("surplusStockNum")){d.surplusStockNum=m.surplusStockNum}if(m.occupyStockNum!=null&&m.hasOwnProperty("occupyStockNum")){d.occupyStockNum=m.occupyStockNum}if(m.showMiddleUpDealerBtn!=null&&m.hasOwnProperty("showMiddleUpDealerBtn")){d.showMiddleUpDealerBtn=m.showMiddleUpDealerBtn}if(m.totalStockNum!=null&&m.hasOwnProperty("totalStockNum")){d.totalStockNum=m.totalStockNum}if(m.betCoinOption&&m.betCoinOption.length){d.betCoinOption=[];for(var j=0;j<m.betCoinOption.length;++j){if(typeof m.betCoinOption[j]==="number")d.betCoinOption[j]=o.longs===String?String(m.betCoinOption[j]):m.betCoinOption[j];else d.betCoinOption[j]=o.longs===String?$util.Long.prototype.toString.call(m.betCoinOption[j]):o.longs===Number?new $util.LongBits(m.betCoinOption[j].low>>>0,m.betCoinOption[j].high>>>0).toNumber(true):m.betCoinOption[j]}}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){d.autoLevel=o.enums===String?$root.humanboy_proto.AutoBetLevel[m.autoLevel]:m.autoLevel}if(m.items&&m.items.length){d.items=[];for(var j=0;j<m.items.length;++j){d.items[j]=$root.humanboy_proto.FeeItems.toObject(m.items[j],o)}}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){d.usedAutoBetCount=m.usedAutoBetCount}if(m.selectAutoBetCount!=null&&m.hasOwnProperty("selectAutoBetCount")){d.selectAutoBetCount=m.selectAutoBetCount}if(m.AutoBetCountList&&m.AutoBetCountList.length){d.AutoBetCountList=[];for(var j=0;j<m.AutoBetCountList.length;++j){d.AutoBetCountList[j]=m.AutoBetCountList[j]}}if(m.canAdvanceAuto!=null&&m.hasOwnProperty("canAdvanceAuto")){d.canAdvanceAuto=m.canAdvanceAuto}if(m.BetButtonLimitAmount!=null&&m.hasOwnProperty("BetButtonLimitAmount")){if(typeof m.BetButtonLimitAmount==="number")d.BetButtonLimitAmount=o.longs===String?String(m.BetButtonLimitAmount):m.BetButtonLimitAmount;else d.BetButtonLimitAmount=o.longs===String?$util.Long.prototype.toString.call(m.BetButtonLimitAmount):o.longs===Number?new $util.LongBits(m.BetButtonLimitAmount.low>>>0,m.BetButtonLimitAmount.high>>>0).toNumber():m.BetButtonLimitAmount}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}if(m.reachLimitBet!=null&&m.hasOwnProperty("reachLimitBet")){d.reachLimitBet=m.reachLimitBet}return d};GameDataSynNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameDataSynNotify}();humanboy_proto.FeeItems=function(){function FeeItems(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}FeeItems.prototype.id=0;FeeItems.prototype.coin=$util.Long?$util.Long.fromBits(0,0,false):0;FeeItems.create=function create(properties){return new FeeItems(properties)};FeeItems.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.id!=null&&Object.hasOwnProperty.call(m,"id"))w.uint32(8).int32(m.id);if(m.coin!=null&&Object.hasOwnProperty.call(m,"coin"))w.uint32(16).int64(m.coin);return w};FeeItems.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};FeeItems.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.FeeItems;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.id=r.int32();break;case 2:m.coin=r.int64();break;default:r.skipType(t&7);break}}return m};FeeItems.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};FeeItems.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.id!=null&&m.hasOwnProperty("id")){if(!$util.isInteger(m.id))return"id: integer expected"}if(m.coin!=null&&m.hasOwnProperty("coin")){if(!$util.isInteger(m.coin)&&!(m.coin&&$util.isInteger(m.coin.low)&&$util.isInteger(m.coin.high)))return"coin: integer|Long expected"}return null};FeeItems.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.FeeItems)return d;var m=new $root.humanboy_proto.FeeItems;if(d.id!=null){m.id=d.id|0}if(d.coin!=null){if($util.Long)(m.coin=$util.Long.fromValue(d.coin)).unsigned=false;else if(typeof d.coin==="string")m.coin=parseInt(d.coin,10);else if(typeof d.coin==="number")m.coin=d.coin;else if(typeof d.coin==="object")m.coin=new $util.LongBits(d.coin.low>>>0,d.coin.high>>>0).toNumber()}return m};FeeItems.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.id=0;if($util.Long){var n=new $util.Long(0,0,false);d.coin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.coin=o.longs===String?"0":0}if(m.id!=null&&m.hasOwnProperty("id")){d.id=m.id}if(m.coin!=null&&m.hasOwnProperty("coin")){if(typeof m.coin==="number")d.coin=o.longs===String?String(m.coin):m.coin;else d.coin=o.longs===String?$util.Long.prototype.toString.call(m.coin):o.longs===Number?new $util.LongBits(m.coin.low>>>0,m.coin.high>>>0).toNumber():m.coin}return d};FeeItems.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return FeeItems}();humanboy_proto.OptionResults=function(){function OptionResults(p){this.results=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OptionResults.prototype.option=0;OptionResults.prototype.results=$util.emptyArray;OptionResults.prototype.loseHand=0;OptionResults.create=function create(properties){return new OptionResults(properties)};OptionResults.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.results!=null&&m.results.length){w.uint32(18).fork();for(var i=0;i<m.results.length;++i)w.int32(m.results[i]);w.ldelim()}if(m.loseHand!=null&&Object.hasOwnProperty.call(m,"loseHand"))w.uint32(24).int32(m.loseHand);return w};OptionResults.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OptionResults.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.OptionResults;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:if(!(m.results&&m.results.length))m.results=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.results.push(r.int32())}else m.results.push(r.int32());break;case 3:m.loseHand=r.int32();break;default:r.skipType(t&7);break}}return m};OptionResults.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OptionResults.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.results!=null&&m.hasOwnProperty("results")){if(!Array.isArray(m.results))return"results: array expected";for(var i=0;i<m.results.length;++i){if(!$util.isInteger(m.results[i]))return"results: integer[] expected"}}if(m.loseHand!=null&&m.hasOwnProperty("loseHand")){if(!$util.isInteger(m.loseHand))return"loseHand: integer expected"}return null};OptionResults.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.OptionResults)return d;var m=new $root.humanboy_proto.OptionResults;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.results){if(!Array.isArray(d.results))throw TypeError(".humanboy_proto.OptionResults.results: array expected");m.results=[];for(var i=0;i<d.results.length;++i){m.results[i]=d.results[i]|0}}if(d.loseHand!=null){m.loseHand=d.loseHand|0}return m};OptionResults.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.results=[]}if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;d.loseHand=0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.results&&m.results.length){d.results=[];for(var j=0;j<m.results.length;++j){d.results[j]=m.results[j]}}if(m.loseHand!=null&&m.hasOwnProperty("loseHand")){d.loseHand=m.loseHand}return d};OptionResults.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OptionResults}();humanboy_proto.BetOptionInfo=function(){function BetOptionInfo(p){this.amount=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetOptionInfo.prototype.option=0;BetOptionInfo.prototype.selfBet=$util.Long?$util.Long.fromBits(0,0,true):0;BetOptionInfo.prototype.totalBet=$util.Long?$util.Long.fromBits(0,0,true):0;BetOptionInfo.prototype.amount=$util.emptyArray;BetOptionInfo.create=function create(properties){return new BetOptionInfo(properties)};BetOptionInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.selfBet!=null&&Object.hasOwnProperty.call(m,"selfBet"))w.uint32(16).uint64(m.selfBet);if(m.totalBet!=null&&Object.hasOwnProperty.call(m,"totalBet"))w.uint32(24).uint64(m.totalBet);if(m.amount!=null&&m.amount.length){w.uint32(34).fork();for(var i=0;i<m.amount.length;++i)w.uint64(m.amount[i]);w.ldelim()}return w};BetOptionInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetOptionInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.BetOptionInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.selfBet=r.uint64();break;case 3:m.totalBet=r.uint64();break;case 4:if(!(m.amount&&m.amount.length))m.amount=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.amount.push(r.uint64())}else m.amount.push(r.uint64());break;default:r.skipType(t&7);break}}return m};BetOptionInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetOptionInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.selfBet!=null&&m.hasOwnProperty("selfBet")){if(!$util.isInteger(m.selfBet)&&!(m.selfBet&&$util.isInteger(m.selfBet.low)&&$util.isInteger(m.selfBet.high)))return"selfBet: integer|Long expected"}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(!$util.isInteger(m.totalBet)&&!(m.totalBet&&$util.isInteger(m.totalBet.low)&&$util.isInteger(m.totalBet.high)))return"totalBet: integer|Long expected"}if(m.amount!=null&&m.hasOwnProperty("amount")){if(!Array.isArray(m.amount))return"amount: array expected";for(var i=0;i<m.amount.length;++i){if(!$util.isInteger(m.amount[i])&&!(m.amount[i]&&$util.isInteger(m.amount[i].low)&&$util.isInteger(m.amount[i].high)))return"amount: integer|Long[] expected"}}return null};BetOptionInfo.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.BetOptionInfo)return d;var m=new $root.humanboy_proto.BetOptionInfo;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.selfBet!=null){if($util.Long)(m.selfBet=$util.Long.fromValue(d.selfBet)).unsigned=true;else if(typeof d.selfBet==="string")m.selfBet=parseInt(d.selfBet,10);else if(typeof d.selfBet==="number")m.selfBet=d.selfBet;else if(typeof d.selfBet==="object")m.selfBet=new $util.LongBits(d.selfBet.low>>>0,d.selfBet.high>>>0).toNumber(true)}if(d.totalBet!=null){if($util.Long)(m.totalBet=$util.Long.fromValue(d.totalBet)).unsigned=true;else if(typeof d.totalBet==="string")m.totalBet=parseInt(d.totalBet,10);else if(typeof d.totalBet==="number")m.totalBet=d.totalBet;else if(typeof d.totalBet==="object")m.totalBet=new $util.LongBits(d.totalBet.low>>>0,d.totalBet.high>>>0).toNumber(true)}if(d.amount){if(!Array.isArray(d.amount))throw TypeError(".humanboy_proto.BetOptionInfo.amount: array expected");m.amount=[];for(var i=0;i<d.amount.length;++i){if($util.Long)(m.amount[i]=$util.Long.fromValue(d.amount[i])).unsigned=true;else if(typeof d.amount[i]==="string")m.amount[i]=parseInt(d.amount[i],10);else if(typeof d.amount[i]==="number")m.amount[i]=d.amount[i];else if(typeof d.amount[i]==="object")m.amount[i]=new $util.LongBits(d.amount[i].low>>>0,d.amount[i].high>>>0).toNumber(true)}}return m};BetOptionInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.amount=[]}if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.selfBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.selfBet=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.totalBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBet=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.selfBet!=null&&m.hasOwnProperty("selfBet")){if(typeof m.selfBet==="number")d.selfBet=o.longs===String?String(m.selfBet):m.selfBet;else d.selfBet=o.longs===String?$util.Long.prototype.toString.call(m.selfBet):o.longs===Number?new $util.LongBits(m.selfBet.low>>>0,m.selfBet.high>>>0).toNumber(true):m.selfBet}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(typeof m.totalBet==="number")d.totalBet=o.longs===String?String(m.totalBet):m.totalBet;else d.totalBet=o.longs===String?$util.Long.prototype.toString.call(m.totalBet):o.longs===Number?new $util.LongBits(m.totalBet.low>>>0,m.totalBet.high>>>0).toNumber(true):m.totalBet}if(m.amount&&m.amount.length){d.amount=[];for(var j=0;j<m.amount.length;++j){if(typeof m.amount[j]==="number")d.amount[j]=o.longs===String?String(m.amount[j]):m.amount[j];else d.amount[j]=o.longs===String?$util.Long.prototype.toString.call(m.amount[j]):o.longs===Number?new $util.LongBits(m.amount[j].low>>>0,m.amount[j].high>>>0).toNumber(true):m.amount[j]}}return d};BetOptionInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetOptionInfo}();humanboy_proto.DealNotify=function(){function DealNotify(p){this.players=[];this.lastResult=[];this.dealerInfo=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DealNotify.prototype.card=null;DealNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;DealNotify.prototype.players=$util.emptyArray;DealNotify.prototype.param=null;DealNotify.prototype.changed=false;DealNotify.prototype.lastResult=$util.emptyArray;DealNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;DealNotify.prototype.canAuto=false;DealNotify.prototype.dealerInfo=$util.emptyArray;DealNotify.prototype.totalStockNum=0;DealNotify.create=function create(properties){return new DealNotify(properties)};DealNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.card!=null&&Object.hasOwnProperty.call(m,"card"))$root.humanboy_proto.CardItem.encode(m.card,w.uint32(10).fork()).ldelim();if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(16).int64(m.nextRoundEndStamp);if(m.players!=null&&m.players.length){for(var i=0;i<m.players.length;++i)$root.humanboy_proto.GamePlayer.encode(m.players[i],w.uint32(26).fork()).ldelim()}if(m.param!=null&&Object.hasOwnProperty.call(m,"param"))$root.humanboy_proto.RoomParam.encode(m.param,w.uint32(34).fork()).ldelim();if(m.changed!=null&&Object.hasOwnProperty.call(m,"changed"))w.uint32(40).bool(m.changed);if(m.lastResult!=null&&m.lastResult.length){w.uint32(50).fork();for(var i=0;i<m.lastResult.length;++i)w.int32(m.lastResult[i]);w.ldelim()}if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(56).int64(m.leftSeconds);if(m.canAuto!=null&&Object.hasOwnProperty.call(m,"canAuto"))w.uint32(64).bool(m.canAuto);if(m.dealerInfo!=null&&m.dealerInfo.length){for(var i=0;i<m.dealerInfo.length;++i)$root.humanboy_proto.DealerPlayerInfo.encode(m.dealerInfo[i],w.uint32(74).fork()).ldelim()}if(m.totalStockNum!=null&&Object.hasOwnProperty.call(m,"totalStockNum"))w.uint32(80).uint32(m.totalStockNum);return w};DealNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DealNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.DealNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.card=$root.humanboy_proto.CardItem.decode(r,r.uint32());break;case 2:m.nextRoundEndStamp=r.int64();break;case 3:if(!(m.players&&m.players.length))m.players=[];m.players.push($root.humanboy_proto.GamePlayer.decode(r,r.uint32()));break;case 4:m.param=$root.humanboy_proto.RoomParam.decode(r,r.uint32());break;case 5:m.changed=r.bool();break;case 6:if(!(m.lastResult&&m.lastResult.length))m.lastResult=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.lastResult.push(r.int32())}else m.lastResult.push(r.int32());break;case 7:m.leftSeconds=r.int64();break;case 8:m.canAuto=r.bool();break;case 9:if(!(m.dealerInfo&&m.dealerInfo.length))m.dealerInfo=[];m.dealerInfo.push($root.humanboy_proto.DealerPlayerInfo.decode(r,r.uint32()));break;case 10:m.totalStockNum=r.uint32();break;default:r.skipType(t&7);break}}return m};DealNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DealNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.card!=null&&m.hasOwnProperty("card")){{var e=$root.humanboy_proto.CardItem.verify(m.card);if(e)return"card."+e}}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.players!=null&&m.hasOwnProperty("players")){if(!Array.isArray(m.players))return"players: array expected";for(var i=0;i<m.players.length;++i){{var e=$root.humanboy_proto.GamePlayer.verify(m.players[i]);if(e)return"players."+e}}}if(m.param!=null&&m.hasOwnProperty("param")){{var e=$root.humanboy_proto.RoomParam.verify(m.param);if(e)return"param."+e}}if(m.changed!=null&&m.hasOwnProperty("changed")){if(typeof m.changed!=="boolean")return"changed: boolean expected"}if(m.lastResult!=null&&m.hasOwnProperty("lastResult")){if(!Array.isArray(m.lastResult))return"lastResult: array expected";for(var i=0;i<m.lastResult.length;++i){if(!$util.isInteger(m.lastResult[i]))return"lastResult: integer[] expected"}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){if(typeof m.canAuto!=="boolean")return"canAuto: boolean expected"}if(m.dealerInfo!=null&&m.hasOwnProperty("dealerInfo")){if(!Array.isArray(m.dealerInfo))return"dealerInfo: array expected";for(var i=0;i<m.dealerInfo.length;++i){{var e=$root.humanboy_proto.DealerPlayerInfo.verify(m.dealerInfo[i]);if(e)return"dealerInfo."+e}}}if(m.totalStockNum!=null&&m.hasOwnProperty("totalStockNum")){if(!$util.isInteger(m.totalStockNum))return"totalStockNum: integer expected"}return null};DealNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.DealNotify)return d;var m=new $root.humanboy_proto.DealNotify;if(d.card!=null){if(typeof d.card!=="object")throw TypeError(".humanboy_proto.DealNotify.card: object expected");m.card=$root.humanboy_proto.CardItem.fromObject(d.card)}if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.players){if(!Array.isArray(d.players))throw TypeError(".humanboy_proto.DealNotify.players: array expected");m.players=[];for(var i=0;i<d.players.length;++i){if(typeof d.players[i]!=="object")throw TypeError(".humanboy_proto.DealNotify.players: object expected");m.players[i]=$root.humanboy_proto.GamePlayer.fromObject(d.players[i])}}if(d.param!=null){if(typeof d.param!=="object")throw TypeError(".humanboy_proto.DealNotify.param: object expected");m.param=$root.humanboy_proto.RoomParam.fromObject(d.param)}if(d.changed!=null){m.changed=Boolean(d.changed)}if(d.lastResult){if(!Array.isArray(d.lastResult))throw TypeError(".humanboy_proto.DealNotify.lastResult: array expected");m.lastResult=[];for(var i=0;i<d.lastResult.length;++i){m.lastResult[i]=d.lastResult[i]|0}}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.canAuto!=null){m.canAuto=Boolean(d.canAuto)}if(d.dealerInfo){if(!Array.isArray(d.dealerInfo))throw TypeError(".humanboy_proto.DealNotify.dealerInfo: array expected");m.dealerInfo=[];for(var i=0;i<d.dealerInfo.length;++i){if(typeof d.dealerInfo[i]!=="object")throw TypeError(".humanboy_proto.DealNotify.dealerInfo: object expected");m.dealerInfo[i]=$root.humanboy_proto.DealerPlayerInfo.fromObject(d.dealerInfo[i])}}if(d.totalStockNum!=null){m.totalStockNum=d.totalStockNum>>>0}return m};DealNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.players=[];d.lastResult=[];d.dealerInfo=[]}if(o.defaults){d.card=null;if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;d.param=null;d.changed=false;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;d.canAuto=false;d.totalStockNum=0}if(m.card!=null&&m.hasOwnProperty("card")){d.card=$root.humanboy_proto.CardItem.toObject(m.card,o)}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.players&&m.players.length){d.players=[];for(var j=0;j<m.players.length;++j){d.players[j]=$root.humanboy_proto.GamePlayer.toObject(m.players[j],o)}}if(m.param!=null&&m.hasOwnProperty("param")){d.param=$root.humanboy_proto.RoomParam.toObject(m.param,o)}if(m.changed!=null&&m.hasOwnProperty("changed")){d.changed=m.changed}if(m.lastResult&&m.lastResult.length){d.lastResult=[];for(var j=0;j<m.lastResult.length;++j){d.lastResult[j]=m.lastResult[j]}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){d.canAuto=m.canAuto}if(m.dealerInfo&&m.dealerInfo.length){d.dealerInfo=[];for(var j=0;j<m.dealerInfo.length;++j){d.dealerInfo[j]=$root.humanboy_proto.DealerPlayerInfo.toObject(m.dealerInfo[j],o)}}if(m.totalStockNum!=null&&m.hasOwnProperty("totalStockNum")){d.totalStockNum=m.totalStockNum}return d};DealNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DealNotify}();humanboy_proto.BetReq=function(){function BetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetReq.prototype.detail=null;BetReq.create=function create(properties){return new BetReq(properties)};BetReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.detail!=null&&Object.hasOwnProperty.call(m,"detail"))$root.humanboy_proto.BetDetail.encode(m.detail,w.uint32(10).fork()).ldelim();return w};BetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.BetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.detail=$root.humanboy_proto.BetDetail.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};BetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.detail!=null&&m.hasOwnProperty("detail")){{var e=$root.humanboy_proto.BetDetail.verify(m.detail);if(e)return"detail."+e}}return null};BetReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.BetReq)return d;var m=new $root.humanboy_proto.BetReq;if(d.detail!=null){if(typeof d.detail!=="object")throw TypeError(".humanboy_proto.BetReq.detail: object expected");m.detail=$root.humanboy_proto.BetDetail.fromObject(d.detail)}return m};BetReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.detail=null}if(m.detail!=null&&m.hasOwnProperty("detail")){d.detail=$root.humanboy_proto.BetDetail.toObject(m.detail,o)}return d};BetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetReq}();humanboy_proto.BillInfo=function(){function BillInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BillInfo.prototype.BillNo="";BillInfo.prototype.time=$util.Long?$util.Long.fromBits(0,0,false):0;BillInfo.create=function create(properties){return new BillInfo(properties)};BillInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.BillNo!=null&&Object.hasOwnProperty.call(m,"BillNo"))w.uint32(10).string(m.BillNo);if(m.time!=null&&Object.hasOwnProperty.call(m,"time"))w.uint32(16).int64(m.time);return w};BillInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BillInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.BillInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.BillNo=r.string();break;case 2:m.time=r.int64();break;default:r.skipType(t&7);break}}return m};BillInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BillInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.BillNo!=null&&m.hasOwnProperty("BillNo")){if(!$util.isString(m.BillNo))return"BillNo: string expected"}if(m.time!=null&&m.hasOwnProperty("time")){if(!$util.isInteger(m.time)&&!(m.time&&$util.isInteger(m.time.low)&&$util.isInteger(m.time.high)))return"time: integer|Long expected"}return null};BillInfo.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.BillInfo)return d;var m=new $root.humanboy_proto.BillInfo;if(d.BillNo!=null){m.BillNo=String(d.BillNo)}if(d.time!=null){if($util.Long)(m.time=$util.Long.fromValue(d.time)).unsigned=false;else if(typeof d.time==="string")m.time=parseInt(d.time,10);else if(typeof d.time==="number")m.time=d.time;else if(typeof d.time==="object")m.time=new $util.LongBits(d.time.low>>>0,d.time.high>>>0).toNumber()}return m};BillInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.BillNo="";if($util.Long){var n=new $util.Long(0,0,false);d.time=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.time=o.longs===String?"0":0}if(m.BillNo!=null&&m.hasOwnProperty("BillNo")){d.BillNo=m.BillNo}if(m.time!=null&&m.hasOwnProperty("time")){if(typeof m.time==="number")d.time=o.longs===String?String(m.time):m.time;else d.time=o.longs===String?$util.Long.prototype.toString.call(m.time):o.longs===Number?new $util.LongBits(m.time.low>>>0,m.time.high>>>0).toNumber():m.time}return d};BillInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BillInfo}();humanboy_proto.BetResp=function(){function BetResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetResp.prototype.code=0;BetResp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;BetResp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;BetResp.prototype.bill=null;BetResp.create=function create(properties){return new BetResp(properties)};BetResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);if(m.bill!=null&&Object.hasOwnProperty.call(m,"bill"))$root.humanboy_proto.BillInfo.encode(m.bill,w.uint32(42).fork()).ldelim();return w};BetResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.BetResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;case 5:m.bill=$root.humanboy_proto.BillInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};BetResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}if(m.bill!=null&&m.hasOwnProperty("bill")){{var e=$root.humanboy_proto.BillInfo.verify(m.bill);if(e)return"bill."+e}}return null};BetResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.BetResp)return d;var m=new $root.humanboy_proto.BetResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}if(d.bill!=null){if(typeof d.bill!=="object")throw TypeError(".humanboy_proto.BetResp.bill: object expected");m.bill=$root.humanboy_proto.BillInfo.fromObject(d.bill)}return m};BetResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0;d.bill=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}if(m.bill!=null&&m.hasOwnProperty("bill")){d.bill=$root.humanboy_proto.BillInfo.toObject(m.bill,o)}return d};BetResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetResp}();humanboy_proto.BetNotify=function(){function BetNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetNotify.prototype.uid=0;BetNotify.prototype.detail=null;BetNotify.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,true):0;BetNotify.prototype.selfBet=$util.Long?$util.Long.fromBits(0,0,true):0;BetNotify.prototype.totalBet=$util.Long?$util.Long.fromBits(0,0,true):0;BetNotify.prototype.curUsdt=$util.Long?$util.Long.fromBits(0,0,true):0;BetNotify.create=function create(properties){return new BetNotify(properties)};BetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.detail!=null&&Object.hasOwnProperty.call(m,"detail"))$root.humanboy_proto.BetDetail.encode(m.detail,w.uint32(18).fork()).ldelim();if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(24).uint64(m.curCoin);if(m.selfBet!=null&&Object.hasOwnProperty.call(m,"selfBet"))w.uint32(32).uint64(m.selfBet);if(m.totalBet!=null&&Object.hasOwnProperty.call(m,"totalBet"))w.uint32(40).uint64(m.totalBet);if(m.curUsdt!=null&&Object.hasOwnProperty.call(m,"curUsdt"))w.uint32(48).uint64(m.curUsdt);return w};BetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.BetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.detail=$root.humanboy_proto.BetDetail.decode(r,r.uint32());break;case 3:m.curCoin=r.uint64();break;case 4:m.selfBet=r.uint64();break;case 5:m.totalBet=r.uint64();break;case 6:m.curUsdt=r.uint64();break;default:r.skipType(t&7);break}}return m};BetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.detail!=null&&m.hasOwnProperty("detail")){{var e=$root.humanboy_proto.BetDetail.verify(m.detail);if(e)return"detail."+e}}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}if(m.selfBet!=null&&m.hasOwnProperty("selfBet")){if(!$util.isInteger(m.selfBet)&&!(m.selfBet&&$util.isInteger(m.selfBet.low)&&$util.isInteger(m.selfBet.high)))return"selfBet: integer|Long expected"}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(!$util.isInteger(m.totalBet)&&!(m.totalBet&&$util.isInteger(m.totalBet.low)&&$util.isInteger(m.totalBet.high)))return"totalBet: integer|Long expected"}if(m.curUsdt!=null&&m.hasOwnProperty("curUsdt")){if(!$util.isInteger(m.curUsdt)&&!(m.curUsdt&&$util.isInteger(m.curUsdt.low)&&$util.isInteger(m.curUsdt.high)))return"curUsdt: integer|Long expected"}return null};BetNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.BetNotify)return d;var m=new $root.humanboy_proto.BetNotify;if(d.uid!=null){m.uid=d.uid>>>0}if(d.detail!=null){if(typeof d.detail!=="object")throw TypeError(".humanboy_proto.BetNotify.detail: object expected");m.detail=$root.humanboy_proto.BetDetail.fromObject(d.detail)}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=true;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber(true)}if(d.selfBet!=null){if($util.Long)(m.selfBet=$util.Long.fromValue(d.selfBet)).unsigned=true;else if(typeof d.selfBet==="string")m.selfBet=parseInt(d.selfBet,10);else if(typeof d.selfBet==="number")m.selfBet=d.selfBet;else if(typeof d.selfBet==="object")m.selfBet=new $util.LongBits(d.selfBet.low>>>0,d.selfBet.high>>>0).toNumber(true)}if(d.totalBet!=null){if($util.Long)(m.totalBet=$util.Long.fromValue(d.totalBet)).unsigned=true;else if(typeof d.totalBet==="string")m.totalBet=parseInt(d.totalBet,10);else if(typeof d.totalBet==="number")m.totalBet=d.totalBet;else if(typeof d.totalBet==="object")m.totalBet=new $util.LongBits(d.totalBet.low>>>0,d.totalBet.high>>>0).toNumber(true)}if(d.curUsdt!=null){if($util.Long)(m.curUsdt=$util.Long.fromValue(d.curUsdt)).unsigned=true;else if(typeof d.curUsdt==="string")m.curUsdt=parseInt(d.curUsdt,10);else if(typeof d.curUsdt==="number")m.curUsdt=d.curUsdt;else if(typeof d.curUsdt==="object")m.curUsdt=new $util.LongBits(d.curUsdt.low>>>0,d.curUsdt.high>>>0).toNumber(true)}return m};BetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;d.detail=null;if($util.Long){var n=new $util.Long(0,0,true);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.selfBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.selfBet=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.totalBet=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBet=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,true);d.curUsdt=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curUsdt=o.longs===String?"0":0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.detail!=null&&m.hasOwnProperty("detail")){d.detail=$root.humanboy_proto.BetDetail.toObject(m.detail,o)}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber(true):m.curCoin}if(m.selfBet!=null&&m.hasOwnProperty("selfBet")){if(typeof m.selfBet==="number")d.selfBet=o.longs===String?String(m.selfBet):m.selfBet;else d.selfBet=o.longs===String?$util.Long.prototype.toString.call(m.selfBet):o.longs===Number?new $util.LongBits(m.selfBet.low>>>0,m.selfBet.high>>>0).toNumber(true):m.selfBet}if(m.totalBet!=null&&m.hasOwnProperty("totalBet")){if(typeof m.totalBet==="number")d.totalBet=o.longs===String?String(m.totalBet):m.totalBet;else d.totalBet=o.longs===String?$util.Long.prototype.toString.call(m.totalBet):o.longs===Number?new $util.LongBits(m.totalBet.low>>>0,m.totalBet.high>>>0).toNumber(true):m.totalBet}if(m.curUsdt!=null&&m.hasOwnProperty("curUsdt")){if(typeof m.curUsdt==="number")d.curUsdt=o.longs===String?String(m.curUsdt):m.curUsdt;else d.curUsdt=o.longs===String?$util.Long.prototype.toString.call(m.curUsdt):o.longs===Number?new $util.LongBits(m.curUsdt.low>>>0,m.curUsdt.high>>>0).toNumber(true):m.curUsdt}return d};BetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetNotify}();humanboy_proto.MergeAutoBetNotify=function(){function MergeAutoBetNotify(p){this.notify=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}MergeAutoBetNotify.prototype.notify=$util.emptyArray;MergeAutoBetNotify.create=function create(properties){return new MergeAutoBetNotify(properties)};MergeAutoBetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.notify!=null&&m.notify.length){for(var i=0;i<m.notify.length;++i)$root.humanboy_proto.BetNotify.encode(m.notify[i],w.uint32(10).fork()).ldelim()}return w};MergeAutoBetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};MergeAutoBetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.MergeAutoBetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.notify&&m.notify.length))m.notify=[];m.notify.push($root.humanboy_proto.BetNotify.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};MergeAutoBetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};MergeAutoBetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.notify!=null&&m.hasOwnProperty("notify")){if(!Array.isArray(m.notify))return"notify: array expected";for(var i=0;i<m.notify.length;++i){{var e=$root.humanboy_proto.BetNotify.verify(m.notify[i]);if(e)return"notify."+e}}}return null};MergeAutoBetNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.MergeAutoBetNotify)return d;var m=new $root.humanboy_proto.MergeAutoBetNotify;if(d.notify){if(!Array.isArray(d.notify))throw TypeError(".humanboy_proto.MergeAutoBetNotify.notify: array expected");m.notify=[];for(var i=0;i<d.notify.length;++i){if(typeof d.notify[i]!=="object")throw TypeError(".humanboy_proto.MergeAutoBetNotify.notify: object expected");m.notify[i]=$root.humanboy_proto.BetNotify.fromObject(d.notify[i])}}return m};MergeAutoBetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.notify=[]}if(m.notify&&m.notify.length){d.notify=[];for(var j=0;j<m.notify.length;++j){d.notify[j]=$root.humanboy_proto.BetNotify.toObject(m.notify[j],o)}}return d};MergeAutoBetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return MergeAutoBetNotify}();humanboy_proto.BetDetail=function(){function BetDetail(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}BetDetail.prototype.option=0;BetDetail.prototype.betAmount=$util.Long?$util.Long.fromBits(0,0,true):0;BetDetail.prototype.auto=false;BetDetail.prototype.betGameCoin=$util.Long?$util.Long.fromBits(0,0,false):0;BetDetail.create=function create(properties){return new BetDetail(properties)};BetDetail.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(16).int32(m.option);if(m.betAmount!=null&&Object.hasOwnProperty.call(m,"betAmount"))w.uint32(24).uint64(m.betAmount);if(m.auto!=null&&Object.hasOwnProperty.call(m,"auto"))w.uint32(32).bool(m.auto);if(m.betGameCoin!=null&&Object.hasOwnProperty.call(m,"betGameCoin"))w.uint32(40).int64(m.betGameCoin);return w};BetDetail.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};BetDetail.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.BetDetail;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 2:m.option=r.int32();break;case 3:m.betAmount=r.uint64();break;case 4:m.auto=r.bool();break;case 5:m.betGameCoin=r.int64();break;default:r.skipType(t&7);break}}return m};BetDetail.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};BetDetail.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.betAmount!=null&&m.hasOwnProperty("betAmount")){if(!$util.isInteger(m.betAmount)&&!(m.betAmount&&$util.isInteger(m.betAmount.low)&&$util.isInteger(m.betAmount.high)))return"betAmount: integer|Long expected"}if(m.auto!=null&&m.hasOwnProperty("auto")){if(typeof m.auto!=="boolean")return"auto: boolean expected"}if(m.betGameCoin!=null&&m.hasOwnProperty("betGameCoin")){if(!$util.isInteger(m.betGameCoin)&&!(m.betGameCoin&&$util.isInteger(m.betGameCoin.low)&&$util.isInteger(m.betGameCoin.high)))return"betGameCoin: integer|Long expected"}return null};BetDetail.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.BetDetail)return d;var m=new $root.humanboy_proto.BetDetail;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.betAmount!=null){if($util.Long)(m.betAmount=$util.Long.fromValue(d.betAmount)).unsigned=true;else if(typeof d.betAmount==="string")m.betAmount=parseInt(d.betAmount,10);else if(typeof d.betAmount==="number")m.betAmount=d.betAmount;else if(typeof d.betAmount==="object")m.betAmount=new $util.LongBits(d.betAmount.low>>>0,d.betAmount.high>>>0).toNumber(true)}if(d.auto!=null){m.auto=Boolean(d.auto)}if(d.betGameCoin!=null){if($util.Long)(m.betGameCoin=$util.Long.fromValue(d.betGameCoin)).unsigned=false;else if(typeof d.betGameCoin==="string")m.betGameCoin=parseInt(d.betGameCoin,10);else if(typeof d.betGameCoin==="number")m.betGameCoin=d.betGameCoin;else if(typeof d.betGameCoin==="object")m.betGameCoin=new $util.LongBits(d.betGameCoin.low>>>0,d.betGameCoin.high>>>0).toNumber()}return m};BetDetail.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.betAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.betAmount=o.longs===String?"0":0;d.auto=false;if($util.Long){var n=new $util.Long(0,0,false);d.betGameCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.betGameCoin=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.betAmount!=null&&m.hasOwnProperty("betAmount")){if(typeof m.betAmount==="number")d.betAmount=o.longs===String?String(m.betAmount):m.betAmount;else d.betAmount=o.longs===String?$util.Long.prototype.toString.call(m.betAmount):o.longs===Number?new $util.LongBits(m.betAmount.low>>>0,m.betAmount.high>>>0).toNumber(true):m.betAmount}if(m.auto!=null&&m.hasOwnProperty("auto")){d.auto=m.auto}if(m.betGameCoin!=null&&m.hasOwnProperty("betGameCoin")){if(typeof m.betGameCoin==="number")d.betGameCoin=o.longs===String?String(m.betGameCoin):m.betGameCoin;else d.betGameCoin=o.longs===String?$util.Long.prototype.toString.call(m.betGameCoin):o.longs===Number?new $util.LongBits(m.betGameCoin.low>>>0,m.betGameCoin.high>>>0).toNumber():m.betGameCoin}return d};BetDetail.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return BetDetail}();humanboy_proto.GameRoundEndNotify=function(){function GameRoundEndNotify(p){this.playerHoleCard=[];this.playerSettle=[];this.matchOption=[];this.dealer=[];this.optionResult=[];this.hitJackpotAward=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameRoundEndNotify.prototype.playerHoleCard=$util.emptyArray;GameRoundEndNotify.prototype.playerSettle=$util.emptyArray;GameRoundEndNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.matchOption=$util.emptyArray;GameRoundEndNotify.prototype.stopWorld=0;GameRoundEndNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.otherPlayers=null;GameRoundEndNotify.prototype.maxLevel=0;GameRoundEndNotify.prototype.dealerWinAll=0;GameRoundEndNotify.prototype.jackpotLeftMoney=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.dealerTotalWin=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.dealer=$util.emptyArray;GameRoundEndNotify.prototype.optionResult=$util.emptyArray;GameRoundEndNotify.prototype.hitJackpotAward=$util.emptyArray;GameRoundEndNotify.prototype.maxLevelOption=0;GameRoundEndNotify.prototype.change_points=$util.Long?$util.Long.fromBits(0,0,false):0;GameRoundEndNotify.prototype.idle_roomid=0;GameRoundEndNotify.create=function create(properties){return new GameRoundEndNotify(properties)};GameRoundEndNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.playerHoleCard!=null&&m.playerHoleCard.length){for(var i=0;i<m.playerHoleCard.length;++i)$root.humanboy_proto.PlayerHoleCard.encode(m.playerHoleCard[i],w.uint32(10).fork()).ldelim()}if(m.playerSettle!=null&&m.playerSettle.length){for(var i=0;i<m.playerSettle.length;++i)$root.humanboy_proto.PlayerSettle.encode(m.playerSettle[i],w.uint32(26).fork()).ldelim()}if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(40).int64(m.nextRoundEndStamp);if(m.matchOption!=null&&m.matchOption.length){w.uint32(50).fork();for(var i=0;i<m.matchOption.length;++i)w.int32(m.matchOption[i]);w.ldelim()}if(m.stopWorld!=null&&Object.hasOwnProperty.call(m,"stopWorld"))w.uint32(56).int32(m.stopWorld);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(64).int64(m.leftSeconds);if(m.otherPlayers!=null&&Object.hasOwnProperty.call(m,"otherPlayers"))$root.humanboy_proto.PlayerSettle.encode(m.otherPlayers,w.uint32(74).fork()).ldelim();if(m.maxLevel!=null&&Object.hasOwnProperty.call(m,"maxLevel"))w.uint32(80).int32(m.maxLevel);if(m.dealerWinAll!=null&&Object.hasOwnProperty.call(m,"dealerWinAll"))w.uint32(88).uint32(m.dealerWinAll);if(m.jackpotLeftMoney!=null&&Object.hasOwnProperty.call(m,"jackpotLeftMoney"))w.uint32(96).int64(m.jackpotLeftMoney);if(m.dealerTotalWin!=null&&Object.hasOwnProperty.call(m,"dealerTotalWin"))w.uint32(104).int64(m.dealerTotalWin);if(m.dealer!=null&&m.dealer.length){for(var i=0;i<m.dealer.length;++i)$root.humanboy_proto.DealerPlayerInfo.encode(m.dealer[i],w.uint32(114).fork()).ldelim()}if(m.optionResult!=null&&m.optionResult.length){for(var i=0;i<m.optionResult.length;++i)$root.humanboy_proto.OptionResult.encode(m.optionResult[i],w.uint32(122).fork()).ldelim()}if(m.hitJackpotAward!=null&&m.hitJackpotAward.length){for(var i=0;i<m.hitJackpotAward.length;++i)$root.humanboy_proto.HitJackpotAward.encode(m.hitJackpotAward[i],w.uint32(130).fork()).ldelim()}if(m.maxLevelOption!=null&&Object.hasOwnProperty.call(m,"maxLevelOption"))w.uint32(136).int32(m.maxLevelOption);if(m.change_points!=null&&Object.hasOwnProperty.call(m,"change_points"))w.uint32(144).int64(m.change_points);if(m.idle_roomid!=null&&Object.hasOwnProperty.call(m,"idle_roomid"))w.uint32(152).uint32(m.idle_roomid);return w};GameRoundEndNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameRoundEndNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GameRoundEndNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.playerHoleCard&&m.playerHoleCard.length))m.playerHoleCard=[];m.playerHoleCard.push($root.humanboy_proto.PlayerHoleCard.decode(r,r.uint32()));break;case 3:if(!(m.playerSettle&&m.playerSettle.length))m.playerSettle=[];m.playerSettle.push($root.humanboy_proto.PlayerSettle.decode(r,r.uint32()));break;case 5:m.nextRoundEndStamp=r.int64();break;case 6:if(!(m.matchOption&&m.matchOption.length))m.matchOption=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.matchOption.push(r.int32())}else m.matchOption.push(r.int32());break;case 7:m.stopWorld=r.int32();break;case 8:m.leftSeconds=r.int64();break;case 9:m.otherPlayers=$root.humanboy_proto.PlayerSettle.decode(r,r.uint32());break;case 10:m.maxLevel=r.int32();break;case 11:m.dealerWinAll=r.uint32();break;case 12:m.jackpotLeftMoney=r.int64();break;case 13:m.dealerTotalWin=r.int64();break;case 14:if(!(m.dealer&&m.dealer.length))m.dealer=[];m.dealer.push($root.humanboy_proto.DealerPlayerInfo.decode(r,r.uint32()));break;case 15:if(!(m.optionResult&&m.optionResult.length))m.optionResult=[];m.optionResult.push($root.humanboy_proto.OptionResult.decode(r,r.uint32()));break;case 16:if(!(m.hitJackpotAward&&m.hitJackpotAward.length))m.hitJackpotAward=[];m.hitJackpotAward.push($root.humanboy_proto.HitJackpotAward.decode(r,r.uint32()));break;case 17:m.maxLevelOption=r.int32();break;case 18:m.change_points=r.int64();break;case 19:m.idle_roomid=r.uint32();break;default:r.skipType(t&7);break}}return m};GameRoundEndNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameRoundEndNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.playerHoleCard!=null&&m.hasOwnProperty("playerHoleCard")){if(!Array.isArray(m.playerHoleCard))return"playerHoleCard: array expected";for(var i=0;i<m.playerHoleCard.length;++i){{var e=$root.humanboy_proto.PlayerHoleCard.verify(m.playerHoleCard[i]);if(e)return"playerHoleCard."+e}}}if(m.playerSettle!=null&&m.hasOwnProperty("playerSettle")){if(!Array.isArray(m.playerSettle))return"playerSettle: array expected";for(var i=0;i<m.playerSettle.length;++i){{var e=$root.humanboy_proto.PlayerSettle.verify(m.playerSettle[i]);if(e)return"playerSettle."+e}}}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.matchOption!=null&&m.hasOwnProperty("matchOption")){if(!Array.isArray(m.matchOption))return"matchOption: array expected";for(var i=0;i<m.matchOption.length;++i){switch(m.matchOption[i]){default:return"matchOption: enum value[] expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}}if(m.stopWorld!=null&&m.hasOwnProperty("stopWorld")){if(!$util.isInteger(m.stopWorld))return"stopWorld: integer expected"}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.otherPlayers!=null&&m.hasOwnProperty("otherPlayers")){{var e=$root.humanboy_proto.PlayerSettle.verify(m.otherPlayers);if(e)return"otherPlayers."+e}}if(m.maxLevel!=null&&m.hasOwnProperty("maxLevel")){switch(m.maxLevel){default:return"maxLevel: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:break}}if(m.dealerWinAll!=null&&m.hasOwnProperty("dealerWinAll")){if(!$util.isInteger(m.dealerWinAll))return"dealerWinAll: integer expected"}if(m.jackpotLeftMoney!=null&&m.hasOwnProperty("jackpotLeftMoney")){if(!$util.isInteger(m.jackpotLeftMoney)&&!(m.jackpotLeftMoney&&$util.isInteger(m.jackpotLeftMoney.low)&&$util.isInteger(m.jackpotLeftMoney.high)))return"jackpotLeftMoney: integer|Long expected"}if(m.dealerTotalWin!=null&&m.hasOwnProperty("dealerTotalWin")){if(!$util.isInteger(m.dealerTotalWin)&&!(m.dealerTotalWin&&$util.isInteger(m.dealerTotalWin.low)&&$util.isInteger(m.dealerTotalWin.high)))return"dealerTotalWin: integer|Long expected"}if(m.dealer!=null&&m.hasOwnProperty("dealer")){if(!Array.isArray(m.dealer))return"dealer: array expected";for(var i=0;i<m.dealer.length;++i){{var e=$root.humanboy_proto.DealerPlayerInfo.verify(m.dealer[i]);if(e)return"dealer."+e}}}if(m.optionResult!=null&&m.hasOwnProperty("optionResult")){if(!Array.isArray(m.optionResult))return"optionResult: array expected";for(var i=0;i<m.optionResult.length;++i){{var e=$root.humanboy_proto.OptionResult.verify(m.optionResult[i]);if(e)return"optionResult."+e}}}if(m.hitJackpotAward!=null&&m.hasOwnProperty("hitJackpotAward")){if(!Array.isArray(m.hitJackpotAward))return"hitJackpotAward: array expected";for(var i=0;i<m.hitJackpotAward.length;++i){{var e=$root.humanboy_proto.HitJackpotAward.verify(m.hitJackpotAward[i]);if(e)return"hitJackpotAward."+e}}}if(m.maxLevelOption!=null&&m.hasOwnProperty("maxLevelOption")){switch(m.maxLevelOption){default:return"maxLevelOption: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.change_points!=null&&m.hasOwnProperty("change_points")){if(!$util.isInteger(m.change_points)&&!(m.change_points&&$util.isInteger(m.change_points.low)&&$util.isInteger(m.change_points.high)))return"change_points: integer|Long expected"}if(m.idle_roomid!=null&&m.hasOwnProperty("idle_roomid")){if(!$util.isInteger(m.idle_roomid))return"idle_roomid: integer expected"}return null};GameRoundEndNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GameRoundEndNotify)return d;var m=new $root.humanboy_proto.GameRoundEndNotify;if(d.playerHoleCard){if(!Array.isArray(d.playerHoleCard))throw TypeError(".humanboy_proto.GameRoundEndNotify.playerHoleCard: array expected");m.playerHoleCard=[];for(var i=0;i<d.playerHoleCard.length;++i){if(typeof d.playerHoleCard[i]!=="object")throw TypeError(".humanboy_proto.GameRoundEndNotify.playerHoleCard: object expected");m.playerHoleCard[i]=$root.humanboy_proto.PlayerHoleCard.fromObject(d.playerHoleCard[i])}}if(d.playerSettle){if(!Array.isArray(d.playerSettle))throw TypeError(".humanboy_proto.GameRoundEndNotify.playerSettle: array expected");m.playerSettle=[];for(var i=0;i<d.playerSettle.length;++i){if(typeof d.playerSettle[i]!=="object")throw TypeError(".humanboy_proto.GameRoundEndNotify.playerSettle: object expected");m.playerSettle[i]=$root.humanboy_proto.PlayerSettle.fromObject(d.playerSettle[i])}}if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.matchOption){if(!Array.isArray(d.matchOption))throw TypeError(".humanboy_proto.GameRoundEndNotify.matchOption: array expected");m.matchOption=[];for(var i=0;i<d.matchOption.length;++i){switch(d.matchOption[i]){default:case"BetZoneOption_DUMMY":case 0:m.matchOption[i]=0;break;case"HOST":case 1:m.matchOption[i]=1;break;case"POS1":case 2:m.matchOption[i]=2;break;case"POS2":case 3:m.matchOption[i]=3;break;case"POS3":case 4:m.matchOption[i]=4;break;case"POS4":case 5:m.matchOption[i]=5;break;case"POS_LUCK":case 100:m.matchOption[i]=100;break;case"POS_LUCK_1":case 101:m.matchOption[i]=101;break;case"POS_LUCK_2":case 102:m.matchOption[i]=102;break;case"POS_LUCK_3":case 103:m.matchOption[i]=103;break;case"POS_LUCK_4":case 104:m.matchOption[i]=104;break;case"POS_LUCK_5":case 105:m.matchOption[i]=105;break;case"POS_LUCK_6":case 106:m.matchOption[i]=106;break}}}if(d.stopWorld!=null){m.stopWorld=d.stopWorld|0}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.otherPlayers!=null){if(typeof d.otherPlayers!=="object")throw TypeError(".humanboy_proto.GameRoundEndNotify.otherPlayers: object expected");m.otherPlayers=$root.humanboy_proto.PlayerSettle.fromObject(d.otherPlayers)}switch(d.maxLevel){case"CardResult_Dummy":case 0:m.maxLevel=0;break;case"GAO_PAI":case 1:m.maxLevel=1;break;case"YI_DUI":case 2:m.maxLevel=2;break;case"LIAN_DUI":case 3:m.maxLevel=3;break;case"SAN_TIAO":case 4:m.maxLevel=4;break;case"SHUN_ZI":case 5:m.maxLevel=5;break;case"TONG_HUA":case 6:m.maxLevel=6;break;case"HU_LU":case 7:m.maxLevel=7;break;case"SI_TIAO":case 8:m.maxLevel=8;break;case"TONG_HUA_SHUN":case 9:m.maxLevel=9;break;case"HUANG_TONG":case 10:m.maxLevel=10;break}if(d.dealerWinAll!=null){m.dealerWinAll=d.dealerWinAll>>>0}if(d.jackpotLeftMoney!=null){if($util.Long)(m.jackpotLeftMoney=$util.Long.fromValue(d.jackpotLeftMoney)).unsigned=false;else if(typeof d.jackpotLeftMoney==="string")m.jackpotLeftMoney=parseInt(d.jackpotLeftMoney,10);else if(typeof d.jackpotLeftMoney==="number")m.jackpotLeftMoney=d.jackpotLeftMoney;else if(typeof d.jackpotLeftMoney==="object")m.jackpotLeftMoney=new $util.LongBits(d.jackpotLeftMoney.low>>>0,d.jackpotLeftMoney.high>>>0).toNumber()}if(d.dealerTotalWin!=null){if($util.Long)(m.dealerTotalWin=$util.Long.fromValue(d.dealerTotalWin)).unsigned=false;else if(typeof d.dealerTotalWin==="string")m.dealerTotalWin=parseInt(d.dealerTotalWin,10);else if(typeof d.dealerTotalWin==="number")m.dealerTotalWin=d.dealerTotalWin;else if(typeof d.dealerTotalWin==="object")m.dealerTotalWin=new $util.LongBits(d.dealerTotalWin.low>>>0,d.dealerTotalWin.high>>>0).toNumber()}if(d.dealer){if(!Array.isArray(d.dealer))throw TypeError(".humanboy_proto.GameRoundEndNotify.dealer: array expected");m.dealer=[];for(var i=0;i<d.dealer.length;++i){if(typeof d.dealer[i]!=="object")throw TypeError(".humanboy_proto.GameRoundEndNotify.dealer: object expected");m.dealer[i]=$root.humanboy_proto.DealerPlayerInfo.fromObject(d.dealer[i])}}if(d.optionResult){if(!Array.isArray(d.optionResult))throw TypeError(".humanboy_proto.GameRoundEndNotify.optionResult: array expected");m.optionResult=[];for(var i=0;i<d.optionResult.length;++i){if(typeof d.optionResult[i]!=="object")throw TypeError(".humanboy_proto.GameRoundEndNotify.optionResult: object expected");m.optionResult[i]=$root.humanboy_proto.OptionResult.fromObject(d.optionResult[i])}}if(d.hitJackpotAward){if(!Array.isArray(d.hitJackpotAward))throw TypeError(".humanboy_proto.GameRoundEndNotify.hitJackpotAward: array expected");m.hitJackpotAward=[];for(var i=0;i<d.hitJackpotAward.length;++i){if(typeof d.hitJackpotAward[i]!=="object")throw TypeError(".humanboy_proto.GameRoundEndNotify.hitJackpotAward: object expected");m.hitJackpotAward[i]=$root.humanboy_proto.HitJackpotAward.fromObject(d.hitJackpotAward[i])}}switch(d.maxLevelOption){case"BetZoneOption_DUMMY":case 0:m.maxLevelOption=0;break;case"HOST":case 1:m.maxLevelOption=1;break;case"POS1":case 2:m.maxLevelOption=2;break;case"POS2":case 3:m.maxLevelOption=3;break;case"POS3":case 4:m.maxLevelOption=4;break;case"POS4":case 5:m.maxLevelOption=5;break;case"POS_LUCK":case 100:m.maxLevelOption=100;break;case"POS_LUCK_1":case 101:m.maxLevelOption=101;break;case"POS_LUCK_2":case 102:m.maxLevelOption=102;break;case"POS_LUCK_3":case 103:m.maxLevelOption=103;break;case"POS_LUCK_4":case 104:m.maxLevelOption=104;break;case"POS_LUCK_5":case 105:m.maxLevelOption=105;break;case"POS_LUCK_6":case 106:m.maxLevelOption=106;break}if(d.change_points!=null){if($util.Long)(m.change_points=$util.Long.fromValue(d.change_points)).unsigned=false;else if(typeof d.change_points==="string")m.change_points=parseInt(d.change_points,10);else if(typeof d.change_points==="number")m.change_points=d.change_points;else if(typeof d.change_points==="object")m.change_points=new $util.LongBits(d.change_points.low>>>0,d.change_points.high>>>0).toNumber()}if(d.idle_roomid!=null){m.idle_roomid=d.idle_roomid>>>0}return m};GameRoundEndNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.playerHoleCard=[];d.playerSettle=[];d.matchOption=[];d.dealer=[];d.optionResult=[];d.hitJackpotAward=[]}if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;d.stopWorld=0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;d.otherPlayers=null;d.maxLevel=o.enums===String?"CardResult_Dummy":0;d.dealerWinAll=0;if($util.Long){var n=new $util.Long(0,0,false);d.jackpotLeftMoney=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.jackpotLeftMoney=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.dealerTotalWin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.dealerTotalWin=o.longs===String?"0":0;d.maxLevelOption=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.change_points=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.change_points=o.longs===String?"0":0;d.idle_roomid=0}if(m.playerHoleCard&&m.playerHoleCard.length){d.playerHoleCard=[];for(var j=0;j<m.playerHoleCard.length;++j){d.playerHoleCard[j]=$root.humanboy_proto.PlayerHoleCard.toObject(m.playerHoleCard[j],o)}}if(m.playerSettle&&m.playerSettle.length){d.playerSettle=[];for(var j=0;j<m.playerSettle.length;++j){d.playerSettle[j]=$root.humanboy_proto.PlayerSettle.toObject(m.playerSettle[j],o)}}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.matchOption&&m.matchOption.length){d.matchOption=[];for(var j=0;j<m.matchOption.length;++j){d.matchOption[j]=o.enums===String?$root.humanboy_proto.BetZoneOption[m.matchOption[j]]:m.matchOption[j]}}if(m.stopWorld!=null&&m.hasOwnProperty("stopWorld")){d.stopWorld=m.stopWorld}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.otherPlayers!=null&&m.hasOwnProperty("otherPlayers")){d.otherPlayers=$root.humanboy_proto.PlayerSettle.toObject(m.otherPlayers,o)}if(m.maxLevel!=null&&m.hasOwnProperty("maxLevel")){d.maxLevel=o.enums===String?$root.humanboy_proto.CardResult[m.maxLevel]:m.maxLevel}if(m.dealerWinAll!=null&&m.hasOwnProperty("dealerWinAll")){d.dealerWinAll=m.dealerWinAll}if(m.jackpotLeftMoney!=null&&m.hasOwnProperty("jackpotLeftMoney")){if(typeof m.jackpotLeftMoney==="number")d.jackpotLeftMoney=o.longs===String?String(m.jackpotLeftMoney):m.jackpotLeftMoney;else d.jackpotLeftMoney=o.longs===String?$util.Long.prototype.toString.call(m.jackpotLeftMoney):o.longs===Number?new $util.LongBits(m.jackpotLeftMoney.low>>>0,m.jackpotLeftMoney.high>>>0).toNumber():m.jackpotLeftMoney}if(m.dealerTotalWin!=null&&m.hasOwnProperty("dealerTotalWin")){if(typeof m.dealerTotalWin==="number")d.dealerTotalWin=o.longs===String?String(m.dealerTotalWin):m.dealerTotalWin;else d.dealerTotalWin=o.longs===String?$util.Long.prototype.toString.call(m.dealerTotalWin):o.longs===Number?new $util.LongBits(m.dealerTotalWin.low>>>0,m.dealerTotalWin.high>>>0).toNumber():m.dealerTotalWin}if(m.dealer&&m.dealer.length){d.dealer=[];for(var j=0;j<m.dealer.length;++j){d.dealer[j]=$root.humanboy_proto.DealerPlayerInfo.toObject(m.dealer[j],o)}}if(m.optionResult&&m.optionResult.length){d.optionResult=[];for(var j=0;j<m.optionResult.length;++j){d.optionResult[j]=$root.humanboy_proto.OptionResult.toObject(m.optionResult[j],o)}}if(m.hitJackpotAward&&m.hitJackpotAward.length){d.hitJackpotAward=[];for(var j=0;j<m.hitJackpotAward.length;++j){d.hitJackpotAward[j]=$root.humanboy_proto.HitJackpotAward.toObject(m.hitJackpotAward[j],o)}}if(m.maxLevelOption!=null&&m.hasOwnProperty("maxLevelOption")){d.maxLevelOption=o.enums===String?$root.humanboy_proto.BetZoneOption[m.maxLevelOption]:m.maxLevelOption}if(m.change_points!=null&&m.hasOwnProperty("change_points")){if(typeof m.change_points==="number")d.change_points=o.longs===String?String(m.change_points):m.change_points;else d.change_points=o.longs===String?$util.Long.prototype.toString.call(m.change_points):o.longs===Number?new $util.LongBits(m.change_points.low>>>0,m.change_points.high>>>0).toNumber():m.change_points}if(m.idle_roomid!=null&&m.hasOwnProperty("idle_roomid")){d.idle_roomid=m.idle_roomid}return d};GameRoundEndNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameRoundEndNotify}();humanboy_proto.OptionResult=function(){function OptionResult(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}OptionResult.prototype.option=0;OptionResult.prototype.result=0;OptionResult.prototype.loseHand=0;OptionResult.create=function create(properties){return new OptionResult(properties)};OptionResult.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.result!=null&&Object.hasOwnProperty.call(m,"result"))w.uint32(16).int32(m.result);if(m.loseHand!=null&&Object.hasOwnProperty.call(m,"loseHand"))w.uint32(24).int32(m.loseHand);return w};OptionResult.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};OptionResult.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.OptionResult;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:m.result=r.int32();break;case 3:m.loseHand=r.int32();break;default:r.skipType(t&7);break}}return m};OptionResult.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};OptionResult.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.result!=null&&m.hasOwnProperty("result")){if(!$util.isInteger(m.result))return"result: integer expected"}if(m.loseHand!=null&&m.hasOwnProperty("loseHand")){if(!$util.isInteger(m.loseHand))return"loseHand: integer expected"}return null};OptionResult.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.OptionResult)return d;var m=new $root.humanboy_proto.OptionResult;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.result!=null){m.result=d.result|0}if(d.loseHand!=null){m.loseHand=d.loseHand|0}return m};OptionResult.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;d.result=0;d.loseHand=0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.result!=null&&m.hasOwnProperty("result")){d.result=m.result}if(m.loseHand!=null&&m.hasOwnProperty("loseHand")){d.loseHand=m.loseHand}return d};OptionResult.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return OptionResult}();humanboy_proto.PlayerHoleCard=function(){function PlayerHoleCard(p){this.holeCards=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerHoleCard.prototype.option=0;PlayerHoleCard.prototype.holeCards=$util.emptyArray;PlayerHoleCard.prototype.level=0;PlayerHoleCard.prototype.result=0;PlayerHoleCard.create=function create(properties){return new PlayerHoleCard(properties)};PlayerHoleCard.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.holeCards!=null&&m.holeCards.length){for(var i=0;i<m.holeCards.length;++i)$root.humanboy_proto.CardItem.encode(m.holeCards[i],w.uint32(18).fork()).ldelim()}if(m.level!=null&&Object.hasOwnProperty.call(m,"level"))w.uint32(24).int32(m.level);if(m.result!=null&&Object.hasOwnProperty.call(m,"result"))w.uint32(40).int32(m.result);return w};PlayerHoleCard.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerHoleCard.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.PlayerHoleCard;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:if(!(m.holeCards&&m.holeCards.length))m.holeCards=[];m.holeCards.push($root.humanboy_proto.CardItem.decode(r,r.uint32()));break;case 3:m.level=r.int32();break;case 5:m.result=r.int32();break;default:r.skipType(t&7);break}}return m};PlayerHoleCard.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerHoleCard.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.holeCards!=null&&m.hasOwnProperty("holeCards")){if(!Array.isArray(m.holeCards))return"holeCards: array expected";for(var i=0;i<m.holeCards.length;++i){{var e=$root.humanboy_proto.CardItem.verify(m.holeCards[i]);if(e)return"holeCards."+e}}}if(m.level!=null&&m.hasOwnProperty("level")){switch(m.level){default:return"level: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:break}}if(m.result!=null&&m.hasOwnProperty("result")){if(!$util.isInteger(m.result))return"result: integer expected"}return null};PlayerHoleCard.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.PlayerHoleCard)return d;var m=new $root.humanboy_proto.PlayerHoleCard;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.holeCards){if(!Array.isArray(d.holeCards))throw TypeError(".humanboy_proto.PlayerHoleCard.holeCards: array expected");m.holeCards=[];for(var i=0;i<d.holeCards.length;++i){if(typeof d.holeCards[i]!=="object")throw TypeError(".humanboy_proto.PlayerHoleCard.holeCards: object expected");m.holeCards[i]=$root.humanboy_proto.CardItem.fromObject(d.holeCards[i])}}switch(d.level){case"CardResult_Dummy":case 0:m.level=0;break;case"GAO_PAI":case 1:m.level=1;break;case"YI_DUI":case 2:m.level=2;break;case"LIAN_DUI":case 3:m.level=3;break;case"SAN_TIAO":case 4:m.level=4;break;case"SHUN_ZI":case 5:m.level=5;break;case"TONG_HUA":case 6:m.level=6;break;case"HU_LU":case 7:m.level=7;break;case"SI_TIAO":case 8:m.level=8;break;case"TONG_HUA_SHUN":case 9:m.level=9;break;case"HUANG_TONG":case 10:m.level=10;break}if(d.result!=null){m.result=d.result|0}return m};PlayerHoleCard.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.holeCards=[]}if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;d.level=o.enums===String?"CardResult_Dummy":0;d.result=0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.holeCards&&m.holeCards.length){d.holeCards=[];for(var j=0;j<m.holeCards.length;++j){d.holeCards[j]=$root.humanboy_proto.CardItem.toObject(m.holeCards[j],o)}}if(m.level!=null&&m.hasOwnProperty("level")){d.level=o.enums===String?$root.humanboy_proto.CardResult[m.level]:m.level}if(m.result!=null&&m.hasOwnProperty("result")){d.result=m.result}return d};PlayerHoleCard.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerHoleCard}();humanboy_proto.PlayerSettle=function(){function PlayerSettle(p){this.settle=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerSettle.prototype.uid=0;PlayerSettle.prototype.settle=$util.emptyArray;PlayerSettle.prototype.totalWinAmount=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerSettle.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerSettle.prototype.keepWinCount=0;PlayerSettle.prototype.hasBet=0;PlayerSettle.prototype.pos4WinAmount=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerSettle.prototype.posLuckWinAmount=$util.Long?$util.Long.fromBits(0,0,false):0;PlayerSettle.create=function create(properties){return new PlayerSettle(properties)};PlayerSettle.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.settle!=null&&m.settle.length){for(var i=0;i<m.settle.length;++i)$root.humanboy_proto.ZoneSettleDetail.encode(m.settle[i],w.uint32(18).fork()).ldelim()}if(m.totalWinAmount!=null&&Object.hasOwnProperty.call(m,"totalWinAmount"))w.uint32(24).int64(m.totalWinAmount);if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(32).int64(m.curCoin);if(m.keepWinCount!=null&&Object.hasOwnProperty.call(m,"keepWinCount"))w.uint32(40).int32(m.keepWinCount);if(m.hasBet!=null&&Object.hasOwnProperty.call(m,"hasBet"))w.uint32(48).int32(m.hasBet);if(m.pos4WinAmount!=null&&Object.hasOwnProperty.call(m,"pos4WinAmount"))w.uint32(56).int64(m.pos4WinAmount);if(m.posLuckWinAmount!=null&&Object.hasOwnProperty.call(m,"posLuckWinAmount"))w.uint32(64).int64(m.posLuckWinAmount);return w};PlayerSettle.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerSettle.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.PlayerSettle;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:if(!(m.settle&&m.settle.length))m.settle=[];m.settle.push($root.humanboy_proto.ZoneSettleDetail.decode(r,r.uint32()));break;case 3:m.totalWinAmount=r.int64();break;case 4:m.curCoin=r.int64();break;case 5:m.keepWinCount=r.int32();break;case 6:m.hasBet=r.int32();break;case 7:m.pos4WinAmount=r.int64();break;case 8:m.posLuckWinAmount=r.int64();break;default:r.skipType(t&7);break}}return m};PlayerSettle.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerSettle.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.settle!=null&&m.hasOwnProperty("settle")){if(!Array.isArray(m.settle))return"settle: array expected";for(var i=0;i<m.settle.length;++i){{var e=$root.humanboy_proto.ZoneSettleDetail.verify(m.settle[i]);if(e)return"settle."+e}}}if(m.totalWinAmount!=null&&m.hasOwnProperty("totalWinAmount")){if(!$util.isInteger(m.totalWinAmount)&&!(m.totalWinAmount&&$util.isInteger(m.totalWinAmount.low)&&$util.isInteger(m.totalWinAmount.high)))return"totalWinAmount: integer|Long expected"}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}if(m.keepWinCount!=null&&m.hasOwnProperty("keepWinCount")){if(!$util.isInteger(m.keepWinCount))return"keepWinCount: integer expected"}if(m.hasBet!=null&&m.hasOwnProperty("hasBet")){if(!$util.isInteger(m.hasBet))return"hasBet: integer expected"}if(m.pos4WinAmount!=null&&m.hasOwnProperty("pos4WinAmount")){if(!$util.isInteger(m.pos4WinAmount)&&!(m.pos4WinAmount&&$util.isInteger(m.pos4WinAmount.low)&&$util.isInteger(m.pos4WinAmount.high)))return"pos4WinAmount: integer|Long expected"}if(m.posLuckWinAmount!=null&&m.hasOwnProperty("posLuckWinAmount")){if(!$util.isInteger(m.posLuckWinAmount)&&!(m.posLuckWinAmount&&$util.isInteger(m.posLuckWinAmount.low)&&$util.isInteger(m.posLuckWinAmount.high)))return"posLuckWinAmount: integer|Long expected"}return null};PlayerSettle.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.PlayerSettle)return d;var m=new $root.humanboy_proto.PlayerSettle;if(d.uid!=null){m.uid=d.uid>>>0}if(d.settle){if(!Array.isArray(d.settle))throw TypeError(".humanboy_proto.PlayerSettle.settle: array expected");m.settle=[];for(var i=0;i<d.settle.length;++i){if(typeof d.settle[i]!=="object")throw TypeError(".humanboy_proto.PlayerSettle.settle: object expected");m.settle[i]=$root.humanboy_proto.ZoneSettleDetail.fromObject(d.settle[i])}}if(d.totalWinAmount!=null){if($util.Long)(m.totalWinAmount=$util.Long.fromValue(d.totalWinAmount)).unsigned=false;else if(typeof d.totalWinAmount==="string")m.totalWinAmount=parseInt(d.totalWinAmount,10);else if(typeof d.totalWinAmount==="number")m.totalWinAmount=d.totalWinAmount;else if(typeof d.totalWinAmount==="object")m.totalWinAmount=new $util.LongBits(d.totalWinAmount.low>>>0,d.totalWinAmount.high>>>0).toNumber()}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=false;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber()}if(d.keepWinCount!=null){m.keepWinCount=d.keepWinCount|0}if(d.hasBet!=null){m.hasBet=d.hasBet|0}if(d.pos4WinAmount!=null){if($util.Long)(m.pos4WinAmount=$util.Long.fromValue(d.pos4WinAmount)).unsigned=false;else if(typeof d.pos4WinAmount==="string")m.pos4WinAmount=parseInt(d.pos4WinAmount,10);else if(typeof d.pos4WinAmount==="number")m.pos4WinAmount=d.pos4WinAmount;else if(typeof d.pos4WinAmount==="object")m.pos4WinAmount=new $util.LongBits(d.pos4WinAmount.low>>>0,d.pos4WinAmount.high>>>0).toNumber()}if(d.posLuckWinAmount!=null){if($util.Long)(m.posLuckWinAmount=$util.Long.fromValue(d.posLuckWinAmount)).unsigned=false;else if(typeof d.posLuckWinAmount==="string")m.posLuckWinAmount=parseInt(d.posLuckWinAmount,10);else if(typeof d.posLuckWinAmount==="number")m.posLuckWinAmount=d.posLuckWinAmount;else if(typeof d.posLuckWinAmount==="object")m.posLuckWinAmount=new $util.LongBits(d.posLuckWinAmount.low>>>0,d.posLuckWinAmount.high>>>0).toNumber()}return m};PlayerSettle.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.settle=[]}if(o.defaults){d.uid=0;if($util.Long){var n=new $util.Long(0,0,false);d.totalWinAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalWinAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0;d.keepWinCount=0;d.hasBet=0;if($util.Long){var n=new $util.Long(0,0,false);d.pos4WinAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.pos4WinAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.posLuckWinAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.posLuckWinAmount=o.longs===String?"0":0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.settle&&m.settle.length){d.settle=[];for(var j=0;j<m.settle.length;++j){d.settle[j]=$root.humanboy_proto.ZoneSettleDetail.toObject(m.settle[j],o)}}if(m.totalWinAmount!=null&&m.hasOwnProperty("totalWinAmount")){if(typeof m.totalWinAmount==="number")d.totalWinAmount=o.longs===String?String(m.totalWinAmount):m.totalWinAmount;else d.totalWinAmount=o.longs===String?$util.Long.prototype.toString.call(m.totalWinAmount):o.longs===Number?new $util.LongBits(m.totalWinAmount.low>>>0,m.totalWinAmount.high>>>0).toNumber():m.totalWinAmount}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber():m.curCoin}if(m.keepWinCount!=null&&m.hasOwnProperty("keepWinCount")){d.keepWinCount=m.keepWinCount}if(m.hasBet!=null&&m.hasOwnProperty("hasBet")){d.hasBet=m.hasBet}if(m.pos4WinAmount!=null&&m.hasOwnProperty("pos4WinAmount")){if(typeof m.pos4WinAmount==="number")d.pos4WinAmount=o.longs===String?String(m.pos4WinAmount):m.pos4WinAmount;else d.pos4WinAmount=o.longs===String?$util.Long.prototype.toString.call(m.pos4WinAmount):o.longs===Number?new $util.LongBits(m.pos4WinAmount.low>>>0,m.pos4WinAmount.high>>>0).toNumber():m.pos4WinAmount}if(m.posLuckWinAmount!=null&&m.hasOwnProperty("posLuckWinAmount")){if(typeof m.posLuckWinAmount==="number")d.posLuckWinAmount=o.longs===String?String(m.posLuckWinAmount):m.posLuckWinAmount;else d.posLuckWinAmount=o.longs===String?$util.Long.prototype.toString.call(m.posLuckWinAmount):o.longs===Number?new $util.LongBits(m.posLuckWinAmount.low>>>0,m.posLuckWinAmount.high>>>0).toNumber():m.posLuckWinAmount}return d};PlayerSettle.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerSettle}();humanboy_proto.ZoneSettleDetail=function(){function ZoneSettleDetail(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ZoneSettleDetail.prototype.option=0;ZoneSettleDetail.prototype.betAmount=$util.Long?$util.Long.fromBits(0,0,true):0;ZoneSettleDetail.prototype.winAmount=$util.Long?$util.Long.fromBits(0,0,false):0;ZoneSettleDetail.prototype.isAuto=0;ZoneSettleDetail.prototype.betGameCoin=$util.Long?$util.Long.fromBits(0,0,false):0;ZoneSettleDetail.create=function create(properties){return new ZoneSettleDetail(properties)};ZoneSettleDetail.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(16).int32(m.option);if(m.betAmount!=null&&Object.hasOwnProperty.call(m,"betAmount"))w.uint32(24).uint64(m.betAmount);if(m.winAmount!=null&&Object.hasOwnProperty.call(m,"winAmount"))w.uint32(32).int64(m.winAmount);if(m.isAuto!=null&&Object.hasOwnProperty.call(m,"isAuto"))w.uint32(40).int32(m.isAuto);if(m.betGameCoin!=null&&Object.hasOwnProperty.call(m,"betGameCoin"))w.uint32(48).int64(m.betGameCoin);return w};ZoneSettleDetail.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ZoneSettleDetail.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.ZoneSettleDetail;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 2:m.option=r.int32();break;case 3:m.betAmount=r.uint64();break;case 4:m.winAmount=r.int64();break;case 5:m.isAuto=r.int32();break;case 6:m.betGameCoin=r.int64();break;default:r.skipType(t&7);break}}return m};ZoneSettleDetail.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ZoneSettleDetail.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.betAmount!=null&&m.hasOwnProperty("betAmount")){if(!$util.isInteger(m.betAmount)&&!(m.betAmount&&$util.isInteger(m.betAmount.low)&&$util.isInteger(m.betAmount.high)))return"betAmount: integer|Long expected"}if(m.winAmount!=null&&m.hasOwnProperty("winAmount")){if(!$util.isInteger(m.winAmount)&&!(m.winAmount&&$util.isInteger(m.winAmount.low)&&$util.isInteger(m.winAmount.high)))return"winAmount: integer|Long expected"}if(m.isAuto!=null&&m.hasOwnProperty("isAuto")){if(!$util.isInteger(m.isAuto))return"isAuto: integer expected"}if(m.betGameCoin!=null&&m.hasOwnProperty("betGameCoin")){if(!$util.isInteger(m.betGameCoin)&&!(m.betGameCoin&&$util.isInteger(m.betGameCoin.low)&&$util.isInteger(m.betGameCoin.high)))return"betGameCoin: integer|Long expected"}return null};ZoneSettleDetail.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.ZoneSettleDetail)return d;var m=new $root.humanboy_proto.ZoneSettleDetail;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.betAmount!=null){if($util.Long)(m.betAmount=$util.Long.fromValue(d.betAmount)).unsigned=true;else if(typeof d.betAmount==="string")m.betAmount=parseInt(d.betAmount,10);else if(typeof d.betAmount==="number")m.betAmount=d.betAmount;else if(typeof d.betAmount==="object")m.betAmount=new $util.LongBits(d.betAmount.low>>>0,d.betAmount.high>>>0).toNumber(true)}if(d.winAmount!=null){if($util.Long)(m.winAmount=$util.Long.fromValue(d.winAmount)).unsigned=false;else if(typeof d.winAmount==="string")m.winAmount=parseInt(d.winAmount,10);else if(typeof d.winAmount==="number")m.winAmount=d.winAmount;else if(typeof d.winAmount==="object")m.winAmount=new $util.LongBits(d.winAmount.low>>>0,d.winAmount.high>>>0).toNumber()}if(d.isAuto!=null){m.isAuto=d.isAuto|0}if(d.betGameCoin!=null){if($util.Long)(m.betGameCoin=$util.Long.fromValue(d.betGameCoin)).unsigned=false;else if(typeof d.betGameCoin==="string")m.betGameCoin=parseInt(d.betGameCoin,10);else if(typeof d.betGameCoin==="number")m.betGameCoin=d.betGameCoin;else if(typeof d.betGameCoin==="object")m.betGameCoin=new $util.LongBits(d.betGameCoin.low>>>0,d.betGameCoin.high>>>0).toNumber()}return m};ZoneSettleDetail.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,true);d.betAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.betAmount=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.winAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.winAmount=o.longs===String?"0":0;d.isAuto=0;if($util.Long){var n=new $util.Long(0,0,false);d.betGameCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.betGameCoin=o.longs===String?"0":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.betAmount!=null&&m.hasOwnProperty("betAmount")){if(typeof m.betAmount==="number")d.betAmount=o.longs===String?String(m.betAmount):m.betAmount;else d.betAmount=o.longs===String?$util.Long.prototype.toString.call(m.betAmount):o.longs===Number?new $util.LongBits(m.betAmount.low>>>0,m.betAmount.high>>>0).toNumber(true):m.betAmount}if(m.winAmount!=null&&m.hasOwnProperty("winAmount")){if(typeof m.winAmount==="number")d.winAmount=o.longs===String?String(m.winAmount):m.winAmount;else d.winAmount=o.longs===String?$util.Long.prototype.toString.call(m.winAmount):o.longs===Number?new $util.LongBits(m.winAmount.low>>>0,m.winAmount.high>>>0).toNumber():m.winAmount}if(m.isAuto!=null&&m.hasOwnProperty("isAuto")){d.isAuto=m.isAuto}if(m.betGameCoin!=null&&m.hasOwnProperty("betGameCoin")){if(typeof m.betGameCoin==="number")d.betGameCoin=o.longs===String?String(m.betGameCoin):m.betGameCoin;else d.betGameCoin=o.longs===String?$util.Long.prototype.toString.call(m.betGameCoin):o.longs===Number?new $util.LongBits(m.betGameCoin.low>>>0,m.betGameCoin.high>>>0).toNumber():m.betGameCoin}return d};ZoneSettleDetail.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ZoneSettleDetail}();humanboy_proto.ConnClosed=function(){function ConnClosed(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}ConnClosed.prototype.Reason=0;ConnClosed.create=function create(properties){return new ConnClosed(properties)};ConnClosed.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.Reason!=null&&Object.hasOwnProperty.call(m,"Reason"))w.uint32(8).int32(m.Reason);return w};ConnClosed.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};ConnClosed.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.ConnClosed;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.Reason=r.int32();break;default:r.skipType(t&7);break}}return m};ConnClosed.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};ConnClosed.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.Reason!=null&&m.hasOwnProperty("Reason")){if(!$util.isInteger(m.Reason))return"Reason: integer expected"}return null};ConnClosed.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.ConnClosed)return d;var m=new $root.humanboy_proto.ConnClosed;if(d.Reason!=null){m.Reason=d.Reason|0}return m};ConnClosed.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.Reason=0}if(m.Reason!=null&&m.hasOwnProperty("Reason")){d.Reason=m.Reason}return d};ConnClosed.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return ConnClosed}();humanboy_proto.LeaveRoomReq=function(){function LeaveRoomReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LeaveRoomReq.create=function create(properties){return new LeaveRoomReq(properties)};LeaveRoomReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};LeaveRoomReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LeaveRoomReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.LeaveRoomReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};LeaveRoomReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LeaveRoomReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};LeaveRoomReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.LeaveRoomReq)return d;return new $root.humanboy_proto.LeaveRoomReq};LeaveRoomReq.toObject=function toObject(){return{}};LeaveRoomReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LeaveRoomReq}();humanboy_proto.LeaveRoomResp=function(){function LeaveRoomResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LeaveRoomResp.prototype.code=0;LeaveRoomResp.create=function create(properties){return new LeaveRoomResp(properties)};LeaveRoomResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};LeaveRoomResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LeaveRoomResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.LeaveRoomResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};LeaveRoomResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LeaveRoomResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}return null};LeaveRoomResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.LeaveRoomResp)return d;var m=new $root.humanboy_proto.LeaveRoomResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};LeaveRoomResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}return d};LeaveRoomResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LeaveRoomResp}();humanboy_proto.StartBetNotify=function(){function StartBetNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}StartBetNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;StartBetNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;StartBetNotify.create=function create(properties){return new StartBetNotify(properties)};StartBetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(8).int64(m.nextRoundEndStamp);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(16).int64(m.leftSeconds);return w};StartBetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};StartBetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.StartBetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.nextRoundEndStamp=r.int64();break;case 2:m.leftSeconds=r.int64();break;default:r.skipType(t&7);break}}return m};StartBetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};StartBetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}return null};StartBetNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.StartBetNotify)return d;var m=new $root.humanboy_proto.StartBetNotify;if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}return m};StartBetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}return d};StartBetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return StartBetNotify}();humanboy_proto.AutoBetReq=function(){function AutoBetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AutoBetReq.create=function create(properties){return new AutoBetReq(properties)};AutoBetReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};AutoBetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AutoBetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AutoBetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};AutoBetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AutoBetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};AutoBetReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AutoBetReq)return d;return new $root.humanboy_proto.AutoBetReq};AutoBetReq.toObject=function toObject(){return{}};AutoBetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AutoBetReq}();humanboy_proto.AutoBetResp=function(){function AutoBetResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AutoBetResp.prototype.code=0;AutoBetResp.prototype.canAuto=false;AutoBetResp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;AutoBetResp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;AutoBetResp.prototype.bill=null;AutoBetResp.create=function create(properties){return new AutoBetResp(properties)};AutoBetResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.canAuto!=null&&Object.hasOwnProperty.call(m,"canAuto"))w.uint32(16).bool(m.canAuto);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);if(m.bill!=null&&Object.hasOwnProperty.call(m,"bill"))$root.humanboy_proto.BillInfo.encode(m.bill,w.uint32(42).fork()).ldelim();return w};AutoBetResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AutoBetResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AutoBetResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.canAuto=r.bool();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;case 5:m.bill=$root.humanboy_proto.BillInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};AutoBetResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AutoBetResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){if(typeof m.canAuto!=="boolean")return"canAuto: boolean expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}if(m.bill!=null&&m.hasOwnProperty("bill")){{var e=$root.humanboy_proto.BillInfo.verify(m.bill);if(e)return"bill."+e}}return null};AutoBetResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AutoBetResp)return d;var m=new $root.humanboy_proto.AutoBetResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.canAuto!=null){m.canAuto=Boolean(d.canAuto)}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}if(d.bill!=null){if(typeof d.bill!=="object")throw TypeError(".humanboy_proto.AutoBetResp.bill: object expected");m.bill=$root.humanboy_proto.BillInfo.fromObject(d.bill)}return m};AutoBetResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.canAuto=false;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0;d.bill=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.canAuto!=null&&m.hasOwnProperty("canAuto")){d.canAuto=m.canAuto}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}if(m.bill!=null&&m.hasOwnProperty("bill")){d.bill=$root.humanboy_proto.BillInfo.toObject(m.bill,o)}return d};AutoBetResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AutoBetResp}();humanboy_proto.AutoBetNotify=function(){function AutoBetNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AutoBetNotify.prototype.open=false;AutoBetNotify.create=function create(properties){return new AutoBetNotify(properties)};AutoBetNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.open!=null&&Object.hasOwnProperty.call(m,"open"))w.uint32(16).bool(m.open);return w};AutoBetNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AutoBetNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AutoBetNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 2:m.open=r.bool();break;default:r.skipType(t&7);break}}return m};AutoBetNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AutoBetNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.open!=null&&m.hasOwnProperty("open")){if(typeof m.open!=="boolean")return"open: boolean expected"}return null};AutoBetNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AutoBetNotify)return d;var m=new $root.humanboy_proto.AutoBetNotify;if(d.open!=null){m.open=Boolean(d.open)}return m};AutoBetNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.open=false}if(m.open!=null&&m.hasOwnProperty("open")){d.open=m.open}return d};AutoBetNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AutoBetNotify}();humanboy_proto.PlayerListReq=function(){function PlayerListReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerListReq.create=function create(properties){return new PlayerListReq(properties)};PlayerListReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};PlayerListReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerListReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.PlayerListReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};PlayerListReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerListReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};PlayerListReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.PlayerListReq)return d;return new $root.humanboy_proto.PlayerListReq};PlayerListReq.toObject=function toObject(){return{}};PlayerListReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerListReq}();humanboy_proto.PlayerListResp=function(){function PlayerListResp(p){this.gamePlayers=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}PlayerListResp.prototype.code=0;PlayerListResp.prototype.gamePlayers=$util.emptyArray;PlayerListResp.prototype.self=null;PlayerListResp.prototype.playerNum=0;PlayerListResp.create=function create(properties){return new PlayerListResp(properties)};PlayerListResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.gamePlayers!=null&&m.gamePlayers.length){for(var i=0;i<m.gamePlayers.length;++i)$root.humanboy_proto.GamePlayer.encode(m.gamePlayers[i],w.uint32(18).fork()).ldelim()}if(m.self!=null&&Object.hasOwnProperty.call(m,"self"))$root.humanboy_proto.GamePlayer.encode(m.self,w.uint32(26).fork()).ldelim();if(m.playerNum!=null&&Object.hasOwnProperty.call(m,"playerNum"))w.uint32(40).uint32(m.playerNum);return w};PlayerListResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};PlayerListResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.PlayerListResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:if(!(m.gamePlayers&&m.gamePlayers.length))m.gamePlayers=[];m.gamePlayers.push($root.humanboy_proto.GamePlayer.decode(r,r.uint32()));break;case 3:m.self=$root.humanboy_proto.GamePlayer.decode(r,r.uint32());break;case 5:m.playerNum=r.uint32();break;default:r.skipType(t&7);break}}return m};PlayerListResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};PlayerListResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.gamePlayers!=null&&m.hasOwnProperty("gamePlayers")){if(!Array.isArray(m.gamePlayers))return"gamePlayers: array expected";for(var i=0;i<m.gamePlayers.length;++i){{var e=$root.humanboy_proto.GamePlayer.verify(m.gamePlayers[i]);if(e)return"gamePlayers."+e}}}if(m.self!=null&&m.hasOwnProperty("self")){{var e=$root.humanboy_proto.GamePlayer.verify(m.self);if(e)return"self."+e}}if(m.playerNum!=null&&m.hasOwnProperty("playerNum")){if(!$util.isInteger(m.playerNum))return"playerNum: integer expected"}return null};PlayerListResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.PlayerListResp)return d;var m=new $root.humanboy_proto.PlayerListResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.gamePlayers){if(!Array.isArray(d.gamePlayers))throw TypeError(".humanboy_proto.PlayerListResp.gamePlayers: array expected");m.gamePlayers=[];for(var i=0;i<d.gamePlayers.length;++i){if(typeof d.gamePlayers[i]!=="object")throw TypeError(".humanboy_proto.PlayerListResp.gamePlayers: object expected");m.gamePlayers[i]=$root.humanboy_proto.GamePlayer.fromObject(d.gamePlayers[i])}}if(d.self!=null){if(typeof d.self!=="object")throw TypeError(".humanboy_proto.PlayerListResp.self: object expected");m.self=$root.humanboy_proto.GamePlayer.fromObject(d.self)}if(d.playerNum!=null){m.playerNum=d.playerNum>>>0}return m};PlayerListResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.gamePlayers=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.self=null;d.playerNum=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.gamePlayers&&m.gamePlayers.length){d.gamePlayers=[];for(var j=0;j<m.gamePlayers.length;++j){d.gamePlayers[j]=$root.humanboy_proto.GamePlayer.toObject(m.gamePlayers[j],o)}}if(m.self!=null&&m.hasOwnProperty("self")){d.self=$root.humanboy_proto.GamePlayer.toObject(m.self,o)}if(m.playerNum!=null&&m.hasOwnProperty("playerNum")){d.playerNum=m.playerNum}return d};PlayerListResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return PlayerListResp}();humanboy_proto.GamePlayer=function(){function GamePlayer(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GamePlayer.prototype.uid=0;GamePlayer.prototype.name="";GamePlayer.prototype.head="";GamePlayer.prototype.totalBetAmount=$util.Long?$util.Long.fromBits(0,0,true):0;GamePlayer.prototype.winCount=0;GamePlayer.prototype.rank=0;GamePlayer.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,true):0;GamePlayer.prototype.keepWinCount=0;GamePlayer.prototype.curUsdt=$util.Long?$util.Long.fromBits(0,0,true):0;GamePlayer.prototype.plat=0;GamePlayer.create=function create(properties){return new GamePlayer(properties)};GamePlayer.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.name!=null&&Object.hasOwnProperty.call(m,"name"))w.uint32(18).string(m.name);if(m.head!=null&&Object.hasOwnProperty.call(m,"head"))w.uint32(26).string(m.head);if(m.totalBetAmount!=null&&Object.hasOwnProperty.call(m,"totalBetAmount"))w.uint32(32).uint64(m.totalBetAmount);if(m.winCount!=null&&Object.hasOwnProperty.call(m,"winCount"))w.uint32(40).uint32(m.winCount);if(m.rank!=null&&Object.hasOwnProperty.call(m,"rank"))w.uint32(48).uint32(m.rank);if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(56).uint64(m.curCoin);if(m.keepWinCount!=null&&Object.hasOwnProperty.call(m,"keepWinCount"))w.uint32(64).int32(m.keepWinCount);if(m.curUsdt!=null&&Object.hasOwnProperty.call(m,"curUsdt"))w.uint32(72).uint64(m.curUsdt);if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(80).uint32(m.plat);return w};GamePlayer.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GamePlayer.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GamePlayer;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.name=r.string();break;case 3:m.head=r.string();break;case 4:m.totalBetAmount=r.uint64();break;case 5:m.winCount=r.uint32();break;case 6:m.rank=r.uint32();break;case 7:m.curCoin=r.uint64();break;case 8:m.keepWinCount=r.int32();break;case 9:m.curUsdt=r.uint64();break;case 10:m.plat=r.uint32();break;default:r.skipType(t&7);break}}return m};GamePlayer.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GamePlayer.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.name!=null&&m.hasOwnProperty("name")){if(!$util.isString(m.name))return"name: string expected"}if(m.head!=null&&m.hasOwnProperty("head")){if(!$util.isString(m.head))return"head: string expected"}if(m.totalBetAmount!=null&&m.hasOwnProperty("totalBetAmount")){if(!$util.isInteger(m.totalBetAmount)&&!(m.totalBetAmount&&$util.isInteger(m.totalBetAmount.low)&&$util.isInteger(m.totalBetAmount.high)))return"totalBetAmount: integer|Long expected"}if(m.winCount!=null&&m.hasOwnProperty("winCount")){if(!$util.isInteger(m.winCount))return"winCount: integer expected"}if(m.rank!=null&&m.hasOwnProperty("rank")){if(!$util.isInteger(m.rank))return"rank: integer expected"}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}if(m.keepWinCount!=null&&m.hasOwnProperty("keepWinCount")){if(!$util.isInteger(m.keepWinCount))return"keepWinCount: integer expected"}if(m.curUsdt!=null&&m.hasOwnProperty("curUsdt")){if(!$util.isInteger(m.curUsdt)&&!(m.curUsdt&&$util.isInteger(m.curUsdt.low)&&$util.isInteger(m.curUsdt.high)))return"curUsdt: integer|Long expected"}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}return null};GamePlayer.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GamePlayer)return d;var m=new $root.humanboy_proto.GamePlayer;if(d.uid!=null){m.uid=d.uid>>>0}if(d.name!=null){m.name=String(d.name)}if(d.head!=null){m.head=String(d.head)}if(d.totalBetAmount!=null){if($util.Long)(m.totalBetAmount=$util.Long.fromValue(d.totalBetAmount)).unsigned=true;else if(typeof d.totalBetAmount==="string")m.totalBetAmount=parseInt(d.totalBetAmount,10);else if(typeof d.totalBetAmount==="number")m.totalBetAmount=d.totalBetAmount;else if(typeof d.totalBetAmount==="object")m.totalBetAmount=new $util.LongBits(d.totalBetAmount.low>>>0,d.totalBetAmount.high>>>0).toNumber(true)}if(d.winCount!=null){m.winCount=d.winCount>>>0}if(d.rank!=null){m.rank=d.rank>>>0}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=true;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber(true)}if(d.keepWinCount!=null){m.keepWinCount=d.keepWinCount|0}if(d.curUsdt!=null){if($util.Long)(m.curUsdt=$util.Long.fromValue(d.curUsdt)).unsigned=true;else if(typeof d.curUsdt==="string")m.curUsdt=parseInt(d.curUsdt,10);else if(typeof d.curUsdt==="number")m.curUsdt=d.curUsdt;else if(typeof d.curUsdt==="object")m.curUsdt=new $util.LongBits(d.curUsdt.low>>>0,d.curUsdt.high>>>0).toNumber(true)}if(d.plat!=null){m.plat=d.plat>>>0}return m};GamePlayer.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;d.name="";d.head="";if($util.Long){var n=new $util.Long(0,0,true);d.totalBetAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.totalBetAmount=o.longs===String?"0":0;d.winCount=0;d.rank=0;if($util.Long){var n=new $util.Long(0,0,true);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0;d.keepWinCount=0;if($util.Long){var n=new $util.Long(0,0,true);d.curUsdt=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curUsdt=o.longs===String?"0":0;d.plat=0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.name!=null&&m.hasOwnProperty("name")){d.name=m.name}if(m.head!=null&&m.hasOwnProperty("head")){d.head=m.head}if(m.totalBetAmount!=null&&m.hasOwnProperty("totalBetAmount")){if(typeof m.totalBetAmount==="number")d.totalBetAmount=o.longs===String?String(m.totalBetAmount):m.totalBetAmount;else d.totalBetAmount=o.longs===String?$util.Long.prototype.toString.call(m.totalBetAmount):o.longs===Number?new $util.LongBits(m.totalBetAmount.low>>>0,m.totalBetAmount.high>>>0).toNumber(true):m.totalBetAmount}if(m.winCount!=null&&m.hasOwnProperty("winCount")){d.winCount=m.winCount}if(m.rank!=null&&m.hasOwnProperty("rank")){d.rank=m.rank}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber(true):m.curCoin}if(m.keepWinCount!=null&&m.hasOwnProperty("keepWinCount")){d.keepWinCount=m.keepWinCount}if(m.curUsdt!=null&&m.hasOwnProperty("curUsdt")){if(typeof m.curUsdt==="number")d.curUsdt=o.longs===String?String(m.curUsdt):m.curUsdt;else d.curUsdt=o.longs===String?$util.Long.prototype.toString.call(m.curUsdt):o.longs===Number?new $util.LongBits(m.curUsdt.low>>>0,m.curUsdt.high>>>0).toNumber(true):m.curUsdt}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}return d};GamePlayer.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GamePlayer}();humanboy_proto.KickNotify=function(){function KickNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}KickNotify.prototype.kickType=0;KickNotify.prototype.desc="";KickNotify.prototype.idle_roomid=0;KickNotify.create=function create(properties){return new KickNotify(properties)};KickNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.kickType!=null&&Object.hasOwnProperty.call(m,"kickType"))w.uint32(8).int32(m.kickType);if(m.desc!=null&&Object.hasOwnProperty.call(m,"desc"))w.uint32(18).string(m.desc);if(m.idle_roomid!=null&&Object.hasOwnProperty.call(m,"idle_roomid"))w.uint32(24).uint32(m.idle_roomid);return w};KickNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};KickNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.KickNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.kickType=r.int32();break;case 2:m.desc=r.string();break;case 3:m.idle_roomid=r.uint32();break;default:r.skipType(t&7);break}}return m};KickNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};KickNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.kickType!=null&&m.hasOwnProperty("kickType")){switch(m.kickType){default:return"kickType: enum value expected";case 0:case 1:case 2:break}}if(m.desc!=null&&m.hasOwnProperty("desc")){if(!$util.isString(m.desc))return"desc: string expected"}if(m.idle_roomid!=null&&m.hasOwnProperty("idle_roomid")){if(!$util.isInteger(m.idle_roomid))return"idle_roomid: integer expected"}return null};KickNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.KickNotify)return d;var m=new $root.humanboy_proto.KickNotify;switch(d.kickType){case"Kick_DUMMY":case 0:m.kickType=0;break;case"IDLE_LONG_TIME":case 1:m.kickType=1;break;case"Stop_World":case 2:m.kickType=2;break}if(d.desc!=null){m.desc=String(d.desc)}if(d.idle_roomid!=null){m.idle_roomid=d.idle_roomid>>>0}return m};KickNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.kickType=o.enums===String?"Kick_DUMMY":0;d.desc="";d.idle_roomid=0}if(m.kickType!=null&&m.hasOwnProperty("kickType")){d.kickType=o.enums===String?$root.humanboy_proto.Kick[m.kickType]:m.kickType}if(m.desc!=null&&m.hasOwnProperty("desc")){d.desc=m.desc}if(m.idle_roomid!=null&&m.hasOwnProperty("idle_roomid")){d.idle_roomid=m.idle_roomid}return d};KickNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return KickNotify}();humanboy_proto.TrendReq=function(){function TrendReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TrendReq.create=function create(properties){return new TrendReq(properties)};TrendReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};TrendReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TrendReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.TrendReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};TrendReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TrendReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};TrendReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.TrendReq)return d;return new $root.humanboy_proto.TrendReq};TrendReq.toObject=function toObject(){return{}};TrendReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TrendReq}();humanboy_proto.TrendResp=function(){function TrendResp(p){this.trendOption=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TrendResp.prototype.trendOption=$util.emptyArray;TrendResp.prototype.handLevelStatistics=null;TrendResp.prototype.handNum=0;TrendResp.prototype.code=0;TrendResp.create=function create(properties){return new TrendResp(properties)};TrendResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.trendOption!=null&&m.trendOption.length){for(var i=0;i<m.trendOption.length;++i)$root.humanboy_proto.RoomTrendOption.encode(m.trendOption[i],w.uint32(10).fork()).ldelim()}if(m.handLevelStatistics!=null&&Object.hasOwnProperty.call(m,"handLevelStatistics"))$root.humanboy_proto.RoomTrendLevelStatistics.encode(m.handLevelStatistics,w.uint32(18).fork()).ldelim();if(m.handNum!=null&&Object.hasOwnProperty.call(m,"handNum"))w.uint32(24).int32(m.handNum);if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(32).int32(m.code);return w};TrendResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TrendResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.TrendResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.trendOption&&m.trendOption.length))m.trendOption=[];m.trendOption.push($root.humanboy_proto.RoomTrendOption.decode(r,r.uint32()));break;case 2:m.handLevelStatistics=$root.humanboy_proto.RoomTrendLevelStatistics.decode(r,r.uint32());break;case 3:m.handNum=r.int32();break;case 4:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};TrendResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TrendResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.trendOption!=null&&m.hasOwnProperty("trendOption")){if(!Array.isArray(m.trendOption))return"trendOption: array expected";for(var i=0;i<m.trendOption.length;++i){{var e=$root.humanboy_proto.RoomTrendOption.verify(m.trendOption[i]);if(e)return"trendOption."+e}}}if(m.handLevelStatistics!=null&&m.hasOwnProperty("handLevelStatistics")){{var e=$root.humanboy_proto.RoomTrendLevelStatistics.verify(m.handLevelStatistics);if(e)return"handLevelStatistics."+e}}if(m.handNum!=null&&m.hasOwnProperty("handNum")){if(!$util.isInteger(m.handNum))return"handNum: integer expected"}if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}return null};TrendResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.TrendResp)return d;var m=new $root.humanboy_proto.TrendResp;if(d.trendOption){if(!Array.isArray(d.trendOption))throw TypeError(".humanboy_proto.TrendResp.trendOption: array expected");m.trendOption=[];for(var i=0;i<d.trendOption.length;++i){if(typeof d.trendOption[i]!=="object")throw TypeError(".humanboy_proto.TrendResp.trendOption: object expected");m.trendOption[i]=$root.humanboy_proto.RoomTrendOption.fromObject(d.trendOption[i])}}if(d.handLevelStatistics!=null){if(typeof d.handLevelStatistics!=="object")throw TypeError(".humanboy_proto.TrendResp.handLevelStatistics: object expected");m.handLevelStatistics=$root.humanboy_proto.RoomTrendLevelStatistics.fromObject(d.handLevelStatistics)}if(d.handNum!=null){m.handNum=d.handNum|0}switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};TrendResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.trendOption=[]}if(o.defaults){d.handLevelStatistics=null;d.handNum=0;d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.trendOption&&m.trendOption.length){d.trendOption=[];for(var j=0;j<m.trendOption.length;++j){d.trendOption[j]=$root.humanboy_proto.RoomTrendOption.toObject(m.trendOption[j],o)}}if(m.handLevelStatistics!=null&&m.hasOwnProperty("handLevelStatistics")){d.handLevelStatistics=$root.humanboy_proto.RoomTrendLevelStatistics.toObject(m.handLevelStatistics,o)}if(m.handNum!=null&&m.hasOwnProperty("handNum")){d.handNum=m.handNum}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}return d};TrendResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TrendResp}();humanboy_proto.RoomTrendNotice=function(){function RoomTrendNotice(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendNotice.create=function create(properties){return new RoomTrendNotice(properties)};RoomTrendNotice.encode=function encode(m,w){if(!w)w=$Writer.create();return w};RoomTrendNotice.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendNotice.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.RoomTrendNotice;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};RoomTrendNotice.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendNotice.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};RoomTrendNotice.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.RoomTrendNotice)return d;return new $root.humanboy_proto.RoomTrendNotice};RoomTrendNotice.toObject=function toObject(){return{}};RoomTrendNotice.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendNotice}();humanboy_proto.TrendData=function(){function TrendData(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TrendData.prototype.result=0;TrendData.prototype.handLevel=0;TrendData.create=function create(properties){return new TrendData(properties)};TrendData.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.result!=null&&Object.hasOwnProperty.call(m,"result"))w.uint32(8).int32(m.result);if(m.handLevel!=null&&Object.hasOwnProperty.call(m,"handLevel"))w.uint32(16).int32(m.handLevel);return w};TrendData.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TrendData.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.TrendData;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.result=r.int32();break;case 2:m.handLevel=r.int32();break;default:r.skipType(t&7);break}}return m};TrendData.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TrendData.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.result!=null&&m.hasOwnProperty("result")){if(!$util.isInteger(m.result))return"result: integer expected"}if(m.handLevel!=null&&m.hasOwnProperty("handLevel")){switch(m.handLevel){default:return"handLevel: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:break}}return null};TrendData.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.TrendData)return d;var m=new $root.humanboy_proto.TrendData;if(d.result!=null){m.result=d.result|0}switch(d.handLevel){case"CardResult_Dummy":case 0:m.handLevel=0;break;case"GAO_PAI":case 1:m.handLevel=1;break;case"YI_DUI":case 2:m.handLevel=2;break;case"LIAN_DUI":case 3:m.handLevel=3;break;case"SAN_TIAO":case 4:m.handLevel=4;break;case"SHUN_ZI":case 5:m.handLevel=5;break;case"TONG_HUA":case 6:m.handLevel=6;break;case"HU_LU":case 7:m.handLevel=7;break;case"SI_TIAO":case 8:m.handLevel=8;break;case"TONG_HUA_SHUN":case 9:m.handLevel=9;break;case"HUANG_TONG":case 10:m.handLevel=10;break}return m};TrendData.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.result=0;d.handLevel=o.enums===String?"CardResult_Dummy":0}if(m.result!=null&&m.hasOwnProperty("result")){d.result=m.result}if(m.handLevel!=null&&m.hasOwnProperty("handLevel")){d.handLevel=o.enums===String?$root.humanboy_proto.CardResult[m.handLevel]:m.handLevel}return d};TrendData.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TrendData}();humanboy_proto.RoomTrendOption=function(){function RoomTrendOption(p){this.road=[];this.lastResult=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendOption.prototype.option=0;RoomTrendOption.prototype.stats=null;RoomTrendOption.prototype.road=$util.emptyArray;RoomTrendOption.prototype.lastResult=$util.emptyArray;RoomTrendOption.prototype.lastRow=0;RoomTrendOption.prototype.lastCol=0;RoomTrendOption.create=function create(properties){return new RoomTrendOption(properties)};RoomTrendOption.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.stats!=null&&Object.hasOwnProperty.call(m,"stats"))$root.humanboy_proto.RoomTrendOptionStats.encode(m.stats,w.uint32(26).fork()).ldelim();if(m.road!=null&&m.road.length){for(var i=0;i<m.road.length;++i)$root.humanboy_proto.TrendRoad.encode(m.road[i],w.uint32(34).fork()).ldelim()}if(m.lastResult!=null&&m.lastResult.length){w.uint32(42).fork();for(var i=0;i<m.lastResult.length;++i)w.int32(m.lastResult[i]);w.ldelim()}if(m.lastRow!=null&&Object.hasOwnProperty.call(m,"lastRow"))w.uint32(56).int32(m.lastRow);if(m.lastCol!=null&&Object.hasOwnProperty.call(m,"lastCol"))w.uint32(64).int32(m.lastCol);return w};RoomTrendOption.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendOption.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.RoomTrendOption;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 3:m.stats=$root.humanboy_proto.RoomTrendOptionStats.decode(r,r.uint32());break;case 4:if(!(m.road&&m.road.length))m.road=[];m.road.push($root.humanboy_proto.TrendRoad.decode(r,r.uint32()));break;case 5:if(!(m.lastResult&&m.lastResult.length))m.lastResult=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.lastResult.push(r.int32())}else m.lastResult.push(r.int32());break;case 7:m.lastRow=r.int32();break;case 8:m.lastCol=r.int32();break;default:r.skipType(t&7);break}}return m};RoomTrendOption.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendOption.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.stats!=null&&m.hasOwnProperty("stats")){{var e=$root.humanboy_proto.RoomTrendOptionStats.verify(m.stats);if(e)return"stats."+e}}if(m.road!=null&&m.hasOwnProperty("road")){if(!Array.isArray(m.road))return"road: array expected";for(var i=0;i<m.road.length;++i){{var e=$root.humanboy_proto.TrendRoad.verify(m.road[i]);if(e)return"road."+e}}}if(m.lastResult!=null&&m.hasOwnProperty("lastResult")){if(!Array.isArray(m.lastResult))return"lastResult: array expected";for(var i=0;i<m.lastResult.length;++i){if(!$util.isInteger(m.lastResult[i]))return"lastResult: integer[] expected"}}if(m.lastRow!=null&&m.hasOwnProperty("lastRow")){if(!$util.isInteger(m.lastRow))return"lastRow: integer expected"}if(m.lastCol!=null&&m.hasOwnProperty("lastCol")){if(!$util.isInteger(m.lastCol))return"lastCol: integer expected"}return null};RoomTrendOption.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.RoomTrendOption)return d;var m=new $root.humanboy_proto.RoomTrendOption;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.stats!=null){if(typeof d.stats!=="object")throw TypeError(".humanboy_proto.RoomTrendOption.stats: object expected");m.stats=$root.humanboy_proto.RoomTrendOptionStats.fromObject(d.stats)}if(d.road){if(!Array.isArray(d.road))throw TypeError(".humanboy_proto.RoomTrendOption.road: array expected");m.road=[];for(var i=0;i<d.road.length;++i){if(typeof d.road[i]!=="object")throw TypeError(".humanboy_proto.RoomTrendOption.road: object expected");m.road[i]=$root.humanboy_proto.TrendRoad.fromObject(d.road[i])}}if(d.lastResult){if(!Array.isArray(d.lastResult))throw TypeError(".humanboy_proto.RoomTrendOption.lastResult: array expected");m.lastResult=[];for(var i=0;i<d.lastResult.length;++i){m.lastResult[i]=d.lastResult[i]|0}}if(d.lastRow!=null){m.lastRow=d.lastRow|0}if(d.lastCol!=null){m.lastCol=d.lastCol|0}return m};RoomTrendOption.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.road=[];d.lastResult=[]}if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0;d.stats=null;d.lastRow=0;d.lastCol=0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.stats!=null&&m.hasOwnProperty("stats")){d.stats=$root.humanboy_proto.RoomTrendOptionStats.toObject(m.stats,o)}if(m.road&&m.road.length){d.road=[];for(var j=0;j<m.road.length;++j){d.road[j]=$root.humanboy_proto.TrendRoad.toObject(m.road[j],o)}}if(m.lastResult&&m.lastResult.length){d.lastResult=[];for(var j=0;j<m.lastResult.length;++j){d.lastResult[j]=m.lastResult[j]}}if(m.lastRow!=null&&m.hasOwnProperty("lastRow")){d.lastRow=m.lastRow}if(m.lastCol!=null&&m.hasOwnProperty("lastCol")){d.lastCol=m.lastCol}return d};RoomTrendOption.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendOption}();humanboy_proto.RoomTrendOptionStats=function(){function RoomTrendOptionStats(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendOptionStats.prototype.capHandNum=0;RoomTrendOptionStats.prototype.lenHandNum=0;RoomTrendOptionStats.prototype.win=0;RoomTrendOptionStats.prototype.lose=0;RoomTrendOptionStats.prototype.equal=0;RoomTrendOptionStats.create=function create(properties){return new RoomTrendOptionStats(properties)};RoomTrendOptionStats.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.capHandNum!=null&&Object.hasOwnProperty.call(m,"capHandNum"))w.uint32(8).int32(m.capHandNum);if(m.lenHandNum!=null&&Object.hasOwnProperty.call(m,"lenHandNum"))w.uint32(16).int32(m.lenHandNum);if(m.win!=null&&Object.hasOwnProperty.call(m,"win"))w.uint32(24).int32(m.win);if(m.lose!=null&&Object.hasOwnProperty.call(m,"lose"))w.uint32(32).int32(m.lose);if(m.equal!=null&&Object.hasOwnProperty.call(m,"equal"))w.uint32(40).int32(m.equal);return w};RoomTrendOptionStats.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendOptionStats.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.RoomTrendOptionStats;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.capHandNum=r.int32();break;case 2:m.lenHandNum=r.int32();break;case 3:m.win=r.int32();break;case 4:m.lose=r.int32();break;case 5:m.equal=r.int32();break;default:r.skipType(t&7);break}}return m};RoomTrendOptionStats.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendOptionStats.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.capHandNum!=null&&m.hasOwnProperty("capHandNum")){if(!$util.isInteger(m.capHandNum))return"capHandNum: integer expected"}if(m.lenHandNum!=null&&m.hasOwnProperty("lenHandNum")){if(!$util.isInteger(m.lenHandNum))return"lenHandNum: integer expected"}if(m.win!=null&&m.hasOwnProperty("win")){if(!$util.isInteger(m.win))return"win: integer expected"}if(m.lose!=null&&m.hasOwnProperty("lose")){if(!$util.isInteger(m.lose))return"lose: integer expected"}if(m.equal!=null&&m.hasOwnProperty("equal")){if(!$util.isInteger(m.equal))return"equal: integer expected"}return null};RoomTrendOptionStats.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.RoomTrendOptionStats)return d;var m=new $root.humanboy_proto.RoomTrendOptionStats;if(d.capHandNum!=null){m.capHandNum=d.capHandNum|0}if(d.lenHandNum!=null){m.lenHandNum=d.lenHandNum|0}if(d.win!=null){m.win=d.win|0}if(d.lose!=null){m.lose=d.lose|0}if(d.equal!=null){m.equal=d.equal|0}return m};RoomTrendOptionStats.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.capHandNum=0;d.lenHandNum=0;d.win=0;d.lose=0;d.equal=0}if(m.capHandNum!=null&&m.hasOwnProperty("capHandNum")){d.capHandNum=m.capHandNum}if(m.lenHandNum!=null&&m.hasOwnProperty("lenHandNum")){d.lenHandNum=m.lenHandNum}if(m.win!=null&&m.hasOwnProperty("win")){d.win=m.win}if(m.lose!=null&&m.hasOwnProperty("lose")){d.lose=m.lose}if(m.equal!=null&&m.hasOwnProperty("equal")){d.equal=m.equal}return d};RoomTrendOptionStats.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendOptionStats}();humanboy_proto.RoomTrendLevelStatistics=function(){function RoomTrendLevelStatistics(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendLevelStatistics.prototype.stats=null;RoomTrendLevelStatistics.create=function create(properties){return new RoomTrendLevelStatistics(properties)};RoomTrendLevelStatistics.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.stats!=null&&Object.hasOwnProperty.call(m,"stats"))$root.humanboy_proto.RoomTrendLevelStatisticsStats.encode(m.stats,w.uint32(10).fork()).ldelim();return w};RoomTrendLevelStatistics.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendLevelStatistics.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.RoomTrendLevelStatistics;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.stats=$root.humanboy_proto.RoomTrendLevelStatisticsStats.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};RoomTrendLevelStatistics.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendLevelStatistics.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.stats!=null&&m.hasOwnProperty("stats")){{var e=$root.humanboy_proto.RoomTrendLevelStatisticsStats.verify(m.stats);if(e)return"stats."+e}}return null};RoomTrendLevelStatistics.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.RoomTrendLevelStatistics)return d;var m=new $root.humanboy_proto.RoomTrendLevelStatistics;if(d.stats!=null){if(typeof d.stats!=="object")throw TypeError(".humanboy_proto.RoomTrendLevelStatistics.stats: object expected");m.stats=$root.humanboy_proto.RoomTrendLevelStatisticsStats.fromObject(d.stats)}return m};RoomTrendLevelStatistics.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.stats=null}if(m.stats!=null&&m.hasOwnProperty("stats")){d.stats=$root.humanboy_proto.RoomTrendLevelStatisticsStats.toObject(m.stats,o)}return d};RoomTrendLevelStatistics.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendLevelStatistics}();humanboy_proto.RoomTrendLevelStatisticsStats=function(){function RoomTrendLevelStatisticsStats(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}RoomTrendLevelStatisticsStats.prototype.capHandNum=0;RoomTrendLevelStatisticsStats.prototype.lenHandNum=0;RoomTrendLevelStatisticsStats.prototype.gaoPai=0;RoomTrendLevelStatisticsStats.prototype.yuDui=0;RoomTrendLevelStatisticsStats.prototype.lianDui=0;RoomTrendLevelStatisticsStats.prototype.sanTiao=0;RoomTrendLevelStatisticsStats.prototype.shunZiAnd1=0;RoomTrendLevelStatisticsStats.prototype.huLuAnd3=0;RoomTrendLevelStatisticsStats.prototype.winAll=0;RoomTrendLevelStatisticsStats.prototype.loseAll=0;RoomTrendLevelStatisticsStats.create=function create(properties){return new RoomTrendLevelStatisticsStats(properties)};RoomTrendLevelStatisticsStats.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.capHandNum!=null&&Object.hasOwnProperty.call(m,"capHandNum"))w.uint32(8).int32(m.capHandNum);if(m.lenHandNum!=null&&Object.hasOwnProperty.call(m,"lenHandNum"))w.uint32(16).int32(m.lenHandNum);if(m.gaoPai!=null&&Object.hasOwnProperty.call(m,"gaoPai"))w.uint32(24).int32(m.gaoPai);if(m.yuDui!=null&&Object.hasOwnProperty.call(m,"yuDui"))w.uint32(32).int32(m.yuDui);if(m.lianDui!=null&&Object.hasOwnProperty.call(m,"lianDui"))w.uint32(40).int32(m.lianDui);if(m.sanTiao!=null&&Object.hasOwnProperty.call(m,"sanTiao"))w.uint32(48).int32(m.sanTiao);if(m.shunZiAnd1!=null&&Object.hasOwnProperty.call(m,"shunZiAnd1"))w.uint32(56).int32(m.shunZiAnd1);if(m.huLuAnd3!=null&&Object.hasOwnProperty.call(m,"huLuAnd3"))w.uint32(64).int32(m.huLuAnd3);if(m.winAll!=null&&Object.hasOwnProperty.call(m,"winAll"))w.uint32(72).int32(m.winAll);if(m.loseAll!=null&&Object.hasOwnProperty.call(m,"loseAll"))w.uint32(80).int32(m.loseAll);return w};RoomTrendLevelStatisticsStats.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};RoomTrendLevelStatisticsStats.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.RoomTrendLevelStatisticsStats;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.capHandNum=r.int32();break;case 2:m.lenHandNum=r.int32();break;case 3:m.gaoPai=r.int32();break;case 4:m.yuDui=r.int32();break;case 5:m.lianDui=r.int32();break;case 6:m.sanTiao=r.int32();break;case 7:m.shunZiAnd1=r.int32();break;case 8:m.huLuAnd3=r.int32();break;case 9:m.winAll=r.int32();break;case 10:m.loseAll=r.int32();break;default:r.skipType(t&7);break}}return m};RoomTrendLevelStatisticsStats.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};RoomTrendLevelStatisticsStats.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.capHandNum!=null&&m.hasOwnProperty("capHandNum")){if(!$util.isInteger(m.capHandNum))return"capHandNum: integer expected"}if(m.lenHandNum!=null&&m.hasOwnProperty("lenHandNum")){if(!$util.isInteger(m.lenHandNum))return"lenHandNum: integer expected"}if(m.gaoPai!=null&&m.hasOwnProperty("gaoPai")){if(!$util.isInteger(m.gaoPai))return"gaoPai: integer expected"}if(m.yuDui!=null&&m.hasOwnProperty("yuDui")){if(!$util.isInteger(m.yuDui))return"yuDui: integer expected"}if(m.lianDui!=null&&m.hasOwnProperty("lianDui")){if(!$util.isInteger(m.lianDui))return"lianDui: integer expected"}if(m.sanTiao!=null&&m.hasOwnProperty("sanTiao")){if(!$util.isInteger(m.sanTiao))return"sanTiao: integer expected"}if(m.shunZiAnd1!=null&&m.hasOwnProperty("shunZiAnd1")){if(!$util.isInteger(m.shunZiAnd1))return"shunZiAnd1: integer expected"}if(m.huLuAnd3!=null&&m.hasOwnProperty("huLuAnd3")){if(!$util.isInteger(m.huLuAnd3))return"huLuAnd3: integer expected"}if(m.winAll!=null&&m.hasOwnProperty("winAll")){if(!$util.isInteger(m.winAll))return"winAll: integer expected"}if(m.loseAll!=null&&m.hasOwnProperty("loseAll")){if(!$util.isInteger(m.loseAll))return"loseAll: integer expected"}return null};RoomTrendLevelStatisticsStats.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.RoomTrendLevelStatisticsStats)return d;var m=new $root.humanboy_proto.RoomTrendLevelStatisticsStats;if(d.capHandNum!=null){m.capHandNum=d.capHandNum|0}if(d.lenHandNum!=null){m.lenHandNum=d.lenHandNum|0}if(d.gaoPai!=null){m.gaoPai=d.gaoPai|0}if(d.yuDui!=null){m.yuDui=d.yuDui|0}if(d.lianDui!=null){m.lianDui=d.lianDui|0}if(d.sanTiao!=null){m.sanTiao=d.sanTiao|0}if(d.shunZiAnd1!=null){m.shunZiAnd1=d.shunZiAnd1|0}if(d.huLuAnd3!=null){m.huLuAnd3=d.huLuAnd3|0}if(d.winAll!=null){m.winAll=d.winAll|0}if(d.loseAll!=null){m.loseAll=d.loseAll|0}return m};RoomTrendLevelStatisticsStats.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.capHandNum=0;d.lenHandNum=0;d.gaoPai=0;d.yuDui=0;d.lianDui=0;d.sanTiao=0;d.shunZiAnd1=0;d.huLuAnd3=0;d.winAll=0;d.loseAll=0}if(m.capHandNum!=null&&m.hasOwnProperty("capHandNum")){d.capHandNum=m.capHandNum}if(m.lenHandNum!=null&&m.hasOwnProperty("lenHandNum")){d.lenHandNum=m.lenHandNum}if(m.gaoPai!=null&&m.hasOwnProperty("gaoPai")){d.gaoPai=m.gaoPai}if(m.yuDui!=null&&m.hasOwnProperty("yuDui")){d.yuDui=m.yuDui}if(m.lianDui!=null&&m.hasOwnProperty("lianDui")){d.lianDui=m.lianDui}if(m.sanTiao!=null&&m.hasOwnProperty("sanTiao")){d.sanTiao=m.sanTiao}if(m.shunZiAnd1!=null&&m.hasOwnProperty("shunZiAnd1")){d.shunZiAnd1=m.shunZiAnd1}if(m.huLuAnd3!=null&&m.hasOwnProperty("huLuAnd3")){d.huLuAnd3=m.huLuAnd3}if(m.winAll!=null&&m.hasOwnProperty("winAll")){d.winAll=m.winAll}if(m.loseAll!=null&&m.hasOwnProperty("loseAll")){d.loseAll=m.loseAll}return d};RoomTrendLevelStatisticsStats.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return RoomTrendLevelStatisticsStats}();humanboy_proto.TrendRoad=function(){function TrendRoad(p){this.roadRow=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TrendRoad.prototype.roadRow=$util.emptyArray;TrendRoad.create=function create(properties){return new TrendRoad(properties)};TrendRoad.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roadRow!=null&&m.roadRow.length){for(var i=0;i<m.roadRow.length;++i)$root.humanboy_proto.TrendRoadInfo.encode(m.roadRow[i],w.uint32(10).fork()).ldelim()}return w};TrendRoad.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TrendRoad.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.TrendRoad;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:if(!(m.roadRow&&m.roadRow.length))m.roadRow=[];m.roadRow.push($root.humanboy_proto.TrendRoadInfo.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};TrendRoad.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TrendRoad.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roadRow!=null&&m.hasOwnProperty("roadRow")){if(!Array.isArray(m.roadRow))return"roadRow: array expected";for(var i=0;i<m.roadRow.length;++i){{var e=$root.humanboy_proto.TrendRoadInfo.verify(m.roadRow[i]);if(e)return"roadRow."+e}}}return null};TrendRoad.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.TrendRoad)return d;var m=new $root.humanboy_proto.TrendRoad;if(d.roadRow){if(!Array.isArray(d.roadRow))throw TypeError(".humanboy_proto.TrendRoad.roadRow: array expected");m.roadRow=[];for(var i=0;i<d.roadRow.length;++i){if(typeof d.roadRow[i]!=="object")throw TypeError(".humanboy_proto.TrendRoad.roadRow: object expected");m.roadRow[i]=$root.humanboy_proto.TrendRoadInfo.fromObject(d.roadRow[i])}}return m};TrendRoad.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.roadRow=[]}if(m.roadRow&&m.roadRow.length){d.roadRow=[];for(var j=0;j<m.roadRow.length;++j){d.roadRow[j]=$root.humanboy_proto.TrendRoadInfo.toObject(m.roadRow[j],o)}}return d};TrendRoad.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TrendRoad}();humanboy_proto.TrendRoadInfo=function(){function TrendRoadInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}TrendRoadInfo.prototype.result=0;TrendRoadInfo.prototype.eqc=0;TrendRoadInfo.create=function create(properties){return new TrendRoadInfo(properties)};TrendRoadInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.result!=null&&Object.hasOwnProperty.call(m,"result"))w.uint32(8).int32(m.result);if(m.eqc!=null&&Object.hasOwnProperty.call(m,"eqc"))w.uint32(16).int32(m.eqc);return w};TrendRoadInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};TrendRoadInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.TrendRoadInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.result=r.int32();break;case 2:m.eqc=r.int32();break;default:r.skipType(t&7);break}}return m};TrendRoadInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};TrendRoadInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.result!=null&&m.hasOwnProperty("result")){if(!$util.isInteger(m.result))return"result: integer expected"}if(m.eqc!=null&&m.hasOwnProperty("eqc")){if(!$util.isInteger(m.eqc))return"eqc: integer expected"}return null};TrendRoadInfo.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.TrendRoadInfo)return d;var m=new $root.humanboy_proto.TrendRoadInfo;if(d.result!=null){m.result=d.result|0}if(d.eqc!=null){m.eqc=d.eqc|0}return m};TrendRoadInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.result=0;d.eqc=0}if(m.result!=null&&m.hasOwnProperty("result")){d.result=m.result}if(m.eqc!=null&&m.hasOwnProperty("eqc")){d.eqc=m.eqc}return d};TrendRoadInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return TrendRoadInfo}();humanboy_proto.GetBuyStockNumReq=function(){function GetBuyStockNumReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GetBuyStockNumReq.create=function create(properties){return new GetBuyStockNumReq(properties)};GetBuyStockNumReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};GetBuyStockNumReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GetBuyStockNumReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GetBuyStockNumReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};GetBuyStockNumReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GetBuyStockNumReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};GetBuyStockNumReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GetBuyStockNumReq)return d;return new $root.humanboy_proto.GetBuyStockNumReq};GetBuyStockNumReq.toObject=function toObject(){return{}};GetBuyStockNumReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GetBuyStockNumReq}();humanboy_proto.GetBuyStockNumResp=function(){function GetBuyStockNumResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GetBuyStockNumResp.prototype.code=0;GetBuyStockNumResp.prototype.stockNum=0;GetBuyStockNumResp.create=function create(properties){return new GetBuyStockNumResp(properties)};GetBuyStockNumResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.stockNum!=null&&Object.hasOwnProperty.call(m,"stockNum"))w.uint32(16).uint32(m.stockNum);return w};GetBuyStockNumResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GetBuyStockNumResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GetBuyStockNumResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.stockNum=r.uint32();break;default:r.skipType(t&7);break}}return m};GetBuyStockNumResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GetBuyStockNumResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.stockNum!=null&&m.hasOwnProperty("stockNum")){if(!$util.isInteger(m.stockNum))return"stockNum: integer expected"}return null};GetBuyStockNumResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GetBuyStockNumResp)return d;var m=new $root.humanboy_proto.GetBuyStockNumResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.stockNum!=null){m.stockNum=d.stockNum>>>0}return m};GetBuyStockNumResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.stockNum=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.stockNum!=null&&m.hasOwnProperty("stockNum")){d.stockNum=m.stockNum}return d};GetBuyStockNumResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GetBuyStockNumResp}();humanboy_proto.UpDealerReq=function(){function UpDealerReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}UpDealerReq.prototype.buyStockNum=0;UpDealerReq.create=function create(properties){return new UpDealerReq(properties)};UpDealerReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.buyStockNum!=null&&Object.hasOwnProperty.call(m,"buyStockNum"))w.uint32(8).uint32(m.buyStockNum);return w};UpDealerReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};UpDealerReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.UpDealerReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.buyStockNum=r.uint32();break;default:r.skipType(t&7);break}}return m};UpDealerReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};UpDealerReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.buyStockNum!=null&&m.hasOwnProperty("buyStockNum")){if(!$util.isInteger(m.buyStockNum))return"buyStockNum: integer expected"}return null};UpDealerReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.UpDealerReq)return d;var m=new $root.humanboy_proto.UpDealerReq;if(d.buyStockNum!=null){m.buyStockNum=d.buyStockNum>>>0}return m};UpDealerReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.buyStockNum=0}if(m.buyStockNum!=null&&m.hasOwnProperty("buyStockNum")){d.buyStockNum=m.buyStockNum}return d};UpDealerReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return UpDealerReq}();humanboy_proto.UpDealerResp=function(){function UpDealerResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}UpDealerResp.prototype.code=0;UpDealerResp.prototype.buyStockNum=0;UpDealerResp.create=function create(properties){return new UpDealerResp(properties)};UpDealerResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.buyStockNum!=null&&Object.hasOwnProperty.call(m,"buyStockNum"))w.uint32(16).uint32(m.buyStockNum);return w};UpDealerResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};UpDealerResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.UpDealerResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.buyStockNum=r.uint32();break;default:r.skipType(t&7);break}}return m};UpDealerResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};UpDealerResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.buyStockNum!=null&&m.hasOwnProperty("buyStockNum")){if(!$util.isInteger(m.buyStockNum))return"buyStockNum: integer expected"}return null};UpDealerResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.UpDealerResp)return d;var m=new $root.humanboy_proto.UpDealerResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.buyStockNum!=null){m.buyStockNum=d.buyStockNum>>>0}return m};UpDealerResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.buyStockNum=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.buyStockNum!=null&&m.hasOwnProperty("buyStockNum")){d.buyStockNum=m.buyStockNum}return d};UpDealerResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return UpDealerResp}();humanboy_proto.UpDealerNotify=function(){function UpDealerNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}UpDealerNotify.prototype.uid=0;UpDealerNotify.prototype.holdStockNum=0;UpDealerNotify.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,false):0;UpDealerNotify.create=function create(properties){return new UpDealerNotify(properties)};UpDealerNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.holdStockNum!=null&&Object.hasOwnProperty.call(m,"holdStockNum"))w.uint32(16).uint32(m.holdStockNum);if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(24).int64(m.curCoin);return w};UpDealerNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};UpDealerNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.UpDealerNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.holdStockNum=r.uint32();break;case 3:m.curCoin=r.int64();break;default:r.skipType(t&7);break}}return m};UpDealerNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};UpDealerNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.holdStockNum!=null&&m.hasOwnProperty("holdStockNum")){if(!$util.isInteger(m.holdStockNum))return"holdStockNum: integer expected"}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}return null};UpDealerNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.UpDealerNotify)return d;var m=new $root.humanboy_proto.UpDealerNotify;if(d.uid!=null){m.uid=d.uid>>>0}if(d.holdStockNum!=null){m.holdStockNum=d.holdStockNum>>>0}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=false;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber()}return m};UpDealerNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;d.holdStockNum=0;if($util.Long){var n=new $util.Long(0,0,false);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.holdStockNum!=null&&m.hasOwnProperty("holdStockNum")){d.holdStockNum=m.holdStockNum}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber():m.curCoin}return d};UpDealerNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return UpDealerNotify}();humanboy_proto.DownDealerReq=function(){function DownDealerReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DownDealerReq.create=function create(properties){return new DownDealerReq(properties)};DownDealerReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};DownDealerReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DownDealerReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.DownDealerReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};DownDealerReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DownDealerReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};DownDealerReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.DownDealerReq)return d;return new $root.humanboy_proto.DownDealerReq};DownDealerReq.toObject=function toObject(){return{}};DownDealerReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DownDealerReq}();humanboy_proto.DownDealerResp=function(){function DownDealerResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DownDealerResp.prototype.code=0;DownDealerResp.prototype.doNow=0;DownDealerResp.create=function create(properties){return new DownDealerResp(properties)};DownDealerResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.doNow!=null&&Object.hasOwnProperty.call(m,"doNow"))w.uint32(16).uint32(m.doNow);return w};DownDealerResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DownDealerResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.DownDealerResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.doNow=r.uint32();break;default:r.skipType(t&7);break}}return m};DownDealerResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DownDealerResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.doNow!=null&&m.hasOwnProperty("doNow")){if(!$util.isInteger(m.doNow))return"doNow: integer expected"}return null};DownDealerResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.DownDealerResp)return d;var m=new $root.humanboy_proto.DownDealerResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.doNow!=null){m.doNow=d.doNow>>>0}return m};DownDealerResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.doNow=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.doNow!=null&&m.hasOwnProperty("doNow")){d.doNow=m.doNow}return d};DownDealerResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DownDealerResp}();humanboy_proto.CancelWaitReq=function(){function CancelWaitReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CancelWaitReq.create=function create(properties){return new CancelWaitReq(properties)};CancelWaitReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};CancelWaitReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CancelWaitReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.CancelWaitReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};CancelWaitReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CancelWaitReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};CancelWaitReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.CancelWaitReq)return d;return new $root.humanboy_proto.CancelWaitReq};CancelWaitReq.toObject=function toObject(){return{}};CancelWaitReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CancelWaitReq}();humanboy_proto.CancelWaitResp=function(){function CancelWaitResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CancelWaitResp.prototype.code=0;CancelWaitResp.create=function create(properties){return new CancelWaitResp(properties)};CancelWaitResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);return w};CancelWaitResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CancelWaitResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.CancelWaitResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;default:r.skipType(t&7);break}}return m};CancelWaitResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CancelWaitResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}return null};CancelWaitResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.CancelWaitResp)return d;var m=new $root.humanboy_proto.CancelWaitResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}return m};CancelWaitResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}return d};CancelWaitResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CancelWaitResp}();humanboy_proto.DownDealerNotify=function(){function DownDealerNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DownDealerNotify.prototype.reason=0;DownDealerNotify.prototype.uid=0;DownDealerNotify.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,false):0;DownDealerNotify.prototype.holdStockNum=0;DownDealerNotify.create=function create(properties){return new DownDealerNotify(properties)};DownDealerNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.reason!=null&&Object.hasOwnProperty.call(m,"reason"))w.uint32(8).int32(m.reason);if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(16).uint32(m.uid);if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(24).int64(m.curCoin);if(m.holdStockNum!=null&&Object.hasOwnProperty.call(m,"holdStockNum"))w.uint32(32).uint32(m.holdStockNum);return w};DownDealerNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DownDealerNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.DownDealerNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.reason=r.int32();break;case 2:m.uid=r.uint32();break;case 3:m.curCoin=r.int64();break;case 4:m.holdStockNum=r.uint32();break;default:r.skipType(t&7);break}}return m};DownDealerNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DownDealerNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.reason!=null&&m.hasOwnProperty("reason")){switch(m.reason){default:return"reason: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:break}}if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}if(m.holdStockNum!=null&&m.hasOwnProperty("holdStockNum")){if(!$util.isInteger(m.holdStockNum))return"holdStockNum: integer expected"}return null};DownDealerNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.DownDealerNotify)return d;var m=new $root.humanboy_proto.DownDealerNotify;switch(d.reason){case"DownDummy":case 0:m.reason=0;break;case"NoMoney":case 1:m.reason=1;break;case"LongTime":case 2:m.reason=2;break;case"Leave":case 3:m.reason=3;break;case"Offline":case 4:m.reason=4;break;case"CalmDown":case 5:m.reason=5;break}if(d.uid!=null){m.uid=d.uid>>>0}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=false;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber()}if(d.holdStockNum!=null){m.holdStockNum=d.holdStockNum>>>0}return m};DownDealerNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.reason=o.enums===String?"DownDummy":0;d.uid=0;if($util.Long){var n=new $util.Long(0,0,false);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0;d.holdStockNum=0}if(m.reason!=null&&m.hasOwnProperty("reason")){d.reason=o.enums===String?$root.humanboy_proto.DownDealerReason[m.reason]:m.reason}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber():m.curCoin}if(m.holdStockNum!=null&&m.hasOwnProperty("holdStockNum")){d.holdStockNum=m.holdStockNum}return d};DownDealerNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DownDealerNotify}();humanboy_proto.DealerListReq=function(){function DealerListReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DealerListReq.create=function create(properties){return new DealerListReq(properties)};DealerListReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};DealerListReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DealerListReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.DealerListReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};DealerListReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DealerListReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};DealerListReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.DealerListReq)return d;return new $root.humanboy_proto.DealerListReq};DealerListReq.toObject=function toObject(){return{}};DealerListReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DealerListReq}();humanboy_proto.DealerListResp=function(){function DealerListResp(p){this.waitList=[];this.dealerList=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DealerListResp.prototype.code=0;DealerListResp.prototype.waitList=$util.emptyArray;DealerListResp.prototype.dealerList=$util.emptyArray;DealerListResp.create=function create(properties){return new DealerListResp(properties)};DealerListResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.waitList!=null&&m.waitList.length){for(var i=0;i<m.waitList.length;++i)$root.humanboy_proto.DealerPlayerInfo.encode(m.waitList[i],w.uint32(18).fork()).ldelim()}if(m.dealerList!=null&&m.dealerList.length){for(var i=0;i<m.dealerList.length;++i)$root.humanboy_proto.DealerPlayerInfo.encode(m.dealerList[i],w.uint32(26).fork()).ldelim()}return w};DealerListResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DealerListResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.DealerListResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:if(!(m.waitList&&m.waitList.length))m.waitList=[];m.waitList.push($root.humanboy_proto.DealerPlayerInfo.decode(r,r.uint32()));break;case 3:if(!(m.dealerList&&m.dealerList.length))m.dealerList=[];m.dealerList.push($root.humanboy_proto.DealerPlayerInfo.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};DealerListResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DealerListResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.waitList!=null&&m.hasOwnProperty("waitList")){if(!Array.isArray(m.waitList))return"waitList: array expected";for(var i=0;i<m.waitList.length;++i){{var e=$root.humanboy_proto.DealerPlayerInfo.verify(m.waitList[i]);if(e)return"waitList."+e}}}if(m.dealerList!=null&&m.hasOwnProperty("dealerList")){if(!Array.isArray(m.dealerList))return"dealerList: array expected";for(var i=0;i<m.dealerList.length;++i){{var e=$root.humanboy_proto.DealerPlayerInfo.verify(m.dealerList[i]);if(e)return"dealerList."+e}}}return null};DealerListResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.DealerListResp)return d;var m=new $root.humanboy_proto.DealerListResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.waitList){if(!Array.isArray(d.waitList))throw TypeError(".humanboy_proto.DealerListResp.waitList: array expected");m.waitList=[];for(var i=0;i<d.waitList.length;++i){if(typeof d.waitList[i]!=="object")throw TypeError(".humanboy_proto.DealerListResp.waitList: object expected");m.waitList[i]=$root.humanboy_proto.DealerPlayerInfo.fromObject(d.waitList[i])}}if(d.dealerList){if(!Array.isArray(d.dealerList))throw TypeError(".humanboy_proto.DealerListResp.dealerList: array expected");m.dealerList=[];for(var i=0;i<d.dealerList.length;++i){if(typeof d.dealerList[i]!=="object")throw TypeError(".humanboy_proto.DealerListResp.dealerList: object expected");m.dealerList[i]=$root.humanboy_proto.DealerPlayerInfo.fromObject(d.dealerList[i])}}return m};DealerListResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.waitList=[];d.dealerList=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.waitList&&m.waitList.length){d.waitList=[];for(var j=0;j<m.waitList.length;++j){d.waitList[j]=$root.humanboy_proto.DealerPlayerInfo.toObject(m.waitList[j],o)}}if(m.dealerList&&m.dealerList.length){d.dealerList=[];for(var j=0;j<m.dealerList.length;++j){d.dealerList[j]=$root.humanboy_proto.DealerPlayerInfo.toObject(m.dealerList[j],o)}}return d};DealerListResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DealerListResp}();humanboy_proto.DealerPlayerInfo=function(){function DealerPlayerInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}DealerPlayerInfo.prototype.uid=0;DealerPlayerInfo.prototype.head="";DealerPlayerInfo.prototype.name="";DealerPlayerInfo.prototype.curCoin=$util.Long?$util.Long.fromBits(0,0,true):0;DealerPlayerInfo.prototype.stockNum=0;DealerPlayerInfo.prototype.beDealerNum=0;DealerPlayerInfo.prototype.stockCoin=$util.Long?$util.Long.fromBits(0,0,false):0;DealerPlayerInfo.prototype.winningCoin=$util.Long?$util.Long.fromBits(0,0,false):0;DealerPlayerInfo.prototype.recentlyWinCoin=$util.Long?$util.Long.fromBits(0,0,false):0;DealerPlayerInfo.prototype.plat=0;DealerPlayerInfo.create=function create(properties){return new DealerPlayerInfo(properties)};DealerPlayerInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.head!=null&&Object.hasOwnProperty.call(m,"head"))w.uint32(18).string(m.head);if(m.name!=null&&Object.hasOwnProperty.call(m,"name"))w.uint32(26).string(m.name);if(m.curCoin!=null&&Object.hasOwnProperty.call(m,"curCoin"))w.uint32(32).uint64(m.curCoin);if(m.stockNum!=null&&Object.hasOwnProperty.call(m,"stockNum"))w.uint32(40).uint32(m.stockNum);if(m.beDealerNum!=null&&Object.hasOwnProperty.call(m,"beDealerNum"))w.uint32(48).uint32(m.beDealerNum);if(m.stockCoin!=null&&Object.hasOwnProperty.call(m,"stockCoin"))w.uint32(56).int64(m.stockCoin);if(m.winningCoin!=null&&Object.hasOwnProperty.call(m,"winningCoin"))w.uint32(64).int64(m.winningCoin);if(m.recentlyWinCoin!=null&&Object.hasOwnProperty.call(m,"recentlyWinCoin"))w.uint32(72).int64(m.recentlyWinCoin);if(m.plat!=null&&Object.hasOwnProperty.call(m,"plat"))w.uint32(80).uint32(m.plat);return w};DealerPlayerInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};DealerPlayerInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.DealerPlayerInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.head=r.string();break;case 3:m.name=r.string();break;case 4:m.curCoin=r.uint64();break;case 5:m.stockNum=r.uint32();break;case 6:m.beDealerNum=r.uint32();break;case 7:m.stockCoin=r.int64();break;case 8:m.winningCoin=r.int64();break;case 9:m.recentlyWinCoin=r.int64();break;case 10:m.plat=r.uint32();break;default:r.skipType(t&7);break}}return m};DealerPlayerInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};DealerPlayerInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.head!=null&&m.hasOwnProperty("head")){if(!$util.isString(m.head))return"head: string expected"}if(m.name!=null&&m.hasOwnProperty("name")){if(!$util.isString(m.name))return"name: string expected"}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(!$util.isInteger(m.curCoin)&&!(m.curCoin&&$util.isInteger(m.curCoin.low)&&$util.isInteger(m.curCoin.high)))return"curCoin: integer|Long expected"}if(m.stockNum!=null&&m.hasOwnProperty("stockNum")){if(!$util.isInteger(m.stockNum))return"stockNum: integer expected"}if(m.beDealerNum!=null&&m.hasOwnProperty("beDealerNum")){if(!$util.isInteger(m.beDealerNum))return"beDealerNum: integer expected"}if(m.stockCoin!=null&&m.hasOwnProperty("stockCoin")){if(!$util.isInteger(m.stockCoin)&&!(m.stockCoin&&$util.isInteger(m.stockCoin.low)&&$util.isInteger(m.stockCoin.high)))return"stockCoin: integer|Long expected"}if(m.winningCoin!=null&&m.hasOwnProperty("winningCoin")){if(!$util.isInteger(m.winningCoin)&&!(m.winningCoin&&$util.isInteger(m.winningCoin.low)&&$util.isInteger(m.winningCoin.high)))return"winningCoin: integer|Long expected"}if(m.recentlyWinCoin!=null&&m.hasOwnProperty("recentlyWinCoin")){if(!$util.isInteger(m.recentlyWinCoin)&&!(m.recentlyWinCoin&&$util.isInteger(m.recentlyWinCoin.low)&&$util.isInteger(m.recentlyWinCoin.high)))return"recentlyWinCoin: integer|Long expected"}if(m.plat!=null&&m.hasOwnProperty("plat")){if(!$util.isInteger(m.plat))return"plat: integer expected"}return null};DealerPlayerInfo.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.DealerPlayerInfo)return d;var m=new $root.humanboy_proto.DealerPlayerInfo;if(d.uid!=null){m.uid=d.uid>>>0}if(d.head!=null){m.head=String(d.head)}if(d.name!=null){m.name=String(d.name)}if(d.curCoin!=null){if($util.Long)(m.curCoin=$util.Long.fromValue(d.curCoin)).unsigned=true;else if(typeof d.curCoin==="string")m.curCoin=parseInt(d.curCoin,10);else if(typeof d.curCoin==="number")m.curCoin=d.curCoin;else if(typeof d.curCoin==="object")m.curCoin=new $util.LongBits(d.curCoin.low>>>0,d.curCoin.high>>>0).toNumber(true)}if(d.stockNum!=null){m.stockNum=d.stockNum>>>0}if(d.beDealerNum!=null){m.beDealerNum=d.beDealerNum>>>0}if(d.stockCoin!=null){if($util.Long)(m.stockCoin=$util.Long.fromValue(d.stockCoin)).unsigned=false;else if(typeof d.stockCoin==="string")m.stockCoin=parseInt(d.stockCoin,10);else if(typeof d.stockCoin==="number")m.stockCoin=d.stockCoin;else if(typeof d.stockCoin==="object")m.stockCoin=new $util.LongBits(d.stockCoin.low>>>0,d.stockCoin.high>>>0).toNumber()}if(d.winningCoin!=null){if($util.Long)(m.winningCoin=$util.Long.fromValue(d.winningCoin)).unsigned=false;else if(typeof d.winningCoin==="string")m.winningCoin=parseInt(d.winningCoin,10);else if(typeof d.winningCoin==="number")m.winningCoin=d.winningCoin;else if(typeof d.winningCoin==="object")m.winningCoin=new $util.LongBits(d.winningCoin.low>>>0,d.winningCoin.high>>>0).toNumber()}if(d.recentlyWinCoin!=null){if($util.Long)(m.recentlyWinCoin=$util.Long.fromValue(d.recentlyWinCoin)).unsigned=false;else if(typeof d.recentlyWinCoin==="string")m.recentlyWinCoin=parseInt(d.recentlyWinCoin,10);else if(typeof d.recentlyWinCoin==="number")m.recentlyWinCoin=d.recentlyWinCoin;else if(typeof d.recentlyWinCoin==="object")m.recentlyWinCoin=new $util.LongBits(d.recentlyWinCoin.low>>>0,d.recentlyWinCoin.high>>>0).toNumber()}if(d.plat!=null){m.plat=d.plat>>>0}return m};DealerPlayerInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;d.head="";d.name="";if($util.Long){var n=new $util.Long(0,0,true);d.curCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.curCoin=o.longs===String?"0":0;d.stockNum=0;d.beDealerNum=0;if($util.Long){var n=new $util.Long(0,0,false);d.stockCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.stockCoin=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.winningCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.winningCoin=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.recentlyWinCoin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.recentlyWinCoin=o.longs===String?"0":0;d.plat=0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.head!=null&&m.hasOwnProperty("head")){d.head=m.head}if(m.name!=null&&m.hasOwnProperty("name")){d.name=m.name}if(m.curCoin!=null&&m.hasOwnProperty("curCoin")){if(typeof m.curCoin==="number")d.curCoin=o.longs===String?String(m.curCoin):m.curCoin;else d.curCoin=o.longs===String?$util.Long.prototype.toString.call(m.curCoin):o.longs===Number?new $util.LongBits(m.curCoin.low>>>0,m.curCoin.high>>>0).toNumber(true):m.curCoin}if(m.stockNum!=null&&m.hasOwnProperty("stockNum")){d.stockNum=m.stockNum}if(m.beDealerNum!=null&&m.hasOwnProperty("beDealerNum")){d.beDealerNum=m.beDealerNum}if(m.stockCoin!=null&&m.hasOwnProperty("stockCoin")){if(typeof m.stockCoin==="number")d.stockCoin=o.longs===String?String(m.stockCoin):m.stockCoin;else d.stockCoin=o.longs===String?$util.Long.prototype.toString.call(m.stockCoin):o.longs===Number?new $util.LongBits(m.stockCoin.low>>>0,m.stockCoin.high>>>0).toNumber():m.stockCoin}if(m.winningCoin!=null&&m.hasOwnProperty("winningCoin")){if(typeof m.winningCoin==="number")d.winningCoin=o.longs===String?String(m.winningCoin):m.winningCoin;else d.winningCoin=o.longs===String?$util.Long.prototype.toString.call(m.winningCoin):o.longs===Number?new $util.LongBits(m.winningCoin.low>>>0,m.winningCoin.high>>>0).toNumber():m.winningCoin}if(m.recentlyWinCoin!=null&&m.hasOwnProperty("recentlyWinCoin")){if(typeof m.recentlyWinCoin==="number")d.recentlyWinCoin=o.longs===String?String(m.recentlyWinCoin):m.recentlyWinCoin;else d.recentlyWinCoin=o.longs===String?$util.Long.prototype.toString.call(m.recentlyWinCoin):o.longs===Number?new $util.LongBits(m.recentlyWinCoin.low>>>0,m.recentlyWinCoin.high>>>0).toNumber():m.recentlyWinCoin}if(m.plat!=null&&m.hasOwnProperty("plat")){d.plat=m.plat}return d};DealerPlayerInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return DealerPlayerInfo}();humanboy_proto.JackpotDataReq=function(){function JackpotDataReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotDataReq.prototype.roomType=0;JackpotDataReq.create=function create(properties){return new JackpotDataReq(properties)};JackpotDataReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomType!=null&&Object.hasOwnProperty.call(m,"roomType"))w.uint32(8).uint32(m.roomType);return w};JackpotDataReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotDataReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.JackpotDataReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomType=r.uint32();break;default:r.skipType(t&7);break}}return m};JackpotDataReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotDataReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomType!=null&&m.hasOwnProperty("roomType")){if(!$util.isInteger(m.roomType))return"roomType: integer expected"}return null};JackpotDataReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.JackpotDataReq)return d;var m=new $root.humanboy_proto.JackpotDataReq;if(d.roomType!=null){m.roomType=d.roomType>>>0}return m};JackpotDataReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomType=0}if(m.roomType!=null&&m.hasOwnProperty("roomType")){d.roomType=m.roomType}return d};JackpotDataReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotDataReq}();humanboy_proto.JackpotDataResp=function(){function JackpotDataResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotDataResp.prototype.code=0;JackpotDataResp.prototype.data=null;JackpotDataResp.create=function create(properties){return new JackpotDataResp(properties)};JackpotDataResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.data!=null&&Object.hasOwnProperty.call(m,"data"))$root.humanboy_proto.JackpotDataInfo.encode(m.data,w.uint32(18).fork()).ldelim();return w};JackpotDataResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotDataResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.JackpotDataResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.data=$root.humanboy_proto.JackpotDataInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};JackpotDataResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotDataResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.data!=null&&m.hasOwnProperty("data")){{var e=$root.humanboy_proto.JackpotDataInfo.verify(m.data);if(e)return"data."+e}}return null};JackpotDataResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.JackpotDataResp)return d;var m=new $root.humanboy_proto.JackpotDataResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.data!=null){if(typeof d.data!=="object")throw TypeError(".humanboy_proto.JackpotDataResp.data: object expected");m.data=$root.humanboy_proto.JackpotDataInfo.fromObject(d.data)}return m};JackpotDataResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.data=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.data!=null&&m.hasOwnProperty("data")){d.data=$root.humanboy_proto.JackpotDataInfo.toObject(m.data,o)}return d};JackpotDataResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotDataResp}();humanboy_proto.JackpotDataInfo=function(){function JackpotDataInfo(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotDataInfo.prototype.leftAmount=$util.Long?$util.Long.fromBits(0,0,true):0;JackpotDataInfo.prototype.huangTongPer=0;JackpotDataInfo.prototype.siTiaoPer=0;JackpotDataInfo.prototype.tongHuaShunPer=0;JackpotDataInfo.prototype.roomType=0;JackpotDataInfo.create=function create(properties){return new JackpotDataInfo(properties)};JackpotDataInfo.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.leftAmount!=null&&Object.hasOwnProperty.call(m,"leftAmount"))w.uint32(8).uint64(m.leftAmount);if(m.huangTongPer!=null&&Object.hasOwnProperty.call(m,"huangTongPer"))w.uint32(16).uint32(m.huangTongPer);if(m.siTiaoPer!=null&&Object.hasOwnProperty.call(m,"siTiaoPer"))w.uint32(24).uint32(m.siTiaoPer);if(m.tongHuaShunPer!=null&&Object.hasOwnProperty.call(m,"tongHuaShunPer"))w.uint32(32).uint32(m.tongHuaShunPer);if(m.roomType!=null&&Object.hasOwnProperty.call(m,"roomType"))w.uint32(40).uint32(m.roomType);return w};JackpotDataInfo.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotDataInfo.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.JackpotDataInfo;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.leftAmount=r.uint64();break;case 2:m.huangTongPer=r.uint32();break;case 3:m.siTiaoPer=r.uint32();break;case 4:m.tongHuaShunPer=r.uint32();break;case 5:m.roomType=r.uint32();break;default:r.skipType(t&7);break}}return m};JackpotDataInfo.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotDataInfo.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.leftAmount!=null&&m.hasOwnProperty("leftAmount")){if(!$util.isInteger(m.leftAmount)&&!(m.leftAmount&&$util.isInteger(m.leftAmount.low)&&$util.isInteger(m.leftAmount.high)))return"leftAmount: integer|Long expected"}if(m.huangTongPer!=null&&m.hasOwnProperty("huangTongPer")){if(!$util.isInteger(m.huangTongPer))return"huangTongPer: integer expected"}if(m.siTiaoPer!=null&&m.hasOwnProperty("siTiaoPer")){if(!$util.isInteger(m.siTiaoPer))return"siTiaoPer: integer expected"}if(m.tongHuaShunPer!=null&&m.hasOwnProperty("tongHuaShunPer")){if(!$util.isInteger(m.tongHuaShunPer))return"tongHuaShunPer: integer expected"}if(m.roomType!=null&&m.hasOwnProperty("roomType")){if(!$util.isInteger(m.roomType))return"roomType: integer expected"}return null};JackpotDataInfo.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.JackpotDataInfo)return d;var m=new $root.humanboy_proto.JackpotDataInfo;if(d.leftAmount!=null){if($util.Long)(m.leftAmount=$util.Long.fromValue(d.leftAmount)).unsigned=true;else if(typeof d.leftAmount==="string")m.leftAmount=parseInt(d.leftAmount,10);else if(typeof d.leftAmount==="number")m.leftAmount=d.leftAmount;else if(typeof d.leftAmount==="object")m.leftAmount=new $util.LongBits(d.leftAmount.low>>>0,d.leftAmount.high>>>0).toNumber(true)}if(d.huangTongPer!=null){m.huangTongPer=d.huangTongPer>>>0}if(d.siTiaoPer!=null){m.siTiaoPer=d.siTiaoPer>>>0}if(d.tongHuaShunPer!=null){m.tongHuaShunPer=d.tongHuaShunPer>>>0}if(d.roomType!=null){m.roomType=d.roomType>>>0}return m};JackpotDataInfo.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,true);d.leftAmount=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftAmount=o.longs===String?"0":0;d.huangTongPer=0;d.siTiaoPer=0;d.tongHuaShunPer=0;d.roomType=0}if(m.leftAmount!=null&&m.hasOwnProperty("leftAmount")){if(typeof m.leftAmount==="number")d.leftAmount=o.longs===String?String(m.leftAmount):m.leftAmount;else d.leftAmount=o.longs===String?$util.Long.prototype.toString.call(m.leftAmount):o.longs===Number?new $util.LongBits(m.leftAmount.low>>>0,m.leftAmount.high>>>0).toNumber(true):m.leftAmount}if(m.huangTongPer!=null&&m.hasOwnProperty("huangTongPer")){d.huangTongPer=m.huangTongPer}if(m.siTiaoPer!=null&&m.hasOwnProperty("siTiaoPer")){d.siTiaoPer=m.siTiaoPer}if(m.tongHuaShunPer!=null&&m.hasOwnProperty("tongHuaShunPer")){d.tongHuaShunPer=m.tongHuaShunPer}if(m.roomType!=null&&m.hasOwnProperty("roomType")){d.roomType=m.roomType}return d};JackpotDataInfo.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotDataInfo}();humanboy_proto.JackpotAwardListReq=function(){function JackpotAwardListReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotAwardListReq.prototype.roomType=0;JackpotAwardListReq.create=function create(properties){return new JackpotAwardListReq(properties)};JackpotAwardListReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.roomType!=null&&Object.hasOwnProperty.call(m,"roomType"))w.uint32(8).uint32(m.roomType);return w};JackpotAwardListReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotAwardListReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.JackpotAwardListReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.roomType=r.uint32();break;default:r.skipType(t&7);break}}return m};JackpotAwardListReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotAwardListReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.roomType!=null&&m.hasOwnProperty("roomType")){if(!$util.isInteger(m.roomType))return"roomType: integer expected"}return null};JackpotAwardListReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.JackpotAwardListReq)return d;var m=new $root.humanboy_proto.JackpotAwardListReq;if(d.roomType!=null){m.roomType=d.roomType>>>0}return m};JackpotAwardListReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.roomType=0}if(m.roomType!=null&&m.hasOwnProperty("roomType")){d.roomType=m.roomType}return d};JackpotAwardListReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotAwardListReq}();humanboy_proto.JackpotAwardListResp=function(){function JackpotAwardListResp(p){this.lastData=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}JackpotAwardListResp.prototype.code=0;JackpotAwardListResp.prototype.luckyOne=null;JackpotAwardListResp.prototype.lastData=$util.emptyArray;JackpotAwardListResp.create=function create(properties){return new JackpotAwardListResp(properties)};JackpotAwardListResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.luckyOne!=null&&Object.hasOwnProperty.call(m,"luckyOne"))$root.humanboy_proto.AwardData.encode(m.luckyOne,w.uint32(18).fork()).ldelim();if(m.lastData!=null&&m.lastData.length){for(var i=0;i<m.lastData.length;++i)$root.humanboy_proto.AwardData.encode(m.lastData[i],w.uint32(26).fork()).ldelim()}return w};JackpotAwardListResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};JackpotAwardListResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.JackpotAwardListResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.luckyOne=$root.humanboy_proto.AwardData.decode(r,r.uint32());break;case 3:if(!(m.lastData&&m.lastData.length))m.lastData=[];m.lastData.push($root.humanboy_proto.AwardData.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};JackpotAwardListResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};JackpotAwardListResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.luckyOne!=null&&m.hasOwnProperty("luckyOne")){{var e=$root.humanboy_proto.AwardData.verify(m.luckyOne);if(e)return"luckyOne."+e}}if(m.lastData!=null&&m.hasOwnProperty("lastData")){if(!Array.isArray(m.lastData))return"lastData: array expected";for(var i=0;i<m.lastData.length;++i){{var e=$root.humanboy_proto.AwardData.verify(m.lastData[i]);if(e)return"lastData."+e}}}return null};JackpotAwardListResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.JackpotAwardListResp)return d;var m=new $root.humanboy_proto.JackpotAwardListResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.luckyOne!=null){if(typeof d.luckyOne!=="object")throw TypeError(".humanboy_proto.JackpotAwardListResp.luckyOne: object expected");m.luckyOne=$root.humanboy_proto.AwardData.fromObject(d.luckyOne)}if(d.lastData){if(!Array.isArray(d.lastData))throw TypeError(".humanboy_proto.JackpotAwardListResp.lastData: array expected");m.lastData=[];for(var i=0;i<d.lastData.length;++i){if(typeof d.lastData[i]!=="object")throw TypeError(".humanboy_proto.JackpotAwardListResp.lastData: object expected");m.lastData[i]=$root.humanboy_proto.AwardData.fromObject(d.lastData[i])}}return m};JackpotAwardListResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.lastData=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.luckyOne=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.luckyOne!=null&&m.hasOwnProperty("luckyOne")){d.luckyOne=$root.humanboy_proto.AwardData.toObject(m.luckyOne,o)}if(m.lastData&&m.lastData.length){d.lastData=[];for(var j=0;j<m.lastData.length;++j){d.lastData[j]=$root.humanboy_proto.AwardData.toObject(m.lastData[j],o)}}return d};JackpotAwardListResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return JackpotAwardListResp}();humanboy_proto.AwardData=function(){function AwardData(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AwardData.prototype.name="";AwardData.prototype.handLevel=0;AwardData.prototype.amount=0;AwardData.prototype.timeStamp=$util.Long?$util.Long.fromBits(0,0,true):0;AwardData.create=function create(properties){return new AwardData(properties)};AwardData.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.name!=null&&Object.hasOwnProperty.call(m,"name"))w.uint32(10).string(m.name);if(m.handLevel!=null&&Object.hasOwnProperty.call(m,"handLevel"))w.uint32(16).int32(m.handLevel);if(m.amount!=null&&Object.hasOwnProperty.call(m,"amount"))w.uint32(24).uint32(m.amount);if(m.timeStamp!=null&&Object.hasOwnProperty.call(m,"timeStamp"))w.uint32(32).uint64(m.timeStamp);return w};AwardData.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AwardData.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AwardData;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.name=r.string();break;case 2:m.handLevel=r.int32();break;case 3:m.amount=r.uint32();break;case 4:m.timeStamp=r.uint64();break;default:r.skipType(t&7);break}}return m};AwardData.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AwardData.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.name!=null&&m.hasOwnProperty("name")){if(!$util.isString(m.name))return"name: string expected"}if(m.handLevel!=null&&m.hasOwnProperty("handLevel")){switch(m.handLevel){default:return"handLevel: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:break}}if(m.amount!=null&&m.hasOwnProperty("amount")){if(!$util.isInteger(m.amount))return"amount: integer expected"}if(m.timeStamp!=null&&m.hasOwnProperty("timeStamp")){if(!$util.isInteger(m.timeStamp)&&!(m.timeStamp&&$util.isInteger(m.timeStamp.low)&&$util.isInteger(m.timeStamp.high)))return"timeStamp: integer|Long expected"}return null};AwardData.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AwardData)return d;var m=new $root.humanboy_proto.AwardData;if(d.name!=null){m.name=String(d.name)}switch(d.handLevel){case"CardResult_Dummy":case 0:m.handLevel=0;break;case"GAO_PAI":case 1:m.handLevel=1;break;case"YI_DUI":case 2:m.handLevel=2;break;case"LIAN_DUI":case 3:m.handLevel=3;break;case"SAN_TIAO":case 4:m.handLevel=4;break;case"SHUN_ZI":case 5:m.handLevel=5;break;case"TONG_HUA":case 6:m.handLevel=6;break;case"HU_LU":case 7:m.handLevel=7;break;case"SI_TIAO":case 8:m.handLevel=8;break;case"TONG_HUA_SHUN":case 9:m.handLevel=9;break;case"HUANG_TONG":case 10:m.handLevel=10;break}if(d.amount!=null){m.amount=d.amount>>>0}if(d.timeStamp!=null){if($util.Long)(m.timeStamp=$util.Long.fromValue(d.timeStamp)).unsigned=true;else if(typeof d.timeStamp==="string")m.timeStamp=parseInt(d.timeStamp,10);else if(typeof d.timeStamp==="number")m.timeStamp=d.timeStamp;else if(typeof d.timeStamp==="object")m.timeStamp=new $util.LongBits(d.timeStamp.low>>>0,d.timeStamp.high>>>0).toNumber(true)}return m};AwardData.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.name="";d.handLevel=o.enums===String?"CardResult_Dummy":0;d.amount=0;if($util.Long){var n=new $util.Long(0,0,true);d.timeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.timeStamp=o.longs===String?"0":0}if(m.name!=null&&m.hasOwnProperty("name")){d.name=m.name}if(m.handLevel!=null&&m.hasOwnProperty("handLevel")){d.handLevel=o.enums===String?$root.humanboy_proto.CardResult[m.handLevel]:m.handLevel}if(m.amount!=null&&m.hasOwnProperty("amount")){d.amount=m.amount}if(m.timeStamp!=null&&m.hasOwnProperty("timeStamp")){if(typeof m.timeStamp==="number")d.timeStamp=o.longs===String?String(m.timeStamp):m.timeStamp;else d.timeStamp=o.longs===String?$util.Long.prototype.toString.call(m.timeStamp):o.longs===Number?new $util.LongBits(m.timeStamp.low>>>0,m.timeStamp.high>>>0).toNumber(true):m.timeStamp}return d};AwardData.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AwardData}();humanboy_proto.HitJackpotAward=function(){function HitJackpotAward(p){this.hitJackpotAwardData=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}HitJackpotAward.prototype.option=0;HitJackpotAward.prototype.hitJackpotAwardData=$util.emptyArray;HitJackpotAward.create=function create(properties){return new HitJackpotAward(properties)};HitJackpotAward.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.option!=null&&Object.hasOwnProperty.call(m,"option"))w.uint32(8).int32(m.option);if(m.hitJackpotAwardData!=null&&m.hitJackpotAwardData.length){for(var i=0;i<m.hitJackpotAwardData.length;++i)$root.humanboy_proto.HitJackpotAwardData.encode(m.hitJackpotAwardData[i],w.uint32(18).fork()).ldelim()}return w};HitJackpotAward.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};HitJackpotAward.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.HitJackpotAward;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.option=r.int32();break;case 2:if(!(m.hitJackpotAwardData&&m.hitJackpotAwardData.length))m.hitJackpotAwardData=[];m.hitJackpotAwardData.push($root.humanboy_proto.HitJackpotAwardData.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};HitJackpotAward.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};HitJackpotAward.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.option!=null&&m.hasOwnProperty("option")){switch(m.option){default:return"option: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 100:case 101:case 102:case 103:case 104:case 105:case 106:break}}if(m.hitJackpotAwardData!=null&&m.hasOwnProperty("hitJackpotAwardData")){if(!Array.isArray(m.hitJackpotAwardData))return"hitJackpotAwardData: array expected";for(var i=0;i<m.hitJackpotAwardData.length;++i){{var e=$root.humanboy_proto.HitJackpotAwardData.verify(m.hitJackpotAwardData[i]);if(e)return"hitJackpotAwardData."+e}}}return null};HitJackpotAward.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.HitJackpotAward)return d;var m=new $root.humanboy_proto.HitJackpotAward;switch(d.option){case"BetZoneOption_DUMMY":case 0:m.option=0;break;case"HOST":case 1:m.option=1;break;case"POS1":case 2:m.option=2;break;case"POS2":case 3:m.option=3;break;case"POS3":case 4:m.option=4;break;case"POS4":case 5:m.option=5;break;case"POS_LUCK":case 100:m.option=100;break;case"POS_LUCK_1":case 101:m.option=101;break;case"POS_LUCK_2":case 102:m.option=102;break;case"POS_LUCK_3":case 103:m.option=103;break;case"POS_LUCK_4":case 104:m.option=104;break;case"POS_LUCK_5":case 105:m.option=105;break;case"POS_LUCK_6":case 106:m.option=106;break}if(d.hitJackpotAwardData){if(!Array.isArray(d.hitJackpotAwardData))throw TypeError(".humanboy_proto.HitJackpotAward.hitJackpotAwardData: array expected");m.hitJackpotAwardData=[];for(var i=0;i<d.hitJackpotAwardData.length;++i){if(typeof d.hitJackpotAwardData[i]!=="object")throw TypeError(".humanboy_proto.HitJackpotAward.hitJackpotAwardData: object expected");m.hitJackpotAwardData[i]=$root.humanboy_proto.HitJackpotAwardData.fromObject(d.hitJackpotAwardData[i])}}return m};HitJackpotAward.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.hitJackpotAwardData=[]}if(o.defaults){d.option=o.enums===String?"BetZoneOption_DUMMY":0}if(m.option!=null&&m.hasOwnProperty("option")){d.option=o.enums===String?$root.humanboy_proto.BetZoneOption[m.option]:m.option}if(m.hitJackpotAwardData&&m.hitJackpotAwardData.length){d.hitJackpotAwardData=[];for(var j=0;j<m.hitJackpotAwardData.length;++j){d.hitJackpotAwardData[j]=$root.humanboy_proto.HitJackpotAwardData.toObject(m.hitJackpotAwardData[j],o)}}return d};HitJackpotAward.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return HitJackpotAward}();humanboy_proto.HitJackpotAwardData=function(){function HitJackpotAwardData(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}HitJackpotAwardData.prototype.uid=0;HitJackpotAwardData.prototype.amount=0;HitJackpotAwardData.create=function create(properties){return new HitJackpotAwardData(properties)};HitJackpotAwardData.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.amount!=null&&Object.hasOwnProperty.call(m,"amount"))w.uint32(16).uint32(m.amount);return w};HitJackpotAwardData.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};HitJackpotAwardData.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.HitJackpotAwardData;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.amount=r.uint32();break;default:r.skipType(t&7);break}}return m};HitJackpotAwardData.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};HitJackpotAwardData.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.amount!=null&&m.hasOwnProperty("amount")){if(!$util.isInteger(m.amount))return"amount: integer expected"}return null};HitJackpotAwardData.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.HitJackpotAwardData)return d;var m=new $root.humanboy_proto.HitJackpotAwardData;if(d.uid!=null){m.uid=d.uid>>>0}if(d.amount!=null){m.amount=d.amount>>>0}return m};HitJackpotAwardData.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;d.amount=0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.amount!=null&&m.hasOwnProperty("amount")){d.amount=m.amount}return d};HitJackpotAwardData.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return HitJackpotAwardData}();humanboy_proto.GameWillStartNotify=function(){function GameWillStartNotify(p){this.dealer=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}GameWillStartNotify.prototype.showMiddleUpDealerBtn=false;GameWillStartNotify.prototype.surplusStockNum=0;GameWillStartNotify.prototype.roundState=0;GameWillStartNotify.prototype.leftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;GameWillStartNotify.prototype.nextRoundEndStamp=$util.Long?$util.Long.fromBits(0,0,false):0;GameWillStartNotify.prototype.dealer=$util.emptyArray;GameWillStartNotify.create=function create(properties){return new GameWillStartNotify(properties)};GameWillStartNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.showMiddleUpDealerBtn!=null&&Object.hasOwnProperty.call(m,"showMiddleUpDealerBtn"))w.uint32(8).bool(m.showMiddleUpDealerBtn);if(m.surplusStockNum!=null&&Object.hasOwnProperty.call(m,"surplusStockNum"))w.uint32(16).uint32(m.surplusStockNum);if(m.roundState!=null&&Object.hasOwnProperty.call(m,"roundState"))w.uint32(24).int32(m.roundState);if(m.leftSeconds!=null&&Object.hasOwnProperty.call(m,"leftSeconds"))w.uint32(32).int64(m.leftSeconds);if(m.nextRoundEndStamp!=null&&Object.hasOwnProperty.call(m,"nextRoundEndStamp"))w.uint32(40).int64(m.nextRoundEndStamp);if(m.dealer!=null&&m.dealer.length){for(var i=0;i<m.dealer.length;++i)$root.humanboy_proto.DealerPlayerInfo.encode(m.dealer[i],w.uint32(50).fork()).ldelim()}return w};GameWillStartNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};GameWillStartNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.GameWillStartNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.showMiddleUpDealerBtn=r.bool();break;case 2:m.surplusStockNum=r.uint32();break;case 3:m.roundState=r.int32();break;case 4:m.leftSeconds=r.int64();break;case 5:m.nextRoundEndStamp=r.int64();break;case 6:if(!(m.dealer&&m.dealer.length))m.dealer=[];m.dealer.push($root.humanboy_proto.DealerPlayerInfo.decode(r,r.uint32()));break;default:r.skipType(t&7);break}}return m};GameWillStartNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};GameWillStartNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.showMiddleUpDealerBtn!=null&&m.hasOwnProperty("showMiddleUpDealerBtn")){if(typeof m.showMiddleUpDealerBtn!=="boolean")return"showMiddleUpDealerBtn: boolean expected"}if(m.surplusStockNum!=null&&m.hasOwnProperty("surplusStockNum")){if(!$util.isInteger(m.surplusStockNum))return"surplusStockNum: integer expected"}if(m.roundState!=null&&m.hasOwnProperty("roundState")){switch(m.roundState){default:return"roundState: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:break}}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(!$util.isInteger(m.leftSeconds)&&!(m.leftSeconds&&$util.isInteger(m.leftSeconds.low)&&$util.isInteger(m.leftSeconds.high)))return"leftSeconds: integer|Long expected"}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(!$util.isInteger(m.nextRoundEndStamp)&&!(m.nextRoundEndStamp&&$util.isInteger(m.nextRoundEndStamp.low)&&$util.isInteger(m.nextRoundEndStamp.high)))return"nextRoundEndStamp: integer|Long expected"}if(m.dealer!=null&&m.hasOwnProperty("dealer")){if(!Array.isArray(m.dealer))return"dealer: array expected";for(var i=0;i<m.dealer.length;++i){{var e=$root.humanboy_proto.DealerPlayerInfo.verify(m.dealer[i]);if(e)return"dealer."+e}}}return null};GameWillStartNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.GameWillStartNotify)return d;var m=new $root.humanboy_proto.GameWillStartNotify;if(d.showMiddleUpDealerBtn!=null){m.showMiddleUpDealerBtn=Boolean(d.showMiddleUpDealerBtn)}if(d.surplusStockNum!=null){m.surplusStockNum=d.surplusStockNum>>>0}switch(d.roundState){case"RoundState_DUMMY":case 0:m.roundState=0;break;case"GAME_PENDING":case 1:m.roundState=1;break;case"NEW_ROUND":case 2:m.roundState=2;break;case"BET":case 3:m.roundState=3;break;case"WAIT_NEXT_ROUND":case 4:m.roundState=4;break;case"WAIT_NEXT_ROUND2":case 5:m.roundState=5;break}if(d.leftSeconds!=null){if($util.Long)(m.leftSeconds=$util.Long.fromValue(d.leftSeconds)).unsigned=false;else if(typeof d.leftSeconds==="string")m.leftSeconds=parseInt(d.leftSeconds,10);else if(typeof d.leftSeconds==="number")m.leftSeconds=d.leftSeconds;else if(typeof d.leftSeconds==="object")m.leftSeconds=new $util.LongBits(d.leftSeconds.low>>>0,d.leftSeconds.high>>>0).toNumber()}if(d.nextRoundEndStamp!=null){if($util.Long)(m.nextRoundEndStamp=$util.Long.fromValue(d.nextRoundEndStamp)).unsigned=false;else if(typeof d.nextRoundEndStamp==="string")m.nextRoundEndStamp=parseInt(d.nextRoundEndStamp,10);else if(typeof d.nextRoundEndStamp==="number")m.nextRoundEndStamp=d.nextRoundEndStamp;else if(typeof d.nextRoundEndStamp==="object")m.nextRoundEndStamp=new $util.LongBits(d.nextRoundEndStamp.low>>>0,d.nextRoundEndStamp.high>>>0).toNumber()}if(d.dealer){if(!Array.isArray(d.dealer))throw TypeError(".humanboy_proto.GameWillStartNotify.dealer: array expected");m.dealer=[];for(var i=0;i<d.dealer.length;++i){if(typeof d.dealer[i]!=="object")throw TypeError(".humanboy_proto.GameWillStartNotify.dealer: object expected");m.dealer[i]=$root.humanboy_proto.DealerPlayerInfo.fromObject(d.dealer[i])}}return m};GameWillStartNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.dealer=[]}if(o.defaults){d.showMiddleUpDealerBtn=false;d.surplusStockNum=0;d.roundState=o.enums===String?"RoundState_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.leftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.leftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.nextRoundEndStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.nextRoundEndStamp=o.longs===String?"0":0}if(m.showMiddleUpDealerBtn!=null&&m.hasOwnProperty("showMiddleUpDealerBtn")){d.showMiddleUpDealerBtn=m.showMiddleUpDealerBtn}if(m.surplusStockNum!=null&&m.hasOwnProperty("surplusStockNum")){d.surplusStockNum=m.surplusStockNum}if(m.roundState!=null&&m.hasOwnProperty("roundState")){d.roundState=o.enums===String?$root.humanboy_proto.RoundState[m.roundState]:m.roundState}if(m.leftSeconds!=null&&m.hasOwnProperty("leftSeconds")){if(typeof m.leftSeconds==="number")d.leftSeconds=o.longs===String?String(m.leftSeconds):m.leftSeconds;else d.leftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.leftSeconds):o.longs===Number?new $util.LongBits(m.leftSeconds.low>>>0,m.leftSeconds.high>>>0).toNumber():m.leftSeconds}if(m.nextRoundEndStamp!=null&&m.hasOwnProperty("nextRoundEndStamp")){if(typeof m.nextRoundEndStamp==="number")d.nextRoundEndStamp=o.longs===String?String(m.nextRoundEndStamp):m.nextRoundEndStamp;else d.nextRoundEndStamp=o.longs===String?$util.Long.prototype.toString.call(m.nextRoundEndStamp):o.longs===Number?new $util.LongBits(m.nextRoundEndStamp.low>>>0,m.nextRoundEndStamp.high>>>0).toNumber():m.nextRoundEndStamp}if(m.dealer&&m.dealer.length){d.dealer=[];for(var j=0;j<m.dealer.length;++j){d.dealer[j]=$root.humanboy_proto.DealerPlayerInfo.toObject(m.dealer[j],o)}}return d};GameWillStartNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return GameWillStartNotify}();humanboy_proto.KickDealerApplyNotify=function(){function KickDealerApplyNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}KickDealerApplyNotify.prototype.reason=0;KickDealerApplyNotify.prototype.extension="";KickDealerApplyNotify.create=function create(properties){return new KickDealerApplyNotify(properties)};KickDealerApplyNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.reason!=null&&Object.hasOwnProperty.call(m,"reason"))w.uint32(8).int32(m.reason);if(m.extension!=null&&Object.hasOwnProperty.call(m,"extension"))w.uint32(18).string(m.extension);return w};KickDealerApplyNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};KickDealerApplyNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.KickDealerApplyNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.reason=r.int32();break;case 2:m.extension=r.string();break;default:r.skipType(t&7);break}}return m};KickDealerApplyNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};KickDealerApplyNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.reason!=null&&m.hasOwnProperty("reason")){switch(m.reason){default:return"reason: enum value expected";case 0:case 1:case 2:case 3:case 4:break}}if(m.extension!=null&&m.hasOwnProperty("extension")){if(!$util.isString(m.extension))return"extension: string expected"}return null};KickDealerApplyNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.KickDealerApplyNotify)return d;var m=new $root.humanboy_proto.KickDealerApplyNotify;switch(d.reason){case"K_NULL":case 0:m.reason=0;break;case"K_NoMoney":case 1:m.reason=1;break;case"K_SUPPLY":case 2:m.reason=2;break;case"K_OFFLINE":case 3:m.reason=3;break;case"K_LEAVE":case 4:m.reason=4;break}if(d.extension!=null){m.extension=String(d.extension)}return m};KickDealerApplyNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.reason=o.enums===String?"K_NULL":0;d.extension=""}if(m.reason!=null&&m.hasOwnProperty("reason")){d.reason=o.enums===String?$root.humanboy_proto.KickApplyDealerReason[m.reason]:m.reason}if(m.extension!=null&&m.hasOwnProperty("extension")){d.extension=m.extension}return d};KickDealerApplyNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return KickDealerApplyNotify}();humanboy_proto.SetGameOptionReq=function(){function SetGameOptionReq(p){this.betCoinOption=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SetGameOptionReq.prototype.autoLevel=0;SetGameOptionReq.prototype.betCoinOption=$util.emptyArray;SetGameOptionReq.create=function create(properties){return new SetGameOptionReq(properties)};SetGameOptionReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.autoLevel!=null&&Object.hasOwnProperty.call(m,"autoLevel"))w.uint32(8).int32(m.autoLevel);if(m.betCoinOption!=null&&m.betCoinOption.length){w.uint32(18).fork();for(var i=0;i<m.betCoinOption.length;++i)w.uint64(m.betCoinOption[i]);w.ldelim()}return w};SetGameOptionReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SetGameOptionReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.SetGameOptionReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.autoLevel=r.int32();break;case 2:if(!(m.betCoinOption&&m.betCoinOption.length))m.betCoinOption=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.betCoinOption.push(r.uint64())}else m.betCoinOption.push(r.uint64());break;default:r.skipType(t&7);break}}return m};SetGameOptionReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SetGameOptionReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){switch(m.autoLevel){default:return"autoLevel: enum value expected";case 0:case 1:break}}if(m.betCoinOption!=null&&m.hasOwnProperty("betCoinOption")){if(!Array.isArray(m.betCoinOption))return"betCoinOption: array expected";for(var i=0;i<m.betCoinOption.length;++i){if(!$util.isInteger(m.betCoinOption[i])&&!(m.betCoinOption[i]&&$util.isInteger(m.betCoinOption[i].low)&&$util.isInteger(m.betCoinOption[i].high)))return"betCoinOption: integer|Long[] expected"}}return null};SetGameOptionReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.SetGameOptionReq)return d;var m=new $root.humanboy_proto.SetGameOptionReq;switch(d.autoLevel){case"Level_Normal":case 0:m.autoLevel=0;break;case"Level_Advance":case 1:m.autoLevel=1;break}if(d.betCoinOption){if(!Array.isArray(d.betCoinOption))throw TypeError(".humanboy_proto.SetGameOptionReq.betCoinOption: array expected");m.betCoinOption=[];for(var i=0;i<d.betCoinOption.length;++i){if($util.Long)(m.betCoinOption[i]=$util.Long.fromValue(d.betCoinOption[i])).unsigned=true;else if(typeof d.betCoinOption[i]==="string")m.betCoinOption[i]=parseInt(d.betCoinOption[i],10);else if(typeof d.betCoinOption[i]==="number")m.betCoinOption[i]=d.betCoinOption[i];else if(typeof d.betCoinOption[i]==="object")m.betCoinOption[i]=new $util.LongBits(d.betCoinOption[i].low>>>0,d.betCoinOption[i].high>>>0).toNumber(true)}}return m};SetGameOptionReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.betCoinOption=[]}if(o.defaults){d.autoLevel=o.enums===String?"Level_Normal":0}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){d.autoLevel=o.enums===String?$root.humanboy_proto.AutoBetLevel[m.autoLevel]:m.autoLevel}if(m.betCoinOption&&m.betCoinOption.length){d.betCoinOption=[];for(var j=0;j<m.betCoinOption.length;++j){if(typeof m.betCoinOption[j]==="number")d.betCoinOption[j]=o.longs===String?String(m.betCoinOption[j]):m.betCoinOption[j];else d.betCoinOption[j]=o.longs===String?$util.Long.prototype.toString.call(m.betCoinOption[j]):o.longs===Number?new $util.LongBits(m.betCoinOption[j].low>>>0,m.betCoinOption[j].high>>>0).toNumber(true):m.betCoinOption[j]}}return d};SetGameOptionReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SetGameOptionReq}();humanboy_proto.SetGameOptionResp=function(){function SetGameOptionResp(p){this.betCoinOption=[];if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SetGameOptionResp.prototype.code=0;SetGameOptionResp.prototype.autoLevel=0;SetGameOptionResp.prototype.betCoinOption=$util.emptyArray;SetGameOptionResp.create=function create(properties){return new SetGameOptionResp(properties)};SetGameOptionResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.autoLevel!=null&&Object.hasOwnProperty.call(m,"autoLevel"))w.uint32(16).int32(m.autoLevel);if(m.betCoinOption!=null&&m.betCoinOption.length){w.uint32(26).fork();for(var i=0;i<m.betCoinOption.length;++i)w.uint64(m.betCoinOption[i]);w.ldelim()}return w};SetGameOptionResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SetGameOptionResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.SetGameOptionResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.autoLevel=r.int32();break;case 3:if(!(m.betCoinOption&&m.betCoinOption.length))m.betCoinOption=[];if((t&7)===2){var c2=r.uint32()+r.pos;while(r.pos<c2)m.betCoinOption.push(r.uint64())}else m.betCoinOption.push(r.uint64());break;default:r.skipType(t&7);break}}return m};SetGameOptionResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SetGameOptionResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){switch(m.autoLevel){default:return"autoLevel: enum value expected";case 0:case 1:break}}if(m.betCoinOption!=null&&m.hasOwnProperty("betCoinOption")){if(!Array.isArray(m.betCoinOption))return"betCoinOption: array expected";for(var i=0;i<m.betCoinOption.length;++i){if(!$util.isInteger(m.betCoinOption[i])&&!(m.betCoinOption[i]&&$util.isInteger(m.betCoinOption[i].low)&&$util.isInteger(m.betCoinOption[i].high)))return"betCoinOption: integer|Long[] expected"}}return null};SetGameOptionResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.SetGameOptionResp)return d;var m=new $root.humanboy_proto.SetGameOptionResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}switch(d.autoLevel){case"Level_Normal":case 0:m.autoLevel=0;break;case"Level_Advance":case 1:m.autoLevel=1;break}if(d.betCoinOption){if(!Array.isArray(d.betCoinOption))throw TypeError(".humanboy_proto.SetGameOptionResp.betCoinOption: array expected");m.betCoinOption=[];for(var i=0;i<d.betCoinOption.length;++i){if($util.Long)(m.betCoinOption[i]=$util.Long.fromValue(d.betCoinOption[i])).unsigned=true;else if(typeof d.betCoinOption[i]==="string")m.betCoinOption[i]=parseInt(d.betCoinOption[i],10);else if(typeof d.betCoinOption[i]==="number")m.betCoinOption[i]=d.betCoinOption[i];else if(typeof d.betCoinOption[i]==="object")m.betCoinOption[i]=new $util.LongBits(d.betCoinOption[i].low>>>0,d.betCoinOption[i].high>>>0).toNumber(true)}}return m};SetGameOptionResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.arrays||o.defaults){d.betCoinOption=[]}if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.autoLevel=o.enums===String?"Level_Normal":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.autoLevel!=null&&m.hasOwnProperty("autoLevel")){d.autoLevel=o.enums===String?$root.humanboy_proto.AutoBetLevel[m.autoLevel]:m.autoLevel}if(m.betCoinOption&&m.betCoinOption.length){d.betCoinOption=[];for(var j=0;j<m.betCoinOption.length;++j){if(typeof m.betCoinOption[j]==="number")d.betCoinOption[j]=o.longs===String?String(m.betCoinOption[j]):m.betCoinOption[j];else d.betCoinOption[j]=o.longs===String?$util.Long.prototype.toString.call(m.betCoinOption[j]):o.longs===Number?new $util.LongBits(m.betCoinOption[j].low>>>0,m.betCoinOption[j].high>>>0).toNumber(true):m.betCoinOption[j]}}return d};SetGameOptionResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SetGameOptionResp}();humanboy_proto.SendExpressionReq=function(){function SendExpressionReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SendExpressionReq.prototype.id=0;SendExpressionReq.prototype.toUid=0;SendExpressionReq.create=function create(properties){return new SendExpressionReq(properties)};SendExpressionReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.id!=null&&Object.hasOwnProperty.call(m,"id"))w.uint32(8).int32(m.id);if(m.toUid!=null&&Object.hasOwnProperty.call(m,"toUid"))w.uint32(16).uint32(m.toUid);return w};SendExpressionReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SendExpressionReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.SendExpressionReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.id=r.int32();break;case 2:m.toUid=r.uint32();break;default:r.skipType(t&7);break}}return m};SendExpressionReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SendExpressionReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.id!=null&&m.hasOwnProperty("id")){if(!$util.isInteger(m.id))return"id: integer expected"}if(m.toUid!=null&&m.hasOwnProperty("toUid")){if(!$util.isInteger(m.toUid))return"toUid: integer expected"}return null};SendExpressionReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.SendExpressionReq)return d;var m=new $root.humanboy_proto.SendExpressionReq;if(d.id!=null){m.id=d.id|0}if(d.toUid!=null){m.toUid=d.toUid>>>0}return m};SendExpressionReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.id=0;d.toUid=0}if(m.id!=null&&m.hasOwnProperty("id")){d.id=m.id}if(m.toUid!=null&&m.hasOwnProperty("toUid")){d.toUid=m.toUid}return d};SendExpressionReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SendExpressionReq}();humanboy_proto.SendExpressionResp=function(){function SendExpressionResp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SendExpressionResp.prototype.code=0;SendExpressionResp.prototype.coin=$util.Long?$util.Long.fromBits(0,0,false):0;SendExpressionResp.create=function create(properties){return new SendExpressionResp(properties)};SendExpressionResp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.coin!=null&&Object.hasOwnProperty.call(m,"coin"))w.uint32(16).int64(m.coin);return w};SendExpressionResp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SendExpressionResp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.SendExpressionResp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.coin=r.int64();break;default:r.skipType(t&7);break}}return m};SendExpressionResp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SendExpressionResp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.coin!=null&&m.hasOwnProperty("coin")){if(!$util.isInteger(m.coin)&&!(m.coin&&$util.isInteger(m.coin.low)&&$util.isInteger(m.coin.high)))return"coin: integer|Long expected"}return null};SendExpressionResp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.SendExpressionResp)return d;var m=new $root.humanboy_proto.SendExpressionResp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.coin!=null){if($util.Long)(m.coin=$util.Long.fromValue(d.coin)).unsigned=false;else if(typeof d.coin==="string")m.coin=parseInt(d.coin,10);else if(typeof d.coin==="number")m.coin=d.coin;else if(typeof d.coin==="object")m.coin=new $util.LongBits(d.coin.low>>>0,d.coin.high>>>0).toNumber()}return m};SendExpressionResp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;if($util.Long){var n=new $util.Long(0,0,false);d.coin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.coin=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.coin!=null&&m.hasOwnProperty("coin")){if(typeof m.coin==="number")d.coin=o.longs===String?String(m.coin):m.coin;else d.coin=o.longs===String?$util.Long.prototype.toString.call(m.coin):o.longs===Number?new $util.LongBits(m.coin.low>>>0,m.coin.high>>>0).toNumber():m.coin}return d};SendExpressionResp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SendExpressionResp}();humanboy_proto.SendExpressionNotify=function(){function SendExpressionNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}SendExpressionNotify.prototype.uid=0;SendExpressionNotify.prototype.toUid=0;SendExpressionNotify.prototype.coin=$util.Long?$util.Long.fromBits(0,0,false):0;SendExpressionNotify.create=function create(properties){return new SendExpressionNotify(properties)};SendExpressionNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.uid!=null&&Object.hasOwnProperty.call(m,"uid"))w.uint32(8).uint32(m.uid);if(m.toUid!=null&&Object.hasOwnProperty.call(m,"toUid"))w.uint32(16).uint32(m.toUid);if(m.coin!=null&&Object.hasOwnProperty.call(m,"coin"))w.uint32(24).int64(m.coin);return w};SendExpressionNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};SendExpressionNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.SendExpressionNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.uid=r.uint32();break;case 2:m.toUid=r.uint32();break;case 3:m.coin=r.int64();break;default:r.skipType(t&7);break}}return m};SendExpressionNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};SendExpressionNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.uid!=null&&m.hasOwnProperty("uid")){if(!$util.isInteger(m.uid))return"uid: integer expected"}if(m.toUid!=null&&m.hasOwnProperty("toUid")){if(!$util.isInteger(m.toUid))return"toUid: integer expected"}if(m.coin!=null&&m.hasOwnProperty("coin")){if(!$util.isInteger(m.coin)&&!(m.coin&&$util.isInteger(m.coin.low)&&$util.isInteger(m.coin.high)))return"coin: integer|Long expected"}return null};SendExpressionNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.SendExpressionNotify)return d;var m=new $root.humanboy_proto.SendExpressionNotify;if(d.uid!=null){m.uid=d.uid>>>0}if(d.toUid!=null){m.toUid=d.toUid>>>0}if(d.coin!=null){if($util.Long)(m.coin=$util.Long.fromValue(d.coin)).unsigned=false;else if(typeof d.coin==="string")m.coin=parseInt(d.coin,10);else if(typeof d.coin==="number")m.coin=d.coin;else if(typeof d.coin==="object")m.coin=new $util.LongBits(d.coin.low>>>0,d.coin.high>>>0).toNumber()}return m};SendExpressionNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.uid=0;d.toUid=0;if($util.Long){var n=new $util.Long(0,0,false);d.coin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.coin=o.longs===String?"0":0}if(m.uid!=null&&m.hasOwnProperty("uid")){d.uid=m.uid}if(m.toUid!=null&&m.hasOwnProperty("toUid")){d.toUid=m.toUid}if(m.coin!=null&&m.hasOwnProperty("coin")){if(typeof m.coin==="number")d.coin=o.longs===String?String(m.coin):m.coin;else d.coin=o.longs===String?$util.Long.prototype.toString.call(m.coin):o.longs===Number?new $util.LongBits(m.coin.low>>>0,m.coin.high>>>0).toNumber():m.coin}return d};SendExpressionNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return SendExpressionNotify}();humanboy_proto.AdvanceAutoBetReq=function(){function AdvanceAutoBetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetReq.create=function create(properties){return new AdvanceAutoBetReq(properties)};AdvanceAutoBetReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};AdvanceAutoBetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AdvanceAutoBetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};AdvanceAutoBetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};AdvanceAutoBetReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AdvanceAutoBetReq)return d;return new $root.humanboy_proto.AdvanceAutoBetReq};AdvanceAutoBetReq.toObject=function toObject(){return{}};AdvanceAutoBetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetReq}();humanboy_proto.AdvanceAutoBetRsp=function(){function AdvanceAutoBetRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetRsp.prototype.code=0;AdvanceAutoBetRsp.prototype.usedAutoBetCount=0;AdvanceAutoBetRsp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;AdvanceAutoBetRsp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;AdvanceAutoBetRsp.prototype.bill=null;AdvanceAutoBetRsp.create=function create(properties){return new AdvanceAutoBetRsp(properties)};AdvanceAutoBetRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.usedAutoBetCount!=null&&Object.hasOwnProperty.call(m,"usedAutoBetCount"))w.uint32(16).int32(m.usedAutoBetCount);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);if(m.bill!=null&&Object.hasOwnProperty.call(m,"bill"))$root.humanboy_proto.BillInfo.encode(m.bill,w.uint32(42).fork()).ldelim();return w};AdvanceAutoBetRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AdvanceAutoBetRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.usedAutoBetCount=r.int32();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;case 5:m.bill=$root.humanboy_proto.BillInfo.decode(r,r.uint32());break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){if(!$util.isInteger(m.usedAutoBetCount))return"usedAutoBetCount: integer expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}if(m.bill!=null&&m.hasOwnProperty("bill")){{var e=$root.humanboy_proto.BillInfo.verify(m.bill);if(e)return"bill."+e}}return null};AdvanceAutoBetRsp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AdvanceAutoBetRsp)return d;var m=new $root.humanboy_proto.AdvanceAutoBetRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.usedAutoBetCount!=null){m.usedAutoBetCount=d.usedAutoBetCount|0}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}if(d.bill!=null){if(typeof d.bill!=="object")throw TypeError(".humanboy_proto.AdvanceAutoBetRsp.bill: object expected");m.bill=$root.humanboy_proto.BillInfo.fromObject(d.bill)}return m};AdvanceAutoBetRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.usedAutoBetCount=0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0;d.bill=null}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){d.usedAutoBetCount=m.usedAutoBetCount}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}if(m.bill!=null&&m.hasOwnProperty("bill")){d.bill=$root.humanboy_proto.BillInfo.toObject(m.bill,o)}return d};AdvanceAutoBetRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetRsp}();humanboy_proto.CancelAdvanceAutoBetReq=function(){function CancelAdvanceAutoBetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CancelAdvanceAutoBetReq.create=function create(properties){return new CancelAdvanceAutoBetReq(properties)};CancelAdvanceAutoBetReq.encode=function encode(m,w){if(!w)w=$Writer.create();return w};CancelAdvanceAutoBetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CancelAdvanceAutoBetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.CancelAdvanceAutoBetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){default:r.skipType(t&7);break}}return m};CancelAdvanceAutoBetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CancelAdvanceAutoBetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";return null};CancelAdvanceAutoBetReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.CancelAdvanceAutoBetReq)return d;return new $root.humanboy_proto.CancelAdvanceAutoBetReq};CancelAdvanceAutoBetReq.toObject=function toObject(){return{}};CancelAdvanceAutoBetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CancelAdvanceAutoBetReq}();humanboy_proto.CancelAdvanceAutoBetRsp=function(){function CancelAdvanceAutoBetRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}CancelAdvanceAutoBetRsp.prototype.code=0;CancelAdvanceAutoBetRsp.prototype.is_manual=false;CancelAdvanceAutoBetRsp.create=function create(properties){return new CancelAdvanceAutoBetRsp(properties)};CancelAdvanceAutoBetRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.is_manual!=null&&Object.hasOwnProperty.call(m,"is_manual"))w.uint32(16).bool(m.is_manual);return w};CancelAdvanceAutoBetRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};CancelAdvanceAutoBetRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.CancelAdvanceAutoBetRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.is_manual=r.bool();break;default:r.skipType(t&7);break}}return m};CancelAdvanceAutoBetRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};CancelAdvanceAutoBetRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.is_manual!=null&&m.hasOwnProperty("is_manual")){if(typeof m.is_manual!=="boolean")return"is_manual: boolean expected"}return null};CancelAdvanceAutoBetRsp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.CancelAdvanceAutoBetRsp)return d;var m=new $root.humanboy_proto.CancelAdvanceAutoBetRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.is_manual!=null){m.is_manual=Boolean(d.is_manual)}return m};CancelAdvanceAutoBetRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.is_manual=false}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.is_manual!=null&&m.hasOwnProperty("is_manual")){d.is_manual=m.is_manual}return d};CancelAdvanceAutoBetRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return CancelAdvanceAutoBetRsp}();humanboy_proto.AdvanceAutoBetSetReq=function(){function AdvanceAutoBetSetReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetSetReq.prototype.count=0;AdvanceAutoBetSetReq.create=function create(properties){return new AdvanceAutoBetSetReq(properties)};AdvanceAutoBetSetReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.count!=null&&Object.hasOwnProperty.call(m,"count"))w.uint32(8).int32(m.count);return w};AdvanceAutoBetSetReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetSetReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AdvanceAutoBetSetReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.count=r.int32();break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetSetReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetSetReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.count!=null&&m.hasOwnProperty("count")){if(!$util.isInteger(m.count))return"count: integer expected"}return null};AdvanceAutoBetSetReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AdvanceAutoBetSetReq)return d;var m=new $root.humanboy_proto.AdvanceAutoBetSetReq;if(d.count!=null){m.count=d.count|0}return m};AdvanceAutoBetSetReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.count=0}if(m.count!=null&&m.hasOwnProperty("count")){d.count=m.count}return d};AdvanceAutoBetSetReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetSetReq}();humanboy_proto.AdvanceAutoBetSetRsp=function(){function AdvanceAutoBetSetRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetSetRsp.prototype.code=0;AdvanceAutoBetSetRsp.prototype.count=0;AdvanceAutoBetSetRsp.prototype.CalmDownLeftSeconds=$util.Long?$util.Long.fromBits(0,0,false):0;AdvanceAutoBetSetRsp.prototype.CalmDownDeadLineTimeStamp=$util.Long?$util.Long.fromBits(0,0,false):0;AdvanceAutoBetSetRsp.create=function create(properties){return new AdvanceAutoBetSetRsp(properties)};AdvanceAutoBetSetRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.count!=null&&Object.hasOwnProperty.call(m,"count"))w.uint32(16).int32(m.count);if(m.CalmDownLeftSeconds!=null&&Object.hasOwnProperty.call(m,"CalmDownLeftSeconds"))w.uint32(24).int64(m.CalmDownLeftSeconds);if(m.CalmDownDeadLineTimeStamp!=null&&Object.hasOwnProperty.call(m,"CalmDownDeadLineTimeStamp"))w.uint32(32).int64(m.CalmDownDeadLineTimeStamp);return w};AdvanceAutoBetSetRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetSetRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AdvanceAutoBetSetRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.count=r.int32();break;case 3:m.CalmDownLeftSeconds=r.int64();break;case 4:m.CalmDownDeadLineTimeStamp=r.int64();break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetSetRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetSetRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.count!=null&&m.hasOwnProperty("count")){if(!$util.isInteger(m.count))return"count: integer expected"}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(!$util.isInteger(m.CalmDownLeftSeconds)&&!(m.CalmDownLeftSeconds&&$util.isInteger(m.CalmDownLeftSeconds.low)&&$util.isInteger(m.CalmDownLeftSeconds.high)))return"CalmDownLeftSeconds: integer|Long expected"}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(!$util.isInteger(m.CalmDownDeadLineTimeStamp)&&!(m.CalmDownDeadLineTimeStamp&&$util.isInteger(m.CalmDownDeadLineTimeStamp.low)&&$util.isInteger(m.CalmDownDeadLineTimeStamp.high)))return"CalmDownDeadLineTimeStamp: integer|Long expected"}return null};AdvanceAutoBetSetRsp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AdvanceAutoBetSetRsp)return d;var m=new $root.humanboy_proto.AdvanceAutoBetSetRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.count!=null){m.count=d.count|0}if(d.CalmDownLeftSeconds!=null){if($util.Long)(m.CalmDownLeftSeconds=$util.Long.fromValue(d.CalmDownLeftSeconds)).unsigned=false;else if(typeof d.CalmDownLeftSeconds==="string")m.CalmDownLeftSeconds=parseInt(d.CalmDownLeftSeconds,10);else if(typeof d.CalmDownLeftSeconds==="number")m.CalmDownLeftSeconds=d.CalmDownLeftSeconds;else if(typeof d.CalmDownLeftSeconds==="object")m.CalmDownLeftSeconds=new $util.LongBits(d.CalmDownLeftSeconds.low>>>0,d.CalmDownLeftSeconds.high>>>0).toNumber()}if(d.CalmDownDeadLineTimeStamp!=null){if($util.Long)(m.CalmDownDeadLineTimeStamp=$util.Long.fromValue(d.CalmDownDeadLineTimeStamp)).unsigned=false;else if(typeof d.CalmDownDeadLineTimeStamp==="string")m.CalmDownDeadLineTimeStamp=parseInt(d.CalmDownDeadLineTimeStamp,10);else if(typeof d.CalmDownDeadLineTimeStamp==="number")m.CalmDownDeadLineTimeStamp=d.CalmDownDeadLineTimeStamp;else if(typeof d.CalmDownDeadLineTimeStamp==="object")m.CalmDownDeadLineTimeStamp=new $util.LongBits(d.CalmDownDeadLineTimeStamp.low>>>0,d.CalmDownDeadLineTimeStamp.high>>>0).toNumber()}return m};AdvanceAutoBetSetRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.count=0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownLeftSeconds=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownLeftSeconds=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.CalmDownDeadLineTimeStamp=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.CalmDownDeadLineTimeStamp=o.longs===String?"0":0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.count!=null&&m.hasOwnProperty("count")){d.count=m.count}if(m.CalmDownLeftSeconds!=null&&m.hasOwnProperty("CalmDownLeftSeconds")){if(typeof m.CalmDownLeftSeconds==="number")d.CalmDownLeftSeconds=o.longs===String?String(m.CalmDownLeftSeconds):m.CalmDownLeftSeconds;else d.CalmDownLeftSeconds=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownLeftSeconds):o.longs===Number?new $util.LongBits(m.CalmDownLeftSeconds.low>>>0,m.CalmDownLeftSeconds.high>>>0).toNumber():m.CalmDownLeftSeconds}if(m.CalmDownDeadLineTimeStamp!=null&&m.hasOwnProperty("CalmDownDeadLineTimeStamp")){if(typeof m.CalmDownDeadLineTimeStamp==="number")d.CalmDownDeadLineTimeStamp=o.longs===String?String(m.CalmDownDeadLineTimeStamp):m.CalmDownDeadLineTimeStamp;else d.CalmDownDeadLineTimeStamp=o.longs===String?$util.Long.prototype.toString.call(m.CalmDownDeadLineTimeStamp):o.longs===Number?new $util.LongBits(m.CalmDownDeadLineTimeStamp.low>>>0,m.CalmDownDeadLineTimeStamp.high>>>0).toNumber():m.CalmDownDeadLineTimeStamp}return d};AdvanceAutoBetSetRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetSetRsp}();humanboy_proto.UserPointsChangeNotice=function(){function UserPointsChangeNotice(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}UserPointsChangeNotice.prototype.change_points=$util.Long?$util.Long.fromBits(0,0,false):0;UserPointsChangeNotice.create=function create(properties){return new UserPointsChangeNotice(properties)};UserPointsChangeNotice.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.change_points!=null&&Object.hasOwnProperty.call(m,"change_points"))w.uint32(8).int64(m.change_points);return w};UserPointsChangeNotice.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};UserPointsChangeNotice.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.UserPointsChangeNotice;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.change_points=r.int64();break;default:r.skipType(t&7);break}}return m};UserPointsChangeNotice.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};UserPointsChangeNotice.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.change_points!=null&&m.hasOwnProperty("change_points")){if(!$util.isInteger(m.change_points)&&!(m.change_points&&$util.isInteger(m.change_points.low)&&$util.isInteger(m.change_points.high)))return"change_points: integer|Long expected"}return null};UserPointsChangeNotice.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.UserPointsChangeNotice)return d;var m=new $root.humanboy_proto.UserPointsChangeNotice;if(d.change_points!=null){if($util.Long)(m.change_points=$util.Long.fromValue(d.change_points)).unsigned=false;else if(typeof d.change_points==="string")m.change_points=parseInt(d.change_points,10);else if(typeof d.change_points==="number")m.change_points=d.change_points;else if(typeof d.change_points==="object")m.change_points=new $util.LongBits(d.change_points.low>>>0,d.change_points.high>>>0).toNumber()}return m};UserPointsChangeNotice.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.change_points=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.change_points=o.longs===String?"0":0}if(m.change_points!=null&&m.hasOwnProperty("change_points")){if(typeof m.change_points==="number")d.change_points=o.longs===String?String(m.change_points):m.change_points;else d.change_points=o.longs===String?$util.Long.prototype.toString.call(m.change_points):o.longs===Number?new $util.LongBits(m.change_points.low>>>0,m.change_points.high>>>0).toNumber():m.change_points}return d};UserPointsChangeNotice.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return UserPointsChangeNotice}();humanboy_proto.AdvanceAutoBetAddReq=function(){function AdvanceAutoBetAddReq(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetAddReq.prototype.count=0;AdvanceAutoBetAddReq.create=function create(properties){return new AdvanceAutoBetAddReq(properties)};AdvanceAutoBetAddReq.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.count!=null&&Object.hasOwnProperty.call(m,"count"))w.uint32(8).int32(m.count);return w};AdvanceAutoBetAddReq.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetAddReq.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AdvanceAutoBetAddReq;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.count=r.int32();break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetAddReq.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetAddReq.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.count!=null&&m.hasOwnProperty("count")){if(!$util.isInteger(m.count))return"count: integer expected"}return null};AdvanceAutoBetAddReq.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AdvanceAutoBetAddReq)return d;var m=new $root.humanboy_proto.AdvanceAutoBetAddReq;if(d.count!=null){m.count=d.count|0}return m};AdvanceAutoBetAddReq.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.count=0}if(m.count!=null&&m.hasOwnProperty("count")){d.count=m.count}return d};AdvanceAutoBetAddReq.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetAddReq}();humanboy_proto.AdvanceAutoBetAddRsp=function(){function AdvanceAutoBetAddRsp(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}AdvanceAutoBetAddRsp.prototype.code=0;AdvanceAutoBetAddRsp.prototype.autoBetCount=0;AdvanceAutoBetAddRsp.prototype.usedAutoBetCount=0;AdvanceAutoBetAddRsp.prototype.numberHandAdded=0;AdvanceAutoBetAddRsp.create=function create(properties){return new AdvanceAutoBetAddRsp(properties)};AdvanceAutoBetAddRsp.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.code!=null&&Object.hasOwnProperty.call(m,"code"))w.uint32(8).int32(m.code);if(m.autoBetCount!=null&&Object.hasOwnProperty.call(m,"autoBetCount"))w.uint32(16).int32(m.autoBetCount);if(m.usedAutoBetCount!=null&&Object.hasOwnProperty.call(m,"usedAutoBetCount"))w.uint32(24).int32(m.usedAutoBetCount);if(m.numberHandAdded!=null&&Object.hasOwnProperty.call(m,"numberHandAdded"))w.uint32(32).int32(m.numberHandAdded);return w};AdvanceAutoBetAddRsp.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};AdvanceAutoBetAddRsp.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.AdvanceAutoBetAddRsp;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.code=r.int32();break;case 2:m.autoBetCount=r.int32();break;case 3:m.usedAutoBetCount=r.int32();break;case 4:m.numberHandAdded=r.int32();break;default:r.skipType(t&7);break}}return m};AdvanceAutoBetAddRsp.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};AdvanceAutoBetAddRsp.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.code!=null&&m.hasOwnProperty("code")){switch(m.code){default:return"code: enum value expected";case 0:case 1:case 41e3:case 41001:case 41002:case 41003:case 41004:case 41005:case 41006:case 41007:case 41008:case 41009:case 41010:case 41011:case 41012:case 41013:case 41014:case 41015:case 41016:case 41017:case 41018:case 41019:case 41020:case 41021:case 41022:case 41023:case 41024:case 41025:case 41026:case 41027:case 41028:case 41029:case 41034:case 41030:case 41031:case 41032:case 41033:case 31117:case 31118:break}}if(m.autoBetCount!=null&&m.hasOwnProperty("autoBetCount")){if(!$util.isInteger(m.autoBetCount))return"autoBetCount: integer expected"}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){if(!$util.isInteger(m.usedAutoBetCount))return"usedAutoBetCount: integer expected"}if(m.numberHandAdded!=null&&m.hasOwnProperty("numberHandAdded")){if(!$util.isInteger(m.numberHandAdded))return"numberHandAdded: integer expected"}return null};AdvanceAutoBetAddRsp.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.AdvanceAutoBetAddRsp)return d;var m=new $root.humanboy_proto.AdvanceAutoBetAddRsp;switch(d.code){case"ErrorCode_DUMMY":case 0:m.code=0;break;case"OK":case 1:m.code=1;break;case"ROOM_WITHOUT_YOU":case 41e3:m.code=41e3;break;case"LOW_VERSION":case 41001:m.code=41001;break;case"INVALID_TOKEN":case 41002:m.code=41002;break;case"SERVER_BUSY":case 41003:m.code=41003;break;case"WITHOUT_LOGIN":case 41004:m.code=41004;break;case"ROOM_NOT_MATCH":case 41005:m.code=41005;break;case"ROOM_NOT_EXIST":case 41006:m.code=41006;break;case"BET_EXCEED_LIMIT":case 41007:m.code=41007;break;case"ROOM_PLAYER_LIMIT":case 41008:m.code=41008;break;case"NO_BET":case 41009:m.code=41009;break;case"BET_AMOUNT_NOT_MATCH":case 41010:m.code=41010;break;case"NO_MONEY":case 41011:m.code=41011;break;case"BET_BAD_PARAM":case 41012:m.code=41012;break;case"STOP_SERVICE":case 41013:m.code=41013;break;case"NOT_BET_WHEN_AUTO_BET":case 41014:m.code=41014;break;case"BET_TOO_SMALL":case 41015:m.code=41015;break;case"BET_COUNT_LIMIT":case 41016:m.code=41016;break;case"AUTO_BET_LIMIT":case 41017:m.code=41017;break;case"TOO_MANY_PEOPLE":case 41018:m.code=41018;break;case"NO_UP_DEALER":case 41019:m.code=41019;break;case"STOCK_NUM_EXCEED":case 41020:m.code=41020;break;case"NO_MONEY_TO_DEALER":case 41021:m.code=41021;break;case"NOT_A_DEALER":case 41022:m.code=41022;break;case"NOT_IN_APPLY":case 41023:m.code=41023;break;case"DEALER_NO_BET":case 41024:m.code=41024;break;case"BAD_REQ_PARAM":case 41025:m.code=41025;break;case"NO_SET_ADVANCE_AUTO_BET":case 41026:m.code=41026;break;case"AUTO_BET_COUNT_LIMIT":case 41027:m.code=41027;break;case"AUTO_BET_NO_MONEY":case 41028:m.code=41028;break;case"AUTO_BET_EXCEED_LIMIT":case 41029:m.code=41029;break;case"REACH_LIMIT_BET":case 41034:m.code=41034;break;case"ROOM_SYSTEM_FORCE_CLOSED":case 41030:m.code=41030;break;case"CAN_NOT_LEAVE_IN_BETTING":case 41031:m.code=41031;break;case"CAN_NOT_LEAVE_IN_DEALER":case 41032:m.code=41032;break;case"IN_CALM_DOWN":case 41033:m.code=41033;break;case"C2CPAYMENT_LIST_GET_ERROR":case 31117:m.code=31117;break;case"C2CPAYMENT_NOT_ALLOW":case 31118:m.code=31118;break}if(d.autoBetCount!=null){m.autoBetCount=d.autoBetCount|0}if(d.usedAutoBetCount!=null){m.usedAutoBetCount=d.usedAutoBetCount|0}if(d.numberHandAdded!=null){m.numberHandAdded=d.numberHandAdded|0}return m};AdvanceAutoBetAddRsp.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){d.code=o.enums===String?"ErrorCode_DUMMY":0;d.autoBetCount=0;d.usedAutoBetCount=0;d.numberHandAdded=0}if(m.code!=null&&m.hasOwnProperty("code")){d.code=o.enums===String?$root.humanboy_proto.ErrorCode[m.code]:m.code}if(m.autoBetCount!=null&&m.hasOwnProperty("autoBetCount")){d.autoBetCount=m.autoBetCount}if(m.usedAutoBetCount!=null&&m.hasOwnProperty("usedAutoBetCount")){d.usedAutoBetCount=m.usedAutoBetCount}if(m.numberHandAdded!=null&&m.hasOwnProperty("numberHandAdded")){d.numberHandAdded=m.numberHandAdded}return d};AdvanceAutoBetAddRsp.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return AdvanceAutoBetAddRsp}();humanboy_proto.LeftGameCoinNotify=function(){function LeftGameCoinNotify(p){if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)this[ks[i]]=p[ks[i]]}LeftGameCoinNotify.prototype.cur_game_coin=$util.Long?$util.Long.fromBits(0,0,false):0;LeftGameCoinNotify.prototype.lost_game_coin=$util.Long?$util.Long.fromBits(0,0,false):0;LeftGameCoinNotify.create=function create(properties){return new LeftGameCoinNotify(properties)};LeftGameCoinNotify.encode=function encode(m,w){if(!w)w=$Writer.create();if(m.cur_game_coin!=null&&Object.hasOwnProperty.call(m,"cur_game_coin"))w.uint32(8).int64(m.cur_game_coin);if(m.lost_game_coin!=null&&Object.hasOwnProperty.call(m,"lost_game_coin"))w.uint32(16).int64(m.lost_game_coin);return w};LeftGameCoinNotify.encodeDelimited=function encodeDelimited(message,writer){return this.encode(message,writer).ldelim()};LeftGameCoinNotify.decode=function decode(r,l){if(!(r instanceof $Reader))r=$Reader.create(r);var c=l===undefined?r.len:r.pos+l,m=new $root.humanboy_proto.LeftGameCoinNotify;while(r.pos<c){var t=r.uint32();switch(t>>>3){case 1:m.cur_game_coin=r.int64();break;case 2:m.lost_game_coin=r.int64();break;default:r.skipType(t&7);break}}return m};LeftGameCoinNotify.decodeDelimited=function decodeDelimited(reader){if(!(reader instanceof $Reader))reader=new $Reader(reader);return this.decode(reader,reader.uint32())};LeftGameCoinNotify.verify=function verify(m){if(typeof m!=="object"||m===null)return"object expected";if(m.cur_game_coin!=null&&m.hasOwnProperty("cur_game_coin")){if(!$util.isInteger(m.cur_game_coin)&&!(m.cur_game_coin&&$util.isInteger(m.cur_game_coin.low)&&$util.isInteger(m.cur_game_coin.high)))return"cur_game_coin: integer|Long expected"}if(m.lost_game_coin!=null&&m.hasOwnProperty("lost_game_coin")){if(!$util.isInteger(m.lost_game_coin)&&!(m.lost_game_coin&&$util.isInteger(m.lost_game_coin.low)&&$util.isInteger(m.lost_game_coin.high)))return"lost_game_coin: integer|Long expected"}return null};LeftGameCoinNotify.fromObject=function fromObject(d){if(d instanceof $root.humanboy_proto.LeftGameCoinNotify)return d;var m=new $root.humanboy_proto.LeftGameCoinNotify;if(d.cur_game_coin!=null){if($util.Long)(m.cur_game_coin=$util.Long.fromValue(d.cur_game_coin)).unsigned=false;else if(typeof d.cur_game_coin==="string")m.cur_game_coin=parseInt(d.cur_game_coin,10);else if(typeof d.cur_game_coin==="number")m.cur_game_coin=d.cur_game_coin;else if(typeof d.cur_game_coin==="object")m.cur_game_coin=new $util.LongBits(d.cur_game_coin.low>>>0,d.cur_game_coin.high>>>0).toNumber()}if(d.lost_game_coin!=null){if($util.Long)(m.lost_game_coin=$util.Long.fromValue(d.lost_game_coin)).unsigned=false;else if(typeof d.lost_game_coin==="string")m.lost_game_coin=parseInt(d.lost_game_coin,10);else if(typeof d.lost_game_coin==="number")m.lost_game_coin=d.lost_game_coin;else if(typeof d.lost_game_coin==="object")m.lost_game_coin=new $util.LongBits(d.lost_game_coin.low>>>0,d.lost_game_coin.high>>>0).toNumber()}return m};LeftGameCoinNotify.toObject=function toObject(m,o){if(!o)o={};var d={};if(o.defaults){if($util.Long){var n=new $util.Long(0,0,false);d.cur_game_coin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.cur_game_coin=o.longs===String?"0":0;if($util.Long){var n=new $util.Long(0,0,false);d.lost_game_coin=o.longs===String?n.toString():o.longs===Number?n.toNumber():n}else d.lost_game_coin=o.longs===String?"0":0}if(m.cur_game_coin!=null&&m.hasOwnProperty("cur_game_coin")){if(typeof m.cur_game_coin==="number")d.cur_game_coin=o.longs===String?String(m.cur_game_coin):m.cur_game_coin;else d.cur_game_coin=o.longs===String?$util.Long.prototype.toString.call(m.cur_game_coin):o.longs===Number?new $util.LongBits(m.cur_game_coin.low>>>0,m.cur_game_coin.high>>>0).toNumber():m.cur_game_coin}if(m.lost_game_coin!=null&&m.hasOwnProperty("lost_game_coin")){if(typeof m.lost_game_coin==="number")d.lost_game_coin=o.longs===String?String(m.lost_game_coin):m.lost_game_coin;else d.lost_game_coin=o.longs===String?$util.Long.prototype.toString.call(m.lost_game_coin):o.longs===Number?new $util.LongBits(m.lost_game_coin.low>>>0,m.lost_game_coin.high>>>0).toNumber():m.lost_game_coin}return d};LeftGameCoinNotify.prototype.toJSON=function toJSON(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)};return LeftGameCoinNotify}();return humanboy_proto}();module.exports=$root;