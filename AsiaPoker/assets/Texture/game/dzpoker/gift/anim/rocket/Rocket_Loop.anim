{"__type__": "cc.AnimationClip", "_name": "Rocket_Loop", "_objFlags": 0, "_native": "", "_duration": 0.8333333333333334, "sample": 60, "speed": 1, "wrapMode": 2, "curveData": {"props": {"opacity": [{"frame": 0, "value": 255}]}, "paths": {"Node_BigHitScale_Rocket/Node_Pivot/Rocket_Fire": {"comps": {}, "props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1}}, {"frame": 0.8333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_BigHitScale_Rocket/Node_Pivot/Rocket_Particle_Fire": {"comps": {}, "props": {"active": [{"frame": 0, "value": true}]}}, "Node_BigHitScale_Rocket/Node_Pivot": {"comps": {}, "props": {"position": [{"frame": 0, "value": [-13.5, 5.3]}, {"frame": 0.08333333333333333, "value": [-15.714, 3.603, 0]}, {"frame": 0.16666666666666666, "value": [-10.827, 6.186, 0]}, {"frame": 0.25, "value": [-15.333, 6.682, 0]}, {"frame": 0.3333333333333333, "value": [-12.4, 3.372, 0]}, {"frame": 0.4166666666666667, "value": [-15.764, 5.259, 0]}, {"frame": 0.5, "value": [-11.801, 4.521, 0]}, {"frame": 0.5833333333333334, "value": [-13.185, 7.299, 0]}, {"frame": 0.6666666666666666, "value": [-13.921, 2.721, 0]}, {"frame": 0.75, "value": [-14.051, 7.175, 0]}, {"frame": 0.8333333333333334, "value": [-13.5, 5.3]}], "opacity": [{"frame": 0, "value": 255}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "sprite_Outline_Glow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "Node_Particles_GiftEdge/Node_Particle_Edge1": {"comps": {}, "props": {"position": [{"frame": 0, "value": [296, -124, 0]}], "active": [{"frame": 0, "value": true}]}}, "Node_Particles_GiftEdge/Node_Particle_Edge2": {"comps": {}, "props": {"active": [{"frame": 0, "value": true}], "position": [{"frame": 0, "value": [-296, 124, 0]}]}}, "Node_BigHitScale_Rocket/Node_Pivot/Node_Window/windowGlare": {"props": {"position": [{"frame": 0, "value": [-65, -45, 0]}, {"frame": 0.8333333333333334, "value": [65.716, 46.528, 0]}]}}, "sprite_BGTint": {"props": {"opacity": [{"frame": 0, "value": 128}]}}, "Rocket_Particle_Stars": {"comps": {"cc.ParticleSystem": {"duration": [{"frame": 0, "value": -1}]}}}, "Rocket_Particle_StarsTrails": {"comps": {"cc.ParticleSystem": {"duration": [{"frame": 0, "value": -1}]}}}, "Node_BigHitScale_Rocket/Node_BigHitScale_Value": {"props": {"opacity": [{"frame": 0, "value": 255}]}}}}, "events": []}