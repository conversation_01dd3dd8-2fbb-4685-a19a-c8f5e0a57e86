{"__type__": "cc.AnimationClip", "_name": "Lamb<PERSON>ghini_Start", "_objFlags": 0, "_native": "", "_duration": 1.5, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"sprite_BGTint": {"props": {"opacity": [{"frame": 0, "value": 128}]}}, "sprite_Outline_Glow": {"props": {"opacity": [{"frame": 0.8333333333333334, "value": 0}, {"frame": 1, "value": 255}, {"frame": 1.1, "value": 255}, {"frame": 1.5, "value": 0}]}}, "Node_Particles_GiftEdge/Node_Particle_Edge1": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.6666666666666666, "value": true}], "position": [{"frame": 0.6666666666666666, "value": [-296, 124, 0]}, {"frame": 1.0833333333333333, "value": [296, 124, 0], "curve": "cubicOut"}, {"frame": 1.4166666666666667, "value": [296, -124, 0]}]}}, "Node_Particles_GiftEdge/Node_Particle_Edge2": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.6666666666666666, "value": true}], "position": [{"frame": 0.6666666666666666, "value": [296, -124, 0]}, {"frame": 1.0833333333333333, "value": [-296, -124, 0], "curve": "cubicOut"}, {"frame": 1.4166666666666667, "value": [-296, 124, 0]}]}}, "Node_BigHitScale_Lamborghini/Node_Pivot": {"props": {"position": [{"frame": 0, "value": [126, -27, 0], "curve": "cubicOut"}], "scale": [{"frame": 0.6666666666666666, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 1, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 1.1333333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1.03}}, {"frame": 1.25, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 1.3833333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1.03}}, {"frame": 1.5, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}], "opacity": [{"frame": 0, "value": 255}]}}, "Node_BigHitScale_Lamborghini/Node_Pivot/Lamborghini_Particle_Smoke": {"props": {"active": [{"frame": 0, "value": false}]}, "comps": {"cc.ParticleSystem": {"positionType": [{"frame": 0, "value": 2}]}}}, "Node_BigHitScale_Lamborghini/Node_BigHitScale_Value": {"props": {"opacity": [{"frame": 0, "value": 255}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Rocket_Particle_SpeedLine": {"comps": {"cc.ParticleSystem": {"duration": [{"frame": 0, "value": -1}], "positionType": [{"frame": 0, "value": 2}]}}}, "Rocket_Particle_SpeedLine2": {"comps": {"cc.ParticleSystem": {"duration": [{"frame": 0, "value": -1}], "positionType": [{"frame": 0, "value": 2}]}}}, "Lamborghini_Particle_SmokeStream": {"comps": {"cc.ParticleSystem": {"duration": [{"frame": 0, "value": -1}], "positionType": [{"frame": 0, "value": 2}]}}, "props": {"active": [{"frame": 0, "value": false}, {"frame": 0.6666666666666666, "value": true}]}}}, "props": {"opacity": [{"frame": 0.6666666666666666, "value": 0}, {"frame": 1, "value": 255}]}}, "events": []}