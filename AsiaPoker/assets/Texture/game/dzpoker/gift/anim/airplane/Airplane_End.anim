{"__type__": "cc.AnimationClip", "_name": "Airplane_End", "_objFlags": 0, "_native": "", "_duration": 1.1666666666666667, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"Node_BigHitScale_Airplane/Node_Pivot/Airplane/AirplaneTrail": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}, {"frame": 0.3333333333333333, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.5166666666666667, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}, {"frame": 0.6666666666666666, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.8333333333333334, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}]}}, "Node_BigHitScale_Airplane/Node_Pivot/Airplane/AirplaneTrail_2": {"comps": {}, "props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}, {"frame": 0.36666666666666664, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.5, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}, {"frame": 0.7, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.8333333333333334, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}]}}, "Node_BigHitScale_Airplane/Node_Pivot/Airplane/AirplaneTrail_3": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.18333333333333332, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}, {"frame": 0.3333333333333333, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.4666666666666667, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}, {"frame": 0.6666666666666666, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.8333333333333334, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}]}}, "Node_BigHitScale_Airplane/Node_Pivot/Airplane/AirplaneTrail_4": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.15, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}, {"frame": 0.3333333333333333, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.5, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}, {"frame": 0.6333333333333333, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1}}, {"frame": 0.8333333333333334, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.6}}]}}, "sprite_BGTint": {"props": {"opacity": [{"frame": 0, "value": 128}, {"frame": 0.25, "value": 0}]}}, "sprite_Outline_Glow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "Node_Particles_GiftEdge/Node_Particle_Edge1": {"props": {"active": [{"frame": 0, "value": true}], "position": [{"frame": 0, "value": [296, -124, 0]}]}}, "Node_Particles_GiftEdge/Node_Particle_Edge2": {"props": {"active": [{"frame": 0, "value": true}], "position": [{"frame": 0, "value": [-296, 124, 0]}]}}, "Node_BigHitScale_Airplane/Node_Pivot": {"props": {"position": [{"frame": 0, "value": [0.1, 0, 0]}, {"frame": 0.8333333333333334, "value": [-1041.226, 609.261, 0]}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}], "opacity": [{"frame": 0.3333333333333333, "value": 255}, {"frame": 0.5833333333333334, "value": 0}]}}, "Node_BigHitScale_Airplane/Node_BigHitScale_Value": {"props": {"opacity": [{"frame": 0.5833333333333334, "value": 255}, {"frame": 0.8333333333333334, "value": 0}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "curve": "cubicOut"}, {"frame": 0.8333333333333334, "value": {"__type__": "cc.Vec2", "x": 1.6, "y": 1.6}}]}}, "Node_BigHitScale_Airplane/Node_Pivot/Airplane/Rocket_Particle_Wind": {"comps": {"cc.ParticleSystem": {"duration": [{"frame": 0, "value": 1}], "emissionRate": [{"frame": 0, "value": 100}, {"frame": 0.8333333333333334, "value": 0}]}}}, "Node_BigHitScale_Airplane/Node_Pivot/Airplane/Rocket_Particle_Wind2": {"comps": {"cc.ParticleSystem": {"duration": [{"frame": 0, "value": 1}], "emissionRate": [{"frame": 0.3333333333333333, "value": 100}, {"frame": 0.8333333333333334, "value": 0}]}}}}, "props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 1, "value": 255}, {"frame": 1.1666666666666667, "value": 0}]}}, "events": []}