<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>01.png</key>
            <dict>
                <key>frame</key>
                <string>{{366,340},{136,136}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{31,31},{136,136}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>02.png</key>
            <dict>
                <key>frame</key>
                <string>{{366,194},{144,144}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{27,27},{144,144}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>03.png</key>
            <dict>
                <key>frame</key>
                <string>{{350,478},{154,154}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,22},{154,154}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>04.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,550},{164,162}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{17,18},{164,162}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>05.png</key>
            <dict>
                <key>frame</key>
                <string>{{176,376},{172,172}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{13,13},{172,172}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>06.png</key>
            <dict>
                <key>frame</key>
                <string>{{184,194},{180,180}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{9,9},{180,180}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>07.png</key>
            <dict>
                <key>frame</key>
                <string>{{194,2},{190,190}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{4,4},{190,190}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>08.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,376},{172,172}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{13,13},{172,172}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>09.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,194},{180,180}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{9,9},{180,180}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>10.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{190,190}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{4,4},{190,190}}</string>
                <key>sourceSize</key>
                <string>{198,198}</string>
            </dict>
            <key>di.png</key>
            <dict>
                <key>frame</key>
                <string>{{386,2},{176,47}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{176,47}}</string>
                <key>sourceSize</key>
                <string>{176,47}</string>
            </dict>
            <key>di2.png</key>
            <dict>
                <key>frame</key>
                <string>{{435,2},{128,35}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{128,35}}</string>
                <key>sourceSize</key>
                <string>{128,35}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>winAmount.png</string>
            <key>size</key>
            <string>{512,1024}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:919dfad9cc6583d9f0e12cf7df3becd6$</string>
            <key>textureFileName</key>
            <string>winAmount.png</string>
        </dict>
    </dict>
</plist>
