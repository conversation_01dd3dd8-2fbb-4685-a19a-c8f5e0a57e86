<?xml version="1.0" encoding="utf-8"?>
<plist version="1.0"><dict><key>angle</key>
<real>0.000000</real>
<key>angleVariance</key>
<real>0.000000</real>
<key>duration</key>
<real>-1.000000</real>
<key>startParticleSize</key>
<real>33.000000</real>
<key>startParticleSizeVariance</key>
<real>64.000000</real>
<key>finishParticleSize</key>
<real>9.000000</real>
<key>finishParticleSizeVariance</key>
<real>0.000000</real>
<key>gravityx</key>
<real>0.000000</real>
<key>gravityy</key>
<real>0.000000</real>
<key>maxParticles</key>
<real>10.000000</real>
<key>maxRadius</key>
<real>100.000000</real>
<key>maxRadiusVariance</key>
<real>0.000000</real>
<key>minRadius</key>
<real>0.000000</real>
<key>particleLifespan</key>
<real>0.400000</real>
<key>particleLifespanVariance</key>
<real>1.100000</real>
<key>rotatePerSecond</key>
<real>0.000000</real>
<key>rotatePerSecondVariance</key>
<real>0.000000</real>
<key>rotationEnd</key>
<real>0.000000</real>
<key>rotationEndVariance</key>
<real>0.000000</real>
<key>rotationStart</key>
<real>0.000000</real>
<key>rotationStartVariance</key>
<real>360.000000</real>
<key>sourcePositionVariancex</key>
<real>0.000000</real>
<key>sourcePositionVariancey</key>
<real>0.000000</real>
<key>sourcePositionx</key>
<real>159.000000</real>
<key>sourcePositiony</key>
<real>255.000000</real>
<key>speed</key>
<real>0.000000</real>
<key>speedVariance</key>
<real>0.000000</real>
<key>startColorAlpha</key>
<real>1.000000</real>
<key>startColorBlue</key>
<real>1.000000</real>
<key>startColorGreen</key>
<real>1.000000</real>
<key>startColorRed</key>
<real>1.000000</real>
<key>startColorVarianceAlpha</key>
<real>0.000000</real>
<key>startColorVarianceBlue</key>
<real>0.000000</real>
<key>startColorVarianceGreen</key>
<real>0.000000</real>
<key>startColorVarianceRed</key>
<real>0.000000</real>
<key>finishColorAlpha</key>
<real>0.000000</real>
<key>finishColorBlue</key>
<real>1.000000</real>
<key>finishColorGreen</key>
<real>1.000000</real>
<key>finishColorRed</key>
<real>1.000000</real>
<key>finishColorVarianceAlpha</key>
<real>0.000000</real>
<key>finishColorVarianceBlue</key>
<real>0.000000</real>
<key>finishColorVarianceGreen</key>
<real>0.000000</real>
<key>finishColorVarianceRed</key>
<real>0.000000</real>
<key>tangentialAccelVariance</key>
<real>0.000000</real>
<key>tangentialAcceleration</key>
<real>0.000000</real>
<key>radialAccelVariance</key>
<real>0.000000</real>
<key>radialAcceleration</key>
<real>0.000000</real>
<key>blendFuncSource</key>
<integer>1</integer>
<key>blendFuncDestination</key>
<integer>1</integer>
<key>emitterType</key>
<real>0</real>
<key>textureFileName</key>
<string>xingxing.png</string>
<key>textureImageData</key>
<string>eJwBggV9+olQTkcNChoKAAAADUlIRFIAAABAAAAAQAgCAAAAJQvmiQAAAANzQklUCAgI2+FP4AAAAAlwSFlzAAAWJQAAFiUBSVIk8AAABSVJREFUaIHtWluP0zgYjS+5NEnbpNMpIAaNVAmE+sKM5v//BB54YwQPCAQlUtopTadJ7Djh4ahWdthdrbY22mrnPERuo/rz8Xe+i9M4ziMe8R8GIYQQYtUEszs7Y5xzx3G6rrNkglqaF+Cc+77PmMVtskiAEOJ5XhiGruvaE5JFApRS3/eDIHBd16IVe1MTQnzfD8OQc36SHgCBIAgotelni1NTOhgMBoOB1UxqnYDv+13XnWQapZQGQeB5nnOidQAEfN8/VQkRQlzXRRE4vSxECKGUuq7red4JZyHP81zXZYydqgc8z0MvhI82DFmZFKJnjHmeFwTBSVZiRDAKmeu69jhw4zPq8PV9P4qiKIp83+eco5y1bWvWnGECfemjEYITUA26rpNSmi1qJgkQQjjn2HucBOCBMAzDMNRB3DSNQT8YIwDRY+kowKPRaDwex3E8Go3iOEYuIoQIIYQQpjiYIYC978smiqI0Tcfj8Wg0mk6nSZLoZEopbdvWlJbMEEDGhPRBYDgcTqfTyWSSpunFxUWWZZRSXQ2UUl3XNU1zPAcDBCilnHOoH/qJ4/j8/Pzy8nI2m6Vp+urVqzzPnUN9aNu2aRpwwPUY68c+L+gXLCQc7P3Lly9vbm5ev36dpinnvKqq/X4vpWzbVinVHnCkdWMEgiAYDofD4TBJktlsNp/Pr6+vr66unj59ioB2HKeuayxdXzHAJPpqkoCOOQwYY4wx9wDIPQzDOI7TNJ3NZhcXF/P5fLFYXF1dvXnz5sWLF4PBAEUNmRTnG3YAYgbBg2oN4C454O+5/eGG/g1mcXuADUhFB6smkCRJmqZnZ2fn5+fPnj178uTJZDLpH+ebpimKYrVaZVmWZdlqtfrx40dRFLvd7v7+fr/f39/fl2VZlqUQoqqq+gAhhJRSStk0jY6cftg8DGKdEOH64IDBYKBXjHF4QBzHw+EQBSuO41+fAimldrvdZrPZ7/eMsfF47HneZDIpyxKrBwGMy7Lc7Xb4EpFTVVVZluCDZqRP4KEHHMdBPtFXeINzrlXUl5NO/1EUwRXT6fTy8nKxWMzn89Fo1HXdZrP58OHD7e3t169fV6sV1lcdIKUUQugNVkphoK99D6CK/6UHcAO/1OLT+sPdvgcRIZxzpM4kSZIkOTs7Wy6Xm82maZrFYsEY+/Lly7t3725vb5fLZZ7nm82mKApoBktHTGPaB+dPba4/6K/5T+rAP38K0rYtjGEX4XHP85bLpVIqDMPZbBZF0adPnz5+/Pj9+/ftdguRQCF1XUsplVKY6t8VBANptO8rXaqklK7rPn/+XCn19u3b9+/fr9froigQuKCht/+YWnZsJUY1hSuQcNEjSCm/ffuW57kQ4vPnz1mWIeE8WP2R1g0QcA6SE0LAFdj+qqryPF+v11LKLMvyPEeWROCaWr1jsJ1WSgkh9KCqqvV6vd1uu667u7u7u7uD4tFLm1q9Y/ZAg+5SKSWlrOt6u90WRUEI2W63u91OZ8MjRf8Aho+Uus+hlCLbMMYg+n7zYxDmD/UIYkopIoExprON8dU7NggA0BII2Fu9Y4+A4zggwDm3oRwNix5o27auaxCwZMWx6gEQYIzZ237HKoGu606egBDCNgG7rxo0TSOEsPcHmfMbPIDm1J4Vu0HcNA1j7IQ9IKWklJ4wAfQUJywhRPCpEkAdQBNqz4rdXqiuazzjsGfFroT2+z0hxOD561fYfekPcWzw/5hfYZeAfhZtNZNaxG94b/QRj/i/4ydNYPFMdhkABwAAAABJRU5ErkJggsDTrXY=</string>
</dict></plist>
