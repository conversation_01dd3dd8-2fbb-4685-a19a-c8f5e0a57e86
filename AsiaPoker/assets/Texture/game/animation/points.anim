{"__type__": "cc.AnimationClip", "_name": "points", "_objFlags": 0, "_native": "", "_duration": 2.3333333333333335, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"UI1": {"props": {"position": [{"frame": 0.3, "value": [-2, 29, 0]}, {"frame": 0.31666666666666665, "value": [-2, 29, 0]}, {"frame": 0.48333333333333334, "value": [-2, 29, 0]}, {"frame": 0.5333333333333333, "value": [-2, 29, 0]}, {"frame": 0.5833333333333334, "value": [-2, 29, 0]}, {"frame": 0.6333333333333333, "value": [-2, 29, 0]}, {"frame": 0.6833333333333333, "value": [-2, 29, 0]}, {"frame": 0.7666666666666667, "value": [-2, 29, 0]}, {"frame": 1.15, "value": [-2, 85, 0]}, {"frame": 2, "value": [-2, 85, 0]}, {"frame": 2.3333333333333335, "value": [-2, 85, 0]}], "opacity": [{"frame": 0.3, "value": 0}, {"frame": 0.31666666666666665, "value": 255}, {"frame": 0.48333333333333334, "value": 255}, {"frame": 0.5333333333333333, "value": 255}, {"frame": 0.5833333333333334, "value": 255}, {"frame": 0.6333333333333333, "value": 255}, {"frame": 0.6833333333333333, "value": 255}, {"frame": 0.7666666666666667, "value": 255}, {"frame": 1.15, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.3333333333333335, "value": 0}], "angle": [{"frame": 0.3, "value": 0}, {"frame": 0.31666666666666665, "value": 0}, {"frame": 0.48333333333333334, "value": 0}, {"frame": 0.5333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 0}, {"frame": 0.6333333333333333, "value": 0}, {"frame": 0.6833333333333333, "value": 0}, {"frame": 0.7666666666666667, "value": 0}, {"frame": 1.15, "value": 0}, {"frame": 2, "value": 0}, {"frame": 2.3333333333333335, "value": 0}], "scale": [{"frame": 0.3, "value": {"__type__": "cc.Vec2", "x": 0.1, "y": 0.1}}, {"frame": 0.31666666666666665, "value": {"__type__": "cc.Vec2", "x": 0.1, "y": 0.1}}, {"frame": 0.48333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.5333333333333333, "value": {"__type__": "cc.Vec2", "x": 1.1, "y": 1.1}}, {"frame": 0.5833333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.6333333333333333, "value": {"__type__": "cc.Vec2", "x": 1.05, "y": 1.05}}, {"frame": 0.6833333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.7666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 1.15, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 2, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 2.3333333333333335, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "quan_1": {"props": {"position": [{"frame": 0, "value": [-1, 2, 0]}, {"frame": 0.016666666666666666, "value": [-1, 2, 0]}, {"frame": 0.18333333333333332, "value": [-1, 2, 0]}, {"frame": 0.2, "value": [-1, 2, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.016666666666666666, "value": 0}, {"frame": 0.18333333333333332, "value": 255}, {"frame": 0.2, "value": 0}], "angle": [{"frame": 0, "value": 0}, {"frame": 0.016666666666666666, "value": 0}, {"frame": 0.18333333333333332, "value": 0}, {"frame": 0.2, "value": 0}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1.5, "y": 1.5}}, {"frame": 0.016666666666666666, "value": {"__type__": "cc.Vec2", "x": 2, "y": 2}}, {"frame": 0.18333333333333332, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.2, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "quan_2": {"props": {"position": [{"frame": 0.11666666666666667, "value": [-1, 2, 0]}, {"frame": 0.13333333333333333, "value": [-1, 2, 0]}, {"frame": 0.3, "value": [-1, 2, 0]}, {"frame": 0.31666666666666665, "value": [-1, 2, 0]}], "opacity": [{"frame": 0.11666666666666667, "value": 0}, {"frame": 0.13333333333333333, "value": 0}, {"frame": 0.3, "value": 255}, {"frame": 0.31666666666666665, "value": 0}], "scale": [{"frame": 0.11666666666666667, "value": {"__type__": "cc.Vec2", "x": 1.5, "y": 1.5}}, {"frame": 0.13333333333333333, "value": {"__type__": "cc.Vec2", "x": 2, "y": 2}}, {"frame": 0.3, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.31666666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "quan_3": {"props": {"position": [{"frame": 0.3, "value": [-1, 2, 0]}, {"frame": 0.31666666666666665, "value": [-1, 2, 0]}, {"frame": 0.6, "value": [-1, 2, 0]}], "opacity": [{"frame": 0.3, "value": 0}, {"frame": 0.31666666666666665, "value": 255}, {"frame": 0.6, "value": 0}], "scale": [{"frame": 0.3, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.31666666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.6, "value": {"__type__": "cc.Vec2", "x": 2, "y": 2}}]}}, "quan_4": {"props": {"position": [{"frame": 0.4666666666666667, "value": [-1, 2, 0]}, {"frame": 0.48333333333333334, "value": [-1, 2, 0]}, {"frame": 0.75, "value": [-1, 2, 0]}], "opacity": [{"frame": 0.4666666666666667, "value": 0}, {"frame": 0.48333333333333334, "value": 255}, {"frame": 0.75, "value": 0}], "scale": [{"frame": 0.4666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.48333333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.75, "value": {"__type__": "cc.Vec2", "x": 2, "y": 2}}]}}, "quan_5": {"props": {"position": [{"frame": 0.6166666666666667, "value": [-1, 2, 0]}, {"frame": 0.6333333333333333, "value": [-1, 2, 0]}, {"frame": 0.9, "value": [-1, 2, 0]}], "scale": [{"frame": 0.6166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.6333333333333333, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.9, "value": {"__type__": "cc.Vec2", "x": 2, "y": 2}}], "opacity": [{"frame": 0.6166666666666667, "value": 0}, {"frame": 0.6333333333333333, "value": 255}, {"frame": 0.9, "value": 0}]}}}}, "events": []}