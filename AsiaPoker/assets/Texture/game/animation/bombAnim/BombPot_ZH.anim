{"__type__": "cc.AnimationClip", "_name": "BombPot_ZH", "_objFlags": 0, "_native": "", "_duration": 2.5, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"Node_Center/sprite_Lights": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.4166666666666667, "value": {"__type__": "cc.Vec2", "x": 1.2, "y": 1.2}}, {"frame": 1, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.6666666666666666, "value": 128}], "angle": [{"frame": 0, "value": 0}, {"frame": 2.5, "value": 41}]}}, "Node_Center/sprite_Lights_FX": {"props": {"scale": [{"frame": 0.08333333333333333, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": "cubicOut"}, {"frame": 0.5, "value": {"__type__": "cc.Vec2", "x": 1.3, "y": 1.3}}], "opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.5, "value": 60}, {"frame": 1, "value": 128}], "angle": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 2.5, "value": -42}], "color": [{"frame": 1, "value": {"__type__": "cc.Color", "r": 217, "g": 85, "b": 250, "a": 255}}, {"frame": 1.5, "value": {"__type__": "cc.Color", "r": 250, "g": 204, "b": 85, "a": 255}}]}}, "Node_Center/sprite_Stars_01": {"props": {"scale": [{"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": "sineOut"}, {"frame": 0.4166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Center/sprite_Stars_02": {"props": {"scale": [{"frame": 0.21666666666666667, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": "sineOut"}, {"frame": 0.5, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Center/sprite_GoldCirlce": {"props": {"opacity": [{"frame": 0.16666666666666666, "value": 0, "curve": "linear"}, {"frame": 0.25, "value": 255}], "scale": [{"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 0.2, "y": 0.25}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.4166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Center/sprite_Stars_02_FX": {"comps": {}, "props": {"opacity": [{"frame": 0.48333333333333334, "value": 255, "curve": "cubicOut"}, {"frame": 0.6666666666666666, "value": 0}], "scale": [{"frame": 0.31666666666666665, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.5666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Center/sprite_Stars_01_FX": {"comps": {}, "props": {"opacity": [{"frame": 0.43333333333333335, "value": 255, "curve": "cubicOut"}, {"frame": 0.6166666666666667, "value": 0}], "scale": [{"frame": 0.26666666666666666, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.5166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "Node_Center/sprite_Word": {"props": {"scale": [{"frame": 0.25, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": "cubicOut"}, {"frame": 0.5666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}], "opacity": [{"frame": 0.25, "value": 0}, {"frame": 0.4166666666666667, "value": 255}]}, "comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0, "value": {"__uuid__": "bcf65cfa-e6f7-48a6-8048-e2fade3aae84"}}]}}}, "Node_Center/sprite_Word_FX": {"props": {"scale": [{"frame": 0.25, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": "cubicOut"}, {"frame": 0.6666666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}], "opacity": [{"frame": 0.25, "value": 0}, {"frame": 0.6666666666666666, "value": 255, "curve": "cubicOut"}, {"frame": 1, "value": 0}]}, "comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0, "value": {"__uuid__": "bcf65cfa-e6f7-48a6-8048-e2fade3aae84"}}]}}}, "Node_Center/sprite_Word/sprite_Flare_01": {"comps": {}, "props": {"scale": [{"frame": 0.4166666666666667, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.5833333333333334, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}], "angle": [{"frame": 0.4166666666666667, "value": 0}, {"frame": 2.5, "value": 175}], "position": [{"frame": 0, "value": [-217.426, 56.757, 0]}]}}, "Node_Center/sprite_Word/sprite_Flare_02": {"comps": {}, "props": {"scale": [{"frame": 0.5, "value": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.6666666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}], "angle": [{"frame": 0.5, "value": 0}, {"frame": 2.5, "value": -167}], "position": [{"frame": 0, "value": [217.371, -65.261, 0]}]}}, "Node_Center/Node_Particles/New Particle_Stars": {"comps": {}, "props": {"active": [{"frame": 0.3, "value": false}, {"frame": 0.31666666666666665, "value": true}]}}, "Node_Center/Node_Particles/New Particle_Atom_Big": {"comps": {}, "props": {"active": [{"frame": 0.23333333333333334, "value": false}, {"frame": 0.25, "value": true}]}}, "Node_Center/Node_Particles/New Particle_Atom_Small": {"comps": {}, "props": {"active": [{"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "Node_Center": {"props": {"opacity": [{"frame": 2, "value": 255}, {"frame": 2.5, "value": 0}]}}, "Node_Fire": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.26666666666666666, "value": 255}, {"frame": 2, "value": 255}, {"frame": 2.5, "value": 0}]}}, "Node_Fire/sprite_splash_Glow": {"props": {"color": [{"frame": 0, "value": {"__type__": "cc.Color", "r": 65, "g": 65, "b": 65, "a": 255}}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Color", "r": 194, "g": 173, "b": 137, "a": 255}}, {"frame": 0.3333333333333333, "value": {"__type__": "cc.Color", "r": 136, "g": 104, "b": 47, "a": 255}}, {"frame": 0.5333333333333333, "value": {"__type__": "cc.Color", "r": 109, "g": 83, "b": 38, "a": 255}}, {"frame": 0.75, "value": {"__type__": "cc.Color", "r": 136, "g": 104, "b": 47, "a": 255}}, {"frame": 0.95, "value": {"__type__": "cc.Color", "r": 109, "g": 83, "b": 38, "a": 255}}, {"frame": 1.1666666666666667, "value": {"__type__": "cc.Color", "r": 136, "g": 104, "b": 47, "a": 255}}, {"frame": 1.3833333333333333, "value": {"__type__": "cc.Color", "r": 109, "g": 83, "b": 38, "a": 255}}, {"frame": 1.6, "value": {"__type__": "cc.Color", "r": 136, "g": 104, "b": 47, "a": 255}}, {"frame": 2.5, "value": {"__type__": "cc.Color", "r": 24, "g": 24, "b": 24, "a": 255}}]}}, "sprite_DarkTint": {"props": {"opacity": [{"frame": 2, "value": 128}, {"frame": 2.5, "value": 0}]}}}}, "events": []}