<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>allin.png</key>
            <dict>
                <key>frame</key>
                <string>{{606,329},{148,59}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{148,59}}</string>
                <key>sourceSize</key>
                <string>{148,59}</string>
            </dict>
            <key>bai.png</key>
            <dict>
                <key>frame</key>
                <string>{{674,2},{670,325}}</string>
                <key>offset</key>
                <string>{-2,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{5,5},{670,325}}</string>
                <key>sourceSize</key>
                <string>{684,339}</string>
            </dict>
            <key>d0001.png</key>
            <dict>
                <key>frame</key>
                <string>{{666,1099},{391,215}}</string>
                <key>offset</key>
                <string>{0,1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{33,18},{391,215}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0002.png</key>
            <dict>
                <key>frame</key>
                <string>{{439,627},{397,227}}</string>
                <key>offset</key>
                <string>{0,1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{30,12},{397,227}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0003.png</key>
            <dict>
                <key>frame</key>
                <string>{{243,1587},{411,239}}</string>
                <key>offset</key>
                <string>{-1,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{22,5},{411,239}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0004.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1587},{417,239}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{19,6},{417,239}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0005.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,627},{435,239}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{10,6},{435,239}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0006.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1346},{423,239}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{16,6},{423,239}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0007.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1105},{423,239}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{16,6},{423,239}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0008.png</key>
            <dict>
                <key>frame</key>
                <string>{{668,674},{423,239}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{16,6},{423,239}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0009.png</key>
            <dict>
                <key>frame</key>
                <string>{{429,1026},{425,235}}</string>
                <key>offset</key>
                <string>{-1,4}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{15,5},{425,235}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>d0010.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,868},{425,235}}</string>
                <key>offset</key>
                <string>{-1,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{15,5},{425,235}}</string>
                <key>sourceSize</key>
                <string>{457,253}</string>
            </dict>
            <key>huang.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{670,325}}</string>
                <key>offset</key>
                <string>{-4,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{8,8},{670,325}}</string>
                <key>sourceSize</key>
                <string>{694,349}</string>
            </dict>
            <key>huang2.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,329},{602,296}}</string>
                <key>offset</key>
                <string>{-4,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{7,7},{602,296}}</string>
                <key>sourceSize</key>
                <string>{624,318}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>allinanim.png</string>
            <key>size</key>
            <string>{1024,2048}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:a74cc821068a44d55a6ff9e5a94cb03a$</string>
            <key>textureFileName</key>
            <string>allinanim.png</string>
        </dict>
    </dict>
</plist>
