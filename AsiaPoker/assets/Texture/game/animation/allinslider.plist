<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>guang0001.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,304},{151,226}}</string>
                <key>offset</key>
                <string>{-1,7}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{14,0},{151,226}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0002.png</key>
            <dict>
                <key>frame</key>
                <string>{{228,608},{149,224}}</string>
                <key>offset</key>
                <string>{0,8}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{16,0},{149,224}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0003.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,606},{167,224}}</string>
                <key>offset</key>
                <string>{7,8}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{14,0},{167,224}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0004.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,775},{155,224}}</string>
                <key>offset</key>
                <string>{3,8}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{16,0},{155,224}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0005.png</key>
            <dict>
                <key>frame</key>
                <string>{{159,775},{153,224}}</string>
                <key>offset</key>
                <string>{3,8}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{17,0},{153,224}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0006.png</key>
            <dict>
                <key>frame</key>
                <string>{{230,459},{147,226}}</string>
                <key>offset</key>
                <string>{0,7}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{17,0},{147,226}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0007.png</key>
            <dict>
                <key>frame</key>
                <string>{{230,308},{149,226}}</string>
                <key>offset</key>
                <string>{0,7}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{16,0},{149,226}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0008.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,153},{149,230}}</string>
                <key>offset</key>
                <string>{0,5}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{16,0},{149,230}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0009.png</key>
            <dict>
                <key>frame</key>
                <string>{{236,2},{155,230}}</string>
                <key>offset</key>
                <string>{-3,5}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{10,0},{155,230}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0010.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{149,232}}</string>
                <key>offset</key>
                <string>{1,4}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{17,0},{149,232}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0011.png</key>
            <dict>
                <key>frame</key>
                <string>{{234,159},{147,230}}</string>
                <key>offset</key>
                <string>{1,5}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{18,0},{147,230}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
            <key>guang0012.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,457},{147,226}}</string>
                <key>offset</key>
                <string>{1,7}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{18,0},{147,226}}</string>
                <key>sourceSize</key>
                <string>{181,240}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>allinslider.png</string>
            <key>size</key>
            <string>{512,1024}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:719e30d807405aa33dc477656929de90$</string>
            <key>textureFileName</key>
            <string>allinslider.png</string>
        </dict>
    </dict>
</plist>
