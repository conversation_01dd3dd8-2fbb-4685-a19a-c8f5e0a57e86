import { EmittableService } from '../core/core-index';
import type { IGetEventStatusResponse, ISocket, IEventStatusClient, Rebate } from '../poker-client/poker-client-index';
import { ErrorMessageService } from './error-message-service';
import * as core from '../core/core';

export interface RebateEvents {
    eventStatusResult: (result: IEventStatusClient) => void;
    eventStatusStop: () => void;
    rebateRewardResult: (result: Rebate.IRebateNoticeMessage) => void;
    refreshEventStatus: () => void;
    eventLeaderboardResult: (result: Rebate.IGetLeaderboardResponse) => void;
}

export class RebateService extends EmittableService<RebateEvents> {
    static readonly serviceName = 'RebateService';

    private _socket: ISocket;

    private _rebateEvents: IEventStatusClient[] = [];
    get rebateEvents() {
        return this._rebateEvents;
    }

    private _rebateEventStatus: IGetEventStatusResponse = null;
    get rebateEventStatus() {
        return this._rebateEventStatus;
    }

    private _rebateLeaderboard: { [key: number]: Rebate.ILeaderboard };
    get rebateLeaderboards() {
        return this._rebateLeaderboard;
    }

    private _preRankChangeNotice: Rebate.IRankChangeNotice = null;
    private _nowRankChangeNotice: Rebate.IRankChangeNotice = null;
    get rankChangeNotice() {
        if (this._preRankChangeNotice && this._nowRankChangeNotice) {
            // display frequency is limited to once every 5 minutes.
            const preTimestamp = this._preRankChangeNotice.timestamp || 0;
            const nowTimestamp = this._nowRankChangeNotice.timestamp || 0;
            if (nowTimestamp - preTimestamp < 5 * 60) return null;
        }

        if (this._nowRankChangeNotice) {
            const { rank_from: rankFrom = 0, rank_to: rankTo = 0, top_n: topN = 0 } = this._nowRankChangeNotice;

            // Check if the new rank is within the top N list
            const hasEnteredTopList = rankTo !== 0 && rankTo <= topN;
            
            // Check if it's a new entry or the rank has improved
            const isNewEntryOrRankUp = rankFrom === 0 || rankTo < rankFrom;
            
            if (hasEnteredTopList && isNewEntryOrRankUp) {
                const data = this._nowRankChangeNotice;
                this._preRankChangeNotice = this._nowRankChangeNotice;
                this._nowRankChangeNotice = null;
                return data;
            }
        }

        return null;
    }

    private _activeEventId: number = 0;
    get activeEventId() {
        return this._activeEventId;
    }   
    set activeEventId(value: number) {
        // If the id changes, clean up the rank change data
        if (this._activeEventId !==0 && this._activeEventId !== value) {
            this.cleanRankChangeData();
        }
        this._activeEventId = value;
    }

    private _errorMessageService: ErrorMessageService = null;

    constructor(socket: ISocket) {
        super(RebateService.serviceName);
        this._socket = socket;
        this._socket.notification.on('rebateEventStatus', this.onEventStatusNotify.bind(this));
        this._socket.notification.on('rebateNotice', this.rebateClaimNotify.bind(this));
        this._socket.notification.on('rebateRankChange', this.rebateRankChange.bind(this));

        this._errorMessageService = core.serviceManager.get(ErrorMessageService);
    }

    async getEventStatus(): Promise<IGetEventStatusResponse> {
        const response = await this._socket.getEventStatus();
        this._rebateEvents = response.events;
        return response;
    }

    async getLeaderBoard(): Promise<Rebate.IGetLeaderboardResponse> {
        const response = await this._socket.getRebateLeaderboard();
        this._rebateLeaderboard = response.leaderboards;
        return response;
    }

    onEventStatusNotify() {
        cc.log('rebate event status notify received');
        this.emit('refreshEventStatus');
    }

    rebateClaimNotify(data) {
        cc.log('rebate claim notify received');
        this.emit('rebateRewardResult', data);
    }

    rebateRankChange(data) {
        cc.log('rebate ranking change received');
        this._nowRankChangeNotice = data;
    }

    cleanRankChangeData() {
        cc.log('clean rank change data');
        this._preRankChangeNotice = null;
        this._nowRankChangeNotice = null;
    }
}
