import {observer, render} from "../common/stores/observer";
import {Holdem_RoomMenu} from './Holdem_RoomMenu_ts';
import {Holdem_PlayerControl} from './Holdem_PlayerControl_ts';
import {Holdem_RoomHolders} from './Holdem_RoomHolders_ts';
import {Holdem_CardsHandler} from './Holdem_CardsHandler_ts';
import {Holdem_Stake} from './Holdem_Stake_ts';
import {Holdem_Player} from './Holdem_Player_ts';
import {Holdem_Icon} from './Holdem_Icon_ts';
import {PlayerLayoutType, PlayerState} from "./Holdem_Player_ts";
import {Holdem_Room_Info} from "./Holdem_Room_Info_ts";
import {CUSTOM_PLAYER_STATE, HOLDEM_TOURNAMENT_TYPE, holdemRoomStore} from "../common/stores/holdemRoom";
import {holdemPlayerStore, holdemSelfStore} from "../common/stores/holdemPlayer";
import {Translate} from "../common/Translator";
import {Translation} from "../common/lang";
import {ProtoBuf} from "../common/net/Pb";
import {holdem_game_records} from "../../prefab/impoker/hall/game_view/game_menu/holdem_game_records/holdem_game_records";
import {buildConfig, globalConfig, localConfig, ppConfig} from "../common/mttconfig";
import Holdem_Room_Info_Alert from "./Holdem_Room_Info_Alert_ts";
import {VoiceRecorder} from "../common/game/VoiceRecorder";
import {Holdem_SelfSetting} from "./Holdem_SelfSetting";
import {VoiceMessagePlayer} from "../../prefab/common/VoiceMessagePlayer";
import {GameEmoticons} from "./GameEmoticons_ts";
import {soundEffect} from "../common/soundEffect";
import {TopUpPage} from "../../prefab/impoker/hall/top_up/TopUpPage";
import {Holdem_GuessHand} from "./Holdem_GuessHand";
import {ResourcesLoader} from "../common/ResourcesLoader";
import {Marquee} from "../../prefab/common/Marquee";
import { AndroidBackButton } from "../common/AndroidBackButton";
import CommonTools from "../common/CommonTools";
import HoldemTournamentDetails
    from "../../prefab/impoker/hall/game_view/game_menu/holdem_game_tournament_records/HoldemTournamentDetails";
import MultipleGame, { MultipleGameType } from "../common/game/MultipleGame";
import LoadingBlocker from "../common/LoadingBlocker";
import BasicScene from "../common/BasicScene";
import MultipleGameScene from "../common/game/MultipleGameScene";
import MTTConnector from "../common/MTTConnector";
import AttackIcons from "../pineapple/player/game_attacks/AttackIcons_ts";
import Holdem_Live from "./Holdem_Live";
import {Holdem_PlayerAvatar} from "./Holdem_PlayerAvatar_ts";
import PlayerSetting from "../pineapple/player/PlayerSetting_ts";
import {Holdem_TableSetting} from "./Holdem_TableSetting";
import {CelebritySelfSetting} from "./CelebritySelfSetting";
import {FormatParser} from "../common/tools/FormatParser";
import VoiceMessageDialog from "../../prefab/common/VoiceMessageDialog";
import { MysteryBountyMain } from "../../../mystery_bounty/Scripts/MysteryBountyMain";

const LanguageManager = require("LanguageManager");
const GameConfig = require("Holdem_GameConfig");


const {ccclass, property} = cc._decorator;

@ccclass
@observer
export class Holdem_Room extends MultipleGameScene {

    static ROOM_PANEL = cc.Enum({
        EMOTICONS: 0,
        SELF_SETTING: 1,
        PLAYER_SETTING: 2,
        RED_POCKET: 3,
        BIG_WIN: 4,
        CELEBRITY_SELF_SETTING: 5,
        TABLE_SETTING: 6
    });

    // static instance:Holdem_Room = null;
    store: holdemRoomStore;
    seats: number;
    
    static getSelfUserId() {
        try {
        if (cc.vv.DataManager.userId !== undefined)
        return cc.vv.DataManager.userId;
        } catch (err) { }
        return 1;
    }

    rebuyLeftTime:number = 0;
    morebuyLeftTime:number = 0;

    isRebuyStage:boolean = false;
    isMorebuyStage:boolean = false;

    @property(cc.Node)
    playersAnchor:cc.Node = null;

    // @property([cc.Node])
    playerNodes:cc.Node[] = [];

    @property([cc.Node])
    playerNodesPos:cc.Node[] = [];

    @property(cc.Sprite)
    bg:cc.Sprite = null;

    @property(cc.Sprite)
    bgTop:cc.Sprite = null;

    @property(cc.Sprite)
    bgTable:cc.Sprite = null;

    // @property(cc.Label)
    // marqueeLabel:cc.Label = null;

    @property(cc.Node)
    marqueeParent:cc.Node = null;

    marqueeNode:cc.Node = null;

    // @property(cc.Prefab)
    // redPocketDialogue:cc.Prefab = null;

    // @property(cc.Node)
    // redPocketNode:cc.Node = null;

    redPocketPrefab:cc.Node = null;

    @property(cc.Node)
    playersHolder:cc.Node = null;

    @property(cc.Node)
    infoPanel:cc.Node = null;
    
    @property(cc.Button)
    menuButton:cc.Button = null;
    
    @property(cc.Button)
    historyButton:cc.Button = null;

    @property(cc.Button)
    tournamentButton:cc.Button = null;
    
    @property(cc.Button)
    emojiButton:cc.Button = null;

    @property(cc.Node)
    gameMicIcon: cc.Node = null;

    @property(cc.Node)
    timeBankBtn:cc.Node = null;
    
    @property(Holdem_RoomMenu)
    menu:Holdem_RoomMenu = null;
    
    @property(cc.Node)
    playerControlContainer:cc.Node = null;
    
    @property(Holdem_PlayerControl)
    playerControl:Holdem_PlayerControl = null;
    
    @property(Holdem_RoomHolders)
    holders:Holdem_RoomHolders = null;
    
    @property(Holdem_CardsHandler)
    publicCardsHandler:Holdem_CardsHandler = null;
    
    @property(Holdem_Stake)
    mainPotStake:Holdem_Stake = null;

    @property(cc.Node)
    sidePotNode:cc.Node = null;
    
    @property(cc.Label)
    potLabel:cc.Label = null;

    @property(cc.Node)
    potBBLabel:cc.Node = null;

    @property(Holdem_Icon)
    iconH:Holdem_Icon = null;

    gameRecord:holdem_game_records = null;

    @property(cc.Prefab)
    gameRecordPrefab:cc.Prefab = null;
    
    @property(cc.Node)
    gameRecordPos:cc.Node = null;

    tournamentRecord:HoldemTournamentDetails = null;

    @property(cc.Prefab)
    tournamentRecordPrefab:cc.Prefab = null;
    
    @property(cc.Node)
    tournamentRecordPos:cc.Node = null;

    @property(cc.Node)
    gameLogo: cc.Node = null;

    @property(cc.Sprite)
    gameIcon:cc.Sprite = null;

    @property(cc.Sprite)
    gameSubIcon:cc.Sprite = null;

    @property(cc.Sprite)
    spinsLogo:cc.Sprite = null;

    @property(cc.Node)
    cardType:cc.Node = null;

    @property(cc.Node)
    waitStart:cc.Node=null;

    @property(cc.Node)
    waitStartHu:cc.Node=null;

    @property
    sidePotList:cc.Node[] = [];

    @property(Holdem_Room_Info_Alert)
    roomInfoAlert:Holdem_Room_Info_Alert = null;

    @property(cc.Prefab)
    holdemPlayerPrefab:cc.Prefab = null;

    @property(cc.Prefab)
    roomInfoPrefab:cc.Prefab = null;

    @property(cc.Node)
    gameReminderParent:cc.Node = null;

    @property(cc.Prefab)
    gameReminderPrefab:cc.Prefab = null;

    @property(cc.Prefab)
    reConnectLoading: cc.Prefab = null;

    _reConnectLoading: cc.Node = null;

    @property(cc.Node)
    roomInfoPos:cc.Node = null;
    
    roomInfo:Holdem_Room_Info = null;

    @property(Holdem_GuessHand)
    guessHand:Holdem_GuessHand = null;

    @property(cc.Node)
    guessHandControl:cc.Node = null;

    _blindTime:number = 0;

    @property(cc.Node)
    mysteryBountyContainer:cc.Node = null;
    mysteryBountyNode:cc.Node = null;
    mysteryBountyMain:MysteryBountyMain = null;

    get blindTime()
    {
        return this._blindTime;
    }

    set blindTime(value)
    {
        this._blindTime = value;
        this.updateBlindTime(this.blindTime);
        // cc.vv.ConsoleLog.log('blindTime', 193, this.store.currentBlindLevel, this.store.rbcList.length);
        if (this.store && this.store.rbcList.length && this.store.currentBlindLevel==this.store.rbcList.length){
            cc.vv.ConsoleLog.log('blindTime', 193, this.store.currentBlindLevel, this.store.rbcList.length);
            if (this.blindTime!==0){
                this.blindTime = 0;
                this.store.updateRestRiseTime(-1);
            }
        }
        this.lastSetBlindTime = new Date().getTime();
        cc.vv.ConsoleLog.log("blindTime", this._blindTime);
    }

    lastSetBlindTime:number = 0;
    _stopBlindTimeCounter:boolean = false;

    get stopBlindTimeCounter()
    {
        return this._stopBlindTimeCounter;
    }

    set stopBlindTimeCounter(value)
    {
        if( this._stopBlindTimeCounter != value )
        {
            if(value)
            {
                let now = new Date().getTime();
                let diff = Math.ceil((now - this.lastSetBlindTime) / 1000);

                // Force set blindTime if timer is pause to save the current time
                this._blindTime = Math.max(this._blindTime - diff, 0);
                cc.vv.ConsoleLog.log("blindTime", this._blindTime);
            }
            else
            {
                // _stopBlindTimeCounter change to false mean start timer so that set lastSetBlindTime to current time,
                // but do not need to change blindTime because it already been set when lastSetBlindTime set to true
                this.lastSetBlindTime = new Date().getTime();
            }
        }


        this._stopBlindTimeCounter = value;

        cc.vv.ConsoleLog.log("stopBlindTimeCounter", this._stopBlindTimeCounter);
    }


    restTime:number = 0;

    isReplay:boolean = false;

    needAnimation:boolean = true;

    sidePotObjList:cc.Node[] = [];

    gameTheme:boolean = false;
    isWPT:boolean = false;
    isXDevice:boolean = false;
    bottomPadH5:boolean = false;
    isSpin:boolean = false;

    @property()
    get pot():any {
        return this._pot;
    }
    set pot(value: any) {
        this._pot = value;

        let str = '';
        if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.HOLDEM_NORMAL){
            str = value.toFixed(2);
        }else{
            if (this.store) {
                const unitStr = FormatParser.GetStackUnit(this.store?.isShortDeck);
                this.potBBLabel.getComponent(cc.Label).string = unitStr;
                this.potBBLabel.active = value > 0 && this.coinMode == 1;
                str = FormatParser.DisplayStack(value, this.store.calcBigBlind, this.store.calcAnte, this.coinMode, this.store.isShortDeck);
            }
            
            // if (value<1001) {
            //     str = Math.floor(value).toString();
            // }else if (value>=1001 && value<1000000){
            //     str = (Math.floor(value/100)/10).toFixed(1) + "k";
            // }else if (value>=1000000 && value<1000000000) {
            //     str = (Math.floor(value/10000)/100).toFixed(2) + "M";
            // }else{
            //     str = (Math.floor(value/10000000)/100).toFixed(2) + "B";
            // }
        }
        this.potLabel.string = Translate(Translation.HOLDEM.POT)+" "+str;
        this.potLabel.node.active = value > 0;
        if (this.isReplay)
        {
            this.potLabel.node.parent.x = 500;
            this.mainPotStake.IconNodeStartPosition = new cc.Vec2(this.potLabel.node.parent.position.x, this.mainPotStake.IconNodeStartPosition.y);
        }
    }

    @property(Holdem_Player)
    _players:Holdem_Player[] = [];

    _pot = 0;

    coinMode:number = 0; // 0:coin, 1: bb
    
    _roomId = 0;

    playerIds:{[seat:number]:number} = {};
    boardCards:number[] = [];

    dealerPos: number;
    straddlePos: number;

    _gameMode:number;
    _gameViewType:number;

    riseOpen:boolean = false;

    get moveCount():number {
        return this._moveCount;
    }
    set moveCount(v:number){
        this._moveCount = v;
    }

    _moveCount:number = 0;

    _dealerFirstDelay:boolean = true;

    hasPopupJoinDialog:boolean = false;
    // @property(cc.Node)
    // endGameBlockerSng: cc.Node = null;
    // @property(cc.Node)
    // readyCountDownBlocker: cc.Node = null;
    // @property(cc.Node)
    // tournamentKO: cc.Node = null;
    @property(cc.Node)
    animationLayer:cc.Node = null;



    @property(cc.Node)
    roomMessageLayer:cc.Node = null;

    @property(cc.Node)
    restMessageLayer:cc.Node = null;

    @property(cc.Node)
    handForHandMessageLayer:cc.Node = null;

    @property(cc.Node)
    waitForTableMessageLayer:cc.Node = null;

    @property(cc.Prefab)
    voiceRecordingMessagePrefab:cc.Prefab = null;

    @property(cc.Node)
    voiceRecordingMessage:cc.Node = null;
    voiceMessageDialog:VoiceMessageDialog = null;

    _voiceRecorder:VoiceRecorder = null;

    @property(VoiceMessagePlayer)
    voiceMessagePlayer:VoiceMessagePlayer = null;

    @property(cc.Node)
    settingContainer:cc.Node = null;

    @property(cc.Node)
    playerSetting:cc.Node = null;
    playerSettingNode:cc.Node = null;

    @property(cc.Node)
    selfSetting:cc.Node = null;
    selfSettingNode:cc.Node = null;

    celebritySelfSettingNode:cc.Node = null;


    @property(cc.Label)
    sendIconNotificationContent:cc.Label = null;

    @property(cc.Node)
    inMoneyNotification:cc.Node = null;

    @property(cc.Label)
    inMoneyNotificationTitle:cc.Label = null;

    @property(cc.Label)
    inMoneyNotificationContent:cc.Label = null;



    @property(cc.Prefab)
    rankingPrefab:cc.Prefab = null;

    // @property(cc.Prefab)
    // attackIconPrefab:cc.Prefab = null;

    @property(cc.Node)
    emoticonsListStartPos:cc.Node = null;

    @property(cc.Node)
    emoticonsListStopPos:cc.Node = null;

    @property(cc.Node)
    emoticonsNode:cc.Node = null;
    emoticonsList:cc.Node = null;

    @property(cc.Prefab)
    topUpPrefab:cc.Prefab = null;

    @property(cc.Node)
    blockLayer:cc.Node = null;

    @property(cc.Node)
    _topUpPage:TopUpPage = null;

    @property([cc.Node])
    position:cc.Node[] = [];

    @property(cc.Node)
    layer:cc.Node = null;

    @property(cc.Prefab)
    loadingPrefab:cc.Prefab = null;
    loading:cc.Node = null;

    @property(cc.Node)
    loadingLayer:cc.Node = null;

    @property(cc.Node)
    gameEndLayer: cc.Node = null;

    @property(cc.Node)
    tableBackNode: cc.Node = null;

    @property
    pageActionSpeed:number = 0.15;
    loadingDelay:number = 0.3;

    @property(Holdem_Live)
    liveHandler: Holdem_Live = null;

    @property(cc.Button)
    liveBtnAnnouncer:cc.Button = null;
    keepAnnouncerSetting:boolean = false;

    // @property(cc.Prefab)
    // bigWin:cc.Prefab = null;
    // bigWinNode:cc.Node = null;
    bigWinPrefab:cc.Node = null;

    dealerPosActionInterval:Function;

    _firstGuessHandPopUp:boolean = false;

    // scheduler
    roomMessageDelay: any = null;   // 3s
    handForHandMessageDelay: any = null; //0.1s
    animationPlayDelay: any = null; // 5s
    koAnimationDelay:any = null;    // 2s
    hideLoadingDelay:any = null;    // 0.3s loadingDelay
    //dealerPosActionInterval       // 0.6s
    handCardDelay:any = null;       // 1.2s
    gameStartDelay:any = null;      // 0.3s GameConfig.GAME_START_DELAY_TIME
    onTurnDelay:any = null;         // 0.2s + 0.1s (GameConfig.PLAYER_STAKE_MOVE_TIME + GameConfig.COLLECT_STAKE_DELAY_TIME)
    onTurnResponseDelay:any = null; // 0.5s + 0.1s (GameConfig.PUBLIC_STAKE_MOVE_TIME + GameConfig.DEAL_PUBLIC_CARDS_DELAY_TIME)

    prefix:string = "Holdem Game";
    onDestroy(){
        this.unscheduleAllCallbacks();
        this.unregisterMsg();
        if (this._voiceRecorder) {
            this._voiceRecorder.unregisterVoiceRecordFinishCallback(this.node);
        }
        this.store.Destroy();
        if(!this.isReplay)
        {
            cc.game.off(cc.game.EVENT_HIDE, this.onAppPause);
            cc.game.off(cc.game.EVENT_SHOW, this.onAppResume);
        }
        // cc.systemEvent.off(cc.SystemEvent.EventType.KEY_UP, this.onKeyUp, this);
        // this.unscheduleAllCallbacks();
        this.audioPlayer.playEffect(soundEffect.PlayerExit, false, 1, ()=>{
            this.destroyBasicScene();
        });
        if( this.sidePotObjList )
        {
            for(let i = 0; i < this.sidePotObjList.length; i++)
            {
                if( this.sidePotObjList[i] )
                {
                    this.sidePotObjList[i].stopAllActions();
                }
            }
        }
        
    }

    onLoad() {
        super.onLoad();
        const {isMtt, gameViewType, currentGameInfo, gameMode, isReplay} = cc.vv.DataManager;
        cc.sys.garbageCollect();
        cc.vv.ConsoleLog.log("MultipleGame.currentMultipleGameIndex", MultipleGame.currentMultipleGameIndex);
        cc.vv.ConsoleLog.log("holdemLive check",this.liveHandler);

        this.initMultipleGame(MultipleGameType.HOLDEM, MultipleGame.currentMultipleGameIndex);
        if(gameViewType==globalConfig.MTT_GAME_MODE.OMAHA){
            // cc.sys.localStorage.setItem(localConfig.key_cardFaceSettingTmp, localConfig.getLocalStorageItem(localConfig.key_cardFaceSetting,0));
            cc.sys.localStorage.setItem(localConfig.key_cardFaceSetting, 0);
            this.playerControl.isOmaha = true;
        }
        // cc.vv.AssetsManager.init();
        // cc.vv.AssetsManager.popupParent = cc.director.getScene().getComponentInChildren(cc.Canvas).node;
        // Holdem_Room.instance = this;
        this.registerMsg();
        this.isReplay = isReplay;
        // cc.vv.ConsoleLog.log("this.isreplay", this.isReplay);
        // init child node
        this.initBasicScene();
        this.CreateChildNode();
        this.setRoomToChild();

        cc.vv.ConsoleLog.log("currentGameInfo", currentGameInfo);
        // set number of seats and blind time
        if (currentGameInfo.Detail == undefined){
            // this.seats = 9; // default 9??
        } else{
            // this.seats = currentGameInfo.Detail.Seats;
            this.stopBlindTimeCounter = true;
        }
        

        let actualGameViewType = gameViewType;
        let actualGameMode = gameMode;
        if( MultipleGame.instance && this.multipleGameInfo )
        {
            actualGameViewType = this.multipleGameInfo.gameMode;
            actualGameMode = this.multipleGameInfo.gameListId;
            this.isWPT = (this.multipleGameInfo.displayTag=="WPT");
        }
        cc.vv.ConsoleLog.log("currentGameInfo gameViewType", gameViewType, actualGameViewType);
        cc.vv.ConsoleLog.log("currentGameInfo gameMode", gameMode, actualGameMode);

        // set game mode
        this._gameMode = gameMode;

        // set game view type
        this._gameViewType = gameViewType;

        // set store
        if(gameMode==globalConfig.GAME_LEVEL_LIST_ID.HOLDEM_NORMAL) {
            this.store = new holdemRoomStore(actualGameViewType, actualGameMode, null, this.node, this, this.isReplay);
            this.tournamentButton.node.active = false;
        }else {
            this.store = new holdemRoomStore(actualGameViewType, actualGameMode, currentGameInfo.Detail, this.node, this, this.isReplay);
        }

        // set player control
        if (gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
            this.playerControl._isAOF = true;
        }
        if (gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG){
            this.playerControl._isSpin = true;
            this.isSpin = true;
        }

        // set self
        this.menu.room = this;

        this.initPrefab();

        if( !this.isReplay )
        {
            this.menuButton.node.on("click", this.onClickMenu.bind(this));
            this.historyButton.node.on("click", this.onClickHistory.bind(this));
            this.tournamentButton.node.on("click", this.onClickTournament.bind(this));
            this.emojiButton.node.on("click", this.onClickEmoji.bind(this));
            this.gameMicIcon.children[0].on(cc.Node.EventType.TOUCH_START, this.onGameMicPressed, this);
            this.gameMicIcon.children[0].on(cc.Node.EventType.TOUCH_END, this.onGameMicReleased, this);
            this.gameMicIcon.children[0].on(cc.Node.EventType.TOUCH_CANCEL, this.onGameMicCanceled, this);
            this.gameMicIcon.active = false;
            this.emojiButton.node.active = false;
            this.guessHandControl.active = false;
            if (this.liveBtnAnnouncer){
                this.liveBtnAnnouncer.node.on("click", this.onClickAnnouncer.bind(this));
                this.liveBtnAnnouncer.node.active = false;
            }

            this.updateWidgetLayout();

            // check not in pkw - 521
            // if (!cc.vv.DataManager.webPlatform && this._gameViewType==globalConfig.MTT_GAME_MODE.NLH) {
            //     this.guessHand.room = this;
            //     if (buildConfig.isPrePro || buildConfig.isPro){
            //         if (buildConfig.isPro){
            //             let whiteList:number[] = [10020, 45765, 10029, 10238, 47406, 131630, 42971];
            //             this.guessHandControl.active = !!whiteList.find(x => x == cc.vv.DataManager.userId);
            //         } else if(buildConfig.isPrePro){
            //             let whiteList:number[] = [934649, 934651, 934653, 934607, 934608, 964609, 934666, 934667, 934669, 944673, 934634];
            //             this.guessHandControl.active = !!whiteList.find(x => x == cc.vv.DataManager.userId);
            //         }
            //     }else{
            //
            //         this.guessHandControl.active = true;
            //
            //     }
            //     this.UpdateGuessHandControl();
            // }

            const value = cc.sys.localStorage.getItem(localConfig.key_enableDisplayBB);
            cc.vv.ConsoleLog.log('check key display_BB_enabled value:', value, value == 1);
            if (value == 1) {
                this.coinMode = 1;
            } else {
                this.coinMode = 0;
            }

        }
        // close window(s) if this room's space area is clicked.
        // this.node.on(cc.Node.EventType.TOUCH_END, () => {
        //     if (cc.isValid(this.emoticonsList)) {
        //         this.emoticonsList.getComponent(GameEmoticons).onClose();
        //     }
        // }, this);

        for(let i = 0; i < 5; i++)
        {
            this.publicCardsHandler.addCard(i);
            // this.publicCardsHandler._cards[i].onLoadInit();
            this.publicCardsHandler._cards[i].sprite.node.active = false;
        }

        // set player node
        this._players = this.playerNodes.map(node => {

            const n = node.getComponent("Holdem_Player_ts") as Holdem_Player;

            n.room = this;
            n.maxCards = this.store.maxHoleCard;

            return n;

        });

        // set player index
        for (let i = 0; i < this._players.length; i++) {
            let player = this._players[i];
            player.seat._index = i;
            if (this._gameMode==4){
                player.node.opacity = 0;
            }
        }

        // update player layout
        this.updatePlayersLayout();

        if (this.isWPT){
            this.WPTUpdateBackground();
        }else{
            this.setBackground();
        }

        this.setGameIcon();

        // init value and delegate
        // this.playerControl.node.active = false;
        this.playerControl.room = this;
        this.mainPotStake.potShow = false;
        this.mainPotStake.value = 0;
        this.pot = 0;

        //this.mttHallInfo = JSON.parse(cc.sys.localStorage.getItem("mttHallInfo"));
        // cc.sys.localStorage.removeItem("mttHallInfo");
        // cc.sys.localStorage.clear();

        if(!this.isReplay)
        {
            // set on app pause resume even
            cc.game.off(cc.game.EVENT_HIDE, this.onAppPause);
            cc.game.off(cc.game.EVENT_SHOW, this.onAppResume);
            cc.game.on(cc.game.EVENT_HIDE, this.onAppPause);
            cc.game.on(cc.game.EVENT_SHOW, this.onAppResume);
        }

        if( this.isReplay )
        {
            for (let i = 0; i < this._players.length; i++) {
                let player = this._players[i];
                player.avatar.node.active = false;
                player.info.node.active = false;
            }
            this.setReplaySpinsLogo();
        }
        // cc.systemEvent.on(cc.SystemEvent.EventType.KEY_UP, this.onKeyUp, this);
        // //to fix all sound cannot be played after appresume with no sound playing, so play a silent mp3 to fix this problem
        // if(cc.vv.DataManager.isWebFeature)
        // {
        //     this.audioPlayer.playEffect(soundEffect.Silent, true);
        // }
        if(MultipleGame.instance?.isEnterFinished){
            this.audioPlayer.playEffect(soundEffect.PlayerEnter);
        }


        // check is iphone x
        // can take out and share to use later
        const isIphonex = () => {
            if (typeof window !== 'undefined' && window) {
                return /iphone/gi.test(window.navigator.userAgent) && window.screen.height >= 812;
            }
            return false;
        };

        // #7608 quick fix
       // if(cc.vv.DataManager.webPlatform == ProtoBuf.commonProto.PLATFORM.PKW){
       //     // pkw set player's anchor when iphone X series model
       //     // check webview ios model
       //      if (!cc.sys.isNative){
       //          if(isIphonex()){
       //              cc.vv.ConsoleLog.log("is iphone x ");
       //              if( this.playersAnchor){
       //                  let widget = this.playersAnchor.getComponent(cc.Widget);
       //                  widget.top = 100;
       //                  widget.updateAlignment();
       //              } else {
       //                  cc.vv.ConsoleLog.log("cannot find player anchor")
       //              }
       //          } else {
       //             cc.vv.ConsoleLog.log("not iphone x ");
       //          }
       //      }
       //  }
    }

    updateGameAlignment()
    {
        this.updateWidgetLayout();
    }

    updateWidgetLayout()
    {
        if(MultipleGame.instance)
        {
            cc.vv.ConsoleLog.log("updateWidgetLayout", MultipleGame.instance.hasShowHeader);
        }
        let scale = 0.85;
        let ratio = cc.view.getFrameSize().height/cc.view.getFrameSize().width;
        if (ratio>2)
        {
            cc.vv.ConsoleLog.log('check device ratio:', ratio);

            scale = 1;
            if (cc.sys.isMobile && !cc.sys.isNative){
                if (MTTConnector.instance.isWPK){ // 18797 h5 殼包
                    this.menuButton.node.getComponent(cc.Widget).top = MTTConnector.instance.xDeviceLayoutYPos.menuButton;
                    this.tournamentButton.getComponent(cc.Widget).top = MTTConnector.instance.xDeviceLayoutYPos.tournamentButton;
                }
                this.bottomPadH5 = true;
            }else{
                if (MultipleGame.instance && MultipleGame.instance.hasShowHeader){
                    ratio = (cc.view.getFrameSize().height-212)/cc.view.getFrameSize().width;
                    if (ratio<2){
                        scale = 0.9;
                    }
                }
                this.isXDevice = true;
                this.updateXDeviceLayout();
            }

        }

        if((!MultipleGame.instance || !MultipleGame.instance.hasShowHeader) && !this.isXDevice)
        {
            if (this.isWPT){
                scale = 0.9;
            }else{
                scale = 1;
                // if (MTTConnector.instance.cv.config.IS_WIDESCREEN){
                //     scale = ratio*0.9;
                // }else{
                //     scale = 1;
                // }
            }
        }

        this.node.scale = scale;
        this.playerControlContainer.scale = scale;
        if(this.mysteryBountyContainer){
            this.mysteryBountyContainer.scale = scale;
            this.mysteryBountyContainer.getComponent(cc.Widget).updateAlignment();
            this.mysteryBountyMain?.updateTableAlignment();
        }
    }

    updateXDeviceLayout(){
        let bottomValue = 57;
        this.gameMicIcon.getComponent(cc.Widget).bottom = bottomValue;
        this.emojiButton.node.getComponent(cc.Widget).bottom = bottomValue;
        this.guessHandControl.getComponent(cc.Widget).bottom = bottomValue;
        this.historyButton.node.getComponent(cc.Widget).bottom = bottomValue;
        this.timeBankBtn.getComponent(cc.Widget).bottom = bottomValue;
        this.menuButton.node.getComponent(cc.Widget).top = MTTConnector.instance.xDeviceLayoutYPos.menuButton;
        this.tournamentButton.getComponent(cc.Widget).top = MTTConnector.instance.xDeviceLayoutYPos.tournamentButton;
        this.addTableButton.getComponent(cc.Widget).top = MTTConnector.instance.xDeviceLayoutYPos.addTableButton;
        this.playerControl.updateXDeviceLayout();
        this.playerNodesPos[0].y = MTTConnector.instance.xDeviceLayoutYPos.playerBottom;
        this.playerNodesPos[1].y = -280;
        this.playerNodesPos[2].y = 170;
        this.playerNodesPos[3].y = 547;
        this.playerNodesPos[4].y = 936;
        this.playerNodesPos[5].y = 936;
        this.playerNodesPos[6].y = 547;
        this.playerNodesPos[7].y = 170;
        this.playerNodesPos[8].y = -280;
        this.cardType.y = (this._gameViewType == globalConfig.MTT_GAME_MODE.OMAHA)?MTTConnector.instance.xDeviceLayoutYPos.cardTypeOmaha:MTTConnector.instance.xDeviceLayoutYPos.cardType;
        for(const i of this._players){
            i.updateXDeviceLayout();
        }
        if(this.mysteryBountyMain){
            this.mysteryBountyMain.updateXDeviceLayout();
        }
        
        let selfPlayerStore = this.store.getSeatedPlayerByUserId(this.store.playerUserId);
        if (selfPlayerStore) {
            let player = this.getHoldemPlayersWithSeatNum(selfPlayerStore.seatNum);
            if (cc.isValid(player)) {
                player.node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[0], cc.Vec2.ZERO)));
            }
        }
    }

    private registerMsg() {
        MTTConnector.instance.registerMessageCenter("mttNotify", this.mttNotify.bind(this), this.node);
        MTTConnector.instance.registerMessageCenter(MTTConnector.instance.config.BroadCast.MYSTERY_BOUNTY_INTRO_COMPLETED, this.onMysteryBountyIntroCompleted.bind(this), this.node);
    }

    private  unregisterMsg() {
        MTTConnector.instance.unregisterMessageCenter("mttNotify", this.node);
        MTTConnector.instance.unregisterMessageCenter(MTTConnector.instance.config.BroadCast.MYSTERY_BOUNTY_INTRO_COMPLETED, this.node);
    }

    onMysteryBountyIntroCompleted()
    {
        cc.vv.ConsoleLog.log("Holdem_Room_ts onMysteryBountyIntroCompleted");

        this.store.isMysteryBountyOnIntro = false;
        if(this.store.restMessage == ProtoBuf.mttPro.RestTime_Type.RestTime_Type_MIDFIELD || this.store.restMessage == ProtoBuf.mttPro.RestTime_Type.RestTime_Type_FINALS){
            this.showRestMessageNodes();
        }
    }

    mttNotify(url:string)
    {
        cc.vv.ConsoleLog.log("mttNotify", url);
        if (url.search("mttjs://back-normal") != -1 || url.search("mttjs://back-abnormal") != -1) {
            if(this.store && cc.isValid(this.node))
            {
                this.store.reboundGameView();
            }
        }
    }

    // setBackground(){
    //     // set BG
    //     let url = "";
    //     let bgColor = 3;
    //     if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG || this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
    //         bgColor = cc.sys.localStorage.getItem(localConfig.key_sngBgColorSetting);
    //         cc.vv.ConsoleLog.log("setBackground sng localStorage" , bgColor);
    //         if(!bgColor){
    //             bgColor = localConfig.BgColor.BG3; // default green sng bg
    //             cc.vv.ConsoleLog.log("setBackground sng default",bgColor);
    //         }
    //     } else {
    //         bgColor = cc.sys.localStorage.getItem(localConfig.key_mttBgColorSetting);
    //         cc.vv.ConsoleLog.log("setBackground mtt localStorage" , bgColor);
    //         if(!bgColor){
    //             bgColor = localConfig.BgColor.BG2; // default red mtt bg
    //             cc.vv.ConsoleLog.log("setBackground mtt default",bgColor);
    //         }
    //     }
    //     url = "BG/BG"+ bgColor;
    //     cc.vv.ConsoleLog.log("setBackground localStorage" , url);
    //     this.loadBackgroundImage(url);
    // }
    _isPlayerCelebrityListSet:boolean=false;
    checkPlayerCelebrityList(){
        if (!this._isPlayerCelebrityListSet){
            this._players.forEach((p)=>{
                p.CelebrityList = this.store.CelebrityList;
                p.updateCelebrityPlayer();
            });
            this._isPlayerCelebrityListSet = true;
        }
    }
    WPTUpdateBackground(){
        this.isWPT = true;
        this.updateGameAlignment();
        this.setBackground();
        MTTConnector.instance.cv.resMgr.adaptWidget(this.node, true);
    }

    getBackgroundURL(logPrefix = "setBackground")
    {
        let bgColor = localConfig.BgColor.BG3;
        if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG || this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
            bgColor = cc.sys.localStorage.getItem(localConfig.key_sngBgColorSetting);
            cc.vv.ConsoleLog.log(logPrefix + " sng localStorage" , bgColor);
            if(!bgColor){
                bgColor = localConfig.BgColor.BG3; // default green sng bg
                cc.vv.ConsoleLog.log(logPrefix + " sng default",bgColor);
            }
        } else {
            if (this.isWPT){
                bgColor = localConfig.getLocalStorageItem(localConfig.key_wptBgColorSetting,0);
            }else{
                bgColor = cc.sys.localStorage.getItem(localConfig.key_mttBgColorSetting);
            }

            cc.vv.ConsoleLog.log(logPrefix + " mtt localStorage" , bgColor);
            if(!bgColor){
                if (this.isWPT){
                    bgColor = localConfig.BgColor.BG_WPT; // default red mtt bg
                }else{
                    bgColor = localConfig.BgColor.BG2; // default red mtt bg
                }
                cc.vv.ConsoleLog.log(logPrefix + " mtt default",bgColor);
            }
            this.updateRoomInfoColor(bgColor);
        }
        this.setBackgroundTheme(bgColor>MTTConnector.instance.backgroundThemeCount);
        this.bgTop.node.active = (bgColor==4);
        return MTTConnector.instance.getHoldemBackgroundData(bgColor);
    }
    setBackgroundTheme(flatUI:boolean){
        // todo update player / playerControl sprite
        if (this.gameTheme!==flatUI){
            this.gameTheme = flatUI;

            this.playerControl.updateTheme(flatUI);

            for (const i of this._players){
                i.updateTheme(flatUI);
            }

            // subMenu
            this.menuButton.node.children[0].active = !flatUI;
            this.historyButton.node.children[0].active = !flatUI;
            this.tournamentButton.node.children[0].active = !flatUI;
            this.emojiButton.node.children[0].active = !flatUI;

            this.menuButton.node.children[1].active = flatUI;
            this.historyButton.node.children[1].active = flatUI;
            this.tournamentButton.node.children[1].active = flatUI;
            this.emojiButton.node.children[1].active = flatUI;
        }
    }
    updateRoomInfoColor(bgColor:number){
        if(this.store.isMysterB)    return;
        cc.vv.ConsoleLog.log('updateRoomInfoColor',bgColor);

        let color = "#000000";
        let op = 0.5;
        if (bgColor<=5){
            color = "#FFFFFF";
            op = 0.4;
        }
        let hexColor = new cc.Color().fromHEX(color);
        this.gameSubIcon.node.color = hexColor;
        this.gameLogo.color = hexColor;
        this.gameIcon.node.color = hexColor;
        this.roomInfo.updateTextColor(color, op);
    }

    releaseBackground(prefab:cc.Texture2D)
    {
        if( cc.isValid(prefab) )
        {
            cc.loader.release(prefab);
        }
    }

    setBackground(){
        // set BG
        let url = this.getBackgroundURL();
        cc.vv.ConsoleLog.log("setBackground localStorage" , url);
        this.loadBackgroundImage(url[0]);
        if (url[1]!==''){
            this.bgTable.node.active = true;
            this.loadBgTableImage(url[1]);
        }else{
            this.bgTable.node.active = false;
        }

        this.changeGameLogoAlpha(url[2]);
    }

    _retryCount:0;
    loadBackgroundImage(url:string){
        cc.vv.ConsoleLog.log("loadBackgroundImage",url);
        // url = (cc.vv.DataManager.webPlatform)?url+'_pkw':url;
        cc.vv.AssetsManager.loadImage(this.bg, url, null, null, null, 4, false);
        // cc.vv.AssetsManager.loadRes(url,cc.Texture2D,(err:any,spriteFrame:any)=>{
        //     try {
        //         if( cc.isValid(this.bg) )
        //         {
        //             if(err){
        //                 cc.vv.ConsoleLog.log("loadBackgroundImage fail",err);
        //                 this._retryCount ++;
        //                 if(this._retryCount < 5){
        //                     this.loadBackgroundImage(url);
        //                 }
        //             } else {
        //                 this._retryCount = 0;
        //                 cc.vv.ConsoleLog.log("loadBackgroundImage success", cc.isValid(this.bg.spriteFrame, true));
        //                 let lastSpriteFrame = null;

        //                 if( cc.isValid(this.bg.spriteFrame) )
        //                 {
        //                     lastSpriteFrame = this.bg.spriteFrame.getTexture();
        //                 }

        //                 if( lastSpriteFrame != spriteFrame )
        //                 {
        //                     this.bg.spriteFrame = new cc.SpriteFrame(spriteFrame);
        //                     this.releaseBackground(lastSpriteFrame);
        //                 }
        //             }
        //         } 
        //     } catch (error) {
        //         cc.vv.ConsoleLog.log("trycatch loadBackgroundImage fail", error);
        //     }
            

        // });
    }

    _retryTableImgCount:0;
    loadBgTableImage(url:string){
        cc.vv.ConsoleLog.log("loadBgTableImage",url);
        // url = (cc.vv.DataManager.webPlatform)?url+'_pkw':url;
        cc.vv.AssetsManager.loadImage(this.bgTable, url, null, null, null, 4, false);
        // cc.vv.AssetsManager.loadRes(url,cc.Texture2D,(err:any,spriteFrame:any)=>{
        //     try {
        //         if( cc.isValid(this.bgTable) )
        //         {
        //             if(err){
        //                 cc.vv.ConsoleLog.log("loadBgTableImage fail",err);
        //                 this._retryTableImgCount ++;
        //                 if(this._retryTableImgCount < 5){
        //                     this.loadBgTableImage(url);
        //                 }
        //             } else {
        //                 this._retryTableImgCount = 0;
        //                 cc.vv.ConsoleLog.log("loadBgTableImage success", cc.isValid(this.bgTable.spriteFrame, true));
        //                 let lastSpriteFrame = null;
        //                 if( cc.isValid(this.bgTable.spriteFrame) )
        //                 {
        //                     lastSpriteFrame = this.bgTable.spriteFrame.getTexture();
        //                 }

        //                 if( lastSpriteFrame != spriteFrame )
        //                 {
        //                     this.bgTable.spriteFrame = new cc.SpriteFrame(spriteFrame);
        //                     this.releaseBackground(lastSpriteFrame);
        //                 }
        //             }
        //         }
        //     } catch (error) {
        //         cc.vv.ConsoleLog.log("trycatch loadBgTableImage fail", error);
        //     }


        // });
    }

    changeGameLogoAlpha(url: string) {
        cc.vv.ConsoleLog.log("changeGameLogoAlpha", url);
        // url = (cc.vv.DataManager.webPlatform) ? url + '_pkw' : url;
        if(cc.isValid(this.gameLogo)) {
            cc.vv.ConsoleLog.log("changeGameLogoAlpha success");
            this.gameLogo.opacity = 255 * parseFloat(url);
        }
    }

    setGameIcon(){
        if(this._gameMode == globalConfig.GAME_LEVEL_LIST_ID.MTT){
            // MTT
            if (this.store && this.store.MTTDetail && (this.store.MTTDetail.TournamentMode==ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER ||this.store.MTTDetail.TournamentMode==ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER)){
                cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.SPRITE.HOLDEM.MODE_HUNTERGAME, cc.SpriteFrame, (err, spriteFrame)=>{
                    this.gameIcon.getComponent(cc.Sprite).spriteFrame = spriteFrame;
                    this.gameIcon.node.opacity = 255;
                })
                // set player hunter number true
                for (const player of this._players) {
                    player.isHunterGame = true;
                }
            }else{
                cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.SPRITE.HOLDEM.MODE_MTT, cc.SpriteFrame, (err, spriteFrame)=>{
                    this.gameIcon.getComponent(cc.Sprite).spriteFrame = spriteFrame;
                    this.gameIcon.node.opacity = 255;
                });
            }
        } else if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG){
            // SNG
            cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.SPRITE.HOLDEM.MODE_SNG, cc.SpriteFrame, (err, spriteFrame)=>{
                this.gameIcon.getComponent(cc.Sprite).spriteFrame = spriteFrame;
                //only show in BL
                if (MTTConnector.instance.isBL) {
                    this.gameIcon.node.opacity = 255;
                }
            });
        } else if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
            // AOF
            cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.SPRITE.HOLDEM.MODE_AOF, cc.SpriteFrame, (err, spriteFrame)=>{
                this.gameIcon.getComponent(cc.Sprite).spriteFrame = spriteFrame;
                this.gameIcon.node.opacity = 255;
            });
        } else { // other Mode
            cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.SPRITE.HOLDEM.MODE_SNG, cc.SpriteFrame, (err, spriteFrame)=>{
                this.gameIcon.getComponent(cc.Sprite).spriteFrame = spriteFrame;
                this.gameIcon.node.opacity = 255;
            })
        }

        // set sub icon
        if(this._gameViewType == globalConfig.MTT_GAME_MODE.OMAHA){
            cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.SPRITE.HOLDEM.MODE_OMAHA, cc.SpriteFrame, (err, spriteFrame)=>{
                this.gameSubIcon.getComponent(cc.Sprite).spriteFrame = spriteFrame;
                this.gameSubIcon.node.opacity = 255;
            });
        } else if(this._gameViewType == globalConfig.MTT_GAME_MODE.SHORT_DECK){
            cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.SPRITE.HOLDEM.MODE_SHORTCARD, cc.SpriteFrame, (err, spriteFrame)=>{
                this.gameSubIcon.getComponent(cc.Sprite).spriteFrame = spriteFrame;
                this.gameSubIcon.node.opacity = 255;
            });
        } else {
            this.gameSubIcon.node.opacity = 0;
        }
    }

    setReplaySpinsLogo(){
        if(cc.vv.DataManager.replayGameResultDetail.TypeId == ProtoBuf.commonProto.GAME_CATEGORY.SNG ) {
            // Show the Spins Logo in SNG Replay
            this.spinsLogo.node.active = true;
            return;
        }
        else {
            this.spinsLogo.node.active = false;
        }
    }

    initPrefab(){
        let temp = null;

        // set loading
        // this.loading = cc.instantiate(this.loadingPrefab);
        // this.loading.parent = this.loadingLayer;

        // set layout setting
        // let playerSettingPos = this.playerSetting.position;
        // this.playerSetting = cc.instantiate(this.playerSettingPrefab);
        // this.playerSetting.parent = this.settingContainer;
        // this.playerSetting.position = playerSettingPos;
        // this.playerSetting.active = false;
        // // set button callback
        // this.playerSetting.getComponent(ppConfig.PlayerSetting).mute.on('click', this.onPlayerSettingMuteClicked, this);
        // this.playerSetting.getComponent(ppConfig.PlayerSetting).soundReplay.node.on('click', this.onPlayerSettingReplayClicked, this);
        //
        //
        // // set self player setting
        // let selfSettingPos = this.selfSetting.position;
        // this.selfSetting = cc.instantiate(this.selfSettingPrefab);
        // this.selfSetting.parent = this.settingContainer;
        // this.selfSetting.position = selfSettingPos;
        // this.selfSetting.active = false;

        // set emoji layout
        // this.emoticonsList = cc.instantiate(this.emoticonsListPrefab);
        // this.emoticonsList.parent = this.emoticonsNode;
        // this.emoticonsList.position = this.emoticonsListStartPos.position;
        // this.emoticonsList.getComponent(GameEmoticons)._gameEmoticonsStartPosition = this.emoticonsListStartPos;
        // this.emoticonsList.getComponent(GameEmoticons)._gameEmoticonsStopPosition = this.emoticonsListStopPos;
        // this.emoticonsList.active = false;

        // set voice msg layout
        temp = cc.instantiate(this.voiceRecordingMessagePrefab);
        this.voiceMessageDialog = temp.getComponent(VoiceMessageDialog);
        temp.parent = this.node.parent;
        temp.position = this.voiceRecordingMessage.position;
        temp.active = false;

        // set redpocket dialogue
        // this.redPocketNode = cc.instantiate(this.redPocketDialogue);
        // this.redPocketNode.parent = cc.director.getScene().getComponentInChildren(cc.Canvas).node;
        // this.redPocketNode.active = false;

        this.marqueeParent.zIndex = 999;
        cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.PREFAB.COMMON.MARQUEE, cc.Prefab,  (err:any, prefab:any)=> {
            this.marqueeNode = cc.instantiate(prefab);
            this.marqueeNode.parent = this.marqueeParent;
        });
    }

    _speedMode:boolean= false;
    checkSpeedModeAlert(){
        this._speedMode = true;
        // just show for bl
        // if (!localConfig.getLocalStorageItem(localConfig.key_holdemSpeedModeAlert, false) && !this._speedHintShowed){
        //     this.initPanel(Holdem_Room.ROOM_PANEL.SPEED_HINTS, ()=>{
        //         this.speedHints.active = true;
        //         this._speedHintShowed = true;
        //     });
        // }
    }

    checkBulletModeAlert(){
        // just show for bl
        // if (!localConfig.getLocalStorageItem(localConfig.key_bullet_explanation, false) && !this._bulletHintShowed){
        //     this.initPanel(Holdem_Room.ROOM_PANEL.BULLET_HINTS, ()=>{
        //         this.bulletHints.active = true;
        //         this._bulletHintShowed = true;
        //     });
        // }
    }

    initPanel (panel:number, initCallback: () => void = null) {
        switch (panel) {
            case Holdem_Room.ROOM_PANEL.EMOTICONS:
                if (!cc.isValid(this.emoticonsList)) {
                    // this.emoticonsList = cc.instantiate(this.emoticonsListPrefab);
                    // this.emoticonsList.parent = this.emoticonsNode;
                    // this.emoticonsList.position = this.emoticonsListStartPos.position;
                    // this.emoticonsList.getComponent(GameEmoticons)._gameEmoticonsStartPosition = this.emoticonsListStartPos;
                    // this.emoticonsList.getComponent(GameEmoticons)._gameEmoticonsStopPosition = this.emoticonsListStopPos;
                    // this.emoticonsList.active = false;

                    ResourcesLoader.instance.loadRes(ResourcesLoader.RES_PATH.PREFAB.HOLDEM.EMOTICONS_LIST, cc.Prefab, null, (err, prefab) => {
                        if (cc.isValid(this.emoticonsList)) {
                            return;
                        }
                        this.emoticonsList = cc.instantiate(prefab);
                        this.emoticonsList.parent = this.emoticonsNode;
                        this.emoticonsList.position = this.emoticonsListStartPos.position;
                        let emoticonsListScript = this.emoticonsList.getComponent(GameEmoticons);
                        if( emoticonsListScript )
                        {
                            emoticonsListScript.setHoldemRoom(this);
                            emoticonsListScript.init();
                            emoticonsListScript._gameEmoticonsStartPosition = this.emoticonsListStartPos;
                            emoticonsListScript._gameEmoticonsStopPosition = this.emoticonsListStopPos;
                            emoticonsListScript._off = this.emoticonsNodeOff.bind(this);
                        }
                        
                        this.emoticonsList.active = false;

                        if (initCallback) {
                            initCallback();
                        }
                    });
                }
                break;
            case Holdem_Room.ROOM_PANEL.PLAYER_SETTING:
                if (!cc.isValid(this.playerSettingNode)){
                    let url = ResourcesLoader.RES_PATH.PREFAB.COMMON.PLAYER_SETTING;
                    ResourcesLoader.instance.loadRes(url, cc.Prefab, null, (err, prefab)=>{
                        // set layout setting
                        this.playerSettingNode = cc.instantiate(prefab);
                        this.playerSettingNode.parent = this.settingContainer;
                        this.playerSettingNode.position = this.playerSetting.position;
                        this.playerSettingNode.active = false;
                        // // set button callback
                        this.playerSettingNode.getComponent(PlayerSetting).setHoldemRoom(this);
                        this.playerSettingNode.getComponent(PlayerSetting).init();
                        if(this._gameMode == globalConfig.GAME_LEVEL_LIST_ID.SNG ){
                            this.playerSettingNode.getComponent(PlayerSetting).updateSNGPlayerSetting(false);
                        }
                        if (initCallback) {
                            initCallback();
                        }
                    });
                }
                break;
            case Holdem_Room.ROOM_PANEL.SELF_SETTING: // change to table setting pkw 20210311
                cc.vv.ConsoleLog.log('init panel 770', cc.isValid(this.selfSettingNode));
                if (!cc.isValid(this.playerSettingNode)){
                    cc.vv.ConsoleLog.log('pat_check is valid self setting');
                    let url = ResourcesLoader.RES_PATH.PREFAB.COMMON.PLAYER_SETTING;
                    ResourcesLoader.instance.loadRes(url, cc.Prefab, null, (err, prefab)=>{
                        // set layout setting
                        this.playerSettingNode = cc.instantiate(prefab);
                        this.playerSettingNode.parent = this.settingContainer;
                        this.playerSettingNode.position = this.playerSetting.position;
                        this.playerSettingNode.active = false;
                        // // set button callback
                        this.playerSettingNode.getComponent(PlayerSetting).setHoldemRoom(this);
                        if(this._gameMode == globalConfig.GAME_LEVEL_LIST_ID.SNG ){
                            this.playerSettingNode.getComponent(PlayerSetting).updateSNGPlayerSetting(true);
                        }
                        if (initCallback) {
                            initCallback();
                        }
                    });
                }
                break;
            case Holdem_Room.ROOM_PANEL.RED_POCKET:
                if (!cc.isValid(this.redPocketPrefab)){
                    ResourcesLoader.instance.loadRes(ResourcesLoader.RES_PATH.PREFAB.COMMON.RED_POCKET, cc.Prefab, null, (err, prefab)=>{
                        this.redPocketPrefab = cc.instantiate(prefab);
                        this.redPocketPrefab.parent = cc.director.getScene().getComponentInChildren(cc.Canvas).node;
                        this.redPocketPrefab.active = false;

                        if (initCallback) {
                            initCallback();
                        }
                    });
                }
                break;
            case Holdem_Room.ROOM_PANEL.BIG_WIN:
                if (!cc.isValid(this.bigWinPrefab)){
                    ResourcesLoader.instance.loadRes(ResourcesLoader.RES_PATH.PREFAB.COMMON.BIG_WIN, cc.Prefab, null, (err, prefab)=>{
                        this.bigWinPrefab = cc.instantiate(prefab);
                        this.bigWinPrefab.parent = this.animationLayer;
                        this.bigWinPrefab.active = false;

                        if (initCallback) {
                            initCallback();
                        }
                    });
                }
                break;
            case Holdem_Room.ROOM_PANEL.CELEBRITY_SELF_SETTING:
                if (!cc.isValid(this.celebritySelfSettingNode)) {
                    ResourcesLoader.instance.loadRes(ResourcesLoader.RES_PATH.PREFAB.HOLDEM.CELEBRITY_SELF_SETTING, cc.Prefab, null, (err, prefab)=>{
                        this.celebritySelfSettingNode = cc.instantiate(prefab);
                        this.celebritySelfSettingNode.getComponent(CelebritySelfSetting).setHoldemRoom(this);
                        this.celebritySelfSettingNode.parent = this.settingContainer;
                        this.celebritySelfSettingNode.position = this.selfSetting.position;

                        if (initCallback) {
                            initCallback();
                        }
                    });
                }
                break;
            case Holdem_Room.ROOM_PANEL.TABLE_SETTING:
                cc.vv.ConsoleLog.log('init panel 770', cc.isValid(this.selfSettingNode));
                if (!cc.isValid(this.selfSettingNode)){
                    let url = ResourcesLoader.RES_PATH.PREFAB.HOLDEM.TABLE_SETTING;
                    ResourcesLoader.instance.loadRes(url, cc.Prefab, null, (err, prefab)=>{
                        // set self player setting
                        this.selfSettingNode = cc.instantiate(prefab);
                        this.selfSettingNode.getComponent(Holdem_TableSetting).setHoldemRoom(this);
                        this.selfSettingNode.getComponent(Holdem_TableSetting).initLocalSetting();
                        this.selfSettingNode.parent = this.settingContainer;
                        this.selfSettingNode.position = this.selfSetting.position;
                        this.selfSettingNode.active = false;

                        if (initCallback) {
                            initCallback();
                        }
                    });
                }
                break;
        }

    }

    update()
    {
        // cc.vv.ConsoleLog.log("stopBlindTimeCounter",this.stopBlindTimeCounter);
        if( !this.stopBlindTimeCounter )
        {
            let now = new Date().getTime();
            let diff = Math.ceil((now - this.lastSetBlindTime) / 1000);
            // console.log("Timer", this.lastSetBlindTime, now, diff, this.blindTime);
            this.updateBlindTime(this.blindTime - diff);
        }
    }

    setRoomToChild()
    {
        this.menu.setHoldemRoom(this);
        this.gameRecord.setHoldemRoom(this);
        this.tournamentRecord.setHoldemRoom(this);
        this.publicCardsHandler.setHoldemRoom(this);
        this.playerControl.setHoldemRoom(this);
        this.holders.setHoldemRoom(this);
        for(let i = 0; i < this.playerNodes.length; i++) {
            if(cc.isValid(this.playerNodes[i]))
            {
                this.playerNodes[i].getComponent(Holdem_Player).setHoldemRoom(this);
            }
        }
    }

    CreateChildNode() {

        let temp:cc.Node = null;
        // ?? 9 Child Node
        this.playerNodes = new Array(9);
        for(let i = 0; i < 9; i++) {
            temp = cc.instantiate(this.holdemPlayerPrefab);
            // cc.vv.ConsoleLog.log("holdem_room prefab", temp);
            temp.parent = this.playerNodesPos[i].parent;
            temp.position = this.playerNodesPos[i].position;
            temp.active = false;
            let tempScript = temp.getComponent(Holdem_Player);
            if( tempScript )
            {
                tempScript.avatar.node.active = false;
                tempScript.info.node.active = false;
            }
            
            this.playerNodes[i] = temp;
        }
        this.infoPanel = cc.instantiate(this.roomInfoPrefab);
        this.infoPanel.parent = this.tableBackNode;
        this.infoPanel.position = this.roomInfoPos.position;
        this.infoPanel.setSiblingIndex(this.roomInfoPos.getSiblingIndex());
        this.roomInfo = this.infoPanel.getComponent(Holdem_Room_Info);

        // 10429 Hide mtt's info
        // this.infoPanel.active = false;

        temp = cc.instantiate(this.tournamentRecordPrefab);
        temp.parent = this.tournamentRecordPos.parent;
        temp.position = this.tournamentRecordPos.position;
        temp.setSiblingIndex(this.tournamentRecordPos.getSiblingIndex());
        temp.active = false;
        this.tournamentRecord = temp.getComponent(HoldemTournamentDetails);

        temp = cc.instantiate(this.gameRecordPrefab);
        temp.parent = this.gameRecordPos.parent;
        temp.position = this.gameRecordPos.position;
        temp.setSiblingIndex(this.gameRecordPos.getSiblingIndex());
        temp.active = false;
        this.gameRecord = temp.getComponent(holdem_game_records);

        if(!this._reConnectLoading) {
            this._reConnectLoading = cc.instantiate(this.reConnectLoading);
            this._reConnectLoading.parent = this.node.parent;
            this._reConnectLoading.active = false;
            cc.vv.DataManager.reConnectLoading = this._reConnectLoading;
        }

        if( this.isReplay )
        {
            this.menuButton.node.active = false;
            this.historyButton.node.active = false;
            this.tournamentButton.node.active = false;
            this.emojiButton.node.active = false;
            // this.gameMicIcon.active = false;
            this.enableGameMic(false);
            this.guessHandControl.active = false;
            this.infoPanel.active = false;
        }

        for(let i = 0; i < 9; i++)
        {
            let obj = cc.instantiate(this.sidePotNode);
            obj.active = false;
            obj.getComponent(Holdem_Stake).potShow = true;
            this.sidePotObjList.push(obj);
        }
        
    }

    hideReplayUI()
    {
        if( this.isReplay )
        {
            cc.vv.ConsoleLog.log('pat check hideReplayUI');
            // set player node
            this.store.maxHoleCard = this._gameViewType==globalConfig.MTT_GAME_MODE.OMAHA ? 4:2;
            this._players = this.playerNodes.map(node => {
                const n = node.getComponent("Holdem_Player_ts") as Holdem_Player;
                n.room = this;
                n.maxCards = this.store.maxHoleCard;
                return n;
            });
            if( MultipleGame.instance )
            {
                this.audioPlayer.setBlock(true);
                this.loadBackgroundImage('mtt/BG/replay_bg');
                this.gameLogo.active = false;
                this.bgTable.node.active = false;
                this.bgTop.node.active = false;
                this.gameSubIcon.node.active = false;
                this.gameIcon.node.active = false;
                // this.potLabel.node.parent.y = -250;
                this.holders.dealingShoe.y = 150;

                if( this.roomInfo )
                {
                    this.roomInfo.blindLevelContainer.active = false;
                    this.roomInfo.endJoinLevelContainer.active = false;
                    this.roomInfo.anteContainer.active = false;
                    this.roomInfo.nextBlindContainer.active = false;
                    this.roomInfo.nextTimeContainer.active = false;
                    this.roomInfoAlert.node.active = false;
                }

                // todo replay ui update here
    
                this.node.scale = 0.67;
            }
            this.menuButton.node.active = false;
            this.historyButton.node.active = false;
            this.tournamentButton.node.active = false;
            this.emojiButton.node.active = false;
            // this.gameMicIcon.active = false;
            this.enableGameMic(false);
            this.guessHandControl.active = false;
            this.liveBtnAnnouncer.node.active = false;

            for (let i = 0; i < this._players.length; i++) {
                let player = this._players[i];
                player.avatar.node.active = false;
                player.info.node.active = false;
            }

            this.roomInfo.roomName.string = cc.vv.DataManager.replayGameResultDetail.Name;

            cc.game.off(cc.game.EVENT_HIDE, this.onAppPause);
            cc.game.off(cc.game.EVENT_SHOW, this.onAppResume);
        }
    }

    // onKeyUp = (event: cc.Event.EventKeyboard)=>{
    //     switch ((event as any).keyCode) {
    //         case cc.KEY.a:
    //             cc.vv.ConsoleLog.log("onAppPause");
    //             this.onAppPause();
    //             break;
    //         // case cc.KEY.i:
    //         //     this.store.ws.stopListen = !this.store.ws.stopListen;
    //         //     cc.vv.ConsoleLog.log("onRoomSnapshot trigger websocket stop listen: ", this.store.ws.stopListen);
    //         //     break;
    //         case cc.KEY.x:
    //             WorldWebSocket.getInstance().close();
    //             cc.vv.ConsoleLog.log("force close WorldWebSocket");
    //             break;
    //         case cc.KEY.y:
    //             let msg = new ProtoBuf.commonProto.Broadcast_Message_Envelope;
    //             msg.Body = new Uint8Array([8, 1, 16, 2, 40, 210, 30, 50, 10, 67, 101, 110, 100, 111, 32, 84, 101, 115, 116, 56, 3,]);
    //             msg.TypeId = 1000;
    //             this.store.broadcastMessageEnvelope(msg);
    //             break;
    //         case cc.KEY.s:
    //             cc.vv.ConsoleLog.log("onAppResume");
    //             this.onAppResume();
    //             break;
    //         case cc.KEY.d:
    //             if(this.store && this.store.self && this.store.self.seatNum - 1 < this._players.length && this._players[this.store.self.seatNum - 1])
    //             {
    //                 cc.vv.ConsoleLog.log("force change self layout to PlayerLayoutType.Bottom");
    //                 this._players[this.store.self.seatNum - 1].layout = PlayerLayoutType.Bottom;
    //             }
    //             break;
    //     }
    // }

    onAppPause = ()=>{
        if( !this.store.isAppPause )
        {
            cc.vv.ConsoleLog.log("onAppPause Holdem_Room");
            this.store.isAppPause = true;
            // unscheduleAllscheduler
            this.unscheduleAllCallbacks();
            // app pause, reset guess hand loop setting
            this.RemoveGuessHandLoop();
            this.onAppPauseLive();
        }
        else
        {
            cc.vv.ConsoleLog.log("onAppPause Holdem_Room other");
        }
        
        this.restMessageLayer.active = false;
    };

    onAppResume = ()=>{
        if( this.store.isAppPause )
        {
            cc.vv.ConsoleLog.log("onAppResume Holdem_Room");
            this.store.isAppPause = false;

            this.onAppResumeLive();
        }
        else
        {
            cc.vv.ConsoleLog.log("onAppResume Holdem_Room other");
        }
    };

    // _rollMsgList:string[] = [];
    // @render
    // MarqueeCarousel(){
    //     if(this.store){
    //         const {marqueeMsg} = this.store;
    //         if(marqueeMsg){
    //             this.showNotification(marqueeMsg);
    //         }
    //     }
    // }
    addNotification(data:string) {
        // this._rollMsgList.push(data);
        // cc.vv.ConsoleLog.log(this._rollMsgList);
        // if (!this.marqueeLabel.node.parent.active) {
        //
        //     this.marqueeLabel.node.parent.active = true;
        //     this.popNotification();
        // }
        this.marqueeNode.getComponent(Marquee).showMarqueeStr(data);
    }
    // popNotification() {
    //     this.marqueeLabel.node.runAction(cc.sequence(
    //         cc.callFunc(() => {
    //
    //             this.marqueeLabel.string = this._rollMsgList.shift();
    //             this.marqueeLabel.node.setPosition(this.marqueeLabel.node.width + this.marqueeLabel.node.parent.width, this.marqueeLabel.node.position.y);
    //
    //             cc.vv.ConsoleLog.log("Msg to show: ", this.marqueeLabel.string);
    //
    //         }),
    //         cc.moveTo(15, (this.marqueeLabel.node.width + this.marqueeLabel.node.parent.width) * -1, this.marqueeLabel.node.position.y),
    //         cc.callFunc(()=> {
    //
    //             if (this._rollMsgList.length > 0) {
    //                 this.popNotification();
    //             } else {
    //                 this.marqueeLabel.node.parent.active = false;
    //             }
    //         })
    //     ));
    // }


    @render
    setRedPocketDialog(){
        if(this.store){
            const{redPocketPrize,redPocketApproved,redPocketRecordId} = this.store;
            if(redPocketPrize != ""){
                let callback = ()=>{
                    // show redpocket dialog
                    let self = this;
                    let message = redPocketPrize;
                    let buttonActions = [{
                        buttonName: redPocketApproved ? Translate(Translation.RED_POCKET.CONTENT_CS) : Translate(Translation.GAME.CONFIRM),
                        callback: () => {
                            self.hideRedPocketDialog();
                            self.store.resetRedPocketDialog();
                        },
                    }];
                    if(redPocketRecordId){
                        this.showRedPocketDialog(message,buttonActions,redPocketRecordId);
                    } else {
                        this.showRedPocketDialog(message,buttonActions,"NO RECORD ID");
                    }


                };

                // big win
                this.initBigWin(callback);
            }
        }
    }

    showRedPocketDialog(message:any,buttonActions:any,redPocketRecordId?:any){
        this.initPanel(Holdem_Room.ROOM_PANEL.RED_POCKET , () => {
            this.redPocketPrefab.active = true;
            this.redPocketPrefab.getComponent(ppConfig.RedPocketDialog).showBlockedDialogMessage(Translate(Translation.RED_POCKET.CONGREGATION), message, buttonActions,redPocketRecordId);
        });

    }

    hideRedPocketDialog(){
        if (cc.isValid(this.redPocketPrefab)){
            this.redPocketPrefab.getComponent(ppConfig.RedPocketDialog).hide();
        }
    }

    initBigWin(cb:any){
        cc.vv.ConsoleLog.log("big win");
        // 先播1.5秒animation
        // 再show redpocket dialogue
        this.initPanel(Holdem_Room.ROOM_PANEL.BIG_WIN , () => {
            this.bigWinPrefab.active = true;
            let showDuration = 1.5;
            let bigWinAnim = this.bigWinPrefab.children[0].getComponent(cc.Animation);
            if (bigWinAnim){
                showDuration = bigWinAnim.getClips()[0].duration;
                bigWinAnim.play();
            }
            let self = this;
            this.bigWinPrefab.runAction(
                cc.sequence(
                    cc.delayTime(showDuration),
                    cc.callFunc(function () {
                        self.bigWinPrefab.active = false;
                        self.bigWinPrefab.destroy();
                        cb();
                    }, this)
                )
            );
        });
    }

    @render
    RoomIdChanged() {
        if(this.store && this.store.roomId && this._roomId == 0) {
            cc.vv.ConsoleLog.log('render -> RoomIdChanged');
            this.enter();
            if (this.store.totalSeatCount !== 0 && this.store.totalSeatCount!== this.seats){
                cc.vv.ConsoleLog.log('render -> RoomIdChanged set Seat', this.seats, this.store.totalSeatCount);
                this.seats = this.store.totalSeatCount;
                if (this.isReplay && MultipleGame.instance){
                    this.setMaxPlayerPOPUPReplay(this.seats);
                }else{
                    this.setMaxPlayer(this.seats);
                }

            }
        }
    }

    @render
    BuyTime(){
        if (this.store){
            const {buyTimeCount} = this.store;
            cc.vv.ConsoleLog.log('room BuyTime - ',buyTimeCount, this.playerControl.isBuyingTime);
            if(buyTimeCount == 0){
                this.playerControl.roundUpdateBuyButton();
            } else {
                if (this.playerControl.isBuyingTime){
                    this.setNotificationMsg(cc.js.formatStr(LanguageManager.t("HOLDEM.REMAIN_BUY_TIME_COUNT"), (5-buyTimeCount)));
                    this.playerControl.isBuyingTime = false;
                }

                this.playerControl.setBuyButtonLabel(buyTimeCount);
            }
        }
    }

    @render
    WaitForChangeTable(){
        if (this.store && !this.isReplay){
            const {waitForChangeTable} = this.store;
            this.waitForTableMessageLayer.active = waitForChangeTable;
            if (waitForChangeTable){
                this.waitForTableMessageLayer.children[0].getComponent(cc.Label).string = Translate(Translation.ERROR_CODE_PKW["60086"]);
                this.historyButton.node.active = false;
                this.tournamentButton.node.active = false;
            }else{
                this.historyButton.node.active = true;
                this.tournamentButton.node.active = true;
            }
        }
    }

    PreparingMessage(isPreparing:boolean){
        if (this.store){
            cc.vv.ConsoleLog.log(isPreparing,"PreparingMessage");
            if(isPreparing){
                this.restMessageLayer.active = true;
                this.restMessageLayer.children[1].active = true; // rest msg
                this.restMessageLayer.children[1].children[0].getComponent(cc.Label).string = Translate(Translation.MTT_HALL.MTT_GAME_NOT_STARTED);
                this.restMessageLayer.children[0].active = false; // style 1
                this.restMessageLayer.children[1].children[1].active = false; // hide timer
            } else {
                // reset
                this.restMessageLayer.active = false;
                this.restMessageLayer.children[1].active = false;
                this.restMessageLayer.children[1].children[1].active = true;
            }
        }
    }

    resumeMsgConversion() {
        if(this.store.resumeTimeRemain >= 0)
        {
            this.store.resumeMessage = this.store._pauseMessage.replace(/\\n/g,'\n').replace("%time%", this.formatTime(this.store.resumeTimeRemain));
            this.roomMessageLayer.children[0].getComponent(cc.Label).string = this.store.resumeMessage;
        }
    }

    resumeCountdown() {
        if(this.store.resumeTimeRemain > 0)
        {
            this.store.resumeTimeRemain--;
            this.resumeMsgConversion();
        }else{
            this.roomMessageLayer.active = false;
            this.store.resumeMessage = "";
            this.unschedule(this.resumeCountdown);
        }
    }

    formatTime(time:number) {
        if(time <= 0)
        {
            return '00:00:00';
        }

        let h = time >= 3600 ? Math.floor(time/3600) : 0;
        let m = time >= 60 ? Math.floor(time%3600/60) : 0;
        let s = time > 0 ? Math.floor(time%3600%60) : 0;

        let hh = this.padZeroToTime(h);
        let mm = this.padZeroToTime(m);
        let ss = this.padZeroToTime(s);

        return `${hh}:${mm}:${ss}`;
    }

    @render
    RestMessage(){
        if (this.store){
            const {restMessage,restTime, roomMessage} = this.store;
            const {RestTime_Type} = ProtoBuf.mttPro;
            cc.vv.ConsoleLog.log("RestMessage", restMessage, restTime, roomMessage);

            if(roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Wait_Rebuy || roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Wait_Morebuy){
                    if(roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Wait_Rebuy){
                        this.roomMessageLayer.children[0].getComponent(cc.Label).string = Translate(Translation.GAME.WAIT_FOR_REBUY_START);
                    }
                    if(roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Wait_Morebuy){
                        this.roomMessageLayer.children[0].getComponent(cc.Label).string = Translate(Translation.GAME.WAIT_FOR_MOREBUY_START);
                    }
            }

            if (roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Pause || roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Final_Pause || roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Will_Playing){
                if (roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Final_Pause){
                    this.roomMessageLayer.children[0].getComponent(cc.Label).string = cc.js.formatStr(Translate(Translation.GAME.Game_Final_Pause), this.getTimeString(this.store.nextPeriodStartTime));
                }else{
                    if(this.store.resumeTimeRemain > 0){
                        if(this.store._pauseMessage!=="")
                        {
                            this.resumeMsgConversion();
                            this.schedule(this.resumeCountdown, 1);
                        }else{
                            this.roomMessageLayer.children[0].getComponent(cc.Label).string = Translate(Translation.GAME.GAME_ROOM_PAUSE);
                        }
                    }
                    else{
                        this.roomMessageLayer.children[0].getComponent(cc.Label).string = this.store._pauseMessage==""?Translate(Translation.GAME.GAME_ROOM_PAUSE):this.store._pauseMessage;
                    }
                }

                if (this.roomMessageDelay==null){
                    this.roomMessageDelay = ()=>{
                        this.roomMessageLayer.active = true;
                        this.store.resetHandForHandMessage();
                    }
                }
                this.scheduleOnce(this.roomMessageDelay,3);
                this.ResetPlayerControl();
                this.restMessageLayer.active = false;
            } else{
                if (restMessage==RestTime_Type.RestTime_Type_Null){
                    if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG){
                        if (this.store.sngStatus==101){
                            this.restMessageLayer.active = false;
                        }else{
                            this.restMessageLayer.active = false;
                        }
                    }else{
                        this.restMessageLayer.active = false;
                    }
                } else {
                    const restTimeRemain = this.getTimeDiff(this.store.restEndTime);
                    
                    if (restMessage==RestTime_Type.RestTime_Type_WILL_REST){
                        //this.restMessageLayer.children[0].children[0].getComponent(cc.Label).string = Translate(Translation.MTT_PINEAPPLE.WILL_REST_NOTIFICATION);
                        let restMsg = "";
                        if(this.store.MTTDetail && this.store.MTTDetail.RestDuration != 0){
                            restMsg = cc.js.formatStr(Translate(Translation.MTT_PINEAPPLE.SYNC_REST_NOTIFICATION), this.store.MTTDetail.RestDuration);
                        } else {
                            restMsg = Translate(Translation.MTT_PINEAPPLE.WILL_REST_NOTIFICATION);
                        }
                        this.restMessageLayer.children[0].children[0].getComponent(cc.Label).string = restMsg;
                        this.restMessageLayer.children[0].active = true;
                        this.restMessageLayer.children[1].active = false;
                        if( this.store.self && this.store.self.hasFold )
                        {
                            this.ResetPlayerControl();
                        }
                        if(!this.store.isMysteryBountyOnIntro){
                            this.restMessageLayer.active = true;
                        }
                    }else{
                        this.store.resetHandForHandMessage();
                    }
                    
                    if(restMessage==RestTime_Type.RestTime_Type_MIDFIELD || restMessage==RestTime_Type.RestTime_Type_FINALS){
                        cc.vv.ConsoleLog.log("RestMessage rest info check", restMessage, restTimeRemain, !this.store.isMysteryBountyOnIntro);
                        if(restTimeRemain > 0){
                            if(restMessage==RestTime_Type.RestTime_Type_MIDFIELD){
                                this.restMessageLayer.children[1].children[0].getComponent(cc.Label).string = Translate(Translation.MTT_PINEAPPLE.MIDDLE_ROUND_REST_NOTIFICATION);
                            } else {
                                this.restMessageLayer.children[1].children[0].getComponent(cc.Label).string = Translate(Translation.MTT_PINEAPPLE.FINAL_ROUND_REST_NOTIFICATION);
                            }

                            cc.vv.ConsoleLog.log("Holdem_Room_ts RestMessage check", this.store.isMysteryBountyOnIntro, this.store.restEndTime);
                            this.showRestMessageNodes();

                            this.restMessageLayer.children[0].active = false;
                            this.restTime = restTime;
                            this.unschedule(this.countDownRest);
                            this.schedule(this.countDownRest,0.5);
                            this.ResetPlayerControl();
                            this.ResetAllPlayer();
                        }
                    }
                }

                if(roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Wait_Rebuy || roomMessage==ProtoBuf.mttPro.Tips_Type.Game_Wait_Morebuy){
                    this.roomMessageLayer.active = true;
                    if(restTime>0){
                        this.restMessageLayer.active = false;
                    }
                }else{
                    this.roomMessageLayer.active = false;    
                }

                if (this.roomMessageDelay){
                    this.unschedule(this.roomMessageDelay);
                    this.roomMessageDelay = null;
                }
            }
        }
    }

    getTimeDiff(timestamp:number){
        return Math.floor((timestamp - cc.vv.DataManager.getNow().getTime())/1000);
    }

    showRestMessageNodes(){
        const rebuyTimeRemain = this.getTimeDiff(this.store.rebuyEndTime);
        const morebuyTimeRemain = this.getTimeDiff(this.store.morebuyEndTime);

        cc.vv.ConsoleLog.log("Holdem_Room_ts showRestMessageNodes", rebuyTimeRemain <= 0, morebuyTimeRemain <= 0, !this.store.isMysteryBountyOnIntro, this.getTimeDiff(this.store.restEndTime));

        if(rebuyTimeRemain <= 0 && morebuyTimeRemain <= 0 && !this.store.isMysteryBountyOnIntro){   // do not show rest message during rebuy, morebuy stage, mystery bounty intro
            const restTimeRemain = this.getTimeDiff(this.store.restEndTime);
            this.restMessageLayer.children[1].active = (restTimeRemain>0);
            this.restMessageLayer.active = (restTimeRemain>0);
        }
    }

    getTimeString(num:number){
        let d = new Date(num);
        let dd = this.padZeroToTime(d.getDate());
        let MM = this.padZeroToTime(d.getMonth()+1);
        let hh = this.padZeroToTime(d.getHours());
        let mm = this.padZeroToTime(d.getMinutes());
        let ss = this.padZeroToTime(d.getSeconds());
        return MM+"/"+dd+" "+hh+":"+mm+":"+ss;
    }
    padZeroToTime(num:number){
        return num<10?"0"+num:num;
    }

    @render
    HandForHandMessageHandler(){
        if(this.store){
            const{handForHandMessage} = this.store;
            switch (handForHandMessage){
                case ProtoBuf.mttPro.Tips_Type.Game_Sync_Poker_Enter:
                    this.handForHandMessageAnimation(Translate(Translation.GAME.GAME_SYNC_POKER_ENTER));
                    break;
                case ProtoBuf.mttPro.Tips_Type.Game_Sync_Pokering:
                        this.handForHandMessageLayer.children[0].getComponent(cc.Label).string = Translate(Translation.GAME.GAME_SYNC_POKERING);
                        if (this.handForHandMessageDelay==null){
                            this.handForHandMessageDelay = ()=>{
                                if(this.store.restMessage != ProtoBuf.mttPro.RestTime_Type.RestTime_Type_MIDFIELD && this.store.restMessage != ProtoBuf.mttPro.RestTime_Type.RestTime_Type_FINALS){ // not in rest
                                    this.handForHandMessageLayer.active = true;
                                }
                            }
                        }
                        this.scheduleOnce(this.handForHandMessageDelay,0.1);

                    break;
                case ProtoBuf.mttPro.Tips_Type.Game_Sync_Poker_Out:
                    this.handForHandMessageLayer.active = false;
                    if (this.handForHandMessageDelay){
                        this.unschedule(this.handForHandMessageDelay);
                        this.handForHandMessageDelay = null;
                    }
                    this.setNotificationMsg(Translate(Translation.GAME.GAME_SYNC_POKER_OUT));
                    // this.handForHandMessageAnimation(Translate(Translation.GAME.GAME_SYNC_POKER_OUT));
                    break;
                case 0:
                    //this.store.resetHandForHandMessage(); <~ use this to reset 
                    this.handForHandMessageLayer.active = false;
                    if (this.handForHandMessageDelay){
                        this.unschedule(this.handForHandMessageDelay);
                        this.handForHandMessageDelay = null;
                    }
                    break;
            }
        }
    }

    CheckPlayerControlPanel()
    {
        if( !this.isReplay )
        {
            if (!cc.vv.DataManager.webPlatform && (this._gameViewType==globalConfig.MTT_GAME_MODE.NLH) && localConfig.getLocalStorageItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, false) && this.store.self && !this.store.self.showControl && !this.store.waitForStart ){
                this.guessHand.PanelActive(this.store.self.cards.length>0);
                this.playerControl.preActionPanel.active = false;
            }else{

                if( this.store.self && !this.store.self.showControl && !this.store.self.hasFold && !this.store.waitForStart && this.store.self.cards.length > 0 && this.store.self.state != ProtoBuf.holdem.PlayerState.WAITING ){
                    if (this.store.self.actualState==ProtoBuf.holdem.Action.ALL_IN){
                        this.playerControl.preActionPanel.active = false;
                    }else{
                        this.playerControl.preActionPanel.active = !this.store.isShowDown;
                    }

                }else{
                    this.guessHand.PanelActive(false);
                    this.playerControl.preActionPanel.active = false;
                }
            }

            if( this.store.self && this.store.self.showControl)
            {
                this.playerControl.mainPanel.active = true;
            }
            else
            {
                this.playerControl.mainPanel.active = false;
            }
            
            if( this.store.self )
            {
                // cc.vv.ConsoleLog.log(this.store.self, "playerControl preActionPanel",this.playerControl.preActionPanel.active, this.store.self.showControl, this.store.self.hasFold, this.store.waitForStart, this.store.self.cards.length, this.store.self.state);
                // cc.vv.ConsoleLog.log("playerControl preActionPanel state", this.store.self.state, this.store.self.hasFold);
                this.timeBankBtn.active = !this._speedMode;
            }else{
                this.timeBankBtn.active = false;
            }
                        
            if (this.store.getSeatedPlayerByUserId(this.store.playerUserId)) {
                this.emojiButton.node.active = true;
            }
        }
        else
        {
            this.playerControl.preActionPanel.active = false;
            this.playerControl.mainPanel.active = false;
        }
    }

    ResetPlayerControl() {
        //cc.vv.ConsoleLog.log("playerControl preActionPanel false");
        
        // this.CheckPlayerControlPanel();
        if( this.playerControl )
        {
            this.playerControl.preActionPanel.active = false;
            this.playerControl.mainPanel.active = false;
            this.playerControl.allInPanel.active = false;
            this.playerControl.showAllInSlide(false);
            // this.waitStart.active = false;
            this.iconH.showDealer(0);
        }
    }

    ResetAllPlayer() {
        // cc.vv.ConsoleLog.log("playerControl ResetAllPlayerHoldCard");
        for(const player in this._players)
        {
            this.ResetPlayer(this._players[player]);
        }
    }

    ResetPlayer(player:Holdem_Player) {
        const offset = player._seatIndex * 100;
        player.cardChangesRemoveAllCard();
        player.stake.value = 0;
    }

    countDownRest(){

        this.restMessageLayer.children[1].children[1].active = true;
        // this.restMessageLayer.children[1].getComponent(cc.Label).string = second.toString();
        const remainTime = this.getTimeDiff(this.store.restEndTime);

        if (remainTime <= 0) {
            this.unschedule(this.countDownRest);
            this.restMessageLayer.active = false;
        }

        const cT = Math.floor(remainTime/60);
        const cS = remainTime%60;
        this.restMessageLayer.children[1].children[1].getComponent(cc.Label).string = (Array(2).join('0') + cT).slice(-2) +":"+ (Array(2).join('0') + cS).slice(-2);
        this.restTime = remainTime > 0? remainTime : 0;
    }

    // use once
    @render
    PlayerGameEndAnimation(){
        if (this.store){
            const {animate} = this.store;
            if(animate == 1){
                let an:cc.Node;
                cc.vv.ConsoleLog.log('animate start');
                an = this.animationLayer.children[animate-1];
                an.active = true;
                an.getComponent(cc.Animation).once('finished',()=>{an.active = false; cc.vv.ConsoleLog.log('animate finish'); });
                an.getComponent(cc.Animation).play();
                this.store.resetAnimate();
            }
        }
    }

    // use once
    @render
    PlayGameStartAnimation(){
        if (this.store){
            const {animate} = this.store;
            if(animate == 2){
                // let an:cc.Node;
                // an = this.animationLayer.children[animate-1];
                // sng game start animation
                cc.vv.ConsoleLog.log('** UI SNG START ANIMATION, count down animate start');
                let self = this;
                this.animationPlayDelay = function(){
                    self.RiseBlind();
                    // self.animationLayer.children[animate-1].destroy();
                };
                this.scheduleOnce(this.animationPlayDelay, 6.1);

                // todo hide room info alert
                if(this.roomInfoAlert != null){
                    this.roomInfoAlert.hideAlert();
                }

                for (const i of this._players){
                    i.seat.removeTakeInPopUp();
                }
                // an.active = true;
                // an.getComponent(cc.Animation).once('finished',()=>{an.active = false; cc.vv.ConsoleLog.log('animate finish'); });
                // an.getComponent(cc.Animation).play();
            }
        }
    }

    // will reuse
    @render
    PlayKOAnimation(){
        if (this.store){
            const {animate} = this.store;
            if (animate == 3){
                let an:cc.Node;
                cc.vv.ConsoleLog.log('ko animate start');
                an = this.animationLayer.children[animate-1];
                //delay ko animation
                this.koAnimationDelay = function(){
                    an.active = true;
                    an.getComponent(cc.Animation).once('finished',()=>{an.active = false; cc.vv.ConsoleLog.log('animate finish'); });
                    an.getComponent(cc.Animation).play();
                };
                this.scheduleOnce(this.koAnimationDelay, 2);
                this.store.resetAnimate();
                return;
            }
        }
    }

    // will reuse
    @render
    PlayRiseBlindAnimation(){
        if (this.store){
            const {animate, bigBlind, smallBlind, ante, currentBlindLevel} = this.store;
            if (currentBlindLevel > 0)
                this.tournamentRecord.updateCurrentLevel(currentBlindLevel);
            if (animate==4){
                let an:cc.Node;
                an = this.animationLayer.children[animate-1];
                // rise blind animation
                cc.vv.ConsoleLog.log('** UI RISE BLIND ANIMATION');
                this.infoPanel.children[1].children[1].getComponent(cc.Label).string = currentBlindLevel.toString();
                let blindLv = (this._gameViewType !== globalConfig.MTT_GAME_MODE.SHORT_DECK)?  smallBlind+"/"+bigBlind: bigBlind;
                an.children[1].getComponent(cc.Label).string = cc.js.formatStr(Translate(Translation.HOLDEM.RISE_BLIND_MTT), blindLv,ante);
                console.log(cc.js.formatStr(Translate(Translation.HOLDEM.RISE_BLIND_MTT), smallBlind, bigBlind,ante), smallBlind);

                this.RiseBlind();

                an.active = true;
                an.getComponent(cc.Animation).once('finished',()=>{an.active = false; cc.vv.ConsoleLog.log('animate finish'); });
                an.getComponent(cc.Animation).play();

                this.store.resetAnimate();
            }
        }

    }

    PlayVoiceMessageAnimation(msg:string){
        let an = this.animationLayer.children[4];
        an.children[1].getComponent(cc.Label).string = msg;
        an.active = true;
        an.getComponent(cc.Animation).once('finished',()=>{an.active = false; cc.vv.ConsoleLog.log('animate finish'); });
        an.getComponent(cc.Animation).play();
    }

    handForHandMessageAnimation(msg:string){
        let an = this.animationLayer.children[5];
        an.children[1].getComponent(cc.Label).string = msg;
        an.active = true;
        an.getComponent(cc.Animation).once('finished',()=>{an.active = false; cc.vv.ConsoleLog.log('animate finish'); });
        an.getComponent(cc.Animation).play();
    }
    RoomInfoAlert(active:boolean, reliveCount:number=0){
        if (active){
            // show room alert
            if((this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG || this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF) && this.roomInfoAlert != null && this.store){
                const {regFee,srvFee, blindLevelTime, minTakein} = this.store;
                if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
                    this.roomInfoAlert.setReliveTime(reliveCount, true);
                }
                this.roomInfoAlert.showAlert(minTakein, (regFee + srvFee), FormatParser.RoundToDecimal(blindLevelTime / 60, 1));
            }
        } else{
            this.roomInfoAlert.hideAlert();
        }
    }

    @render
    HideRoomInfoAlert() {
        // if (this.store.roomSnapshotState >= 4 && this.store.roomSnapshotState !==32) {
        //     this.roomInfoAlert.hideAlert();
        // }else{
        //     // this.roomInfoAlert.lb_relive =
        //     if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
        //         this.roomInfoAlert.setReliveTime(this.store.leftRejoinCount, true);
        //     }
        // }
    }

    @render
    WaitForNext(){
        if (this.store){
            const {waitForStart} = this.store;
            if (waitForStart){
                cc.vv.ConsoleLog.log('hide wait for start next round');

                // for (const player in this._players){
                //     if(this._players[player].store && this._players[player].store.userId == this.store.playerUserId )
                //     {
                //         // if(this._players[player].cardsHandler.cardsLength() > 0)
                //         // {
                //         //     this._players[player].cardsHandler.clearCards(this.holders.publicDealingShoe, false);
                //         // }
                //         this._players[player].cardsHandler.clearCardImmediate();
                //     }
                // }
                // this.ResetPlayerControl();
                // ResetPlayerControl but show dealer Bug #2531
                this.playerControl.preActionPanel.active = false;
                this.playerControl.mainPanel.active = false;
                this.playerControl.allInPanel.active = false;
                this.playerControl.showAllInSlide(false);

                if (this.store.self && this.store.self.state==ProtoBuf.holdem.PlayerState.PLAYER_STATE_HU_WAIT){
                    this.waitStartHu.active = true;
                    this.waitStart.active = false;
                }else{
                    this.waitStart.active = true;
                    this.waitStartHu.active = false;
                }
                // this.playerControl.preActionPanel.active = false;
            }else{
                this.waitStart.active = false;
                this.waitStartHu.active = false;
            }
        }
    }
    @render
    BlindTimeChange()
    {
        if (this.store){
            const {riseBlindTime} = this.store;
            cc.vv.ConsoleLog.log('riseBlindTime', riseBlindTime);
            // set snapshot riseBlindTime and clear for next action
            if (riseBlindTime>=0){
                this.blindTime = riseBlindTime;
                this.store.updateRestRiseTime(-1);
            }
        }
    }


    @render
    RoomStatusChanged(){
        if (this.store){
            const {roomSnapshotState, sngStatus} = this.store;
            cc.vv.ConsoleLog.log('RoomStatusChanged', roomSnapshotState, sngStatus);
            // cc.vv.ConsoleLog.log('snapRise', riseBlindTime, roomSnapshotState, sngStatus);
            // if (this._gameMode!==globalConfig.GAME_LEVEL_LIST_ID.MTT){
            //     if (sngStatus>=1 && sngStatus!==101){
            //         this.riseOpen = true;
            //     }
            //     if (riseBlindTime>0 && (roomSnapshotState>4 && roomSnapshotState!== 32&&sngStatus!==101)){
            //         this.RiseBlind(riseBlindTime);
            //     }else{
            //         this.blindTime = riseBlindTime;
            //         // this.countDownBlind();
            //     }
            // }else{
            //     cc.vv.ConsoleLog.log('rise mtt?');
            //     if (riseBlindTime>0 && this.store.restMessage==0){
            //         cc.vv.ConsoleLog.log('rise mtt 622 ', riseBlindTime);
            //         this.RiseBlind(riseBlindTime);
            //     } else{
            //         cc.vv.ConsoleLog.log('rise mtt 625 ', riseBlindTime);
            //         this.blindTime = riseBlindTime;
            //         // this.countDownBlind();
            //     }
            // }
        }

    }

    onClickSwitchCoinMode() {
        if (this.store) {
            cc.vv.ConsoleLog.log('update coin mode value:');
            MultipleGame.instance.updateCoinModeDisplay(MultipleGameType.HOLDEM);
        } else {
            cc.vv.ConsoleLog.log('pat_check no store');
        }
    }

    updateCoinModeValue(){
        if (this.store){
            const value = cc.sys.localStorage.getItem(localConfig.key_enableDisplayBB);
            cc.vv.ConsoleLog.log('check key display_BB_enabled value:', value, value == 1);
            if (value == 1) {
                this.coinMode = 1;
            } else {
                this.coinMode = 0;
            }
            cc.vv.ConsoleLog.log('pat_check :', this.coinMode);
            this.pot = this.store.tempPot;
            // playerControl
            this.playerControl.coinMode = this.coinMode;
            this.updatePlayerControlCoinMode();

            // stake
            let stakes = this.node.getComponentsInChildren(Holdem_Stake);
            const denominator = this.store.isShortDeck? this.store.calcAnte : this.store.calcBigBlind;
            stakes.forEach(s=>{
                let st = s.getComponent(Holdem_Stake);
                st.updateCoinModeInfo(this.coinMode, denominator, this.store.isShortDeck);
                st.value = st._value;
            });

            for (const i of this._players){
                i.switchCoinMode(this.coinMode, this.store.calcBigBlind);
                i.updateBalanceString();
            }

            if (cc.isValid(this.selfSettingNode)) {
                this.selfSettingNode.getComponent(Holdem_TableSetting).updateCoinMode();
            }

            if (this.tournamentRecord) {
                this.tournamentRecord.updateCoinMode();
            }
        }
    }

    @render
    NewRound() {
        if (this.store){
            const {dealerPos,straddlePos,restMessage} = this.store;
            const {RestTime_Type} = ProtoBuf.mttPro;
            this.dealerPos = dealerPos;
            cc.vv.ConsoleLog.log('dealer debug:', this.dealerPos);
            this.straddlePos = straddlePos;
            if( this.dealerPosActionInterval != null )
            {
                this.unschedule(this.dealerPosActionInterval);
                cc.vv.ConsoleLog.log('remove dealerPosActionInterval 1');
                this.dealerPosActionInterval = null;
            }

            cc.vv.ConsoleLog.log("new round", dealerPos, straddlePos, restMessage);
            this.iconH.node.active = true;
            // if (this.dealerPos >0 || this.straddlePos)
            // todo -> set player icon with mobX bug
            // this.iconH.showStraddle(this.straddlePos)
            // if (dealerPos>0 && this.riseOpen){
            //     if (this.store.riseBlindTime>0){
            //         this.RiseBlind(this.store.riseBlindTime);

            //     }
            // }
            this.playerControl.resetPreActionPanel();

            if( restMessage!=RestTime_Type.RestTime_Type_MIDFIELD && restMessage!=RestTime_Type.RestTime_Type_FINALS )
            {
                this.iconH.showDealer(dealerPos);
                if (dealerPos!==undefined && dealerPos != 0){

                    for (const player in this._players){
                        if (this._players[player].state!==ProtoBuf.holdem.PlayerState.PLAYER_STATE_ZOMBIE){
                            this._players[player].state = ProtoBuf.holdem.PlayerState.NONE_STATE;
                            this._players[player].actualState = ProtoBuf.holdem.PlayerState.NONE_STATE;
                        }
                        if (player==(dealerPos-1).toString()){
                            cc.vv.ConsoleLog.log('setDealerPos icon', player, this._players[player].node.position, CommonTools.instance.convertToWorldSpace(this._players[player].node, cc.v2(0,0)), this._players[player]._layout);

                            this.iconH.moveDealer(CommonTools.instance.convertToWorldSpace(this._players[player].node, cc.v2(0,0)),this._players[player]._layout, this._players[player]._isUseCelebrityLayout);
                            // }
        
                        }
                        // this._players[player].avatar.showAllInAnimation(2);
                    }
        
                }
            }
            else
            {
                this.iconH.showDealer(0);
            }
        }
        
        
    }

    @render
    MenuItem(){
        // if (this.store.self){
        //
        // }
    }

    UpdatePreCallLabel(index:number) {
        this.playerControl.preTextLabels.forEach((label,i)=>{
            label.active = index==i;
        });
        this.playerControl.preTextLabelsUnClick.forEach((label,i)=>{
            label.active = index==i;
        });
    }

    UpdatePreFoldLabel(index:number){
        this.playerControl.preFoldLabels.forEach((label,i)=>{
           label.active = index==i;
        });
        this.playerControl.preFoldLabelsUnClick.forEach((label,i)=>{
            label.active = index==i;
        });
    }

    ChangePreCallLabel(index:number) {
        this.UpdatePreCallLabel(index);
        this.UpdatePreFoldLabel(index==2?1:0);
    }

    ShowAutoWatch() {
        // this.playerControl.preCallButton.children[1].active = true;
        this.playerControl.preCallValue.node.active = false;
        this.playerControl.preCallValueUnit.node.active = false;
        this._callDiffValue = 0;
        if(this.coinMode == 0){
            this.playerControl.preCallValue.string = '0';
        }else{
            this.playerControl.preCallValue.string = '0BB';
        }
        this.ChangePreCallLabel(2);
    }

    _callDiffValue:number = 0;
    ShowFreeCall(calCall:number) {
        let callDiff = calCall-this.store.self.deskCoin;
        callDiff = Math.min(callDiff, this.store.self.leftCoin);
        const unitStr = FormatParser.GetStackUnit(this.store?.isShortDeck);
        cc.vv.ConsoleLog.log("ShowFreeCall", calCall, callDiff, this.store.self.deskCoin, this.store.self.leftCoin, this.playerControl.preCallValue.string, this.store.self.state);
        if (callDiff!== this._callDiffValue && this.store.self.state!==ProtoBuf.holdem.Action.ALL_IN) {

            if (this.playerControl._preClickCall) {
                this.playerControl.resetPreActionPanel();
            }
        }
        if (callDiff >= this.store.self.leftCoin){
            // this.playerControl.preCallValue.string = Math.floor(this.store.self.leftCoin).toString();
            this.playerControl.preCallValue.node.active = false;
            this.playerControl.preCallValueUnit.node.active = false;
            this._callDiffValue = callDiff;
            this.ChangePreCallLabel(0);
        } else{
            this.playerControl.preCallValue.node.active = true;
            this._callDiffValue = callDiff;
            this.playerControl.preCallValue.string = FormatParser.DisplayStack(callDiff, this.store?.calcBigBlind, this.store?.calcAnte, this.coinMode, this.store?.isShortDeck);
            this.playerControl.preCallValueUnit.node.active = this.coinMode == 1;
            this.playerControl.preCallValueUnit.string = unitStr;
            this.ChangePreCallLabel(1);
        }

        // this.playerControl.preCallValue.node.active = true;
        // this.playerControl.preCallButton.children[1].active = false;

    }

    @render
    PreActionButton(){
        if (this.store){
            const {tmpMaxDeskCoin,self,allBoardCards} = this.store;
            cc.vv.ConsoleLog.log('tmpMax - ', self);
            if (self!==undefined && self!==null) {
                cc.vv.ConsoleLog.log(483, tmpMaxDeskCoin, this.store.calcBigBlind);
                
                if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
                    // AOF free call all in
                    this.ShowFreeCall(self.leftCoin+self.deskCoin);
                } else{
                    if (tmpMaxDeskCoin==0){
                        // call ok
                        this.ShowAutoWatch();
                    } else{
                        let calCall = tmpMaxDeskCoin;
                        // #31446
                        let compareNum = (this._gameViewType !== globalConfig.MTT_GAME_MODE.SHORT_DECK)?this.store.calcBigBlind:this.store.calcBigBlind/2;
                        calCall = (tmpMaxDeskCoin>compareNum)?tmpMaxDeskCoin : compareNum;
                        cc.vv.ConsoleLog.log('PreActionButton', calCall, this.store.self.deskCoin);
                        if (calCall<=this.store.self.deskCoin){
                            this.ShowAutoWatch();
                        } else{
                            this.ShowFreeCall(calCall);
                        }
                    }
                }
            }else{
                this.playerControl.node.active = false;
            }
        }

    }

    @render
    PotChanged() {
        if (this.store){
            const {tempPot, mainPot, tournamentMode, sidePot} = this.store;
            if (tournamentMode == 5) {

            }
            this.pot = tempPot;
            cc.vv.ConsoleLog.log('mainpot debug', mainPot, this.mainPotStake.value);
            this.mainPotStake.value = mainPot;

            // todo sidepot

            cc.vv.ConsoleLog.log('PotChange debug mainPot:', mainPot, ' tempPot:', tempPot, ' sidePot:',sidePot);

            const denominator = this.store.isShortDeck? this.store.calcAnte : this.store.calcBigBlind;

            if (sidePot.length > 1) {
                // 1. check alive side pot, compare with now side pot
                // 2. add side pot
                // add side pot, if side pot >3 (x = 0), side pot >


                const dy = -70, dx = 220, sidePotScale = 1, columnLimit = 2, animateduration = 0.2;

                let sidePotParent = this.mainPotStake.node.parent.getChildByName("side_pot");
                // let startX = 0;
                // let startY = 0;

                if (this.sidePotList.length < sidePot.length) {
                    //add side pot object

                    // let topItem = sidePotParent.children.length%3;
                    // scale 0.5
                    // position -168, 0, 168, y 50 -> for 3
                    // position -100, 100, y 50 -> for 2
                    let tmpRowContainer: cc.Node[][] = [];
                    for (let i = 0; i < sidePot.length; i++) {
                        let currentRowIndex = Math.floor(i / columnLimit);
                        let currentColumnIndex = i % columnLimit;

                        let tmpColumnContainer: cc.Node[];
                        if (currentColumnIndex == 0) {
                            //New column container
                            tmpColumnContainer = [];
                            //Add to row container
                            tmpRowContainer[currentRowIndex] = tmpColumnContainer;
                        } else if (currentColumnIndex > 0 && currentColumnIndex <= 5) {
                            tmpColumnContainer = tmpRowContainer[currentRowIndex];
                            if (tmpColumnContainer.length == 0) {
                                //Error
                                cc.vv.ConsoleLog.log("[Error] did not have any Node in a row [" + currentRowIndex + "] item of current column index: " +
                                    currentColumnIndex + ".\n Column Length:" + tmpColumnContainer.length);
                            }
                        }

                        let obj;
                        if (i < this.sidePotList.length) {
                            obj = this.sidePotList[i];
                            obj.getComponent("Holdem_Stake_ts").stakeMode = 0;
                        } else {
                            obj = this.sidePotObjList[i];
                            obj.active = true;
                            obj.parent = sidePotParent;
                            obj.position = cc.Vec2.ZERO;
                            obj.opacity = 0;
                            this.sidePotList[i] = obj;
                        }
                        cc.vv.ConsoleLog.log("Add side pot "+(i+1)+" ($"+sidePot[i]+") to Row["+currentRowIndex+"], Column["+currentColumnIndex+"]")
                        tmpColumnContainer[currentColumnIndex] = obj;
                        obj.getComponent("Holdem_Stake_ts").updateCoinModeInfo(this.coinMode, denominator, this.store.isShortDeck);
                        obj.getComponent("Holdem_Stake_ts").value = sidePot[i];
                        cc.vv.ConsoleLog.log("Set side pot: $"+obj.getComponent("Holdem_Stake_ts").value);
                    }

                    //Set last pot
                    this.sidePotList[this.sidePotList.length-1].getComponent("Holdem_Stake_ts").stakeMode = 2;

                    //set first pot
                    this.sidePotList[0].getComponent("Holdem_Stake_ts").stakeMode = 1;

                    //Animate all sidePot(s)
                    for (let iy = 0; iy < tmpRowContainer.length; iy++) {
                        let tmpCloumnContain = tmpRowContainer[iy];
                        let cloumnMode = tmpCloumnContain.length;
                        cc.vv.ConsoleLog.log("ColumnMode: "+cloumnMode);
                        for (let ix = 0; ix < tmpCloumnContain.length; ix++) {
                            let obj: cc.Node = tmpCloumnContain[ix];
                            let targetPos: cc.Vec2 = cc.Vec2.ZERO;

                            targetPos.x = (((cloumnMode % 2 == 0) ? dx / 2 : 0) + Math.floor((cloumnMode - 1) / 2) * dx) * -1 + ix * dx;
                            targetPos.y = iy * dy;

                            cc.vv.ConsoleLog.log("[" + iy + "][" + ix + "] pos: " + targetPos);

                            obj.stopAllActions();
                            obj.runAction(
                                    cc.spawn(
                                        cc.fadeTo(animateduration, 255),
                                        cc.scaleTo(animateduration, sidePotScale, sidePotScale),
                                        cc.moveTo(animateduration, targetPos)
                                ));
                        }
                    }
                }else{
                    if (this.sidePotList.length == sidePot.length){
                        for (let i = 0; i < sidePot.length; i++) {
                            cc.vv.ConsoleLog.log('update Pot Value', this.sidePotList[i].getComponent("Holdem_Stake_ts").value, sidePot[i]);
                            this.sidePotList[i].getComponent("Holdem_Stake_ts").updateCoinModeInfo(this.coinMode, denominator, this.store.isShortDeck);
                            this.sidePotList[i].getComponent("Holdem_Stake_ts").value = sidePot[i];
                        }
                    }
                }

                // if (this.mainPotStake.node.opacity > 0) {
                //     this.mainPotStake.node.stopAllActions();
                //     this.mainPotStake.node.runAction(
                //         cc.sequence(
                //             cc.delayTime(0.3),
                //             cc.fadeTo(animateduration, 0)
                //         )
                //     );
                // }

            } else {
                // Reset pot when side pot length = 0 or 1
                this.mainPotStake.node.stopAllActions();
                this.mainPotStake.node.runAction(
                    cc.fadeTo(0.2, 255)
                );

                for (let i = 0; i < this.sidePotList.length; i++) {
                    let obj = this.sidePotList[i];
                    obj.stopAllActions();
                    obj.runAction(cc.sequence(cc.fadeTo(0.2, 0), cc.callFunc(() => {
                        obj.active = false;
                    })));
                }

                this.sidePotList = [];
            }
        }
    }

    @render
    RefreshGameRecordsPlayerName(){
        if (this.store){
            const {seatedPlayers} = this.store;
            if (seatedPlayers!==undefined){
                let list:any[] = [];

                for(let seat in seatedPlayers) {
                    let data = {userId:-1, nickName: ""};
                    data.userId = seatedPlayers[seat].userId;
                    data.nickName = seatedPlayers[seat].nickName;

                    list.push(data);
                }
                // if (this.gameRecord.node.activeInHierarchy){
                //     this.gameRecord.refreshRecordPlayerName(list);
                // }
            }
        }
    }

    @render
    TournamentRoomInfo() {
        if (this.store){
            const {prizePool, bountyPool, prizeCircle} = this.store;
            // this.tournamentRecord.setRoomInfoLayout(this._gameMode);
            this.tournamentRecord.updatePrizePool(prizePool);
            this.tournamentRecord.updateBountyPool(bountyPool);
            this.tournamentRecord.updatePrizeCircle(prizeCircle);
        }
    }

    @render
    TournamentPrizeList() {
        if (this.store){
            const {prizeList} = this.store;
            cc.vv.ConsoleLog.log("Tournament Record Update Reward List", prizeList);
            this.tournamentRecord.rewardPage.updateRankingRewardList(prizeList);
        }
    }

    @render
    TournamentBlindList() {
        if (this.store){
            const {rbcList} = this.store;

            if (rbcList.length > 0){
                cc.vv.ConsoleLog.log("Tournament Record Update Blind List");
                this.tournamentRecord.blindLevelPage.updateBlindLevelList(rbcList);
            }
        }
    }

    TournamentTableList() {
        if (this.store){
            const {mttTablesDetail} = this.store;

            if (mttTablesDetail && mttTablesDetail.length > 0){
                cc.vv.ConsoleLog.log("Tournament Record Update Tables", mttTablesDetail.length);
                this.tournamentRecord.tablePage.updateTable(mttTablesDetail.sort((a,b) => a.TableId - b.TableId));
            }
        }

    }

    @render
    CountDownChanged() {
        if (this.store){
            const {countdownSeat, countdownLastSeat, countdownTtl, countdownLeft} = this.store;
            if(countdownLastSeat) {
                this._players[countdownLastSeat - 1].timer.stopTimer();
            }
            if(countdownSeat) {
                const p = this._players[countdownSeat - 1];
                //todo round result will trigger here
                // cc.vv.ConsoleLog.log('start Timer', countdownSeat, countdownTtl, countdownLeft);
                cc.vv.ConsoleLog.log('check snapshot here', p);
                p.timer.startTimer(countdownTtl, countdownLeft, p.store.timeBank);
                cc.vv.ConsoleLog.log('countDown', countdownSeat);
                if((p.store as holdemSelfStore).showControl) {
                    cc.vv.ConsoleLog.log('log check control',this.playerControl._preClickCall, this.playerControl._preClickFold);
                    // p.tags.leftTag.spriteFrame = null;
                    // p.tags.rightTag.spriteFrame = null;
                    this.TogglePlayerControl(p.store as holdemSelfStore);
                }
            }
        }
    }

    @render
    PlayerControl(){
        if (this.store){
            const {self} = this.store;
            if (!self){
                this.CheckPlayerControlPanel();
            }
            if (!this.isReplay){
                if(MultipleGame.instance && !MultipleGame.instance.shouldShowHeader() && self){
                    this.addTableButton.node.active = true;
                }else{
                    this.addTableButton.node.active = false;
                }
            }
        }
    }

    @render
    PlayerChanged() {
        cc.vv.ConsoleLog.log("PlayerChanged roomId", this.store);
        if (this.store){
            const {seatedPlayers, roomId} = this.store;
            cc.vv.ConsoleLog.log("PlayerChanged roomId, seatedPlayers", roomId, seatedPlayers, this.playerIds);
            if(roomId) {
                let seat:string;
                for(seat in seatedPlayers) {
                    if(seatedPlayers[seat] && this.playerIds[seat] != seatedPlayers[seat].userId) {
                        const p = seatedPlayers[seat];
                        this.playerIds[seat] = p.userId;
                        this.setPlayerToSeatWithStore(parseInt(seat), p, false);
                        this.updateLivePlayer();
                    }
                }
                for(seat in this.playerIds) {
                    if(seatedPlayers[seat] == undefined) {
                        this.playerLeaveSeat(parseInt(seat));
                        // this.updateLivePlayer();
                    }
                }
                this.updatePlayerSeats();
            }
        }
    }

    @render
    HandCard(){
        if (this.store){
            const {allBoardCards,self} = this.store;
            // cc.vv.ConsoleLog.log('handcard check self', self);
            if (self!=undefined && (!this.store.isReplay || !MultipleGame.instance)){

                const {cards, state, seatNum,hasCards} = self;

                let text = this.cardType.children[0].getComponent(cc.Label);

                if (this.store.waitForStart || ( !hasCards)){

                    this.cardType.active = false;
                    text.string = '';
                    return;
                }else{
                    this.cardType.active = true;
                }

                if (state==4 || self.hasFold){

                    text.string = Translate(Translation.POKER_HAND_RANKINGS.FOLD);
                    // cc.vv.ConsoleLog.log('handCard check 841',text.string);
                    return;
                }

                let rank:number=0;
                let totalCards = [...allBoardCards, ...cards];
                if (cards.length==4){
                    let pp = [];

                    for (var i =0;i<allBoardCards.length;i++){
                        for (var k=i+1; k<allBoardCards.length;k++){
                            for (var j=k+1;j<allBoardCards.length;j++){
                                pp.push([allBoardCards[i],allBoardCards[k],allBoardCards[j]]);
                            }
                        }
                    }

                    let ps=[];

                    for (var i=0;i<cards.length;i++){
                        for (j=i+1; j<cards.length;j++){
                            ps.push([cards[i],cards[j]]);
                        }
                    }

                    if (pp.length>=1){
                        for (var i=0; i<pp.length;i++){
                            for (var j=0;j<ps.length;j++){
                                let cardArr = [...pp[i],...ps[j]];
                                rank = Math.max(this.checkCardType(cardArr), rank);
                            }
                        }
                    } else {
                        for (var j=0;j<ps.length;j++){
                            rank = Math.max(this.checkCardType(ps[j]), rank);
                        }
                    }



                } else{
                    rank = this.checkCardType(totalCards);
                }

                let _self = this;
                this.handCardDelay = function(){
                    if (_self.cardType!==null){
                        _self.cardType.active = (totalCards && totalCards.length>0);
                    }

                    cc.vv.ConsoleLog.log('check card type final rank -> ',rank);
                    switch (rank) {
                        case 1:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.HIGH_CARD);
                            break;
                        case 2:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.PAIR);
                            break;
                        case 3:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.TWO_PAIRS);
                            break;
                        case 4:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.THREE_OF_A_KIND);
                            break;
                        case 5:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.STRAIGHT);
                            break;
                        case 6:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.FLUSH);
                            break;
                        case 7:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.FULL_HOUSE);
                            break;
                        case 8:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.FOUR_OF_A_KIND);
                            break;
                        case 9:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.STRAIGHT_FLUSH);
                            break;
                        case 10:
                            text.string = Translate(Translation.POKER_HAND_RANKINGS.ROYAL_FLUSH);
                            break;
                    }
                    // cc.vv.ConsoleLog.log('handCard check 927',text.string);
                };
                this.scheduleOnce(this.handCardDelay,1.2);
            }else{
                this.cardType.active = false;
            }
        }
    }

    @render
    GameMenu(){
        if (this.store){
            const {roomSnapshotState,sngStatus,self} = this.store;
            if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.MTT){
                const noSelfPlayer = this._players.filter(p => p.store && this.store.playerUserId == p.store.userId).length === 0;
                if (this.store.MTTDetail!==undefined){
                    cc.vv.ConsoleLog.log(841, noSelfPlayer, this.store.leftRejoinCount, this.store.MTTDetail.LevelStopSignup, this.store.currentBlindLevel);
                    if ((this.store.isRebuyAllow && this.rebuyLeftTime > 0) || (this.store.leftRejoinCount && noSelfPlayer && (this.store.currentBlindLevel < this.store.MTTDetail.LevelStopSignup))){
                        this.menu.updateReliveButton(true);
                    } else{
                        this.menu.updateReliveButton(false);
                    }
                }else{
                    cc.vv.ConsoleLog.log(1912,'GameMenu no mttDetail....', noSelfPlayer, this.store.leftRejoinCount, (this.store.MTTDetail)?this.store.MTTDetail:"no MTT DETAIL");
                    this.menu.updateReliveButton(false);
                }
                this.menu.reliveButton.active = true;
                this.menu.cancelApplyButton.active = false;
                // this.menu.updateReliveButton(true);

                /*if (!this.isReplay && this.store.getSeatedPlayerByUserId(this.store.playerUserId)) {
                    // this.FilterGuessHandControlButton(); pkw no guess hand
                }*/
            } else if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF || this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG){
                this.menu.updateCancelJoinButton(false);
                // if (sngStatus>=1){
                //     this.menu.updateCancelJoinButton(false);
                //     this.roomInfoAlert.hideAlert();
                //     // display SNG self information
                //     this.gameRecord.myRankingValue_sng.node.active = true;
                //     this.gameRecord.playerCountValue_sng.node.active = true;
                //     // enable player use voice message
                //     if (this.store.getSeatedPlayerByUserId(this.store.playerUserId)) {
                //         cc.vv.ConsoleLog.log("Game mic active at snapshot");
                //         // this.gameMicIcon.active = true;
                //         this.enableGameMic(true);
                //     }
                // }else{
                //     if (self && (roomSnapshotState<4 || roomSnapshotState==32)){
                //         this.menu.updateCancelJoinButton(true);
                //     } else{
                //         this.menu.updateCancelJoinButton(false);
                //     }
                // }
            }
        }
    }

    FilterGuessHandControlButton(){
        if (!cc.vv.DataManager.webPlatform && this._gameViewType==globalConfig.MTT_GAME_MODE.NLH) {
            this.guessHand.room = this;
            // if (buildConfig.isPrePro || buildConfig.isPro){
            //     if (buildConfig.isPro){
            //         let whiteList:number[] = [10020, 45765, 10029, 10238, 47406, 131630, 42971];
            //         this.guessHandControl.active = !!whiteList.find(x => x == cc.vv.DataManager.userId);
            //     } else if(buildConfig.isPrePro){
            //         let whiteList:number[] = [934649, 934651, 934653, 934607, 934608, 964609, 934666, 934667, 934669, 944673, 934634];
            //
            //         this.guessHandControl.active = !!whiteList.find(x => x == cc.vv.DataManager.userId);
            //         if (this.guessHandControl.active==false){
            //             cc.sys.localStorage.setItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, 0);
            //         }
            //     }
            // }else{
            //
            //     this.guessHandControl.active = true;
            //
            // }
            // unlock all user to guess hand
            this.guessHandControl.active = true;
            this.UpdateGuessHandControl();
        }
    }

    // @render
    // BoardCardsHighlight() {
    //     const {boardCardsHighlight} = this.store;
    //     if(boardCardsHighlight && boardCardsHighlight.length) {
    //         cc.vv.ConsoleLog.log('highlight', this.publicCardsHandler._cards);
    //         boardCardsHighlight.forEach((highlight, i) => {
    //             if(!highlight){
    //                 this.publicCardsHandler._cards[i].moveToDark(false);
    //             }
    //             else
    //             {
    //                 this.publicCardsHandler._cards[i].moveToShow(false);
    //             }
    //         })
    //     }
        
    // }

    CreateCard(index:number, cardId:number, onlyOneAnimation:boolean)
    {
        let publicCard = this.publicCardsHandler._cards[index];
        while( index > this.publicCardsHandler._tempCardIds.length )
        {
            this.publicCardsHandler._tempCardIds.push(0);
        }
        this.publicCardsHandler._tempCardIds[index] = cardId;

        publicCard.node.active = true;
        if( onlyOneAnimation )
        {
            publicCard._cardId = cardId;
            publicCard.moveToShow(0, ()=>{
                publicCard.sprite.node.active = true;
                publicCard.init(this.holders.publicDealingShoe, this.publicCardsHandler.node);
            },() => {
                this.audioPlayer.playEffect(soundEffect.BoardCardDelivered);
            });
            publicCard.rotateCard(cardId, this.holders.publicDealingShoe.scale, ()=>{

            }, ()=>{

            });
        }
        else
        {
            publicCard._cardId = cardId;
            publicCard.moveToHide(0,() => {
                publicCard.sprite.node.active = true;
                publicCard.init(this.holders.publicDealingShoe, this.publicCardsHandler.node);
            }, ()=>{

            });
            publicCard.rotateCard(cardId, this.holders.publicDealingShoe.scale, ()=>{

            }, ()=>{

            });
            publicCard.moveToShow(0, ()=>{

            }, ()=>{
                this.audioPlayer.playEffect(soundEffect.BoardCardDelivered);
            });
        }
    }
    BoardCardsChangedNoAnimation(allBoardCards:number[], boardCardsHighlight:boolean[])
    {
        this.publicCardsHandler.clearCardImmediate();
        for (let i = 0; i < allBoardCards.length; i++) {
            let card = this.publicCardsHandler._cards[i];
            card.init(this.holders.publicDealingShoe, this.publicCardsHandler.node);
            card.node.active = true;
            card.sprite.node.active = true;
            this.publicCardsHandler._tempCardIds.push(allBoardCards[i]);
            let pos = card._showHolder.position;
            if (card._showHolder.parent != card.node.parent) {
                let worldPos = CommonTools.instance.convertToWorldSpace(card._showHolder, cc.v2(0, 0));
                pos = CommonTools.instance.convertToNodeSpace(card.node.parent, worldPos);
            }
            card.node.position = pos;
            // card.node.rotation = card._showHolder.rotation;
            card.node.angle = card._showHolder.angle;
            card.node.scale = card._showHolder.scale;
            card.node.opacity = 255;
            card.setId(allBoardCards[i]);
        }
        if(boardCardsHighlight && boardCardsHighlight.length) {
            cc.vv.ConsoleLog.log('highlight', this.publicCardsHandler._cards);
            boardCardsHighlight.forEach((highlight, i) => {
                if( !highlight )
                {
                    if( this.publicCardsHandler._cards[i] && this.publicCardsHandler._cards[i].node )
                    {
                        this.publicCardsHandler._cards[i].sprite.node.color = new cc.Color(127.5, 127.5, 127.5, 255);
                    }
                }
            })
        }
    }

    RemoveAllCard()
    {
        for(let i = 0; i < this.publicCardsHandler._cards.length; i++)
        {
            const card = this.publicCardsHandler._cards[i];
            card.node.active = true;
            this.publicCardsHandler._tempCardIds = [];

            card.moveToClear(0, this.holders.publicDealingShoe, ()=>{
                card._cardId = 0;
            }, ()=>{
                card.sprite.node.active = false;
            });
        }
        
    }

    @render
    BoardCardsChanged() {
        if (this.store){
            const {allBoardCards, boardCardsHighlight} = this.store;
            cc.vv.ConsoleLog.log(this.BoardCardsChanged.name, allBoardCards, this.publicCardsHandler._cards, this.publicCardsHandler._tempCardIds, this.needAnimation);
            if( !this.needAnimation )
            {
                this.BoardCardsChangedNoAnimation(allBoardCards, boardCardsHighlight);
            }
            else
            {
                if(allBoardCards.length) {
                    let equalsUntilIndex = this.publicCardsHandler.equalsUntil(allBoardCards);
                    if( equalsUntilIndex == -1 )
                    {
                        this.publicCardsHandler.clearCardImmediate();
                        for (let i = 0; i < allBoardCards.length; i++) {
                            this.CreateCard(i, allBoardCards[i], false);
                        }
                    }
                    else if( allBoardCards.length - equalsUntilIndex > 0 )
                    {
                        let onlyOneAnimation:boolean = (allBoardCards.length - (equalsUntilIndex + 1) == 1);
                        for (let i = equalsUntilIndex + 1; i < allBoardCards.length; i++) {
                            this.publicCardsHandler.clearCardIndexImmediate(i);
                            this.CreateCard(i, allBoardCards[i], onlyOneAnimation);
                        }
                    }
                }
                else
                {
                    this.RemoveAllCard();
                }
                if(boardCardsHighlight && boardCardsHighlight.length) {
                    // cc.vv.ConsoleLog.log('highlight', this.publicCardsHandler._cards);
                    boardCardsHighlight.forEach((highlight, i) => {
                        if(!highlight){
                            this.publicCardsHandler._cards[i].moveToDark(false);
                        }
                    })
                }
            }
        }
    }

    RiseBlind(second?:number) {
        this.riseOpen = false;
        // this.unschedule(this.countDownBlind);
        if (second){
            this.blindTime = second;
        } else{
            this.blindTime = this.store.blindLevelTime;
        }
        //
        // if (currentGameInfo.Detail == undefined){
        //     this.blindTime = 180;
        // }else{
        //
        // }
        // this.schedule(this.countDownBlind,1);
    }

    @render
    UpdateRoomInfo(){
        if (this.store){
            // room name

            // MTT Mode
            if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.MTT){
                this.infoPanel.children[3].getComponent(cc.Label).string = this.store.tournamentRoomName;
                // hide for not update room detail
                // const {MTTDetail} = this.store;
                // this.infoPanel.children[1].children[1].getComponent(cc.Label).string = this.store.currentBlindLevel.toString();
                //
                // // end join level
                // // this.infoPanel.children[2].active = true;
                // if (MTTDetail!==undefined){
                //     if (MTTDetail.LevelStopSignup){
                //         this.infoPanel.children[2].children[1].getComponent(cc.Label).string = MTTDetail.LevelStopSignup.toString();
                //     }
                // }
                //
                //
                // this.infoPanel.children[0].children[0].children[2].getComponent(cc.Label).string = (this.store.roomId%1000).toString();
                //
                // this.roomInfo.anteValue.string = this.NumberFormatStr(this.store.ante).toString();
                //
                // if (this.store.currentBlindLevel>=1){
                //     if (this.store.rbcList.length && this.store.currentBlindLevel!==this.store.rbcList.length){
                //         let cBInfo = this.store.rbcList[this.store.currentBlindLevel];
                //
                //         this.infoPanel.children[6].active = true;
                //         this.infoPanel.children[6].children[1].getComponent(cc.Label).string = (this._gameViewType !== globalConfig.MTT_GAME_MODE.SHORT_DECK)?cBInfo.SmallBlind+"/"+cBInfo.BigBlind:cBInfo.BigBlind;
                //         // this.roomInfo.updateNextBlind(cBInfo.SmallBlind, cBInfo.BigBlind);
                //     } else{
                //         this.infoPanel.children[6].active = false;
                //     }
                // }

            }else{
                this.infoPanel.children[3].getComponent(cc.Label).string = "";
            }


            // this.infoPanel.children[4].children[1].getComponent(cc.Label).string = this.store.ante.toString();
            // current blind
            // this.infoPanel.children[5].active = true;
            // this.infoPanel.children[5].children[1].getComponent(cc.Label).string = this.store.smallBlind+ "/" + this.store.bigBlind;
            this.infoPanel.children[5].active = true;
            this.infoPanel.children[3].active = true;
            if (this._gameViewType == globalConfig.MTT_GAME_MODE.SHORT_DECK){

                this.roomInfo.anteValue.string = this.store.ante.toString();
                this.infoPanel.children[5].children[1].getComponent(cc.Label).string = this.store.bigBlind.toString();
            }else{
                let anteStr = (this.store.ante)?"("+this.store.ante+")":"";
                this.infoPanel.children[5].children[1].getComponent(cc.Label).string = this.store.smallBlind+ "/" + this.store.bigBlind+anteStr;
            }
            //this.roomInfo.updateCurrentBlind(this.store.smallBlind, this.store.bigBlind);
            if( this.isReplay && MultipleGame.instance )
            {
                this.infoPanel.active = false;
                this.roomInfoAlert.node.active = false;

                this.roomInfo.roomName.string = cc.vv.DataManager.replayGameResultDetail.Name;
            }
            // this.currentBlindValue.string = this.store.smallBlind+ "/" + this.store.bigBlind;
        }
    }

    // Dummy
    start() {
        if(!MultipleGame.instance)
        {
            this.startGame();
        }
    }

    startGame() {
        super.startGame();
        this.startDummy();
        this.store.connectWebSocket();

        this._voiceRecorder = new VoiceRecorder(this.store.ws);
        this._voiceRecorder.registerVoiceRecordFinishCallback(this.node);
    }

    startDummy() {

    }

    // Button Events
    onClickMenu() {
        this.GameMenu();
        this.menu.show();
    }

    onClickHistory() {
        this.gameRecord.show();
        this.gameRecord.updateXDeviceLayout(this.isXDevice || this.bottomPadH5);
        //this.store.RoomBill();
        let userId = this.store.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT && this.store.checkIsPlayerSeated(this.store.playerUserId) ? this.store.playerUserId : 0;
        if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG || this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
            if (MTTConnector.instance.isBL){
                this.store.SngRealTimeRecord();
            }
            if (!this.gameRecord.isRequestingGameRecord) {
                const input = {
                    userId: userId,
                    roomId: this.store.roomId,
                    uuid: this.store.uuid
                };
                this.gameRecord.requestHoldemRecordList(input, {after:new Date(this.gameRecord.lastRoundRecordRequestTimestamp), limit:10});
            } else {
                cc.vv.ConsoleLog.log("already requesting HoldemRecordList");
            }
        } else if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.MTT){
            if (!this.store.MTTDetail) {
                // If no MTTDetail, request MTTDetail to enable proper rows of header
                this.store.updateMttDetail();
            }
            // if (this.gameRecord.isShowingTop100Players)
            //     this.store.MttRealTimeRecord(true);
            // else
            //     this.store.MttRealTimeRecord(false);
            if (!this.gameRecord.isRequestingGameRecord) {
                const input = {
                    userId: userId,
                    roomId: userId ? 0 : this.store.roomId,
                    mttId: this.store.tournamentId
                }
                this.gameRecord.requestHoldemRecordList(input, {after:new Date(this.gameRecord.lastRoundRecordRequestTimestamp), limit:10});
            } else {
                cc.vv.ConsoleLog.log("already requesting HoldemRecordList");
            }
        }
    }

    onClickTournament(){
        if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.MTT){
            this.store.MttRealTimeRecord(true);
            this.store.updateMttDetail();
        } else {
            this.store.updateSngRewardInfo();
            this.tournamentRecord.updateBlindLevelList(this.store.rbcList);
            if (!MTTConnector.instance.isBL) {
                this.store.SngRealTimeRecord();
            }
        }
        this.tournamentRecord.show();
    }

    closeEmoji() {
        if (cc.isValid(this.emoticonsList)) {
            this.emoticonsList.getComponent(GameEmoticons).onClose();
        }
    }

    emoticonsNodeOff() {
        if(cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
            AndroidBackButton.getInstance().removeBackFunction("GameEmoticons");
        }
        this.emoticonsNode.off(cc.Node.EventType.TOUCH_END, this.closeEmoji, this);
    }

    onClickEmoji() {
        CommonTools.instance.addButtonCooldown(this.emojiButton);
        this.initPanel(Holdem_Room.ROOM_PANEL.EMOTICONS , () => {
            if(cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
                AndroidBackButton.getInstance().addBackFunction("GameEmoticons", this.closeEmoji.bind(this));
            }
            this.emoticonsNode.on(cc.Node.EventType.TOUCH_END, this.closeEmoji, this);
            this.emoticonsList.active = true;
            // #13510 move to GameEmoticons init, because this initCallback is called after GameEmoticons init. Will have case that GameEmoticons init with 0 animationCount
            // if (this.store.anmiTimes){
            //     this.emoticonsList.getComponent(GameEmoticons).animationCount = this.store.anmiTimes;
            // }

            this.emoticonsList.getComponent(GameEmoticons).show();
        });
        // this.emoticonsList.active = true;
        // this.emoticonsList.runAction(cc.sequence(cc.moveTo(this.emoticonsList.getComponent(GameEmoticons).showingSpeedInSecond, this.emoticonsListStopPos.position),
        //     cc.callFunc(()=>{
        //         this.emoticonsList.position = this.emoticonsListStopPos.position;
        //     }, this)));

    }

    enableGameMic(enable:boolean) {
        if (!enable && this._voiceRecorder && this._voiceRecorder.isRecording) {
            this.onGameMicCanceled();
        }

        this.gameMicIcon.active = enable && cc.sys.isNative && this.store.self && !this.liveHandler.isSelfStreaming();
        cc.vv.ConsoleLog.log("enable game mic", enable, this.gameMicIcon.active);
    }

    onGameMicPressed() {
        cc.vv.ConsoleLog.log("onGameMicPressed");
        if (this._voiceRecorder.isReady && !this.voiceMessageDialog.node.active) {
            if (this.store.voiceInFinal) {
                this._voiceRecorder.startRecorder(() => {
                    this.voiceMessageDialog.show();
                });
            } else {
                cc.vv.ConsoleLog.log("Voice message not allowed in final game");
                this.PlayVoiceMessageAnimation(Translate(Translation.VOICE_MESSAGE.ERROR_FINAL_GAME_NO_VOICE_MESSAGE));
            }
        }
    }

    onGameMicReleased() {
        cc.vv.ConsoleLog.log("onGameMicReleased", this._voiceRecorder.isRecording, this._voiceRecorder.isConverting);
        if (this._voiceRecorder.isRecording && !this._voiceRecorder.isConverting) {
            let duration = (Date.now() - this._voiceRecorder._recordStartTime) / 1000;
            cc.vv.ConsoleLog.log("Voice message duration: " + duration);
            if (duration > 0.5 && duration < 60) {
                this.voiceMessageDialog.playSuccessAnimation();
                switch (this._gameMode) {
                    case globalConfig.GAME_LEVEL_LIST_ID.SNG:
                        this._voiceRecorder.stopRecorderAndUpload(globalConfig.GAME_TYPE.SNG_HOLDEM, this.store.roomId);
                        break;
                    case globalConfig.GAME_LEVEL_LIST_ID.MTT:
                        this._voiceRecorder.stopRecorderAndUpload(globalConfig.GAME_TYPE.MTT_HOLDEM, this.store.roomId);
                        break;
                    case globalConfig.GAME_LEVEL_LIST_ID.AOF:
                        this._voiceRecorder.stopRecorderAndUpload(globalConfig.GAME_TYPE.SNG_HOLDEM, this.store.roomId);
                        break;
                    default:
                        cc.vv.ConsoleLog.log("Unknown Game Type - " + this._gameMode + "...Cancel Audio Record");
                        this._voiceRecorder.cancelRecorder();
                        break;
                }
            } else {
                if (duration < 0.5) {
                    cc.vv.ConsoleLog.log("Voice message too short");
                    this.PlayVoiceMessageAnimation(Translate(Translation.VOICE_MESSAGE.WARNING_TOO_SHORT));
                } else if (duration > 60) {
                    cc.vv.ConsoleLog.log("Voice message too long");
                    this.PlayVoiceMessageAnimation(Translate(Translation.VOICE_MESSAGE.WARNING_TOO_LONG));
                }

                this.onGameMicCanceled();
            }
        }
        else {
            this.onGameMicCanceled();
        }
    }

    onGameMicCanceled() {
        cc.vv.ConsoleLog.log("onGameMicCanceled");
        if (this.voiceMessageDialog.node.active) {
            this.voiceMessageDialog.close();
        }
        this._voiceRecorder.cancelRecorder();
    }

    _timeBankFlag:boolean = false;
    _timeBankClickBlock:boolean= false;
    onTimeBankClick(){
        if (this.store){
            this._timeBankClickBlock=true;
            this.store.TimeBankFlagSetReq(!this._timeBankFlag);
        }
    }

    setTimeBankButton(flag:boolean){
        this._timeBankFlag = flag;
        this._timeBankClickBlock = false;
        this.timeBankBtn.opacity = flag?127:255;
    }

    setTimeBankText(val:string){
        this.timeBankBtn.children[1].getComponent(cc.Label).string = val;
    }



    // onPlayerSettingEnable() {
    //     this.store.sendMttUserInfoReq(this.playerSettingNode.getComponent(ppConfig.PlayerSetting)._player.userId);
    // }
    //
    // onSelfSettingEnable() {
    //     this.store.sendMttUserInfoReq(require(ppConfig.DataManager).userId);
    // }

    /*onPlayerSettingAvatarPressed() {
        this.playerSetting.getComponent(ppConfig.PlayerSetting).hunterBubble._isDisplay = true;
        this.store.sendMttUserInfoReq(this.store.tournamentId, this.playerSetting.getComponent(ppConfig.PlayerSetting)._player.userId);
    }

    onPlayerSettingAvatarReleased() {
        this.playerSetting.getComponent(ppConfig.PlayerSetting).hunterBubble._isDisplay = false;
        this.playerSetting.getComponent(ppConfig.PlayerSetting).hunterBubble.node.active = false;
    }

    onPlayerSettingAvatarCancelled() {
        this.playerSetting.getComponent(ppConfig.PlayerSetting).hunterBubble._isDisplay = false;
        this.playerSetting.getComponent(ppConfig.PlayerSetting).hunterBubble.node.active = false;
    }

    onSelfSettingAvatarPressed() {
        this.selfSetting.getComponent(Holdem_SelfSetting).hunterBubble._isDisplay = true;
        this.store.sendMttUserInfoReq(this.store.tournamentId, require(ppConfig.DataManager).userId);
    }

    onSelfSettingAvatarReleased() {
        this.selfSetting.getComponent(Holdem_SelfSetting).hunterBubble._isDisplay = false;
        this.selfSetting.getComponent(Holdem_SelfSetting).hunterBubble.node.active = false;
    }

    onSelfSettingAvatarCancelled() {
        this.selfSetting.getComponent(Holdem_SelfSetting).hunterBubble._isDisplay = false;
        this.selfSetting.getComponent(Holdem_SelfSetting).hunterBubble.node.active = false;
    }*/

    playVoiceMessage (userId:number) {
        let player = this._players.find((p) => {
            return p?.info?.userId == userId;
        });
        if (player) {
            // if (this.store.voiceMessageList[userId] && !this.store.checkMuteList(userId.toString()) && localConfig.getLocalStorageItem(localConfig.key_enableVoiceMessage, 1) == 1) { // always on with the voiceMsg
            if (this.store.voiceMessageList[userId] && !this.store.checkMuteList(userId)) {
                // stop previous voice message
                this.voiceMessagePlayer.stopVoiceMessage(this.store.voiceMessageList[userId].voiceId);

                this.store.voiceMessageList[userId].voiceId = this.voiceMessagePlayer.playVoiceMessage(this.store.voiceMessageList[userId].voiceClip, () => {
                    cc.vv.ConsoleLog.log(userId + " player voice message finish.");
                    player.voiceBar.active = false;
                });

                if (this.store.voiceMessageList[userId].voiceId > 0) {
                    player.voiceBar.active = true;
                }

                player.voiceBar.getComponent(cc.Animation).play();
            }
        }
    }

    stopVoiceMessage (userId:number) {
        let player = this._players.find((p) => {
            return p?.info?.userId == userId;
        });
        if (player) {
            player.voiceBar.active = false;
            if (this.store.voiceMessageList[userId])
                this.voiceMessagePlayer.stopVoiceMessage(this.store.voiceMessageList[userId].voiceId);
        }
    }

    showGuessHandResult(reward:number, seat:number) {
        cc.vv.ConsoleLog.log('showGuessHandResult', reward, seat);
        if (this.store.self && this._players[seat-1]){
            if (this.store.self.userId == this._players[seat-1].store.userId){
                cc.vv.ConsoleLog.log('showGuessHandResult', this._players[seat-1]);
                this._players[seat-1].showGuessHandResult(reward);
            }
        }else{
            cc.vv.ConsoleLog.log('showGuessHandResult -> no self or player not seat down', '|seat:', seat);
        }
    }

    ToggleGuessHandControl(){
        if (localConfig.getLocalStorageItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, false)) {
            cc.sys.localStorage.setItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, 0);
            // this.showSendIconNotification(Translate(Translation.HOLDEM.GUESS_HAND_CLOSE));
        }else{
            cc.sys.localStorage.setItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, 1);
            // this.showSendIconNotification(Translate(Translation.HOLDEM.GUESS_HAND_OPEN));
            this.guessHand.resetAllSetting();
        }

        this.UpdateGuessHandControl();
        this.CheckPlayerControlPanel()
    }

    UpdateGuessHandControl(){

        // on off off_text
        let guessHandSetting:boolean = (localConfig.getLocalStorageItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, false));
        this.guessHandControl.children[0].active = guessHandSetting;
        this.guessHandControl.children[1].active = !guessHandSetting;
        this.CheckGuessHandActive(guessHandSetting);


    }

    CheckGuessHandActive(active:boolean){
        this.guessHand.node.active = active && this.store.self && !this.store.self.showControl;
        if (active && this.store.self && !this.store.self.showControl && this.store.self.hasCards){
            this.guessHand.PanelActive(active, (active)?this.showSendIconNotification:null);
        } else{
            this.guessHand.PanelActive(false);
        }
    }


    // resetGuessHandBuy flat
    GuessedHandReset(){
        if (this.guessHand){
            this.guessHand._thisRoundGuessed = false;

            if (!this.guessHand.loopSetting){
                this.guessHand.tempMoney = 0;
            }

            this.CheckPlayerControlPanel();
        }

    }

    PlayerGuessCardNextHandPanel(active:boolean, snapshotData:any=null){
        // not handle guess hand in pkw
        if (!cc.vv.DataManager.webPlatform && (this._gameViewType==globalConfig.MTT_GAME_MODE.NLH)){
            if (active && localConfig.getLocalStorageItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, false) ){
                cc.vv.ConsoleLog.log('PL CCMCNR 2654',active);
                // active = (!localConfig.getLocalStorageItem(localConfig.key_enableGuessHand, false))? false:active;

                // need to confirm show or not
                // if (localConfig.getLocalStorageItem(localConfig.key_first_guess_hand, true) && active && !this._firstGuessHandPopUp){
                //     // show guess hand pop up
                //     this._firstGuessHandPopUp = true;
                //     this.store.callPopUpBox(Translate(Translation.HOLDEM.FIRST_GUESS_HAND),()=>{cc.sys.localStorage.setItem(localConfig.key_first_guess_hand, 0);},'我知道了');
                // }



                if (snapshotData && active){
                    this.guessHand.PanelSnapshot(snapshotData);
                } else{
                    this.guessHand.PanelActive(active, (active)?this.showSendIconNotification:null);
                }

            }else{
                this.guessHand.PanelActive(false);
            }
        }
    }

    CheckGuessHandLoop(){
        if (localConfig.getLocalStorageItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, false) && !cc.vv.DataManager.webPlatform && (this._gameViewType==globalConfig.MTT_GAME_MODE.NLH) ){
            if (this.guessHand.loopSetting && cc.vv.DataManager.userData.Gold>= this.guessHand.tempMoney && !this.guessHand._thisRoundGuessed){
                this.store.GuessHandReq(this.guessHand.tempMoney, this.guessHand.currentState+1, true);
            }
        }
    }

    RemoveGuessHandLoop(){
        if (this.guessHand && this.guessHand.loopSetting){
            this.guessHand.removeLoopSetting();
        }
    }

    showPlayerSetting(playerData:any) {
        if (playerData.userId != Holdem_Room.getSelfUserId()) {
            // show player setting
            this.initPanel(Holdem_Room.ROOM_PANEL.PLAYER_SETTING , () => {
                this.store.sendMttUserGameSumInfoReq(playerData.userId);
                // this.playerSettingNode.getComponent(PlayerSetting).hunterBubble.active = false;
                // if (this.store && this.store.MTTDetail && (this.store.MTTDetail.TournamentMode==ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER || this.store.MTTDetail.TournamentMode==ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER)) {
                //     this.playerSettingNode.getComponent(ppConfig.PlayerSetting).hunterBubble.getComponent(HunterValueBlock).setHunterBlockLayout(this.store.MTTDetail.TournamentMode);
                //     this.store.sendMttUserInfoReq(playerData.userId);
                // }
                this.playerSettingNode.active = true;
                this.playerSettingNode.getComponent(PlayerSetting).updatePlayerSetting(playerData);
            });
        } else {
            if (this._gameMode == globalConfig.GAME_LEVEL_LIST_ID.SNG) {
                //not show self setting in JSNG
                return;
            }
            // show self setting
            if (this.isSelfCelebrity()) {
                if (this.isBroadcastAvailable) {
                    this.initPanel(Holdem_Room.ROOM_PANEL.CELEBRITY_SELF_SETTING, () => {
                        this.celebritySelfSettingNode.active = true;
                        this.celebritySelfSettingNode.getComponent(CelebritySelfSetting).updateSelfSetting(playerData);
                    });
                }else{
                    this.initPanel(Holdem_Room.ROOM_PANEL.SELF_SETTING , () => {
                        this.store.sendMttUserGameSumInfoReq(playerData.userId);
                        this.playerSettingNode.active = true;
                        this.playerSettingNode.getComponent(PlayerSetting).updatePlayerSetting(playerData);
                    });
                }
            }else{
                this.initPanel(Holdem_Room.ROOM_PANEL.SELF_SETTING , () => {
                    this.store.sendMttUserGameSumInfoReq(playerData.userId);
                    this.playerSettingNode.active = true;
                    this.playerSettingNode.getComponent(PlayerSetting).updatePlayerSetting(playerData);
                });
            }
        }
    }

    updatePlayerGameSumInfo(msg,isSelf:boolean){
        cc.vv.ConsoleLog.log('pat_check updatePlayerGameSumInfo',msg,isSelf);
        if (cc.isValid(this.playerSettingNode)){
            this.playerSettingNode.getComponent(PlayerSetting).updatePlayerGameSumInfo(msg,isSelf);
        }
    }

    roomSetting(){
        this.initPanel(Holdem_Room.ROOM_PANEL.TABLE_SETTING , () => {
            // this.selfSettingNode.getComponent(Holdem_SelfSetting).hunterBubble.active = false;
            // if (this.store && this.store.MTTDetail && (this.store.MTTDetail.TournamentMode==ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER || this.store.MTTDetail.TournamentMode==ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER)) {
            //     this.selfSettingNode.getComponent(Holdem_SelfSetting).hunterBubble.getComponent(HunterValueBlock).setHunterBlockLayout(this.store.MTTDetail.TournamentMode);
            //
            //     this.store.sendMttUserInfoReq(playerData.userId);
            //     // this.selfSettingNode.on("enable", this.onSelfSettingEnable, this);
            // }
            this.selfSettingNode.active = true;
            // this.selfSettingNode.getComponent(Holdem_SelfSetting).closeRaiseMenu();
            // this.selfSettingNode.getComponent(Holdem_SelfSetting).updateSelfSetting();
        });
    }

    _NotificationMsg: string[] = [];
    setNotificationMsg(msg:string){
        this._NotificationMsg.push(msg);
        if (!this.sendIconNotificationContent.node.parent.active && !this._isShowInMoney){
            this.showSendIconNotification();
        }
    }
    showSendIconNotification = ()=> {
        let animationNode = this.sendIconNotificationContent.node.parent;
        animationNode.zIndex = this.playerSetting.zIndex + 1; // make notification always on top of player setting
        animationNode.active = true;

        this.sendIconNotificationContent.string = this._NotificationMsg.shift();
        animationNode.getComponent(cc.Animation).once('finished',()=>{
            animationNode.active = false;
            if (this._NotificationMsg.length>0){
                this.showSendIconNotification();
            }
        });
        animationNode.getComponent(cc.Animation).play();
    };

    needShowInTheMoney: boolean = false;
    showInMoneyNotifyAtNewRound(){
        if (this.needShowInTheMoney) {
            this.needShowInTheMoney = false;
            this.showInMoneyNotification();
        }
    }

    // onEnterRewardMsg
    _isShowInMoney:boolean=false;
    showInMoneyNotification = ()=> {
        let animationNode = this.inMoneyNotification;
        animationNode.zIndex = this.playerSetting.zIndex + 1; // make notification always on top of player setting
        animationNode.active = true;
        this._isShowInMoney = true;

        this.inMoneyNotificationTitle.string = Translate(Translation.PKW.POPUP.ENTER_REWARD_TITLE);
        this.inMoneyNotificationContent.string = Translate(Translation.PKW.POPUP.ENTER_REWARD_CONTENT);

        animationNode.getComponent(cc.Animation).once('finished',()=>{
            animationNode.active = false;
            this._isShowInMoney = false;
            if (this._NotificationMsg.length>0){
                this.showSendIconNotification();
            }
        });
        animationNode.getComponent(cc.Animation).play();
    };



    startEmoticon(emoji:any) {
        let player = this._players.find(p=>{return p.store ? p.store.userId == emoji.userId : false;});
        if (player) {
            player.playEmoticon(emoji.body);
        }
    }

    startIconAttack(animName: string, animSender: number, animTarget: number) {
        const sender = this._players.find(p=>{return p.store ? p.store.userId == animSender : false;});
        const target = this._players.find(p=>{return p.store ? p.store.userId == animTarget : false;});
        let url = ResourcesLoader.RES_PATH.PREFAB.COMMON.ATTACK_ICON;
        let targetNode = (target.node && target.node.getComponent(Holdem_Player).avatar)?target.node.getComponent(Holdem_Player).avatar.node:target.node;

        if (sender && target) {
            let senderNode = sender.avatar?.node || sender.node;

            ResourcesLoader.instance.loadRes(url, cc.Prefab, null, (err, prefab) => {
                if (!err) {
                    let attackIcon = cc.instantiate(prefab);
                    attackIcon.parent = this.animationLayer;
                    let attackIconScript = attackIcon.getComponent(AttackIcons);
                    if( attackIconScript )
                    {
                        attackIconScript.setHoldemRoom(this);
                        attackIconScript.startAttack(animName, senderNode, targetNode);
                    }
                }
            });
        }else if (cc.vv.DataManager.webPlatform == ProtoBuf.commonProto.PLATFORM.PKW && !sender){
            ResourcesLoader.instance.loadRes(url, cc.Prefab, null, (err, prefab) => {
                if (!err) {
                    let attackIcon = cc.instantiate(prefab);
                    attackIcon.parent = this.animationLayer;
                    let attackIconScript = attackIcon.getComponent(AttackIcons);
                    if( attackIconScript )
                    {
                        attackIconScript.setHoldemRoom(this);
                        attackIconScript.startAttack(animName, this.menuButton.node, targetNode);
                    }
                }
            });
        }

    }

    addMainPot(coin: number) {
        cc.vv.ConsoleLog.log('----------------', this.mainPotStake.value, coin);
        if(coin===null) {
            this.mainPotStake.value = 0;
        }else{
            this.mainPotStake.value += coin;
        }
    }

    // Server Events
    enter() {
        // this.store.ChangeGameView = true;

        const {store} = this;
        const roomId = store.roomId;
        const mainPot = store.mainPot;
        // const sidePots = store.sidePot;
        // const smallBlind = store.smallBlind; // missing
        // const bigBlind = store.bigBlind; // missing

        this._roomId = roomId;
        this.gameRecord.roomId = roomId;
        this.dealerPos = store.dealerPos;
        cc.vv.ConsoleLog.log('dealer debug:', this.dealerPos);
        this.initPlayerNodes();

        // this.roomNumber.string = roomId.toString();

        cc.vv.ConsoleLog.log("Game Mode debug:", this._gameMode);
        if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.SNG || this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.AOF){
            this.infoPanel.children[0].active = false;
            this.infoPanel.children[1].active = false;
            this.infoPanel.children[2].active = false;
            this.infoPanel.children[4].active = false;
            this.infoPanel.children[6].active = false;

            // hide 牌桌列表
            // this.tournamentRecord.gameRecordsBackground.enabled = false;
            // this.tournamentRecord.buttonPanel.active = false;

            // this.gameRecord.setRealTimeRecordLayout(-1); // set default SNG
        }else{
            /*if (this.store.MTTDetail){
                cc.vv.ConsoleLog.log("Set Real Time Record Tournament Mode", this.store.MTTDetail.TournamentMode);
                this.gameRecord.setRealTimeRecordLayout(this.store.MTTDetail.TournamentMode);
            } else {
                cc.vv.ConsoleLog.log("Set Real Time Record Tournament Mode. MTT Detail is not set");
            }*/


            // next blind
            // this.infoPanel.children[6].active = true;
            // this.infoPanel.children[6].children[1].getComponent(cc.Label).string =

            // next blind level countdown
            if (this._gameMode==globalConfig.GAME_LEVEL_LIST_ID.HOLDEM_NORMAL){
                this.infoPanel.children[7].active = false;
            }

        }

        // Playing
        cc.vv.ConsoleLog.log('mainpot debug',mainPot, this.mainPotStake.value);
        this.mainPotStake.value = mainPot;
        // for (const playerData of players) {
        //     const seatNum = playerData.seatNum;
        //     const deskCoin = playerData.deskCoin;
        //     const action = playerData.action;
        //     const state = playerData.state;
        //
        //     let player = this._players[seatNum - 1];
        //     player.stake.value = deskCoin;
        //     if (state != PlayerState.WaitNext) { // 不是等待下一局的玩家
        //         player.cardsHandler.addCard().setToHide();
        //         player.cardsHandler.addCard().setToHide();
        //         player.showAction(action);
        //     }
        // }
        this.updatePot();
        // for (const publicCardId of publicCards) {
        //     let card = this.publicCardsHandler.addCard();
        //     card.setId(publicCardId);
        // }
        // if (watingPlayer.seat) {
        //     let player = this._players[watingPlayer.seat - 1];
        //     player.timer.startTimer(watingPlayer.time, watingPlayer.remainTime);
        // }
    }

    playerLeaveSeat(seatNum:number) {

        let player = this._players[seatNum - 1];

        if (player.store.userId == this.store.playerUserId) {
            cc.vv.ConsoleLog.log("Game mic in-active at leave seat");
            // this.gameMicIcon.active = false;
            this.enableGameMic(false);
            this.emojiButton.node.active = false;
            this.guessHandControl.active = false;
        }

        if (this.isCelebrityPlayer(player)){
            this.removeLivePlayer(player);
        }

        // player.cardsHandler.clearCards(this.holders.dealingShoe, false, player.store? player.store.needAnimation : true);
        player.cardsHandler.clearCardImmediate();

        cc.vv.ConsoleLog.log(333333, this.dealerPos);

        Object.keys(this.playerIds).forEach((seat) => {
            let nSeat = Number(seat);
            if (nSeat==seatNum){
                delete this.playerIds[nSeat];
            }
        });

        player.store = undefined;

        cc.vv.ConsoleLog.log(this.dealerPos!== undefined, this.dealerPos!==0);
        cc.vv.ConsoleLog.log(this.dealerPos!== undefined|| this.dealerPos!==0);

        player.setEmpty(this.dealerPos !== undefined && this.dealerPos!==0);



        this.updatePlayerSeats();
        this.setPlayerLayout(seatNum-1, player, false);

    }

    setPlayerAutoPlay(playerID:number, active:boolean){
        if (cc.vv.DataManager.webPlatform==ProtoBuf.commonProto.PLATFORM.PKW){
            let player = this.getPlayerByUserId(playerID);

            if(player){
                player.setAutoPlayLabel(active);
            }else{
                cc.vv.ConsoleLog.log('player not ready for setPlayerAutoPlay..');
            }

        }
    }

    PlayerActionPanelControl(active:boolean){
        this.playerControl.node.active = !this.store.isReplay && active;
    }

    TogglePlayerControl(player: holdemSelfStore) {
        if( !this.isReplay )
        {
            if(player){
                if (this._gameMode!==globalConfig.GAME_LEVEL_LIST_ID.AOF){
                    if (this.playerControl._preClickFold || this.playerControl._preClickCall){
                        cc.vv.ConsoleLog.log('togglePlayerControl skip', this.playerControl._preClickCall, this.playerControl._preClickFold, (this.playerControl._preClickFold || this.playerControl._preClickCall));
                        const actions = ProtoBuf.holdem.Action;
                        if (this.playerControl._preClickFold){
                            // todo -> fold action send
                            cc.vv.ConsoleLog.log('send fold action');
                            // this.store.Action(actions.FOLD,0)
                            this.store.Action( (player.optAction == actions.OPT_FIRST_CHECK || player.optAction==actions.CHECK) ? actions.CHECK : actions.FOLD, 0);
                            this.playerControl._preClickFold = false;
                        }
                        if (this.playerControl._preClickCall){
                            // todo -> 1. call or check, 2. call number
                            cc.vv.ConsoleLog.log('send call action');
                            cc.vv.ConsoleLog.log('tt跟注/過牌', (player.optCoin - player.deskCoin) > 0 ? '跟' : '過', 'call:'+(player.optCoin - player.deskCoin), 'minBet:'+player.minBetCoin, 'desk:'+player.deskCoin);
                            if ((player.optCoin - player.deskCoin)>0){
                                if ((player.optCoin - player.deskCoin)>=player.leftCoin){
                                    this.store.Action( actions.ALL_IN, player.deskCoin);
                                } else{
                                    this.store.Action( actions.CALL, (player.optCoin - player.deskCoin));
                                }
                            }else{
                                this.store.Action( actions.CHECK, 0);
                            }

                            // if((player.optCoin - player.deskCoin) <= player.deskCoin) {
                            //     this.store.Action( player.optAction == actions.OPT_FIRST_CHECK ? actions.CHECK : player.optAction, (player.optCoin - player.deskCoin));
                            // }else{
                            //     cc.vv.ConsoleLog.log('preAllin',player.optCoin, player.deskCoin);
                            //     this.store.Action( actions.ALL_IN, player.deskCoin);
                            // }
                            this.playerControl._preClickCall = false;

                        }
                        this.playerControl.resetPreActionPanel();
                    }else {
                        if( this.store.self && this.store.self.showControl )
                        {
                            if (!cc.vv.DataManager.webPlatform && this._gameViewType==globalConfig.MTT_GAME_MODE.NLH){
                                this.guessHand.PanelActive(false);
                            }

                            this.playerControl.setBuyButtonLabel(this.store.buyTimeCount);

                            cc.vv.ConsoleLog.log("playerControl preActionPanel false",player.state, this.playerControl.allInPanel.active);
                            this.playerControl.preActionPanel.active = false;

                            if(this.playerControl.allInPanel.active){
                                if (player.state!=CUSTOM_PLAYER_STATE.EXTEND_TIME){
                                    this.playerControl.mainPanel.active = true;
                                    this.playerControl.allInPanel.active = false;
                                    this.playerControl.showAllInSlide(false);
                                }
                            }else{
                                this.playerControl.mainPanel.active = true;
                                this.playerControl.allInPanel.active = false;
                                this.playerControl.showAllInSlide(false);
                            }
                            // waiting panel hide, control panel show

                            // let potCal:number = this.pot + player.optCoin - player.deskCoin;
                            this.playerControl._smallBlind = this.store.calcSmallBlind;
                            this.playerControl._deskCoin = player.deskCoin;
                            this.playerControl._maxBetCoin = player.maxBetCoin;
                            this.playerControl._optCoin = player.optCoin;
                            this.playerControl.minBetCoin = player.minBetCoin;
                            this.playerControl.lastRaisePos = player.lastRaisePos;

                            // ----- for slider
                            let min = player.minBetCoin;
                            let max = (this.store.maxHoleCard==2)?player.leftCoin:((player.leftCoin<=player.maxBetCoin)?player.leftCoin:player.maxBetCoin);
                            cc.vv.ConsoleLog.log('2438 omaha debug', player.minBetCoin,player.maxBetCoin, min, max, player.optCoin, player.deskCoin);


                            let base = 5;
                            let slideArr:number[] = [0];
                            if (player.deskCoin!==0){
                                min = min+player.deskCoin;
                                max = max+player.deskCoin;
                            }
                            let indexValue = min;
                            let betType = this.playerControl.getBetType(this.isSpin, this.playerControl._betPeriod);
                            if(min<max){
                                slideArr.push(indexValue);
                                if (betType==1){
                                    base = Math.round(this.store.calcBigBlind/10);
                                }
                                while(indexValue<max){
                                    if(indexValue.toString().length>3&&betType!==1){
                                        base = Math.pow(10,indexValue.toString().length-2) / 2;
                                    }
                                    // fix forever loop when base is <= 0
                                    if( base <= 0 )
                                    {
                                        base = 1;
                                    }
                                    if(indexValue%base){
                                        indexValue = indexValue-indexValue%base+base;
                                    }else{
                                        indexValue=indexValue+base;
                                    }
                                    if (max < indexValue){
                                        indexValue = max;
                                    }
                                    slideArr.push(indexValue);
                                }
                            }else{
                                slideArr.push(max);
                            }
                            this.playerControl._lastBetNum = max;
                            cc.vv.ConsoleLog.log('TogglePlayerControl -> slideArr',slideArr.length);
                            // ----- for slider
                            this.playerControl._betPeriod = this.store.allBoardCards.length<3?1:2;
                            this.playerControl._slideNumberArray = slideArr;
                            this.playerControl.pot = this.pot;


                            // cc.vv.ConsoleLog.log('potCal',potCal, 'left:',player.leftCoin, 'optCoin:',player.optCoin, 'desk:',player.deskCoin,'minBet:',player.minBetCoin);
                            // cc.vv.ConsoleLog.log('left:',player.leftCoin, 'optCoin:',player.optCoin, 'desk:',player.deskCoin,'minBet:',player.minBetCoin);

                            this.playerControl.call = player.optCoin - player.deskCoin;
                            this.playerControl.optAction = player.optAction;
                            this.updateControlTimer(player);
                            this.playerControl.remainCoin = player.leftCoin;
                            // cc.vv.ConsoleLog.log('TogglePlayerControl', player.leftCoin, player.optCoin, player.deskCoin, player.minBetCoin);

                        }
                        else
                        {
                            this.CheckPlayerControlPanel();
                            // this.playerControl.mainPanel.active = false;
                            this.playerControl.allInPanel.active = false;
                            this.playerControl.showAllInSlide(false);
                        }
                        
                    }
                } else{
                    if (this.playerControl._preClickFold || this.playerControl._preClickCall){
                        const actions = ProtoBuf.holdem.Action;
                        if (this.playerControl._preClickFold){
                            // todo -> fold action send
                            this.store.Action(actions.FOLD,0)
                            this.playerControl._preClickFold = false;
                            this.playerControl.resetPreActionPanel();
                        }
                        if (this.playerControl._preClickCall){
                            // todo -> 1. call or check, 2. call number
                            this.store.Action( actions.ALL_IN, player.deskCoin);
                            this.playerControl._preClickCall = false;
                            this.playerControl.resetPreActionPanel();
                        }
                    }else {
                        this.CheckPlayerControlPanel();
                        // this.playerControl.mainPanel.active = true;
                        this.playerControl.allInPanel.active = false;
                        this.playerControl.showAllInSlide(false);
                        this.playerControl.remainCoin = player.leftCoin;

                    }
                }

            }else{
                
                // waiting panel show, control panel hide
                // this.playerControl.node.active = false;
                //this.playerControl.preActionPanel.active = this.store.self !== null;
                this.CheckPlayerControlPanel();
                // this.playerControl.mainPanel.active = false;
                this.playerControl.allInPanel.active = false;
            }
        }
        else
        {
            this.playerControl.preActionPanel.active = false;
            this.playerControl.mainPanel.active = false;
            this.playerControl.allInPanel.active = false;
        }

    }
    updatePlayerControlCoinMode(){
        if (this.store){
            const unitStr = FormatParser.GetStackUnit(this.store?.isShortDeck);
            this.playerControl.preCallValue.string = FormatParser.DisplayStack(this._callDiffValue, this.store.calcBigBlind, this.store.calcAnte, this.coinMode, this.store.isShortDeck);
            this.playerControl.callValue.string = FormatParser.DisplayStack(this.playerControl.call, this.store.calcBigBlind, this.store.calcAnte, this.coinMode, this.store.isShortDeck);
            this.playerControl.preCallValueUnit.node.active = this.coinMode == 1 && this._callDiffValue > 0;
            this.playerControl.callValueUnit.node.active = this.coinMode == 1 && this.playerControl.call > 0;
            this.playerControl.preCallValueUnit.string = unitStr;
            this.playerControl.callValueUnit.string = unitStr;

            const denominator = this.store.isShortDeck? this.store.calcAnte : this.store.calcBigBlind;
            this.playerControl.potButtons.forEach((p)=>{
                p.setUnit(this._gameViewType == globalConfig.MTT_GAME_MODE.SHORT_DECK);
                p.updateCoinMode(this.coinMode,denominator);
            });
        }
    }
    updateControlTimer(player:holdemSelfStore){
        this.playerControl.stopAllTimer();
        if (player.optCoin==0 || player.optCoin==player.deskCoin){
            this.playerControl.callTimer.startTimer(player.countdownTtl,player.countdownLeft,0, player.state==CUSTOM_PLAYER_STATE.EXTEND_TIME);
        }else{
            this.playerControl.foldTimer.startTimer(player.countdownTtl,player.countdownLeft,0, player.state==CUSTOM_PLAYER_STATE.EXTEND_TIME);
        }
    }

    // Common
    updatePot() {
        cc.vv.ConsoleLog.log('mainpot debug', this.mainPotStake.value);
        let pot = this.mainPotStake.value;
        for (const player of this._players) {
            pot += player.stake.value;
        }
        this.pot = pot;
    }

    setPlayerToSeatWithStore(seat: number, store: holdemPlayerStore, noAnimation:boolean) {
        let seatIndex = seat - 1;
        let player = this._players[seatIndex];
        player.setStore(store);
        if (Holdem_Room.getSelfUserId() == store.userId && !this.isReplay) {
            let currentSeatIndex = (seatIndex + player._seatOffset) % this.seats;
            this.moveCount = (this.seats - currentSeatIndex) % this.seats;
            cc.vv.ConsoleLog.log('move dealer debug', this._moveCount, this.store.dealerPos, seatIndex, seat, player.layout);

            if (this._moveCount > 0) {
                this.rotate(this._moveCount, noAnimation);
            }
            else
            {
                this.updatePlayersLayout();
            }

            player.layout = PlayerLayoutType.Self;
            if( player.store instanceof holdemSelfStore )
            {
                // this.TogglePlayerControl(player.store as holdemSelfStore);
                this.CheckPlayerControlPanel();
            }
        }
        else
        {
            this.updatePlayersLayout();
        }
        player.cardChanges();
        this.addLivePlayer(player);
    }

    // setPlayerToSeat(seat:number, userId:number, userName:string, profile:string, coin:number, noAnimation:boolean) {
    //     let seatIndex = seat - 1;
    //     let player = this._players[seatIndex];
    //     player.setPlayer(userId, userName, coin, profile);
    //     if (Holdem_Room.getSelfUserId() == userId) {
    //         let currentSeatIndex = (seatIndex + player._seatOffset) % this._players.length;
    //         let moveCount = (this._players.length - currentSeatIndex) % this._players.length;
    //         if (moveCount > 0) {
    //             this.rotate(moveCount, noAnimation);
    //         }
    //         player.layout = PlayerLayoutType.Self;
    //     }
    //
    //     this.updatePlayerSeats();
    // }

    countSameCards(cards:number[]) {
        let sameNum = [];
        let sameCard = [];
        const count:any = cards.reduce((prev:any, curr) => (prev[curr] = ++prev[curr] || 1, prev), {});
        for (let key in count){
            // cc.vv.ConsoleLog.log( count[key], key )
            if (count[key]>1){
                sameNum.push(count[key])
                sameCard.push(parseInt(key))
            }
        }
        return [sameNum, sameCard];
    }

    checkCardType(cards:number[]){
        cards.sort((a,b)=>b-a);

        let readableCard:number[] = [];
        let sameSuit: number[][] = [[],[],[],[]];
        let rank:number = 1;

        for (const card of cards){
            readableCard.push(card&0x0f);

            switch (card&0xf0) {
                case 16:
                    sameSuit[0].push(card&0x0f);
                    break;
                case 32:
                    sameSuit[1].push(card&0x0f);
                    break;
                case 64:
                    sameSuit[2].push(card&0x0f);
                    break;
                case 128:
                    sameSuit[3].push(card&0x0f);
                    break;
            }
        }

        readableCard.sort((a,b)=>b-a);

        let Oj = this.countSameCards(readableCard);
        let sameNum = [];
        let sameCard = [];
        sameNum = Oj[0];
        sameCard = Oj[1];

        // cc.vv.ConsoleLog.log('check3: ',sameNum, sameCard, sameSuit, readableCard);

        if (readableCard.length>4){

            let isSameSuit = false;
            let isStraight = false;
            let sameSuitArr:number[] = [];

            // 花
            for (const i of sameSuit){
                if (i.length>4){
                    isSameSuit = true;
                    sameSuitArr = i;
                    break;
                }
            }

            // 順
            let ckArr:number[] = [];
            if (isSameSuit){
                // use sameSuitArr check
                ckArr = sameSuitArr;
            } else{
                // use readableCard check
                ckArr = readableCard.filter((el, i, a) => i === a.indexOf(el));
            }
            if (ckArr.length>4){
                // check A2345 case
                if (ckArr[0]==14 && ckArr[ckArr.length-4]==5 && ckArr[ckArr.length-3]==4 && ckArr[ckArr.length-2]==3 && ckArr[ckArr.length-1]==2){
                    isStraight = true;
                }
                else if(this.store && this.store._roomMode == ProtoBuf.commonProto.MTT_GAME_MODE.SHORT_DECK && ( ckArr[0]==14 && ckArr[ckArr.length-4]==9 && ckArr[ckArr.length-3]==8 && ckArr[ckArr.length-2]==7 && ckArr[ckArr.length-1]==6 ))
                {
                    isStraight = true;
                }
                else
                {
                    for(var i=0;ckArr.length-i>4;i++){
                        // cc.vv.ConsoleLog.log(ckArr[i],ckArr[i+4]);
                        if (ckArr[i]-ckArr[i+4]==4){
                            isStraight = true;
                        }
                    }
                }
            }

            if (isStraight && isSameSuit){
                // royal flush
                if (ckArr[0]==14 && ckArr[1]==13 && ckArr[2]==12 && ckArr[3]==11 && ckArr[4]==10){
                    rank = Math.max(10, rank);
                } else{
                    rank = Math.max(9, rank);
                }
            } else if (isSameSuit){
                rank = Math.max(6, rank);
            }else if (isStraight){
                rank = Math.max(5, rank)
            }

        }

        if (sameNum.length> 0){
            let total = 0;
            for (const num of sameNum){
                total = total+num;
            }
            // cc.vv.ConsoleLog.log(total);
            switch(total){
                case 2:
                    cc.vv.ConsoleLog.log('pair');
                    rank = Math.max(2, rank);
                    break;
                case 3:
                    cc.vv.ConsoleLog.log('3kind');
                    rank = Math.max(4, rank);
                    break;
                case 4:
                    if(sameNum.length==1){
                        cc.vv.ConsoleLog.log('4kind');
                        rank = Math.max(8, rank);
                    }else{
                        cc.vv.ConsoleLog.log('2pairs');
                        rank = Math.max(3, rank);
                    }
                    break;
                case 5:
                    cc.vv.ConsoleLog.log('fullhouse');
                    rank = Math.max(7, rank);
                    break;
                case 6:
                    if(sameNum.length==2){
                        if(sameNum[0]!==3){
                            cc.vv.ConsoleLog.log('4kind');
                            rank = Math.max(8, rank);
                        }else{
                            cc.vv.ConsoleLog.log('fullhouse')
                            rank = Math.max(7, rank);
                        }
                    }else if(sameNum.length==3){
                        cc.vv.ConsoleLog.log('2pairs')
                        rank = Math.max(3, rank);
                    }
                    break;
                case 7:
                    if (sameNum.length==2){
                        cc.vv.ConsoleLog.log('4kind');
                        rank = Math.max(8, rank);
                    }else if(sameNum.length==3){
                        rank = Math.max(7, rank);
                    }
                    break;
            }

        }

        cc.vv.ConsoleLog.log('check Card Type -> ',readableCard, rank);

        return rank;

    }

    initPlayerNodes() {
        // 將player nodes移動到同一個parent下，支持轉動
        for (const player of this._players) {
            player._startWorldPosition = CommonTools.instance.convertToWorldSpace(player.node, cc.Vec2.ZERO);
            player.node.parent = this.playersHolder;
            player.node.position = CommonTools.instance.convertToWorldSpace(this.playersHolder, player._startWorldPosition);
            player._startLocalPosition = player.node.position;
        }
    }

    setDealerToPlayer()
    {
        if (this.store && this.store.dealerPos){
            this.iconH.moveDealer(CommonTools.instance.convertToWorldSpace(this._players[this.store.dealerPos - 1].node, cc.v2(0, 0)), this._players[this.store.dealerPos - 1]._layout, this._players[this.store.dealerPos - 1]._isUseCelebrityLayout);
        }
    }
    updateDealerToPlayerEndPos(){
        if (this.store && this.store.dealerPos){
            this.iconH.setDealerPosition(CommonTools.instance.convertToWorldSpace(this._players[this.store.dealerPos - 1].node, cc.v2(0, 0)), this._players[this.store.dealerPos - 1]._layout, this._players[this.store.dealerPos - 1]._isUseCelebrityLayout);
        }
    }

    rotateCheck(userID:number, isCenter:boolean){
        let player = this.findPlayerByUserId(userID);
        let seatIndex = player.store.seatNum-1;
        let currentSeatIndex = (seatIndex + player._seatOffset) % this.seats;

        let indexCal = (isCenter)? (this.seats + seatIndex) : (this.seats - currentSeatIndex);

        this.moveCount = (indexCal) % this.seats;
        if (this._moveCount > 0) {
            this.rotate(this._moveCount, false);
        }
    }

    rotate(moveCount:number, noAnimation:boolean) {
        cc.vv.ConsoleLog.log('** UI rotate player', moveCount);
        for (let i = 0; i < this.seats; i++) {
            const player = this._players[i];
            let points = [];
            if (moveCount <= Math.floor(this.seats / 2)) {
                // 順時針
                for (let j = 0; j <= moveCount; j++) {
                    let nextIndex = (i + j + player._seatOffset) % this.seats;
                    let nextPlayer = this._players[nextIndex];
                    points.push(nextPlayer._startLocalPosition);
                }
            }
            else {
                // 逆時針
                for (let j = 0; j <= this.seats - moveCount; j++) {
                    let nextIndex = (i - j + player._seatOffset + this.seats) % this.seats;
                    let nextPlayer = this._players[nextIndex];
                    points.push(nextPlayer._startLocalPosition);
                }
            }
            player._seatOffset += moveCount;
            // if( player.store )
            // {
            //     cc.vv.ConsoleLog.log("rotate player", player.store.userId, player._seatOffset);
            // }
            if (noAnimation) {
                player.node.position = points[points.length - 1];
            }
            else {
                let rot = cc.sequence(
                    cc.catmullRomTo(GameConfig.SEAT_ROTATE_TIME, points).easing(cc.easeInOut(GameConfig.EASE_RATE)),
                    cc.callFunc(()=>{
                        // cc.vv.ConsoleLog.log("Move Dealer after rotate");
                        this.setDealerToPlayer();
                    }, this)
                );
                player.node.runAction(rot);
            }
        }
        this.updatePlayersLayout();
    }

    onTurn(response:any, callNextTurn:boolean) {
        this.onTurnResponseDelay = response;
        let self = this;
        this.onTurnDelay = function(){
            // 隱藏標籤,收集籌碼
            for (const player of self._players) {
                if( callNextTurn )
                {
                    player.nextTurn();
                }
                let playerStake = player.stake.value;
                if (playerStake > 0) {
                    player.stake.value = 0;
                    cc.vv.ConsoleLog.log(1792, self.mainPotStake.value, playerStake);
                    if( self.needAnimation )
                    {
                        // this.mainPotStake.stakeFrom(player.stake.iconNode.convertToWorldSpace(cc.Vec2.ZERO), () => {
                        //     //this.mainPotStake.value += playerStake;
                        // });
                    }
                }
            }

            this.scheduleOnce(response, (GameConfig.PUBLIC_STAKE_MOVE_TIME + GameConfig.DEAL_PUBLIC_CARDS_DELAY_TIME));
        };


        this.scheduleOnce(this.onTurnDelay, (GameConfig.PLAYER_STAKE_MOVE_TIME + GameConfig.COLLECT_STAKE_DELAY_TIME));
    }

    updatePlayersLayout() {
        cc.vv.ConsoleLog.log('update Layout');
        for (let i = 0; i < this._players.length; i++) {
            const player = this._players[i];
            let index = (i + player._seatOffset) % this.seats;
            this.setPlayerLayout(index, player, true);
        }
    }

    setPlayerLayout(index:number, player:Holdem_Player, checkSelf:boolean){
        // cc.vv.ConsoleLog.log('enter setPlayerLayout');
        cc.vv.ConsoleLog.log('pat check setPlayerLayout', this.isReplay);
        if(player.store && this.store && player.store.userId == this.store.playerUserId && checkSelf && (!this.isReplay || !MultipleGame.instance)) {
            player.layout = PlayerLayoutType.Self;
        }else{
            player.layout = PlayerLayoutType.Left;
            if (this.seats==9){
                switch (index) {
                    case 0:
                        player.layout = PlayerLayoutType.Bottom; break;
                    case 1:
                    case 2:
                    case 3:
                        player.layout = PlayerLayoutType.Left; break;
                    case 4:
                    case 5:
                        player.layout = PlayerLayoutType.Top; break;
                    case 6:
                    case 7:
                    case 8:
                        player.layout = PlayerLayoutType.Right; break;
                }
            } else if (this.seats==8) {
                switch (index) {
                    case 0:
                        player.layout = PlayerLayoutType.Bottom; break;
                    case 1:
                    case 2:
                    case 3:
                        player.layout = PlayerLayoutType.Left; break;
                    case 4:
                        player.layout = PlayerLayoutType.Top; break;
                    case 5:
                    case 6:
                    case 7:
                        player.layout = PlayerLayoutType.Right; break;
                }
            } else if (this.seats==7) {
                switch (index) {
                    case 0:
                        player.layout = PlayerLayoutType.Bottom; break;
                    case 1:
                    case 2:
                    case 3:
                        player.layout = PlayerLayoutType.Left; break;
                    case 4:
                    case 5:
                    case 6:
                        player.layout = PlayerLayoutType.Right; break;
                }
            } else if (this.seats==6){
                switch (index) {
                    case 0:
                        player.layout = PlayerLayoutType.Bottom; break;
                    case 1:
                    case 2:
                        player.layout = PlayerLayoutType.Left; break;
                    case 3:
                        player.layout = PlayerLayoutType.Top; break;
                    case 4:
                    case 5:
                        player.layout = PlayerLayoutType.Right; break;
                }
            } else if (this.seats==5){
                switch (index) {
                    case 0:
                        player.layout = PlayerLayoutType.Bottom; break;
                    case 1:
                    case 2:
                        player.layout = PlayerLayoutType.Left; break;
                    case 3:
                    case 4:
                        player.layout = PlayerLayoutType.Right; break;
                }
            } else if (this.seats==4){
                switch (index) {
                    case 0:
                        player.layout = PlayerLayoutType.Bottom; break;
                    case 1:
                        player.layout = PlayerLayoutType.Left; break;
                    case 2:
                        player.layout = PlayerLayoutType.Top; break;
                    case 3:
                        player.layout = PlayerLayoutType.Right; break;
                }
            } else if (this.seats==3){
                switch (index) {
                    case 0:
                        player.layout = PlayerLayoutType.Bottom; break;
                    case 1:
                        player.layout = PlayerLayoutType.Left; break;
                    case 2:
                        player.layout = PlayerLayoutType.Right; break;
                }
            }else if (this.seats==2){
                switch (index) {
                    case 0:
                        player.layout = PlayerLayoutType.Bottom; break;
                    case 1:
                        player.layout = PlayerLayoutType.Top; break;
                }
            }
        }
    }

    updateBlindTime(currentBlindTime:number)
    {
        let actualCurrentBlindTime = Math.max(currentBlindTime, 0);
        const cT = Math.floor(actualCurrentBlindTime/60);
        const cS = actualCurrentBlindTime%60;
        let blindTime = (Array(2).join('0') + cT).slice(-2) +":"+ (Array(2).join('0') + cS).slice(-2);
        this.infoPanel.children[7].children[1].getComponent(cc.Label).string = blindTime;
        if (this.store.blindRiseType == globalConfig.BLIND_RISE_TYPE.DURATION){
            this.tournamentRecord.levelCountdownTimer.string = blindTime;
        }
    }
    switchBlindMode(blindTimeShow:boolean,endRise:boolean = false){
        if (endRise){
            this.infoPanel.children[7].active = false;
            this.infoPanel.children[8].active = false;
        }else{
            this.infoPanel.children[7].active = blindTimeShow;
            this.infoPanel.children[8].active = !blindTimeShow;
        }
    }
    updateBlindHand(leftBlindHand:number){
        cc.vv.ConsoleLog.log('updateBlindHand', leftBlindHand);
        this.infoPanel.children[8].children[1].getComponent(cc.Label).string = leftBlindHand.toString();
        if (this.store.blindRiseType == globalConfig.BLIND_RISE_TYPE.HANDS) {
            this.tournamentRecord.levelCountdownTimer.string = leftBlindHand.toString();
        }
    }

    getPlayerByUserId(userId:number) {
        return this._players.find(p => p.info.userId == userId);
    }

    updatePlayerSeats() {
        const pUserId = this.store.playerUserId;

        const noSelfPlayer = this._players.filter(p => p.store && pUserId == p.store.userId).length === 0;
        for (const player of this._players) {
            player.seat.removeClick();
            player.seat.enableSeat = noSelfPlayer;
        }
    }

    setMaxPlayer(seats: number) {
        if (seats>=5 && seats<=6){
            this.tableBackNode.y = 120;
            if (cc.isValid(this.restMessageLayer)){
                this.restMessageLayer.y = 20;
            }
            if(seats==6 && !this.isXDevice){
                // update table info
                this.playerNodesPos[0].y = -769;
                this.cardType.y = -930;
                this.playerControl.node.y = -665;
            }
        }else{
            this.tableBackNode.y = 0
            if (cc.isValid(this.restMessageLayer)){
                this.restMessageLayer.y = 0;
            }
        }
        switch(seats) {
            case 2:
                this._players[1].node.setPosition((CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)).x + CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[5], cc.Vec2.ZERO)).x) / 2, CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)).y);
                break;
            case 3:
                this._players[1].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[3], cc.Vec2.ZERO)).x, (CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[3], cc.Vec2.ZERO)).y-42));
                this._players[2].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[6], cc.Vec2.ZERO)).x, (CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[6], cc.Vec2.ZERO)).y-42));
                break;
            case 4:
                this._players[1].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[2], cc.Vec2.ZERO)));
                this._players[2].node.setPosition((CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)).x + CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[5], cc.Vec2.ZERO)).x) / 2, CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)).y);
                this._players[3].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[7], cc.Vec2.ZERO)));
                break;
            case 5:
                this._players[1].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[1], cc.Vec2.ZERO)));
                this._players[2].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[3], cc.Vec2.ZERO)));
                this._players[3].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[6], cc.Vec2.ZERO)));
                this._players[4].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[8], cc.Vec2.ZERO)));
                break;
            case 6:
                this._players[1].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[1], cc.Vec2.ZERO)).x, (CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[1], cc.Vec2.ZERO)).y+82));
                this._players[2].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[3], cc.Vec2.ZERO)).x, (CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[3], cc.Vec2.ZERO)).y-42));
                this._players[3].node.setPosition((CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)).x + CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[5], cc.Vec2.ZERO)).x) / 2, CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)).y);
                this._players[4].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[6], cc.Vec2.ZERO)).x, (CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[6], cc.Vec2.ZERO)).y-42));
                this._players[5].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[8], cc.Vec2.ZERO)).x, (CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[8], cc.Vec2.ZERO)).y+82));
                break;
            case 7:
                this._players[1].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[1], cc.Vec2.ZERO)));
                this._players[2].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[2], cc.Vec2.ZERO)));
                this._players[3].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[3], cc.Vec2.ZERO)));
                this._players[4].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[6], cc.Vec2.ZERO)));
                this._players[5].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[7], cc.Vec2.ZERO)));
                this._players[6].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[8], cc.Vec2.ZERO)));
                break;
            case 8:
                this._players[1].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[1], cc.Vec2.ZERO)));
                this._players[2].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[2], cc.Vec2.ZERO)));
                this._players[3].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[3], cc.Vec2.ZERO)));
                if (!cc.sys.isNative){
                    this._players[4].node.zIndex = 1;
                }
                this._players[4].node.setPosition((CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)).x + CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[5], cc.Vec2.ZERO)).x) / 2, CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)).y);
                this._players[5].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[6], cc.Vec2.ZERO)));
                this._players[6].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[7], cc.Vec2.ZERO)));
                this._players[7].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[8], cc.Vec2.ZERO)));
                break;
            case 9:
                this._players[1].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[1], cc.Vec2.ZERO)));
                this._players[2].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[2], cc.Vec2.ZERO)));
                this._players[3].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[3], cc.Vec2.ZERO)));
                this._players[4].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[4], cc.Vec2.ZERO)));
                this._players[5].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[5], cc.Vec2.ZERO)));
                this._players[6].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[6], cc.Vec2.ZERO)));
                this._players[7].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[7], cc.Vec2.ZERO)));
                this._players[8].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[8], cc.Vec2.ZERO)));
                break;
        }

        this._players[0].node.setPosition(CommonTools.instance.convertToNodeSpace(this.playersHolder, CommonTools.instance.convertToWorldSpace(this.playerNodesPos[0], cc.Vec2.ZERO)));

        for(let i = 0; i < 9; i++) {
            this._players[i]._startLocalPosition = this._players[i].node.position;
            this._players[i].node.active = i < seats;
        }

    }

    setMaxPlayerPOPUPReplay(seats: number) {

        let positionArr:cc.Vec2[]= [
            cc.v2(540,650),
            cc.v2(120,860),
            cc.v2(-120,1100),
            cc.v2(-120,1400),
            cc.v2(150,1600),
            cc.v2(930,1600),
            cc.v2(1200,1400),
            cc.v2(1200,1100),
            cc.v2(960,860),
            cc.v2(150,1600)
        ];
        switch(seats) {
            case 2:
                this._players[1].node.setPosition(positionArr[9]);
                break;
            case 3:
                this._players[1].node.setPosition(positionArr[2]);
                this._players[2].node.setPosition(positionArr[7]);
                break;
            case 4:
                this._players[1].node.setPosition(positionArr[2]);
                this._players[2].node.setPosition(positionArr[9]);
                this._players[3].node.setPosition(positionArr[7]);
                break;
            case 5:
                this._players[1].node.setPosition(positionArr[1]);
                this._players[2].node.setPosition(positionArr[3]);
                this._players[3].node.setPosition(positionArr[6]);
                this._players[4].node.setPosition(positionArr[8]);
                break;
            case 6:
                this._players[1].node.setPosition(positionArr[1]);
                this._players[2].node.setPosition(positionArr[3]);
                this._players[3].node.setPosition(positionArr[9]);
                this._players[4].node.setPosition(positionArr[6]);
                this._players[5].node.setPosition(positionArr[8]);
                break;
            case 7:
                this._players[1].node.setPosition(positionArr[1]);
                this._players[2].node.setPosition(positionArr[2]);
                this._players[3].node.setPosition(positionArr[3]);
                this._players[4].node.setPosition(positionArr[6]);
                this._players[5].node.setPosition(positionArr[7]);
                this._players[6].node.setPosition(positionArr[8]);
                break;
            case 8:
                this._players[1].node.setPosition(positionArr[1]);
                this._players[2].node.setPosition(positionArr[2]);
                this._players[3].node.setPosition(positionArr[3]);
                if (!cc.sys.isNative){
                    this._players[4].node.zIndex = 1;
                }
                this._players[4].node.setPosition(positionArr[9]);
                this._players[5].node.setPosition(positionArr[6]);
                this._players[6].node.setPosition(positionArr[7]);
                this._players[7].node.setPosition(positionArr[8]);
                break;
            case 9:
                this._players[1].node.setPosition(positionArr[1]);
                this._players[2].node.setPosition(positionArr[2]);
                this._players[3].node.setPosition(positionArr[3]);
                this._players[4].node.setPosition(positionArr[4]);
                this._players[5].node.setPosition(positionArr[5]);
                this._players[6].node.setPosition(positionArr[6]);
                this._players[7].node.setPosition(positionArr[7]);
                this._players[8].node.setPosition(positionArr[8]);
                break;
        }

        this._players[0].node.setPosition(positionArr[0]);

        for(let i = 0; i < 9; i++) {
            this._players[i]._startLocalPosition = this._players[i].node.position;
            this._players[i].node.active = i < seats;
        }

    }

    

    onClickTopUpOnOtherPage(returnNode:cc.Node,callback?:()=>any)
    {
        this.blockLayer.active = true;
        if(!this._topUpPage){
            let temp = cc.instantiate(this.topUpPrefab);
            temp.parent = this.layer;
            this._topUpPage = temp.getComponent(TopUpPage);
            this._topUpPage.init(this);
            // this._topUpPage.setPosition(this.position[2].position);
        }
        // cc.vv.ConsoleLog.log("onClickTopUpOnOtherPage 1");
        // this.scheduleOnce(()=>{
        //     cc.vv.ConsoleLog.log("onClickTopUpOnOtherPage 2");
        //     if(callback){
        //         callback();
        //     }
        //     this.blockLayer.active = false;
        //     this._topUpPage.addWebView();
        // },this.pageActionSpeed*1.1);
        
        // setTimeout(()=>{
        //     if(callback){
        //         callback();
        //     }
        //     this.blockLayer.active = false;
        //     this._topUpPage.addWebView();
        // },this.pageActionSpeed*1100);
        this._topUpPage._returnNode = returnNode;
        this.movePageFromRight(this._topUpPage.node,returnNode, false, ()=>{
            // cc.vv.ConsoleLog.log("onClickTopUpOnOtherPage 2");
            this.blockLayer.active = false;
            if(callback){
                callback();
            }
            
            this._topUpPage.addWebView();
        });
    }   

    setPos(page:cc.Node,position:cc.Vec2,enable:boolean = false){
        page.setPosition(position);
        this.setWidget(page,enable);
        page.width = this.node.width;
        page.height = this.node.height;
    }
    setWidget(page:cc.Node,enable:boolean){
        let widget = page.getComponent(cc.Widget);
        widget.enabled = enable;
        if(enable){
            // widget.bottom = 0;
            // widget.top = 0;
            widget.left = 0;
            widget.right = 0;
            widget.updateAlignment();
        }
    }

    movePageFromRight(pageNode:cc.Node,hidPage:cc.Node = this.node,hide:boolean = true,callback?:()=>any){
        // pageNode.setPosition(this.position[2].position);
        if(!cc.isValid(pageNode)||!cc.isValid(hidPage)){
            if(callback && callback instanceof Function){
                callback();
            }
            return;
        }
        this.setPos(pageNode,this.position[2].position);
        pageNode.active = true;
        let action = cc.moveTo(this.pageActionSpeed,this.position[1].position).easing(cc.easeSineInOut());
        pageNode.runAction( cc.sequence(action,cc.callFunc(()=>{
            pageNode.position = this.position[1].position;
            this.setWidget(pageNode,true);
            if(callback && callback instanceof Function){
                callback();
            }
            if(hide && hidPage.active==true){
                hidPage.active = false;
            }
        })));
    }
    movePageToRight(pageNode:cc.Node,hidPage:cc.Node = this.node,callback?:()=>any){
        if(!cc.isValid(pageNode)||!cc.isValid(hidPage)||!pageNode.active){
            if(callback && callback instanceof Function){
                callback();
            }
            return;
        }
        if(hidPage.active == false){hidPage.active = true;}
        // hidPage.setPosition(this.position[1].position);
        this.setPos(hidPage,this.position[1].position,true);
        // this.setPos(pageNode,this.position[1].position);
        let action = cc.moveTo(this.pageActionSpeed,this.position[2].position).easing(cc.easeSineInOut());
        this.setWidget(pageNode,false);
        pageNode.runAction( cc.sequence(action,cc.callFunc(()=>{
            pageNode.position = this.position[2].position;
            if(callback){
                callback();
            }
            pageNode.active = false;
        })));
    }

    initShowLoading(handleLoadingTimeout?:()=>any,maxLoadingTimeInSecond = 60){
        // if( !this.store.ws || !this.store.ws.stopAllConnect )
        // {
            if(!this.loading){
                this.loading = cc.instantiate(this.loadingPrefab);
                this.loading.parent = this.loadingLayer;
            }
            if(!(handleLoadingTimeout instanceof  Function)){
                handleLoadingTimeout = ()=>{
                    this.initHideLoading();
                    if(this.blockLayer.active === true){
                        this.blockLayer.active = false;
                    }
                    // cc.vv.ConsoleLog.log("check game view",this.newGameView,this._gameView);
                };
            }
            // cc.vv.DataManager.addLoading(this, handleLoadingTimeout);
            // Bug #3750 cannot get component of null
            if(this.loading) {
                this.loading.getComponent("LoadingBlocker").setLoading(handleLoadingTimeout,maxLoadingTimeInSecond);
            }
            // if(!this.loading.active){this.loading.active = true;}
        // }
    }
    initHideLoading(callback?:()=>any) {
        let self = this;
        this.hideLoadingDelay = function () {
            // cc.vv.DataManager.removeLoading(this);
            // if (self.loading) {
            //     self.loading.active = false;
            // }
            if (callback) {
                callback();
            }
        };
        this.scheduleOnce(this.hideLoadingDelay, this.loadingDelay);
    }

    unscheduleAllRoomScheduler(){
        cc.vv.ConsoleLog.log("unscheduleAllRoomScheduler scheduleOnce");
        this.unscheduleAllCallbacks();
        // if(this.roomMessageDelay) this.unschedule(this.roomMessageDelay);
        // if(this.animationPlayDelay) this.unschedule(this.animationPlayDelay);
        // if(this.koAnimationDelay) this.unschedule(this.koAnimationDelay);
        // if(this.hideLoadingDelay) this.unschedule(this.hideLoadingDelay);
        // if(this.handCardDelay) this.unschedule(this.handCardDelay);
        // if(this.gameStartDelay) this.unschedule(this.gameStartDelay);
        // if(this.handCardDelay) this.unschedule(this.handCardDelay);
        // if(this.gameStartDelay) this.unschedule(this.gameStartDelay);
        // if(this.onTurnDelay) this.unschedule(this.onTurnDelay);
        // if(this.onTurnResponseDelay) this.unschedule(this.onTurnResponseDelay);

        // roomMessageDelay: any = null;   // 3s
        // animationPlayDelay: any = null; // 5s
        // koAnimationDelay:any = null;    // 2s
        // hideLoadingDelay:any = null;    // 0.3s loadingDelay
        // //dealerPosActionInterval       // 0.6s
        // handCardDelay:any = null;       // 1.2s
        // gameStartDelay:any = null;      // 0.3s GameConfig.GAME_START_DELAY_TIME
        // onTurnDelay:any = null;         // 0.2s + 0.1s (GameConfig.PLAYER_STAKE_MOVE_TIME + GameConfig.COLLECT_STAKE_DELAY_TIME)
        // onTurnResponseDelay:any = null; // 0.5s + 0.1s (GameConfig.PUBLIC_STAKE_MOVE_TIME + GameConfig.DEAL_PUBLIC_CARDS_DELAY_TIME)
    }

    NumberFormatStr(num:number){
        return num;
        /*if(num<1000) {
            return num.toString();
        }else if (num>=1000 && num<1000000){
            return num/1000 +"k";
        }else if (num>=1000000 && num<1000000000){
            return num/1000000+"M";
        }else if (num>=1000000000){
            return num/1000000000 +"B";
        }*/
    }
    //***************** live ****************
    isLiveValid(){
        return cc.isValid(this.liveHandler) && cc.sys.isNative && !this.isReplay && this.store.isCelebrityTournament;
    }
    isSelfLiveAvailable(){
        //condition for live activation
        return this.isSelected();
    }

    findPlayerByUserId(userId:number): Holdem_Player {
        return this._players.find((player) => player.store && player.store.userId == userId);
    }

    initLiveHandler(){
        if (!this.isLiveValid())
            return;
        this.setLiveRoomData();

        cc.vv.ConsoleLog.log(this.store.tournamentId, "holdemLive initLiveHandler");

        this.liveHandler.init(this, this.onLiveInit.bind(this), this.onCleanLivePlayer.bind(this));
    }
    setLiveRoomData(){
        if (!this.isLiveValid() || this.store.roomId == 0)
            return;
        cc.vv.ConsoleLog.log(this.store.tournamentId, "holdemLive setLiveRoomData");

        let userId = cc.vv.DataManager.userId;//self user id
        let channelName = "holdem_"+this.store.roomId;

        this.liveHandler.setRoomData(channelName, userId, this);
    }
    onLiveInit(){
        this.applyMuteListToLivePlayer();
        this.refreshAnnouncer();
    }
    resumeLive(){
        if (!this.isLiveValid())
            return;
        this.liveHandler.playLive();
    }
    stopLive(){
        if (!this.isLiveValid())
            return;
        this.liveHandler.stopLive();
    }
    addAllLivePlayerAvatar(){
        this._players.forEach((player)=> {
            if (player.store && this.store){
                if (this.store.CelebrityList.findIndex(celebrity => celebrity.UserID == player.store.userId) > -1){
                    this.liveHandler.addPlayerAvatar(player.celebrityNode.avatar, player.store.userId);
                }
            }
        });
    }
    getSelfRole(){
        if (this.isSelfCelebrity()){
            return Holdem_Live.ROLE.CELEBRITY;
        } if (this.isAnnouncer(cc.vv.DataManager.userId)){
            return Holdem_Live.ROLE.ANNOUNCER;
        }else {
            return Holdem_Live.ROLE.NORMAL;
        }
    }
    isSelfCelebrity(){
        return this.isCelebrity(cc.vv.DataManager.userId);
    }
    isCelebrity(userId:number){
        if (this.store){
            return this.store.CelebrityList.findIndex(el => el.UserID == userId && el.Role != ProtoBuf.commonProto.CelebrityRole.Commentator) > -1
                && this._players.findIndex(player =>  player.store && player.store.userId == userId) > -1;
        }
        return false;
    }
    isStreamingPlayer(userId:number){
        if (this.store){
            return this.store.streamingPlayers.findIndex(el => el == userId) > -1;
        }
        return false;
    }
    updateLivePlayer(){
        cc.vv.ConsoleLog.log("updateLivePlayer",this.store.tournamentId,this.isSelfCelebrity());
        if (!this.isLiveValid())
            return;
        if (!Holdem_Live.isEngineValid())
            return;
        this.liveHandler.setVideoClientRole(this.getSelfRole());

        //remove not exist live player
        this.clearNotExistCelebrityPlayer();
    }
    clearNotExistCelebrityPlayer(){
        let removeKeys = [];
        Object.keys(this.liveHandler._playerIconMap).forEach(key => {
            if (this._players.findIndex(p => p.store && p.store.userId == this.liveHandler._playerIconMap[key].uid) == -1){
                removeKeys.push(key);
            }
        });
        cc.vv.ConsoleLog.log(this.store.tournamentId, "holdemLive clearNotExistCelebrityPlayer",JSON.stringify(removeKeys));
        removeKeys.forEach(k => {
            this.liveHandler.removePlayerAvatar(parseInt(k));
        });
    }
    refreshLivePlayerAvatar(player:Holdem_Player){
        if (!this.isLiveValid())
            return;
        if (this.isCelebrity(player.store.userId)) {
            this.liveHandler.refreshLivePlayerAvatar(player.celebrityNode.avatar, player.store.userId);
        }
    }
    isCelebrityPlayer(player:Holdem_Player){
        if (this.store && player.store){
            return this.store.CelebrityList.findIndex(el => el.UserID == player.store.userId && el.Role != ProtoBuf.commonProto.CelebrityRole.Commentator) > -1;
        }
        return false;
    }
    isAnnouncer(userId:number){
        if (this.store){
            return this.store.CelebrityList.findIndex(el => el.UserID == userId && el.Role == ProtoBuf.commonProto.CelebrityRole.Commentator) > -1;
        }
        return false;
    }
    addLivePlayer(player:Holdem_Player){
        if (!this.isLiveValid())
            return;
        if (player && player.store) {
            if (player.store.userId == cc.vv.DataManager.userId){
                this.liveHandler.setVideoClientRole(this.getSelfRole());
            }
            if (this.isCelebrity(player.store.userId) && this.isStreamingPlayer(player.store.userId)){
                if (!this.store.checkCameraMuteList(player.store.userId)) {
                    player.activeCelebrityLayout(true);
                }

                this.liveHandler.addPlayerAvatar(player.celebrityNode.avatar, player.store.userId);
            }
        }
    }
    removeLivePlayer(player:Holdem_Player){
        if (!this.isLiveValid())
            return;
        if (player && player.store) {
            if (player.store.userId == cc.vv.DataManager.userId){
                this.liveHandler.isBroadcastValid = false;
            }
            this.liveHandler.removePlayerAvatar(player.store.userId);
            player.activeCelebrityLayout(false);
        }
    }
    setValidBroadcast(valid:boolean, showDialog:boolean = false){
        if (!this.isLiveValid())
            return;
        //show dialog
        if (!valid && showDialog) {
            this.showReachMaxTerminated();
        }
        this.liveHandler.isBroadcastValid = valid;
    }

    showReachMaxCannotStart(){
        this.store.showDialogBox("",Translate("PKW_LIVE.REACH_MAX.CAN_NOT_START"),false,[
            {
                type: 0,
                text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                callback:null,
            }
        ]);
    }
    showReachMaxTerminated(){
        this.store.showDialogBox("",Translate("PKW_LIVE.REACH_MAX.TERMINAT"),false,[
            {
                type: 0,
                text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                callback:null,
            }
        ]);
    }
    applyMuteListToLivePlayer() {
        this._players.forEach((player)=> {
            cc.vv.ConsoleLog.log("holdemLive applyMuteListToLivePlayer",!!player);
            if (player && player.store && this.store && player.store.userId != this.store.playerUserId){
                let userId = player.store.userId;
                if (this.isCelebrity(userId)) {
                    this.liveHandler.muteRemoteVideo(userId, this.store.checkCameraMuteList(userId));
                    this.liveHandler.muteRemoteAudio(userId, this.store.checkMuteList(userId));
                    player.activeCelebrityLayout(!this.store.checkCameraMuteList(userId) && this.isStreamingPlayer(userId));
                }
                else {
                    cc.vv.ConsoleLog.log("holdemLive applyMuteListToLivePlayer player not celebrity",userId);
                }
            }
        });
    }

    StreamPlayerChanged(){
        cc.vv.ConsoleLog.log("holdemLive StreamPlayerChanged",this.isLiveValid());
        if (this.store){
            if (!this.isLiveValid()) {
                return;
            }

            if (!this.liveHandler._joinedChannel){
                this.liveHandler.setVideoClientRole(this.getSelfRole());
                this.liveHandler.joinVideoChannel();
            }

            const {streamingPlayers} = this.store;
            let livePlayer = this.liveHandler.getAllPlayerAvatarKey().map(x => parseInt(x));

            cc.vv.ConsoleLog.log("holdemLive StreamPlayerChanged",streamingPlayers,livePlayer);
            livePlayer.forEach( live => {
                if (streamingPlayers.findIndex(streaming => streaming == live) == -1){
                    this.setCelebrityVideoActive(live, false);
                }
            });
            streamingPlayers.forEach( streaming => {
                if (livePlayer.findIndex(live => live == streaming) == -1){
                    this.setCelebrityVideoActive(streaming, true);
                }
            });
        }
    }
    setCelebrityVideoActive(uid:number, active:boolean){
        cc.vv.ConsoleLog.log("holdemLive setCelebrityVideoActive",uid,active);
        let player = this._players.find(el => el.store && el.store.userId == uid);//this.getPlayerByUserId(uid);
        if (player){
            if (active) {
                this.addLivePlayer(player);
            }else {
                this.removeLivePlayer(player);
            }
        }else {
            cc.vv.ConsoleLog.log("holdemLive setCelebrityVideoActive player not find");

        }
    }
    onMuteCelebrity(uid:number, isMute:boolean){
        cc.vv.ConsoleLog.log("holdemLive onMuteCelebrity",uid,this.isStreamingPlayer(uid));
        let player = this._players.find(el => el.store && el.store.userId == uid);// this.getPlayerByUserId(uid);
        if (player) {
            if (this.isStreamingPlayer(uid)) {
                player.activeCelebrityLayout(!isMute);
            }else {
                player.activeCelebrityLayout(false);
            }
            this.liveHandler.muteRemoteVideo(uid, isMute);
        }else{
            cc.vv.ConsoleLog.log("holdemLive onMuteCelebrity player not find");
        }
    }
    onAppPauseLive(){
        if (!this.isLiveValid())
            return;
        cc.vv.ConsoleLog.log("holdemLive onAppPauseLive");
        this.liveHandler.leaveVideoChannel();
    }
    onAppResumeLive(){
        if (!this.isLiveValid())
            return;
        cc.vv.ConsoleLog.log("holdemLive onAppResumeLive");
        if ((this.isSelfCelebrity() && this.isStreamingPlayer(cc.vv.DataManager.userId))
            || this.isAnnouncer(cc.vv.DataManager.userId)) {
            this.liveHandler.onVideoEngineStateChanged(Holdem_Live.getSdkStatus());
        }
    }
    onCleanLivePlayer(uid:number){
        if (!this.isLiveValid())
            return;
        cc.vv.ConsoleLog.log("holdemLive onCleanLivePlayer");
        let player = this.getPlayerByUserId(uid);
        if (player) {
            this.removeLivePlayer(player);
        }
    }

    StreamAudioPlayerChanged(){
        cc.vv.ConsoleLog.log("holdemLive StreamAudioPlayerChanged",this.isLiveValid());
        if (this.store && this.isLiveValid()){
            const {streamingAudioPlayers} = this.store;
            cc.vv.ConsoleLog.log("holdemLive StreamAudioPlayerChanged",streamingAudioPlayers);

            this._players.forEach(player => {
                if (player) {
                    player.celebrityNode.avatar.activeCelebrityAudioIcon(player.store && streamingAudioPlayers.findIndex(uid => uid == player.store.userId) >-1);
                }
            });
            if (this.isSelfCelebrity()){
                this.liveHandler.enableLocalAudio(streamingAudioPlayers.findIndex(uid => uid == cc.vv.DataManager.userId) > -1);
            }
        }
    }
    updateLivePlayerLayout(isLiveMode:boolean, uiD:number){
        // player position
        let isSelf = (this.store && this.store.self && this.store.self.userId==uiD)
        if (isSelf){
            this.playerControl.updateLiveViewLayout(isLiveMode,this.isXDevice);
        }
        this.updateDealerToPlayerEndPos();
    }
    refreshAnnouncer(){
        cc.vv.ConsoleLog.log("holdemLive refreshAnnouncer", this.isLiveValid(),this.hasAnnouncer());

        if (!this.isLiveValid() || !this.hasAnnouncer()) {
            this.activeAnnouncerButton(false);
            return;
        }
        cc.vv.ConsoleLog.log("holdemLive refreshAnnouncer", this.liveHandler.currentRole,this.keepAnnouncerSetting);
        this.activeAnnouncerButton(true);
        if (this.liveHandler.currentRole == Holdem_Live.ROLE.ANNOUNCER) {
            this.setAnnouncerButtonOnOff(false);
        }else {
            if (!this.keepAnnouncerSetting) {
                this.setAnnouncerButtonOnOff(true);
            }
        }
        this.applyAnnouncerAudio();
    }
    applyAnnouncerAudio(){
        if (!this.isLiveValid())
            return;
        cc.vv.ConsoleLog.log("holdemLive applyAnnouncerAudio", this.liveHandler.currentRole, this.isAnnouncerOn());

        if (this.liveHandler.currentRole == Holdem_Live.ROLE.ANNOUNCER) {
            this.liveHandler.enableLocalAudio(this.isAnnouncerOn());
        }else {
            let announcers = this.getAnnouncerList();
            announcers.forEach(el => {
                this.liveHandler.muteRemoteAudio(el.UserID, !this.isAnnouncerOn());
            });
        }
    }
    get isBroadcastAvailable() {
        return (this.seats == 6 || this.seats == 9) && this.store.isCelebrityTournament;
    }
    isAnnouncerOn(){
        if (this.liveBtnAnnouncer) {
            return this.liveBtnAnnouncer.node.children[0].active;
        }
        return false;
    }
    setAnnouncerButtonOnOff(on:boolean){
        cc.vv.ConsoleLog.log("holdemLive setAnnouncerButtonOnOff", on);

        if (this.liveBtnAnnouncer){
            this.liveBtnAnnouncer.node.children[0].active = on;
            this.liveBtnAnnouncer.node.children[1].active = !on;
        }
    }
    activeAnnouncerButton(active:boolean){
        if (this.liveBtnAnnouncer){
            this.liveBtnAnnouncer.node.active = active;
        }
    }
    onClickAnnouncer(){
        if (!this.isLiveValid())
            return;
        cc.vv.ConsoleLog.log("holdemLive onClickAnnouncer",this.isAnnouncerOn());

        this.keepAnnouncerSetting = true;
        this.setAnnouncerButtonOnOff(!this.isAnnouncerOn());
        this.applyAnnouncerAudio();
    }
    getAnnouncerList(){
        if (this.store){
            return this.store.CelebrityList.filter(el => el.Role == ProtoBuf.commonProto.CelebrityRole.Commentator);
        }
        return [];
    }
    onUpdateMttDetailLive(){
        cc.vv.ConsoleLog.log("holdemLive onUpdateMttDetailLive");

        this.updateLivePlayer();
        this.StreamPlayerChanged();
        this.StreamAudioPlayerChanged();
        this.refreshAnnouncer();
    }
    hasAnnouncer(){
        return this.getAnnouncerList().length > 0;
    }
    //***************** live END ****************

    //override
    onMultipleGameChangePage(multipleGameIndex:number){
        cc.vv.ConsoleLog.log("onMultipleGameChangePage", multipleGameIndex);
        if( multipleGameIndex == this.multipleGameIndex )
        {
            cc.vv.ConsoleLog.log("onMultipleGameChangePage isSelf", multipleGameIndex);
            this.resumeLive();
        }else
        {
            this.stopLive();
        }
    }
    
    matchingGameAnim() {
    }

    stopMatchGameAnim() {
    }

    @render
    onRebuy() {
        if (this.store){
            const {rebuyLeftTime, isRebuyAllow} = this.store;
            this.rebuyLeftTime = rebuyLeftTime;

            this.roomMessageLayer.active = false;

            if(this.rebuyLeftTime > 0){
                if(isRebuyAllow){
                    if(this.store.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT){
                        let signUpPopUp = this.gameEndLayer.getComponentInChildren('SignUpWithToolPopUp');
                        if( signUpPopUp && !this.store.isRebuyShown){
                            signUpPopUp.onCancelClicked();
                            this.store.reBuyPopUp();
                        } else if (!signUpPopUp && !this.store.isRebuyShown){
                            this.store.reBuyPopUp();
                        }
                    }
                } else {
                    this.hideSignUpPopUp();
                }

                this.updateRebuyPostMsg();
                this.schedule(this.countDownRebuy, 0.5);
            }else{
                this.unschedule(this.countDownRebuy);

                // temp fix
               if(this.morebuyLeftTime <= 0){
                    this.roomMessageLayer.active = false;
                    this.hideSignUpPopUp();
               }
            }
        }
    }

    updateRebuyPostMsg() {
        const remainTime = this.getTimeDiff(this.store.rebuyEndTime);
        this.roomMessageLayer.children[0].getComponent(cc.Label).string = cc.js.formatStr(Translate(Translation.GAME.WAIT_FOR_REBUY_FINISH), remainTime);
        this.roomMessageLayer.active = true;
    }
    
    countDownRebuy() {
        const remainTime = this.getTimeDiff(this.store.rebuyEndTime);
        this.rebuyLeftTime = remainTime;
        if(remainTime > 0){
            this.updateRebuyPostMsg();
        }else{
            this.unschedule(this.countDownRebuy);
            this.roomMessageLayer.active = false;
            this.showRestMessageNodes();
            this.store.resetRebuyAllow();
            // #31510
            this.checkIsRoomEnd();
        }
    }

    checkIsRoomEnd(){
        let roomHasPlayer = false;
        for(let i in this.store.seatedPlayers){
            if(this.store.seatedPlayers[i]){
                roomHasPlayer = true;
            }
        }

        if(!roomHasPlayer){
            this.store.mttEndRoomId = this.store.roomId;
            this.store.loseState[3] = true;
            this.store.loseCondition(3);
        }
    }
    
    @render
    onMorebuy() {
        if (this.store){
            const {morebuyLeftTime, isMorebuyAllow} = this.store;
            this.morebuyLeftTime = morebuyLeftTime;

            if(this.morebuyLeftTime > 0){
                if(isMorebuyAllow){
                    if(this.store.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT){
                        if(!this.gameEndLayer.getComponentInChildren('SignUpWithToolPopUp') && !this.store.isMorebuyShown){
                            this.store.moreBuyPopUp();
                        }
                    }
                }else{
                    this.hideSignUpPopUp();
                }
                
                this.updateMorebuyPostMsg();
                this.schedule(this.countDownMorebuy, 0.5);
            }else{
                this.unschedule(this.countDownMorebuy);
                this.roomMessageLayer.active = false;

                this.hideSignUpPopUp();
            }
        }
    }

    updateMorebuyPostMsg() {
        const remainTime = this.getTimeDiff(this.store.morebuyEndTime);
        this.roomMessageLayer.children[0].getComponent(cc.Label).string = cc.js.formatStr(Translate(Translation.GAME.WAIT_FOR_MOREBUY_FINISH), remainTime);
        this.roomMessageLayer.active = true;
    }

    countDownMorebuy() {
        const remainTime = this.getTimeDiff(this.store.morebuyEndTime);
        
        if(remainTime > 0){
            this.updateMorebuyPostMsg();
        }else{
            this.unschedule(this.countDownMorebuy);
            this.roomMessageLayer.active = false;
            this.showRestMessageNodes();
        }
    }

    hideSignUpPopUp(){
        if(this.store.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT){            
            if(this.gameEndLayer.getComponentInChildren('SignUpWithToolPopUp')){
                this.gameEndLayer.getComponentInChildren('SignUpWithToolPopUp').zoomOut();
            }
        }
    }

    // ---- MysteryBounty Start ------
    _isCreated:boolean = false;
    private _initMysteryBountyFinishedAciton:any = null;
    initMysteryBounty(initFinishedAction:any = null){
        if(initFinishedAction){
            this._initMysteryBountyFinishedAciton = initFinishedAction;
        }
        if(!this._isCreated){ // create once
            this._isCreated = true;
            cc.vv.AssetsManager.loadRes(ResourcesLoader.RES_PATH.PREFAB.HOLDEM.MYSTERY_BOUNTY, cc.Prefab, (err, prefab)=>{
                console.log("load prefab MYSTERY_BOUNTY");
                this.mysteryBountyNode = cc.instantiate(prefab);
                this.mysteryBountyNode.parent = this.mysteryBountyContainer;
                this.mysteryBountyNode.active = true;
                this.mysteryBountyMain = this.mysteryBountyNode.getComponent(MysteryBountyMain);
                this.mysteryBountyMain.holdemRoom = this;
                if(this._initMysteryBountyFinishedAciton){
                    this._initMysteryBountyFinishedAciton();
                }
            });
        }
    }
    playMysteryBountyIntroChestAnim(){
        this.getHoldemPlayers().forEach(holdemPlayer => {
            holdemPlayer.playMysteryBountyIntroChestAnim();
        });
    }

    showMysteryBountyWinValueAnim(num:number){
        this.getHoldemPlayers().forEach(holdemPlayer => {
            holdemPlayer.showMysteryBountyWinValueAnim(num);
        });
    }

    playMysteryBountyGloveHitAnim(eliminator:number[],winnerInfo:any,animFinishCb:any){
        let animDelayTime :number = 1;
        this.mysteryBountyMain.playBellSound();
        if(eliminator && eliminator.length > 0 ){
            eliminator.forEach(userId => {
                this.scheduleOnce(()=>{
                    this.getPlayerByUserId(userId)?.playMysteryBountyGloveHitAnim();
                },animDelayTime)
                animDelayTime += 1;
            });
        }
        animDelayTime += 2;
        this.scheduleOnce(()=>{
            this.mysteryBountyMain.playSingleChest(winnerInfo,animFinishCb);
        },animDelayTime)
    }

    playMysteryBountyGloveHitTestAnim(seatNum:number){
        let playerNode = this.playerNodes[seatNum-1];
        if(playerNode){
            playerNode.getComponent(Holdem_Player)?.playMysteryBountyGloveHitAnim();
        }
    }

    getHoldemPlayers(): Holdem_Player[]{
        let holdemPlayes:Holdem_Player[] = [];
        for(let seat in  this.store.seatedPlayers) {
            if(this.store.seatedPlayers[seat]) {
                const playerStore = this.store.seatedPlayers[seat];
                let playerNode = this.playerNodes[playerStore.seatNum-1];
                if(cc.isValid(playerNode)){
                    holdemPlayes.push(playerNode.getComponent(Holdem_Player));
                }
            }
        }
        return holdemPlayes;
    }
    getHoldemPlayersWithSeatNum(seatNum:number): Holdem_Player{
        let holdemPlayer:Holdem_Player = null;
        let playerNode = this.playerNodes[seatNum - 1];
        if(cc.isValid(playerNode)){
            holdemPlayer = playerNode.getComponent(Holdem_Player);
        }
        return holdemPlayer;
    }


    playMysteryBountyStartIntroAnim(prizeList:any,totalPrize:number){
        if(this.mysteryBountyMain){
            this.mysteryBountyMain.updatePrizeList(prizeList,totalPrize);
            this.mysteryBountyMain.playStartIntroAnim();
        }
    }

    setMysteryBountyPrizeList(prizeList:any,totalPrize:number) {
        if(this.mysteryBountyMain) {
            this.mysteryBountyMain.updatePrizeList(prizeList,totalPrize);
            this.mysteryBountyMain.openBountyPopup();
        }
    }

    showMysteryBountyJackpotGlobalMsg(winnerNames:string[],prize:number){
        this.mysteryBountyMain?.playJackpotAnimation(winnerNames,prize);
    }

    showMysteryBountyEnterTips(msg:any,levelStopSignup:number){
        this.mysteryBountyMain?.showEnterTips(msg,levelStopSignup);
    }
    // ---- MysteryBounty End ------
}
