import {WorldWebSocket} from "./net/worldWebsocket";
import {PpWebSocket} from "./net/ppWebsocket";
import {commonProto, modelProto} from "./pb/commonProto";
import {StatusBar} from "../../prefab/common/StatusBar";
import GPSController from "./GPSController";
import MTT_GAME_MODE = commonProto.MTT_GAME_MODE;
import SngTournament = commonProto.SngTournament;
import MttTournament = commonProto.MttTournament;
import PP_Level = commonProto.PP_Level;
import { globalConfig, localConfig } from "./mttconfig";
import {FormatParser} from "./tools/FormatParser";
import {CustomXMLHttpRequest, httpApis} from "./net/httpApis";
import { ProtoBuf } from "./net/Pb";
import { MultipleGameInfo } from "./game/MultipleGame";
import MTTConnector from "./MTTConnector";

const {ccclass} = cc._decorator;

@ccclass
class DataManager {

    static ERROR_CODE:any = {};
    static ante = 0;
    static gameList = "";
    static token = "";
    static mttId = 0;
    static moreCoin = 0;
    static currentRoomID = 0;
    static reConnectLoading: cc.Node = null;
    static loading:any = null;
    static loadingTargets:any[] = [];
    static addLoading = (target:any,handleLoadingTimeout?:()=>any,maxLoadingTimeInSecond:number = 40) => {
        if(DataManager.loading){
            DataManager.loadingTargets.push(target);
            if(!(handleLoadingTimeout instanceof Function)){
                handleLoadingTimeout = ()=>{
                    DataManager.removeLoading(target);
                };
            }
            DataManager.loading.getComponent("LoadingBlocker").setLoading(handleLoadingTimeout,maxLoadingTimeInSecond);
            DataManager.updateLoading();
        }
    };
    static removeLoading = (target:any,removeAll:boolean=false) => {
        DataManager.loadingTargets = removeAll?[]:DataManager.loadingTargets.filter(t => t != target);
        DataManager.updateLoading();
    };
    static updateLoading = () => {
        if(DataManager.loading) DataManager.loading.active =  DataManager.loadingTargets.length > 0;
    };
    static get worldNetwork(): WorldWebSocket { return WorldWebSocket.getInstance(); };
    static SMSStatus = ["sent","checksum","error","wait","user_exists","user_not_exists","locked"];
    static LanguageSelected = "LanguageSelected";
    static versionNumber = "1.1";

    static getStoredLoginData()
    {
        return null;
        let storedVal = MTTConnector.instance.getStorage(localConfig.key_loginData);
        if( storedVal )
        {
            let json = storedVal;
            let uintArray:number[] = [];
            let i = 0;
            while(typeof json[i] != "undefined")
            {
                uintArray.push(json[i]);
                i++;
            }
            return ProtoBuf.commonProto.User_Login_Response.decode(new Uint8Array(uintArray));
        }
        return null;
    }

    static getStoredMttTournament()
    {
        return null;
        let storedVal = MTTConnector.instance.getStorage(localConfig.key_mttGameLevelList)
        if( storedVal )
        {
            let json = storedVal;
            let uintArray:number[] = [];
            let i = 0;
            while(typeof json[i] != "undefined")
            {
                uintArray.push(json[i]);
                i++;
            }
            return ProtoBuf.commonProto.Game_Level_List.decode(new Uint8Array(uintArray));
        }
    }

    static updateStoredJoinedTournaments()
    {
        return;
        let storedData = DataManager.getStoredLoginData();
        if( storedData )
        {
            storedData.JoinedTournaments = DataManager.JoinedTournaments;
            MTTConnector.instance.setStorage(localConfig.key_loginData, ProtoBuf.commonProto.User_Login_Response.encode(storedData).finish());
        }
    }

    static getDateTimeString(type:string, time:number){
        let nwTime = new Date(time*1000);
        switch (type) {
            case 'date':
                const monthNamesEn = [
                    'January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'
                ];
                const dayNamesEn = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                const dayNamesZh = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

                let month = nwTime.getMonth();
                let date = nwTime.getDate();
                let day = nwTime.getDay();

                if (DataManager.lang == 'sc' || DataManager.lang == 'tc') {
                    return (month + 1) + '月' + date + '日 ' + dayNamesZh[day];
                } else {
                    return dayNamesEn[day] + ', ' + monthNamesEn[month] + ' ' + date;
                }

            case 'time':
                return (nwTime.getHours() < 10 ? '0' : '') + nwTime.getHours() + ':' + (nwTime.getMinutes() < 10 ? '0' : '') + nwTime.getMinutes();
            case 'timezone':
                let timezone = -(nwTime.getTimezoneOffset()/60);
                return "GMT"+(timezone>=0?'+':'')+timezone;
        }
    }

    //impoker
    static get hasArchivedMttTournament():boolean {
        return false;
        // if( !cc.isValid(DataManager.GAME_LEVEL_LIST) || !cc.isValid(DataManager.GAME_LEVEL_LIST.MttTournaments) || DataManager.GAME_LEVEL_LIST.MttTournaments.length <= 0 )
        // {
        let storedData = DataManager.getStoredMttTournament();
        if( storedData )
        {
            DataManager.GAME_LEVEL_LIST = storedData;
            // DataManager.GAME_LEVEL_LIST = JSON.parse(MTTConnector.instance.getStorage(localConfig.key_mttGameLevelList));
            cc.vv.ConsoleLog.log("DataManager.GAME_LEVEL_LIST get", DataManager.GAME_LEVEL_LIST);
        }
        // }
        // if( !cc.isValid(DataManager.JoinedTournaments) || DataManager.JoinedTournaments.length <= 0 )
        // {
        let storedData2 = DataManager.getStoredLoginData();
        if(storedData)
        {
            cc.vv.DataManager.JoinedTournaments = storedData2.JoinedTournaments;
            cc.vv.ConsoleLog.log("DataManager.JoinedTournaments get", cc.vv.DataManager.JoinedTournaments);
        }
            
        // }
        return cc.isValid(DataManager.GAME_LEVEL_LIST) && cc.isValid(DataManager.GAME_LEVEL_LIST.MttTournaments) && DataManager.GAME_LEVEL_LIST.MttTournaments.length > 0;
    }
    static lastCallGetLevelListTime:number = 0;
    static _GAME_LEVEL_LIST:commonProto.Game_Level_List;
    static get GAME_LEVEL_LIST():commonProto.Game_Level_List {
        if (!DataManager._GAME_LEVEL_LIST) {
            DataManager._GAME_LEVEL_LIST = new ProtoBuf.commonProto.Game_Level_List;
        }
        return DataManager._GAME_LEVEL_LIST;
    }
    static set GAME_LEVEL_LIST(value) {
        DataManager._GAME_LEVEL_LIST = value;
    }
    static mttHallMultiFightBackList: any[] = [];
    static mttTournamentMultiList:commonProto.IMttTournamentInfo[] = [];

    static mttHallBack = (hallScript:any, callback?:any, onErrorCallback?:any)=>{
        if( DataManager.mttHallMultiFightBackList.length >= 2 )
        {
            let tournamentObj = DataManager.mttHallMultiFightBackList[DataManager.mttHallMultiFightBackList.length - 2];
            cc.vv.ConsoleLog.log("mttHallBack", tournamentObj);
            if( tournamentObj.tournamentId && cc.isValid(hallScript) )
            {
                hallScript._mttPage.getComponent("MttPrefabPkw").callMultiFlightMttHall(tournamentObj.multiFlightId, tournamentObj.tournamentId, false, false, callback);
            }
            else
            {
                hallScript._mttPage.getComponent("MttPrefabPkw").callMultiFlightMttPage(tournamentObj.multiFlightId, false, callback, onErrorCallback);
            }
        }
        DataManager.removeLastMttHallMultiFightBackList();
    }

    static isTournamentObjSetEqual = (a:any[], b:any[]) =>
    {
        for( let i = 0; i < a.length; i++ )
        {
            if( !DataManager.isTournamentObjEqual(a[i], b[i]) )
            {
                return false;
            }
        }
        return true;
    }

    static isTournamentObjEqual = (a:any, b:any) =>
    {
        return a.tournamentId == b.tournamentId && a.multiFlightId == b.multiFlightId;
    }

    static addMttHallMultiFightBackList = (tournamentObj:any)=>{
        DataManager.mttHallMultiFightBackList.push(tournamentObj);
        if( DataManager.mttHallMultiFightBackList.length >= 5 )
        {
            let arr = DataManager.mttHallMultiFightBackList.slice();
            let setA = arr.slice(-2);
            arr = arr.slice(0, -3);
            if( setA.length == 2 )
            {
                let i = arr.length - 1;
                while( i > 0)
                {
                    let setB = arr.slice(-2);
                    if( DataManager.isTournamentObjSetEqual(setA, setB) )
                    {
                        DataManager.mttHallMultiFightBackList = arr;
                        break;
                    }
                    i-=2;
                }
            }
        }
    }
    static removeLastMttHallMultiFightBackList = ()=>{
        if (DataManager.mttHallMultiFightBackList.length > 0) {
            DataManager.mttHallMultiFightBackList = DataManager.mttHallMultiFightBackList.slice(0, -1);
        }
    }
    static resetMttHallMultiFightBackList = ()=>{
        cc.vv.ConsoleLog.log("resetMttHallMultiFightBackList");
        DataManager.mttHallMultiFightBackList = [];
    }
    static pageActionSpeed:number = 0.15;
    static timeOffset:number = 0;
    static getNow = ()=>{
        let now = new Date();
        now = new Date(now.getTime()-DataManager.timeOffset);
        return now;
    };
    static floorValue = (value:number)=>{
        return Math.floor(value*100)/100;
    };
    static roundCoins = (value:number)=>{
        return Number.isInteger(value)?value.toString():value.toFixed(2);
    };
    static loginData:commonProto.IUser_Login_Response = null;
    // Current Games
    /*static currentGames:commonProto.IUser_Current_Game[] = [];
    static findCurrentGameByRoomId (roomId:number):commonProto.IUser_Current_Game {
        return this.currentGames.find((game)=>{return game.RoomId == roomId;});
    }
    static findCurrentGameByMttLevelId (mttLevelId:number):commonProto.IUser_Current_Game {
        return this.currentGames.find((game)=>{return game.SngMttLevelId == mttLevelId;});
    }
    static addCurrentGame (gameCategory:commonProto.GAME_CATEGORY, roomId:number, mttLevelId:number = 0) {
        if (!this.findCurrentGameByRoomId(roomId)) {
            let newGame = ProtoBuf.commonProto.User_Current_Game.create({
                Category: gameCategory,
                RoomId: roomId,
                SngMttLevelId: mttLevelId
            });
            this.currentGames.push(newGame);
            cc.vv.ConsoleLog.log("add current game", JSON.stringify(newGame), JSON.stringify(this.currentGames));
        }
    }
    static removeCurrentGameByRoomId (roomId:number) {
        let currentGameIndex = this.currentGames.indexOf(this.findCurrentGameByRoomId(roomId));
        if (currentGameIndex >= 0) {
            this.currentGames.splice(currentGameIndex, 1);
            cc.vv.ConsoleLog.log("removed current game room id = " + roomId, JSON.stringify(this.currentGames));
        }
    }
    static removeCurrentGameByMttLevelId (mttLevelId:number) {
        let currentGameIndex = this.currentGames.indexOf(this.findCurrentGameByMttLevelId(mttLevelId));
        if (currentGameIndex >= 0) {
            this.currentGames.splice(currentGameIndex, 1);
            cc.vv.ConsoleLog.log("removed current game mtt level id = " + mttLevelId, JSON.stringify(this.currentGames));
        }
    }*/
    static popUps:cc.Node[] = [];
    static agentCodes:any = {};
    static defaultAgentCode:string = "";
    static ownAssociationId:number = 0;
    //impoker from original
    static _userData:modelProto.User = null;
    static set userData(data:modelProto.User){
        if(data){
            if (!this.needGoldCheck){
                // not need check gold, default set 9999999 to pass check gold function.
                data.Gold = 9999999;
            } else{
                if(data.Gold){
                    data.Gold = FormatParser.DisplayGold(Math.max(0,data.Gold));
                }
            }

            this._userData = data;
        }
        else{
            this._userData = data;
        }
    }
    static get userData(){
        if( !this._userData )
        {
            let temp = new ProtoBuf.modelProto.User();
            temp.Gold = MTTConnector.instance.getUserGold();
            temp.Id = DataManager.toolUserId;
            return temp;
        }
        return this._userData;
    }
    static get userId()
    {
        if( DataManager.isReplay && DataManager.replayUserId )
        {
            return DataManager.replayUserId;
        }
        else if( DataManager.userData )
        {
            return DataManager.userData.Id;
        }
        return 0;
    }
    static replayUserId:number = 0;
    static toolUserId: number = 0;
    static JoinedTournaments:commonProto.User_Mtt_Join_Info[] = [];
    static banners:any[] = [];
    static events:any[] = [];
    static gameMode:number = 0;
    static isMtt:boolean = false;//can be deleted
    static mttReviewTableId:number = 0;
    static mttReviewPlayerId:number = 0;
    static mttReViewDisplayTag:string = "";
    static gameViewType:MTT_GAME_MODE = 0;
    static listPageIdx:number = 0;
    static filterOption: any = {};
    static currentGameInfo:PP_Level|SngTournament|MttTournament;
    static lastGameInfo:PP_Level|SngTournament|MttTournament;
    static multipleGameInfos:MultipleGameInfo[] = [];
    static currentXhrs:CustomXMLHttpRequest[] = [];
    static _backPackTool:any[] = [];
    static _backPackToolRecord:any[] = [];
    static newBagsCount:number = 0;
    static get allBackPackTool() {
        return DataManager._backPackTool;
    }
    static get backPackTool(){
        return DataManager._backPackTool.filter(oneTool=> !(oneTool.Expired || (oneTool.Expiry && ((oneTool.Expiry.getTime()) <= DataManager.getNow().getTime()))));
    }
    static set backPackTool(tools:any[]){
        DataManager._backPackTool = tools;
    }
    static get backPackToolRecord(){
        let expiredTools = DataManager._backPackTool.filter(oneTool => (oneTool.Expired || (oneTool.Expiry && ((oneTool.Expiry.getTime()) <= DataManager.getNow().getTime()))));
        return DataManager._backPackToolRecord.concat(expiredTools);
    }
    static set backPackToolRecord(tools:any[]){
        DataManager._backPackToolRecord = tools;
    }
    static toolConsumeInWpt:any[] = [];
    static StatusBar: StatusBar = null;
    static GPSController: GPSController = new GPSController();
    // static currentGamePageIdx:number;
    static sngSortStatus:[number,number] = [0,0];
    static pineSortStatus:[number,number] = [0,0];
    static joinedGameClicked:boolean = false;
    static withdrawResetTimer:number = 0;
    static isEndGame:boolean = false;
    static init()
    {
        // DataManager.currentGamePageIdx = 0;
        DataManager.gameMode = 0;
        DataManager.sngSortStatus = [0,0];
        DataManager.pineSortStatus = [0,0];
        DataManager.gameViewType = 0;
        DataManager.listPageIdx = 0;
        DataManager.filterOption = {};
    }
    static hasRedirect: boolean = false;
    static hasShowLoginPopup:boolean = false;
    static replayRecord:any[] = [];
    static isReplay:boolean = false;
    static _currentResultId:number = 0;
    static get currentResultId():number
    {
        return DataManager._currentResultId;
    }
    static set currentResultId(value)
    {
        cc.vv.ConsoleLog.log("currentResultId", value);
        DataManager._currentResultId = value;
    }
    static currentResultUserId:number = 0;
    static currentFavouriteResultId:number = 0;
    static currentReplayBackScene:string = globalConfig.SCENE_NAME.HALL;
    static replayGameResultDetail:commonProto.IGameResultDetail = null;
    static isWebReplay:boolean = false;
    static isWebFeature:boolean = false;
    static webPlatform:number = 1;
    static needGoldCheck:boolean = true;
    static webPage:number = globalConfig.WEB_PAGE.MTT_LIST;
    static isFullScreen:boolean = false;
    static lang:string = "sc";
    static miniGameId:number = 0;
    static miniGamePlatform:number = 0;
    static miniGameToolId:number = 0;
    static miniGameURL:string = "";
    static miniGameRedPocketNotice:commonProto.Broadcast_Message_Events_RED_POCKET_NOTICE[] = [];
    static miniGameActivityRewards:modelProto.ICowboyHoldemActivityRewards[]=[];
    static i18DataMap:Record<string, string> = {
        "en" : "En",
        "sc" : "Sc",
        "tc" : "Tc",
        "vn" : "Vn",
        "th" : "Th",
        "hi" : "Hi"
    };
    static i18DataFromServer(defaultStr:string, i18Str:string){
        try {
            if (i18Str && DataManager.lang!=='sc'){
                let obj = JSON.parse(i18Str);
                let la = DataManager.i18DataMap[DataManager.lang];
                // la = la.charAt(0).toUpperCase() + la.slice(1);
                if (la && obj[la]){
                    return obj[la];
                }
                else
                {
                    la = DataManager.lang;
                    la = la.charAt(0).toUpperCase() + la.slice(1);
                    if (obj[la]){
                        return obj[la];
                    }
                }
                //如果後台沒有設置對應語言，先找下有沒有英文，沒有再返回defaultStr
                if(obj["En"]){
                    return obj["En"];
                }
            }
        } catch (error) {
            cc.vv.ConsoleLog.error(error);
        }
        
        return defaultStr;
    };
    static customFormatStr(str:string, param:any[])
    {
        let outputStr = str;
        for(let i = 0; i < param.length; i++)
        {
            outputStr = outputStr.replace("{$" + (i + 1) + "}", param[i]);
        }
        return outputStr;
    }

    static onGetToolsInfo(msg:commonProto.User_Tool_In_Backpacks_Response, msgT: commonProto.Tool_Info_Response, callback?:()=>any, isRecord: boolean = false)
    {
        cc.vv.ConsoleLog.log("getMyToolsInfo_RES");
        let myBackPack:any[] = [];
        for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)){
            let infoS = msgT.ToolInfos.find((tool:modelProto.ITool)=>{return tool.Id==myTools.ToolId;});
            // cc.vv.ConsoleLog.log('test ->', infoS);
            if(infoS){//some tools may be deleted
                let myBackPackT:any = myTools;
                if(!myBackPackT.Created){myBackPackT.created = infoS.Created;cc.vv.ConsoleLog.log("empty Created!");}
                myBackPackT.Value = infoS.Value;
                myBackPackT.IconUrl = infoS.IconUrl;
                myBackPackT.Name = cc.vv.DataManager.i18DataFromServer(infoS.Name,infoS.NameI18N);
                myBackPackT.SellRatio = infoS.SellRatio;
                myBackPackT.Type = infoS.Type;
                myBackPackT.Description = cc.vv.DataManager.i18DataFromServer(infoS.Description,infoS.DescriptionI18N);
                if(!myBackPackT.Expiry) myBackPackT.Expiry = infoS.Expiry;
                if (!isRecord && myBackPackT.Expiry) {
                    myBackPackT.Expiry = new Date(myBackPackT.Expiry.getTime() + 86399000); //add 23:59:59
                }
                myBackPackT.Currency = infoS.Currency;
                myBackPackT.Validity = infoS.Validity;
                if(infoS.Config.startsWith("\"{") && infoS.Config.endsWith("}\"")) {
                    infoS.Config = infoS.Config.slice(1, -1).split("\\\"").join("\"");
                }
                myBackPackT.Config = infoS.Config?JSON.parse(infoS.Config):{ForCategory:[],Aof:false,ForId:[],Days:0};
                if( (!!myBackPackT.Config)&&(typeof myBackPackT.Config === "object") ){
                    if(Array.isArray(myBackPackT.Config.ForCategory) ){
                        myBackPackT.Config.ForCategory.forEach((element:any,index:number,array:any[])=>{array[index] = Number(element);});
                    }
                    else{
                        myBackPackT.Config.ForCategory = [];
                    }
                }
                else{
                    myBackPackT.Config = {ForCategory:[],Aof:false,ForId:[]};
                }
                // cc.vv.ConsoleLog.log("back pack",myBackPackT.Config);
                myBackPack.push(myBackPackT);
            }
        }
        if(isRecord) {
            DataManager.backPackToolRecord = myBackPack;
        } else {
            DataManager.backPackTool = myBackPack;
        }
        if(callback){
            callback();
        }
    }

    static onGetToolsInfoEmpty(callback?:()=>any, isRecord: boolean = false)
    {
        if(isRecord) {
            DataManager.backPackToolRecord = [];
        } else {
            DataManager.backPackTool = [];
        }
        if(callback){
            callback();
        }
    }

    static updateToolList(callback?:()=>any, isRecord: boolean = false){
        let toolList:number[] = [];
        DataManager.worldNetwork.requestToolsInBackpackRequest((msg:commonProto.User_Tool_In_Backpacks_Response)=>{
            cc.vv.ConsoleLog.log("getMyToolIds_RES:",(isRecord ? msg.ToolConsumptions.length : msg.ToolInBackpacks.length), msg.ToolConsumeInWPT);
            DataManager.toolConsumeInWpt = msg.ToolConsumeInWPT;
            for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)){
                // cc.vv.ConsoleLog.log(myTools);
                cc.vv.DataManager.toolUserId = myTools.UserId;
                toolList.push(myTools.ToolId);
            }
            if (toolList.length>0){
                let toolData = {ToolIds:toolList};
                DataManager.worldNetwork.getToolsInfo(toolData, (msgT: commonProto.Tool_Info_Response) => {
                    DataManager.onGetToolsInfo(msg, msgT, callback, isRecord);
                });
            }
            else{
                DataManager.onGetToolsInfoEmpty(callback, isRecord);
            }
        });
    }

    static updateToolListHttp(callback?:()=>any, isRecord: boolean = false, onError?:()=>any){
        let toolList:number[] = [];
        httpApis.requestUserToolInBackPack((msg:commonProto.User_Tool_In_Backpacks_Response)=>{
            cc.vv.ConsoleLog.log("getMyToolIds_RES:", msg.ErrorCode, (isRecord ? msg.ToolConsumptions.length : msg.ToolInBackpacks.length), msg.ToolConsumeInWPT);
            cc.vv.DataManager.toolConsumeInWpt = msg.ToolConsumeInWPT;
            for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)){
                // cc.vv.ConsoleLog.log(myTools);
                cc.vv.DataManager.toolUserId = myTools.UserId;
                toolList.push(myTools.ToolId);
            }
            if (toolList.length>0){
                let toolData = {ToolIds:toolList};
                httpApis.requestToolInfo(toolData, (msgT: commonProto.Tool_Info_Response) => {
                    DataManager.onGetToolsInfo(msg, msgT, callback, isRecord);
                }, ()=>{
                    cc.vv.ConsoleLog.log('httpApis.requestToolInfo onerror');
                    if( onError )
                    {
                        onError();
                    }
                });
            }
            else{
                DataManager.onGetToolsInfoEmpty(callback, isRecord);
            }
        },()=>{
            cc.vv.ConsoleLog.log('httpApis.requestUserToolInBackPack onerror');
            if( onError )
            {
                onError();
            }
        });
    }

    static getAvatarId(avatar:any, userId:number)
    {
        if( avatar )
        {
            let tempAvatar:string = avatar.toString();
            if( tempAvatar.indexOf(",") >= 0 )
            {
                let tempAvatarArray = tempAvatar.split(",");
                if( tempAvatarArray.length > 0 )
                {
                    tempAvatar = tempAvatarArray[0];
                }
            }
            // tempAvatar = tempAvatar.replace("/system", "").replace(".jpg", "").replace("/","");
            return tempAvatar;
        }
        else if( userId )
        {
            return userId.toString();
        }
        return "";
    }
    static get avatarId()
    {
       
        if( DataManager.userData )
        {
            // if( DataManager.userData.Avatar )
            // {
            //     let tempAvatar = DataManager.userData.Avatar.toString();
            //     if( tempAvatar.indexOf(",") >= 0 )
            //     {
            //         let tempAvatarArray = tempAvatar.split(",");
            //         if( tempAvatarArray.length > 0 )
            //         {
            //             tempAvatar = tempAvatarArray[0];
            //         }
            //     }
            //     return tempAvatar;
            // }
            // else if( DataManager.userData.Id )
            // {
            //     return DataManager.userData.Id.toString();
            // }
            return DataManager.getAvatarId(DataManager.userData.Avatar, DataManager.userData.Id);
        }
        return "";
    }

    static isMultipleGame()
    {
        return cc.director.getScene().name == globalConfig.SCENE_NAME.MULTIPLE_GAME;
    }

    static getMyJoinStatus(tournamentId:number){
        let joinedTournaments = DataManager.JoinedTournaments;
        let index = joinedTournaments.findIndex((tournament:any)=>tournament.TournamentId == tournamentId);
        return index>=0?DataManager.JoinedTournaments[index].JoinStatus:0;
    }

    static resetVariables(){
        DataManager.ERROR_CODE = {};
        DataManager.ante = 0;
        DataManager.gameList = "";
        // DataManager.token = "";//cleared by others
        DataManager.mttId = 0;
        DataManager.moreCoin = 0;
        DataManager.currentRoomID = 0;
        // DataManager.loading:any = null;
        // DataManager.loadingTargets:any[] = [];
        // DataManager.popUps: = [];
        //impoker

        DataManager.GAME_LEVEL_LIST=null;
        DataManager.mttHallMultiFightBackList = [];
        DataManager.lastCallGetLevelListTime = 0;
        DataManager.timeOffset = 0;
        DataManager.loginData = null;
        // Current Games
        // DataManager.currentGames = [];
        DataManager.agentCodes = {};
        DataManager.defaultAgentCode = "";
        DataManager.ownAssociationId = 0;
        //impoker from original
        DataManager.userData = null;
        DataManager.replayUserId = 0;
        DataManager.toolUserId = 0;
        DataManager.JoinedTournaments = [];
        // DataManager.banners = [];
        // DataManager.events = [];
        DataManager.gameMode = 0;
        DataManager.isMtt = false;//can be deleted
        DataManager.mttReviewTableId = 0;
        DataManager.mttReviewPlayerId = 0;
        DataManager.gameViewType = 0;
        DataManager.listPageIdx = 0;
        DataManager.filterOption = {};
        DataManager.currentGameInfo = null;
        DataManager.lastGameInfo = null;
        DataManager.multipleGameInfos = [];
        DataManager.currentXhrs = [];
        DataManager.backPackTool = [];
        DataManager.backPackToolRecord = [];
        DataManager.newBagsCount = 0;

        // DataManager.currentGamePageIdx = 0;//cleared by others
        DataManager.isEndGame = false;
        DataManager.hasShowLoginPopup = false;
        DataManager.replayRecord = [];
        DataManager.isReplay = false;
        DataManager.currentResultId = 0;
        DataManager.currentResultUserId = 0;
        DataManager.replayGameResultDetail = null;
        DataManager.currentFavouriteResultId = 0;
        DataManager.currentReplayBackScene = globalConfig.SCENE_NAME.HALL;

        DataManager.isWebReplay = false;
        DataManager.isFullScreen = false;

        // do not need to reset because these value not suppose to be changed
        // DataManager.isWebFeature = false;
        // DataManager.webPlatform = ProtoBuf.commonProto.PLATFORM.TRIBAL_PIONEER;
        // DataManager.webPage = globalConfig.WEB_PAGE.MTT_LIST;
        
        DataManager.miniGameId = 0;
        DataManager.miniGamePlatform = 0;
        DataManager.miniGameURL = "";
        DataManager.miniGameRedPocketNotice = [];
        DataManager.miniGameActivityRewards = [];
        
        DataManager.mttTournamentMultiList = [];
    }
}