import { Translation } from "../lang";
import {globalConfig} from "../mttconfig";
import {commonProto} from "../pb/commonProto";
import { Translate } from "../Translator";

export class FormatParser {

    static addStartPadding(arr:(string|number)[], len:number, padding:string):string[] {
        return Array.from(arr, s => s.toString().padStart(len, padding));
    }

    static RoundToDecimal (value:number, decimalPlaces:number = 0) {
        let decimalPow = Math.pow(10, decimalPlaces);
        return Math.round(value * decimalPow) / decimalPow;
    }

    static FloorToDecimal(value:number, decimalPlaces:number = 0){
        let decimalPow = Math.pow(10,decimalPlaces);
        return Math.sign(value) * Math.floor(Math.abs(value)*decimalPow)/decimalPow;
    }

    static DisplayGold(value:number, decimalPlaces:number = 2){
        let fillerValue = Math.sign(value) * (Math.abs(value) + Math.pow(10,-4));
        return FormatParser.FloorToDecimal(fillerValue,decimalPlaces);
    }

    static ThousandPointFormat (value:number) {
        let dotSplit = value.toString().split('.');
        let quotientStr = dotSplit[0];

        let quotientStrArray = quotientStr.split('');
        let thousandPointStr = "";
        quotientStrArray.reverse().forEach((digit:string, index:number) => {
            if (index % 3 == 0 && index > 0) {
                thousandPointStr = "," + thousandPointStr;
            }
            thousandPointStr = digit + thousandPointStr;
        });

        if (dotSplit.length > 1) {
            thousandPointStr += '.' + dotSplit[1];
        }

        return thousandPointStr;
    }

    static changeSecToTime (second: number, format:string = "hhmmss"){
        let sec = Math.floor(second);
        let h = Math.floor(sec / 3600);
        let m = Math.floor((sec % 3600) / 60);
        let s = sec % 3600 % 60;
        switch (format) {
            case "hhmmss":
                return h.toString().padStart(2, '0')+":"+m.toString().padStart(2, '0')+":"+s.toString().padStart(2,'0');
            case "mmss":
                m += h * 60;
                return m.toString().padStart(2, '0')+":"+s.toString().padStart(2,'0');
            default:
                return "";
        }
    }

    static ScientificNotation (value:number, decimalPlaces:number = 1) {
        if (value >= 1000000) {
            return FormatParser.RoundToDecimal(value / 1000000, decimalPlaces) + "M";
        }
        else if (value >= 1000) {
            return FormatParser.RoundToDecimal(value / 1000, decimalPlaces) + "k";
        }
        else {
            return value.toString();
        }
    }

    static GetCurrencySign(currency:string): string {
        let currencySign = globalConfig.CURRENCY_SIGN[currency];
        return currencySign ? currencySign : "";
    }

    static GetCurrencyRate(rates:(commonProto.ITournamentRate|commonProto.TournamentRate)[], currency:string) {
        const rate = rates.find(r => r.Currency == currency);
        return !!rate ? rate.ExchangeRate : 1;
    }

    static ExchangeCurrency(value:number, fxRate:number, outputCurrency:string):number {
        return outputCurrency == globalConfig.CURRENCY.GOLD ? value*fxRate : value/fxRate;
    }

    static ExchangeCurrency_DisplayGoldValue(value:number, fxRate:number, displayCurrency:string, inputCurrency:string = globalConfig.CURRENCY.GOLD, decimalPlaces:number = 2):number {
        return FormatParser.DisplayGold(displayCurrency != inputCurrency ? FormatParser.ExchangeCurrency(value, fxRate, displayCurrency) : value, decimalPlaces);
    }

    static ExchangeCurrency_DisplayGold(value:number, fxRate:number, displayCurrency:string, inputCurrency:string = globalConfig.CURRENCY.GOLD, decimalPlaces:number = 2):string {
        let currencySign = globalConfig.CURRENCY_SIGN[displayCurrency];
        return (currencySign ? currencySign : "") + FormatParser.ExchangeCurrency_DisplayGoldValue(value, fxRate, displayCurrency, inputCurrency, decimalPlaces);
    }

    static ExchangeCurrency_DisplayGoldNoSign(value:number, fxRate:number, displayCurrency:string, inputCurrency:string = globalConfig.CURRENCY.GOLD, decimalPlaces:number = 2):string {
        return FormatParser.ExchangeCurrency_DisplayGoldValue(value, fxRate, displayCurrency, inputCurrency, decimalPlaces).toString();
    }

    static ExchangeCurrency_ThousandPointFormat(value:number, fxRate:number, displayCurrency:string, inputCurrency:string = globalConfig.CURRENCY.GOLD, decimalPlaces:number = 2):string {
        let currencySign = globalConfig.CURRENCY_SIGN[displayCurrency];
        let fxValue = FormatParser.DisplayGold(displayCurrency != inputCurrency ? FormatParser.ExchangeCurrency(value, fxRate, displayCurrency) : value, decimalPlaces);
        // if (displayCurrency != globalConfig.CURRENCY.GOLD) {
        //     let roundedFxValue = Math.round(fxValue);
        //     let diff = FormatParser.RoundToDecimal(roundedFxValue - fxValue, 2);
        //     if (diff == 0.01) {
        //         fxValue = roundedFxValue;
        //     }
        // }
        return (currencySign ? currencySign : "") + FormatParser.ThousandPointFormat(fxValue);
    }

    static ExchangeCurrency_ThousandPointFormatNoSign(value:number, fxRate:number, displayCurrency:string, inputCurrency:string = globalConfig.CURRENCY.GOLD, decimalPlaces:number = 2):string {
        let fxValue = FormatParser.DisplayGold(displayCurrency != inputCurrency ? FormatParser.ExchangeCurrency(value, fxRate, displayCurrency) : value, decimalPlaces);
        // if (displayCurrency != globalConfig.CURRENCY.GOLD) {
        // let roundedFxValue = Math.round(fxValue);
        // let diff = FormatParser.RoundToDecimal(roundedFxValue - fxValue, 2);
        // if (diff == 0.01) {
        //     fxValue = roundedFxValue;
        // }
        // }
        return FormatParser.ThousandPointFormat(fxValue);
    }

    static ExchangeCurrency_ScientificNotation(value:number, fxRate:number, displayCurrency:string, inputCurrency:string = globalConfig.CURRENCY.GOLD, decimalPlaces:number = 2):string {
        let currencySign = globalConfig.CURRENCY_SIGN[displayCurrency];
        let fxValue = FormatParser.DisplayGold(displayCurrency != inputCurrency ? FormatParser.ExchangeCurrency(value, fxRate, displayCurrency) : value, decimalPlaces);
        // if (displayCurrency != globalConfig.CURRENCY.GOLD) {
        //     let roundedFxValue = Math.round(fxValue);
        //     let diff = FormatParser.RoundToDecimal(roundedFxValue - fxValue, 2);
        //     if (diff == 0.01) {
        //         fxValue = roundedFxValue;
        //     }
        // }
        return (currencySign ? currencySign : "") + FormatParser.ScientificNotation(fxValue);
    }

    static ExchangeUsdt(value:number, fxRate:number):number {
        return value/fxRate;
    }

    static ExchangeUsdt_DisplayGold(value:number, fxRate:number, displayCurrency:string, inputCurrency:string = globalConfig.CURRENCY.GOLD, decimalPlaces:number = 2):string {
        let fxValue = FormatParser.DisplayGold(displayCurrency != inputCurrency ? FormatParser.ExchangeCurrency(value, fxRate, displayCurrency) : value, decimalPlaces);
        // if (displayCurrency != globalConfig.CURRENCY.GOLD) {
        //     let roundedFxValue = Math.round(fxValue);
        //     let diff = FormatParser.RoundToDecimal(roundedFxValue - fxValue, 2);
        //     if (diff == 0.01) {
        //         fxValue = roundedFxValue;
        //     }
        // }
        return "" + fxValue;
    }

    static DisplayStack(stack: number, bigBlind: number, ante: number, coinMode: number, isShortDeck: boolean) {
        if (coinMode == 0) {
            return FormatParser.ThousandPointFormat(FormatParser.RoundToDecimal(stack, 2));
        } else {
            const denominator = isShortDeck ? ante : bigBlind;
            return FormatParser.RoundToDecimal(stack / denominator, 1).toString();
        }
    }

    static GetStackUnit(isShortDeck: boolean) {
        return isShortDeck ? Translate(Translation.HOLDEM.BIG_BLIND_UNIT) : Translate(Translation.HOLDEM.BIG_BLIND_UNIT);
    }
}
