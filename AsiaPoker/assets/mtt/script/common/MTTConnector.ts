import cv from "../../../Script/components/lobby/cv";
import { ProtoBuf } from "./net/Pb";
import { globalConfig, localConfig } from "./mttconfig";
import { AndroidBackButton } from "./AndroidBackButton";
import LoadingBlocker from "./LoadingBlocker";
import { ResourcesLoader } from "./ResourcesLoader";
import { Translate } from "./Translator";
import { ConsoleLog } from "./ConsoleLog";
import { httpApis } from "./net/httpApis";
import { commonProto } from "./pb/commonProto";
import { GameViewLoader } from "./GameViewLoader";
import { Translation } from "./lang";
import FontManager from "./FontManager";
import WcGuessingActivity from "../WorldCup/WcGuessingActivity";
import { ImpokerHall } from "../../prefab/impoker/hall/hall_script/ImpokerHall";
import { MttPrefabPkw } from "../../prefab/impoker/hall/game_list/mtt/pkw/MttPrefabPkw";
import { MttHallPkw } from "../../prefab/impoker/hall/game_list/mtt/pkw/MttHallPkw";
import { ImpokerHallFeature } from "../feature/ImpokerHallFeature";
import { MTTPrefabType } from "../../prefab/impoker/hall/game_list/mtt/mtt_script/MttPrefab";
import CommonTools from "./CommonTools";

const {ccclass, property} = cc._decorator;

export enum MTTRequestTokenStatus
{
    ERROR = -1,
    NOT_REQUEST = 0,
    REQUESTING = 1,
    REQUESTED = 2
}

export enum FindViewShowType
{
    NONE = 0,
    MTT = 1,
    JSNG = 2,
}

@ccclass
export default class MTTConnector {
    private static _instance:MTTConnector = null;
    static get instance()
    {
        if( !cc.isValid(MTTConnector._instance) )
        {
            MTTConnector._instance = new MTTConnector();
            MTTConnector._instance.initCCVV();
        }
        return MTTConnector._instance;
    };
    
    get cv()
    {
        return cv;
    }

    get isBL() {
        return this.prefix === "bl";
    }

    get isPKW() {
        return this.prefix === "pkw";
    }

    get isWPK() {
        return this.prefix === "wpk";
    }

    prefix:string = "pkw";
    langMap: Record<string, string> = {
        "en_us": "en",
        "zh_cn": "sc",
        "yn_th": "vn",
        "th_ph": "th",
        "hi_in": "hi"
    };    

    get promoterList() {
        return MTTConnector.instance.getDebugMode() === 1 ?
            [
                5897330, // Game0016
                5897331, // Game0017
                5897356, // Game0018
            ] :
            [
                164927, // JRB
                158574, // IVEY
                6983,   // Dwan
                342093, // Allen Iverson
                145853, // Andy Milonakis
                414163, // 主播Celina
                413564, // Nick Petrangelo
                413569, // Stephen Chidwick
                413571, // Dan Dvoress
                413587, // Tony Dunst
                413590, // Matt Savage
                413593, // Lynn Gilmartin
                413597, // Phil Hellmuth
                414039, // Brad Owen
                413604, // Andrew Neeme
                413606, // Scotty Nguyen
            ];
    }

    listColorCode:string = "#71727E";
    listSelectedColorCode:string = "#B39F6D";

    filterColorCode:string = "#414248";
    filterSelectedColorCode:string = "#504226";

    selfSettingBoarderOFF: cc.Color = new cc.Color().fromHEX("979797");
    selfSettingBoarderON: cc.Color = new cc.Color().fromHEX("E9C501");
    selfSettingMenuON: cc.Color = new cc.Color().fromHEX("FBD888");
    selfSettingMenuOFF: cc.Color = new cc.Color().fromHEX("8A8B90");

    gameListRoomInfoColor:any = {
        NORMAL: "#C7C4D3",
        USD: "#FFFFFF"
    };

    gameListRoomInfoIconColor:any = {
        NORMAL: "#C5C5D1",
        USD: "#888DC8"
    };

    gameListGameTypeColor = {
        NORMAL: "#C7C4D3",
        USD: "#C6C9ED"
    };

    gameListRegColor:any = {
        0: "#DEC68D", // DATE
        1: "#DEC68D", // TIME
        2: "#DE5959", // LATE
        3: "#C7C4D3", // END
        4: "#C6C9ED" // WPT
    };

    mttHallColorOn: cc.Color = new cc.Color().fromHEX("#E5D38D");
    mttHallColorOff: cc.Color = new cc.Color().fromHEX("#C7C4D3");
    signUpColorLightUp: cc.Color = new cc.Color().fromHEX("#E9C501");
    signUpColorLightDown: cc.Color = new cc.Color().fromHEX("#9B9B9B");

    tournamentDetailColorCode = {
        tabBtnLabelColorOff: "#C7C4D3",
        tabBtnLabelColorOn: "#D0AB6E",
        blindLevel: {
            normalColor: "#C7C4D3",
            highLightColor : "#D0AB6E"
        }
    };

    isIPhoneX():boolean {
        let ratio = cc.view.getFrameSize().height/cc.view.getFrameSize().width;
        return ratio > 2;
    }

    xDeviceLayoutYPos = {
        platform:"pkw",
        playerBottom:-830,
        cardType:-995,
        cardTypeOmaha:-985,
        playerControlNode:-720,
        potButtons:230,
        potButtonSpaceX:43,
        freeRaiseButton:89,
        allInPanel:98,
        slideBackBtn:-9,
        selfCardHandlerY:100,
        menuButton:115,
        tournamentButton:107,
        addTableButton:280,
        omahaSelfCardScale:0.845,
        omahaSelfCardPosition:[-127,-26,75,176],
        getSelfStakeY(gameMode:number = 0){
            switch(gameMode)
            {
                case globalConfig.GAME_LEVEL_LIST_ID.SNG:   //150, 520
                    return new cc.Vec2(180,550)
                default:
                    return new cc.Vec2(140,480)
            }
        }
    }
    originLayoutPos = {
        potButtons:240,
        freeRaiseButton:96,
        allInPanel:119,
        slideBackBtn:-22,
    }
    liveViewLayoutPosChange = {
        playerControl:100,
    }
    liveViewLayoutXDevicePosChange = {
        playerControl:100,
    }
    getMttCelebrityAvatarScale(seatCount:number){
        return seatCount<=6 ? 1 : 0.8;
    }

    backgroundThemeCount: number = 13;
    customMaxAudioInstance:number = 30;

    needHideLoadingUI:boolean = true;

    config:MTTConfig = new MTTConfig();
    platform:number = ProtoBuf.commonProto.PLATFORM.PKW;
    requestTokenStatus:MTTRequestTokenStatus = MTTRequestTokenStatus.NOT_REQUEST;
    requestTokenResponseTimeout:any = null;
    requestTokenResponseInterval:number = 5000;
    fonts: Record<string, string> = {
        "Microsoft YaHei": "zh_CN/font/arial", //"mtt/font/microsoft_yahei",
        "PingFang SC": "zh_CN/font/arial",//"Fonts/PingFang/PingFang SC Regular",
    };
    toFontFamilies: Record<string, string> = {
        "STHeitiSC": "Arial",
        "PingFang SC": "Arial",
    }; 

    
    _wcGuessActivity:WcGuessingActivity = null;
    _wcGuessAlreadyShow:boolean = false;
    _wcTeamData:commonProto.IWorldCupGuess_Team_Info[] = [];
    _wcJoinStatus:number = 2;//ref commonProto.UserJoinWorldCupGuessState.Disabled

    _mttToolIdFiltered: MttPrefabPkw;

    showJSNG:boolean = true;
    _activeH5Optimize:boolean = false;

    currentFindViewShowType: FindViewShowType = 0;
    /**
     * if true, active special optimization for h5.
     */
    get activeH5Optimize(){
        return this._activeH5Optimize && cc.sys.isBrowser;
    }

    enterScene(node:cc.Node, name:string)
    {
        cv.config.setCurrentScene(name);
        cv.config.adaptScreen(node);
        // cv.resMgr.adaptWidget(node, true);
        
        cv.LoadingView.reset();
        cv.TP.reset();
        cv.TT.reset();
        cv.StatusView.reset(name);
        cv.pushNotice.reset();
        cv.MessageCenter.send("switchSceneFinish", name);
        cv.SwitchLoadingView.hide();
    }

    getDebugMode()
    {
        return cv.config.GET_DEBUG_MODE();
    }

    getHoldemBackgroundData(bgColor:number)
    {
        switch(+bgColor)
        {
            case +localConfig.BgColor.BG1:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG1,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE1,"0.1"];
            case +localConfig.BgColor.BG2:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG2,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE2,"0.1"];
            case +localConfig.BgColor.BG3:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG3,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE3,"0.1"];
            case +localConfig.BgColor.BG4:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG4,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE4,"0.1"];
            case +localConfig.BgColor.BG5:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG5,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE5,"0.2"];
            case +localConfig.BgColor.BG6:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG6,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE6,"0.2"];
            case +localConfig.BgColor.BG7:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG7,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE7,"0.2"];
            case +localConfig.BgColor.BG8:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG8,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE8,"0.2"];
            case +localConfig.BgColor.BG9:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG9,'',"0.2"];
            case +localConfig.BgColor.BG10:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG10,'',"0.2"];
            case +localConfig.BgColor.BG11:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG11,'',"0.2"];
            case +localConfig.BgColor.BG12:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG12,'',"0.2"];
            case +localConfig.BgColor.BG_WPT:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG_WPT,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE_WPT,"0.2"];
            default:
                return [ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.BG1,ResourcesLoader.RES_PATH.TEXTURE2D.HOLDEM.TABLE1,"0.1"];
        }
        return ["mtt/BG/BG_Floor" + bgColor, "mtt/BG/BG_Table" + bgColor, "1"];
    }

    getHallSceneName()
    {
        return cv.Enum.SCENE.HALL_SCENE;
    }

    loadScene(sceneName:string, onLaunched?: Function)
    {
        if( cc.vv.DataManager.webPlatform == ProtoBuf.commonProto.PLATFORM.PKW )
        {
            if( sceneName == globalConfig.SCENE_NAME.HALL )
            {
                sceneName = MTTConnector.instance.getHallSceneName();
            }
            else if( sceneName == globalConfig.SCENE_NAME.HOLDEM )
            {
                if (cc.vv.DataManager.gameMode == 3){
                    sceneName = globalConfig.SCENE_NAME.MULTIPLE_GAME_JSNG;
                }else{
                    sceneName = globalConfig.SCENE_NAME.MULTIPLE_GAME;
                }
            }
            else if( sceneName == globalConfig.SCENE_NAME.HOLDEM_REPLAY )
            {
                sceneName = globalConfig.SCENE_NAME.HOLDEM_REPLAY_FEATURE;
            }
        }
        if(cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
            AndroidBackButton.getInstance().resetBackFunction();
        }
        LoadingBlocker.show("loadScene " + sceneName);
        // this.cv.SwitchLoadingView.show(Translate(Translation.LOADER.LOADING_VIEW));
        ResourcesLoader.instance.Reset();
        // @ts-ignore
        cv.action.switchScene(sceneName, onLaunched);
    }

    sendMessageCenter(msg: string, params: any = null)
    {
        cv.MessageCenter.send(msg, params);
    }

    registerMessageCenter(msg: string, callback: Function, object: cc.Node)
    {
        cv.MessageCenter.register(msg, callback, object);
    }

    unregisterMessageCenter(msg: string, object: cc.Node)
    {
        cv.MessageCenter.unregister(msg, object);
    }

    openMttToolIdFiltered(toolId:number, prefab:cc.Prefab = null, parentLayer:cc.Node = null)
    {
        if( !prefab && ImpokerHall.instance )
        {
            prefab = (ImpokerHall.instance as ImpokerHallFeature).mttToolIdFilteredPrefab;
        }
        if( !parentLayer && ImpokerHall.instance && ImpokerHall.instance.layers && ImpokerHall.instance.layers.length > 0 )
        {
            parentLayer = ImpokerHall.instance.node.parent;
        }
        if( prefab && cc.isValid(parentLayer) )
        {
            if( !cc.isValid(this._mttToolIdFiltered) )
            {
                let temp = cc.instantiate(prefab);
                this._mttToolIdFiltered = temp.getComponent(MttPrefabPkw);
                this._mttToolIdFiltered.setMttPrefabType(MTTPrefabType.TOOL_ID_FILTERED);
            }
            this._mttToolIdFiltered.node.parent = parentLayer;
            this._mttToolIdFiltered.node.active = true;
            this._mttToolIdFiltered.node.setSiblingIndex(parentLayer.childrenCount + 1);
            CommonTools.instance.setWidget(this._mttToolIdFiltered.node, true);
            this._mttToolIdFiltered.setPage(null, true, MTTPrefabType.TOOL_ID_FILTERED, {toolId: toolId});
        }
    }

    isInMTTGame()
    {
        return cv.config.getCurrentScene() == globalConfig.SCENE_NAME.HOLDEM || cv.config.getCurrentScene() == globalConfig.SCENE_NAME.MULTIPLE_GAME || cv.config.getCurrentScene() == globalConfig.SCENE_NAME.MULTIPLE_GAME_JSNG;
    }

    initCCVV() {
        if (!cc.vv) {
            cc.vv = {};
            cc.vv.DataManager = require("DataManager");
            cc.vv.AssetsManager = require("AssetsManager");
            cc.vv.LanguageData = {
                t: Translate,
                init: Translate.SetLanguage
            };
            cc.vv.ConsoleLog = {
                log: ConsoleLog.log,
                error: ConsoleLog.error,
                trace: ConsoleLog.trace,
                warn: ConsoleLog.warn
            };
            FontManager.instance.loadFont(this.fonts, this.toFontFamilies);
            this.removeExpiredLogs();

            cc.vv.AssetsManager.init();
            cc.vv.DataManager.init()
            cc.vv.DataManager.resetVariables();
            // cc.vv.DataManager.loadingTargets = [];
            // cc.vv.DataManager.loading = this._loading;

        }
    }

    getAllUrlParams(url: string) {

        // get query string from url (optional) or window
        var queryString = url ? url.split('?')[1] : window.location.search.slice(1);

        // we'll store the parameters here
        var obj = {};

        // if query string exists
        if (queryString) {

            // stuff after # is not part of query string, so get rid of it
            queryString = queryString.split('#')[0];

            // split our query string into its component parts
            var arr = queryString.split('&');

            for (var i = 0; i < arr.length; i++) {
                // separate the keys and the values
                var a = arr[i].split('=');

                // set parameter name and value (use 'true' if empty)
                var paramName = a[0];
                var paramValue = typeof (a[1]) === 'undefined' ? true : a[1];

                // (optional) keep case consistent
                paramName = paramName.toLowerCase();
                if (typeof paramValue === 'string') paramValue = paramValue.toLowerCase();

                // if the paramName ends with square brackets, e.g. colors[] or colors[2]
                if (paramName.match(/\[(\d+)?\]$/)) {

                    // create key if it doesn't exist
                    var key = paramName.replace(/\[(\d+)?\]/, '');
                    if (!obj[key]) obj[key] = [];

                    // if it's an indexed array e.g. colors[2]
                    if (paramName.match(/\[\d+\]$/)) {
                        // get the index value and add the entry at the appropriate position
                        var index = /\[(\d+)\]/.exec(paramName)[1];
                        obj[key][index] = paramValue;
                    } else {
                        // otherwise add the value to the end of the array
                        obj[key].push(paramValue);
                    }
                } else {
                    // we're dealing with a string
                    if (!obj[paramName]) {
                        // if it doesn't exist, create property
                        obj[paramName] = paramValue;
                    } else if (obj[paramName] && typeof obj[paramName] === 'string') {
                        // if property does exist and it's a string, convert it to an array
                        obj[paramName] = [obj[paramName]];
                        obj[paramName].push(paramValue);
                    } else {
                        // otherwise add the property
                        obj[paramName].push(paramValue);
                    }
                }
            }
        }

        return obj;
    }

    removeExpiredLogs() {
        try {
            if (cc.sys.os === cc.sys.OS_ANDROID) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "removeExpiredLogs", "(I)V", 1);
            } else if (cc.sys.os === cc.sys.OS_IOS) {
                jsb.reflection.callStaticMethod("AppController", "removeExpiredLogs:", 1);
            }
        } catch (error) {

        }
    }

    getLangCode(lang:string)
    {
        let langStr = lang;

        if (langStr && this.langMap[langStr]) {
            return this.langMap[langStr];
        }
        return "en";
    }

    covertToSelfLangCode(lang:string)
    {
        if (lang && typeof lang === "string") {
            let langStr = lang.toLowerCase();

            if (langStr && this.langMap[langStr]) {
                return this.langMap[langStr];
            }
        }
        return "en";
    }

    getCurrentLanguage()
    {
        return cv.config.getCurrentLanguage();
    }

    getSelfCurrentLanguage()
    {
        let language = this.getCurrentLanguage();
        return this.covertToSelfLangCode(language);
    }

    getFindView() {
        let findview = cc.director.getScene().getComponentInChildren("FindView");
        if (cc.isValid(findview)) {
            return findview;
        }
        return null;
    }
    isInFindViewMTTTab()
    {
        let findView = this.getFindView();
        // cc.vv.ConsoleLog.log("MTTConnector.instance.isInFindViewMTTTab()", cc.isValid(findView), findView, findView.node.active, findView._gameType == findView.MTT_NUM);
        return cc.isValid(findView) && findView.node.active && findView._gameType == findView.MTT_NUM;
    }
    handleHideMTTHall()
    {
        // cc.vv.ConsoleLog.log("MTTConnector handleHideMTTHall", MTTConnector.instance.isInFindViewMTTTab(), ImpokerHall.instance);
        if( !MTTConnector.instance.isInFindViewMTTTab() && !MTTConnector.instance.isInMTTGame() && ImpokerHall.instance && cc.isValid(ImpokerHall.instance._mttPage) )
        {
            let mttPrefab = ImpokerHall.instance._mttPage.getComponent(MttPrefabPkw);
            if( cc.isValid(mttPrefab) && cc.isValid(mttPrefab._mttHall) && mttPrefab._mttHall.active )
            {
                let mttHall = mttPrefab._mttHall.getComponent(MttHallPkw)
                if( mttHall )
                {
                    mttHall.onReturnClicked();
                }
            }
        }
    }

    getGameListScrollBottomSpacing() {
        return 0;
    }

    enterMTTGameFailAction(errMsg: string)
    {
        cv.TP.showMsg(errMsg, cv.Enum.ButtonStyle.GOLD_BUTTON, null, null, false, Translate(Translation.ERROR_CODE_PKW.TITLE));
    }

    /**
     * 切換至 mtt 牌桌, 切換失敗會自動切換至 hall scene
     * @param id                mtt 比賽的 id
     * @param callback          切換場景後回調
     */
    enterMTTGame(id: number, callback?: Function) {
        this.initCCVV();

        httpApis.requestMttTournamentDetail(
            id,
            (data: commonProto.MttTournamentDetailResponse) => {
                if (data && !data.ErrorCode && data.TournamentDetail) {

                    cc.vv.ConsoleLog.log("enterMTTGame enter", data.TournamentDetail);
                    GameViewLoader.switchGameScene(
                        data.TournamentDetail.GameMode,
                        globalConfig.GAME_LEVEL_LIST_ID.MTT,
                        data.TournamentDetail.TournamentId,
                        0,
                        0,
                        false,
                        data.TournamentDetail.PlayOnJoint,
                        callback
                    );

                } else {
                    
                    if (data.ErrorCode&&data.ErrorCode==31001){
                        cc.vv.ConsoleLog.log("enterMTTGame game_end", data);
                    }else{
                        cc.vv.ConsoleLog.log("enterMTTGame data", data);
                        this.enterMTTGameFailAction(data.ErrorCode ? Translate(Translation.ERROR_CODE_PKW[data.ErrorCode]) : Translate(Translation.NETWORK.PINEAPPLE_DISCONNECT));
                        ConsoleLog.uploadWebLogs(false, "entermtt/empty");
                    }

                }
            },
            (err: any) => {

                cc.vv.ConsoleLog.log("enterMTTGame error", err);
                this.enterMTTGameFailAction(Translate(Translation.NETWORK.PINEAPPLE_DISCONNECT));
                ConsoleLog.uploadWebLogs(false, "entermtt/network");
            }
        );
    }

    initMTTBag()
    {
        this.initMTTParams();
    }
    
    initMTTParams()
    {
        this.initCCVV();
        // Fix calling new LoadingBlocker onLoad earlier than old LoadingBlocker onDestroy
        LoadingBlocker._instance = null;

        cc.vv.DataManager.isWebFeature = true;
        cc.vv.DataManager.needGoldCheck = false;

        cc.vv.DataManager.webPlatform = 1;
        cc.vv.DataManager.webPage = 0;
        cc.vv.DataManager.isWebFeature = true;
        cc.vv.DataManager.lang = MTTConnector.instance.getSelfCurrentLanguage();
        cc.vv.DataManager.isFullScreen = true;
        ResourcesLoader.instance.updateResPath();
        ResourcesLoader.instance.initNeccessaryPrefabPath();
        cc.vv.LanguageData.init(cc.vv.DataManager.lang);
    }

    requestToken()
    {
        this.initCCVV();
        cc.vv.ConsoleLog.log('mtt requestToken');
        if( this.requestTokenStatus != MTTRequestTokenStatus.REQUESTING )
        {
            if( !cc.vv.DataManager.token )
            {
                this.requestTokenStatus = MTTRequestTokenStatus.REQUESTING;
                let success = cv.worldNet.RequestAuthApi();
                if( !success )
                {
                    this.onAuthMttError(this.config.tokenErrorMsg.REQUEST_TOKEN_FAIL);
                }
            }
            else
            {
                cc.vv.ConsoleLog.log('mtt requestToken is already exist');
                this.onTokenUpdate(cc.vv.DataManager.token);
            }
        }
        else
        {
            cc.vv.ConsoleLog.log('mtt requestToken is requesting');
        }
        
    }

    onAuthMttSucc(msg: any)
    {
        cc.vv.ConsoleLog.log('onAuthMttSucc', msg);
        clearTimeout(this.requestTokenResponseTimeout);
        if (msg.url) {
            cv.dataHandler.getUserData().mtt_url = msg.url;
        }
        if( msg.bl_token )
        {
            cv.dataHandler.getUserData().mtt_token = msg.bl_token;
            this.requestTokenStatus = MTTRequestTokenStatus.REQUESTED;
            this.onTokenUpdate(msg.bl_token);
        }
        else if( msg.token )
        {
            cv.dataHandler.getUserData().mtt_token = msg.token;
            this.requestTokenStatus = MTTRequestTokenStatus.REQUESTED;
            this.onTokenUpdate(msg.token);
        }
        else
        {
            this.onAuthMttError(this.config.tokenErrorMsg.NO_TOKEN);
        }
    }

    onAuthMttError(msg: any)
    {
        cc.vv.ConsoleLog.log('onAuthMttError', msg);
        clearTimeout(this.requestTokenResponseTimeout);
        this.requestTokenStatus = MTTRequestTokenStatus.ERROR;
        cc.vv.DataManager.token = "";
        this.sendMessageCenter(this.config.BroadCast.MTT_TOKEN_ERROR, msg);
    }

    onTokenUpdate(token:string)
    {
        cc.vv.ConsoleLog.log('onTokenUpdate', token);
        let lastToken = cc.vv.DataManager.token;
        this.initCCVV();
        cc.vv.DataManager.token = token;
        if(lastToken != token)
        {
            this.sendMessageCenter(this.config.BroadCast.MTT_TOKEN_UPDATE);
        }
        this.sendMessageCenter(this.config.BroadCast.MTT_TOKEN_END);
    }

    isJSONString = (str = '') => {
        if (typeof str === 'string') {
            try {
                if (typeof JSON.parse(str) === 'object') {
                    return true;
                }
            } catch (error) {
                // cc.vv.ConsoleLog.log("isJSONString", error);
            }
        }
      
        return false;
    };

    getStorage = (key:string, global = false) => {
        const data = cc.sys.localStorage.getItem(key);
      
        if (this.isJSONString(data)) {
            const newData = JSON.parse(data);
            
            if ((global && newData.id === 'global') || (!global && newData.id === cv.dataHandler.getUserData().u32Uid.toString())) {
                return newData.data;
            }
            // remove if id do not match
            cc.sys.localStorage.removeItem(key);
        
            return null;
        }
      
        return data;
      };
      
    setStorage = (key:string, data:any, global = false) => {
        try {
            const newData = JSON.stringify({
                id: global ? 'global' : cv.dataHandler.getUserData().u32Uid.toString(),
                data,
            });
      
            cc.sys.localStorage.setItem(key, newData);
        } catch (error) {
            cc.vv.ConsoleLog.log("setStorage", key, error);
        }  
    };

    get WcTeamData(){
        return this._wcTeamData;
    }
    canShowWcGuess(){
        cc.vv.ConsoleLog.log("canShowWcGuess",!this._wcGuessAlreadyShow
                            ,cc.vv.DataManager.token !="" 
                            ,this._wcJoinStatus == ProtoBuf.commonProto.UserJoinWorldCupGuessState.Not_Joined
                            ,cc.vv.DataManager.userId != 0
                            ,!cc.isValid(this._wcGuessActivity));
        return !this._wcGuessAlreadyShow 
                && cc.vv.DataManager.token !="" 
                && this._wcJoinStatus == ProtoBuf.commonProto.UserJoinWorldCupGuessState.Not_Joined
                && cc.vv.DataManager.userId != 0
                && !cc.isValid(this._wcGuessActivity);
    }
    onShowWcGuess(guessNode:cc.Node){
        this._wcGuessActivity = guessNode.getComponent(WcGuessingActivity);
    }
    onCloseWcGuess(){
        this._wcGuessActivity = null;
        this._wcGuessAlreadyShow = true;
    }
    createWcGuessingPopup(targetClass: cc.Component, prefab:cc.Prefab){
        if (this.canShowWcGuess()){
            let node = cv.action.addChildToScene(targetClass, prefab, [], cv.Enum.ZORDER_TYPE.ZORDER_TOP, true);
            node.active = true;
            this.onShowWcGuess(node);
        }
    }
    requestWcInitData(){
        /*
        let input:commonProto.ICheck_User_WorldCupGuess_Request={
            UserId: cc.vv.DataManager.userId.toString(),
            PlatForm: this.platform,
            Token: cc.vv.DataManager.token
        }
        cc.vv.ConsoleLog.log("requestWcInitData", input);
        httpApis.requestCheckUserWorldCupGuess(input,(msg:commonProto.Check_User_WorldCupGuess_Response)=>{
            cc.vv.ConsoleLog.log("responseWcInitData", msg);
            if (!msg.ErrorCode){
                this._wcJoinStatus = msg.JoinStatus;
                this._wcTeamData = msg.TeamInfos;

                if (msg.JoinStatus == ProtoBuf.commonProto.UserJoinWorldCupGuessState.Not_Joined){
                    this.sendMessageCenter(this.config.wcEvent.SHOW_GUESSING_ACTIVITY);
                }
            }
        });
        */
    }
    requestWcChoiceTeam(selectedTeamId:number[], response:(msg:commonProto.Choice_WorldCupGuess_Team_Response)=>void, onerror:()=>void){
        let input:commonProto.IChoice_WorldCupGuess_Team_Request = {
            UserId: cc.vv.DataManager.userId.toString(),
            PlatForm: this.platform,
            TeamId: selectedTeamId,
            Token: cc.vv.DataManager.token
        };
        cc.vv.ConsoleLog.log("requestWcChoiceTeam", input);
        httpApis.requestChoiceWorldCupGuessTeam(input, (msg:commonProto.Choice_WorldCupGuess_Team_Response)=>{
            cc.vv.ConsoleLog.log("responseWcChoiceTeam", msg);
           
            if (response){
                response(msg);
            }
        },onerror);
    }
    showToastMsg(msg:string){
        cc.vv.ConsoleLog.log("Wc showToastMsg", msg);
        cv.TT.showMsg(msg, cv.Enum.ToastType.ToastTypeInfo);
    }
    resetWc(){
        this._wcGuessAlreadyShow = false;
        this._wcTeamData = [];
        this._wcJoinStatus = 2;
        if (cc.isValid(this._wcGuessActivity)){
            this._wcGuessActivity.node.destroy();
        }
        if (cc.vv && cc.vv.DataManager){
            cc.vv.DataManager.token = "";
            cc.vv.DataManager.toolUserId = 0;
            cc.vv.DataManager.userData = null;
            cc.vv.DataManager.JoinedTournaments = [];
        }
        this._wcGuessActivity = null;
    }
    onLogout(){
        cc.vv.ConsoleLog.log("MTTConnector onLogout");
        this.resetWc();
    }

    /*
    * please use 'catch' for error handling
    * */
    MTTSearchUser(data:{SearchForeignId:string}){
        const userSearchPromise = (inputData:any) => new Promise((resolve, reject) => {
            httpApis.requestUserSearch(inputData, (msg:commonProto.User_Search_Response) => {
                cc.vv.ConsoleLog.log("MTTSearchUser", msg);
                if (!msg.ErrorCode) {
                    resolve(msg);
                }
                else {
                    let errMsg = Translate("ERROR_CODE_PKW."+msg.ErrorCode);
                    if( msg.ErrorCode == ProtoBuf.commonProto.ErrorCode.User_Not_Exist )
                    {
                        errMsg = Translate(Translation.PKW_BAG.SEARCH_EMPTY);
                    }
                    reject(errMsg);
                }
            }, () => {
                reject(Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE));
            });
        });

        if (this.isWPK) {
            const wpkInput = {
                wuid: data.SearchForeignId
            };
            return HMFHTTPClient.MTTSearchUser(wpkInput).then((wpkMsg:any) => {
                if (wpkMsg.data && wpkMsg.data.uid != 0) {
                    const input = {
                        SearchForeignId: wpkMsg.data.uid.toString()
                    };

                    return userSearchPromise(input).then((msg:commonProto.User_Search_Response) => {
                        msg.ForeignId = data.SearchForeignId;
                        return msg;
                    });
                }
                else {
                    let errMsg = Translate(Translation.PKW_BAG.SEARCH_EMPTY);
                    if (wpkMsg.errMsg || wpkMsg.errorCode) {
                        errMsg = wpkMsg.errMsg || MTTConnector.instance.getLanguageStr(`errorCode.${wpkMsg.errorCode}`);
                    }
                    cc.vv.ConsoleLog.log("mtt search user resolve, search id may not be wpk id\n", errMsg);

                    // search id for bl or pkw
                    return userSearchPromise(data);
                }
            }, (error:any) => {
                cc.vv.ConsoleLog.log("mtt search user reject\n", error);
                let errMsg = Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE);
                if (error.errMsg || error.errorCode) {
                    errMsg = error.errMsg || MTTConnector.instance.getLanguageStr(`errorCode.${error.errorCode}`);
                }
                throw errMsg; //throw errMsg to catch
            });
        }
        else {
            return userSearchPromise(data);
        }
    }
    getLanguageStr(key:string){
        if (this.isWPK){
            return HMFUtils.getLanguageStr(key);
        }else{
            return key;
        }
    }
    showBagToastMessage(toastPrefab:cc.Prefab, parent:cc.Node, hints: string, duration: number = 2){
        // let toast = cc.instantiate(toastPrefab);
        // toast.getComponent(ToastMessage).showToastMessage(hints, duration);
        // toast.parent = parent;
    }
    getBagStatusBarHeight(){
        return 0;
    }
    getSafeArea(){
        return cv.SafeAreaWithDifferentDevices.getSafeArea();
    }
    getUserGold() {
        if (this.isPKW) {
            return cv.StringTools.serverGoldToShowNumber(cv.dataHandler.getUserData().u32Chips);
        }
        else if (this.isWPK) {
            return CurrentUserWalletInfo.amount;
        }
        else {
            return cc.vv.BLDataManager.userData.Gold;
        }
    }
    getUserUsdt() {
        if (this.isPKW) {
            return cv.StringTools.serverGoldToShowNumber(cv.dataHandler.getUserData().usdt);
        }
        else if (this.isWPK) {
            return CurrentUserWalletInfo.USDTAmount;
        }
        else {
            return cc.vv.BLDataManager.userData.Usdt;
        }
    }

    setFindViewShowType(findViewShowType: FindViewShowType)
    {
        cc?.vv?.ConsoleLog?.log("setFindViewShowType", findViewShowType);
        this.currentFindViewShowType = findViewShowType;
        let findView = this.getFindView();
        if( cc.isValid(findView) )
        {

            switch(findViewShowType)
            {
                default:
                    if( cc.isValid(findView.mtt) )
                    {
                        findView.mtt._mttPage.active = false;
                    }
                    if( cc.isValid(findView.jsngGameList) )
                    {
                        findView.jsngGameList.node.active = false;
                        findView.jsngGameList.reset();
                    }
                    break;
                case FindViewShowType.MTT:
                    if( cc.isValid(findView.mtt) )
                    {
                        findView.mtt._mttPage.active = true;
                    }
                    if( cc.isValid(findView.jsngGameList) )
                    {
                        findView.jsngGameList.node.active = false;
                        findView.jsngGameList.reset();
                    }
                    break;
                case FindViewShowType.JSNG:
                    if( cc.isValid(findView.jsngGameList) )
                    {
                        findView.jsngGameList.node.active = true;
                        findView.jsngGameList.scheduleRequestJackpotSngGameList();
                    }
                    if( cc.isValid(findView.mtt) )
                    {
                        findView.mtt._mttPage.active = false;
                    }
                    break;
            }
        }
    }

    checkWptGiftDialogEditBoxUserId(){
        if(this.isWPK){
            return CurrentUserInfo?.user?.userId;
        }
        return 0;
    }
}

class MTTConfig
{
    mttColor:MTTColorConfig = new MTTColorConfig();
    tokenErrorMsg:MTTTokenErrorMsg = new MTTTokenErrorMsg();
    BroadCast:MTTKeyConfigBroadCast = new MTTKeyConfigBroadCast();
    wcEvent:WcEvent = new WcEvent();
}

class MTTColorConfig
{
    bagPrefab: MTTColorBagPrefab = new MTTColorBagPrefab();
    messagePage:MTTColorMessagePage = new MTTColorMessagePage();
}

class MTTColorBagPrefab
{
    headerOff: cc.Color = new cc.Color().fromHEX("#999990");
    headerOn: cc.Color = new cc.Color().fromHEX("#C8A053");
}

class MTTColorMessagePage
{
    headerOff: cc.Color = new cc.Color().fromHEX("#9B9B9B");
    headerOn: cc.Color = new cc.Color().fromHEX("#FFFFFF");
}

class MTTKeyConfigBroadCast
{
    UPDATE_MTT_TOKEN:string = "updateMTTToken";
    HAS_SHOW_DISCONNECT_DIALOG:string = "hasShowDisconnectDialog";
    RECONNECT_WEBSOCKET:string = "reconnectWebsocket";
    FORCE_CLOSE_WEBSOCKET:string = "forceCloseWebcoket";
    LOGIN_SUCCESS: string = "loginSuccess";
    DIALOG_CANCEL_RECONNECT: string = "dialogCancelReconnect";
    CHANGE_LANGUAGE:string = "change_language";
    PLATFORM_TOKEN_UPDATE_SUCCESS:string = "onAuthMttSucc";
    PLATFORM_TOKEN_UPDATE_ERROR:string = "onAuthMttError";
    MTT_TOKEN_UPDATE:string = "mtt_token_update";
    MTT_TOKEN_END:string = "mtt_token_end";
    MTT_TOKEN_ERROR:string = "mtt_token_error";
    LOGOUT:string = "logout";
    LIVEREPLAY_STATUS_UPDATE:string = "liveReplayStatusUpdate";
    CLOSE_ALL_MTT_HALL:string = "closeAllMttHall";
    REFRESH_MTT_BAG_LIST:string = "refreshMttBagList";
    SHOW_GIFT_DIALOG:string = "showGiftDialog";
    GO_BACKPACK:string = "goBackpack";
    AUTO_RESIZE_LABEL_UPDATE_COMPLETED:string = "autoResizeLabelUpdateCompleted";
    UPDATE_MTT_TOURNAMENT_LIST:string = "updateMttTournamentList";
    MYSTERY_BOUNTY_INTRO_COMPLETED:string = "mysteryBountyIntroCompleted";
}

class MTTTokenErrorMsg
{
    NO_TOKEN: string = "mtt_no_token";
    REQUEST_TOKEN_FAIL: string = "mtt_request_token_fail";
    EMPTY_RESPONSE: string = "mtt_empty_response";
    BACK_NORMAL: string = "mtt_back_normal";
    BACK_ABNORMAL: string = "mtt_back_abnormal";
    NETWORK_ERROR: string = "mtt_network_error";
    TOKEN_ERROR: string = "mtt_token_error";
    TOKEN_TIMEOUT: string = "mtt_token_timeout";
}

class WcEvent
{
    SHOW_GUESSING_ACTIVITY: string = "show_guessing_activity";
}
