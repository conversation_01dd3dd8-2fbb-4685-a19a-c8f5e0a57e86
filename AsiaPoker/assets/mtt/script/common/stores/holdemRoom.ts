import {action, observable, observe, spy} from 'mobx';
import {owWebSocket} from "../net/websocket";
import {buildConfig, globalConfig, holdemConfig, localConfig, omahaConfig, ppConfig} from "../mttconfig";
import {ProtoBuf} from "../net/Pb";
import {holdemPlayerStore, holdemSelfStore} from "./holdemPlayer";
import {holdem} from "../pb/holdem";
import {mttPro} from "../pb/mtt";
import {commonProto, modelProto} from "../pb/commonProto";
import {GameEndPopUp} from "../../../prefab/impoker/game/game_scripts/GameEndPopUp";
import {Holdem_Room} from "../../holdem/Holdem_Room_ts";
import {GameOverWindow} from "../../../prefab/impoker/game/game_scripts/GameOverWindow";
import {httpApis} from "../net/httpApis";
import {SignUpBoxProperty, SignUpWithToolPopUp} from "../SignUpWithToolPopUp";
import {Translate} from "../Translator";
import {Translation} from "../lang";
import {WorldWebSocket} from '../net/worldWebsocket';
import {VoiceMessage} from "../../../prefab/common/VoiceMessagePlayer";
import {soundEffect} from "../soundEffect";
import {Holdem_View} from '../../holdem/Holdem_View';
import MttHall from '../../../prefab/impoker/hall/game_list/mtt/mtt_script/MttHall';
import {ActivityPrefab} from "../../../prefab/impoker/hall/activity/ActivityPrefab";
import {GameViewLoader} from "../GameViewLoader";
import {FormatParser} from "../tools/FormatParser";
import { PlayerLayoutType } from '../../holdem/Holdem_Player_ts';
import { SystemAvatar } from '../../../prefab/impoker/hall/profile/personal_setting_page/SystemAvatar';
import LoadingBlocker from '../LoadingBlocker';
import {RoundPlayerRecord} from "../../../prefab/impoker/hall/game_view/game_menu/holdem_game_records/holdem_game_records";
import {Marquee} from "../../../prefab/common/Marquee";
import WebPlatformTools from '../WebPlatformTools';
import { ResourcesLoader } from '../ResourcesLoader';
import CommonTools from '../CommonTools';
import MultipleGame from '../game/MultipleGame';
import {CelebritySelfSetting} from "../../holdem/CelebritySelfSetting";
import MTTConnector from "../MTTConnector";
import {Holdem_Room_jsng} from "../../../../jsng/script/holdem/Holdem_Room_jsng";
import {GameEndPopUpJsng} from "../../../../jsng/script/holdem/GameEndPopUpJsng";
import GeoComplyManager from '../../../../Script/common/security/GeoComplyManager';
import cv from '../../../../Script/components/lobby/cv';
import { WEB_SOCKET_TYPE } from '../net/websocketEnum';

export let STORE_DEBUG = false;

export let HoldemWebSocket: owWebSocket[] = [];

declare module window {
    let ROOM_STORES:holdemRoomStore[];
}

window.ROOM_STORES = [];

if(STORE_DEBUG) {
    spy((event) => {
        if (event.type === 'action') {
            cc.vv.ConsoleLog.log(`[MOBX_ACTION] ${event.name} fired:`, event.arguments)
        }
    });
}

enum HoldemRoomState {
    NEW = 0,
    READY = 1,
}

declare module Object {
    function keys(map:{[key:number]:any}):number[];
    function keys(map:{[key:string]:any}):string[];
}

class Message {
    list: any[] = []
}

export enum HOLDEM_TOURNAMENT_TYPE {
    MTT = 4,
    SNG = 3,
    AOF = 5,
    NORMAL =7,
}

export enum HOLDEM_ANIMATION {
    EMPTY = 0,
    GAME_START = 2,
    GAME_END = 1,
    KO = 3,
    RISE_BLIND = 4,
    VOICE_MSG = 5
}

// frontend should not use custom state, but seems it has a long history, better removed in the future
export enum CUSTOM_PLAYER_STATE {
    EXTEND_TIME = 7,
    WAIT_FOR_START = 106,
    WIN = 999,
}

export class holdemRoomStore {

    ws:owWebSocket;

    tournamentMode: HOLDEM_TOURNAMENT_TYPE;
    tournamentId: number = 0;
    levelId: number = 0;
    tournament: modelProto.SngTournament|modelProto.MttTournament;

    playerUserId: number;

    maxHoleCard = 2;

    @observable
    roomId: number = 0;

    marqueeMsg:string = "";

    @observable
    redPocketPrize:string = "";

    @observable
    redPocketApproved:any;

    @observable
    redPocketRecordId:any;

    focused: boolean = false;

    state: HoldemRoomState = HoldemRoomState.NEW;

    takeInCoin: number;

    node: Holdem_Room_jsng|Holdem_Room;

    roomNode:cc.Node;

    @observable
    self: holdemSelfStore;

    @observable
    seatedPlayers: {[index:number]:holdemPlayerStore} = {};

    @observable
    dealerPos: number;

    @observable
    bigBlind: number = 0;

    @observable
    smallBlind: number = 0;

    @observable
    ante:number = 0;

    minTakein:number = 0;

    maxTakein:number = 0;

    @observable
    straddlePos : number;

    @observable
    sidePot: number[]=[];

    @observable
    tempPot: number = 0;

    @observable
    mainPot: number = 0;

    @observable
    tmpMaxDeskCoin: number = 0;

    // @observable
    // boardCards:number[] = [];

    @observable
    allBoardCards: number[] = [];

    @observable
    boardCardsHighlight:boolean[] = [];

    @observable
    countdownTtl: number = 0;

    @observable
    countdownLeft: number = 0;

    @observable
    countdownSeat: number = 0;

    @observable
    countdownLastSeat: number = 0;

    @observable
    gameRecord:holdem.RoomBillRes;

    @observable
    animate:number =0;

    @observable
    riseBlindTime: number = 0;

    riseBlindHand: number = 0;

    @observable
    waitForStart:boolean = false;

    @observable
    prizeMode: number = 0;

    displayCurrency:string = "";
    //1 Currency to XXX Gold
    exchangeRate:number = 1;
    currencyRate:mttPro.ICurrencyRate[] = [];

    @observable
    rbcList:any[] = [];

    @observable
    rewardList:any[] = [];

    @observable
    toolList:any[] = [];

    @observable
    prizeList:{[rank:number]:{money:number, proportion?:number, tool?:modelProto.ITool}} = {}; // combine rewardList and toolList

    lastRewardMsg:mttPro.RewardMsg;

    @observable
    visitors: holdem.RoomSnapshotMsg.IVisitor[];

    @observable
    roomSnapshotState:number=0;

    @observable
    sngStatus:number=0;

    @observable
    gameDuration: number = 0;

    // @observable
    // sngRealTimeRecordRes: mttPro.SngRealTimeRecordRes;
    //
    // @observable
    // mttRealTimeRecordRes: mttPro.MttRealTimeRecordRes;

    @observable
    prizePool: number = 0;

    @observable
    bountyPool:number = 0;

    @observable
    prizeCircle: number = 0;

    mttTablesDetail: commonProto.IMttTableDetail[];

    @observable
    currentBlindLevel:number = 0;

    @observable
    roomMessage:number = 0;

    roomStatus:number = 0;

    _pauseMessage:string = "";
    resumeMessage:string = "";
    resumeTimeRemain:number = 0;

    @observable
    handForHandMessage:number = 0; // hand for hand game sync

    @observable
    restMessage : number =0;
    @observable
    restTime : number =0;

    restEndTime:number = 0;

    @observable
    roundValue:number = 0;

    @observable
    leftRejoinCount:number = 0;

    @observable
    voiceMessageList:{[userId:number]:VoiceMessage} = {};

    @observable
    buyTimeCount:number = 0;

    @observable
    waitForChangeTable:boolean = false;

    @observable
    isRebuyAllow:boolean = false;
    isRebuyShown:boolean = false;
    @observable
    rebuyLeftTime:number = 0;
    rebuyEndTime:number = 0;

    @observable
    isMorebuyAllow:boolean = false;
    isMorebuyShown:boolean = false;
    @observable
    morebuyLeftTime:number = 0;
    morebuyEndTime:number = 0;

    riseBlinding:boolean = false;

    isPreparing : boolean = false;

    muteList:number[] = [];
    emojiMuteList:number[] = [];
    cameraMuteList:number[] = [];

    voiceInFinal:boolean = true;

    calcBigBlind:number = 0;
    calcSmallBlind:number = 0;
    calcAnte:number = 0;

    totalSeatCount:number = 0;
    tournamentRoomName:string = "";
    SNGReliveLeftTime:number = 0;

    MTTDetail:commonProto.IMttTournamentDetail;

    _roomMode: commonProto.MTT_GAME_MODE;
    _autoPlayAlert:boolean = false;

    private newResetTableTimeout: NodeJS.Timeout;
    private resetTableTime: number = 3000;
    private tournamentType: HOLDEM_TOURNAMENT_TYPE;

    isGps: boolean = false;

    isGameEnd:boolean = false;

    isJointOnResume:boolean = false;

    isSNGMatching:boolean = false;

    hasDisconnected:boolean = false;

    loseFlowPopup:cc.Node[] = [];

    tryCounter:number = 0;

    loseState:boolean[] = [];

    isMultiFlightAlert:boolean = false;

    hasCallGameView:boolean = false;

    checkDate = new Date();

    saveName: string[] = ["selfMessage", "systemMessage"];

    isReplay:boolean = false;

    isMysterB:boolean = false;

    _needAnimation:boolean = true;

    waitForResetTable:boolean = false;

    _rewardCount:number = 0;

    _snapshotSelfFold:boolean = false;
    _notFoldLoopGuess:boolean = false;

    MTTRank:number = 0;

    srvFee:number = 0;
    regFee:number = 0;

    rebuyParam:any;
    morebuyParam:any;
    srvFeeRebuy:number = 0;
    regFeeRebuy:number = 0;
    srvFeeMorebuy:number = 0;
    regFeeMorebuy:number = 0;
    initCoinRebuy:number = 0;
    initCoinMorebuy:number = 0;
    quantityMorebuy:number = 1;

    isShortDeck:boolean = false;
    get blindRiseType() {
        return this.blindHands > 0 ? globalConfig.BLIND_RISE_TYPE.HANDS : globalConfig.BLIND_RISE_TYPE.DURATION;
    }
    blindLevelTime:number = 0;
    blindHands:number = 0;
    url:any;

    get needAnimation() {
        return this._needAnimation;
    }
    set needAnimation(value) {
        this._needAnimation = value;
        if( this.node )
        {
            this.node.needAnimation = value;
        }
    }

    isAppPause:boolean = false;
    mttEndRoomId:number = 0;

    isShowDown:boolean = false;

    hasCallReboundGameView:boolean = false;

    gameViewLoader: GameViewLoader = null;

    _retryEnterRoomTime:number = 0;
    _retryEnterRoomTimeout:any = null;

    anmiTimes:number = 0;
    emojiTimes:number = 0;

    isShowDisconnectDialog:boolean = false;
    isMultiFlightDay1:boolean = false;
    nextPeriodStartTime:number = 0;
    isWPT:boolean = false;
    isCelebrityTournament:boolean = false;

    gameInvitationCode:string = "";
    CelebrityList:commonProto.IMttCelebrityDetail[] = [];

    streamingPlayers:number[] = [];
    streamingAudioPlayers:number[] = [];

    uuid:string = "";
    isMysteryBountyOnIntro: boolean = false;

    constructor(mode: commonProto.MTT_GAME_MODE, tournamentType: HOLDEM_TOURNAMENT_TYPE, tournament: modelProto.SngTournament|modelProto.MttTournament, node:cc.Node, nodeScript:Holdem_Room, isReplay:boolean) {
        this.node = nodeScript;
        if(STORE_DEBUG) {

            cc.vv.ConsoleLog.log(`[ROOM_STORE:${this.tournamentId}] created`);

            window.ROOM_STORES.push(this);

            observe(this, change => {
                // @ts-ignore
                cc.vv.ConsoleLog.log(`[ROOM_STORE:${this.tournamentId}]`, change.type, change.name, "from", change.oldValue, "to", change.object[change.name]);
            });

        }
        this.isReplay = isReplay;

        this.roomNode = node;

        const { holdem, mttPro, commonProto } = ProtoBuf;


        cc.vv.ConsoleLog.log(`holdemRoom constructor`, tournament);
        if (tournamentType!==HOLDEM_TOURNAMENT_TYPE.NORMAL){
            this.tournamentId = tournament.Id;
            if (tournament.LevelId){
                this.levelId = tournament.LevelId;
            }
            this.tournamentType = tournamentType;
        }else{
            this.tournamentId = 3;
        }

        this.loseFlowPopup = new Array(4);
        this.loseState = new Array(4);

        this.tournamentMode = tournamentType;

        const ids = {...holdem.MessageId, ...mttPro.MessageId};

        this.playerUserId = cc.vv.DataManager.userId;

        let url:any;

        this._roomMode = mode;

        if(mode == commonProto.MTT_GAME_MODE.NLH || mode == commonProto.MTT_GAME_MODE.SHORT_DECK) {
            this.isShortDeck = mode == commonProto.MTT_GAME_MODE.SHORT_DECK;
            if (tournamentType==HOLDEM_TOURNAMENT_TYPE.MTT){
                url = holdemConfig.mttWsUrl;
            } else if (tournamentType == HOLDEM_TOURNAMENT_TYPE.NORMAL){
                url = holdemConfig.WsUrl;
            }else if (tournamentType == HOLDEM_TOURNAMENT_TYPE.SNG || tournamentType == HOLDEM_TOURNAMENT_TYPE.AOF){
                url = holdemConfig.sngWsUrl;
            }
            // url = tournamentType == HOLDEM_TOURNAMENT_TYPE.MTT ? holdemConfig.mttWsUrl : holdemConfig.sngWsUrl
        }else{
            this.isShortDeck = false;
            url = tournamentType == HOLDEM_TOURNAMENT_TYPE.MTT ? holdemConfig.mttWsUrl : omahaConfig.sngWsUrl;
            this.maxHoleCard = 4;
        }

        /**Init Mute List*/
        this.muteList = JSON.parse(MTTConnector.instance.getStorage(localConfig.key_muteList, false));
        if (!this.muteList){
            this.muteList = [];
        } else {
            //remove duplicate user ids if any
            this.muteList = this.muteList.filter((value, index, array) => {
                value = typeof value === "string" ? parseInt(value) : value;
                return array.indexOf(value) === index;
            });
        }
        cc.vv.ConsoleLog.log("Init mute List", JSON.stringify(this.muteList));
        
        /**Init Emoji Mute List*/
        this.emojiMuteList = JSON.parse(MTTConnector.instance.getStorage(localConfig.key_emojiMuteList, false));
        if (!this.emojiMuteList){
            this.emojiMuteList = [];
        }
        cc.vv.ConsoleLog.log("Init emoji mute List", JSON.stringify(this.emojiMuteList));

        /**Init Camera Mute List*/
        this.cameraMuteList = JSON.parse(MTTConnector.instance.getStorage(localConfig.key_cameraMuteList, false));
        if (!this.cameraMuteList){
            this.cameraMuteList = [];
        }
        cc.vv.ConsoleLog.log("Init camera mute List", JSON.stringify(this.cameraMuteList));

        // this.broadcastMessageEnvelope(null);

        this.gameViewLoader = new GameViewLoader();
    }

    setReconnectCallBack()
    {
        if( MultipleGame.instance )
        {
            MultipleGame.instance.setHoldemReconnectCallBack(this.node.multipleGameIndex, ()=>{
                this.showLoading("Holdem Game");
                // if( this.node )
                // {
                //     this.node.showLoading();
                // }
                // hide player control when lost connection
                if (this.node){
                    cc.vv.ConsoleLog.log('lost connection, hide player control , removeGuessHandLoop');
                    this.node.ResetPlayerControl();
                    this.node.RemoveGuessHandLoop();
                }
            },
            ()=>{
                this.hideLoading("Holdem Game");
                // if( this.node )
                // {
                //     this.node.hidLoading();
                // }
            },
            this.connectFail,
            null);
        }
        else
        {
            owWebSocket.setReconnectCallBack(()=>{
                this.showLoading("Holdem Game");
                // if( this.node )
                // {
                //     this.node.showLoading();
                // }
                // hide player control when lost connection
                if (this.node){
                    cc.vv.ConsoleLog.log('lost connection, hide player control , removeGuessHandLoop');
                    this.node.ResetPlayerControl();
                    this.node.RemoveGuessHandLoop();
                }
            },
            ()=>{
                this.hideLoading("Holdem Game");
                // if( this.node )
                // {
                //     this.node.hidLoading();
                // }
            },
            this.connectFail,
            this.VerifyToken);
        }
        
    }

    setCallBack()
    {
        if( MultipleGame.instance )
        {
            // MultipleGame.instance.setWorldCallBack(this.node.multipleGameIndex, (msg:any)=>{
            //     this.repeatLoginDialog(msg);
            // },
            // (data:any)=>{
            //     this.updateUserInfo(data);
            // },
            // (msg:commonProto.Broadcast_Message_Envelope)=>{
            //     this.broadcastMessageEnvelope(msg)
            // }, 
            // ()=>{
            //     if(this.node && this.node.loadingBlockerScript)
            //     {
            //         MultipleGame.instance.showLoading("Holdem World");
            //     }
            //     // LoadingBlocker.show("Holdem World");
            //     // if( this.node )
            //     // {
            //     //     this.node.showLoading();
            //     // }
            // },
            // ()=>{
            //     if(this.node && this.node.loadingBlockerScript)
            //     {
            //         MultipleGame.instance.hideLoading("Holdem World");
            //     }
            //     // LoadingBlocker.hide("Holdem World");
            //     // if( this.node )
            //     // {
            //     //     this.node.hidLoading();
            //     // }
            // },
            // null,
            // ()=>{
            //     if (cc.vv.DataManager.token) {
            //         WorldWebSocket.getInstance().requestSecureTokenCheck();
            //     }
            // },
            // ()=>{
            //     cc.vv.ConsoleLog.log("Token end", WorldWebSocket.gameSocket, WorldWebSocket.gameSocket ? WorldWebSocket.gameSocket.isConnected() + " " + WorldWebSocket.gameSocket.isReconnecting : "");
            //     if( WorldWebSocket.gameSocket && !WorldWebSocket.gameSocket.isConnected() && !WorldWebSocket.gameSocket.isReconnecting )
            //     {
            //         WorldWebSocket.gameSocket.setReConnect();
            //         WorldWebSocket.gameSocket.reConnect();
            //     }
            // });
        }
        else
        {
            WorldWebSocket.setCallBack((msg:any)=>{
                this.repeatLoginDialog(msg);
            },
            (data:any)=>{
                this.updateUserInfo(data);
            },
            (msg:commonProto.Broadcast_Message_Envelope)=>{
                this.broadcastMessageEnvelope(msg)
            }, 
            ()=>{
                if(this.node && this.node.loadingBlockerScript)
                {
                    this.showLoading("Holdem World");
                }
                // LoadingBlocker.show("Holdem World");
                // if( this.node )
                // {
                //     this.node.showLoading();
                // }
            },
            ()=>{
                if(this.node && this.node.loadingBlockerScript)
                {
                    this.hideLoading("Holdem World");
                }
                // LoadingBlocker.hide("Holdem World");
                // if( this.node )
                // {
                //     this.node.hidLoading();
                // }
            },
            null,
            ()=>{
                if (cc.vv.DataManager.token) {
                    WorldWebSocket.getInstance().requestSecureTokenCheck();
                }
            },
            ()=>{
                cc.vv.ConsoleLog.log("Token end", WorldWebSocket.gameSocket, WorldWebSocket.gameSocket ? WorldWebSocket.gameSocket.isConnected() + " " + WorldWebSocket.gameSocket.isReconnecting : "");
                if( WorldWebSocket.gameSocket && !WorldWebSocket.gameSocket.isConnected() && !WorldWebSocket.gameSocket.isReconnecting )
                {
                    WorldWebSocket.gameSocket.setReConnect();
                    WorldWebSocket.gameSocket.reConnect();
                }
            });
        }
        
        cc.vv.DataManager.worldNetwork.setReConnect();
        cc.vv.DataManager.worldNetwork.connect();
    }

    connectWebSocket()
    {
       
        // cc.vv.ConsoleLog.log("holdemWebSocket",holdemWebSocket);
        const { holdem, mttPro, commonProto } = ProtoBuf;
        const ids = {...holdem.MessageId, ...mttPro.MessageId};
        if(this._roomMode == commonProto.MTT_GAME_MODE.NLH || this._roomMode == commonProto.MTT_GAME_MODE.SHORT_DECK) {
            if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT){
                this.url = WEB_SOCKET_TYPE.HOLDEM_MTT;
            } else if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.NORMAL){
                this.url = WEB_SOCKET_TYPE.HOLDEM;
            }else if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.SNG || this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.AOF){
                this.url = WEB_SOCKET_TYPE.HOLDEM_SNG;
            }
            // url = tournamentType == HOLDEM_TOURNAMENT_TYPE.MTT ? holdemConfig.mttWsUrl : holdemConfig.sngWsUrl
        }else{
            // OMAHA need to connect to HOLDEM_MTT instead of OMAHA_MTT because we need to support multipleGame. And owWebsocket only support single game websocket
            this.url = this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT ? WEB_SOCKET_TYPE.HOLDEM_MTT : WEB_SOCKET_TYPE.OMAHA_SNG;
            this.maxHoleCard = 4;
        }
        let holdemWebSocket = this.ws =  HoldemWebSocket[this.url];
        if( MultipleGame.instance )
        {
            
            if( !this.isReplay )
            {
                this.setReconnectCallBack();
            }
            if(this.node && !cc.vv.DataManager.isWebReplay)
            {
                this.setCallBack();
            }
    
            this.AddMessageHandler();
            MultipleGame.instance.connectGame(this.node.multipleGameType, this.node.multipleGameIndex);
        }
        else
        {
            if( !this.isReplay )
            {
                if( holdemWebSocket && holdemWebSocket.isConnected() )
                {
                    holdemWebSocket.close(true, true);
                }
                this.setReconnectCallBack();
                holdemWebSocket = this.ws =  HoldemWebSocket[this.url] = new owWebSocket(this.url, {...mttPro, ...holdem, MessageId: ids});
                WorldWebSocket.gameSocket = this.ws;
               
                this.ws.setReConnect();
            }
            // else if(!this.isReplay){
                
                // setTimeout(()=>{
                //     this.EnterRoom();
                // }, 1000);
            // }
            if(this.node && !cc.vv.DataManager.isWebReplay)
            {
                this.setCallBack();
            }
    
    
            this.AddMessageHandler();
        }
       
        // this.ws.connect();
    }

    AddMessageHandler() {
        if( !this.isReplay )
        {
            const { holdem, mttPro, commonProto } = ProtoBuf;
            const ids = {...holdem.MessageId, ...mttPro.MessageId};
            // this.ws.AddMessageHandler(owWebSocket.EVENT_ID.ON_CONNECTED, this.onConnection);
            // this.ws.AddMessageHandler(owWebSocket.EVENT_ID.ON_CLOSE, this.onLostConnection);
            this.ws.AddMessageHandler(ids.UserTokenRes, this.onUserTokenRes);
            this.ws.AddMessageHandler(ids.HideHoleCardRes, this.onHideHoleCardRes);
            this.ws.AddMessageHandler(ids.MttEnterGameRes, this.onMttEnterGameRes);
            this.ws.AddMessageHandler(ids.EnterRoomRes, this.onEnterRoomRes);
            this.ws.AddMessageHandler(ids.SitDownRes, this.onSitDownRes);
            this.ws.AddMessageHandler(ids.SeatOccupiedMsg, this.onSeatOccupiedMsg);
            this.ws.AddMessageHandler(ids.SeatEmptyMsg, this.onSeatEmptyMsg);
            this.ws.AddMessageHandler(ids.ActionRes, this.onActionRes);
            this.ws.AddMessageHandler(ids.PlayerActionMsg, this.onPlayerActionMsg);
            this.ws.AddMessageHandler(ids.NeedActionMsg, this.onNeedActionMsg);
            this.ws.AddMessageHandler(ids.RoomSnapshotMsg, this.onRoomSnapshotMsg);
            this.ws.AddMessageHandler(ids.SngRoomSnapShotMsg, this.onSngRoomSnapShotMsg);
            this.ws.AddMessageHandler(ids.HoleCardsMsg, this.onHoleCardsMsg);
            this.ws.AddMessageHandler(ids.HoleCardListMsg, this.onHoleCardList);
            this.ws.AddMessageHandler(ids.BoardCardsMsg, this.onBoardCardsMsg);
            this.ws.AddMessageHandler(ids.DealerPosMsg, this.onDealerPosMsgNewGame);
            this.ws.AddMessageHandler(ids.PauseGameRes, this.onPauseGameRes);
            this.ws.AddMessageHandler(ids.LeaveRoomRes, this.onLeaveRoomRes);
            this.ws.AddMessageHandler(ids.RoomBillRes, this.onRoomBillRes);
            this.ws.AddMessageHandler(ids.PotsMsg, this.onPotsMsg);
            this.ws.AddMessageHandler(ids.AutoPlayMsg, this.onAutoPlayMsg);
            this.ws.AddMessageHandler(ids.StandbyRes, this.onStandbyRes);
            this.ws.AddMessageHandler(ids.ShowdownMsg, this.onShowdownMsg);
            this.ws.AddMessageHandler(ids.RoundResultMsg, this.onRoundResultMsg);
            this.ws.AddMessageHandler(ids.PlayerNickNameChangeMsg, this.onPlayerNickNameChange);
            this.ws.AddMessageHandler(ids.NeedMoreCoinMsg, this.onNeedMoreCoinMsg);
            this.ws.AddMessageHandler(ids.RiseBlindNotifyMsg, this.onRiseBlindNotifyMsg);
            this.ws.AddMessageHandler(ids.TimeBankMsg, this.onTimeBankMsg);
            this.ws.AddMessageHandler(ids.TimeBankFlagSetRes,this.onTimeBankFlagSetRes);
            this.ws.AddMessageHandler(ids.TimeBankDurationMsg,this.onTimeBankDurationMsg);
            this.ws.AddMessageHandler(ids.RewardMsg, this.onRewardMsg);
            this.ws.AddMessageHandler(ids.SngStartNotifyMsg, this.onSngStartNotifyMsg);
            this.ws.AddMessageHandler(ids.SngRoomRankNotifyMsg, this.onSngRoomRankNotifyMsg);
            this.ws.AddMessageHandler(ids.MttRoomRankNotifyMsg, this.onMTTRoomRankNotifyMsg);
            this.ws.AddMessageHandler(ids.PlayerStateMsg, this.onPlayerStateMsg);
            this.ws.AddMessageHandler(ids.MttNotifyMsg, this.onMttNotifyMsg);
            this.ws.AddMessageHandler(ids.SngRealTimeRecordRes, this.onSngRealTimeRecordRes);
            this.ws.AddMessageHandler(ids.MttRealTimeRecordRes, this.onMttRealTimeRecordRes);
            this.ws.AddMessageHandler(ids.OtherRoomMsg,this.onOtherRoomMsg);
            this.ws.AddMessageHandler(ids.BuyTimeRes,this.onBuyTime);
            this.ws.AddMessageHandler(ids.MttLastRoomNotifyMsg,this.onMttLastRoomNotifyMsg);
            this.ws.AddMessageHandler(ids.EnterRewardMsg,this.onEnterRewardMsg);
            this.ws.AddMessageHandler(ids.MttRoomEndNotifyMsg,this.onMttRoomEndNotifyMsg);
            this.ws.AddMessageHandler(ids.MttRoomSnapshotRes,this.onMttRoomSnapShot);
            this.ws.AddMessageHandler(ids.MttRestTimeNotifyMsg,this.onMttRestTimeNotifyMsg);
            this.ws.AddMessageHandler(ids.SngReliveRes,this.onSngRelive);
            this.ws.AddMessageHandler(ids.MttStopReJoinNotifyMsg,this.onMttStopReJoinNotifyMsg);
            this.ws.AddMessageHandler(ids.VoiceRes,this.onVoiceRes);
            this.ws.AddMessageHandler(ids.VoiceMsg,this.onVoiceMsg);
            this.ws.AddMessageHandler(ids.MttUserRankMsg, this.onMttUserRankMsg);
            this.ws.AddMessageHandler(ids.MttExDataMsg, this.onMttExDataMsg);
            this.ws.AddMessageHandler(ids.SngRoomExDataMsg, this.onSngExDataMsg);
            this.ws.AddMessageHandler(ids.AnimMsg, this.onAnimMsg);
            this.ws.AddMessageHandler(ids.AnimRes, this.onAnimRes);
            this.ws.AddMessageHandler(ids.Emoji, this.onEmojiMsg);
            this.ws.AddMessageHandler(ids.EmojiRes, this.onEmojiRes);
            this.ws.AddMessageHandler(ids.MttHoldemStadiumRes, this.onMTTHoldemStadiumRes);
            this.ws.AddMessageHandler(ids.MttUserInfoRes, this.onMttUserInfoRes);
            this.ws.AddMessageHandler(ids.MttUserGameSumInfoRes, this.onMTTUserGameSumInfoRes);
            this.ws.AddMessageHandler(ids.MttRebuyMsg, this.onMttRebuyMsg);
            this.ws.AddMessageHandler(ids.MttMorebuyMsg, this.onMttMorebuyMsg);
            this.ws.AddMessageHandler(ids.MttCancelBuyRes, this.onMttCancelBuyRes);
            this.ws.AddMessageHandler(ids.SngRewardInfoRes, this.onSngRewardInfoRes);
            this.ws.AddMessageHandler(ids.ShowCardRes, this.onShowCardRes);
            this.ws.AddMessageHandler(ids.MttStateNotifyMsg, this.onMttStateNotifyMsg);
            this.ws.AddMessageHandler(ids.RedPocketCarouseMsg, this.onRedPocketCarouseMsg);
            this.ws.AddMessageHandler(ids.GuessHandlePorkRes, this.onGuessHandlePorkRes);
            this.ws.AddMessageHandler(ids.GuessHandlePorkMsg, this.onGuessHandlePorkMsg);
            this.ws.AddMessageHandler(ids.CelebrityBroadcastRes, this.onCelebrityBroadcastRes);
            this.ws.AddMessageHandler(ids.CelebrityBroadcastNotifyFullMsg, this.onCelebrityBroadcastNotifyFullMsg);
            this.ws.AddMessageHandler(ids.CelebrityBroadcastListMsg, this.onCelebrityBroadcastListMsg);
            this.ws.AddMessageHandler(ids.User_ForbidMsg, this.onUserForbidMsg);
            this.ws.AddMessageHandler(ids.ExitSngRoomLevelRes, this.onExitSngRoomLevelRes);
            this.ws.AddMessageHandler(ids.MysteryPrizeMsg, this.onMysteryPrizeMsg);
            this.ws.AddMessageHandler(ids.MysteryPrizeTableRes, this.onMysteryPrizeTableRes);
            this.ws.AddMessageHandler(ids.MysteryPrizeTableMsg, this.onMysteryPrizeTableMsg);
            this.gameViewLoader.bindMessageHandler();
        }
    }

    RemoveMessageHandler() {
        if( !this.isReplay )
        {
            const { holdem, mttPro, commonProto } = ProtoBuf;
            const ids = {...holdem.MessageId, ...mttPro.MessageId};
            // this.ws.RemoveMessageHandler(owWebSocket.EVENT_ID.ON_CONNECTED, this.onConnection);
            // this.ws.RemoveMessageHandler(owWebSocket.EVENT_ID.ON_CLOSE, this.onLostConnection);
            this.ws.RemoveMessageHandler(ids.UserTokenRes, this.onUserTokenRes);
            this.ws.RemoveMessageHandler(ids.HideHoleCardRes, this.onHideHoleCardRes);
            this.ws.RemoveMessageHandler(ids.MttEnterGameRes, this.onMttEnterGameRes);
            this.ws.RemoveMessageHandler(ids.EnterRoomRes, this.onEnterRoomRes);
            this.ws.RemoveMessageHandler(ids.SitDownRes, this.onSitDownRes);
            this.ws.RemoveMessageHandler(ids.SeatOccupiedMsg, this.onSeatOccupiedMsg);
            this.ws.RemoveMessageHandler(ids.SeatEmptyMsg, this.onSeatEmptyMsg);
            this.ws.RemoveMessageHandler(ids.ActionRes, this.onActionRes);
            this.ws.RemoveMessageHandler(ids.PlayerActionMsg, this.onPlayerActionMsg);
            this.ws.RemoveMessageHandler(ids.NeedActionMsg, this.onNeedActionMsg);
            this.ws.RemoveMessageHandler(ids.RoomSnapshotMsg, this.onRoomSnapshotMsg);
            this.ws.RemoveMessageHandler(ids.HoleCardsMsg, this.onHoleCardsMsg);
            this.ws.RemoveMessageHandler(ids.HoleCardListMsg, this.onHoleCardList);
            this.ws.RemoveMessageHandler(ids.BoardCardsMsg, this.onBoardCardsMsg);
            this.ws.RemoveMessageHandler(ids.DealerPosMsg, this.onDealerPosMsgNewGame);
            this.ws.RemoveMessageHandler(ids.PauseGameRes, this.onPauseGameRes);
            this.ws.RemoveMessageHandler(ids.LeaveRoomRes, this.onLeaveRoomRes);
            this.ws.RemoveMessageHandler(ids.RoomBillRes, this.onRoomBillRes);
            this.ws.RemoveMessageHandler(ids.PotsMsg, this.onPotsMsg);
            this.ws.RemoveMessageHandler(ids.AutoPlayMsg, this.onAutoPlayMsg);
            this.ws.RemoveMessageHandler(ids.StandbyRes, this.onStandbyRes);
            this.ws.RemoveMessageHandler(ids.ShowdownMsg, this.onShowdownMsg);
            this.ws.RemoveMessageHandler(ids.RoundResultMsg, this.onRoundResultMsg);
            this.ws.RemoveMessageHandler(ids.PlayerNickNameChangeMsg, this.onPlayerNickNameChange);
            this.ws.RemoveMessageHandler(ids.NeedMoreCoinMsg, this.onNeedMoreCoinMsg);
            this.ws.RemoveMessageHandler(ids.RiseBlindNotifyMsg, this.onRiseBlindNotifyMsg);
            this.ws.RemoveMessageHandler(ids.TimeBankMsg, this.onTimeBankMsg);
            this.ws.RemoveMessageHandler(ids.TimeBankFlagSetRes,this.onTimeBankFlagSetRes);
            this.ws.RemoveMessageHandler(ids.TimeBankDurationMsg,this.onTimeBankDurationMsg);
            this.ws.RemoveMessageHandler(ids.RewardMsg, this.onRewardMsg);
            this.ws.RemoveMessageHandler(ids.SngStartNotifyMsg, this.onSngStartNotifyMsg);
            this.ws.RemoveMessageHandler(ids.SngRoomRankNotifyMsg, this.onSngRoomRankNotifyMsg);
            this.ws.RemoveMessageHandler(ids.MttRoomRankNotifyMsg, this.onMTTRoomRankNotifyMsg);
            this.ws.RemoveMessageHandler(ids.SngRoomSnapShotMsg, this.onSngRoomSnapShotMsg);
            this.ws.RemoveMessageHandler(ids.MttRoomSnapshotRes, this.onMttRoomSnapShot);
            this.ws.RemoveMessageHandler(ids.PlayerStateMsg, this.onPlayerStateMsg);
            this.ws.RemoveMessageHandler(ids.MttNotifyMsg, this.onMttNotifyMsg);
            this.ws.RemoveMessageHandler(ids.SngRealTimeRecordRes, this.onSngRealTimeRecordRes);
            this.ws.RemoveMessageHandler(ids.MttRealTimeRecordRes, this.onMttRealTimeRecordRes);
            this.ws.RemoveMessageHandler(ids.OtherRoomMsg, this.onOtherRoomMsg);
            this.ws.RemoveMessageHandler(ids.BuyTimeRes, this.onBuyTime);
            this.ws.RemoveMessageHandler(ids.MttLastRoomNotifyMsg, this.onMttLastRoomNotifyMsg);
            this.ws.RemoveMessageHandler(ids.EnterRewardMsg, this.onEnterRewardMsg);
            this.ws.RemoveMessageHandler(ids.MttRoomEndNotifyMsg, this.onMttRoomEndNotifyMsg);
            this.ws.RemoveMessageHandler(ids.MttRestTimeNotifyMsg, this.onMttRestTimeNotifyMsg);
            this.ws.RemoveMessageHandler(ids.SngReliveRes, this.onSngRelive);
            this.ws.RemoveMessageHandler(ids.MttStopReJoinNotifyMsg, this.onMttStopReJoinNotifyMsg);
            this.ws.RemoveMessageHandler(ids.VoiceRes, this.onVoiceRes);
            this.ws.RemoveMessageHandler(ids.VoiceMsg, this.onVoiceMsg);
            this.ws.RemoveMessageHandler(ids.MttUserRankMsg, this.onMttUserRankMsg);
            this.ws.RemoveMessageHandler(ids.MttExDataMsg, this.onMttExDataMsg);
            this.ws.RemoveMessageHandler(ids.SngRoomExDataMsg, this.onSngExDataMsg);
            this.ws.RemoveMessageHandler(ids.AnimMsg, this.onAnimMsg);
            this.ws.RemoveMessageHandler(ids.AnimRes, this.onAnimRes);
            this.ws.RemoveMessageHandler(ids.Emoji, this.onEmojiMsg);
            this.ws.RemoveMessageHandler(ids.EmojiRes, this.onEmojiRes);
            this.ws.RemoveMessageHandler(ids.MttHoldemStadiumRes, this.onMTTHoldemStadiumRes);
            this.ws.RemoveMessageHandler(ids.MttUserInfoRes, this.onMttUserInfoRes);
            this.ws.RemoveMessageHandler(ids.MttUserGameSumInfoRes, this.onMTTUserGameSumInfoRes);
            this.ws.RemoveMessageHandler(ids.MttRebuyMsg, this.onMttRebuyMsg);
            this.ws.RemoveMessageHandler(ids.MttMorebuyMsg, this.onMttMorebuyMsg);
            this.ws.RemoveMessageHandler(ids.MttCancelBuyRes, this.onMttCancelBuyRes);
            this.ws.RemoveMessageHandler(ids.SngRewardInfoRes, this.onSngRewardInfoRes);
            this.ws.RemoveMessageHandler(ids.ShowCardRes, this.onShowCardRes);
            this.ws.RemoveMessageHandler(ids.MttStateNotifyMsg, this.onMttStateNotifyMsg);
            this.ws.RemoveMessageHandler(ids.RedPocketCarouseMsg, this.onRedPocketCarouseMsg);
            this.ws.RemoveMessageHandler(ids.GuessHandlePorkRes, this.onGuessHandlePorkRes);
            this.ws.RemoveMessageHandler(ids.GuessHandlePorkMsg, this.onGuessHandlePorkMsg);
            this.ws.RemoveMessageHandler(ids.CelebrityBroadcastRes, this.onCelebrityBroadcastRes);
            this.ws.RemoveMessageHandler(ids.CelebrityBroadcastNotifyFullMsg, this.onCelebrityBroadcastNotifyFullMsg);
            this.ws.RemoveMessageHandler(ids.CelebrityBroadcastListMsg, this.onCelebrityBroadcastListMsg);
            this.ws.RemoveMessageHandler(ids.User_ForbidMsg, this.onUserForbidMsg);
            this.ws.RemoveMessageHandler(ids.ExitSngRoomLevelRes, this.onExitSngRoomLevelRes);
            this.ws.RemoveMessageHandler(ids.MysteryPrizeMsg, this.onMysteryPrizeMsg);
            this.ws.RemoveMessageHandler(ids.MysteryPrizeTableRes, this.onMysteryPrizeTableRes);
            this.ws.RemoveMessageHandler(ids.MysteryPrizeTableMsg, this.onMysteryPrizeTableMsg);

            this.gameViewLoader.unbindMessageHandler();
        }
    }

    showLoading(key:string)
    {
        if( MultipleGame.instance )
        {
            MultipleGame.instance.showLoading(key);
        }
        else if(this.node)
        {
            this.node.showLoading(key);
        }
    }

    hideLoading(key:string)
    {
        if( MultipleGame.instance )
        {
            MultipleGame.instance.hideLoading(key);
        }
        else if(this.node)
        {
            this.node.hideLoading(key);
        }
    }

    showDialogBox = (title:string, content:string, closeActive:any, options:any, popupParent:cc.Node = null, response:any = null, dialogKey:string = "") => {
        if(this.node && this.node.dialogController)
        {
            if( !popupParent )
            {
                popupParent = this.node.dialogLayer;
            }
            this.node.dialogController.showDialogBox(title, content, closeActive, options, popupParent, response, dialogKey);
        }
        else
        {
            if( !popupParent )
            {
                popupParent = cc.vv.AssetsManager.popupParent;
            }
            cc.vv.AssetsManager.showDialogBox(title, content, closeActive, options, popupParent, response, dialogKey);
        }
    }

    Destroy(){

        // const { holdem, mttPro } = ProtoBuf;
        // const ids = {...holdem.MessageId, ...mttPro.MessageId};

        this.uncacheAllVoiceMessageList();
        
        if(MultipleGame.instance)
        {
            if( this._retryEnterRoomTimeout )
            {
                clearTimeout(this._retryEnterRoomTimeout);
            }
            if( !this.isReplay )
            {
                this.RemoveMessageHandler();
            }
        }
        else
        {
            HoldemWebSocket[this.url] = null;
            if( this._retryEnterRoomTimeout )
            {
                clearTimeout(this._retryEnterRoomTimeout);
            }
            if(this.node && !cc.vv.DataManager.isWebReplay)
            {
                WorldWebSocket.setCallBack();
            }
            // this.ws.setReConnect();
            if( !this.isReplay )
            {
                if( this.ws )
                {
                    this.ws.close(true, true);
                }
                this.RemoveMessageHandler();
            }
        }

        
    }

    @action
    Reset() {
        // this.bigBlind = 0;
        // this.ante =0;
        // this.smallBlind = 0;
        this.tempPot = 0;
        this.mainPot = 0;
        // this.boardCards = [];
        this.allBoardCards = [];
        this.seatedPlayers = {};
        this.countdownTtl = 0;
        this.countdownLeft = 0;
        this.countdownSeat = 0;
        this.countdownLastSeat = 0;
    }

    @action
    resetAnimate(){
        this.animate = HOLDEM_ANIMATION.EMPTY;
    }
    @action
    setAnimate(animate:any){
        this.animate = animate;
    }

    setMarqueeMsg(msg:string){
        this.node.addNotification(msg);
    }
    @action
    setRedPocketDialog(amount:any,approved:any,recordId:any){
        this.redPocketPrize = amount;
        this.redPocketApproved = approved;
        this.redPocketRecordId = recordId;
    }
    @action
    resetRedPocketDialog(){
        this.redPocketPrize = "";
    }

    VerifyToken = ()=>{
        cc.vv.ConsoleLog.log('VerifyToken: '+cc.vv.DataManager.token);
        if( cc.vv.DataManager.token )
        {
            this.ws.Send(ProtoBuf.holdem.UserTokenReq.create({
                token:cc.vv.DataManager.token,
                userId: this.playerUserId,
                hideHole:localConfig.getLocalStorageItem(localConfig.key_enableAutoCheck, 0) == 1 // hard set false
            }));
        }
    }

    connectFail = ()=>
    {
        let dialogKeyConnectFailDialog = "ConnectFailDialog";
        var dialogTexts = cc.vv.LanguageData.t("NETWORK.DISCONNECT_DIALOG").split("|");
        if( !this.isShowDisconnectDialog )
        {
            this.hideLoading("All");
            this.isShowDisconnectDialog = true;
            cc.vv.ConsoleLog.log("connectFail", dialogTexts[1]);
            let self = this;
            this.showDialogBox(dialogTexts[0], cc.vv.LanguageData.t("NETWORK.PINEAPPLE_DISCONNECT"), false, [
                {
                    type: 0,
                    text: dialogTexts[3],
                    callback: function callback() {
                        cc.vv.ConsoleLog.log("Reconnect game websocket");
                        
                        if( MultipleGame.instance )
                        {
                            MultipleGame.instance.hideDialogBox(self.node.multipleGameType, dialogKeyConnectFailDialog);
                        }
                        self.ws.reconnectCount = 0;
                        self.ws.unrespondedVertificationTokenCount = 0;
                        self.ws.setReConnect();
                        self.ws.reConnect();
                        if (WorldWebSocket.checkNetwork("disconnected")) {
                            WorldWebSocket.getInstance().reconnectCount = 0;
                            WorldWebSocket.getInstance().setReConnect();
                            WorldWebSocket.getInstance().reConnect();
                        }
                        self.isShowDisconnectDialog = false;
                    }
                },
                {
                    type: 1,
                    text: dialogTexts[4],
                    callback: function callback() {
                        // cc.vv.DataManager.token = null;
                        cc.vv.ConsoleLog.log("Cancel game reconnect dialog");
                        
                        if( MultipleGame.instance )
                        {
                            MultipleGame.instance.hideDialogBox(self.node.multipleGameType, dialogKeyConnectFailDialog);
                        }
                        self.ws.reset();
                        self.isShowDisconnectDialog = false;
                        self.ws.stopAllConnect = true;
                        // WorldWebSocket.logout();
                    }
                }
            ], null, null, dialogKeyConnectFailDialog);
        }
    }

    protected onUserTokenRes = (msg: holdem.UserTokenRes) => {
        cc.vv.ConsoleLog.log('onUserTokenRes', JSON.stringify(msg));
        if(msg.code === 0) {
            cc.vv.ConsoleLog.log('token cb');
                // this.ws.reconnectCount = 0;
            this.ws.verifyTokenEnd();
            this.EnterRoom();
        }else{
            // back to hall ? or re login ?
            // this.node.initHideLoading()
            if(msg.code!==50001){
                if (msg.code==50003){
                    let txt = msg.code+": userTokenRes "+cc.vv.DataManager.getNow();
                    this.showDialogBox(Translate("MESSAGE_DIALOG_BLOCKER.SYSTEM_ERROR"),txt,false,[
                        {   type: 0,
                            text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                            callback:()=>{this.reboundGameView();},
                        },
                    ])
                } else{
                    this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.code),()=>{this.reboundGameView();});
                }
            }else{
                // if( !this.isReplay )
                // {
                //     this.ws.close(true, true);
                //     WorldWebSocket.gameSocket = null;
                // }
                if( !WorldWebSocket.hasShowLoginFailDialog )
                {
                    WorldWebSocket.hasShowLoginFailDialog = true;
                    if(cc.vv.DataManager.userData.Status == 1){
                        LoadingBlocker.instance.hide("All");
                        // this.node.initHideLoading(()=>{
                        //     LoadingBlocker.instance.hide("All");
                        // });
                    }
                    let hints = (cc.vv.DataManager.userData.Status == 1)?Translate(Translation.LOGIN.USER_ACCOUNT_LOCKED):Translate(Translation.GAME.LOGIN_TIME_OUT);
                    this.callPopUpBox(hints,()=>{
                        WorldWebSocket.hasShowLoginFailDialog = false;
                        WorldWebSocket.logout();
                    });
                }
            }
        }
    };

    MTTEnterRoom(data:any)
    {
        this.ws.Send(ProtoBuf.mttPro.MttEnterGameReq.create(data));
        // this.ws.Send(ProtoBuf.mttPro.MttEnterGameReq.create(data), -1, [ProtoBuf.holdem.MessageId.RoomSnapshotMsg, ProtoBuf.mttPro.MessageId.MttRoomSnapshotRes], ()=>{
        //     cc.vv.ConsoleLog.log("Test MTT timeout", this.isGameEnd);
        //     if( !this.isGameEnd )
        //     {
        //         this.ws.close(true);
        //         this.ws.disconnected();
        //     }
        // }, ()=>{
        //     cc.vv.ConsoleLog.log("Test MTT success");
        //     if( this.node )
        //     {
        //         this.node.hidLoading();
        //     }
        // });
    }

    EnterSngRoomLevel(){
        cc.vv.ConsoleLog.log('pat_entersngroomlevel', this.levelId);
        httpApis.requestEnterSngRoomLevel(ProtoBuf.mttPro.EnterSngRoomLevelReq.create({levelId:this.levelId})).then((msg:mttPro.EnterSngRoomLevelRes)=>{
            cc.vv.ConsoleLog.log('pat_check',msg);
            if (msg.code){
                cc.vv.ConsoleLog.log('enterSngRoomLevel error');
            }
        });
    }

    ExitSngRoomLevel(){
        if(this.roomId){
            this.ws.Send(ProtoBuf.mttPro.ExitSngRoomLevelReq.create({roomId:this.roomId}));
        }
    }

    onExitSngRoomLevelRes = (msg: mttPro.ExitSngRoomLevelRes) =>{
        cc.vv.ConsoleLog.log('onExitSngRoomLevelRes',msg);
        if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.SNG){
            // block click 0.5s
            this.node.blockExitBtn();
        }

        if (msg.roomId == this.roomId){
            if (!msg.code){
                this.reboundGameView();
            }else{
                this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.code), ()=>{
                    if (msg.code == ProtoBuf.mttPro.Code.Sng_Room_Exit_Fail || msg.code == ProtoBuf.mttPro.Code.Sng_User_Match_Success){ // game started
                        
                    }
                    else{
                        this.reboundGameView();
                    }
                });
            }
        }
    }

    onUserForbidMsg = (msg: mttPro.User_ForbidMsg) =>{
        cc.vv.ConsoleLog.log('onUserForbidMsg',msg);
        if (msg.roomId == this.roomId){
            this.callPopUpBox(Translate(Translation.HOLDEM.JSNG.FORBID_RETURN), ()=>{
                this.reboundGameView();
            });
        }
    }

    SNGEnterRoom(data:any)
    {
        this.ws.Send(ProtoBuf.holdem.EnterRoomReq.create(data));
        // this.ws.Send(ProtoBuf.holdem.EnterRoomReq.create(data), -1, [ProtoBuf.holdem.MessageId.RoomSnapshotMsg, ProtoBuf.mttPro.MessageId.SngRoomSnapShotMsg], ()=>{
        //     cc.vv.ConsoleLog.log("Test SNG timeout");
        //     if( !this.isGameEnd )
        //     {
        //         this.ws.close(true);
        //         this.ws.disconnected();
        //     }
        // }, ()=>{
        //     cc.vv.ConsoleLog.log("Test SNG success");
        //     if( this.node )
        //     {
        //         this.node.hidLoading();
        //     }
        // });
    }

    // _retryEnterRoom:number = 0;
    EnterRoom() {
        // if( this.node )
        // {
        //     this.node.initShowLoading();
        // }
        cc.vv.ConsoleLog.log("EnterRoom",WorldWebSocket.checkNetwork('holdem reconnect'), this.isGameEnd);
        if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT){
            if (!this.isGameEnd){
                //this.tournamentId
                cc.vv.ConsoleLog.log('enter MTT, tournamentId:', this.tournamentId,);

                // this.ws.Send(ProtoBuf.mttPro.MttHoldemStadiumReq.create({mttId: this.tournamentId}));
                const {mttReviewTableId,mttReviewPlayerId} = cc.vv.DataManager;

                if (mttReviewTableId || mttReviewPlayerId) {
                    if (mttReviewTableId) {
                        cc.vv.ConsoleLog.log('enter mtt roomId:', mttReviewTableId);
                        this.MTTEnterRoom({
                            mttId: this.tournamentId,
                            roomId: mttReviewTableId
                        });
                    }
                    if (mttReviewPlayerId) {
                        cc.vv.ConsoleLog.log('enter mtt roomId:', mttReviewPlayerId);
                        this.MTTEnterRoom({
                            mttId: this.tournamentId,
                            userId: mttReviewPlayerId
                        });
                    }
                }else{
                    if (this.roomId){
                        this.MTTEnterRoom({mttId: this.tournamentId,roomId:this.roomId});
                    }else{
                        this.MTTEnterRoom({mttId: this.tournamentId});
                    }
                }
            } else {
                cc.vv.ConsoleLog.log('GameEnd, not need to enterRoom');
            }
        }else{
            this.SNGEnterRoom({roomId: this.tournamentId});
        }

    }

    updateSngRewardInfo () {
        this.ws.Send(ProtoBuf.mttPro.SngRewardInfoReq.create({roomId: this.roomId}));
    }

    @action
    onSngRewardInfoRes = (msg:mttPro.SngRewardInfoRes) => {
        if (msg.roomId == this.roomId) {
            this.rewardList = msg.rewardTable;
            this.prizeList = {};
            for(let i=0; i<this.rewardList.length; i++){
                this.prizeList[i+1] = {
                    money: FormatParser.DisplayGold(this.rewardList[i]),
                    // proportion: this.sngProportionTable[i]
                };
            }
        }
    };

    updateMttTableDetail = (data:commonProto.IMttTableDetail[]) => {
        this.mttTablesDetail = data;
        if(this.node){
            this.node.TournamentTableList();
        }
    };

    get isBaseToolPrize() {
        let isBaseToolPrize = this.MTTDetail && this.MTTDetail.BaseToolPrize;
        cc.vv.ConsoleLog.log("Result has tool anyway:", isBaseToolPrize);
        return isBaseToolPrize;
    }

    get isFixPrize() {
        let isFixPrize = this.MTTDetail && this.MTTDetail.PrizeMode == globalConfig.MTT_PRIZE_MODE.MTT_PRIZE_CONFIG_MODE_FIX;
        cc.vv.ConsoleLog.log("Result is fix prize:", isFixPrize);
        return isFixPrize;
    }

    get isTicketBase() {
        let isTicketBase = this.MTTDetail && this.MTTDetail.TicketBase;
        cc.vv.ConsoleLog.log("Result is tool prize first:", isTicketBase);
        return isTicketBase;
    }

    getDisplayGoldWithCurrency(val:number){
        return FormatParser.GetCurrencySign(this.displayCurrency) + FormatParser.DisplayGold(val, 2);
    }

    getDisplayReward (value:number, toolValue:number = 0, toolName:string = "", displayCurrency:string = globalConfig.CURRENCY.GOLD, toolCurrency:string = globalConfig.CURRENCY.GOLD):string {
        let fxValue = displayCurrency == this.displayCurrency ? value : FormatParser.ExchangeCurrency(value, this.exchangeRate, displayCurrency);
        if (toolName) {
            let fxToolValue = toolValue;
            if (displayCurrency != toolCurrency) {
                let validRate = this.getCurrencyRate(toolCurrency)==1?this.getCurrencyRate(displayCurrency):this.getCurrencyRate(toolCurrency);
                fxToolValue = FormatParser.ExchangeCurrency(toolValue, validRate, displayCurrency);
            }

            switch (this.MTTDetail.PrizeMode) {
                case globalConfig.MTT_PRIZE_MODE.MTT_PRIZE_CONFIG_MODE_FIX:
                    if (fxValue > 0) {
                        return FormatParser.GetCurrencySign(displayCurrency) + FormatParser.DisplayGold(fxValue) + '\n' + toolName;
                    } else {
                        return toolName;
                    }
                case globalConfig.MTT_PRIZE_MODE.MTT_PRIZE_CONFIG_MODE_TOOL:
                    return toolName;
                default:
                    const fxRegFee = displayCurrency == this.displayCurrency ? this.MTTDetail.RegFee : FormatParser.ExchangeCurrency(this.MTTDetail.RegFee, this.exchangeRate, displayCurrency);
                    let netValue = FormatParser.DisplayGold(fxValue - fxToolValue);
                    if (netValue > 0) {
                        return FormatParser.GetCurrencySign(displayCurrency) + FormatParser.DisplayGold(netValue) + '\n' + toolName;
                    } else if (netValue == 0) {
                        return toolName;
                    } else {
                        if (this.isBaseToolPrize || (this.isTicketBase && Math.abs(netValue) < fxRegFee)) {
                            return toolName;
                        }
                        else {
                            return FormatParser.GetCurrencySign(displayCurrency) + FormatParser.DisplayGold(fxValue);
                        }
                    }
            }
        } else {
            return  FormatParser.GetCurrencySign(displayCurrency) + FormatParser.DisplayGold(fxValue);
        }
    }

    @action
    updateMTTDetailInfo=()=>{
        if (this.MTTDetail) {
            this.rebuyParam = JSON.parse(this.MTTDetail.RebuyParam);
            this.morebuyParam = JSON.parse(this.MTTDetail.MorebuyParam);

            this.srvFee = this.MTTDetail.SrvFee;
            this.regFee = this.MTTDetail.RegFee;
            this.srvFeeMorebuy = this.MTTDetail.SrvFee;
            this.regFeeMorebuy = this.MTTDetail.RegFee;

            if(this.rebuyParam.defineParam){
                this.srvFee = this.rebuyParam.srvFee;
                this.regFee = this.rebuyParam.regFee;
                this.initCoinRebuy = this.rebuyParam.initCoin;
            }

            if(this.morebuyParam.defineParam){
                this.srvFeeMorebuy = this.morebuyParam.srvFee;
                this.regFeeMorebuy = this.morebuyParam.regFee;
                this.initCoinMorebuy = this.morebuyParam.initCoin;
                this.quantityMorebuy = this.morebuyParam.quantity;
            }

            this.blindLevelTime = this.MTTDetail.LevelTime;
            // this.roomName = msg.roomName;

            this.prizeMode = this.MTTDetail.PrizeMode;
            let moneyList = this.MTTDetail.PrizeMoney.sort((a, b) => {return a.Rank - b.Rank});
            let toolList = this.MTTDetail.PrizeTools;
            if (this.MTTDetail.DisplayTag=="WPT" && !this.isWPT){
                this.isWPT = true;
                if (!this.node.isWPT){
                    this.node.WPTUpdateBackground();
                }
            }
            this.CelebrityList = this.MTTDetail.CelebrityList;
            this.isCelebrityTournament = this.MTTDetail.IsCelebrity;
            this.node.checkPlayerCelebrityList();
            this.node.initLiveHandler();

            this.tournamentRoomName = cc.vv.DataManager.i18DataFromServer(this.MTTDetail.TournamentName, this.MTTDetail.TournamentNameI18N);
            this.node.gameRecord.setGameTitle(this.tournamentRoomName);
            this.node.infoPanel.children[3].getComponent(cc.Label).string = this.tournamentRoomName;

            if (this.MTTDetail.MultiFlightId && this.MTTDetail.MultiFlightLevel==0){
                this.isMultiFlightDay1 = true;
            }

            this.gameInvitationCode = this.MTTDetail.GameInvitationCode;

            this.node.tournamentRecord.updateTournamentInfoDesc(cc.vv.DataManager.i18DataFromServer(this.MTTDetail.Desc, this.MTTDetail.DescI18N));

            this.prizeList = {};
            for (const prizeMoney of moneyList) {
                let prizeRank = prizeMoney.Rank;
                let money = prizeMoney.Money ? prizeMoney.Money : this.MTTDetail.GamePot * prizeMoney.Proportion / 100;
                const item:any = {
                    money: money,
                    proportion: prizeMoney.Proportion
                };

                let prizeTool = toolList.find( (t) => { return t.Rank === prizeRank; } );
                if (prizeTool) {
                    let toolValue = prizeTool.Value;
                    if (prizeTool.Currency != this.displayCurrency) {
                        let toolFxRate = prizeTool.Currency == globalConfig.CURRENCY.GOLD ? this.exchangeRate : this.getCurrencyRate(prizeTool.Currency);
                        toolValue = FormatParser.ExchangeCurrency(toolValue, toolFxRate, this.displayCurrency);
                    }
                    switch (this.prizeMode) {
                        case globalConfig.MTT_PRIZE_MODE.MTT_PRIZE_CONFIG_MODE_FIX:
                            item.tool = prizeTool;
                            break;
                        case globalConfig.MTT_PRIZE_MODE.MTT_PRIZE_CONFIG_MODE_TOOL:
                            item.money = 0;
                            item.tool = prizeTool;
                            break;
                        default:
                            if (toolValue > money) {
                                if (this.MTTDetail.BaseToolPrize || (this.MTTDetail.TicketBase && toolValue - money < this.regFee)) {
                                    item.money = 0;
                                    item.tool = prizeTool;
                                }
                            }
                            else {
                                item.tool = prizeTool;
                                item.money = money - toolValue;
                            }
                            break;
                    }
                }
                this.prizeList[prizeRank] = item;
            }

            this.prizePool = this.MTTDetail.GamePot;
            this.bountyPool = this.MTTDetail.BountyPot;
            this.prizeCircle = this.MTTDetail.InTheMoney;
            this.rbcList = this.MTTDetail.HoldemBlindsConfig;

            if (this.smallBlind==0 && this.currentBlindLevel!==0){
                this.smallBlind = this.rbcList[this.currentBlindLevel].SmallBlind;
                this.bigBlind = this.rbcList[this.currentBlindLevel].BigBlind;
                this.ante = this.rbcList[this.currentBlindLevel].Ante;
            }
            if (this.MTTDetail.BlindHands>0){
                this.blindHands = this.MTTDetail.BlindHands;
                if(this.node){
                    if (this.riseBlindHand && this.currentBlindLevel!==0){
                        this.node.updateBlindHand((this.checkIsLastBlindLevel())?0:this.riseBlindHand);
                    }
                }
            }

            this.node.tournamentRecord.setResurrectLimit(this.MTTDetail.ReentryLimit);
            this.node.tournamentRecord.updateStacksInfo();

            // if (this.MTTDetail.CurrentLevel === this.currentBlindLevel)
            //     this.node.tournamentRecord.updateBlindTimeCountdown(Math.max(this.MTTDetail.LeftRiseBlindTime, 0));
            this.node.tournamentRecord.updateLateRegCountdown(Math.max(this.MTTDetail.TimeLeftSec, 0));

            // cc.vv.ConsoleLog.log("Blind List:", this.rbcList);
            cc.vv.ConsoleLog.log("Prize List:", this.prizeList);
        }
    };

    updateMttDetail=()=>{
        if( !this.isReplay )
        {
            httpApis.requestMttTournamentDetail(this.tournamentId,(data:commonProto.MttTournamentDetailResponse)=>{
                if(Object.keys(data).length && !data.ErrorCode){
                    cc.vv.ConsoleLog.log('requestMttTournamentDetail : ', data);
                    if (!this.MTTDetail) {
                        this.node.tournamentRecord.setTournamentType(data.TournamentDetail.TournamentMode);
                    }
                    this.MTTDetail = data.TournamentDetail;
                    this.updateMttTableDetail(data.TournamentDetail.TablesDetail);
                    this.updateMTTDetailInfo();
                    this.node.GameMenu();
                    this.node.setGameIcon();
                    this.node.onUpdateMttDetailLive();
                    if(this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.Mystery){
                        this.node.initMysteryBounty();
                    }
                }
                else{
                    cc.vv.ConsoleLog.log('mtt blind list error ', Object.keys(data).length,Object.keys(data).length?data.ErrorCode:'no data');
                }
            }, undefined);
        }
        
    };

    @action
    onMTTHoldemStadiumRes = (msg:mttPro.MttHoldemStadiumRes) =>{
        cc.vv.ConsoleLog.log('onMTTHoldemStadiumRes', JSON.stringify(msg));
        if (this.tournamentId == msg.mttId){
            this.rbcList = msg.rbc;
        }
    };

    @action
    onMttEnterGameRes = (msg: mttPro.MttEnterGameRes) => {
        cc.vv.ConsoleLog.log('onMttEnterGameRes', JSON.stringify(msg));
        if (msg.mttId==this.tournamentId){
            cc.vv.DataManager.mttReviewTableId = 0;
            cc.vv.DataManager.mttReviewPlayerId = 0;
            if (msg.code){
                if( this.ws )
                {
                    this.ws.clearMessagePairList();
                }
                switch(msg.code)
                {
                    //60087 early admission
                    case ProtoBuf.mttPro.Code.MTT_Prepare:
                        this.isPreparing = true;
                        this.node.PreparingMessage(this.isPreparing);
                        if( this._retryEnterRoomTimeout )
                        {
                            clearTimeout(this._retryEnterRoomTimeout);
                        }
                        cc.vv.ConsoleLog.log("60087 retryEnterRoomTime", this._retryEnterRoomTime);
                        this.updateMttDetail();
                        this.showBlindTimeCounter(false);
                        this._retryEnterRoomTimeout = setTimeout(()=>{
                            this.EnterRoom();
                        },  msg.leftPrepareTime * 1000);
                        break;
                    case ProtoBuf.mttPro.Code.Mtt_Room_End:
                    case ProtoBuf.mttPro.Code.Mtt_End:
                        if (this.roomId!=0){
                            this.mttEndRoomId = this.roomId;
                        }
                        this.loseState[3] = true;
                        this.loseCondition(3);
                        break;
                    case ProtoBuf.mttPro.Code.NO_MTT_ROOM:
                        this.isGameEnd = true;
                        this.closeAllPopUp();
                        this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.code),()=>{
                            this.reboundGameView(true);
                        });
                        break;
                    case ProtoBuf.mttPro.Code.NOT_ALLOW_ENTER:
                        if( this._retryEnterRoomTime < 3 )
                        {
                            this._retryEnterRoomTime++;
                            let random = (Math.random() * 3) + 1;
                            if( this._retryEnterRoomTimeout )
                            {
                                clearTimeout(this._retryEnterRoomTimeout);
                            }
                            cc.vv.ConsoleLog.log("retryEnterRoomTime", this._retryEnterRoomTime, this._retryEnterRoomTime * random * 1000);
                            this._retryEnterRoomTimeout = setTimeout(()=>{
                                this.EnterRoom();
                            }, this._retryEnterRoomTime * random * 1000);
                        }
                        else
                        {
                            let hint = Translate("ERROR_CODE_PKW."+msg.code);
                            this.callPopUpBox(hint,()=>{
                                this.reboundGameView();
                            });
                        }
                        break;
                    default:
                        let hint = Translate("ERROR_CODE_PKW."+msg.code) + " (MttEnterGameRes)";
                        this.callPopUpBox(hint,()=>{
                            this.reboundGameView();
                        });
                        break;
                }
                // if (msg.code==60081 || msg.code==60076){
                //     if (msg.code==60076){
                //         this.isGameEnd = true;
                //         this.closeAllPopUp();
                //         this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.code),()=>{
                //             this.reboundGameView(true);
                //         });
                //     }else{
                //         if (this.roomId!=0){
                //             this.mttEndRoomId = this.roomId;
                //         }
                //         this.loseState[3] = true;
                //         this.loseCondition(3);
                //     }

                // } 
                // else if( msg.code == ProtoBuf.mttPro.Code.NOT_ALLOW_ENTER )
                // {
                //     if( this._retryEnterRoomTime < 3 )
                //     {
                //         this._retryEnterRoomTime++;
                //         let random = (Math.random() * 3) + 1;
                //         if( this._retryEnterRoomTimeout )
                //         {
                //             clearTimeout(this._retryEnterRoomTimeout);
                //         }
                //         cc.vv.ConsoleLog.log("retryEnterRoomTime", this._retryEnterRoomTime, this._retryEnterRoomTime * random * 1000);
                //         this._retryEnterRoomTimeout = setTimeout(()=>{
                //             this.EnterRoom();
                //         }, this._retryEnterRoomTime * random * 1000);
                //     }
                //     else
                //     {
                //         let hint = Translate("ERROR_CODE_PKW."+msg.code);
                //         this.callPopUpBox(hint,()=>{
                //             this.reboundGameView();
                //         });
                //     }
                    
                // }
                // else{
                //     let hint = "MttEnterGameRes "+msg.code;
                //     this.callPopUpBox(hint,()=>{
                //         this.reboundGameView();
                //     });
                // }
            } else{
                if (this.isPreparing){
                    this.isPreparing = false;
                    this.node.PreparingMessage(false);
                }
                this.showBlindTimeCounter(true);
                this._retryEnterRoomTime = 0;
                if( this._retryEnterRoomTimeout )
                {
                    clearTimeout(this._retryEnterRoomTimeout);
                }

                // let mttDetail = cc.director.getScene().getComponentInChildren(MttHall)._mttHallInfo;
                // if (!this.MTTDetail){
                //     let mttDetail = this.getMttHallInfo();
                //     if (mttDetail) {
                //         cc.vv.ConsoleLog.log("get mtt detail from hall", mttDetail);
                //         this.MTTDetail = mttDetail;
                //         // this.mttTablesDetail = mttDetail.TablesDetail;
                //         this.updateMttTableDetail(mttDetail.TablesDetail);
                //         this.updateMTTDetailInfo();
                //         this.node.tournamentRecord.setTournamentType(this.MTTDetail.TournamentMode);
                //     }else{
                //         this.updateMttDetail();
                //     }
                // } else{
                //     this.updateMttDetail();
                // }
                this.updateMttDetail();
            }


        }

    };

    /******Test Code for side pot******/
    // @action
    // protected  tmpSidepot = (msg:number[])=>{
    //     this.sidePot = msg;
    //
    // };
    /*******By Gary 01-03-2019******/

    @action
    public onEnterRoomRes = (msg: holdem.EnterRoomRes) => {
        cc.vv.ConsoleLog.log("onEnterRoomRes check", msg.code, this.tournamentId, msg.mttId, msg.roomId);
        // cc.vv.ConsoleLog.log('onEnterRoomRes before', msg, this.tournamentMode, this.tournamentId, this.tournamentId == msg.mttId, msg.code === 0);
        if (msg.code === 0) {
            if( this.tournamentId == msg.mttId ) {
                if (this.isSNGMatching) {
                    this.isSNGMatching = false;
                }

                cc.vv.DataManager.currentRoomID = msg.roomId;
                cc.vv.ConsoleLog.log('onEnterRoomRes', msg);
                this.tournamentId = msg.mttId;

                if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && (this.roomId!=msg.roomId)){
                    this.node._dealerFirstDelay = true;
                    // mtt change table, reset guess hand loop setting
                    this.node.RemoveGuessHandLoop();
                }
                
                this.roomId = msg.roomId;
                this.smallBlind = msg.sb;
                this.bigBlind = msg.bb;
                /******Test Code for side pot******/
                //
                // setTimeout(()=>{this.tmpSidepot([130,560])},2000);
                // setTimeout(()=>{this.tmpSidepot([130,560,10240])},4000);
                // setTimeout(()=>{this.tmpSidepot([130,560,10240,21547])},6000);
                // setTimeout(()=>{this.tmpSidepot([130,560,10240,21547,3658])},8000);
                // setTimeout(()=>{this.tmpSidepot([130,560,10240,21547,2548,2587])},10000);
                // setTimeout(()=>{this.tmpSidepot([130,560,10240,2514,36584,15232,122])},12000);
                // setTimeout(()=>{this.tmpSidepot([130,560,10240,325,325,325,254,214])},14000);
                // setTimeout(()=>{this.tmpSidepot([130,560,10240,6987,3254,1225,32574,6458,24185])},16000);
                // setTimeout(()=>{this.tmpSidepot([])},19000);
                //
                // this.mainPot = 2050;
                /*******By Gary 01-03-2019******/
                this.ante = msg.ante;
                this.minTakein = msg.minTakein;
                this.maxTakein = msg.maxTakein;
                this.calcSmallBlind = msg.sb;
                this.calcBigBlind = msg.bb;
                if (this.node && this.node.coinMode==1){
                    this.node.updateCoinModeValue();
                }
                this.calcAnte = msg.ante;
                this.totalSeatCount = msg.seatCount;
                if(msg.flags) {
                    if(msg.flags & ProtoBuf.holdem.RoomFlags.ROOM_FLAG_CHECK_GPS) {
                        if(cc.sys.isNative) {
                            cc.vv.DataManager.StatusBar?.toggleGPSRes(true);
                        }
                        this.isGps = true;
                    }
                    if(msg.flags & ProtoBuf.holdem.RoomFlags.ROOM_FLAG_CHECK_IP) {
                        if(cc.sys.isNative) {
                            cc.vv.DataManager.StatusBar?.toggleIPRes(true);
                        }
                    }
                }

                cc.vv.ConsoleLog.log('onEnterRoomRes', this.bigBlind, this.ante, this.isGps);
                this.waitForChangeTable = false;
            }
        } else {
            if( this.tournamentId == msg.roomId || this.tournamentId == msg.mttId )
            {
                if (msg.code==50072 || msg.code==60076){
                    this.resetTable();
                    this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.code),()=>{
                        this.reboundGameView(true);
                    });
                } else if (msg.code==50073){
                    this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.code),()=>{
                        WorldWebSocket.logout();
                    });
                } else if(msg.code == 60086){
                    if (this.tournamentId==msg.roomId){
                        this.waitForChangeTable = true;
                        if (cc.isValid(this.node.waitForTableMessageLayer)) {
                            this.node.waitForTableMessageLayer.opacity = 255;
                        }
                        cc.vv.ConsoleLog.log('onEnterRoomRes Enter_Room_Need_Wait');
                    }
                } else{
                    this.callPopUpBox("EnterRoomRes "+Translate("ERROR_CODE_PKW."+msg.code),()=>{
                        this.reboundGameView();
                    });
                }
            }
        }
    };

    SitDown(seatNum: number, coin: number, toolID:number) {
        this.takeInCoin = coin;
        let inputData = {
            roomId: this.roomId,
            seatNum: seatNum,
            takeInCoin: this.takeInCoin,
            itemId:toolID
        }
        if(this.isGps) {
            let cb = (location: any) => {
                if(location) {
                    cc.vv.ConsoleLog.log("lat: " + location.lat);
                    cc.vv.ConsoleLog.log("lng: " + location.lng);
                    // @ts-ignore
                    inputData.lat = location.lat;
                    // @ts-ignore
                    inputData.lng = location.lng;
                }
                this.ws.Send(ProtoBuf.holdem.SitDownReq.create(inputData));
            }
            cc.vv.DataManager.GPSController.GPS(cb);
        } else {
            if (toolID === 0 && cc.vv.DataManager.userData.Gold < coin) {
                this.handleNoMoney();
            }else{
                this.ws.Send(ProtoBuf.holdem.SitDownReq.create(inputData));
            }

        }
    }

    @action
    protected onSitDownRes = (msg: holdem.SitDownRes) => {
        if(this.roomId == msg.roomId) {
            this.setCardAnimation(true);
            cc.vv.ConsoleLog.log('onSitDownRes', JSON.stringify(msg));
            if(msg.code == ProtoBuf.holdem.Code.NEAR_GPS || msg.code == ProtoBuf.holdem.Code.SAME_IP) {
                this.showDialogBox(Translate(Translation.POPUP_TITLE.REMIND), Translate(Translation.GAME.PLAYER_SAME_IP), false,
                    [
                        {
                            type: 0,
                            text: Translate(Translation.GAME.CONFIRM),
                            callback: undefined
                        }
                    ]
                );
            }else if (msg.code>0){
                if (msg.code==ProtoBuf.holdem.Code.OTHER_ROOM){
                    cc.vv.ConsoleLog.log("SitDownRes "+msg.code);
                } else if (msg.code==50051) {
                    this.callPopUpBox(Translate(Translation.ERROR_CODE_PKW[msg.code]), ()=>{});
                }else {
                    this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.code),()=>{});
                }
            } else if (msg.code == 0) {
                if( cc.isValid(this.node) )
                {
                    this.node.audioPlayer.playEffect(soundEffect.PlayerSitDown);
                }
            } else if (msg.code==-1){
                this.callPopUpBox(Translate(Translation.HOLDEM.ERROR_SEAT_SEATED), ()=>{});
            }
        }
    };

    @action
    public onSeatOccupiedMsg = (msg: holdem.SeatOccupiedMsg, needAnimation:boolean = true) => {
        if(msg.roomId == this.roomId) {
            this.needAnimation = needAnimation;
            cc.vv.ConsoleLog.log('onSeatOccupiedMsg', JSON.stringify(msg));
            const data = {
                userId: msg.userId,
                leftCoin: msg.coin,
                deskCoin: 0,
                nickName: msg.nickName,
                seatNum: msg.seatNum,
                gender: msg.gender,
                state: ProtoBuf.holdem.PlayerState.NONE_STATE,
                avatar: msg.avatar,
                areaCode: msg.areaCode,
                area: msg.area,
            };
            cc.vv.ConsoleLog.log("onSeatOccupiedMsg this.playerUserId", this.playerUserId);

            if( this.seatedPlayers[msg.seatNum])
            {
                this.seatedPlayers[msg.seatNum].needAnimation = needAnimation;
            }

            // self
            if(msg.userId == this.playerUserId) {
                if (this.waitForChangeTable){
                    this.waitForChangeTable = false;
                }
                if( this.seatedPlayers[msg.seatNum] && this.seatedPlayers[msg.seatNum] instanceof holdemSelfStore )
                {
                    let tempHoldemSelfStore:holdemSelfStore = this.seatedPlayers[msg.seatNum] as holdemSelfStore;
                    tempHoldemSelfStore.UpdateSelfPlayer(this, data, null);

                    if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && tempHoldemSelfStore.mttRank==0 && this.MTTRank!==0){
                        tempHoldemSelfStore.mttRank = this.MTTRank;
                    }
                }
                else
                {
                    this.seatedPlayers[msg.seatNum] = this.self = new holdemSelfStore(this, data, null);
                    // set MTTRank for change table
                    if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && this.seatedPlayers[msg.seatNum].mttRank==0 && this.MTTRank!==0){
                        this.seatedPlayers[msg.seatNum].mttRank = this.MTTRank;
                    }
                }
            }else{ // other player
                if(this.seatedPlayers[msg.seatNum] && this.seatedPlayers[msg.seatNum] instanceof holdemPlayerStore)
                {
                    this.seatedPlayers[msg.seatNum].UpdatePlayer(this, data);
                    this.seatedPlayers[msg.seatNum].userRank = 0;
                    if( needAnimation )
                    {
                        this.seatedPlayers[msg.seatNum].SitDownAnimation();
                    }
                }
                else
                {

                    this.seatedPlayers[msg.seatNum] = new holdemPlayerStore(this, data);
                    if( needAnimation )
                    {
                        this.seatedPlayers[msg.seatNum].SitDownAnimation();
                    }
                }
            }
            if( this.seatedPlayers[msg.seatNum])
            {
                this.seatedPlayers[msg.seatNum].calcBB = this.calcBigBlind;
                this.seatedPlayers[msg.seatNum].needAnimation = needAnimation;
            }

            if( Object.keys(this.seatedPlayers)?.length >= 3 && this.node )
            {
                this.node.stopMatchGameAnim();
            }
        }
    };

    @action
    public onSeatEmptyMsg = (msg: holdem.SeatEmptyMsg, needAnimation:boolean = true) => {
        if(msg.roomId == this.roomId) {
            this.needAnimation = needAnimation;
            cc.vv.ConsoleLog.log('onSeatEmptyMsg',JSON.stringify(msg));
            if( this.seatedPlayers[msg.seatNum]) this.seatedPlayers[msg.seatNum].Reset();
            delete this.seatedPlayers[msg.seatNum];
            if(this.self && msg.seatNum === this.self.seatNum) {
                this.self = null;
            }
            if( this.seatedPlayers[msg.seatNum])
            {
                this.seatedPlayers[msg.seatNum].needAnimation = needAnimation;
            }
        }
    };

    @action
    public onNeedActionMsg = (msg: holdem.NeedActionMsg) => {
        if(this.roomId == msg.roomId) {
            this.setCardAnimation(true);
            cc.vv.ConsoleLog.log('onNeedActionMsg', JSON.stringify(msg));

            if(msg.minBetCoin) {
                // this.countdownLastSeat = this.countdownSeat;
                // this.countdownSeat = msg.seatNum;
                // this.countdownTtl = msg.countDown;
                // this.countdownLeft = msg.countDown;
               
                this.seatedPlayers[msg.seatNum].OnNeedAction(msg, msg.countDown, msg.countDown, msg.maxBetCoin);
            }
        }
    };

    Action(action: holdem.Action, coin: number) {
        cc.vv.ConsoleLog.log('ActionReq', ' action:', action, ' coin: ', coin, ' user: ', this.self.nickName, this.self.userId, this.self.seatNum, this.self.showControl);
        this.self.HideControl();

        this.ws.Send(ProtoBuf.holdem.ActionReq.create({roomId: this.roomId, action, coin}))
    }

    @action
    protected onActionRes = (msg: holdem.ActionRes) => {
        if(this.roomId == msg.roomId) {
            this.setCardAnimation(true);
            cc.vv.ConsoleLog.log('onActionRes', JSON.stringify(msg));
            if(msg.code !==0) {
                cc.vv.ConsoleLog.log('error code: ',msg.code, " object-> ",JSON.stringify(msg));
                //todo error
                // let hints = Translate(Translation.MESSAGE_DIALOG_BLOCKER.NETWORK_ERROR);
                if (msg.code==50007 || msg.code==50004){
                    if (!this._autoPlayAlert){
                        cc.vv.ConsoleLog.log('show time out pop up');
                        this.callPopUpBox(Translate(Translation.POPUP_HINTS.TIME_OUT),()=>{

                        });
                    }
                } else{
                    this.callPopUpBox(Translate(Translation.ERROR_CODE_PKW['50003'])+msg.code,()=>{
                    });
                }

            }
        }
    };

    callPopUpBox(hints:string,callback:()=>any,okLabel = Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK), title:string=""){
        this.showDialogBox(title,hints,false,[
            {   type: 0,
                text: okLabel,
                callback:callback,
            },
        ])
    }

    @action
    public onPlayerActionMsg = (msg: holdem.PlayerActionMsg) => {
        if(msg.roomId == this.roomId) {
            this.setCardAnimation(true);
            cc.vv.ConsoleLog.log('onPlayerActionMsg', JSON.stringify(msg));
            this.tmpMaxDeskCoin = Math.max(this.tmpMaxDeskCoin, msg.deskCoin);
            this.tempPot += msg.deskCoin - this.seatedPlayers[msg.seatNum].deskCoin;
            cc.vv.ConsoleLog.log('PotChange tempPot', this.tempPot, msg.deskCoin, this.seatedPlayers[msg.seatNum].deskCoin);
            this.seatedPlayers[msg.seatNum].countdownTtl = 0;

            this.seatedPlayers[msg.seatNum].OnAction(msg);
            switch (msg.action) {
                case ProtoBuf.holdem.Action.FOLD:
                    if( cc.isValid(this.node) )
                    {
                        this.node.audioPlayer.playEffect(soundEffect.Fold);
                    }
                    break;
                // #13568 move sound effect to Holdem_Player_ts CoinAction stakeFrom animation
                // case ProtoBuf.holdem.Action.CALL:
                //     if( cc.isValid(this.node) )
                //     {
                //         this.node.audioPlayer.playEffect(soundEffect.ChipsToTable);
                //     }
                //     break;
                // case ProtoBuf.holdem.Action.RAISE:
                //     if( cc.isValid(this.node) )
                //     {
                //         this.node.audioPlayer.playEffect(soundEffect.ChipsToTable);
                //     }
                //     break;
            }
            if (this.seatedPlayers[msg.seatNum].userId == this.playerUserId) {
                switch (msg.action) {
                    case ProtoBuf.holdem.Action.FOLD:
                        // if (!this.isReplay){
                        //     this.node.PlayerGuessCardNextHandPanel(true);
                        // }
                        break;
                    case ProtoBuf.holdem.Action.CHECK:
                        if( cc.isValid(this.node) )
                        {
                            this.node.audioPlayer.playEffect(soundEffect.Check);
                        }
                        break;
                    // #13568 move sound effect to Holdem_Player_ts CoinAction stakeFrom animation
                    // case ProtoBuf.holdem.Action.BET:
                    //     if( cc.isValid(this.node) )
                    //     {
                    //         this.node.audioPlayer.playEffect(soundEffect.ChipsToTable);
                    //     }
                    //     break;
                    // case ProtoBuf.holdem.Action.ALL_IN:
                    //     if( cc.isValid(this.node) )
                    //     {
                    //         this.node.audioPlayer.playEffect(soundEffect.ChipsToTable);
                    //     }
                    //     break;
                }
            }
        }
    };

    @action
    public onPlayerStateMsg = (msg: holdem.PlayerStateMsg) => {
        if (msg.roomId == this.roomId){
            this.setCardAnimation(true);
            // this.state = msg.state;
            cc.vv.ConsoleLog.log('onPlayerStateMsg', JSON.stringify(msg));
            
            if (this.self && msg.seatNum==this.self.seatNum){
                // check if state = 20 to show wait for start logo
                this.waitForStart = msg.state == ProtoBuf.holdem.PlayerState.WAITING;
            }
            // all in
            if (msg.state==ProtoBuf.holdem.Action.ALL_IN || msg.state==ProtoBuf.holdem.PlayerState.WAITING){
                this.seatedPlayers[msg.seatNum].state = msg.state;
            }
            this.seatedPlayers[msg.seatNum].actualState = msg.state;
            this.seatedPlayers[msg.seatNum].SetHasFold();

        }
    };

    HideHoleCard(){
        if (this.roomId){
            this.ws.Send(ProtoBuf.holdem.HideHoleCardReq.create({roomId:this.roomId, hide:localConfig.getLocalStorageItem(localConfig.key_enableAutoCheck, 0) == 1}));
        }
    }

    onHideHoleCardRes = (msg:holdem.HideHoleCardRes)=>{
        cc.vv.ConsoleLog.log('onHideHoleCardRes', JSON.stringify(msg));
        if (this.roomId == msg.roomId){
            this.setCardAnimation(true);
            if (msg.code){
                // nothing to do now ...
            }
        }
    }

    StopResetTable()
    {
        this.node.unschedule(this.resetTable);
        if( this.waitForResetTable )
        {
            this.resetTable();
        }
    }

    @action
    public onShowdownMsg = (msg: holdem.ShowdownMsg, needAnimation:boolean = true) => {
        if (this.roomId == msg.roomId) {
            this.StopResetTable();
            cc.vv.ConsoleLog.log('onShowDownMsg', JSON.stringify(msg));
            // this.boardCards = [];
            this.needAnimation = needAnimation;
            this.isShowDown = true;
            let winnerCard = new Uint8Array();
            let winner = (msg.winners && msg.winners.length>0);
            if (winner){
                msg.players.forEach(player=>{
                    if (player.seatNum == msg.winners[0]){
                        winnerCard = player.rankCards;
                    }
                })
            }

            msg.players.forEach(player => {
                // const winner = msg.winners.filter(winner=>winner.seatNum==player.seatNum);
                if (this.maxHoleCard==2){
                    let checkIsWin = msg.winners && msg.winners.length>0 && msg.winners.includes(player.seatNum);
                    cc.vv.ConsoleLog.log('patcheck onShowDownMsg', checkIsWin, player.seatNum, msg.winners, msg.winners[0], player)
                    this.seatedPlayers[player.seatNum].Showdown(player.holeCards, player.rank, (player.rankCards && player.rankCards.length && checkIsWin) ? player.rankCards : winner?winnerCard:null);
                }else{
                    this.seatedPlayers[player.seatNum].Showdown(player.holeCards, player.rank, (player.rankCards && player.rankCards.length) ? player.rankCards : null);
                }

                this.seatedPlayers[player.seatNum].needAnimation = needAnimation;
                if (player.rankCards && player.rankCards.length && msg.winners && msg.winners.length>0 && player.seatNum === msg.winners[0]) {
                    const highlight: boolean[] = [];
                    const cards = this.allBoardCards.slice();
                    cards.forEach(v => {
                        highlight.push(player.rankCards.indexOf(v) !== -1)
                    });
                    cc.vv.ConsoleLog.log('highlight board card', cards, player.rankCards, highlight);
                    this.boardCardsHighlight = highlight;
                }

                // if(winner.length && this.self && player.seatNum === this.self.seatNum && winner[0].rankCards) {
                // if(winner.length) {

                //     const highlight:boolean[] = [];
                //     const cards = this.allBoardCards.slice();
                //     cards.forEach(v => {
                //         highlight.push(winner[0].rankCards.indexOf(v) !== -1)
                //     });
                //     cc.vv.ConsoleLog.log('highlight board card', cards, winner[0].rankCards, highlight);
                //     this.boardCardsHighlight = highlight;
                // }
            });


            for (let i in this.seatedPlayers) {
                this.seatedPlayers[i].NewTurn();
            }
        }

    };

    sendShowCardReq (showCards:Uint8Array, show:boolean) {
        let inputData = {showCards: showCards, show: show, roomId: this.roomId, userId: this.playerUserId};
        cc.vv.ConsoleLog.log("Show Card Req", inputData);
        this.ws.Send(ProtoBuf.holdem.ShowCardReq.create(inputData));
    }

    onShowCardRes = (msg: holdem.ShowCardRes) => {
      if (msg.roomId == this.roomId && msg.userId === this.playerUserId) {
          if (!msg.code) {

          } else {
              cc.vv.ConsoleLog.log("Show Card Msg Error: " + msg.code, JSON.stringify(msg));
          }
      }
    };

    BuyTime(){
        if (this.roomId){
            this.ws.Send(ProtoBuf.mttPro.BuyTimeReq.create({roomId:this.roomId}));
        }
    }

    @action
    onBuyTime = (msg:mttPro.BuyTimeRes)=>{
        cc.vv.ConsoleLog.log('onBuyTime',JSON.stringify(msg));
        if (msg.roomId==this.roomId){
            if (msg.code){
                this.node.BuyTime();
                if (msg.code==60079){
                    let buyTimeArr = [0,2,5,10,10];
                    if (buyTimeArr[this.buyTimeCount] > cc.vv.DataManager.userData.Gold){
                        this.callPopUpBox(Translate(Translation.ICON_MESSAGE.NOT_ENOUGH_COIN),()=>{});
                    } else{
                        this.callPopUpBox(Translate(Translation.ERROR_CODE_PKW[msg.code]),()=>{});
                    }

                } else{
                    this.callPopUpBox("BuyTimeRes "+msg.code,()=>{});
                }
            }else{
                if (this.self && msg.userId==this.self.userId){
                    this.buyTimeCount++;
                }
                for(let i in this.seatedPlayers) {
                    if (this.seatedPlayers[i].userId == msg.userId){
                        this.seatedPlayers[i].OnBuyTime(msg.duration);
                    }
                }
            }
        }
    };

    @action
    public onRoundResultMsg = (msg: holdem.RoundResultMsg, needAnimation:boolean = true, timeOffset:number = 0) => {
        if(this.roomId == msg.roomId) {
            this.needAnimation = needAnimation;
            cc.vv.ConsoleLog.log('onRoundResultMsg', JSON.stringify(msg));
            this.tmpMaxDeskCoin = 0;
            msg.players.forEach(p=>{
                // let pf = Math.round(p.profit);
                // let lc = Math.round(p.leftCoins);
                if (this.seatedPlayers[p.seatNum]){
                    if (this.seatedPlayers[p.seatNum].userId == this.playerUserId && p.profit > 0) {
                        if( cc.isValid(this.node) )
                        {
                            this.node.audioPlayer.playEffect(soundEffect.GetProfit);
                        }
                    }
                    if (this.seatedPlayers[p.seatNum].userId == this.playerUserId && p.leftCoins){
                        if (this.node){
                            this.node.CheckGuessHandLoop();
                        }

                    }
                    this.seatedPlayers[p.seatNum].RoundResultProfit(p.profit, p.leftCoins, p.getPot);
                }
                


                if(this._rewardCount == 0 ){
                    // 沒有復活需求
                    // play 淘汰
                    if(p.leftCoins == 0) {
                        cc.vv.ConsoleLog.log("playerisout leftcoin:",p.leftCoins,"current leftcoin",this.seatedPlayers[p.seatNum]?.leftCoin);
                        this.seatedPlayers[p.seatNum]?.PlayerIsOut(true);
                        // if hunter mode play ko animation
                        if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && this.MTTDetail && (this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER || this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER)) {
                            this.animate = HOLDEM_ANIMATION.KO;
                        }
                    }
                }

            });
            if(this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && !this.MTTDetail){
                cc.vv.ConsoleLog.log('mttDetail empty, request mtt detail again in round result');
                this.updateMttDetail();
            }
            Object.keys(this.seatedPlayers).forEach(seat => {
                // dont reset waiting state in round result
                if(this.seatedPlayers[seat].state!==ProtoBuf.holdem.PlayerState.WAITING){
                    this.seatedPlayers[seat].state = ProtoBuf.holdem.PlayerState.NONE_STATE;
                }
                this.seatedPlayers[seat].needAnimation = needAnimation;
            });
            this.waitForResetTable = true;
            if( this.node && this.self )
            {
                this.node.CheckPlayerControlPanel();
            }

            // msg.losers.forEach(loser=>{
            //     this.seatedPlayers[loser.seatNum].Lose(loser.loseCoins, loser.leftCoins);
            // });
            //
            // for (const i of msg.winners){
            //     cc.vv.ConsoleLog.log('onRoundResultMsg', i, this.seatedPlayers[i.seatNum], this.seatedPlayers);
            //     this.seatedPlayers[i.seatNum].Win(i.winCoins, i.leftCoins);
            //
            // }
            // msg.winners.forEach(winner => {
            //     this.seatedPlayers[winner.seatNum].Win(winner.winCoins, winner.leftCoins);
            //     cc.vv.ConsoleLog.log('on99 ',this.seatedPlayers[winner.seatNum]);
            // });
            this.countdownLastSeat = this.countdownSeat;
            this.countdownSeat = 0;

            // if (this.calcBigBlind !== this.bigBlind){
            //     this.calcBigBlind = this.bigBlind;
            //     this.calcSmallBlind = this.smallBlind;
            //     this.calcAnte = this.ante;
            //     if (this.node && this.node.coinMode==1){
            //         this.node.updateCoinModeValue();
            //     }
            // }
            cc.vv.ConsoleLog.log("this.resetTable before", new Date().getTime());

            this.node.scheduleOnce(this.resetTable, (this.resetTableTime - timeOffset) / 1000);
            // this.newResetTableTimeout = setTimeout(this.resetTable, this.resetTableTime);
        }
    };

    @action
    resetRest = ()=>{
        this.restMessage = ProtoBuf.mttPro.RestTime_Type.RestTime_Type_Null;
    };

    @action
    public onDealerPosMsgNewGame = (msg: holdem.DealerPosMsg, needAnimation:boolean = true) => {

        if(msg.roomId == this.roomId) {
            this.isRebuyShown = false;
            this.isMorebuyShown = false;
            this.isMysteryBountyOnIntro = false;
            this.roomMessage = 0;
            this.resumeTimeRemain = 0;
            this.rebuyLeftTime = 0;
            this.rebuyEndTime = 0;
            this.morebuyLeftTime = 0;
            this.morebuyEndTime = 0;
            this.roomStatus = 0; // hotfix, need modify in the future

            if (cc.isValid(this.node))
            {
                this.node.roomMessageLayer.active = false;
            }
            this.needAnimation = needAnimation;
            // onSngStartNotifyMsg
            cc.vv.ConsoleLog.log('onDealerPosMsgNewGame', JSON.stringify(msg));
            
            this.roomSnapshotState = 4;//try to hide "roomInfoAlert"

            this.dealerPos = msg.dealerPos;
            this.straddlePos = msg.straddlePos;

            this.updateCurrentBlinds(msg.roomBlindIndex ? msg.roomBlindIndex : this.currentBlindLevel);

            // mtt hide rest message
            this.restTime = 0;
            this.restEndTime = 0;
            // will rest need show until rest
            if (this.restMessage!==ProtoBuf.mttPro.RestTime_Type.RestTime_Type_WILL_REST) {
                this.resetRest();
            }
            if (this.isPreparing){
                this.isPreparing = false; // remove preparing prompt
                this.node.PreparingMessage(false);
            }


            this.mainPot = msg.pot;
            this.tempPot = msg.pot;
            
            // hide chai chai my card next round card CCMCNR
            this.isShowDown = false;
            if(this.node && this.self){
                // reset data in new round
                this.node.playerControl.resetPreActionPanel();
                this.node.GuessedHandReset();
            }

            msg.seats.forEach(seat => {
                this.tmpMaxDeskCoin = Math.max(this.tmpMaxDeskCoin, seat.deskCoin);
                this.seatedPlayers[seat.seatNum].PlayerIsBack();
                this.seatedPlayers[seat.seatNum].NewRound(seat.deskCoin, seat.leftCoin, this.calcBigBlind);
                this.seatedPlayers[seat.seatNum].SetStakeIcon(msg.bbPos, msg.sbPos);
                this.seatedPlayers[seat.seatNum].needAnimation = needAnimation;
                this.tempPot += seat.deskCoin;
            });

            if (cc.isValid(this.node)) {
                this.node.showInMoneyNotifyAtNewRound();
                this.node.NewRound();
            }
        }

    };

    updateCurrentBlinds(blindIndex: number = -1): void {
        if (this.rbcList && this.rbcList.length > 0) {
            let ri = blindIndex > 0 ? blindIndex - 1 : 1;
            if (this.rbcList[ri]) {
                this.smallBlind = this.rbcList[ri].SmallBlind;
                this.bigBlind = this.rbcList[ri].BigBlind;
                this.ante = this.rbcList[ri].Ante;
                this.updateCalcBlind()
            }
        }
    }

    updateCalcBlind(){
        this.calcBigBlind = this.bigBlind;
        this.calcSmallBlind = this.smallBlind;
        this.calcAnte = this.ante;
        if (this.node && this.node.coinMode==1){
            this.node.updateCoinModeValue();
        }
    }

    RoomSnapshot() {
        if(this.roomId) {
            cc.vv.ConsoleLog.log("Snapshot roomid: ",this.roomId);
            this.ws.Send(ProtoBuf.holdem.RoomSnapshotReq.create({roomId: this.roomId}));
        }
    }

    @action
    public onRoomSnapshotMsg = (msg: holdem.RoomSnapshotMsg, outPlayerSeatNum:number[] = []) => {
        cc.vv.ConsoleLog.log("onRoomSnapshotMsg Room id msg:",msg.roomId, 'this Room id:',this.roomId);

        if ((this.tournamentMode!==globalConfig.GAME_LEVEL_LIST_ID.MTT || (this.tournamentMode==globalConfig.GAME_LEVEL_LIST_ID.MTT && this.tournamentId==msg.mttId)) && this.roomId==msg.roomId){
            this.roomId = msg.roomId;
            this.needAnimation = false;

            cc.vv.ConsoleLog.log('onRoomSnapshot', JSON.stringify(msg));

            // this.node.unschedule(this.resetTable);
            // this.resetTable();
            this.StopResetTable();
            let tempSeatedPlayers: {[index:number]:holdemPlayerStore} = {};
            for(let i in this.seatedPlayers)
            {
                tempSeatedPlayers[i] = this.seatedPlayers[i];
            }
            this.Reset();
            this.visitors = msg.visitors;
            let isSelf = false;
            this.roomSnapshotState = msg.state;
            // const {PlayerState} = ProtoBuf.holdem;

            let newSeatedPlayer: {[index:number]:holdemPlayerStore} = {};
            msg.players.forEach(player => {
                newSeatedPlayer[player.seatNum] = tempSeatedPlayers[player.seatNum];
            });
            this.seatedPlayers = newSeatedPlayer;

            // reset tmpMaxDeskCoin in snapshot
            this.tmpMaxDeskCoin = 0;

            this.waitForStart = false;

            msg.players.forEach(player => {
                // 自己
                if(player.userId == this.playerUserId) {
                    isSelf = true;
                    // this.seatedPlayers[player.seatNum] = this.self = new holdemSelfStore(this, player, msg.holeCards);
                    const {mttReviewTableId,mttReviewPlayerId} = cc.vv.DataManager;
                    if (mttReviewPlayerId || mttReviewTableId){
                        cc.vv.DataManager.mttReviewPlayerId = 0;
                        cc.vv.DataManager.mttReviewTableId = 0;
                    }

                    if( this.seatedPlayers[player.seatNum] )
                    {
                        if( this.seatedPlayers[player.seatNum] instanceof holdemSelfStore )
                        {
                            let tempSelfPlayer = this.seatedPlayers[player.seatNum] as holdemSelfStore;
                            tempSelfPlayer.UpdateSelfPlayer(this, player, msg.holeCards);
                            this.self = tempSelfPlayer;

                            // todo add to mtt snapshot
                            // this.seatedPlayers[player.seatNum].mttRank = msg.rank;
                        }
                        else
                        {
                            cc.vv.ConsoleLog.log("onRoomSnapshot holdemPlayerStore cannot cast to holdemSelfStore");
                            this.seatedPlayers[player.seatNum] = this.self = new holdemSelfStore(this, player, msg.holeCards);
                        }
                    }
                    else
                    {
                        this.seatedPlayers[player.seatNum] = this.self = new holdemSelfStore(this, player, msg.holeCards);
                    }

                    // this.node.PlayerGuessCardNextHandPanel((player.state==4));

                    this._snapshotSelfFold = (player.state==ProtoBuf.holdem.Action.FOLD);

                    if (player.flags&1) {
                        if (!this._autoPlayAlert){
                            this.AutoPlayMsg();
                        }
                    }

                    // check if state = 20 to show wait for start logo, head up for start
                    this.waitForStart = player.state == ProtoBuf.holdem.PlayerState.WAITING || player.state == ProtoBuf.holdem.PlayerState.PLAYER_STATE_HU_WAIT;

                    if (msg.state==30 || msg.state==31){
                        // set state = 106 to reset allin animation
                        if(!this.waitForStart){
                            player.state = CUSTOM_PLAYER_STATE.WAIT_FOR_START;
                        }
                    }
                } else { //其他人
                    // this.seatedPlayers[player.seatNum] = new holdemPlayerStore(this, player);
                    if (msg.state==30 || msg.state==31){
                        // set state = 106 to reset allin animation
                        player.state = CUSTOM_PLAYER_STATE.WAIT_FOR_START;
                    }
                    if( this.seatedPlayers[player.seatNum] )
                    {
                        this.seatedPlayers[player.seatNum].UpdatePlayer(this, player);
                    }
                    else
                    {
                        this.seatedPlayers[player.seatNum] = new holdemPlayerStore(this, player);
                    }

                    if (player.flags&2){
                        cc.vv.ConsoleLog.log('snapshot holecards', player.holeCards);
                        if (player.holeCards.length){
                            if (this.isReplay && MultipleGame.instance){
                                if (player.state==ProtoBuf.holdem.Action.ALL_IN){
                                    this.seatedPlayers[player.seatNum].Showdown(player.holeCards, 0, null);
                                }else{
                                    this.seatedPlayers[player.seatNum].OnNewCards(null);
                                }
                            }else{
                                this.seatedPlayers[player.seatNum].Showdown(player.holeCards, 0, null);
                            }

                        } else{
                            this.seatedPlayers[player.seatNum].OnNewCards(null);
                        }
                    }
                    else
                    {
                        this.seatedPlayers[player.seatNum].hasCards = false;
                        this.seatedPlayers[player.seatNum].cards = [];
                    }
                    // if (cc.vv.DataManager.webPlatform==ProtoBuf.commonProto.PLATFORM.PKW){
                    //     cc.vv.ConsoleLog.log('check snapshot auto play', player.userId, player.flags, (player.flags & 1));
                    //     this.node.setPlayerAutoPlay(player.userId, !!(player.flags & 1));
                    // }

                }
                if( player.stateTurn && player.stateTurn != msg.state )
                {
                    cc.vv.ConsoleLog.log('pat check here', player.stateTurn, msg.state);
                    // check if state change before first action, mark for first round action
                    // if (!(msg.state == 5 && (player.stateTurn == 31 || player.stateTurn == 32 || player.stateTurn == 4))){
                    if (player.state != ProtoBuf.holdem.Action.FOLD && (player.state!==ProtoBuf.holdem.PlayerState.WAITING && player.state!==ProtoBuf.holdem.PlayerState.PLAYER_STATE_HU_WAIT) && !(msg.state == 5 && (player.stateTurn == 31 || player.stateTurn == 32 || player.stateTurn == 4))){
                        // not reset if player state == allin after first round
                        if (player.state!==ProtoBuf.holdem.Action.ALL_IN){
                            this.seatedPlayers[player.seatNum].state = ProtoBuf.holdem.Action.NONE_ACTION;
                        }
                    }
                }

                this.seatedPlayers[player.seatNum].calcBB = this.calcBigBlind;
                this.seatedPlayers[player.seatNum].countdownTtl = 0;
                this.seatedPlayers[player.seatNum].countdownLeft = 0;
                this.seatedPlayers[player.seatNum].needAnimation = false;
                this.tmpMaxDeskCoin = Math.max(this.tmpMaxDeskCoin, player.deskCoin);
                this.seatedPlayers[player.seatNum].SetStakeIcon(msg.bbPos, msg.sbPos);
                this.tempPot += player.deskCoin;
                if( outPlayerSeatNum.indexOf(player.seatNum) != -1 )
                {
                    this.seatedPlayers[player.seatNum].PlayerIsOut(false);
                }

            });

            if (!isSelf){
                this.self = null;
            }

            if( this.node && this.node.iconH && this.node.iconH.dealerIcon && this.node._players )
            {
                if( msg.dealerPos - 1 < this.node._players.length )
                {
                    if( this.node._players[msg.dealerPos - 1] )
                    {
                        this.node.iconH.stopDealer();
                        let endPos = this.node.iconH.getIconPos(CommonTools.instance.convertToWorldSpace(this.node._players[msg.dealerPos - 1].node, cc.v2(0,0)), this.node._players[msg.dealerPos - 1].layout, this.node.iconH.dealerIcon);
                        this.node.iconH.dealerIcon.position = endPos;
                    }
                }
            }

            const boardCards:number[] = [];
            
            msg.boardCards.forEach((value:number) => {
                boardCards.push(value);
                return null;
            });

            // this.boardCards = boardCards;
            this.allBoardCards = boardCards;

            if(msg.currAct) {
                cc.vv.ConsoleLog.log(626,msg.currAct);
                this.seatedPlayers[msg.currAct.seatNum].OnNeedAction(msg.currAct, msg.currAct.countdownTotal, msg.currAct.countdownLeft, msg.currAct.maxBetCoin);
                if (msg.currAct.extendTime){
                    this.seatedPlayers[msg.currAct.seatNum].state = CUSTOM_PLAYER_STATE.EXTEND_TIME;
                }
                if( this.self && (this.self.seatNum != msg.currAct.seatNum || !msg.currAct.countdownLeft) )
                {
                    this.self.showControl = false;
                }
            }
            else if( this.self )
            {
                this.self.showControl = false;
            }
            if (this.node){
                this.node.CheckPlayerControlPanel();
            }


            
            for(let i in this.seatedPlayers)
            {
                if( !msg.currAct || !msg.currAct.countdownLeft )
                {
                    this.seatedPlayers[i].countdownTtl = 0;
                }
            }
            if (this.node._dealerFirstDelay){
                // reset DealerPos to prevent dealer position same as last room
                this.dealerPos = 0;
            }

            this.dealerPos = msg.dealerPos;




            this.mainPot = msg.pots.length ? msg.pots[0] : 0;
            this.sidePot = msg.pots.length ? msg.pots : [];
            this.straddlePos = msg.straddlePos;

            for (const i of msg.pots){
                this.tempPot += i;
            }

        }




    };

    //leave seat

    Standby() {
        //wait, not started, none
        if(this.roomId) {
            this.ws.Send(ProtoBuf.holdem.StandbyReq.create({roomId: this.roomId}));
        }
    }

    //leave seat

    @action
    protected onStandbyRes = (msg: holdem.StandbyRes) => {
        if(msg.roomId === this.roomId) {
            if (msg.code){
                if (msg.code==60010){
                    this.callPopUpBox(Translate(Translation.POPUP_HINTS.GAME_START), ()=>{});
                } else{
                    this.callPopUpBox("StandbyRes"+msg.code,()=>{});
                }
            }else{
                this.self = null;
                if( cc.isValid(this.node) )
                {
                    this.node.audioPlayer.playEffect(soundEffect.PlayerStandUp);
                }
            }
        }
    };

    RoomBill() {
        // this.self.HideControl();
        if(this.roomId) {
            this.ws.Send(ProtoBuf.holdem.RoomBillReq.create({roomId: this.roomId}));
        }
    }

    @action
    protected onRoomBillRes = (msg: holdem.RoomBillRes) => {
        if(this.roomId == msg.roomId) {
            if(msg.code==0) {
                // cc.vv.ConsoleLog.log(this.node)
                // cc.vv.ConsoleLog.log(this.node.gameRecord)

                // this.node.gameRecord.addRecord(msg);
                // cc.vv.ConsoleLog.log(Holdem_Room.instance.gameRecord);
                // Holdem_Room.instance.gameRecord.addRecord(msg);
                this.gameRecord = msg;
            }else{
                //todo error
            }
        }
    };

    @action
    public onHoleCardsMsg = (msg: holdem.HoleCardsMsg) => {
        cc.vv.ConsoleLog.log('onHoleCardsMsg', JSON.stringify(msg), this.roomId, new Date(), this.playerUserId);
        if( msg.roomId == this.roomId) {
            this.StopResetTable();
            Object.keys(this.seatedPlayers).forEach((seat) => {
                // const state = this.seatedPlayers[seat].state;
                //
                // cc.vv.ConsoleLog.log(222, 'player state', state);
                // // cc.vv.ConsoleLog.log('onHoleCardsMsg', this.seatedPlayers[seat], this.playerUserId == this.seatedPlayers[seat].userId, state);
                // if(state != PlayerState.WAITING && state != PlayerState.PAUSE) {
                //     this.seatedPlayers[seat].OnNewCards(this.playerUserId == this.seatedPlayers[seat].userId ? msg.cards : null);
                // }
                if (this.playerUserId==this.seatedPlayers[seat].userId){
                    this.waitForStart = false;
                    this.seatedPlayers[seat].needAnimation = true;
                    this.seatedPlayers[seat].OnNewCards(msg.cards);

                    //ensure player's layout is PlayerLayoutType.Self when receive HoleCardsMsg to fix #4049
                    if( this.node && this.node._players && this.seatedPlayers[seat].seatNum - 1 < this.node._players.length && this.node._players[this.seatedPlayers[seat].seatNum - 1] && this.node._players[this.seatedPlayers[seat].seatNum - 1].layout != PlayerLayoutType.Self && (!this.isReplay || !MultipleGame.instance))
                    {
                        this.node._players[this.seatedPlayers[seat].seatNum - 1].layout = PlayerLayoutType.Self;
                    }
                }
            });

        }

    };

    @action
    public onHoleCardList = (msg: holdem.HoleCardListMsg)=>{

        cc.vv.ConsoleLog.log('onHoleCardList ', JSON.stringify(msg));

        if (msg.roomId == this.roomId){
            this.StopResetTable();
            if(this.handForHandMessage == ProtoBuf.mttPro.Tips_Type.Game_Sync_Pokering){
                this.handForHandMessage = 0;
            }
            for (const i of msg.playerList){
                if (this.playerUserId!==this.seatedPlayers[i].userId) {
                    this.seatedPlayers[i].needAnimation = true;
                    this.seatedPlayers[i].OnNewCards(null);
                }
            }
        }



        // Object.keys(this.seatedPlayers).forEach((seat,index) => {
        //     cc.vv.ConsoleLog.log(seat, index);
        //     const state = this.seatedPlayers[seat].state;
        //     // cc.vv.ConsoleLog.log('onHoleCardsMsg', this.seatedPlayers[seat], this.playerUserId == this.seatedPlayers[seat].userId, state);
        //     if(state != PlayerState.WAITING && state != PlayerState.PAUSE) {
        //         this.seatedPlayers[seat].OnNewCards(null);
        //     }
        // });
    };

    @action
    protected onPlayerNickNameChange = (msg:holdem.PlayerNickNameChangeMsg)=>{
        if (msg.roomId==this.roomId){
            cc.vv.ConsoleLog.log('onPlayerNickNameChangeMsg', JSON.stringify(msg));

            for(let i in this.seatedPlayers) {
                if (this.seatedPlayers[i].userId == msg.userId){
                    this.seatedPlayers[i].nickName = msg.nickName;
                }
            }
        }
    };

    @action
    public onBoardCardsMsg = (msg: holdem.BoardCardsMsg) => {

        if( msg.roomId == this.roomId) {
            this.needAnimation = true;

            cc.vv.ConsoleLog.log('onBoardCardsMsg', JSON.stringify(msg));

            // const boardCards:number[] = [];
            const allBoardCards = this.allBoardCards.slice();

            cc.vv.ConsoleLog.log('onBoardCardsMsg ->all ',allBoardCards);

            msg.cards.forEach(v => {
                // boardCards.push(v);
                if( allBoardCards.indexOf(v) == -1)
                {
                    allBoardCards.push(v);
                }

            });

            // this.boardCards = boardCards;

            this.allBoardCards = allBoardCards;
            
            for(let i in this.seatedPlayers) {
                this.seatedPlayers[i].NewTurn();
            }

        }

    };

    PauseGame() {
        //waiting
        if(this.roomId) {
            this.ws.Send(ProtoBuf.holdem.PauseGameReq.create({roomId: this.roomId}))
        }
    }

    @action
    protected onPauseGameRes = (msg: holdem.PauseGameRes) => {
        if(msg.roomId == this.roomId) {
            if(msg.code===0) {
                //todo
            }else{

            }
        }
    };

    LeaveGame() {
        cc.vv.ConsoleLog.log('leave game');
        if(this.roomId) {
            this.ws.Send(ProtoBuf.holdem.LeaveRoomReq.create({roomId: this.roomId}))
        }
        // this.ws.RemoveMessageHandler(owWebSocket.EVENT_ID.ON_CLOSE, this.onLostConnection);
        // this.ws.close();
        // cc.vv.ConsoleLog.log(this.ws.isConnected(), this.ws);

        cc.vv.DataManager.currentRoomID = 0;
        this.reboundGameView(false, false);
        // if(this.roomId) {
        //     this.ws.Send(ProtoBuf.holdem.LeaveRoomReq.create({roomId: this.roomId}))
        // }
    }

    @action
    protected onLeaveRoomRes = (msg: holdem.LeaveRoomRes) => {
        if(msg.roomId === this.roomId ) {
            if(msg.code == 0) {
                this.focused = false;

                cc.vv.DataManager.currentRoomID = 0;
                // cc.vv.AssetsManager.loadScene("hall");
                // this.reboundGameView();
            }else{
                //todo
            }
        }
    };

    @action
    public onPotsMsg = (msg: holdem.PotsMsg) => {
        if(this.roomId === msg.roomId) {
            cc.vv.ConsoleLog.log('onPotsMsg',JSON.stringify(msg));
            this.tmpMaxDeskCoin = 0;
            this.mainPot = msg.pots[0];
            this.sidePot = msg.pots.length ? msg.pots : [];

            for(let i in this.seatedPlayers) {
                this.seatedPlayers[i].NewTurn();
            }

        }
    };

    @action
    protected onNeedMoreCoinMsg = (msg: holdem.NeedMoreCoinMsg) => {
        if(msg.roomId == this.roomId) {
            cc.vv.ConsoleLog.log('onNeedMoreCoinMsg',JSON.stringify(msg));
        }
    };

    @action
    public changeBlind(smallBlind:number, bigBlind:number, ante:number, animate:number = 0)
    {
        this.bigBlind = bigBlind;
        this.smallBlind = smallBlind;
        this.ante = ante;
        this.animate = animate;
    }

    @action
    protected onRiseBlindNotifyMsg = (msg: mttPro.RiseBlindNotifyMsg) => {
        if(msg.roomId == this.roomId) {
            cc.vv.ConsoleLog.log('onRiseBlindNotifyMsg',JSON.stringify(msg));
            this.currentBlindLevel = msg.riseIndex;
            if (this.checkIsLastBlindLevel()){
                this.node.switchBlindMode(this.blindHands==0,this.checkIsLastBlindLevel());
            }
            if (this.rbcList.length){
                let currentBlindInfo = this.rbcList[msg.riseIndex-1];
                cc.vv.ConsoleLog.log('onRiseBlindNotifyMsg',currentBlindInfo);
                this.bigBlind = currentBlindInfo.BigBlind;
                this.smallBlind = currentBlindInfo.SmallBlind;
                this.ante = currentBlindInfo.Ante;
                // reset calc blind in hu mode
                if (msg.riseIndex==1){
                    this.calcBigBlind = currentBlindInfo.BigBlind;
                    this.calcSmallBlind = currentBlindInfo.SmallBlind;
                    this.calcAnte = currentBlindInfo.Ante;
                }
            }

            // mtt hide rest message
            // will rest need show until rest
            // if (this.restMessage!==3) {
            //     this.restTime =0;
            //     this.resetRest();
            // }
            // this.bigBlind = msg.bb;
            // this.smallBlind = msg.sb;
            // this.ante = msg.ante;
            if (msg.riseIndex>1){
                this.animate = HOLDEM_ANIMATION.RISE_BLIND;
            } else {
                this.node.RiseBlind(msg.riseLeftTime);
            }
            this.node.tournamentRecord.updateBlindTimeCountdown(msg.riseLeftTime);
            this.showBlindTimeCounter(msg.riseLeftTime > 0);
        }
    };

    SngRoomSnapShot() {
        this.ws.Send(ProtoBuf.mttPro.SngRoomSnapShotReq.create({roomId: this.roomId}));
    }

    _isJackpotMatch:boolean = false;
    @action
    protected onSngRoomSnapShotMsg = (msg: mttPro.SngRoomSnapShotMsg) => {

        if(msg.roomId == this.roomId) {
            cc.vv.ConsoleLog.log('SngRoomSnapshotMsg', JSON.stringify(msg));

            this.riseBlindTime = msg.riselefttime;

            // room config
            this.uuid = msg.Uuid;
            this.srvFee = msg.srvFee;
            this.regFee = msg.regFee;
            this.blindLevelTime = msg.riseBlindTime;
            this.riseBlinding = msg.riseBlinding;
            if (cc.isValid(this.node)) {
                this.node.stopBlindTimeCounter = !msg.riseBlinding;
            }

            this.blindHands = msg.BlindHands;

            this.currencyRate = msg.CurrencyRate;
            this.exchangeRate = this.getCurrencyRate(globalConfig.CURRENCY.USD);
            if (!this.displayCurrency) {
                //All JSNG currency USD
                this.displayCurrency = globalConfig.CURRENCY.USD;
                this.node.tournamentRecord.setDisplayCurrency(this.displayCurrency);
            }

            this.node.tournamentRecord.setBlindRiseType(this.blindRiseType);

            this.tournamentRoomName = msg.roomName;
            this.node.gameRecord.setGameTitle(Translate(Translation.GAME_RECORDS.REAL_TIME_RECORD));
            this.node.gameRecord.resetTimestamp();

            this._isJackpotMatch = msg.Multiplier == msg.AllMultiplier[msg.AllMultiplier.length-1];

            if(this.node && (msg.roomStatus==32 || msg.roomStatus<4)){
                // this.node.RoomInfoAlert(true, msg.reliveCount);
            }else{
                this.node.RoomInfoAlert(false);
            }

            this.rbcList = msg.rbc;
            this.rewardList = msg.reward;
            for(let i=0; i<this.rewardList.length; i++){
                this.prizeList[i+1] = {
                    money: FormatParser.DisplayGold(this.rewardList[i])
                };
            }
            this.currentBlindLevel = msg.riseIndex;
            let ri = msg.riseIndex>0? msg.riseIndex-1 : 1;
            this.smallBlind = msg.rbc[ri].SmallBlind;
            this.bigBlind = msg.rbc[ri].BigBlind;
            this.ante = msg.rbc[ri].Ante;

            if (this.node){
                this.node.switchBlindMode(this.blindHands==0,this.checkIsLastBlindLevel());

                if (msg.sngStatus==ProtoBuf.mttPro.Sng_Status.Sng_Status_Playing || msg.sngStatus==ProtoBuf.mttPro.Sng_Status.Sng_Status_Pause){
                    this._sngWaitingSitDown = false;
                    this.node.stopMatchGameAnim();
                    if (msg.Uuid){
                        let jValue = this._isJackpotMatch?msg.Jackpot + this.regFee*msg.Multiplier:this.regFee*msg.Multiplier;
                        this.node.setJackpotValueLabel(jValue,this._isJackpotMatch);
                        this.node.jackpotValueShowAnim();
                    }
                    this.node.setSNGBg(msg.AllMultiplier,msg.Multiplier,this._isJackpotMatch);
                }else if (msg.sngStatus==ProtoBuf.mttPro.Sng_Status.Sng_Status_Not_Start){
                    this.EnterSngRoomLevel();
                    this.node.setSNGBg(msg.AllMultiplier,-1,false);
                    if(msg.players.length > 0 && (!msg.Uuid || msg.players.length < 3))
                    {
                        this.node.matchingGameAnim();
                    }
                    else
                    {
                        this.node.stopMatchGameAnim();
                    }
                }
            }

            if (msg.BlindHands>0){
                this.riseBlindHand = (this.checkIsLastBlindLevel())?0:msg.LeftBlindHands;
                this.node.updateBlindHand(this.riseBlindHand);
            }

            //0 未开始 1 已开始 3 已结束  101 暂停
            let ss = ProtoBuf.mttPro.Sng_Status;
            if (msg.sngStatus==ss.Sng_Status_Pause){
                if(msg.willPlayStartTime) {
                    this.resumeTimeRemain = Math.floor(msg.willPlayStartTime - (cc.vv.DataManager.getNow().getTime()/1000));
                }else{
                    this.resumeTimeRemain = 0;
                }
                this._pauseMessage = cc.vv.DataManager.i18DataFromServer(msg.pauseMessage,msg.pauseMessageI18N);
                if (this.roomMessage==3 && this.node){
                    // this.roomMessage set to 3 and snapshot still in 3
                    this.node.RestMessage();
                }
                this.roomMessage = 3;
            }else{
                this.roomMessage = 0;
            }

            // set player's rank
            if(msg.players.length > 0){
                for(const player in msg.players) {
                    for(const i in this.seatedPlayers){
                        if(this.seatedPlayers[i].userId == msg.players[player].userId){
                            this.node.setTimeBankButton(msg.timeBankFlag);
                            this.node.setTimeBankText(msg.timeBankDuration+"s");
                            
                            //msg.Multiplier != 0 mean SNG game recieved SngStartNotifyMsg, Uuid is recieved after matched only
                            if (msg.Uuid && msg.Multiplier != 0){
                                if (this.seatedPlayers[i].state!==ProtoBuf.holdem.Action.ALL_IN && this.seatedPlayers[i].leftCoin==0){
                                    if (msg.players[player].userId == this.self.userId){
                                        if (!cc.isValid(this.loseFlowPopup[1])){
                                            this.callPopUpBox(Translate(Translation.HOLDEM.JSNG.USER_ELIMINATED),()=>{
                                                this.reboundGameView();
                                            });
                                        }
                                    }
                                    this.seatedPlayers[i].SetUserRank(msg.players[player].rank);
                                    if( msg.players[player].rank )
                                    {
                                        this.seatedPlayers[i].PlayerIsOut(false);
                                    }
                                    else
                                    {
                                        this.seatedPlayers[i].PlayerIsBack();
                                    }
                                }
                            }
                            else
                            {
                                this.seatedPlayers[i].SetUserRank(msg.players[player].rank);
                                if( msg.players[player].rank )
                                {
                                    this.seatedPlayers[i].PlayerIsOut(false);
                                }
                                else
                                {
                                    this.seatedPlayers[i].PlayerIsBack();
                                }
                            }
                        }
                    }
                }
            }

            // set buy time count
            this.buyTimeCount = msg.buyTimeCount;
            // reset isBuyingTime in snapshot
            if (this.node.playerControl){
                this.node.playerControl.isBuyingTime = false;
            }

            this.leftRejoinCount = msg.LeftRejoinCount;

            if(this.node){
                if (msg.roomStatus==30 || msg.roomStatus==31){
                    this.node.guessHand.PanelActive(false);
                } else{
                    if (msg.betType){
                        this.node.guessHand.PanelSnapshot({type:msg.betType, amount:msg.betAmount});
                    }else{
                        this.node.GuessedHandReset();
                    }
                }
            }





            // if (this._snapshotSelfFold){
            //     this._snapshotSelfFold = false;
            //
            //     this.node.PlayerGuessCardNextHandPanel(true, (msg.betAmount && msg.betType)?{type:msg.betType, amount:msg.betAmount}:null);
            //
            // }else{
            //     this.node.PlayerGuessCardNextHandPanel(false);
            // }

            // check if rejoin popup no show to set the fake rewardMsg
            if (msg.reliveLeftTime && !this.loseFlowPopup[0]){
                this.SNGReliveLeftTime = msg.reliveLeftTime;
                let rewardFake:mttPro.RewardMsg = ProtoBuf.mttPro.RewardMsg.create();
                
                let data = msg.players.filter(p=>p.userId==this.self.userId);

                rewardFake.rank = (data.length==1)?data[0].rank:50;
                rewardFake.mttId = msg.roomId;
                rewardFake.leftRejoinCount = msg.LeftRejoinCount;
                rewardFake.reward = msg.curPlayerReward;
                let totalPeople = Object.keys(this.seatedPlayers).length;
                if ((totalPeople<5 && rewardFake.rank==1) || (totalPeople<8 && rewardFake.rank<3) || (totalPeople==9 && rewardFake.rank<4) ){
                    rewardFake.rewardType = ProtoBuf.mttPro.Rank_Type.Rank_Type_Reward;
                } else{
                    rewardFake.rewardType = ProtoBuf.mttPro.Rank_Type.Rank_Type_No_Reward;
                }

                this.onRewardMsg(rewardFake);



                // msg.curPlayerReward
            }

            this.sngStatus = msg.Uuid?msg.sngStatus:ProtoBuf.mttPro.Sng_Status.Sng_Status_Pause;

            if (msg.sngStatus == ProtoBuf.mttPro.Sng_Status.Sng_Status_Release && !this.isGameEnd) {
                this.showDialogBox(
                    "",
                    Translate(Translation.POPUP_HINTS.GAME_END),
                    false, [
                        {
                            type: 0,
                            text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                            callback: ()=>{
                                this.reboundGameView();
                            },
                        },
                    ]
                );
            }

            // enable player use voice message
            if (!this.isReplay && msg.sngStatus >= 1 && this.getSeatedPlayerByUserId(this.playerUserId)) {
                cc.vv.ConsoleLog.log("Game mic active at snapshot");
                this.node.emojiButton.node.active = true;
                this.node.enableGameMic(true);
            }else{
                cc.vv.ConsoleLog.log("Hide Game mic active at snapshot");
                this.node.enableGameMic(false);
                this.node.emojiButton.node.active = false;
            }

        }
    };

    MttRoomSnapShot() {
        this.ws.Send(ProtoBuf.mttPro.MttRoomSnapshotReq.create({mttId: this.tournamentId}));
    }

    checkIsLastBlindLevel(){
        return (this.rbcList && this.rbcList.length>0 && this.rbcList.length==this.currentBlindLevel);
    }

    @action
    onMttRoomSnapShot = (msg:mttPro.MttRoomSnapshotRes) => {
        if (msg.roomId == this.roomId){
            cc.vv.ConsoleLog.log('onMttRoomSnapshot', JSON.stringify(msg));
            this.voiceInFinal = msg.VoiceInFinal;

            this.roomMessage = 0;
            this.roomStatus = msg.roomStatus;
            this.rebuyLeftTime = msg.rebuyLeftTime;
            this.isRebuyAllow = msg.allowRebuy;
            this.morebuyLeftTime = msg.moreBuyLeftTime;
            this.isMorebuyAllow = msg.allowMorebuy;

            this.rebuyEndTime = this.getEndTimestamp(msg.rebuyLeftTime);
            this.morebuyEndTime = this.getEndTimestamp(msg.moreBuyLeftTime);

            this.currencyRate = msg.CurrencyRate;
            this.exchangeRate = this.getCurrencyRate(msg.DisplayCurrency);
            if (!this.displayCurrency){
                this.displayCurrency = msg.DisplayCurrency;
                this.node.tournamentRecord.setDisplayCurrency(this.displayCurrency);
                this.node.tournamentRecord.currencyToggle.node.active = this.displayCurrency != globalConfig.CURRENCY.GOLD;
            }

            this.currentBlindLevel = msg.blindIndex;
            this.riseBlindTime = msg.RiseLeftTime;
            this.riseBlinding = msg.riseBlinding;
            if( cc.isValid(this.node) )
            {
                this.node.stopBlindTimeCounter = !msg.riseBlinding;
            }
            this.blindHands = msg.BlindHands;
            if (this.node) {
                this.node.tournamentRecord.setBlindRiseType(this.blindRiseType);
                switch (this.blindRiseType) {
                    case globalConfig.BLIND_RISE_TYPE.DURATION:
                        this.node.tournamentRecord.updateBlindTimeCountdown(msg.RiseLeftTime);
                        break;
                    case globalConfig.BLIND_RISE_TYPE.HANDS:
                        this.node.tournamentRecord.levelCountdownTimer.string = msg.LeftBlindHands.toString();
                        break;
                }
                this.node.switchBlindMode(this.blindHands==0,this.checkIsLastBlindLevel());
            }

            if (this.rbcList && this.rbcList.length > 0){
                let ri = msg.blindIndex>0? msg.blindIndex-1 : 1;
                if( this.rbcList[ri] )
                {
                    this.smallBlind = this.rbcList[ri].SmallBlind;
                    this.bigBlind = this.rbcList[ri].BigBlind;
                    this.ante = this.rbcList[ri].Ante;
                }

                let rbi = msg.roomBlindIndex>0? msg.roomBlindIndex-1 : 1;
                if( this.rbcList[rbi] )
                {
                    this.calcSmallBlind = this.rbcList[rbi].SmallBlind;
                    this.calcBigBlind = this.rbcList[rbi].BigBlind;
                    this.calcAnte = this.rbcList[rbi].Ante;
                }
            }
            if (msg.BlindHands>0){
                this.riseBlindHand = (this.checkIsLastBlindLevel())?0:msg.LeftBlindHands;
                this.node.updateBlindHand(this.riseBlindHand);
            }
            this.waitForChangeTable = msg.NeedWaitEnterRoom;

            this.restTime = msg.RestLeftTime;
            this.restEndTime = this.getEndTimestamp(msg.RestLeftTime);

            if (cc.isValid(this.node.waitForTableMessageLayer)) {
                // #35353 do not show waitForTableMessageLayer during rest
                this.node.waitForTableMessageLayer.opacity = msg.RestLeftTime? 0 : 255;
            }

            this.restMessage = msg.RestType;
            this.leftRejoinCount = msg.leftRejoinCount;
            if(msg.players)
            {
                for (const i of msg.players){
                    if (this.self && i.userId==this.self.userId){
                        this.self.mttRank = i.rank;
                        if (this.node.playerControl){
                            this.node.playerControl.isBuyingTime = false;
                        }
                        this.buyTimeCount = i.buyTimeCount;
                        this.MTTRank = i.rank;
                        this.node.setTimeBankButton(msg.timeBankFlag);
                        this.node.setTimeBankText(msg.timeBankDuration+"s");

                        if (msg.thinkTime>0 && this.node){
                            this.node.checkSpeedModeAlert();
                        }
                        if (msg.bullet>0 && this.node){
                            this.node.checkBulletModeAlert();
                        }
                    }
                    this.updatePlayerWins(i.userId, i.wins, i.value);
                }
            }

            // ROOM_STATE_PAUSE          = 1005 //暂停, mttStatus = 5
            if( msg.roomStatus == ProtoBuf.mttPro.Mtt_Room_Status.MttROOM_STATE_PAUSE || msg.roomStatus==ProtoBuf.mttPro.Mtt_Room_Status.ROOM_STATE_FINALE_PAUSE)
            {
                this._pauseMessage = cc.vv.DataManager.i18DataFromServer(msg.message, msg.messageI18N);
                
                if(msg.willPlayStartTime) {
                    this.resumeTimeRemain = Math.floor(msg.willPlayStartTime - (cc.vv.DataManager.getNow().getTime()/1000));
                }else{
                    this.resumeTimeRemain = 0;
                }
                
                if (msg.roomStatus==ProtoBuf.mttPro.Mtt_Room_Status.MttROOM_STATE_PAUSE){
                    if (this.roomMessage==3 && this.node){
                        // this.roomMessage set to 3 and snapshot still in 3
                        this.node.RestMessage();
                    }
                    this.roomMessage = 3;
                }
                if (msg.roomStatus==ProtoBuf.mttPro.Mtt_Room_Status.ROOM_STATE_FINALE_PAUSE){
                    this.nextPeriodStartTime = Number(msg.nextPeriodStartTime)*1000;
                    if (this.roomMessage==7 && this.node){
                        this.node.RestMessage();
                    }
                    this.roomMessage = 7;
                }
            }
            else
            {
                if(msg.mttStatus == ProtoBuf.mttPro.Mtt_Status.Mtt_Status_Will_Reuby && msg.roomStatus == ProtoBuf.mttPro.Mtt_Room_Status.ROOM_STATE_Rebuy){
                    this.roomMessage = ProtoBuf.mttPro.Tips_Type.Game_Wait_Rebuy;
                    this._pauseMessage = "";
                    this.node.RestMessage();
                }
                
                // 等待同步發牌
                if( msg.roomStatus == ProtoBuf.mttPro.Mtt_Room_Status.ROOM_STATE_Sync_Poker){
                    this.handForHandMessage = ProtoBuf.mttPro.Tips_Type.Game_Sync_Pokering;
                } else {
                    this.handForHandMessage = 0; // reset when roomStatus updated 
                }
            }


            this.anmiTimes = msg.anmiTimes;
            this.emojiTimes = msg.emojiTimes;
            
            if( this.node )
            {
                if (msg.roomStatus==ProtoBuf.mttPro.Mtt_Room_Status.ROOM_STATE_SETTLE || msg.roomStatus==ProtoBuf.mttPro.Mtt_Room_Status.ROOM_STATE_NEXT_ROUND){
                    this.node.guessHand.PanelActive(false);
                } else{
                    if (msg.betType){
                        this.node.guessHand.PanelSnapshot({type:msg.betType, amount:msg.betAmount});
                    }else{
                        this.node.GuessedHandReset();
                    }
                }
                this.node.initLiveHandler();

                if (this.node.needShowInTheMoney && msg.roomStatus != ProtoBuf.mttPro.Mtt_Room_Status.ROOM_STATE_Sync_Poker) {
                    this.node.showInMoneyNotifyAtNewRound();
                }
            }

            // enable player use voice message
            if (!this.isReplay && this.getSeatedPlayerByUserId(this.playerUserId)) {
                cc.vv.ConsoleLog.log("Game mic active at snapshot");
                this.node.emojiButton.node.active = true;
                this.node.enableGameMic(true);
                // this.FilterGuessHandControlButton(); pkw no guess hand
            }else{
                cc.vv.ConsoleLog.log("Hide Game mic active at snapshot");
                this.node.enableGameMic(false);
                this.node.emojiButton.node.active = false;
                this.node.guessHandControl.active = false;
            }

            if(msg.mysterStatus){
                if(msg.mysterStatus == ProtoBuf.mttPro.MysterStatus_Type.MysterStatus_Draw || msg.mysterStatus == ProtoBuf.mttPro.MysterStatus_Type.MysterStatus_Animation){
                    if(this.node.mysteryBountyMain){
                        this.node.mysteryBountyMain.playTableChangeAnimaiton(true);
                    }else{
                        this.node.initMysteryBounty(()=>{
                            this.node.mysteryBountyMain.playTableChangeAnimaiton(true)
                        });
                    }
                }
            }

            this.isMysteryBountyOnIntro = msg.mysterStatus == ProtoBuf.mttPro.MysterStatus_Type.MysterStatus_Animation;
        }
    };

    AutoPlayMsg = ()=>{
        this._autoPlayAlert = true;
        cc.vv.ConsoleLog.log("** UI AUTO PLAY POP UP");
        this.showDialogBox(
            "",
            Translate(Translation.POPUP_HINTS.TIME_OUT),
            false, [
                {
                    type: 0,
                    text: Translate(Translation.POP_UP_BUTTON.I_AM_BACK),
                    callback: ()=>{
                        this._autoPlayAlert = false;
                        this.ws.Send(ProtoBuf.holdem.CancelAutoPlayReq.create({roomId:this.roomId}));
                    },
                },
            ]
        );
    };

    @action
    setAutoPlayLabel(userId:number, isAutoPlay:boolean){
        cc.vv.ConsoleLog.log('setAutoPlayLabel',this.seatedPlayers);
        Object.keys(this.seatedPlayers).forEach( (seatNum) => {
            if (this.seatedPlayers[seatNum].userId == userId) {
                this.seatedPlayers[seatNum].isAutoPlay = isAutoPlay;
            }
        });
    }

    onAutoPlayMsg = (msg:holdem.AutoPlayMsg)=>{
        if (msg.roomId == this.roomId){
            cc.vv.ConsoleLog.log('onAutoPlay', JSON.stringify(msg));
            if (cc.vv.DataManager.webPlatform == ProtoBuf.commonProto.PLATFORM.PKW){
                if (msg.autoPlay){
                    if (this.self && msg.userId == this.self.userId){
                        this.node.RemoveGuessHandLoop();
                        if (!this._autoPlayAlert){
                            this.AutoPlayMsg();
                        }
                    } else{
                        cc.vv.ConsoleLog.log('show auto play in other player');
                        // this.node.setPlayerAutoPlay(msg.userId, true);
                        this.setAutoPlayLabel(msg.userId, true);
                    }
                } else{
                    cc.vv.ConsoleLog.log('hide auto play in other player');
                    // this.node.setPlayerAutoPlay(msg.userId,false);
                    this.setAutoPlayLabel(msg.userId, false);
                }
            } else{
                this.node.RemoveGuessHandLoop();
                if (!this._autoPlayAlert){
                    this.AutoPlayMsg();
                }
            }

        }
    };

    onOtherRoomMsg = (msg:holdem.OtherRoomMsg)=>{
        cc.vv.ConsoleLog.log('onOtherRoomMsg', JSON.stringify(msg));
        cc.vv.ConsoleLog.log("** UI OTHER ROOM POP UP");
        this.showDialogBox(
            "",
            Translate(Translation.GAME.PLAYING_OTHER_ROOM),
            false, [
                {
                    type: 1,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.CANCEL),
                    callback: ()=>{

                    },
                },
                {
                    type: 0,
                    text: Translate(Translation.POPUP_HINTS.BACK_ROOM),
                    callback: ()=>{
                        // leave current room first
                        if(this.roomId) {
                            this.ws.Send(ProtoBuf.holdem.LeaveRoomReq.create({roomId: this.roomId}))
                        }
                        /*this.tournamentId = msg.roomId;
                        this.tournamentRoomName = msg.roomName;
                        cc.vv.DataManager.currentGameInfo.Detail.TournamentName = msg.roomName;
                        this.node._roomId = 0;
                        this.EnterRoom();*/
                        switch (msg.categary) {
                            case ProtoBuf.commonProto.GAME_CATEGORY.GEN_PP:
                                // this.node.initShowLoading();
                                this.gameViewLoader.callGameView(ProtoBuf.commonProto.MTT_GAME_MODE.PP, globalConfig.GAME_LEVEL_LIST_ID.PINEAPPLE_NORMAL, msg.roomId);
                                break;
                            case ProtoBuf.commonProto.GAME_CATEGORY.LOOP_PP:
                                // this.node.initShowLoading();
                                this.gameViewLoader.callGameView(ProtoBuf.commonProto.MTT_GAME_MODE.PP, globalConfig.GAME_LEVEL_LIST_ID.PINEAPPLE_ROLL, msg.levelId);
                                break;
                            case ProtoBuf.commonProto.GAME_CATEGORY.SNG:
                                // this.node.initShowLoading();
                                if (msg.isAof) {
                                    this.gameViewLoader.callGameView(ProtoBuf.commonProto.MTT_GAME_MODE.NLH, globalConfig.GAME_LEVEL_LIST_ID.AOF, msg.roomId);
                                } else {
                                    this.gameViewLoader.callGameView(ProtoBuf.commonProto.MTT_GAME_MODE.NLH, globalConfig.GAME_LEVEL_LIST_ID.SNG, msg.roomId);
                                }
                                break;
                        }

                        // this.Destory();
                        // @ts-ignore
                        // this.callGameView(this._roomMode, this._roomTournamentType, msg.roomId);
                    },
                },
            ]
        );
    };

    @action
    onMttNotifyMsg = (msg:mttPro.MttNotifyMsg)=>{
        cc.vv.ConsoleLog.log('onMttNotifyMsg', JSON.stringify(msg));

        let ckID = this.tournamentMode==globalConfig.GAME_LEVEL_LIST_ID.MTT ? this.tournamentId : this.roomId;
        if (msg.mttId == ckID && this.roomId == msg.roomId){

            const tt = ProtoBuf.mttPro.Tips_Type;
            // 3 = 游戏暂停提示  Game_Pause
            // 5 = 牌局已被管理员解散 Game_Release
            // 6 = 游戏重新开始  Game_ReStrart
            // 8 = Game_Sync_Poker_Enter
            // 9 = Game_Sync_Pokering
            // 10 = Game_Sync_Poker_Out
            // 11 = Game_Will_Playing
            // 12 = Game_Wait_Rebuy
            // 13 = Game_Wait_Morebuy
            // 14 = Game_Enter_Mystery
            // 15 = RiseBlind_Begin
            // 16 = RiseBlind_Pause

            if (this.tournamentMode!==globalConfig.GAME_LEVEL_LIST_ID.MTT){
                // this.sngStatus = 101;
                if (msg.tipsType==3){
                    this.sngStatus = 101;
                }
                if (msg.tipsType==6){
                    if (this.uuid){
                        this.sngStatus = 1;
                    }else{
                        this.sngStatus = 101;
                        return;
                    }
                }
            }
            
            if (msg.tipsType == ProtoBuf.mttPro.Tips_Type.RiseBlind_Begin || msg.tipsType == ProtoBuf.mttPro.Tips_Type.RiseBlind_Pause){
                this.riseBlinding = (msg.tipsType == ProtoBuf.mttPro.Tips_Type.RiseBlind_Begin);
                if (cc.isValid(this.node)){
                    this.node.stopBlindTimeCounter = !this.riseBlinding;
                }
            }

            if (msg.tipsType!==tt.Game_Release && msg.tipsType != ProtoBuf.mttPro.Tips_Type.RiseBlind_Begin && msg.tipsType != ProtoBuf.mttPro.Tips_Type.RiseBlind_Pause){
                if (msg.tipsType==tt.Game_Pause || msg.willPlayStartTime){
                    if(msg.willPlayStartTime){
                        this.resumeTimeRemain = Math.floor(msg.willPlayStartTime - (cc.vv.DataManager.getNow().getTime()/1000));
                    }else{
                        this.resumeTimeRemain = 0;
                    }
                    if(msg.message == ""){ // default message if server no return
                        this._pauseMessage = Translate(Translation.GAME.GAME_ROOM_PAUSE);
                    } else {
                        this._pauseMessage = cc.vv.DataManager.i18DataFromServer(msg.message, msg.messageI18N);
                    }
                }else{
                    this._pauseMessage = '';
                }

                if(msg.tipsType == ProtoBuf.mttPro.Tips_Type.Game_Sync_Poker_Enter || msg.tipsType == ProtoBuf.mttPro.Tips_Type.Game_Sync_Pokering || msg.tipsType == ProtoBuf.mttPro.Tips_Type.Game_Sync_Poker_Out ){
                    this.handForHandMessage = msg.tipsType; // hand for hand game sync
                }else{
                    if (msg.tipsType==7){
                        this.nextPeriodStartTime = Number(msg.nextPeriodStartTime)*1000;
                    }
                    if(this.restTime<=0 && this.restMessage!==ProtoBuf.mttPro.RestTime_Type.RestTime_Type_FINALS && this.restMessage!==ProtoBuf.mttPro.RestTime_Type.RestTime_Type_MIDFIELD){
                        this.resetRest();
                    }
                    this.roomMessage = msg.tipsType;
                }
                if(msg.tipsType == ProtoBuf.mttPro.Tips_Type.Game_ReStrart)
                {
                    if(cc.isValid(this.node))
                    {
                        this.roomStatus = 0; // hotfix, need modify in the future
                    }
                }

            }else if (msg.tipsType==tt.Game_Release){
                this.showDialogBox(
                    Translate(Translation.GAME.GAME_ROOM_RELEASE_TITLE),
                    (this.tournamentMode==globalConfig.GAME_LEVEL_LIST_ID.MTT)? Translate(Translation.GAME.GAME_ROOM_RELEASE): Translate(Translation.GAME.GAME_ROOM_RELEASE_SNG),
                    false, [
                        {
                            type: 0,
                            text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                            callback: ()=>{
                                this.reboundGameView();
                            },
                        },
                    ]
                );
            }
        }


    };
    @action
    resetHandForHandMessage = ()=>{
        this.handForHandMessage = 0;
    }

    @action
    protected onTimeBankMsg = (msg: mttPro.TimeBankMsg) => {
        if(msg.roomId == this.roomId) {
            this.seatedPlayers[msg.seatNum].timeBank = msg.timeBank
        }
    };

    TimeBankFlagSetReq(active:boolean) {
        this.ws.Send(ProtoBuf.mttPro.TimeBankFlagSetReq.create({flag:active, mttId:this.tournamentId}));
    }

    onTimeBankFlagSetRes = (msg:mttPro.TimeBankFlagSetRes) => {
        cc.vv.ConsoleLog.log('onTimeBankFlagSetRes', JSON.stringify(msg));
        if (!msg.mttId || msg.mttId == this.tournamentId){  // for backward compatible
            if (msg.code){
                this.callPopUpBox(Translate("ERROR_CODE_PKW." + msg.code), ()=>{});
            }else{
                this.node.setTimeBankButton(!this.node._timeBankFlag);
            }
        }
    }
    onTimeBankDurationMsg = (msg:mttPro.TimeBankDurationMsg) => {
        cc.vv.ConsoleLog.log('onTimeBankDurationMsg', JSON.stringify(msg));
        if (msg.roomId==this.roomId){
            if (this.self && this.self.seatNum==msg.seatNum){
                // update timeBankDuration
                this.node.setTimeBankText(msg.timeBankDuration+"s");
            }
        }
    }

    // protected onConnection = () => {
    //     if( this.hasDisconnected )
    //     {
    //         cc.vv.ConsoleLog.log("** RE CONNECTION");
    //         this.VerifyToken();
    //         this.hasDisconnected = false;
    //         // this.RoomSnapshot();
    //         // if( this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT )
    //         // {
    //         //     this.MttRoomSnapShot();
    //         // }
    //     }
    // };

    // protected onLostConnection = () => {
    //     cc.vv.ConsoleLog.log("** LOST CONNECTION");
    //     this.hasDisconnected = true;
    //     // if (this.ChangeGameView){

    //     if (this.ws.reconnectCount < 10){
    //         this.node.scheduleOnce(()=>{
    //             this.ws.reconnectCount++;
    //             owWebSocket.reconnectCountTotal++;
    //             cc.vv.ConsoleLog.log("_reconnectCount",this.ws.reconnectCount);
    //             this.ws.reConnect();
    //         }, 1);
    //     } else{
    //         if (!WorldWebSocket.getInstance().isShowDisconnectDialog){
    //             var dialogTexts = cc.vv.LanguageData.t("NETWORK.DISCONNECT_DIALOG").split("|");
    //             this.ws.isShowDisconnectDialog = true;
    //             cc.vv.AssetsManager.showDialogBox(dialogTexts[0], dialogTexts[1].replace("${server_name}", 'holdem'), false, [{
    //                 type: 0,
    //                 text: dialogTexts[3],
    //                 callback: ()=> {
    //                     this.ws.reconnectCount = 0;
    //                     this.ws.unrespondedVertificationTokenCount = 0;
    //                     this.ws.reConnect();
    //                     WorldWebSocket.getInstance().setReConnect();
    //                     WorldWebSocket.getInstance().reconnectCount = 0;
    //                     WorldWebSocket.getInstance().reConnect();
    //                     this.ws.isShowDisconnectDialog = false;
    //                     // cc.vv.AssetsManager.loadScene("hall");
    //                 }
    //             },
    //             {
    //                 type: 0,
    //                 text: dialogTexts[2],
    //                 callback: ()=> {
    //                     this.ws.reset();
    //                     this.ws.isShowDisconnectDialog = false;
    //                     // this.reboundGameView(this.isGameEnd); // isEnd
    //                     // cc.vv.AssetsManager.loadScene("hall");
    //                 }
    //             }]);
    //         }
    //     }
    //     // } else{
    //     //     cc.vv.AssetsManager.showDialogBox('', 'holdem'+'游戏伺服器没有回应, 稍后再尝试', false, [
    //     //         // 返回login
    //     //         {
    //     //             type: 0,
    //     //             text: '确定',
    //     //             callback: undefined
    //     //         }]);
    //     // }



    // };

    @action
    resetTable = () => {
        cc.vv.ConsoleLog.log("this.resetTable after", new Date().getTime());
        // this.boardCards = [];
        this.allBoardCards = [];
        this.mainPot = 0;
        this.tempPot = 0;
        this.sidePot = [];
        this.boardCardsHighlight = null;

        Object.keys(this.seatedPlayers).forEach(seat => {
            this.seatedPlayers[seat].ClearTable();
        });
        this.waitForResetTable = false;

        this.node.PlayerGuessCardNextHandPanel(false);

    };

    SNGRelive(reliveType:mttPro.Sng_Relive_Type, coin:number, toolId:number){
        cc.vv.ConsoleLog.log('sngReliveReq', this.roomId, reliveType, coin, toolId);
        if(this._rewardCount > 0){
            if (reliveType==1) {
                 // temp hide reward pop up if relive
                this.loseState[1] = false;
            }

            this.ws.Send(ProtoBuf.mttPro.SngReliveReq.create({roomId:this.roomId, reliveType:reliveType, Coin:coin, ItemId:toolId}));
            this._rewardCount = 0;
        }

    }


    @action
    onSngRelive =(msg:mttPro.SngReliveRes)=>{
        if (msg.roomId==this.roomId){
            // todo -> reset player ?
            cc.vv.ConsoleLog.log('onSngRelive', msg);
            if (msg.code){
                if (msg.code == 60077 || msg.code==50051) {
                    this.callPopUpBox(Translate(Translation.ERROR_CODE_PKW[msg.code]),()=>{
                        this.loseState[1] = true;
                        this.loseCondition(1);
                    });
                }else{
                    this.callPopUpBox("SngReliveRes "+msg.code,()=>{});
                }

                // failed to relive, then show 淘汰 animation
                if(this.self) {
                    this.self.PlayerIsOut(true);
                }
            }
            else
            {
                if (this.self && this.self.seatNum == msg.seatNum){
                    this.loseState[0] = false;
                    this.loseFlowPopup[0] = undefined;
                    if (msg.coin > 0) {
                        this.loseState[1] = false;
                        this.loseFlowPopup[1] = undefined;
                    }
                }

                // check if player is still here
                if(this.seatedPlayers[msg.seatNum]){
                    // check if sng relive rebuy or not
                    if (msg.coin>0){
                        // have rebuy coin
                        // todo sng relive to update layout to self
                        this.seatedPlayers[msg.seatNum].leftCoin = msg.coin;
                        this.seatedPlayers[msg.seatNum].PlayerIsBack();
                    } else {
                        // no rebuy coin 
                        this.seatedPlayers[msg.seatNum].PlayerIsOut(true);
                    }
                }

                // update relive count
                if(this.leftRejoinCount > 0){
                    this.leftRejoinCount--;
                }
            }
        }
    };

    GuessHandReq = (betAmount:number, betType:number, notFoldLoopGuess:boolean=false)=>{
        // if (this.tournamentMode==globalConfig.GAME_LEVEL_LIST_ID.MTT){
        //     cc.vv.ConsoleLog.log('PL0829 GuessHandReq MTT', betAmount, betType, this.tournamentId);
        // } else{
        //     cc.vv.ConsoleLog.log('PL0829 GuessHandReq SNG|AOF', betAmount, betType, this.tournamentId,this.roomId);
        //
        // }

        this._notFoldLoopGuess = notFoldLoopGuess;

        // Bet_Pair_A        = 1;  //一对A 200倍奖励
        // Bet_Has_A         = 2;  //带A
        // Bet_Has_A_K       = 3;  //带A或者K
        // Bet_Is_Pair       = 4;  //是对子
        // Bet_No_A_K_Pair   = 5;  //无A，K，对子


        if(this.tournamentId) {
            cc.vv.ConsoleLog.log('GuessHandReq', cc.vv.DataManager.userData.Gold, this.tournamentId, betType, betAmount,this._notFoldLoopGuess, 'check local storage with UserId', localConfig.getLocalStorageItem(localConfig.key_enableGuessHand+cc.vv.DataManager.userId, false));
            this.ws.Send(ProtoBuf.mttPro.GuessHandlePorkReq.create({stdId: this.tournamentId, betType: betType, betAmount:betAmount}));
        }
    };

    onGuessHandlePorkRes = (msg:mttPro.GuessHandlePorkRes) => {
        cc.vv.ConsoleLog.log('onGuessHandlePorkRes', JSON.stringify(msg), this.tournamentId);
        if (msg.stdId== this.tournamentId){
            if (msg.code){
                // 60083 case no need to handle
                if (msg.code!==60083){
                    this.callPopUpBox(Translate("ERROR_CODE_PKW." + msg.code), ()=>{});
                } 

            } else{
                // show success 2 second
                if (!this._notFoldLoopGuess && this.node) {
                    this.node.guessHand.SuccessPopUp();
                }else{
                    cc.vv.ConsoleLog.log('default guess loop success');
                }

            }
        }
    };



    onGuessHandlePorkMsg = (msg:mttPro.GuessHandlePorkMsg) => {
        cc.vv.ConsoleLog.log('onGuessHandlePorkMsg', JSON.stringify(msg), this.tournamentId);
        if (msg.stdId==this.tournamentId){
            if (this.node) {
                this.node.showGuessHandResult(FormatParser.DisplayGold((msg.reward), 2), this.self.seatNum);
            }
        }
        // if (msg.stdId== this.roomId){
        //
        // }
    };

    onCelebrityBroadcastNotifyFullMsg = (msg:holdem.CelebrityBroadcastNotifyFullMsg) => {
        cc.vv.ConsoleLog.log('onCelebrityBroadcastNotifyFullMsg', JSON.stringify(msg), this.roomId);
        if (msg.roomId == this.roomId) {
            if (this.node) {
                this.node.setValidBroadcast(!msg.closeBroadcast,true);
                this.node.enableGameMic(msg.closeBroadcast);
            }
        }
    };

    MTTRelive(toolId:number,toolInfo:any){
        // this.addSignUpLoading();
        if (cv.geoComplyManager?.isGeoTokenRequired()) {
            cv.geoComplyManager?.requestValidToken((geoToken = "") => {
                if (!geoToken) return;
                this.MTTRelive(toolId, toolInfo);
            }, () => {
                // handle error
            });
            return;
        }
        let inputData = {
            UserId: cc.vv.DataManager.userId,
            TournamentId: this.tournamentId,
            TicketId:toolId,
            PlatForm: 0,
            GeoComplianceToken: ""
        };
        cc.vv.ConsoleLog.log('MTTReliveReq', inputData.UserId, inputData.TournamentId, inputData.TicketId);
        httpApis.requestMttReenter(inputData,(data:any)=>{
            cc.vv.ConsoleLog.log('MTTRelive Response', data);
            if(data.ErrorCode){
                let hints = Translate("ERROR_CODE_PKW."+data.ErrorCode);
                cc.vv.ConsoleLog.log('MTTRelive error', data.ErrorCode);
                // 31000 stop relive, 31001 game ended
                if (data.ErrorCode == ProtoBuf.commonProto.ErrorCode.Mtt_GeoComplianceToken_Check_Fail) {
                    let geoComplyResponse = Buffer.from(data.GeoComplianceRes).toString('utf8');
                    geoComplyResponse = cv.tryParseJSON(geoComplyResponse) || {};
                    cv.geoComplyManager?.serverErrorHandler(geoComplyResponse, null, this.MTTRelive.bind(this, toolId), null, {
                        "ErrorType": "MttReEnterFailed",
                        "ErrorCode": data.ErrorCode.toString(),
                        "ErrorDetails": JSON.stringify(geoComplyResponse),
                    });
                }
                else {
                    let callback = (data.ErrorCode == 31001 || data.ErrorCode==31000)? ()=>{this.loseCondition(1);if (data.ErrorCode==31001){this.isGameEnd=true;} }:undefined;
                    this.callPopUpBox(hints, callback);
                }
            } else {
                // this.updateMyJoinStatus();
                // if (toolId>0){
                //     cc.vv.DataManager.updateToolList(false, false);
                // }

                this.loseState[0] = false;
                this.loseFlowPopup[0] = undefined;
                this.loseState[1] = false;
                this.loseFlowPopup[1] = undefined;
                this.loseState[3] = false;
                this._rewardCount = 0;

                // this.EnterRoom();
                // this.removeSignUpLoading(()=>{
                //     this.changeSignUpButtonStatus(MTT_BUTTON_STATUS.BACK_TO_GAME);//回到比賽
                //     this.callMttGameView();
                // });
            }
        }, (err:any) => {
            // this.removeSignUpLoading();
            cc.vv.ConsoleLog.log('MTTRelive Response err ',err, " object-> ",JSON.stringify(err));
            let filterErr = JSON.parse(JSON.stringify(err, ["message", "arguments", "type", "name"]));
            // prevent {isTrusted:true} err
            if (Object.keys(filterErr).length>0){
                this.msgErrorDialog(globalConfig.ERROR_TYPE.NETWORK_ERROR, globalConfig.NETWORK_ERROR_CODE.MTT_REENTER, null);
            }
        })
    }

    MTTMorebuy(toolInfo:any[]){
        cc.vv.ConsoleLog.log('MTTMorebuy 1st\n', toolInfo);
        let inputData = {
            UserId: cc.vv.DataManager.userId,
            TournamentId: this.tournamentId,
            TicketInfo: toolInfo,
            BuyCount: this.node.gameEndLayer.getComponentInChildren('SignUpWithToolPopUp').morebuyCount
        };
        cc.vv.ConsoleLog.log('MTTMorebuy', inputData.UserId, inputData.TournamentId, inputData.TicketInfo, inputData.BuyCount);

        httpApis.requestMttMorebuy(inputData,(data:any)=>{
            cc.vv.ConsoleLog.log('MTTMorebuy Response', data);
            if(data.ErrorCode){
                let hints = Translate("ERROR_CODE_PKW."+data.ErrorCode);
                cc.vv.ConsoleLog.log('MTTMorebuy error', data.ErrorCode);
                this.callPopUpBox(hints,()=>{});
            }
        }, (err:any) => {
            cc.vv.ConsoleLog.log('MTTMorebuy Response error', err);
        })
    }

    handleNoMoney(){
        cc.vv.ConsoleLog.log("** UI NO MONEY POP UP", this.roomNode);
        if (MTTConnector.instance.isPKW) // #37005
        {
            this.showDialogBox("",Translate(Translation.GAME.INSUFFICIENT_BALANCE),false,[
                {   type: 0,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                    callback:undefined
                }
            ])
        } else {
            this.showDialogBox("",Translate(Translation.POPUP_HINTS.INSUFFICIENT_BALANCE),false,[
                {   type: 0,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                    callback:()=>{
                        if (this.node){
                            this.node.onClickTopUpOnOtherPage(this.roomNode, undefined);
                        }else{
                            cc.vv.ConsoleLog.log('handleNoMoney - node not exist')
                        }

                    },
                    // action:action,
                },
                {   type: 1,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.CANCEL),
                    callback:undefined,
                    // action:action,
                },
            ])
        }
    }

    reJoinPopUp = (rank:number)=>{
        let prefabUrl = ResourcesLoader.RES_PATH.PREFAB.COMMON.MTT_SIGN_UP_BOX;

        // if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.AOF) {
        //
        // }else if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT){
        //
        // }
        cc.vv.ConsoleLog.log("** UI REJOIN POP UP");
        if (rank==-1 || rank==-2){
            // set rejoin when enter from menu
            this.loseState[0] = true;
        }

        let startTime = Math.floor( (cc.vv.DataManager.getNow().getTime())/1000);
        let endTime = (this.SNGReliveLeftTime)?this.SNGReliveLeftTime+startTime:startTime+15;

        // this.node.initShowLoading();
        let initCoin = this.MTTDetail.StartingCoins;
       
        if(this.initCoinRebuy){
            initCoin = this.initCoinRebuy;
        }

        cc.vv.DataManager.updateToolList(()=>{
            // this.node.initHideLoading();
            const loadPopUp = ()=>{
                cc.vv.ConsoleLog.log('check leftRejoinCount', this.leftRejoinCount);
                if (!this.leftRejoinCount && !this.isRebuyAllow){
                    this.loseState[0] = false;
                    return;
                }
                cc.vv.AssetsManager.loadRes(prefabUrl, cc.Prefab, (err:Error, prefab:cc.Prefab) => {
                    cc.loader.setAutoRelease(prefabUrl, true);
                    cc.vv.ConsoleLog.log('rejoin Prefab loaded', this.loseState);
                    if (!this.loseState[0]){
                        return;
                    }
                    let popupNode:cc.Node = cc.instantiate(prefab);
                    let popup:SignUpWithToolPopUp = popupNode.getComponent('SignUpWithToolPopUp');
                    popupNode.parent = this.node.gameEndLayer;
                    // const {currentGameInfo} = cc.vv.DataManager;
                    let totalFee = this.srvFee+this.regFee;
                    let regType = 0;
                    let toolCat = globalConfig.GAME_TO_TOOL_ID.MTT;
                    let showTimer:number = 0;
                    if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.SNG){
                        toolCat = globalConfig.GAME_TO_TOOL_ID.SNG;
                    } else if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.AOF){
                        toolCat = globalConfig.GAME_TO_TOOL_ID.AOF;

                        showTimer = endTime;
                    } else if (this.rebuyLeftTime > 0 && this.isRebuyAllow && this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT){
                        showTimer = this.rebuyEndTime;
                        regType = 1;
                    }
                    cc.vv.ConsoleLog.log('lose flow debug rejoin',this.loseFlowPopup);
                    this.loseFlowPopup[0] = popupNode;
                    cc.vv.ConsoleLog.log('lose flow debug rejoin',this.loseFlowPopup);

                    let signUpOptions = this.MTTDetail? this.MTTDetail.SignUpOptions.toLowerCase() : globalConfig.SIGN_UP_OPTIONS.all;
                    let toolTag = this.MTTDetail.Tag;

                    if(this.rebuyParam.defineParam){
                        signUpOptions = '';
                        toolTag = [];

                        if(this.rebuyParam.AllowGold){
                            signUpOptions += 'gold'
                        }

                        if(this.rebuyParam.AllowTool){
                            if(this.rebuyParam.AllowGold){
                                signUpOptions += ',tool'    
                            }else{
                                signUpOptions += 'tool'
                            }

                            if(this.rebuyParam.signupToolParamList){
                                let length = this.rebuyParam.signupToolParamList.length;
                                if(length > 0){
                                    signUpOptions += ',specific:mtt:';
                                    for(let i=0; i < length; i++){
                                        let toolId = this.rebuyParam.signupToolParamList[i].toolId.toString();
                                        if(i !== length - 1){
                                            toolId += ',';
                                        }
            
                                        let platform = this.rebuyParam.signupToolParamList[i].platform == 3?'a93:':'a92:';
            
                                        signUpOptions += platform + toolId
                                    }
                                }
                            }

                            if(this.rebuyParam.Tag){
                                toolTag = this.rebuyParam.Tag;
                            }
                        }
                    }

                    cc.vv.ConsoleLog.log("reJoinPopUp params: ", signUpOptions, toolTag, typeof(signUpOptions));

                    let signUpBoxProperty = new SignUpBoxProperty(Translate(Translation.HOLDEM.REVIVE_TOURNAMENT),totalFee,toolCat,signUpOptions,this.gameInvitationCode,toolTag,this.MTTDetail.DisplayCurrency, this.MTTDetail.Rate, showTimer, null,this.tournamentRoomName,-1,0, this.MTTDetail.RegGoldType, [], initCoin, regType);
                    cc.vv.ConsoleLog.log('rejoin tools:', signUpBoxProperty.tools.length);
                    if(signUpBoxProperty.toolOption && !signUpBoxProperty.goldOption && signUpBoxProperty.tools.length<1){
                        let hints = Translate(Translation.POPUP_HINTS.NO_TOOL);
                        this.callPopUpBox(hints,()=>{
                            if (popup){
                                popup.zoomOut();
                            }
                            this.loseFlowPopup[0] = undefined;
                            this.loseState[0] = false;
                            cc.vv.ConsoleLog.log('** close rejoin and check reward');
                            this.loseCondition(1);
                        });
                    }else{
                        popup.callPopUp(signUpBoxProperty,
                            (toolInfo:any)=>{
                                let toolId = toolInfo?toolInfo.Id:0;


                                if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.AOF){
                                    this.SNGReliveLeftTime = 0;
                                    if(toolId == 0 && (cc.vv.DataManager.userData.Gold<totalFee) ){
                                        this.handleNoMoney();
                                    }
                                    else{
                                        this.handleCoupon(toolInfo,()=>{
                                            let signUpFee:number = (toolInfo && toolInfo.Type === ProtoBuf.commonProto.TOOL_TYPE.coupon)? toolInfo.Value:totalFee;
                                            this.loseState[0] = false;
                                            this.SNGRelive(ProtoBuf.mttPro.Sng_Relive_Type.Sng_Relive_Type_Rejoin, signUpFee, toolId);
                                        },()=>{loadPopUp()},totalFee);

                                    }

                                } else if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT) {
                                    // todo -> mtt
                                    const balance = MTTConnector.instance.getUserGold();
                                    const multiplier = this.displayCurrency == globalConfig.CURRENCY.USD ? this.exchangeRate : 1;
                                    if (toolId==0&&(balance < totalFee * multiplier)){
                                        this.handleNoMoney();
                                    } else{
                                        this.handleCoupon(toolInfo, ()=>{
                                            this.loseState[0] = false;
                                            this.MTTRelive(toolId,toolInfo);
                                        }, ()=>{loadPopUp()}, totalFee);
                                    }
                                }
                            },
                            ()=>{
                                if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT){
                                    // todo -> mtt
                                    if(rank == -2){
                                        // pop up can show multiple times, comment to avoid multiple requests
                                        //this.MttCancelBuyReq(ProtoBuf.mttPro.MTT_CancelBuy_Type.Rebuy);
                                        this.resetRebuyAllow();
                                    }
                                } else{
                                    this.SNGReliveLeftTime = 0;
                                    this.SNGRelive(ProtoBuf.mttPro.Sng_Relive_Type.Sng_Relive_Type_Cancel, 0, 0);
                                }
                                this.loseState[0] = false;
                                cc.vv.ConsoleLog.log('** close rejoin and check reward');
                                if(!this.isRebuyAllow){
                                    this.loseCondition(1);
                                }

                                // play 淘汰 self only
                                if(this.self){
                                    this.self.PlayerIsOut(true);
                                }

                            });    
                        if(rank == -2){
                            this.isRebuyShown = true;
                        }
                    }
                })
            };
            loadPopUp();
        }, false);


    };

    @action
    resetRebuyAllow(){
        this.isRebuyAllow = false;
    }

    handleCoupon(toolInfo:any,signUpAction:(tool:any)=>any,cancelAction:()=>any, fee:number){

        if(toolInfo && toolInfo.Type === ProtoBuf.commonProto.TOOL_TYPE.coupon && toolInfo.Value != fee){
            let hints = "";
            let toolValue:number = FormatParser.DisplayGold(toolInfo.Value,2);
            if(toolValue > fee){
                hints = Translate(Translation.POPUP_HINTS.HIGHER_COUPON_VALUE);
            }
            else{
                hints = cc.js.formatStr(Translate(Translation.POPUP_HINTS.LOWER_COUPON_VALUE),toolValue,fee-toolValue);
            }
            this.callPopUpWithOptionTwo(hints,()=>{
                if ((fee-toolInfo.Value) > cc.vv.DataManager.userData.Gold ){
                    this.handleNoMoney();
                } else if(signUpAction instanceof Function){
                    signUpAction(toolInfo);
                }
            },()=>{
                if(cancelAction instanceof Function){
                    cancelAction();
                }
            },Translate(Translation.POPUP_TITLE.JOIN_TOURNAMENT));
        }
        else{
            if(signUpAction instanceof Function){
                signUpAction(toolInfo);
            }
        }
    }

    callPopUpWithOptionTwo(hints:string,okCallback:()=>any, cancelCallback:()=>any, title:string){

        this.showDialogBox(title, hints,false,[
            {   type: 1,
                text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.CANCEL),
                callback:cancelCallback,
            },
            {   type: 0,
                text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                callback:okCallback,
            },
        ])
    }

    @action
    onRewardMsg = (msg:mttPro.RewardMsg) => {
        if(msg.mttId==this.tournamentId) {
            // this.showReward = true;
            this.closeAllPopUp();
            cc.vv.ConsoleLog.log('onRewardMsg', JSON.stringify(msg));
            cc.vv.ConsoleLog.log("** UI GAME REWARD POP UP");

            // player lose to reset guess hand loop setting and buyTimeCount
            this.node.RemoveGuessHandLoop();
            // this.buyTimeCount = 0;

            this.lastRewardMsg = msg;
            this.displayCurrency = msg.DisplayCurrency;
            this.callRewardPopUp();

            if (msg.leftRejoinCount>0 && (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.AOF || this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT)){
                // ask rejoin
                this.leftRejoinCount = msg.leftRejoinCount;
                this.loseState[0] = true;
                this.loseState[1] = true;
                this._rewardCount = 1;
                this.reJoinPopUp(msg.rank);

            } else{
                // show end result
                this.loseState[1] = true;
                if (this.isMultiFlightDay1 && msg.rewardType == ProtoBuf.mttPro.Rank_Type.Rank_Type_Final && msg.mttFinalStartTime > 0){

                }else{
                    this.node.scheduleOnce(()=>{
                        if(!this.isRebuyAllow){
                            this.loseCondition(1);
                        }
                    }, 1);
                }

            }


        }

    };

    callRewardPopUp () {
        let popupURL = ResourcesLoader.RES_PATH.PREFAB.HOLDEM.GAME_OVER_WINDOW;

        if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.SNG){
            popupURL = ResourcesLoader.RES_PATH.PREFAB.HOLDEM.GAME_END_POP_UP_JSNG;
        }

        if (this.lastRewardMsg.rewardType == ProtoBuf.mttPro.Rank_Type.Rank_Type_Final && this.lastRewardMsg.mttFinalStartTime > 0){
            this.isMultiFlightAlert = true;
            this.closeAllPopUp();
            let gameName = cc.vv.DataManager.i18DataFromServer(this.lastRewardMsg.mttName, this.lastRewardMsg.mttNameI18N);
            let day2GameName = cc.vv.DataManager.i18DataFromServer(this.lastRewardMsg.mttFinalName, this.lastRewardMsg.mttFinalNameI18N);
            let date = cc.vv.DataManager.getDateTimeString('date',this.lastRewardMsg.mttFinalStartTime);
            let time = cc.vv.DataManager.getDateTimeString('time',this.lastRewardMsg.mttFinalStartTime);
            let timezone = cc.vv.DataManager.getDateTimeString('timezone',this.lastRewardMsg.mttFinalStartTime);

            let finalTxt = cc.js.formatStr(Translate(Translation.GAME_OVER.GO_TO_FINAL), gameName, date, time,timezone, day2GameName);

            this.callPopUpBox(finalTxt,()=>{
                this.reboundGameView(true);
            },Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK), Translate(Translation.GAME_OVER.CONGRATULATIONS));
        }else{
            cc.vv.AssetsManager.loadRes(popupURL, cc.Prefab, (err:Error, prefab:cc.Prefab) => {
                cc.vv.ConsoleLog.log('RewardPopUp prefab loaded', this.loseState, this.isMultiFlightDay1);
                if (!this.loseState[1]){
                    return;
                }
                let prizeStr:string = "";
                let prizeIsTools:boolean = false;
                if (this.lastRewardMsg.toolName) {
                    prizeStr = this.getDisplayReward(this.lastRewardMsg.reward, this.lastRewardMsg.toolValue, cc.vv.DataManager.i18DataFromServer(this.lastRewardMsg.toolName, this.lastRewardMsg.toolNameI18n),this.displayCurrency,this.lastRewardMsg.toolCurrency);
                    prizeIsTools = true;
                } else {
                    prizeStr = this.getDisplayGoldWithCurrency(this.lastRewardMsg.reward);
                }

                if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT){
                    let popupNode:cc.Node = this.loseFlowPopup[1];
                    if (!popupNode) {
                        cc.vv.ConsoleLog.log("Instantiate Reward PopUp");
                        popupNode = cc.instantiate(prefab);
                        let popup:GameOverWindow = popupNode.getComponent('GameOverWindow');
                        popup.setHoldemRoom(this.node);
                        popupNode.parent = this.node.gameEndLayer;

                        popup.callGameOverWindow(this.lastRewardMsg,()=>{
                            this.loseState[1] = false;
                            this.loseFlowPopup[1] = undefined;
                            this.loseCondition(3);
                        },prizeIsTools,prizeStr,this.isMultiFlightDay1,this.isFixPrize,this.displayCurrency,this.exchangeRate);

                        // cc.vv.ConsoleLog.log('lose flow debug reward',this.loseFlowPopup);
                        this.loseFlowPopup[1] = popupNode;
                        // cc.vv.ConsoleLog.log('lose flow debug reward',this.loseFlowPopup);
                        popupNode.active = false;
                        this.loseCondition(1);

                        // setTimeout(()=>{
                        //     if (popupNode.active){
                        //         popup.onCloseClicked();
                        //     }
                        // },4000);
                    } else {
                        cc.vv.ConsoleLog.log("Update Reward PopUp", popupNode);
                        let popup:GameOverWindow = popupNode.getComponent('GameOverWindow');
                        // if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && this.MTTDetail && (this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER || this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER)) {
                        //     popup.callGameOverWindow(this.lastRewardMsg.rank, this.lastRewardMsg.mttName, ()=>{
                        //         this.loseState[1] = false;
                        //         this.loseFlowPopup[1] = undefined;
                        //         this.loseCondition(3);
                        //     }, this.lastRewardMsg.rewardType, prize,toolVal, this.lastRewardMsg.bounty,prizeIsTools,prizeStr,this.isMultiFlightDay1,this.isFixPrize);
                        // } else {
                        popup.callGameOverWindow(this.lastRewardMsg,()=>{
                            this.loseState[1] = false;
                            this.loseFlowPopup[1] = undefined;
                            this.loseCondition(3);
                        }, prizeIsTools,prizeStr,this.isMultiFlightDay1,this.isFixPrize,this.displayCurrency,this.exchangeRate);
                        // }
                        popupNode.active = false;
                        this.loseCondition(1);
                    }
                } else if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.SNG){
                    cc.vv.ConsoleLog.log("Instantiate Reward PopUp SNG");

                    let popupNode = cc.instantiate(prefab);
                    popupNode.parent = this.node.gameEndLayer;
                    let popup:GameEndPopUpJsng = popupNode.getComponent(GameEndPopUpJsng);
                    let infoSNG:any = {};
                    infoSNG.levelId = this.levelId;
                    infoSNG.isJackpotMatch = this._isJackpotMatch;
                    infoSNG.isGPS = this.isGps;
                    this.loseFlowPopup[1] = popupNode;

                    this.node.hidePlayerPopUp();

                    popup.callGameEndPopUp(this.lastRewardMsg, infoSNG,()=>{this.reboundGameView(this.isGameEnd)},()=>{this.sngReEnterLevel()});
                    popupNode.active = false;
                    this.loseCondition(1);
                }

            });
        }
    }

    _sngWaitingSitDown:boolean = false;
    sngReEnterLevel = () => {
        if (cv.geoComplyManager?.isGeoTokenRequired()) {
            // request valid geocomply token
            cv.geoComplyManager?.requestValidToken((geoToken = "") => { 
                // Success callback
                if (!geoToken) return;
                this.sngReEnterLevel();
            }, () => {
                // Handle error
            });
            return;
        }
        if (this.isSNGMatching){
            return;
        }
        this.isSNGMatching = true;
        httpApis.requestSngRoomLevelInfo().then((msg:mttPro.SngRoomLevelInfoRes) => {
            if (!msg.code){
                if (msg.takeinCount<3){
                    this._sngWaitingSitDown = true;
                    if (this.isGps){
                        if (MTTConnector.instance.isBL) {
                            cc.vv.DataManager.GPSController.GPS((location:any) => {
                                if (!location) {
                                    cc.vv.ConsoleLog.log('GPS no location');
                                    return;
                                }
                                const inputData = {
                                    count: 1,
                                    levelId: this.levelId,
                                    lat: location.lat,
                                    lng: location.lng,
                                    geoComplianceToken: ""
                                };
                                if (cv.config.USE_GEOCOMPLY) {
                                    inputData.geoComplianceToken = cv.geoComplyManager?.getGeoToken();
                                }
                                this.requestSNGSignUp(inputData);
                            });
                        }
                        else {
                            const native = MTTConnector.instance.cv.native;
                            if (native.HaveGps(false)) {
                                const location = native.GetLocation();
                                if (!(location && location["latitude"] && location["longitude"])) {
                                    cc.vv.ConsoleLog.log('GPS no location');
                                    return;
                                }
                                const inputData = {
                                    count: 1,
                                    levelId: this.levelId,
                                    lat: location["latitude"],
                                    lng: location["longitude"],
                                    geoComplianceToken: ""
                                };
                                if (cv.config.USE_GEOCOMPLY) {
                                    inputData.geoComplianceToken = cv.geoComplyManager?.getGeoToken();
                                }
                                this.requestSNGSignUp(inputData);
                            }
                            else {
                                this.callPopUpBox(Translate(Translation.GPS.GENERAL.DIALOG.NO_OPEN), ()=>{});
                            }
                        }
                    }else{
                        const inputData = {
                            count: 1,
                            levelId: this.levelId,
                            lat: 0,
                            lng: 0,
                            geoComplianceToken: ""
                        };
                        if (cv.config.USE_GEOCOMPLY) {
                            inputData.geoComplianceToken = cv.geoComplyManager?.getGeoToken();
                        }
                        this.requestSNGSignUp(inputData);
                    }
                }else{
                    // too much
                    cc.vv.ConsoleLog.log('SngRoomLevelInfoRes error',msg);
                    this.callPopUpBox("take in count > 3",()=>{this.reboundGameView();});
                }

            }else{
                cc.vv.ConsoleLog.log('SngRoomLevelInfoRes error',msg);
                this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.code),()=>{this.reboundGameView();});
            }
        });
    }
    
    requestSNGSignUp(inputData:any)
    {
        const regFee = this.regFee;
        const usdt = MTTConnector.instance.getUserUsdt();
        if (usdt < regFee) {
            const diffInGold = (regFee - usdt) * this.exchangeRate;
            if (MTTConnector.instance.getUserGold() < diffInGold) {
                this.node.dialogController.callPopUpBox(Translate(Translation.GAME.INSUFFICIENT_BALANCE),()=>{this.reboundGameView();});
            }
            else {
                const cancelOption = {
                    type: 1,
                    text: Translate(Translation.POP_UP_BUTTON.CANCEL),
                    callback: () => {
                        this.isSNGMatching = false;
                    }
                };
                const confirmOption = {
                    type: 0,
                    text: Translate(Translation.POP_UP_BUTTON.CONFIRM),
                    callback: () => {
                        this.requestEnterSNGRoomLevel(inputData);
                    }
                };
                const hint = cc.vv.DataManager.customFormatStr(Translate(Translation.POPUP_HINTS.BUY_IN_AUTO_EXCHANGE_GOLD), [FormatParser.DisplayGold(diffInGold)]);
                this.node.dialogController.showDialogBox("", hint, false, MTTConnector.instance.isBL ? [cancelOption, confirmOption] : [confirmOption, cancelOption]);
            }
        }
        else {
            this.requestEnterSNGRoomLevel(inputData);
        }
    }

    requestEnterSNGRoomLevel(inputData:any){
        cc.vv.ConsoleLog.log('requestEnterSNGRoomLevel input',inputData);
        httpApis.requestEnterSngRoomLevel(ProtoBuf.mttPro.EnterSngRoomLevelReq.create(inputData)).then((msg2:mttPro.EnterSngRoomLevelRes)=>{
            if (msg2.code){
                this.isSNGMatching = false; // for EnterRoom
                this._sngWaitingSitDown = false;// for SngRoomSnapshot
                cc.vv.ConsoleLog.log('EnterSngRoomLevelRes error',msg2);
                this.callPopUpBox(Translate("ERROR_CODE_PKW."+msg2.code),()=>{this.reboundGameView();});
            }else{
                if (cc.isValid(this.loseFlowPopup[1])){
                    this.loseFlowPopup[1].destroy();
                }
                if( MultipleGame.instance )
                {
                    MultipleGame.instance.checkPlayerJointSNGGame(this.roomId);
                }
            }
        });
    }

    @action
    onSngStartNotifyMsg = (msg:mttPro.SngStartNotifyMsg) => {

        if(msg.roomId == this.roomId) {
            // msg.LeftTime
            cc.vv.ConsoleLog.log('onSngStartNotifyMsg', JSON.stringify(msg));
            this.uuid = msg.Uuid;
            this._sngWaitingSitDown = false;
            this.roomSnapshotState = 4;
            this.animate = HOLDEM_ANIMATION.GAME_START;
            this.node.stopMatchGameAnim();
            this._isJackpotMatch = msg.Multiplier == msg.AllMultiplier[msg.AllMultiplier.length-1];
            this.node.handlerJackpotDraw(msg.AllMultiplier,msg.Multiplier,this._isJackpotMatch,msg.Jackpot);
            this.node.setSNGBg(msg.AllMultiplier,msg.Multiplier,this._isJackpotMatch,true);
            if( MultipleGame.instance && this.node.multipleGameIndex >= 0 )
            {
                MultipleGame.instance.featureChangePage(this.node.multipleGameIndex);
            }

            // enable player use voice message
            if (this.getSeatedPlayerByUserId(this.playerUserId)&&this.node) {
                cc.vv.ConsoleLog.log("Game mic active at sng game start");
                // this.node.gameMicIcon.active = true;
                this.node.emojiButton.node.active = true;
                this.node.enableGameMic(true);
                this.node.FilterGuessHandControlButton();
            }
        }

    };
    @action
    updateRestRiseTime(riseBlindTime:number){
        this.riseBlindTime = riseBlindTime;
    }

    @action
    onMttRestTimeNotifyMsg = (msg:mttPro.MttRestTimeNotifyMsg) => {
        cc.vv.ConsoleLog.log("onMttRestTimeNotifyMsg check", this.tournamentId, msg.mttId, this.roomId, msg.RoomId, msg.RestType);
        if(msg.mttId == this.tournamentId && (!msg.RoomId || msg.RoomId == this.roomId)) {
            // msg.LeftTime
            cc.vv.ConsoleLog.log('onMttRestTimeNotifyMsg', JSON.stringify(msg));
            //msg.RestTimeDuration
            //msg.RestType

            this.restMessage = msg.RestType;
            this.roomMessage = 0;
            this.restTime = msg.RestTimeDuration;
            this.restEndTime = this.getEndTimestamp(msg.RestTimeDuration);


            // if (msg.RestType == ProtoBuf.mttPro.RestTime_Type.RestTime_Type_MIDFIELD) {
            //     // 中場
            //
            // } else if (msg.RestType == ProtoBuf.mttPro.RestTime_Type.RestTime_Type_FINALS) {
            //     // 完場前
            //
            // } else if (msg.RestType == ProtoBuf.mttPro.RestTime_Type.RestTime_Type_WILL_REST) {
            //     // 即將休息
            //
            // } else if (msg.RestType == ProtoBuf.mttPro.RestTime_Type.RestTime_Type_Null) {
            //
            // }

            if (cc.isValid(this.node.waitForTableMessageLayer)) {
                // #35353 do not show waitForTableMessageLayer during rest
                this.node.waitForTableMessageLayer.opacity = msg.RestTimeDuration? 0 : 255;
            }
        }
    };

    getEndTimestamp(duration: number){
        if(duration>0){
            return cc.vv.DataManager.getNow().getTime() + duration * 1000;
        } else {
            return 0;
        }
    }

    @action
    animationS(value:number){
        this.animate=value;
    }


    onSngRoomRankNotifyMsg = (msg: mttPro.SngRoomRankNotifyMsg) => {
        if(msg.roomId == this.roomId) {
            cc.vv.ConsoleLog.log('onSngRoomRankNotifyMsg', JSON.stringify(msg));
            this.closeAllPopUp();
            this.isGameEnd = true;

            // let infoSNG:any = msg;
            //
            // infoSNG.gameRoom = this.tournamentRoomName;
            // // info.gameType = this.tournamentType == HOLDEM_TOURNAMENT_TYPE.MTT ? "MTT" : "SNG";
            // infoSNG.gameType =  this.tournamentMode;
            // infoSNG.Joined = this.totalSeatCount;
            // // info.gameTime = `${Math.floor(msg.GameDuration / 3600)}:${Math.floor(msg.GameDuration / 60)}:${msg.GameDuration % 60}`;
            // infoSNG.gameTime = `${Translate(Translation.GAME_OVER.GAME_TIME)} :  ${this.secondToString(msg.GameDuration,1)} : ${this.secondToString(msg.GameDuration,2)} : ${this.secondToString(msg.GameDuration,3)}`;
            //
            // const{DataManager} = cc.vv;
            // infoSNG.Review = true;
            // infoSNG.gamePlayers = [];
            // for(const i of msg.players){
            //     cc.vv.ConsoleLog.log(i.reward);
            //     if (i.userId == DataManager.userId){
            //         infoSNG.Review = false;
            //         infoSNG.UserProfit = FormatParser.DisplayGold(i.reward, 2).toString();
            //         infoSNG.UserRank = i.rank;
            //         // set rank label
            //         this.self.SetUserRank(i.rank);
            //
            //         // cc.vv.ConsoleLog.log('game end find user');
            //         // break;
            //     }
            //     let player:any = {};
            //     player.ID = i.userId;
            //     // player.ResurrectionCount = this.tournamentType == HOLDEM_TOURNAMENT_TYPE.SNG ? 0 : 0; //todo ResurrectionCount
            //     player.PlayerName = i.nickName;
            //     player.Ranking = i.rank;
            //     player.Prize = FormatParser.DisplayGold(i.reward, 2).toString();
            //     infoSNG.gamePlayers.push(player);
            // }
            //
            //
            //
            // let prefabUrl = ResourcesLoader.RES_PATH.PREFAB.HOLDEM.GAME_END_POP_UP;;
            // if (infoSNG.Review) {
            //     prefabUrl = ResourcesLoader.RES_PATH.PREFAB.HOLDEM.GAME_END_POP_UP_REVIEW;
            // }
            //
            //
            // this.gameEndPopUp(prefabUrl, infoSNG);

        }

    };

    @action
    MTTAverageClearTable(){
        this.allBoardCards = [];
        this.boardCardsHighlight = null;
        this.mainPot = 0;
        this.tempPot = 0;
        this.sidePot = [];
        for (let i in this.seatedPlayers) {
            delete this.seatedPlayers[i];
        }
        // mtt average end to hide control panel
        if (this.node){
            this.node.ResetPlayerControl();
            this.node.PlayerGuessCardNextHandPanel(false);
        } else{
            cc.vv.ConsoleLog.log('MTTAverageClearTable - node not exist');
        }

    }

    gameEndPopUp(prefabUrl:string, data:any){
        this.node.scheduleOnce(()=>{this.animationS(1); cc.vv.ConsoleLog.log('end animation here');},2.5);
        // setTimeout(()=>{this.animationS(1); cc.vv.ConsoleLog.log('end animation here');},2500);

        cc.vv.ConsoleLog.log("** UI GAME RESULT POP UP");
        this.loseState[0] = false;
        this.loseState[2] = true;
        this.resetRest();
        if (this.tournamentMode==globalConfig.GAME_LEVEL_LIST_ID.MTT && this.MTTDetail.PrizeMode == globalConfig.MTT_PRIZE_MODE.MTT_PRIZE_CONFIG_MODE_AVERAGE){
            this.MTTAverageClearTable();
            if (this.loseFlowPopup[0]){
                this.loseFlowPopup[0].active = false;
            }
            this.loseFlowPopup[0] = null;
            this.loseState[0]=false;
        }

        this.loseCondition(2);

        // cc.vv.AssetsManager.loadRes(prefabUrl, cc.Prefab, (err, prefab) => {
        //     cc.vv.ConsoleLog.log('gameEndPopUp prefab loaded', this.loseState);
        //     let popupNode:cc.Node = cc.instantiate(prefab);
        //     popupNode.parent = this.node.gameEndLayer;
        //     let popup:GameEndPopUp = popupNode.getComponent('GameEndPopUp');
        //     if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && this.MTTDetail) {
        //         switch (this.MTTDetail.TournamentMode) {
        //             case ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER:
        //                 popup.mttHunterHeader.active = true;
        //                 break;
        //             case ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER:
        //                 popup.mttSuperHunterHeader.active = true;
        //                 break;
        //             default:
        //                 popup.normalHeader.active = true;
        //         }
        //     }else{
        //         popup.normalHeader.active = true;
        //     }
        //     // cc.vv.ConsoleLog.log('lose flow debug game over',this.loseFlowPopup);
        //
        //     this.loseFlowPopup[2] = popupNode;
        //
        //     popupNode.active = false;
        //
        //     if (this.tournamentMode==globalConfig.GAME_LEVEL_LIST_ID.MTT && this.MTTDetail.PrizeMode == globalConfig.MTT_PRIZE_MODE.MTT_PRIZE_CONFIG_MODE_AVERAGE){
        //         if (this.loseFlowPopup[0]){
        //             this.loseFlowPopup[0].active = false;
        //         }
        //         this.loseFlowPopup[0] = null;
        //         this.loseState[0]=false;
        //     }
        //     this.loseCondition(2);
        //     // this.loseState.game_end_result = true;
        //     // cc.vv.ConsoleLog.log('lose flow debug game over',this.loseFlowPopup);
        //
        //     popup.callGameEndPopUp(data, () => {
        //         this.reboundGameView(this.isGameEnd);
        //         // this.LeaveGame();
        //         // popup.destroy();
        //     });
        //
        // });

    }

    onMTTRoomRankNotifyMsg = (msg: mttPro.MttRoomRankNotifyMsg) => {

        if(msg.mttId == this.tournamentId) {
            cc.vv.ConsoleLog.log('onMTTRoomRankNotifyMsg', JSON.stringify(msg));
            if (this.isMultiFlightAlert == true){
                cc.vv.ConsoleLog.log('MultiFlight Day1 Alert -> true');
                return;
            }
            this.closeAllPopUp();
            this.isGameEnd = true;

            let info:any = msg;

            info.gameRoom = this.tournamentRoomName;
            info.gameType =  this.tournamentMode;
            // info.Joined = this.tournament.Seats;
            // info.gameTime = `${Math.floor(msg.GameDuration / 3600)}:${Math.floor(msg.GameDuration / 60)}:${msg.GameDuration % 60}`;
            info.gameTime = `${Translate(Translation.GAME_OVER.GAME_TIME)} :  ${this.secondToString(msg.GameDuration,1)} : ${this.secondToString(msg.GameDuration,2)} : ${this.secondToString(msg.GameDuration,3)}`;

            info.Review = true;
            info.gamePlayers = [];
            info.allPlayerCount = msg.allPlayerCount;
            if (msg.curPlayer.joinStatus!==0){
                if (msg.curPlayer.toolName) {
                    let reward = msg.curPlayer.reward;
                    if(this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && this.MTTDetail) {
                        if (this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER ||
                            this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER || 
                            ProtoBuf.commonProto.TOURNAMENT_MODE.Mystery) {
                                reward += msg.curPlayer.bounty;
                        }
                    }
                    info.UserProfit = this.getDisplayReward(reward, msg.curPlayer.toolValue, cc.vv.DataManager.i18DataFromServer(msg.curPlayer.toolName, msg.curPlayer.toolNameI18n),this.displayCurrency,msg.curPlayer.toolCurrency);
                } else {
                    info.UserProfit = this.getDisplayGoldWithCurrency((msg.curPlayer.reward + msg.curPlayer.bounty));
                }
                info.UserRank = msg.curPlayer.rank;
                info.Review = false;
            }
            for(const i of msg.players){
                cc.vv.ConsoleLog.log(i);
                let player:any = {};
                let reward: number = i.reward;
                player.ID = i.userId;
                // player.ResurrectionCount = this.tournamentType == HOLDEM_TOURNAMENT_TYPE.SNG ? 0 : 0; //todo ResurrectionCount
                player.PlayerName = i.nickName;
                player.Ranking = i.rank;
                if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT && this.MTTDetail) {
                    if (this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER) {
                        player.HunterValue = FormatParser.RoundToDecimal(i.wins, 1);
                        reward += i.bounty;
                    }
                    else if (this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER) {
                        player.HunterValue = this.getDisplayGoldWithCurrency(i.bounty);
                        reward += i.bounty;
                    }
                }

                if (i.toolName) {
                    player.Prize = this.getDisplayReward(reward, i.toolValue, cc.vv.DataManager.i18DataFromServer(i.toolName, i.toolNameI18n),this.displayCurrency,i.toolCurrency);
                } else {
                    player.Prize = this.getDisplayGoldWithCurrency(reward);
                }
                info.gamePlayers.push(player);
            }

            info.PrizePool = this.getDisplayGoldWithCurrency(msg.PrizePool);

            let prefabUrl = ResourcesLoader.RES_PATH.PREFAB.HOLDEM.GAME_END_POP_UP;
            if (info.Review) {
                prefabUrl = ResourcesLoader.RES_PATH.PREFAB.HOLDEM.GAME_END_POP_UP_REVIEW;
            }

            this.gameEndPopUp(prefabUrl, info);

        }

    };

    SngRealTimeRecord() {
        if(this.roomId) {
            this.ws.Send(ProtoBuf.mttPro.SngRealTimeRecordReq.create({roomId: this.roomId}));
        }
    }

    @action
    onSngRealTimeRecordRes = (msg: mttPro.SngRealTimeRecordRes) => {
        cc.vv.ConsoleLog.log(this.onSngRealTimeRecordRes.name, '----------------------------------------------start');
        if (msg.roomId == this.roomId){
            cc.vv.ConsoleLog.log(this.onSngRealTimeRecordRes.name,'ok', JSON.stringify(msg), cc.vv.DataManager.getNow());
            this.gameDuration = msg.Duration;
            if (MTTConnector.instance.isBL){
                this.node.gameRecord.updateRealTimeRecord(msg);
            }
            else {
                this.node.tournamentRecord.updateRealTimeRecord(msg);
            }
        }
        cc.vv.ConsoleLog.log(this.onSngRealTimeRecordRes.name, '----------------------------------------------end');
    };

    MttRealTimeRecord(fullData:boolean, roomId:number = 0) {
        this.ws.Send(ProtoBuf.mttPro.MttRealTimeRecordReq.create({mttId: this.tournamentId, roomId: roomId ? roomId : this.roomId, fullData: fullData}));
    }

    @action
    onMttRealTimeRecordRes = (msg: mttPro.MttRealTimeRecordRes) => {
        cc.vv.ConsoleLog.log(this.onMttRealTimeRecordRes.name, '----------------------------------------------start');
        if (msg.mttId == this.tournamentId){
            cc.vv.ConsoleLog.log(this.onMttRealTimeRecordRes.name,'ok', JSON.stringify(msg), cc.vv.DataManager.getNow());
            this.gameDuration = msg.Duration;
            // this.node.gameRecord.updateRealTimeRecord(msg);
            this.node.tournamentRecord.updateRealTimeRecord(msg);
            for(const player in msg.players)
            {
                if( this.playerUserId == msg.players[player] )
                {
                    this.self.mttRank = msg.players[player].rank;
                    this.MTTRank = msg.players[player].rank;
                }
            }
        }
        cc.vv.ConsoleLog.log(this.onMttRealTimeRecordRes.name, '----------------------------------------------end');
    };

    requestPlayerName (userId: number[]|string[], callback: (data:any)=>void) {
        httpApis.requestPlayersNickName(userId, callback, () => { cc.vv.ConsoleLog.log("Request Player Name Error."); });
    }

    secondToString(second:number, type:number){
        let num:number =0;
        switch (type) {
            case 1:
                num = Math.floor(second/3600);
                break;
            case 2:
                num = Math.floor(second/60%60);
                break;
            case 3:
                num = Math.floor(second%60);
                break;
        }

        return (Array(2).join('0') + num).slice(-2);
    }

    onMttLastRoomNotifyMsg = (msg:mttPro.MttLastRoomNotifyMsg) => {
        cc.vv.ConsoleLog.log('onMttLastRoomNotifyMsg', JSON.stringify(msg), "tournament Id->", this.tournamentId);
        if (msg.mttId == this.tournamentId) {
            this.voiceInFinal = this.MTTDetail.VoiceInFinal;
            if(this.self){
                this.node.setNotificationMsg(Translate(Translation.PKW.POPUP.LAST_ROOM));
            }


            if (this.node) {
                this.node.tournamentRecord.updateLateRegCountdown(0);
            }
        }
    };

    onEnterRewardMsg = (msg:mttPro.EnterRewardMsg) => {
        cc.vv.ConsoleLog.log('onEnterRewardMsg', JSON.stringify(msg), "tournament Id->", this.tournamentId);
        if (msg.mttId==this.tournamentId && this.self){
            this.node.needShowInTheMoney = true;
        }
    }

    onMttRoomEndNotifyMsg = (msg:mttPro.MttRoomEndNotifyMsg)=>{
        cc.vv.ConsoleLog.log('onMttRoomEndNotifyMsg', JSON.stringify(msg));
        if (this.roomId==msg.roomId){
            cc.vv.ConsoleLog.log('** UI GAME END POP UP');
            this.mttEndRoomId = msg.roomId;
            this.loseState[3] = true;
            this.loseCondition(3);
        }

    };

    @action
    onMttStopReJoinNotifyMsg = (msg:mttPro.MttStopReJoinNotifyMsg)=>{
        if (this.tournamentId==msg.mttId){
            this.leftRejoinCount = 0;
            if (this.loseState[0]){
                this.loseState[0] = false;
                if (this.loseFlowPopup[0]){
                    this.loseFlowPopup[0].active = false;
                }
                this.closeAllPopUp();
                this.node.scheduleOnce(function(){
                    this.store.loseCondition(3);
                }, 0.5);
            }
            // disable the menu and cancel rejoin popup
        }
    };

    @action
    onMttStateNotifyMsg = (msg:mttPro.MttStateNotifyMsg)=>{
        if( msg.mttId == this.tournamentId )
        {
            cc.vv.ConsoleLog.log('onMttStateNotifyMsg', JSON.stringify(msg));
            if (msg.status == ProtoBuf.mttPro.Mtt_Status.Mtt_Status_pause||msg.status==ProtoBuf.mttPro.Mtt_Status.Mtt_Status_final_pause)
            {
                if (this.roomMessage!==7){
                    this.roomMessage = 3;
                }
            }
            else
            {
                this.roomMessage = 0;
            }
        }
       
    };

    @action
    onVoiceRes = (msg:holdem.VoiceRes) => {
      cc.vv.ConsoleLog.log("on voice res: ", JSON.stringify(msg));
      if (msg.roomId == this.roomId) {
          if (msg.code == 0) {
              if( cc.isValid(this.node) )
              {
                this.node.audioPlayer.playEffect(soundEffect.SendAudio);
              }
              this.node.PlayVoiceMessageAnimation(Translate(Translation.VOICE_MESSAGE.MESSAGE_SUCCESS));
          } else {
              cc.vv.ConsoleLog.log("voice res error: ", msg.code);
              switch (msg.code) {
                  case ProtoBuf.holdem.Code.FinalVoice:
                      this.node.PlayVoiceMessageAnimation(Translate(Translation.VOICE_MESSAGE.ERROR_FINAL_GAME_NO_VOICE_MESSAGE));
                      break;
              }
          }
      }
    };

    @action
    onVoiceMsg = (msg:holdem.VoiceMsg) => {
        cc.vv.ConsoleLog.log("on voice msg:", JSON.stringify(msg));
        if (msg.roomId == this.roomId && msg.userId != this.playerUserId) {
            if( CommonTools.instance.isUrlExecutable(msg.voiceUrl) )
            {
                cc.vv.ConsoleLog.log("holdemRoom onVoiceMsg block js ts", msg.voiceUrl);
                return;
            }
            cc.loader.load({url: msg.voiceUrl, type: 'mp3'}, action((err: any, ret: any) => {
                if (err) {
                    cc.vv.ConsoleLog.log("voice msg load error.", err);
                    return;
                }
                if (ret) {
                    let voiceMsg = this.voiceMessageList[msg.userId];
                    if (voiceMsg) {
                        // stop previous voice msg before release
                        this.node.stopVoiceMessage(msg.userId);

                        let voiceClip = voiceMsg.voiceClip;
                        cc.vv.ConsoleLog.log(`uncache player ${msg.userId} previous voice clip`, voiceMsg.voiceId, voiceClip.nativeUrl);
                        cc.audioEngine.uncache(voiceClip);
                    }
                    voiceMsg = new VoiceMessage();
                    voiceMsg.voiceClip = ret;
                    this.voiceMessageList[msg.userId] = voiceMsg;
                    this.node.playVoiceMessage(msg.userId);
                } else {
                    cc.vv.ConsoleLog.log("voice msg load empty return.");
                }
            }));
        }
    };

    uncacheAllVoiceMessageList() {
        Object.keys(this.voiceMessageList).forEach((userId:number)=>{
            let voiceMsg = this.voiceMessageList[userId];
            if (voiceMsg) {
                let voiceClip = voiceMsg.voiceClip;
                cc.vv.ConsoleLog.log(`uncache player ${userId} voice clip`, voiceMsg.voiceId, voiceClip.nativeUrl);
                cc.audioEngine.stop(voiceMsg.voiceId);
                cc.audioEngine.uncache(voiceClip);
            }
        });
    }

    @action
    onMttExDataMsg = (msg: mttPro.MttExDataMsg) => {
        cc.vv.ConsoleLog.log('onMttExDataMsg', JSON.stringify(msg));
        if(msg.roomId==this.roomId){
            msg.players.forEach((p)=>{
                for(let i in this.seatedPlayers)
                {
                    if( this.seatedPlayers[i].userId == p.userId )
                    {
                        this.seatedPlayers[i].bounty = p.value;
                        this.seatedPlayers[i].leftCoin = p.coinLeft;
                    }
                }
            });
            if (this.blindHands>0 && this.node && msg.LeftBlindHand>0){
                this.riseBlindHand = msg.LeftBlindHand;
                this.node.updateBlindHand((this.checkIsLastBlindLevel())?0:this.riseBlindHand);
            }
        }
    }

    @action
    onSngExDataMsg = (msg: mttPro.SngRoomExDataMsg) => {
        cc.vv.ConsoleLog.log('onMttExDataMsg', JSON.stringify(msg));
        if(msg.roomId==this.roomId){
            if (this.blindHands>0 && this.node && msg.LeftBlindHand>0){
                this.riseBlindHand = msg.LeftBlindHand;
                this.node.updateBlindHand((this.checkIsLastBlindLevel())?0:this.riseBlindHand);
            }
        }
    }

    @action
    protected onMttUserRankMsg = (msg: mttPro.MttUserRankMsg) => {
        
        // if(this.MTTDetail)
        // {
        //     cc.vv.ConsoleLog.log("onMttUserRankMsg this.MTTDetail.TournamentId: "+this.MTTDetail.TournamentId);
        // }
        // cc.vv.ConsoleLog.log("onMttUserRankMsg msg.userId: "+msg.userId);
        // cc.vv.ConsoleLog.log("onMttUserRankMsg this.playerUserId: "+this.playerUserId);

        if(this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT){
            if (this.MTTDetail && msg.mttId == this.tournamentId && msg.userId == this.playerUserId) {
                cc.vv.ConsoleLog.log("onMttUserRankMsg:", JSON.stringify(msg));
                // not update user rank in pkw mtt new ui 20200722
                // Object.keys(this.seatedPlayers).forEach( (seatNum) => {
                //     if (this.seatedPlayers[seatNum].userId == msg.userId) {
                //         this.seatedPlayers[seatNum].mttRank = msg.rank;
                //         this.MTTRank = msg.rank;
                //     }
                // });
                if (this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER || this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.HUNTER){
                    // this.updatePlayerWins(msg.userId, msg.wins, msg.value);
                }
            }
        } else {
            // set avatar ranking label
            if (this.tournamentId == msg.mttId){
                Object.keys(this.seatedPlayers).forEach((seatNum) => {
                    if (this.seatedPlayers[seatNum].userId == msg.userId) {
                        if(this.seatedPlayers[seatNum].leftCoin == 0){
                            this.seatedPlayers[seatNum].SetUserRank(msg.rank);
                        }
                    }
                });
            }
        }
    };

    sendEmojiReq (emojiName:string) {
        this.ws.Send(ProtoBuf.holdem.Emoji.create({roomId: this.roomId, userId: this.playerUserId, body:emojiName}));
    }

    onEmojiMsg = (msg:holdem.Emoji) => {
        cc.vv.ConsoleLog.log(this.roomId, " on Emoji Msg: ", JSON.stringify(msg));
        if (msg.roomId == this.roomId) {
            if (!this.checkEmojiMuteList(msg.userId))
                this.node.startEmoticon(msg);
        }
    };

    onEmojiRes = (msg:holdem.EmojiRes)=>{
        cc.vv.ConsoleLog.log(this.roomId, " on Emoji Res: ", JSON.stringify(msg));
        if (msg.roomId==this.roomId){
            if (msg.code){
                switch (msg.code) {
                    // Not Enough Coin
                    case ProtoBuf.holdem.Code.LESS_COIN:
                        this.node.setNotificationMsg(Translate(Translation.ICON_MESSAGE.NOT_ENOUGH_COIN));
                        break;
                    case ProtoBuf.holdem.Code.Emoje_Frequent:
                        this.node.setNotificationMsg(Translate(Translation.ICON_MESSAGE.TOO_FREQUENT));
                        break;
                }
            }else{
                if (cc.vv.DataManager.webPlatform == ProtoBuf.commonProto.PLATFORM.PKW) {
                    this.emojiTimes++;
                }
            }
        }
    };

    sendAnimReq (animName:string, targetUserId?:number) {
        let inputData = {};
        if (targetUserId) {
            inputData = {roomId:this.roomId, anim:animName, targetUser: targetUserId};
        } else {
            inputData = {roomId:this.roomId, anim:animName};
        }
        this.ws.Send(ProtoBuf.holdem.AnimReq.create(inputData));
    }

    @action
    onAnimMsg =  (msg:holdem.AnimMsg) => {
        cc.vv.ConsoleLog.log(this.roomId, "on animation msg", JSON.stringify(msg));
        if(!this.isAppPause){
            if (msg.roomId == this.roomId) {
                if (!this.checkEmojiMuteList(msg.sender))
                    this.node.startIconAttack(msg.anim, msg.sender, msg.targetUser);
            }
        }

    };

    onAnimRes = (msg:holdem.AnimRes) => {
        cc.vv.ConsoleLog.log("animation response", JSON.stringify(msg));
        if (msg.roomId == this.roomId) {
            if (!msg.code) {
                this.anmiTimes++;
            } else {
                cc.vv.ConsoleLog.log("Animation request error: " + msg.code);
                switch (msg.code) {
                    // Not Enough Coin
                    case ProtoBuf.holdem.Code.LESS_COIN:
                        this.node.setNotificationMsg(Translate(Translation.ICON_MESSAGE.NOT_ENOUGH_COIN));
                        break;
                    case ProtoBuf.holdem.Code.Emoje_Frequent:
                        this.node.setNotificationMsg(Translate(Translation.ICON_MESSAGE.TOO_FREQUENT));
                        break;
                    default:
                        let errorMsg = Translate(Translation.ERROR_CODE_PKW[msg.code]);
                        if (errorMsg) {
                            this.node.setNotificationMsg(errorMsg);
                        }
                        break;
                }
            }
        }
    };

    @action
    onRedPocketCarouseMsg = (msg:holdem.RedPocketCarouseMsg) => {
        if (msg) {
            let remapMsg = {
                EventId: msg.eventId,
                Amount: msg.amount,
                Format: msg.format,
                Nickname: msg.nickname,
                Category: msg.category,
                UserId: msg.UserId,
                GameId: msg.GameId,
                ToolName: msg.ToolName
            };
            cc.vv.ConsoleLog.log("onRedPocketCarouseMsg Received",remapMsg);
            this.onBroadcastRedPocketCarousel(remapMsg);
        } else {
            cc.vv.ConsoleLog.log("onRedPocketCarouseMsg Error");
        }
    };

    sendMttUserInfoReq (userId:number) {
        this.ws.Send(ProtoBuf.mttPro.MttUserInfoReq.create({mttId: this.tournamentId, userId: userId}));
    }

    onMttUserInfoRes = (msg: mttPro.MttUserInfoRes) => {
        cc.vv.ConsoleLog.log("Mtt User Info Res", JSON.stringify(msg));
        if (msg.mttId == this.tournamentId) {
            // if (msg.userId == this.playerUserId) {
            //     if (cc.isValid(this.node.selfSettingNode)){
            //         this.node.selfSettingNode.getComponent(Holdem_SelfSetting).hunterBubble.getComponent(HunterValueBlock).updateHunterValue(msg);
            //         this.node.selfSettingNode.getComponent(Holdem_SelfSetting).hunterBubble.active = true;
            //     }
            // } else {
            //     cc.vv.ConsoleLog.log('onMttUserInfoRes check isvalid',cc.isValid(this.node.playerSettingNode), cc.isValid(this.node.playerSettingNode)?this.node.playerSettingNode.active:'invalid');
            //     if (cc.isValid(this.node.playerSettingNode)){
            //         this.node.playerSettingNode.getComponent(ppConfig.PlayerSetting).hunterBubble.getComponent(HunterValueBlock).updateHunterValue(msg);
            //         this.node.playerSettingNode.getComponent(ppConfig.PlayerSetting).hunterBubble.active = true;
            //     }
            // }
            if (this.tournamentMode == HOLDEM_TOURNAMENT_TYPE.MTT && this.MTTDetail && this.MTTDetail.TournamentMode == ProtoBuf.commonProto.TOURNAMENT_MODE.SUPER_HUNTER)
            {
                // this.updatePlayerWins(msg.userId, msg.wins, msg.value);
            }
        }
    };

    sendMttUserGameSumInfoReq (userId:number){
        this.ws.Send(ProtoBuf.mttPro.MttUserGameSumInfoReq.create({mttId: this.tournamentId, userId: userId}));
    }

    onMTTUserGameSumInfoRes = (msg:mttPro.MttUserGameSumInfoRes) => {
        cc.vv.ConsoleLog.log("onMTTUserGameSumInfoRes", JSON.stringify(msg));
        if (msg.mttId == this.tournamentId){
            if (cc.isValid(this.node)){
                this.node.updatePlayerGameSumInfo(msg,(msg.userId == this.playerUserId));
            }
        }
    }

    @action
    onMttRebuyMsg = (msg:mttPro.MttRebuyMsg) => {
        cc.vv.ConsoleLog.log("onMttRebuyMsg", JSON.stringify(msg));
        if (msg.roomId == this.roomId) {
            LoadingBlocker.instance.hide("All");    // #28240
            this.rebuyLeftTime = msg.leftTime;
            this.isRebuyAllow = msg.allow;
            this.rebuyEndTime = this.getEndTimestamp(msg.leftTime);
        }
    }

    @action
    onMttMorebuyMsg = (msg:mttPro.MttMorebuyMsg) => {
        cc.vv.ConsoleLog.log("onMttMorebuyMsg", JSON.stringify(msg));
        if (msg.roomId == this.roomId || (!msg.roomId && msg.mttId == this.tournamentId)) {
            LoadingBlocker.instance.hide("All");    // #28240
            this.morebuyLeftTime = msg.leftTime;
            this.isMorebuyAllow = msg.allow;
            this.morebuyEndTime = this.getEndTimestamp(msg.leftTime);
        }
    }

    MttCancelBuyReq(typeId:number) {
        cc.vv.ConsoleLog.log("MttCancelBuyReq typeId: ", typeId);
        this.ws.Send(ProtoBuf.mttPro.MttCancelBuyReq.create({typeId: typeId, mttId: this.tournamentId}));
    }


    onMttCancelBuyRes = (msg:mttPro.MttCancelBuyRes) => {
        cc.vv.ConsoleLog.log("onMttCancelBuyRes", JSON.stringify(msg));
    }

    @action
    updatePlayerWins(userId:number, wins:number, bounty:number){
        //Update Super Hunter MTT players icon
        cc.vv.ConsoleLog.log("refreshMttSuperHunterPlayerIcon", userId, wins, bounty);
        for(let i in this.seatedPlayers)
        {
            if( this.seatedPlayers[i].userId == userId )
            {
                // this.seatedPlayers[i].wins = wins;
                this.seatedPlayers[i].bounty = bounty;
            }
        }
    }

    updateMuteList (userId:number) {
        var idIndex = this.muteList.indexOf(userId);
        if (idIndex >= 0) {
            this.muteList.splice(idIndex, 1);
            cc.vv.ConsoleLog.log("Splice mute list: ", this.muteList);
        } else {
            this.muteList.push(userId);
            cc.vv.ConsoleLog.log("Push mute list: ", this.muteList);
        }
        MTTConnector.instance.setStorage(localConfig.key_muteList, JSON.stringify(this.muteList), false);
    }

    checkMuteList (userId:number) {
        let isMuted = this.muteList.indexOf(userId) >= 0;
        cc.vv.ConsoleLog.log("voice muted", userId, isMuted);
        return isMuted;
    }

    updateEmojiMuteList(userId:number) {
        var idIndex = this.emojiMuteList.indexOf(userId);
        if (idIndex >= 0) {
            this.emojiMuteList.splice(idIndex, 1);
            cc.vv.ConsoleLog.log("Splice emoji mute list: ", this.emojiMuteList);
        } else {
            this.emojiMuteList.push(userId);
            cc.vv.ConsoleLog.log("Push emoji mute list: ", this.emojiMuteList);
        }
        MTTConnector.instance.setStorage(localConfig.key_emojiMuteList, JSON.stringify(this.emojiMuteList), false);
    }

    checkEmojiMuteList (userId:number) {
        let isMuted = this.emojiMuteList.indexOf(userId) >= 0;
        cc.vv.ConsoleLog.log("emoji muted", userId, isMuted);
        return isMuted;
    }

    updateCameraMuteList (userId:number) {
        var idIndex = this.cameraMuteList.indexOf(userId);
        if (idIndex >= 0) {
            this.cameraMuteList.splice(idIndex, 1);
            cc.vv.ConsoleLog.log("Splice camera mute list: ", this.cameraMuteList);
        } else {
            this.cameraMuteList.push(userId);
            cc.vv.ConsoleLog.log("Push camera mute list: ", this.cameraMuteList);
        }
        MTTConnector.instance.setStorage(localConfig.key_cameraMuteList, JSON.stringify(this.cameraMuteList), false);
    }

    checkCameraMuteList (userId:number) {
        let isMuted = this.cameraMuteList.indexOf(userId) >= 0;
        cc.vv.ConsoleLog.log("camera muted", userId, isMuted);
        return isMuted
    }

    closeAllPopUp(){
        // reset Auto play flag
        this._autoPlayAlert = false;
        cc.vv.ConsoleLog.log('** UI CLOSE ALL POP UP');
        if(this.node && this.node.dialogController)
        {
            this.node.dialogController.hideAllDialogBox();
        }
        else
        {
            if(cc.vv.DataManager.popUps.length>0){
                for(let one of cc.vv.DataManager.popUps){
                    if(one instanceof cc.Node){one.destroy();}
                }
                cc.vv.DataManager.popUps = [];
            }
        }

        if (this.node) {
            if (cc.isValid(this.node.celebritySelfSettingNode)) {
                this.node.celebritySelfSettingNode.getComponent(CelebritySelfSetting).onClose();
            }
        }
    }


    getSeatedPlayerByUserId (userId:number) {
        let playerSeatNum = Object.keys(this.seatedPlayers).find(seatNum => {
            return this.seatedPlayers[seatNum].userId == userId;
        });
        if (playerSeatNum) {
            return this.seatedPlayers[playerSeatNum];
        } else {
            return undefined;
        }
    }

    loseCondition(state:number){
        // state 0=mtt_rejoin, 1 = reward, 2 = game_end
        if(MultipleGame.instance && cc.isValid(this.node))
        {
            MultipleGame.instance._multipleGameTabButtons[this.node.multipleGameIndex].setCards([]);
        }
        for (const i in this.loseState){
            cc.vv.ConsoleLog.log(2634, 'loseCondition',i, state, this.loseState, this.loseState[i]);
            if (this.loseState[i]){
                if (Number(i) <= state){
                    cc.vv.ConsoleLog.log(2638, 'loseCondition', Number(i), state);
                    if (Number(i) == 3) {
                        if (this.roomId == this.mttEndRoomId){
                            this.showLosePopUp(Number(i));
                        }
                    }else{
                        this.showLosePopUp(Number(i));
                    }
                }
                return;
            }
        }
    }

    showLosePopUp(state:number){
        let outputMsg = ['mtt_rejoin', 'reward', 'game_end'];

        if (this.loseFlowPopup[state]!==undefined){
            cc.vv.ConsoleLog.log('lose flow debug open', outputMsg[state], this.loseFlowPopup);
            this.tryCounter =0;
            this.loseFlowPopup[state].active = true;
        } else{
            cc.vv.ConsoleLog.log('lose flow debug ->', outputMsg[state], ' empty');
            if (state==3){
                this.closeAllPopUp();
                this.callPopUpBox(Translate(Translation.ERROR_CODE_PKW['60081']),()=>{
                    this.reboundGameView();
                });
            }else if(state==2){
                cc.vv.ConsoleLog.log('skip game_end popup');
                this.closeAllPopUp();
                this.callPopUpBox(Translate(Translation.ERROR_CODE_PKW['60076']),()=>{
                    this.reboundGameView(this.isGameEnd);
                });
            }
            else {
                try {
                    if (this.tryCounter<4){
                        this.tryCounter++;
                        this.node.scheduleOnce(()=>{this.showLosePopUp(state)},0.1);
                        // setTimeout(()=>{this.showLosePopUp(state)},100);
                    }
                }catch (e) {
                    cc.vv.ConsoleLog.log('lose flow debug -> error',e);
                }
            }

        }

    }

    msgErrorDialog(errorType:number, errorCode:number, callback: Function) {
        let errorMsg = "";
        switch (errorType) {
            case globalConfig.ERROR_TYPE.MSG_ERROR:
                errorMsg = Translate("ERROR_CODE_PKW." + errorCode);
                break;
            case globalConfig.ERROR_TYPE.NETWORK_ERROR:
                errorMsg = Translate(Translation.MESSAGE_DIALOG_BLOCKER.NETWORK_ERROR) + ` (${errorCode})`;
                break;
        }
        this.showDialogBox(
            Translate(Translation.ERROR_CODE_PKW.TITLE),
            errorMsg,
            false,
            [
                {
                    type: 0,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.CANCEL),
                    callback: callback,
                },
            ]
        );
    }

    reboundGameView(isEnd:boolean = false, checkCloseTab:boolean = true, multipleGameNeedBackToHall:boolean = true)
    {
        cc.vv.ConsoleLog.log("reboundGameView",isEnd,this.tournamentId);
        if( !this.hasCallReboundGameView )
        {
            this.hasCallReboundGameView = true;
            // cc.vv.ConsoleLog.log("reboundGameView", MultipleGame.instance.shouldShowHeader());
            // if(MultipleGame.instance && MultipleGame.instance.shouldShowHeader())
            // {
            //     MultipleGame.instance.endGame(this.node.multipleGameIndex);
            // }
            // else
            // {
            cc.vv.DataManager.isEndGame = isEnd;
            cc.vv.DataManager.currentResultId = this.tournamentId;

            // if( this.node )
            // {
            //     this.node.initShowLoading();
            // }
            if( MultipleGame.instance && checkCloseTab && (!multipleGameNeedBackToHall || MultipleGame.instance.shouldShowHeader()) )
            {
                MultipleGame.instance.exitGame(this.node.multipleGameIndex);
            }
            else if(MultipleGame.instance && this.isReplay)
            {
                MultipleGame.instance.exitReplayGame();
            }
            else
            {
                if( !this.isReplay )
                {
                    // this.ws.RemoveMessageHandler(owWebSocket.EVENT_ID.ON_CLOSE, this.onLostConnection);
                    if( this.ws )
                    {
                        this.ws.close(true, true);
                    }
                    WorldWebSocket.gameSocket = null;
                    // cc.vv.ConsoleLog.log(this.ws.isConnected(), this.ws);
                }
    
                cc.vv.DataManager.currentRoomID = 0;
                
                let hallScene = globalConfig.SCENE_NAME.HALL;
                
                if( this.node.audioPlayer )
                {
                    this.node.audioPlayer.playEffect(soundEffect.PlayerExit);
                }

                this.node.scheduleOnce(()=>{
                    cc.vv.AssetsManager.loadScene(hallScene, ()=>{
                        // if( this.node )
                        // {
                        //     this.node.initHideLoading();
                        // }
                        this.hasCallReboundGameView = false;
                    });
                },0.3);
            }
            
            // }
            
        }
        
        
    }

    updateToolList(callback?:()=>any, isRecord: boolean = false)
    {
        cc.vv.DataManager.updateToolList(callback,isRecord);
        // let toolList:number[] = [];
        // // if(WorldWebSocket.checkNetwork("requestToolsInBackpackRequest")) return;
        // cc.vv.DataManager.worldNetwork.requestToolsInBackpackRequest((msg:commonProto.User_Tool_In_Backpacks_Response)=>{
        //     for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)){
        //         cc.vv.ConsoleLog.log(myTools);
        //         toolList.push(myTools.ToolId);
        //     }
        //     if (toolList.length>0){
        //         let toolData = {ToolIds:toolList};
        //         this.getToolsInfo(toolData, (msgT: commonProto.Tool_Info_Response) => {
        //             let myBackPack:any[] = [];
        //             for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)){
        //                 let infoS = msgT.ToolInfos.find((tool:any)=>{return tool.Id==myTools.ToolId;});
        //                 cc.vv.ConsoleLog.log('test ->', infoS);
        //                 let myBackPackT:any = myTools;
        //                 myBackPackT.Created = infoS.Created;
        //                 myBackPackT.Value = infoS.Value;
        //                 myBackPackT.IconUrl = infoS.IconUrl;
        //                 myBackPackT.Name = infoS.Name;
        //                 myBackPackT.SellRatio = infoS.SellRatio;
        //                 myBackPackT.Type = infoS.Type;
        //                 myBackPackT.Description = infoS.Description;
        //                 myBackPackT.Config = infoS.Config?JSON.parse(infoS.Config):{ForCategory:[],Aof:false};
        //                 myBackPackT.Config.ForCategory.forEach((element:any,index:number,array:any[])=>{array[index] = Number(element);});
        //                 // cc.vv.ConsoleLog.log("back pack",myBackPackT.Config);
        //                 myBackPack.push(myBackPackT);
        //             }
        //             if(isRecord) {
        //                 cc.vv.DataManager.backPackToolRecord = myBackPack;
        //             } else {
        //                 cc.vv.DataManager.backPackTool = myBackPack;
        //             }
        //             if(callback){
        //                 callback();
        //             }
        //         });
        //     }
        //     else{
        //         if(callback){
        //             callback();
        //         }
        //     }
        // });
    }

    updateUserInfo(data:any){
        cc.vv.DataManager.userData = data.UserData;
    }

    repeatLoginDialog(msg:commonProto.User_Logout_Response) {
        // WorldWebSocket.forceClose();
        if( WorldWebSocket.hasInstance() )
        {
            WorldWebSocket.getInstance().close(true, true);
        }
        if( this.ws )
        {
            this.ws.close(true, true);
        }
        WorldWebSocket.gameSocket = null;
        if( !WorldWebSocket.hasShowLoginFailDialog && msg.ErrorCode != 20 )
        {
            WorldWebSocket.hasShowLoginFailDialog = true;
            // let hint = (msg&&msg.ErrorCode)? Translate("ERROR_CODE_PKW."+msg.ErrorCode) : Translate(Translation.GAME.OTHER_LOGIN);

            this.showDialogBox(
                Translate(Translation.ERROR_CODE_PKW.TITLE),
                Translate("ERROR_CODE_PKW."+msg.ErrorCode),
                false, [
                    {
                        type: 0,
                        text:Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                        callback: () => {
                            WorldWebSocket.hasShowLoginFailDialog = false;
                            WorldWebSocket.logout();
                            // cc.vv.DataManager.worldNetwork.responseLogout();
                        },
                    },
                ]
            );

            // Fix widget do not update if cc.director is paused so that dialog is not aligned correctly in Replay
            if( cc.director.isPaused() )
            {
                cc.director.resume();
            }
        }
    }

    getMttHallInfo()
    {
        if(Holdem_View.instance)
        {
            return Holdem_View.instance.mttHallInfo;
        }
        let mttHall = cc.director.getScene().getComponentInChildren(MttHall);
        if( mttHall )
        {
            return mttHall._mttHallInfo;
        }
        return null;
    }

    broadcastMessageEnvelope(msg:commonProto.Broadcast_Message_Envelope)
    {
        if(MultipleGame.instance)
        {
            return;
        }
        // msg = new ProtoBuf.commonProto.Broadcast_Message_Envelope;
        // msg.Body = new Uint8Array([8, 1, 16, 2, 40, 149, 24, 50, 10, 67, 101, 110, 100, 111, 32, 84, 101, 115, 116, 56, 3,]);
        // msg.TypeId = 1000;
        
        // ["0":8,"1":1,"2":16,"3":2,"4":40,"5":210,"6":30,"7":50,"8":10,"9":67,"10":101,"11":110,"12":100,"13":111,"14":32,"15":84,"16":101,"17":115,"18":116,"19":56,"20":3]
        // cc.vv.ConsoleLog.log("broadcastMessageEnvelope", JSON.stringify(msg));
        // let message = {
        //     type: msg.TypeId,
        //     messageType: msg.MessageType,
        //     state: globalConfig.messageState.unread,
        //     time: new Date(),
        //     title: msg.Title,
        //     body: {},
        // };
        // let mttMsg;
        switch(msg.TypeId){
            case 0:
                // message.body = ProtoBuf.commonProto.Broadcast_Message_Plain.decode(msg.Body);
                break;
            case 1:
                // message.body = ProtoBuf.commonProto.Broadcast_Message_Link.decode(msg.Body);
                break;
            case 2://Hot update
                // const msgHotUpdate = ProtoBuf.commonProto.Broadcast_Message_Hot_Update_Needed.decode(msg.Body);
                break;
            case 1000:
                const msgEnterGame = ProtoBuf.commonProto.Broadcast_Message_Enter_Game.decode(msg.Body);
                // cc.vv.ConsoleLog.log("msgEnterGame",msgEnterGame);
                let playCurrent = this.getGameStartReminderPlayCurrent(msgEnterGame);
                if( msgEnterGame.TypeId == ProtoBuf.commonProto.GAME_CATEGORY.MTT )
                {
                    httpApis.requestMttTournamentStatus(msgEnterGame.TournamentId, (data:any)=>{
                        cc.vv.ConsoleLog.log("broadcastMessageEnvelope", data);
                        if( data.Status == ProtoBuf.commonProto.MTT_GAME_STATUS.STARTED || data.Status == ProtoBuf.commonProto.MTT_GAME_STATUS.STOP_SIGNUP )
                        {
                            this.node.gameReminderPrefab.data.getComponent("GameStartReminder").reachTimeAlarm(playCurrent, msgEnterGame, this.node.gameReminderParent, this.node.node.parent.height, this.onGameStartReminderEnterClickAction);
                        }
                    }, ()=>{
                        cc.vv.ConsoleLog.log("broadcastMessageEnvelope error");
                    });
                }
                else
                {
                    this.node.gameReminderPrefab.data.getComponent("GameStartReminder").reachTimeAlarm(playCurrent, msgEnterGame, this.node.gameReminderParent, this.node.node.parent.height, this.onGameStartReminderEnterClickAction);
                }
                // message.body = msgEnterGame;
                break;
            case 1002:
                const msgOutGame = ProtoBuf.commonProto.Broadcast_Message_Out_Game.decode(msg.Body);
                cc.vv.ConsoleLog.log("broadcast:msgOutGame",msgOutGame.TournamentId,msgOutGame.JoinedStatus);
                let idx = cc.vv.DataManager.JoinedTournaments.findIndex((object:any)=>object.TournamentId ==msgOutGame.TournamentId);
                if(idx>=0){
                    cc.vv.DataManager.JoinedTournaments[idx].JoinStatus = msgOutGame.JoinedStatus;
                    cc.vv.ConsoleLog.log("check joined",idx,cc.vv.DataManager.JoinedTournaments[idx].JoinStatus);
                }
                else{
                    MttHall.prototype.addJoinedTournaments(msgOutGame.TournamentId,{joinStatus: msgOutGame.JoinedStatus});
                }
                // if(mttHallScript.node.active&&(mttHallScript._mttHallInfo.TournamentId == msgOutGame.TournamentId)){
                //     mttHallScript.updateMyJoinStatus();
                //     mttHallScript.updateSignUpButton();
                // }
                break;
            case 1003:
                // mttMsg = ProtoBuf.commonProto.Broadcast_Message_Mtt_Status_Changed_Notice.decode(msg.Body);
                // message.body = mttMsg;
                // if(mttHallScript.node.active&&(mttHallScript._mttHallInfo.TournamentId == mttMsg.TournamentId)){
                //     mttHallScript.removeJoinedTournaments(mttMsg.TournamentId);
                //     mttHallScript.updateMyJoinStatus();
                //     // mttHallScript.updateSignUpButton();
                //     let hints =  Translate(Translation.ERROR_CODE["31004"]);
                //     mttHallScript.callPopUpBox(hints,()=>{
                //         mttHallScript.onReturnClicked();
                //     });
                // }
                break;
            case 1004:
                // mttMsg = ProtoBuf.commonProto.Broadcast_Message_End_Game.decode(msg.Body);
                // message.body = mttMsg;
                // if(mttHallScript.node.active&&(mttHallScript._mttHallInfo.TournamentId == mttMsg.TournamentId)){
                //     mttHallScript.removeJoinedTournaments(mttMsg.TournamentId);
                //     mttHallScript.updateMyJoinStatus();
                //     let hints =  Translate(Translation.ERROR_CODE["31001"]);
                //     mttHallScript.callPopUpBox(hints,()=>{
                //         mttHallScript.onReturnClicked();
                //     });
                // }
                break;
            case 1005:
                // message.body = ProtoBuf.commonProto.Broadcast_Message_End_Game.decode(msg.Body);
                break;
            case 2000:
                let newEvent = ProtoBuf.commonProto.Broadcast_Message_Events_Updated.decode(msg.Body);
                ActivityPrefab.handleEventBroadcast(newEvent.UpdatedEvent);
                break;
            case 3000:
                let redPocketNotice = ProtoBuf.commonProto.Broadcast_Message_Events_RED_POCKET_NOTICE.decode(msg.Body);
                cc.vv.ConsoleLog.log("holdem redPocketNotice", redPocketNotice);
                cc.vv.ConsoleLog.log("$$$$$$$", redPocketNotice.Amount);
                // popup
                // 每個紅包活動只會有背包或金幣, 不會同時有
                let redPocketPrize = "";
                redPocketPrize = redPocketNotice.Amount.toString();

                if(redPocketNotice.ToolName != ""){
                    redPocketPrize = redPocketNotice.ToolName;
                }

                let approved = redPocketNotice.Approved;
                let recordId = redPocketNotice.RecordId;

                // if(this.MTTDetail){
                //     let mttRegFee = redPocketNotice.MttRegFee;
                //     if(mttRegFee == this.MTTDetail.RegFee){
                //         this.setRedPocketDialog(redPocketPrize,approved,recordId);
                //     }
                // } else {
                this.setRedPocketDialog(redPocketPrize,approved,recordId);
                // }
                break;
            case 3001:
                let redPocketCarousel = ProtoBuf.commonProto.Broadcast_Message_Events_RED_POCKET_CAROUSEL.decode(msg.Body);
                cc.vv.ConsoleLog.log("holdem redPocketCarousel", redPocketCarousel);
                if( !this.isReplay )
                {
                    this.onBroadcastRedPocketCarousel(redPocketCarousel);
                }
                break;
            case 3500:
                let gotToolMsg = ProtoBuf.commonProto.Broadcast_Message_Got_Tool.decode(msg.Body);
                cc.vv.ConsoleLog.log("gotToolMsg(holdemRoom)~~~ ", gotToolMsg);
                cc.vv.DataManager.newBagsCount+=gotToolMsg.Qty;
                break;
            case 4000:
                let self = this;
                this.callPopUpBox(Translate("PERSONAL_SETTING.GENERAL.DIALOG.REVIEW_REJECT.FORMAT").replace("{0}", Translate(`PERSONAL_SETTING.GENERAL.DIALOG.REVIEW_REJECT.${msg.Title.toUpperCase()}`)).replace("{1}", Translate(`PERSONAL_SETTING.GENERAL.DIALOG.REVIEW_REJECT.${msg.Title.toUpperCase()}.ACTION`)), () => {
                    cc.sys.localStorage.setItem(`${cc.vv.DataManager.userId}_${msg.Title.toUpperCase()}_REJECT`, new Date().getTime());
                    if(msg.Title.toUpperCase() === "AVATAR") {
                        SystemAvatar.changeSystemAvatar(-1).then((result: any) => {
                            if(result.status) {
                                for(let playerSeatNum in self.seatedPlayers) {
                                    if(self.seatedPlayers[playerSeatNum].userId == cc.vv.DataManager.userId) {
                                        self.node._players[parseInt(playerSeatNum) - 1].avatar.load(cc.vv.AssetsManager.getAvatarUrl(cc.vv.DataManager.avatarId, {isThumbnails: true, timestamp: Date.now()}));
                                    }
                                }
                            }
                        });
                    }
                });
                break;
            case 4500:
                let pushCarousel = ProtoBuf.commonProto.Broadcast_Message_Push_Carousel.decode(msg.Body);
                this.setMarqueeMsg(pushCarousel.Body);
                break;

            // default:
            //     cc.vv.ConsoleLog.log("broadcast message", msg);
            //     message.body = msg.Body;
            //     break;
        }
    }

    onBroadcastRedPocketCarousel(redPocketCarousel:any){
        if( !this.isReplay )
        {
            // 走馬燈
            // let replaceStr = redPocketCarousel.Format;
            // replaceStr = replaceStr.replace("%name%", redPocketCarousel.Nickname);
            // replaceStr = replaceStr.replace("%coin%", redPocketCarousel.Amount ? redPocketCarousel.Amount.toString() : "");
            // replaceStr = replaceStr.replace("%tool%", redPocketCarousel.ToolName ? redPocketCarousel.ToolName : "");
            //
            //
            // switch (redPocketCarousel.Category) {
            //     case ProtoBuf.commonProto.GAME_CATEGORY.GEN_PP:
            //     case ProtoBuf.commonProto.GAME_CATEGORY.LOOP_PP:
            //         replaceStr = replaceStr.replace("%game%", Translate(Translation.GAME_LIST.PINEAPPLE));
            //         break;
            //
            //     case ProtoBuf.commonProto.GAME_CATEGORY.MTT:
            //         replaceStr = replaceStr.replace("%game%", "MTT");
            //         break;
            //
            //     case ProtoBuf.commonProto.GAME_CATEGORY.SNG:
            //     case ProtoBuf.commonProto.GAME_CATEGORY.SNG_AOF:
            //         replaceStr = replaceStr.replace("%game%", Translate(Translation.GAME_LIST.TEXAS));
            //         break;
            //
            //     default:
            //         replaceStr = replaceStr.replace("%game%", "");
            //         break;
            // }
            let replaceStr = Marquee.prototype.getMsgContent(redPocketCarousel);
            this.setMarqueeMsg(replaceStr);
        }

    }

    getGameStartReminderPlayCurrent(msgEnterGame:any)
    {
        let playCurrent:boolean = false;
        if(this.tournamentId){
            playCurrent = (this.tournamentId == msgEnterGame.TournamentId);
        }
        return playCurrent;
    }
    onGameStartReminderEnterClickAction = (msgEnterGame:any)=>
    {
        // const handleLoadingTimeout = ()=>{
        //     this.node.initHideLoading();
        // };
        // this.node.initShowLoading(handleLoadingTimeout);
        // hallScript.currentGamePageIdx = 0;
        let playCurrent = this.getGameStartReminderPlayCurrent(msgEnterGame);
        cc.vv.ConsoleLog.log("msgEnterGame.TypeId", msgEnterGame);
        if(!playCurrent){
            switch(msgEnterGame.TypeId){
                case(0):
                    this.gameViewLoader.callGameView(ProtoBuf.commonProto.MTT_GAME_MODE.PP, globalConfig.GAME_LEVEL_LIST_ID.PINEAPPLE_NORMAL, msgEnterGame.RoomId);
                    break;
                case(1):
                    this.gameViewLoader.callGameView(msgEnterGame.GameMode, globalConfig.GAME_LEVEL_LIST_ID.MTT, msgEnterGame.TournamentId);
                    break;
                case(2):
                    if(msgEnterGame.IsAof) {
                        this.gameViewLoader.callGameView(msgEnterGame.GameMode, globalConfig.GAME_LEVEL_LIST_ID.AOF, msgEnterGame.TournamentId);
                    } else {
                        this.gameViewLoader.callGameView(msgEnterGame.GameMode, globalConfig.GAME_LEVEL_LIST_ID.SNG, msgEnterGame.TournamentId);
                    }
                    break;
            }
        }
        // else{
        //     this.node.initHideLoading();
        // }
    }
    setHoldemViewMttHallInfo(gameMode:number, tempMttHallInfo:commonProto.MttTournamentDetail)
    {
        if( gameMode == globalConfig.GAME_LEVEL_LIST_ID.MTT )
        {
            let loadedScene:cc.Scene = cc.director.getScene();
            let sceneView:Holdem_View = loadedScene.getComponentInChildren(Holdem_View);
            if( sceneView )
            {
                sceneView.setMttHallInfo(tempMttHallInfo);
            }
        }
    }
    // getLevelList(inputData: {}, callback: Function, errcb: Function = undefined) {
    //     // if(WorldWebSocket.checkNetwork("requestLevelList")) return;
    //     cc.vv.DataManager.worldNetwork.requestLevelList(inputData, (msg: any) => {
    //         if (msg && !msg.ErrorCode) {
    //             cc.vv.DataManager.GAME_LEVEL_LIST = msg;
    //             callback();
    //         } else {
    //             this.msgErrorDialog(msg, errcb);
    //         }
    //     });
    //     // if(this.blockLayer.active) {
    //     //     this.blockLayer.active = false;
    //     // }
    // }
    // callGameView(gameType:commonProto.MTT_GAME_MODE,gameMode:number,tupleInfo:commonProto.PP_Level|commonProto.MttTournament|commonProto.SngTournament|commonProto.Holdem_Level,mttReviewTableId:number = 0,mttReviewPlayerId:number = 0){
    //     if( !this.hasCallGameView )
    //     {
    //         this.hasCallGameView = true;
    //         let inputData = {
    //             ListId: gameMode
    //         };
    //         this.getLevelList(inputData, () => {
    //             let gameNameId = gameMode;
    //             switch(gameMode) {
    //                 case globalConfig.GAME_LEVEL_LIST_ID.PINEAPPLE_NORMAL:
    //                 case globalConfig.GAME_LEVEL_LIST_ID.PINEAPPLE_ROLL:
    //                 case globalConfig.GAME_LEVEL_LIST_ID.MTT:
    //                     gameNameId -= 1;
    //                     break;
    //                 case globalConfig.GAME_LEVEL_LIST_ID.SNG:
    //                 case globalConfig.GAME_LEVEL_LIST_ID.AOF:
    //                     gameNameId = gameMode === globalConfig.GAME_LEVEL_LIST_ID.SNG ? 4 : globalConfig.GAME_LEVEL_LIST_ID.AOF;
    //                     break;
    //             }
    //             let list = cc.vv.DataManager.GAME_LEVEL_LIST[globalConfig.localLevelListNameReference[gameNameId]];
    //             let info: commonProto.PP_Level | commonProto.MttTournament | commonProto.SngTournament | commonProto.Holdem_Level = null;
    //             for(let i = 0; gameMode !== globalConfig.GAME_LEVEL_LIST_ID.HOLDEM_NORMAL && i < list.length; i++) {
    //                 // @ts-ignore
    //                 if((tupleInfo.Level && tupleInfo.Level === list[i].Level) || (tupleInfo === list[i].Level) ||
    //                     // @ts-ignore
    //                     (tupleInfo.RoomId && tupleInfo.RoomId === list[i].RoomId) || (tupleInfo === list[i].RoomId) ||
    //                     // @ts-ignore
    //                     (tupleInfo.Detail && tupleInfo.Detail.Id === list[i].Detail.Id) || (list[i].Detail && tupleInfo === list[i].Detail.Id)) {
    //                     info = list[i];
    //                     break;
    //                 }
    //             }
    //             cc.vv.ConsoleLog.log("tupleInfo",tupleInfo, info);
    //             if(info || gameMode === globalConfig.GAME_LEVEL_LIST_ID.HOLDEM_NORMAL) {
    //                 //this.blockLayer.active = true;
    //                 // this.showLoading();
    //                 cc.vv.DataManager.currentGamePageIdx = gameMode;
    //                 cc.vv.DataManager.gameMode = gameMode;
    //                 cc.vv.DataManager.currentGameInfo = info === null ? tupleInfo : info;
    //                 cc.vv.DataManager.mttReviewTableId = mttReviewTableId;
    //                 cc.vv.DataManager.mttReviewPlayerId = mttReviewPlayerId;
    //                 let type: commonProto.MTT_GAME_MODE;
    //                 let tempMttHallInfo:commonProto.MttTournamentDetail = null;
    //
    //                 switch(gameMode) {
    //                     case globalConfig.GAME_LEVEL_LIST_ID.PINEAPPLE_NORMAL:
    //                     case globalConfig.GAME_LEVEL_LIST_ID.PINEAPPLE_ROLL:
    //                         type = ProtoBuf.commonProto.MTT_GAME_MODE.PP;
    //                         break;
    //                     case globalConfig.GAME_LEVEL_LIST_ID.SNG:
    //                     case globalConfig.GAME_LEVEL_LIST_ID.AOF:
    //                     case globalConfig.GAME_LEVEL_LIST_ID.MTT:
    //                         // @ts-ignore
    //                         type = info.Detail.GameMode;
    //                         break;
    //                     case globalConfig.GAME_LEVEL_LIST_ID.HOLDEM_NORMAL:
    //                         type = gameType;
    //                         break;
    //                 }
    //                 switch (type) {
    //                     case ProtoBuf.commonProto.MTT_GAME_MODE.PP:
    //                         // require(ppConfig.PineappleGame).resetGameRoomState();
    //                         cc.vv.DataManager.gameViewType = ProtoBuf.commonProto.MTT_GAME_MODE.PP;
    //                         // this.newGameView = cc.instantiate(this.pineGameViewPrefab);
    //                         // this.newGameView.parent = this.layers[5];
    //                         // this.newGameView.setPosition(this.position[2].position);
    //                         // this.newGameView.active = true;
    //
    //                         cc.vv.AssetsManager.loadScene("pineappleGame");
    //
    //
    //
    //
    //                         break;
    //                     case ProtoBuf.commonProto.MTT_GAME_MODE.NLH:
    //                         cc.vv.DataManager.gameViewType = ProtoBuf.commonProto.MTT_GAME_MODE.NLH;
    //                         // this.newGameView = cc.instantiate(this.holdemGameViewPrefab);
    //                         // this.newGameView.parent = this.layers[5];
    //
    //                         if( gameMode == globalConfig.GAME_LEVEL_LIST_ID.MTT && info instanceof ProtoBuf.commonProto.MttTournament )
    //                         {
    //                             httpApis.requestMttTournamentDetail(cc.vv.DataManager.currentGameInfo.Detail.Id,(data:any)=>{
    //                                 if(!data.ErrorCode){
    //                                     tempMttHallInfo = data;
    //                                     cc.vv.AssetsManager.loadScene("holdem", ()=>{
    //                                         this.setHoldemViewMttHallInfo(gameMode, tempMttHallInfo);
    //                                     });
    //                                 }
    //                                 // else if(data.ErrorCode == ProtoBuf.commonProto.ErrorCode.Mtt_Tournament_Ended||data.ErrorCode ==ProtoBuf.commonProto.ErrorCode.Mtt_Tournament_Canceled){
    //                                 //     cc.vv.ConsoleLog.log("mtt update error");
    //                                 //     let errorCode = this.checkForCancelOrEnd();
    //                                 //     this.handleGameEndOrCancel(errorCode);
    //                                 // }
    //                             }, undefined);
    //                         }
    //                         else
    //                         {
    //                             cc.vv.AssetsManager.loadScene("holdem");
    //                         }
    //
    //
    //                         break;
    //                     case ProtoBuf.commonProto.MTT_GAME_MODE.OMAHA:
    //                         cc.vv.DataManager.gameViewType = ProtoBuf.commonProto.MTT_GAME_MODE.OMAHA;
    //                         // this.newGameView = cc.instantiate(this.holdemGameViewPrefab);
    //                         // this.newGameView.parent = this.layers[5];
    //                         // if( gameMode == globalConfig.GAME_LEVEL_LIST_ID.MTT )
    //                         // {
    //                         //     cc.sys.localStorage.setItem("mttHallInfo", JSON.stringify(this._mttPage.getComponent("MttPrefab")._mttHall._mttHallInfo) );
    //                         // }
    //
    //                         if( gameMode == globalConfig.GAME_LEVEL_LIST_ID.MTT && info instanceof ProtoBuf.commonProto.MttTournament )
    //                         {
    //                             httpApis.requestMttTournamentDetail(cc.vv.DataManager.currentGameInfo.Detail.Id,(data:any)=>{
    //                                 if(!data.ErrorCode){
    //                                     tempMttHallInfo = data;
    //                                     cc.vv.AssetsManager.loadScene("holdem", ()=>{
    //                                         this.setHoldemViewMttHallInfo(gameMode, tempMttHallInfo);
    //                                     });
    //                                 }
    //                                 // else if(data.ErrorCode == ProtoBuf.commonProto.ErrorCode.Mtt_Tournament_Ended||data.ErrorCode ==ProtoBuf.commonProto.ErrorCode.Mtt_Tournament_Canceled){
    //                                 //     cc.vv.ConsoleLog.log("mtt update error");
    //                                 //     let errorCode = this.checkForCancelOrEnd();
    //                                 //     this.handleGameEndOrCancel(errorCode);
    //                                 // }
    //                             }, undefined);
    //                         }
    //                         else
    //                         {
    //                             cc.vv.AssetsManager.loadScene("holdem");
    //                         }
    //                         break;
    //                 }
    //                 // this.newGameView.setPosition(this.position[2].position);
    //                 // this.newGameView.active = true;
    //                 // this.newGameView.opacity = 100;
    //                 // this.finishGameViewLoading();
    //             } else {
    //                 this.hasCallGameView = false;
    //                 this.node.hidLoading();
    //                 cc.vv.AssetsManager.showDialogBox(
    //                     Translate(Translation.ERROR_CODE_PKW.TITLE),
    //                     "牌局已经结束",
    //                     false,
    //                     [
    //                         {
    //                             type: 0,
    //                             text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
    //                             callback: () => {
    //
    //                             },
    //                         },
    //                     ]
    //                 );
    //             }
    //         });
    //     }
    // }

    setCardAnimation(needAnimation:boolean)
    {
        this.needAnimation = needAnimation;
        for(let playerSeatNum in this.seatedPlayers)
        {
            this.seatedPlayers[playerSeatNum].needAnimation = needAnimation;
        }
    }

    getSelfPlayer (): holdemPlayerStore {
        return this.getPlayer(this.playerUserId);
    }

    getPlayer (userId:number): holdemPlayerStore {
        return this.seatedPlayers[Object.keys(this.seatedPlayers).find(seatNum=>{return this.seatedPlayers[seatNum].userId == userId})];
    }

    checkIsPlayerSeated (userId:number): boolean {
        let seatNum = Object.keys(this.seatedPlayers).find(seatNum=>{return this.seatedPlayers[seatNum].userId == userId});
        return seatNum >= 0;
    }

    private onCelebrityBroadcastResCallback:(msg:holdem.CelebrityBroadcastRes)=>void = null;
    sendCelebrityBroadcastRequest(isOnLive:boolean, isAudioOnlive:boolean, callback?:(msg:holdem.CelebrityBroadcastRes)=>void) {
        this.onCelebrityBroadcastResCallback = callback;
        this.ws.Send(ProtoBuf.holdem.CelebrityBroadcastReq.create({roomId: this.roomId, isOnlive: isOnLive, isAudioOnlive: isAudioOnlive}));
    }

    onCelebrityBroadcastRes = (msg:holdem.CelebrityBroadcastRes) => {
        if (msg && msg.roomId == this.roomId) {
            cc.vv.ConsoleLog.log("onCelebrityBroadcastRes", msg);
            this.node.liveHandler.isBroadcastValid = msg.success;
            this.node.enableGameMic(!(msg.success || msg.audioSuccess));

            if (msg.errorCode){
                cc.vv.ConsoleLog.log("onCelebrityBroadcastRes error", msg.errorCode);
                if (msg.errorCode == 50005){
                    this.node.showReachMaxCannotStart();
                }
            }

            if (this.onCelebrityBroadcastResCallback) {
                this.onCelebrityBroadcastResCallback(msg);
                this.onCelebrityBroadcastResCallback = null;
            }
        }
    };

    @action
    onCelebrityBroadcastListMsg = (msg:holdem.CelebrityBroadcastListMsg) => {
        if (msg && msg.roomId == this.roomId){
            this.streamingPlayers = msg.broadcastList;
            this.streamingAudioPlayers = msg.audioBroadcastList;
            this.node.StreamPlayerChanged();
            this.node.StreamAudioPlayerChanged();
        }
    };

    private hadPlayMysteryBountyStartIntroAnim = false;
    onMysteryPrizeTableMsg = (msg:mttPro.MysteryPrizeTableMsg) => {
        if(msg && msg.mttId == this.tournamentId){
            cc.vv.ConsoleLog.log("MysteryBounty onMysteryPrizeTableMsg : " + msg.mttId + " " +  msg.prizeList);
            if (!this.hadPlayMysteryBountyStartIntroAnim) {
                this.hadPlayMysteryBountyStartIntroAnim = true;
                this.node.playMysteryBountyStartIntroAnim(msg.prizeList,msg.totalBounty);
                this.isMysteryBountyOnIntro = true;
                this.node.restMessageLayer.active = false;
            }
        }
    };

    onMysteryPrizeMsg = (msg:mttPro.MysteryPrizeMsg) => {
        cc.vv.ConsoleLog.log("onMysteryPrizeMsg Success: " + msg.mttId + " " +  msg.eliminatUserInfo);
        //播放全局Jackpot消息
        let jackPotAction = ()=>{
            if(msg && msg.mttId == this.tournamentId && msg.eliminatUserInfo && msg.eliminatUserInfo.length > 0){
                msg.eliminatUserInfo.forEach(eliminaterInfo => {
                    if(eliminaterInfo.hitJackpot){
                        let prize = 0;
                        let winnerNames:string[] = [];
                        eliminaterInfo.winnerInfoList.forEach(winner => {
                            prize += winner.prize;
                            winnerNames.push(winner.nick);
                        });
                        this.node.showMysteryBountyJackpotGlobalMsg(winnerNames,prize);
                    }
                });
            }
        }
        if(msg.roomId == this.roomId){
            if(msg.eliminatUserInfo && msg.eliminatUserInfo.length > 0){
                let eliminators = [];
                msg.eliminatUserInfo.forEach(element => {
                //如果是自己拿自己的赏金，则不需要播放拳头动画
                let isSelfEliminat:boolean = false;
                for (let index = 0; index < element.winnerInfoList.length; index++) {
                    if(element.winnerInfoList[index].userId == element.userId){
                        isSelfEliminat = true;
                        break;
                    }
                }
                if(!isSelfEliminat){
                    eliminators.push(element.userId);
                }
                });
                //自己房间播放完赏金动画，再播放全局Jackpot消息
                this.node.playMysteryBountyGloveHitAnim(eliminators,msg.eliminatUserInfo,jackPotAction);
            }
        }else{
            //不是自己房间，直接播放全局Jackpot消息
            jackPotAction();
        }
    };

    sendMysteryPrizeTableReq(){
        console.log("sendMysteryPrizeTableReq mttId:", this.tournamentId);
        this.ws.Send(ProtoBuf.mttPro.MysteryPrizeTableReq.create({mttId: this.tournamentId}));
    }

    onMysteryPrizeTableRes = (msg:mttPro.MysteryPrizeTableRes) => {
        if(msg && msg.mttId == this.tournamentId)
        if (!msg.code) {
            cc.vv.ConsoleLog.log("MysteryBounty onMysteryPrizeTableRes Success: " + msg.mttId + " " +  msg.prizeList);
            if(msg.prizeList && msg.prizeList.length > 0){
                this.node.setMysteryBountyPrizeList(msg.prizeList,msg.totalBounty);
            }else{
                this.node.showMysteryBountyEnterTips(msg,this.MTTDetail.LevelStopSignup);
            }
        } else {
            cc.vv.ConsoleLog.log("onMysteryPrizeTableRes Error: " + msg.code, JSON.stringify(msg));
        }
    };

    sendUpdateUserNoteRequest(userId:number, note:string) {
       httpApis.updateUserNote(userId, note, (msg:commonProto.UpdateUserNoteResponse) => {
           cc.vv.ConsoleLog.log("Update User Note Response", msg);
       });
    }

    sendUserNoteRequest(userId:number, callback:(note:string)=>void) {
        httpApis.requestUserNote(userId, msg => {
            cc.vv.ConsoleLog.log("Request User Note", msg, userId);
            if (msg && !msg.ErrorCode) {
                callback(msg.Note);
            }
        });
    }

    getCurrencyRate(currency:string) {
        let rate = this.currencyRate.find((r) => {return r.Currency == currency;});
        return !!rate ? rate.Rate : 1;
    }

    showBlindTimeCounter(isVisible:boolean) {
        this.node.infoPanel.children[7].children[0].active = isVisible;
        this.node.infoPanel.children[7].children[1].active = isVisible;
    }

    reBuyPopUp = ()=>{
        const remainTime = Math.floor((this.rebuyEndTime - cc.vv.DataManager.getNow().getTime())/1000);
        if(remainTime <= 0){
            return;
        }
        if(!this.MTTDetail){
            this.node.scheduleOnce(this.reBuyPopUp, 1);
            cc.vv.ConsoleLog.log("retry reBuyPopUp");
            return;
        }

        this.reJoinPopUp(-2);
    };

    moreBuyPopUp = ()=>{
        const remainTime = Math.floor((this.morebuyEndTime - cc.vv.DataManager.getNow().getTime())/1000);
        if(remainTime <= 0){
            return;
        }
        if(!this.MTTDetail){
            this.node.scheduleOnce(this.moreBuyPopUp, 1);
            cc.vv.ConsoleLog.log("retry moreBuyPopUp");
            return;
        }
        
        let prefabUrl = (MTTConnector.instance.isBL)?ResourcesLoader.RES_PATH.PREFAB.COMMON.SIGNUP_WITH_TOOLS_BLOCKER:ResourcesLoader.RES_PATH.PREFAB.COMMON.MTT_SIGN_UP_BOX;

        cc.vv.ConsoleLog.log("** UI MOREBUY POP UP");

        let initCoin = this.MTTDetail.StartingCoins;

        if(this.initCoinMorebuy){
            initCoin = this.initCoinMorebuy;
        }

        cc.vv.DataManager.updateToolList(()=>{
            const loadPopUp = ()=>{
                cc.vv.AssetsManager.loadRes(prefabUrl, cc.Prefab, (err:Error, prefab:cc.Prefab) => {
                    cc.loader.setAutoRelease(prefabUrl, true);
                    cc.vv.ConsoleLog.log('morebuy Prefab loaded', this.loseState);
                    
                    let popupNode:cc.Node = cc.instantiate(prefab);
                    let popup:SignUpWithToolPopUp = popupNode.getComponent('SignUpWithToolPopUp');
                    popupNode.parent = this.node.gameEndLayer;    
                    popup.morebuy.active = true;

                    let totalFee = this.srvFeeMorebuy+this.regFeeMorebuy;
                    let regType = 0;

                    let toolCat = globalConfig.GAME_TO_TOOL_ID.MTT;
                    let showTimer:number = 0;
                    
                    if (this.morebuyLeftTime > 0 && this.isMorebuyAllow && this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT){
                        showTimer = this.morebuyEndTime;
                        regType = 2;
                    }
                
                    let signUpOptions = this.MTTDetail? this.MTTDetail.SignUpOptions.toLowerCase() : globalConfig.SIGN_UP_OPTIONS.all;
                    let toolTag = this.MTTDetail.Tag;

                    if(this.morebuyParam.defineParam){
                        signUpOptions = '';
                        toolTag = [];

                        if(this.morebuyParam.AllowGold){
                            signUpOptions += 'gold'
                        }

                        if(this.morebuyParam.AllowTool){
                            if(this.morebuyParam.AllowGold){
                                signUpOptions += ',tool'    
                            }else{
                                signUpOptions += 'tool'
                            }

                            if(this.morebuyParam.signupToolParamList){
                                let length = this.morebuyParam.signupToolParamList.length;
                                if(length > 0){
                                    signUpOptions += ',specific:mtt:';
                                    for(let i=0; i < length; i++){
                                        let toolId = this.morebuyParam.signupToolParamList[i].toolId.toString();
                                        if(i !== length - 1){
                                            toolId += ',';
                                        }
            
                                        let platform = this.morebuyParam.signupToolParamList[i].platform == 3?'a93:':'a92:';
            
                                        signUpOptions += platform + toolId
                                    }
                                }
                            }

                            if(this.morebuyParam.Tag){
                                toolTag = this.morebuyParam.Tag;
                            }
                        }
                    }

                    cc.vv.ConsoleLog.log("moreBuyPopUp params: ", signUpOptions, toolTag, typeof(signUpOptions));

                    let signUpBoxProperty = new SignUpBoxProperty(Translate(Translation.POP_UP_OPTION.MOREBUY.TITLE),totalFee,toolCat,signUpOptions,this.gameInvitationCode,toolTag,this.MTTDetail.DisplayCurrency, this.MTTDetail.Rate, showTimer, null,this.tournamentRoomName,-1,0, this.MTTDetail.RegGoldType, [], initCoin, regType, this.quantityMorebuy);
                    cc.vv.ConsoleLog.log('morebuy tools:', signUpBoxProperty.tools.length);
                    if(signUpBoxProperty.toolOption && !signUpBoxProperty.goldOption && signUpBoxProperty.tools.length<1){
                        let hints = Translate(Translation.POPUP_HINTS.NO_TOOL);
                        this.callPopUpBox(hints,()=>{
                            //this.loseState[0] = false;
                            cc.vv.ConsoleLog.log('** close morebuy and check reward');
                            //this.loseCondition(1);
                            popup.zoomOut();
                            
                        });
                    }else{
                        popup.callPopUp(signUpBoxProperty,
                            (toolInfo:any)=>{
                                let toolId = toolInfo?toolInfo.Id:0;
                                let morebuyTotalFee = totalFee * popup.morebuyCount;
                                const balance = MTTConnector.instance.getUserGold();
                                const multiplier = this.displayCurrency == globalConfig.CURRENCY.USD ? this.exchangeRate : 1;
                                cc.vv.ConsoleLog.log(`moreBuyPopUp\ntotalFee: ${totalFee}\nmorebuyTotalFee: ${morebuyTotalFee}\ntoolInfo: `, toolInfo);
                                

                                if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT) {
                                    if (toolId==0&&(balance < morebuyTotalFee * multiplier)){
                                        this.handleNoMoney();
                                    } else{
                                        this.handleCoupon(toolInfo, ()=>{
                                            
                                            let toolList = [];  // todo -> support Morebuy with tool
                                            if(toolInfo){
                                                const toolDetail = {
                                                    ticketId: toolInfo.Id,
                                                    platForm: ProtoBuf.commonProto.PLATFORM.PKW
                                                }
                                                toolList.push(toolDetail);
                                            }
                                            this.MTTMorebuy(toolList);
                                            this.isMorebuyShown = true;


                                            
                                        }, ()=>{loadPopUp()}, morebuyTotalFee);
                                    }
                                    
                                }
                            },
                            ()=>{
                                if (this.tournamentMode==HOLDEM_TOURNAMENT_TYPE.MTT){
                                    // todo -> mtt
                                    this.MttCancelBuyReq(ProtoBuf.mttPro.MTT_CancelBuy_Type.Morebuy);
                                }
                            });
                    }
                })
            };
           
            if (MTTConnector.instance.isBL && cc.vv.BLDataManager){
                cc.vv.BLDataManager.updateToolList(()=>{
                    loadPopUp();
                });
            }else{
                loadPopUp();
            }

        }, false);

        
    };
}
