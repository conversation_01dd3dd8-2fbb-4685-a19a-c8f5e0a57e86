import MTTConnector from "./MTTConnector";

export enum BuildPlatform{
    LOCAL_DEV,
    DEV,
    PRE_PRO,
    PRO,
    PKW_PRO,
}

export class BuildConfig
{
    get isPro():boolean
    {
        return this.buildPlatform == BuildPlatform.PKW_PRO;
    }

    get isPrePro():boolean
    {
        return this.buildPlatform == BuildPlatform.PRE_PRO || this.buildPlatform == BuildPlatform.PRO;
    }

    get writeWebLog():boolean
    {
        if( cc.vv && cc.vv.DataManager && cc.vv.DataManager.webPlatform != 0 )
        {
            switch(this.buildPlatform)
            {
                // case BuildPlatform.PKW_PRO:
                case BuildPlatform.DEV:
                    return true;
            }
        }
        return false;
    }

    get blockConsoleLog():boolean
    {
        if( cc.vv && cc.vv.DataManager && cc.vv.DataManager.webPlatform != 0 )
        {
            switch(this.buildPlatform)
            {
                case BuildPlatform.PKW_PRO:
                    return true;
            }
        }
        return false;
    }

    get buildPlatform():BuildPlatform
    {
        if( MTTConnector.instance )
        {
            return MTTConnector.instance.getDebugMode() == 1 ? BuildPlatform.DEV : BuildPlatform.PKW_PRO;
        }
        return BuildPlatform.PKW_PRO;
    }

    // pro : dev
    webVersion: string = '3.10.2';
}
