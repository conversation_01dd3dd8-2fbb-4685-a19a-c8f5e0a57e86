import { AndroidBackButton } from "../../../../../script/common/AndroidBackButton";
import BasicScene from "../../../../../script/common/BasicScene";
import { ConsoleLog } from "../../../../../script/common/ConsoleLog";
import { MttPrefabPkw } from "../../game_list/mtt/pkw/MttPrefabPkw";
import { MTTPrefabType } from "../../game_list/mtt/mtt_script/MttPrefab";
import CommonTools from "../../../../../script/common/CommonTools";
import MTTConnector from "../../../../../script/common/MTTConnector";
import { NodePage } from "../../../../../script/common/NodePage";
import { Translate } from "../../../../../script/common/Translator";
import { Translation } from "../../../../../script/common/lang";
import { hallConfig } from "../../../../../script/common/mttconfig";
import { ProtoBuf } from "../../../../../script/common/net/Pb";
import { httpApis } from "../../../../../script/common/net/httpApis";
import { commonProto, modelProto } from "../../../../../script/common/pb/commonProto";
import { NestableScrollView_Inner_ts } from "../../game_list/NestableScrollView_Inner_ts";
import { NestableScrollView_Outer_ts } from "../../game_list/NestableScrollView_Outer_ts";
import { ImpokerHall } from "../../hall_script/ImpokerHall";
import WptHotelFromBag from "../wpt_register/WptHotelFromBag";
import { WptRegitster } from "../wpt_register/WptRegitster";
import { BagTuplePrefab } from "./BagTuplePrefab";
import { GiftDialog } from "./GiftDialog";
import { TicketTuplePrefab } from "./TicketTuplePrefab";
import { UserDetailTuple } from "./UserDetailTuple";
import WptGiftDialog from "./WptGiftDialog";
import AppUpdate  = require("../../../../../script/update/AppUpdate");

const {ccclass, property} = cc._decorator;

@ccclass
export class BagPrefab extends BasicScene {

    @property(cc.Prefab)
    toastPrefab: cc.Prefab = null;

    @property(cc.Button)
    backButton: cc.Button = null;

    @property(cc.Button)
    recordButton: cc.Button = null;

    // @property(cc.Prefab)
    // bagRecordPrefab:cc.Prefab = null;
    @property(cc.Label)
    pageHeader:cc.Label = null;

    @property(cc.ScrollView)
    scrollView:cc.ScrollView = null;

    @property(cc.Prefab)
    tuplePrefab:cc.Prefab = null;

    @property(cc.Prefab)
    ticketPrefab:cc.Prefab = null;

    @property(cc.Prefab)
    giftDialogPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    wptGiftDialogPrefab:cc.Prefab = null;

    @property(cc.Prefab)
    wptRegisterPagePrefab:cc.Prefab = null;

    @property(cc.Prefab)
    wptHotelRegisterPagePrefab:cc.Prefab = null;

    // @property(cc.Node)
    // emptyMessage:cc.Node = null;
    @property(cc.Node)
    headerContainer:cc.Node = null;

    @property(cc.Node)
    innerNode:cc.Node = null;
    // @property(cc.Prefab)
    // extendTuple:cc.Prefab = null;

    @property(cc.Button)
    searchUserButton:cc.Button = null;

    @property(cc.Button)
    confirmSendButton:cc.Button = null;

    @property(cc.Button)
    selectTicketButton:cc.Button = null;

    // message page

    @property([cc.Node])
    subPageHeader: cc.Node[] = [];

    @property(cc.Node)
    underline: cc.Node = null;

    @property(NestableScrollView_Outer_ts)
    pageView: NestableScrollView_Outer_ts = null;

    @property([NestableScrollView_Inner_ts])
    listView: NestableScrollView_Inner_ts[] = [];

    @property([cc.Label])
    emptyLabel: cc.Label[] = [];

    @property(cc.EditBox)
    searchBar:cc.EditBox = null;

    @property(cc.Node)
    userDetailNode:cc.Node = null;

    @property(cc.Prefab)
    mttToolIdFilteredPrefab:cc.Prefab = null;

    selectedToolId:number = 0;
    selectedUserId:number = 0;
    selectedForeignId:number = 0;
    selectedTicketName:string = "";
    selectedTicketCheck:boolean = false;
    selectedTicketNumber:number = 0;
    selectedTicketMax:number = 0;
    groupTicketOj:any = {};
    tempSelectTicketCheck:boolean = false;
    tempSelectedToolId:number = 0;
    tempSelectedTicketName:string = "";
    tempSelectedTicketMax:number = 0;

    _hallScript:ImpokerHall = null;
    _parentPage:cc.Node = null;
    _bagRecordPage:cc.Node = null;
    // _extendTuple:cc.Node=null;
    tupleLimit:number = 12;
    extendNum:number = 6;
    dataList:any[]=[];
    updateTimer = 0;
    updateInterval = 0.2;
    _isRecord:boolean = false;
    _currentPage:number = 0;
    _dialog:cc.Node = null;
    _wptGiftDialog:WptGiftDialog = null;
    _wptRegisterPage:WptRegitster = null;
    _wptHotelRegisterPage:WptHotelFromBag = null;
    mttSetTimeout:any = null;
    appUpdate:AppUpdate;
    _hasAppUpdate:boolean = false;
    _appUpdateSuccess:boolean = false;

    _spacing: number = 0;
    _titleHeight: number = 0;
    _tupleHeight: number = 0;
    _tuplePerPage: number = 0;
    _lastContentY: number = 0;
    
    _logTimes:number = 0;
    _retryTimeout:any = null;
    _retryInterval:number = 3000;

    _mttToolIdFiltered: MttPrefabPkw;

    onLoad () {
        super.onLoad();
        MTTConnector.instance.unregisterMessageCenter(MTTConnector.instance.config.BroadCast.MTT_TOKEN_END, this.node);
        MTTConnector.instance.unregisterMessageCenter(MTTConnector.instance.config.BroadCast.MTT_TOKEN_ERROR, this.node);
        MTTConnector.instance.unregisterMessageCenter(MTTConnector.instance.config.BroadCast.REFRESH_MTT_BAG_LIST, this.node);
        MTTConnector.instance.registerMessageCenter(MTTConnector.instance.config.BroadCast.MTT_TOKEN_END, this.onTokenEnd, this.node);
        MTTConnector.instance.registerMessageCenter(MTTConnector.instance.config.BroadCast.MTT_TOKEN_ERROR, this.onTokenError, this.node);
        MTTConnector.instance.registerMessageCenter(MTTConnector.instance.config.BroadCast.REFRESH_MTT_BAG_LIST, this.refreshList, this.node);
        this.initBasicScene();
        this.hideHeader();
        this.presetPage();

        this.pageView.node.on("page-turning", () => {
            const contentWidget = this.pageView.content.getComponent(cc.Widget);
            const tmpPageIndex:number = this.pageView.getCurrentPageIndex();
            contentWidget.left = - tmpPageIndex;
            contentWidget.right = tmpPageIndex + 1 - this.listView.length;
            const underlineWidget = this.underline.getComponent(cc.Widget);
            underlineWidget.left = tmpPageIndex / this.listView.length;
            underlineWidget.right = ((tmpPageIndex + 1) % this.listView.length) / this.listView.length;
            for(let i = 0; i < this.subPageHeader.length; i++) {
                this.subPageHeader[i].color = i === tmpPageIndex ? MTTConnector.instance.config.mttColor.bagPrefab.headerOn : MTTConnector.instance.config.mttColor.bagPrefab.headerOff;
            }
            cc.vv.ConsoleLog.log('curr ppp', this._currentPage, tmpPageIndex);
            if (this._currentPage!==tmpPageIndex) {
                this._currentPage = tmpPageIndex;
                if (this._currentPage==0){
                    this.resetConfirmData();
                    // this.resetPage();
                    this.setPage();
                }
            }


        });
        this.getUrlConfigVersion();
    }
    
    connect()
    {
        if( !this._hasAppUpdate )
        {
            this.getUrlConfigVersion();
        }
        else
        {
            this.initStartPage();
        }
    }

    onDestroy()
    {
        super.onDestroy();
        if( cc.isValid(this._mttToolIdFiltered) )
        {
            if( cc.isValid(this._mttToolIdFiltered._mttHall) )
            {
                this._mttToolIdFiltered._mttHall.targetOff(this);
                this._mttToolIdFiltered._mttHall.removeFromParent(true);
                this._mttToolIdFiltered._mttHall.destroy();
                this._mttToolIdFiltered._mttHall = null;
            }
            this._mttToolIdFiltered.node.targetOff(this);
            this._mttToolIdFiltered.node.removeFromParent(true);
            this._mttToolIdFiltered.node.destroy();
            this._mttToolIdFiltered = null;
        }
        MTTConnector.instance.unregisterMessageCenter(MTTConnector.instance.config.BroadCast.MTT_TOKEN_END, this.node);
        MTTConnector.instance.unregisterMessageCenter(MTTConnector.instance.config.BroadCast.MTT_TOKEN_ERROR, this.node);
        MTTConnector.instance.unregisterMessageCenter(MTTConnector.instance.config.BroadCast.REFRESH_MTT_BAG_LIST, this.node);
        this.destroyBasicScene();
    }

    getUrlConfigVersion()
    {
        if(!this._hasAppUpdate)
        {
            this._hasAppUpdate = true;
            this.appUpdate = new AppUpdate();
            this.appUpdate.getUrlConfigVersion(this.initStartPage, this.failGetUrlConfigVersion);
        }
    }

    initStartPage = ()=>
    {
        this._appUpdateSuccess = true;
        this.setPage();
    }

    failGetUrlConfigVersion = ()=>
    {
        if (MTTConnector.instance.isWPK){
            clearTimeout(this.mttSetTimeout);
        }else{
            this.showNetworkErrorDialog();
        }
    }

    showNetworkErrorDialog(){
        this.hideLoading("All");
        const self = this;
        this._hasAppUpdate = false;
        this.dialogController.showDialogBox(
            Translate(Translation.ERROR_CODE_PKW.TITLE),
            Translate(Translation.MESSAGE_DIALOG_BLOCKER.NETWORK_ERROR),
            false,
            [
                {
                    type: 0,
                    text: Translate(Translation.NETWORK.RECONNECT),
                    callback: () => {
                        cc.vv.ConsoleLog.log("App update error retry");
                        self.getUrlConfigVersion();
                    }
                }
            ]
        );
    }

    resetConfirmData(){
        this.selectedUserId = 0;
        this.selectedForeignId = 0;
        this.selectedTicketNumber = 0;
        this.selectedTicketName = "";
        this.selectedTicketMax = 0;
        this.selectedToolId = 0;
        this.selectedTicketCheck = false;
        this.userDetailNode.active = false;
        this.searchBar.string = "";
    }

    resetBagPage(remove: boolean = true){
        if (this._dialog){
            this._dialog.active = false;
            this._dialog = null;
        }
        this.closeAllPopUp();
        if (this._currentPage==0){
            if(remove) this.resetPage();
            this.setPage();
        } else{
            this.resetConfirmData();
        }
    }

    start () {
        // this.setPage();
        // this.listView[0].node.on("scroll-to-bottom",()=>{
        //     // cc.vv.ConsoleLog.log("bottom");
        //     this.onExtendTupleClicked();
        // })
    }

    update (dt:any) {
        // this.updateTimer+=dt;
        // if(this.updateTimer > this.updateInterval){
        //     this.updateTimer = 0;
        //     this.hideTuplesOutsideView(this.listView[0]);
        // }
        if(this.node.active) {
            // let content = this.listView[0].content;
            // if(content.childrenCount > 0) {
            //     if(content.y + content.children[content.childrenCount - 1].y + content.children[content.childrenCount - 1].height >= - content.parent.height) {
            //         this.addTuple(this.pageView.getCurrentPageIndex(), 1);
            //     }
            // }
            if (MTTConnector.instance.isWPK) this.underline.width = this.underline.parent.width / 2;
            const contentOriginalX = - this.pageView.node.width / 2;
            const lineOriginalX = this.underline.width / 2 + contentOriginalX;
            const centerOffset = (this.subPageHeader[0].parent.width - this.underline.width) / 2 + (MTTConnector.instance.isWPK ? 32 : 0);
            this.underline.position = cc.v2(lineOriginalX - (this.pageView.content.x - contentOriginalX) * (this.subPageHeader[0].parent.parent.width / this.pageView.content.width) + centerOffset, this.underline.position.y);

            // update scrollview tuple
            if (this.listView[0].content.childrenCount > 0) {
                // store value for consistent
                const currentContentY = this.listView[0].content.y;
                const scrollViewHeight = this.listView[0].node.height;

                // calculate content movement
                const diffY = currentContentY - this._lastContentY;
                const contentMoveUp = diffY > 0;
                const contentMoveDown = diffY < 0;

                // get the first and the last tuple
                const firstTuple = this.listView[0].content.children[1];
                const lastTuple = this.listView[0].content.children[this.listView[0].content.childrenCount - 1];

                // check movement and move tuple if needed
                if (contentMoveUp) {
                    // check the first tuple is out of screen and move to last
                    if (currentContentY + firstTuple.y > firstTuple.height) {
                        // set tuple data
                        const tuple = firstTuple.getComponent(BagTuplePrefab);
                        const data = this.dataList[this._tuplePerPage * ++tuple._currentPage + tuple._index];
                        if (data !== undefined) tuple.setInfo(data, false, false, false);

                        // set tuple position
                        firstTuple.y = lastTuple.y - lastTuple.height - this._spacing;
                        firstTuple.setSiblingIndex(this.listView[0].content.childrenCount);
                    }
                } else if (contentMoveDown) {
                    // check the last tuple is out of screen and move to first
                    if (currentContentY + lastTuple.y < - scrollViewHeight) {
                        // set tuple data
                        const tuple = lastTuple.getComponent(BagTuplePrefab);
                        const data = this.dataList[this._tuplePerPage * --tuple._currentPage + tuple._index];
                        if (data !== undefined) tuple.setInfo(data, false, false, false);

                        // set tuple position
                        lastTuple.y = firstTuple.y + lastTuple.height + this._spacing;
                        lastTuple.setSiblingIndex(1);
                    }
                }

                // store last content y position
                this._lastContentY = currentContentY;
            }
        }
        // if(this.dataList.length<1){return;}
    }

    addTuple(page: number, count: number, unshift: boolean = false) {
        // if(unshift) {
        //     this.createTuple(0, this.message[page].list[0], this.listView[page].content).setSiblingIndex(0);
        // } else {
        //     let minLength = Math.min(this.listView[page].content.childrenCount + count, this.message[page].list.length);
        //     for(let i = this.listView[page].content.childrenCount; i < minLength; i++) {
        //         this.createTuple(i, this.message[page].list[i], this.listView[page].content);
        //     }
        // }
    }


    hideTuplesOutsideView(scrollView:cc.ScrollView){
        for(const one of scrollView.content.children){
            const viewPos = NodePage.getPositionInView(one,this.listView[0].node);
            const isAbove = (viewPos.y - one.height) > (scrollView.content.parent.height * 1.3 / 2);
            const isBelow = (viewPos.y < -(scrollView.content.parent.height * 1.3 / 2));
            one.opacity = (isAbove || isBelow)?0:255;
        }
    }

    sortList(){
        const sortList:any[] = cc.vv.DataManager.backPackTool;
        return sortList.sort((a,b)=>b.Created.getTime()-a.Created.getTime());
    }

    sortListRecord(){
        const sortList:any[] = cc.vv.DataManager.backPackToolRecord;
        return sortList.sort((a,b)=>((b.Consumed)?b.Consumed.getTime():b.Expiry.getTime())-((a.Consumed)?a.Consumed.getTime():a.Expiry.getTime()));
    }

    clearNewBags(){
        cc.vv.DataManager.newBagsCount=0;
        const list:number[] = [];
        for(const one of cc.vv.DataManager.backPackTool){
            list.push(one.Id);
        }
        NodePage.setItemToLocal(hallConfig.OLD_BAG_ID,list);
    }

    setPage = (callback?:()=>any)=>{
        clearTimeout(this.mttSetTimeout);
        cc.vv.ConsoleLog.log("setPage", cc.vv.DataManager.token);
        if( cc.vv.DataManager.token && this._appUpdateSuccess )
        {
            cc.vv.ConsoleLog.log("cc.vv.DataManager.token", cc.vv.DataManager.token);
            if(cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
                AndroidBackButton.getInstance().addBackFunction("BagPrefab", this.onBackClicked.bind(this));
            }
            this.showLoading();
            this.updateToolListHttp(()=>{
                if(!cc.isValid(this)){return;}
                cc.vv.ConsoleLog.log("check->",cc.vv.DataManager.backPackTool);
                this.dataList = this.sortList();
                this.addList(this.dataList,this.listView[0].content, false);
                this.hideLoading();
                this.clearNewBags();
                if( callback && callback instanceof Function )
                {
                    callback();
                }
            });
        }
        else if(cc.isValid(this.node))
        {
            this.onTokenError(MTTConnector.instance.config.tokenErrorMsg.NO_TOKEN);
        }
        
    }

    refreshList = ()=>
    {
        this.setPage();
    }
    // setPageRecord(){
    //     this._hallScript.showLoading();
    //     this._parentPage = this.node;
    //     cc.vv.DataManager.updateToolList(()=>{
    //         if(!cc.isValid(this)){return;}
    //         cc.vv.ConsoleLog.log("check record->",cc.vv.DataManager.backPackToolRecord);
    //         this.dataList = this.sortListRecord();
    //         this._isRecord = true;
    //         this.addList(this.dataList,this._isRecord);
    //         this._hallScript.hidLoading();
    //     }, true);
    // }
    setParentPage(parentNode:cc.Node){
        this._parentPage = parentNode;
    }

    presetPage(){
        this.pageHeader.string = Translate(Translation.BAG.HEADER.BAG);
    }

    presetPageRecord(parentNode:cc.Node){
        this.recordButton.node.active = false;
        this.pageHeader.string = Translate(Translation.BAG.HEADER.RECORD);
    }

    resetPage(){
        this.listView[0].scrollToTop(0);
        this.removeList();
    }

    addList(list:any[],parent:cc.Node, clickable:boolean){
        // if(list.length<1){
        //     if (clickable==false){
        //         this.emptyLabel[0].node.active=true;
        //     }else{
        //         // cc.vv.ConsoleLog.log('ppp',parent.parent.children);
        //         parent.parent.children[0].active = true;
        //     }
        //     return;
        // }
        if(clickable) {
            const list2 = this.groupTicket(list);
            this.groupTicketOj = list2;
            parent.parent.children[0].active = list2.length <= 0;
            Object.keys(list2).forEach((key) => {
                const temp = cc.instantiate(this.ticketPrefab);
                const ttp = temp.getComponent(TicketTuplePrefab);
                ttp.setClickable(clickable);
                ttp.ticketName.string = list2[key].name;
                ttp.ticketNumber.string = (MTTConnector.instance.isWPK ? "":"x")+`${list2[key].num}`;
                ttp.toolId = list2[key].toolId;
                ttp.bagPrefab = this;
                ttp.setIconUrl(list2[key].iconUrl);
                if(list2[key].toolId == this.selectedToolId) {
                    this.selectedTicketCheck = true;
                    ttp.ticketActive(true);
                }
                temp.parent = parent;
            });
        } else {
            this.onExtendTupleClicked();
        }
    }

    groupTicket(list:any){
        let list2:any = [];
        for (let i=0; i<list.length;i++){
            if (list[i].Usable && (list[i].Expiry==null || list[i].Expiry.getTime() > cc.vv.DataManager.getNow().getTime())){
                if (list[i].ToolId in list2){
                    list2[list[i].ToolId].num = list2[list[i].ToolId].num+1;
                    list2[list[i].ToolId].idArr.push(list[i].Id);
                }else{
                    const tName = this.limitSub(list[i].Name, 26);
                    const arr = {[list[i].ToolId]:{num:1, name:tName,toolId:list[i].ToolId, idArr:[list[i].Id],iconUrl:list[i].IconUrl}};
                    list2 = {...list2, ...arr};
                }
            }
        }
        return list2;
    }

    limitSub(str:string, n:number) {
        const r = /[\u4e00-\u9fa5]/g;
        let m;
        if (str.replace(r, '**').length > n) {
            m = Math.floor(n / 2);
            for (let i = m, l = str.length; i < l; i++) {
                if (str.substr(0, i).replace(r, '**').length >= n) {
                    return str.substr(0, i)+"...";
                }
            }
        }
        return str;
    }

    removeList(){
        this.listView[0].content.destroyAllChildren();
    }

    hideHeader()
    {
        this.headerContainer.active = false;
        const scrollViewWidget:cc.Widget = this.innerNode.getComponent(cc.Widget);
        if( cc.isValid(scrollViewWidget) )
        {
            scrollViewWidget.top = 0;
        }
    }

    onExtendTupleClicked(){
        // store tuple data
        this._spacing = this.listView[0].content.getComponent(cc.Layout).spacingY;
        this._titleHeight = this.tuplePrefab.data.getChildByName("tuple_header").height + this._spacing;
        this._tupleHeight = this.tuplePrefab.data.getChildByName("background").height + this._spacing;

        // 1 for spare tuple (make scrolling continuous)
        this._tuplePerPage = Math.ceil(this.listView[0].node.height / this._titleHeight) + 1;

        // create variable for calculate content height
        let contentHeight = 0;

        // stop auto scroll and reset content y
        this.listView[0].stopAutoScroll();
        this.listView[0].content.y = 0;

        // calculate total node needed include title tuple, 1 for title
        const createNodes = 1 + this._tuplePerPage;

        // reuse or create node and set tuple index
        const currentNodes = this.listView[0].content.childrenCount;
        let temp: cc.Node;
        for (let i = 0; i < createNodes || i - 1 < this.dataList.length; i++) {
            // ignore the first tuple which is title
            const offset = i - 1;

            // check for reuse or instantiate node
            if (i < currentNodes) {
                temp = this.listView[0].content.children[i];
            } else if (i <= createNodes) {
                temp = cc.instantiate(this.tuplePrefab);
                temp.getComponent(BagTuplePrefab).setBagPage(this);

                // set node parent
                temp.parent = this.listView[0].content;
            }

            // set name for debug
            temp.name = `${offset}`;

            // get tuple script
            const tuple = temp.getComponent(BagTuplePrefab);

            // set node index
            tuple.setIndex(offset);

            // init tuple data and position
            if (MTTConnector.instance.isPKW && i === 0) {
                tuple.setInfo(Translate(Translation.PKW_BAG.ALL_TOOL), true, false, false);
                temp.y = contentHeight;

                // accumulate content height
                contentHeight += this._titleHeight;
            } else {
                const data = this.dataList[offset];
                if (data !== undefined) {
                    tuple.setInfo(data, false, false, false);
                    temp.y = - contentHeight;

                    // accumulate content height
                    contentHeight += temp.height + this._spacing;
                } else {
                    temp.y = - (contentHeight + this._tupleHeight * (offset - this.dataList.length));
                }
            }

            // destroy extra tuple
            if (i === this.dataList.length && i >= createNodes) {
                temp.parent = null;
                temp.destroy();
            }
        }

        // set content height by calculated value
        this.listView[0].content.height = contentHeight;

        // set empty label
        this.emptyLabel[0].node.active = this.dataList.length === 0;
    }

    onRecordClicked(){
        // this._hallScript.blockLayer.active = true;
        // setTimeout(()=>{
        //     this._bagRecordPage.getComponent(BagPrefab).setPageRecord();
        //     this._hallScript.blockLayer.active = false;
        //     if(cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
        //         let recordScript = this._bagRecordPage.getComponent(BagPrefab);
        //         AndroidBackButton.getInstance().addBackFunction("BagRecordPrefab", recordScript.onBackClicked.bind(recordScript), true);
        //     }
        // }, cc.vv.DataManager.pageActionSpeed*1100);
        // if(!this._bagRecordPage){
        //     let prefab = cc.director.getScene().getComponentInChildren(ProfilePrefab).bagPrefab;
        //     if(prefab){
        //         this._bagRecordPage = cc.instantiate(prefab);
        //         this._bagRecordPage.parent = this._hallScript.layers[0];
        //         this._bagRecordPage.getComponent(BagPrefab).presetPageRecord(this.node);//behind instance and parent setting
        //     }
        // }
        // if(this._bagRecordPage){
        //     this._hallScript.movePageFromRight(this._bagRecordPage,this.node);
        // }
        // else{
        //     this._hallScript.blockLayer.active = false;
        // }
    }

    onBackClicked(isRecord: boolean = false){
        if(cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
            AndroidBackButton.getInstance().removeBackFunction(isRecord ? "BagRecordPrefab" : "BagPrefab");
        }
        // this._hallScript.movePageToRight(this.node,this._parentPage,()=>{
        //     setTimeout(()=>{
        //         this.resetPage();
        //         this._hallScript.blockLayer.active = false;
        //     },this._hallScript.pageActionSpeed*100);
        // });
    }


    onClickSubHeader(event: any, customEventData: any) {
        this.changeSubPage(parseInt(customEventData))
    }

    changeSubPage(page: number, forceScroll: boolean = false) {
        if(forceScroll || this.pageView.getCurrentPageIndex() !== page) {
            this.pageView.scrollToPage(page, 0.3);
        }
    }

    chooseTicketDialogClick(){
        this.buttonClickable(this.selectTicketButton);
        this.openDialog(2);

    }

    onConfirmClick(){
        this.buttonClickable(this.confirmSendButton);
        this.openDialog(1);
    }

    openDialog(type:number){
        if (type==1){
            cc.vv.ConsoleLog.log('openDialog', this.selectedTicketName);
            if (this.selectedTicketName==""){
                const h = Translate(Translation.PKW_BAG.TICKET_EMPTY);
                if (MTTConnector.instance.isWPK) this.showToastMessage(h);
                else this.callPopUpBox(h, undefined);
                return;
            }
            const temp = cc.instantiate(this.giftDialogPrefab);
            temp.parent = this.node.parent;
            this._dialog = temp;
            const gd = temp.getComponent(GiftDialog);
            gd.setDialogType(type);

            const udt = this.userDetailNode.getComponent(UserDetailTuple);
            this.selectedTicketNumber = udt._currentNum;
            const selectedTool = this.dataList.find(el => el.ToolId == this.selectedToolId);
            gd.setAvatar(udt.avatar.spriteFrame);
            gd.updateConfirmPage(udt.userName.string,this.selectedForeignId,this.selectedTicketName,udt._currentNum, selectedTool ? selectedTool.IconUrl:"");
            gd.callback = ()=>{this.sendGift(); this._dialog=null;};
        }
        if (type==2){

            this.updateToolListHttp(()=>{
                if(!cc.isValid(this)){return;}
                cc.vv.ConsoleLog.log("check->",cc.vv.DataManager.backPackTool);
                this.dataList = this.sortList().filter(tool =>
                    !(
                        tool.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel ||
                        tool.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel_Mall
                    ) 
                    && cc.vv.DataManager.toolConsumeInWpt.findIndex((wptRegTool:any) =>  wptRegTool.ToolsInBackpackId === tool.Id) < 0
                    && tool.Giftable
                );
                const list2 = this.groupTicket(this.dataList);
                if(this.dataList.length < 1 || list2.length < 1) {
                    const h = Translate(Translation.PKW_BAG.NO_TOOL);
                    if (MTTConnector.instance.isWPK) this.showToastMessage(h);
                    else this.callPopUpBox(h, undefined);
                    return;
                }
                const temp = cc.instantiate(this.giftDialogPrefab);
                temp.parent = this.node.parent;
                this._dialog = temp;
                temp.getComponent(GiftDialog).setDialogType(type);
                temp.getComponent(GiftDialog).callback = ()=>{this.updateUserDetail(); this._dialog=null;};
                this.selectedTicketCheck = false;
                this.tempSelectTicketCheck = false;
                this.addList(this.dataList, temp.getComponent(GiftDialog).ticketsNode, true);
                // this._hallScript.hidLoading();
            });
        }
    }

    buttonClickable(button:cc.Button){
        button.interactable = false;
        this.scheduleOnce(()=>{button.interactable = true;},1);
    }

    requestUserSearch(inputData: any, wpkID: string = "") {
        /* httpApis.requestUserSearch(inputData, (msg: commonProto.User_Search_Response) => {
            cc.vv.ConsoleLog.log(msg);
            if (!msg.ErrorCode) {

                if (MTTConnector.instance.isWPK && wpkID) msg.ForeignId = wpkID;

                // set up data
                this.userDetailNode.active = true;
                this.selectedUserId = msg.UserId;
                this.selectedForeignId = parseInt(msg.ForeignId);

                this.userDetailNode.getComponent(UserDetailTuple).initUserDetail(msg, '');
            } else {
                // show error
                // this.userDetailNode.active = false;
                let errMsg = Translate("ERROR_CODE_PKW."+msg.ErrorCode);
                if( msg.ErrorCode == ProtoBuf.commonProto.ErrorCode.User_Not_Exist )
                {
                    errMsg = Translate(Translation.PKW_BAG.SEARCH_EMPTY);
                }
                if (MTTConnector.instance.isWPK) this.showToastMessage(errMsg);
                else this.callPopUpBox(errMsg, undefined);
            }
        }, () => {
            console.log('httpApis.requestUserSearch onerror');
            let h = Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE);
            if (MTTConnector.instance.isWPK) this.showToastMessage(h);
            else this.callPopUpBox(h, () => {});
        }); */
        MTTConnector.instance.MTTSearchUser(inputData).then((msg:commonProto.User_Search_Response) => {
            this.userDetailNode.active = true;
            this.selectedUserId = msg.UserId;
            this.selectedForeignId = parseInt(msg.ForeignId);

            this.userDetailNode.getComponent(UserDetailTuple).initUserDetail(msg, '');
        }).catch((error:any) => {
            if (MTTConnector.instance.isWPK) this.showToastMessage(error);
            else this.callPopUpBox(error, () => {});
        });
    }

    searchButtonClick(){
        // SearchForeignId
        // this.userDetailNode.active = false;

        // disable click 0.5s
        this.buttonClickable(this.searchUserButton);

        if (this.searchBar.string==""){
            const h = Translate(Translation.PKW_BAG.USER_ID_EMPTY);
            if (MTTConnector.instance.isWPK) this.showToastMessage(h);
            else this.callPopUpBox(h, undefined);
            return;
        } if (this.searchBar.string == MTTConnector.instance.cv.dataHandler.getUserData().u32Uid.toString() ||
                MTTConnector.instance.isWPK && this.searchBar.string == CurrentUserInfo.user.userId) {
            const h = Translate(Translation.PKW_BAG.SEARCH_EMPTY);
            if (MTTConnector.instance.isWPK) this.showToastMessage(h);
            else this.callPopUpBox(h, undefined);
            return;
        }


        const inputData = {
            SearchForeignId:this.searchBar.string
        };
        this.resetConfirmData();
        console.log("requestUserSearch", inputData);

        /* if (MTTConnector.instance.isWPK) {

            // use wpk id get pkw id
            MTTConnector.instance.MTTSearchUser({wuid: inputData.SearchForeignId}).then((wpkMsg: any) => {

                console.log("mtt search user resolve\n", wpkMsg);
                if (wpkMsg.data && wpkMsg.data.uid != 0) {

                    this.requestUserSearch({SearchForeignId: wpkMsg.data.uid.toString()}, inputData.SearchForeignId);

                } else {

                    let errMsg = Translate(Translation.PKW_BAG.SEARCH_EMPTY);;
                    if (wpkMsg.errMsg || wpkMsg.errorCode) {
                        errMsg = wpkMsg.errMsg || MTTConnector.instance.getLanguageStr(`errorCode.${wpkMsg.errorCode}`);
                    }
                    console.log("mtt search user resolve, search id may not be wpk id\n", errMsg);

                    // search id for bl or pkw
                    this.requestUserSearch(inputData);

                }

            }, (error: any, data: any) => {

                console.log("mtt search user reject\n", error, data);
                let errMsg = Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE);
                if (error.errMsg || error.errorCode) {
                    errMsg = error.errMsg || MTTConnector.instance.getLanguageStr(`errorCode.${error.errorCode}`);
                }
                this.showToastMessage(errMsg);

            });

        } else {

            this.requestUserSearch(inputData);

        } */
        this.requestUserSearch(inputData);

        // this.userDetailNode.active = true;
        // this.selectedUserId = 1234;
        // this.selectedForeignId = 1234;
        // this.userDetailNode.getComponent(UserDetailTuple).initUserDetail('','');

    }

    handleTempSelectTicket(toolId:number, ticketName:string, ticketNum:number){
        this.tempSelectedToolId = toolId;
        this.tempSelectedTicketName = ticketName;
        this.tempSelectedTicketMax = ticketNum;
        this.selectedTicketCheck = true;
        this.tempSelectTicketCheck = true;
    }

    updateUserDetail(){
        cc.vv.ConsoleLog.log('updateUserDetail',this.selectedTicketMax);
        if (this.tempSelectTicketCheck){
            this.selectedToolId = this.tempSelectedToolId;
            this.selectedTicketName = this.tempSelectedTicketName;
            this.selectedTicketMax = this.tempSelectedTicketMax;
        }

        this.userDetailNode.getComponent(UserDetailTuple).updateTicketInfo(this.selectedTicketName, this.selectedTicketMax)
    }

    sendGift(){
        // call api
        if (this.selectedToolId){
            cc.vv.ConsoleLog.log('sendGift', this.selectedToolId);
            const toolIn:[] = this.groupTicketOj[this.selectedToolId].idArr;
            let requestBackpackIds;
            if (this.selectedTicketNumber<=toolIn.length){
                requestBackpackIds = toolIn.slice(0,this.selectedTicketNumber);
            }
            const inputData = {
                ToUserId: this.selectedUserId,
                ToolInBackpackIds: requestBackpackIds,
            };
            httpApis.requestToolInBackpackGift(inputData,(msg:commonProto.Tool_In_Backpack_Gift_Response)=>{
                cc.vv.ConsoleLog.log(msg);
                if (!msg.ErrorCode){
                    const h = Translate(Translation.POPUP_HINTS.GIFT.HANDSEL_SUCCESSFUL);
                    if (MTTConnector.instance.isWPK) this.showToastMessage(h);
                    else this.callPopUpBox(h, undefined);
                    this.resetConfirmData();
                } else{
                    // show error
                    let errMsg = Translate("ERROR_CODE_PKW." + msg.ErrorCode);
                    if (msg.ErrorCode == ProtoBuf.commonProto.ErrorCode.Temporary_Disabled) {
                        errMsg = Translate(Translation.PKW_BAG.SEND_GIFT_TEMP_BLOCK);
                    }
                    if (MTTConnector.instance.isWPK) this.showToastMessage(errMsg);
                    else this.callPopUpBox(errMsg, undefined);

                }
            },()=>{
                cc.vv.ConsoleLog.log('httpApis.requestToolInBackpackGift onerror');
                const h = Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE);
                if (MTTConnector.instance.isWPK) this.showToastMessage(h);
                else this.callPopUpBox(h, ()=>{});
            });
        } else{
            cc.vv.ConsoleLog.log('sendGift no tool id');
            
        }
    }

    showToastMessage(hints: string, duration: number = 2) {
        cc.vv.ConsoleLog.log("showToastMessage", hints, duration);
        MTTConnector.instance.showBagToastMessage(this.toastPrefab, this.node.parent, hints, duration);
        // let toast = cc.instantiate(this.toastPrefab);
        // toast.getComponent(ToastMessage).showToastMessage(hints, duration);
        // toast.parent = this.node.parent;
    }

    callPopUpBox(hints:string, callback:Function, options:any = null, title = Translate(Translation.ERROR_CODE_PKW.TITLE))
    {
        cc.vv.ConsoleLog.log("callPopUpBox", hints);
        if( !options )
        {
            options = [
                {
                    type: 0,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                    callback,
                },
            ];
        }
        this.dialogController.showDialogBox(title, hints, false, options, this.node.parent);
    }

    closeAllPopUp(){
        this.dialogController.hideAllDialogBox();
        // if(cc.vv.DataManager.popUps.length>0){
        //     for(let one of cc.vv.DataManager.popUps){
        //         if(one instanceof cc.Node){one.destroy();}
        //     }
        //     cc.vv.DataManager.popUps = [];
        // }
    }

    updateToolListHttp(callback?:()=>any, isRecord: boolean = false){
/*
        const toolList:number[] = [];
        httpApis.requestUserToolInBackPack((msg:commonProto.User_Tool_In_Backpacks_Response)=>{
            cc.vv.ConsoleLog.log("getMyToolIds_RES:", msg.ErrorCode, (isRecord ? msg.ToolConsumptions.length : msg.ToolInBackpacks.length), msg.ToolConsumeInWPT);
            cc.vv.DataManager.toolConsumeInWpt = msg.ToolConsumeInWPT;
            for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)){
                // cc.vv.ConsoleLog.log(myTools);
                toolList.push(myTools.ToolId);
            }
            if (toolList.length>0){
                const toolData = {ToolIds:toolList};
                httpApis.requestToolInfo(toolData, (msgT: commonProto.Tool_Info_Response) => {
                    cc.vv.ConsoleLog.log("getMyToolsInfo_RES:", msgT.ErrorCode, msgT.ToolInfos.length);
                    const myBackPack:any[] = [];
                    for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)){
                        const infoS = msgT.ToolInfos.find((tool:modelProto.ITool)=>tool.Id==myTools.ToolId);
                        // cc.vv.ConsoleLog.log('test ->', infoS);
                        if(infoS){// some tools may be deleted
                            const myBackPackT:any = myTools;
                            if(!myBackPackT.Created){myBackPackT.created = infoS.Created;cc.vv.ConsoleLog.log("empty Created!");}
                            myBackPackT.Value = infoS.Value;
                            myBackPackT.IconUrl = infoS.IconUrl;
                            myBackPackT.Name = cc.vv.DataManager.i18DataFromServer(infoS.Name, infoS.NameI18N);
                            myBackPackT.SellRatio = infoS.SellRatio;
                            myBackPackT.Type = infoS.Type;
                            myBackPackT.Description = cc.vv.DataManager.i18DataFromServer(infoS.Description, infoS.DescriptionI18N);
                            if(!myBackPackT.Expiry) myBackPackT.Expiry = infoS.Expiry;
                            if(infoS.Config.startsWith("\"{") && infoS.Config.endsWith("}\"")) {
                                infoS.Config = infoS.Config.slice(1, -1).split("\\\"").join("\"");
                            }
                            myBackPackT.Config = infoS.Config?JSON.parse(infoS.Config):{ForCategory:[],Aof:false};
                            if( (!!myBackPackT.Config)&&(typeof myBackPackT.Config === "object") ){
                                if(Array.isArray(myBackPackT.Config.ForCategory) ){
                                    myBackPackT.Config.ForCategory.forEach((element:any,index:number,array:any[])=>{array[index] = Number(element);});
                                }
                                else{
                                    myBackPackT.Config.ForCategory = [];
                                }
                            }
                            else{
                                myBackPackT.Config = {ForCategory:[],Aof:false};
                            }
                            // cc.vv.ConsoleLog.log("back pack",myBackPackT.Config);
                            myBackPack.push(myBackPackT);
                        }
                    }
                    if(isRecord) {
                        cc.vv.DataManager.backPackToolRecord = myBackPack;
                    } else {
                        cc.vv.DataManager.backPackTool = myBackPack;
                    }
                    if(callback){
                        callback();
                    }
                }, ()=>{
                    console.log('httpApis.requestToolInfo onerror');
                    // let h = Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE);
                    // if (MTTConnector.instance.isWPK) this.showToastMessage(h);
                    // else this.callPopUpBox(h, ()=>{});
                    this.onTokenError(MTTConnector.instance.config.tokenErrorMsg.NETWORK_ERROR);
                });
            }
            else{
                if(isRecord) {
                    cc.vv.DataManager.backPackToolRecord = [];
                } else {
                    cc.vv.DataManager.backPackTool = [];
                }
                if(callback){
                    callback();
                }
            }
        },()=>{
            console.log('httpApis.requestUserToolInBackPack onerror');
            // let h = Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE);
            // if (MTTConnector.instance.isWPK) this.showToastMessage(h);
            // else this.callPopUpBox(h, ()=>{});
            this.onTokenError(MTTConnector.instance.config.tokenErrorMsg.NETWORK_ERROR);
        });
*/
        cc.vv.DataManager.updateToolListHttp(callback, isRecord, () => {
            cc.vv.ConsoleLog.log('httpApis.requestToolInfo onerror');
            this.onTokenError(MTTConnector.instance.config.tokenErrorMsg.NETWORK_ERROR);
        });
    }
    
    retryRequestToken = ()=>{
        MTTConnector.instance.requestToken();
    }

    onTokenEnd = ()=>{
        cc.vv.ConsoleLog.log('ImpokerHallFeature onTokenUpdate', cc.vv.DataManager.token , cc.isValid(this.node));
        if( !cc.isValid(this.node) )
        {
            return;
        }
        clearTimeout(this._retryTimeout);
        this.connect();
    }

    onTokenError = (msg:any)=>{
        cc.vv.ConsoleLog.log('ImpokerHallFeature onTokenError', cc.vv.DataManager.token , msg, cc.isValid(this.node), this.node.active);
        if( !cc.isValid(this.node) )
        {
            return;
        }
        if( msg != MTTConnector.instance.config.tokenErrorMsg.REQUEST_TOKEN_FAIL )
        {
            if( this._logTimes < 5 )
            {
                this._logTimes++;
                ConsoleLog.uploadWebLogs(false, "error_bag");
            }
        }
        if( this.node.active )
        {
            clearTimeout(this._retryTimeout);
            this._retryTimeout = setTimeout(this.retryRequestToken, this._retryInterval);
        }
    }

    showWptGiftDialog(show:boolean) {
        if (!cc.isValid(this._wptGiftDialog)) {
            const temp = cc.instantiate(this.wptGiftDialogPrefab);
            temp.parent = this.node;
            this._wptGiftDialog = temp.getComponent(WptGiftDialog);
            this._wptGiftDialog.setBagPage(this);
        }

        if (show) {
            this._wptGiftDialog.show();
        }
        else {
            this._wptGiftDialog.hide();
        }
    }

    showWptRegisterPage(toolId:number) {
        if (!cc.isValid(this._wptRegisterPage)) {
            const temp = cc.instantiate(this.wptRegisterPagePrefab);
            temp.parent = this.node.parent;
            this._wptRegisterPage = temp.getComponent(WptRegitster);
        }
        this._wptRegisterPage.node.setSiblingIndex(this._wptRegisterPage.node.parent.childrenCount);
        this._wptRegisterPage.node.active = true;
        this._wptRegisterPage.initPage(toolId,()=>{this.setPage()},this.callDialog.bind(this));
    }

    showWptHotelRegisterPage(toolId:number) {
        if (!cc.isValid(this._wptHotelRegisterPage)) {
            const temp = cc.instantiate(this.wptHotelRegisterPagePrefab);
            temp.parent = this.node.parent;
            this._wptHotelRegisterPage = temp.getComponent(WptHotelFromBag);
        }
        this._wptHotelRegisterPage.node.setSiblingIndex(this._wptHotelRegisterPage.node.parent.childrenCount);
        this._wptHotelRegisterPage.node.active = true;
        this._wptHotelRegisterPage.initPage(toolId,()=>{this.setPage()});
    }

    callDialog(title:string, content:string, options:any){
        this.callPopUpBox(content, null, options, title);
    }

    openMttToolIdFiltered(toolId:number)
    {
        let prefab = this.mttToolIdFilteredPrefab;
        let parentLayer = this.node.parent;
        if( prefab && cc.isValid(parentLayer) )
        {
            if( !cc.isValid(this._mttToolIdFiltered) )
            {
                let temp = cc.instantiate(prefab);
                this._mttToolIdFiltered = temp.getComponent(MttPrefabPkw);
                this._mttToolIdFiltered.mttPrefabType = MTTPrefabType.TOOL_ID_FILTERED;
                
            }
            this._mttToolIdFiltered.node.parent = parentLayer;
            this._mttToolIdFiltered.node.active = true;
            this._mttToolIdFiltered.node.setSiblingIndex(parentLayer.childrenCount + 1);
            CommonTools.instance.setWidget(this._mttToolIdFiltered.node, true);
            this._mttToolIdFiltered.setPage(null, true, MTTPrefabType.TOOL_ID_FILTERED, {toolId: toolId});
        }
    }
}
