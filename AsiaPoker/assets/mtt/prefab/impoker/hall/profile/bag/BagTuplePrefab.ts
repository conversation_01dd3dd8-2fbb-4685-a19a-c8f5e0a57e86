import {Translate} from "../../../../../script/common/Translator";
import {Translation} from "../../../../../script/common/lang";
import {globalConfig} from "../../../../../script/common/mttconfig";
import {ImpokerHall} from "../../hall_script/ImpokerHall";
import {BagPrefab} from "./BagPrefab";
import { WorldWebSocket } from "../../../../../script/common/net/worldWebsocket";
import {NodePage} from "../../../../../script/common/NodePage";
import {ProtoBuf} from "../../../../../script/common/net/Pb";
import MTTConnector from "../../../../../script/common/MTTConnector";

const {ccclass, property} = cc._decorator;

@ccclass
export class BagTuplePrefab extends cc.Component {

    @property(cc.Node)
    body:cc.Node = null;
    @property(cc.Node)
    toolDetail:cc.Node = null;
    @property(cc.Label)
    gameName: cc.Label = null;
    @property(cc.Label)
    gameDate: cc.Label = null;
    @property(cc.Sprite)
    toolIcon: cc.Sprite = null;
    @property(cc.Label)
    toolName: cc.Label = null;
    @property(cc.Label)
    toolSpecific: cc.Label = null;
    @property(cc.Node)
    separationPoint: cc.Node = null;
    @property(cc.Label)
    toolTicketId: cc.Label = null;
    @property(cc.Label)
    expiredDate: cc.Label = null;
    @property(cc.Label)
    btnLabel: cc.Label = null;
    @property(cc.Button)
    redeemButton: cc.Button = null;
    @property(cc.Button)
    toolUseButton: cc.Button = null;
    @property(cc.Button)
    regButton:cc.Button = null;
    @property(cc.Button)
    giftButton:cc.Button = null;
    @property(cc.Button)
    infoButton:cc.Button = null;
    @property(cc.Node)
    supplementNode: cc.Node = null;
    @property(cc.Label)
    toolDescription: cc.Label = null;
    @property(cc.Node)
    buttonSet:cc.Node = null;
    @property(cc.SpriteFrame)
    iconOutdate: cc.SpriteFrame = null;
    @property(cc.Label)
    status: cc.Label = null;
    @property([cc.SpriteFrame])
    statusSprite: cc.SpriteFrame[] = [];
    @property(cc.SpriteFrame)
    originIcon: cc.SpriteFrame = null;
    @property(cc.Node)
    wptRegInfo:cc.Node = null;
    @property(cc.Label)
    wptRegName:cc.Label = null;
    @property(cc.Label)
    wptGroup:cc.Label = null;

    _tupleData:any = null;
    _hallScript:any = null;
    _bagPage:BagPrefab = null;
    _toolUrl:string = "";

    _index: number = 0;
    _currentPage: number = 0;

    onDestroy(){
        NodePage.releaseImage(this._toolUrl);
    }

    onLoad () {
        this._hallScript = cc.director.getScene().getComponentInChildren(ImpokerHall);
    }

    start () {

    }

    update (dt) {
        // create variable for check tuple position
        const tuple = this.node;
        const content = tuple.parent;
        const tuplePosition = content.y + tuple.y;
        const scrollview = content.parent;

        // set opacity to reduce drawcall
        tuple.opacity =
            tuple.y > 0 || // out of top content
            tuple.y <= - content.height || // out of bottom content
            tuplePosition > tuple.height || // out of top view
            tuplePosition < - scrollview.height // out of bottom view
                ? 0 : 255;
    }
    setIndex(idx: number, page: number = 0) {
        this._index = idx;
        this._currentPage = page;
    }
    showExpiredDate(show:boolean,expiry?:Date){
        if (MTTConnector.instance.isWPK){
            this.expiredDate.node.active = true;
            let month = (expiry.getMonth() + 1).toString().padStart(2, '0');
            let day = expiry.getDate().toString().padStart(2, '0');
            //let dateStr = expiry ? `${expiry.getFullYear()}-${expiry.getMonth() + 1}-${expiry.getDate()}` : Translate(Translation.BAG.NO_EXPIRED_DATE);
            let dateStr = expiry ? `${expiry.getFullYear()}-${month}-${day}` : Translate(Translation.BAG.NO_EXPIRED_DATE);
            this.expiredDate.string = Translate(Translation.BAG.EXPIRED_DATE).replace("expired_date", dateStr);

        }else{
            this.expiredDate.node.active = show;
            if(show){
                let year = expiry.getFullYear();
                let month = (expiry.getMonth() + 1).toString().padStart(2, '0');
                let day = expiry.getDate().toString().padStart(2, '0');
                //let dateStr = `${convertExpiry.getUTCFullYear()}-${convertExpiry.getUTCMonth()+1}-${convertExpiry.getUTCDate()}`;
                let dateStr = `${year}-${month}-${day}`;
                this.expiredDate.string = Translate(Translation.BAG.EXPIRED_DATE).replace("expired_date",dateStr);
                const isExpired = Date.now() > expiry.getTime();
                if (MTTConnector.instance.isPKW) {
                    this.toolName.node.color = new cc.Color().fromHEX(isExpired ? "#808080" : "#FFFFFF");
                }
            }
            else {
                if (MTTConnector.instance.isPKW) {
                    this.toolName.node.color = cc.Color.WHITE;
                }
            }
        }
    }
    showUsedDate(used?:Date){
        this.expiredDate.node.active = true;
        let month = (used.getMonth() + 1).toString().padStart(2, '0');
        let day = used.getDate().toString().padStart(2, '0');
        //let dateStr = `${used.getFullYear()}-${used.getMonth()+1}-${used.getDate()}`;
        let dateStr = `${used.getFullYear()}-${month}-${day}`;
        this.expiredDate.string = Translate(Translation.BAG.USED_DATE).replace("used_date",dateStr);
    }
    setBagPage(bagPage:BagPrefab) {
        this._bagPage = bagPage;
    }
    setInfo(data: any, title: boolean = false, setSpace: boolean = false, record: boolean = false) {
        this.node.getChildByName("tuple_header").active = title;
        this.node.getChildByName("background").active = !title;
        if(title) {
            if (MTTConnector.instance.isPKW) this.node.getChildByName("tuple_header").getComponent(cc.Layout).paddingTop = record ? 13 : 40;
            this.gameDate.string = data;
            this.gameName.node.active = setSpace;
            this.node.getComponent(cc.Layout).updateLayout();
            return;
        }
        this._tupleData = data;
        // if(this._tupleData){
        //     cc.vv.ConsoleLog.log("tupleData",this._tupleData);
        //     cc.vv.ConsoleLog.log("tupleData.Giftable",this._tupleData.Giftable)
        // }
        this.toolIcon.spriteFrame = this.originIcon;
        NodePage.releaseImage(this._toolUrl);
        if(data.IconUrl && data.IconUrl.length>1){
            this._toolUrl = globalConfig.avatarHost+data.IconUrl + "?" + new Date().getTime();
            cc.vv.AssetsManager.loadWebImage(this.toolIcon,this._toolUrl);
        }
        this.toolName.string = data.Name;
        const expiry = data.Expiry || data.Expired;
        this.showExpiredDate(!!expiry,expiry);
        if(record && data.Consumed) this.showUsedDate(data.Consumed);
        switch(data.Type){
            case ProtoBuf.commonProto.TOOL_TYPE.ticket:
            case ProtoBuf.commonProto.TOOL_TYPE.free_ticket:
            case ProtoBuf.commonProto.TOOL_TYPE.Satellite_Ticket:
                //this.toolSpecific.string = Translate(Translation.BAG.TOOL_TYPE.TICKET);
                this.setToolSpecificString(Translate(Translation.BAG.TOOL_TYPE.TICKET));
                break;
            case ProtoBuf.commonProto.TOOL_TYPE.redPocket:
                //this.toolSpecific.string = Translate(Translation.BAG.TOOL_TYPE.RED_POCKET);
                this.setToolSpecificString(Translate(Translation.BAG.TOOL_TYPE.RED_POCKET));
                break;
            case ProtoBuf.commonProto.TOOL_TYPE.gift:
                //this.toolSpecific.string = Translate(Translation.BAG.TOOL_TYPE.GIFT);
                this.setToolSpecificString(Translate(Translation.BAG.TOOL_TYPE.GIFT));
                break;
            case ProtoBuf.commonProto.TOOL_TYPE.coupon:
                //this.toolSpecific.string = Translate(Translation.BAG.TOOL_TYPE.COUPON);
                this.setToolSpecificString(Translate(Translation.BAG.TOOL_TYPE.COUPON));
                break;
            case ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline:
                //this.toolSpecific.string = Translate(Translation.BAG.TOOL_TYPE.WPT_TICKET);
                this.setToolSpecificString(Translate(Translation.BAG.TOOL_TYPE.WPT_TICKET));
                break;
            case ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel:
                //this.toolSpecific.string = Translate(Translation.BAG.TOOL_TYPE.WPT_HOTEL_TICKET);
                this.setToolSpecificString(Translate(Translation.BAG.TOOL_TYPE.WPT_HOTEL_TICKET));
                break;
            default:
                this.toolSpecific.string = "";
                break;
        }
        //handle redeem
        let redeemable = (data.SellRatio>0);
        // let isExpired = ( (!data.Consumed)&&data.Expiry&&(data.Expiry.getTime()<=cc.vv.DataManager.getNow().getTime()) );
        let recordIsExpired = (!data.Consumed);
        let date = record?(recordIsExpired?expiry:data.Consumed):data.Created;
        this.gameDate.string = `${date.getFullYear()}-${date.getMonth()+1}-${date.getDate()}`;
        if(record){
            this.toolUseButton.node.active = false;
            this.regButton.node.active = false;
            this.redeemButton.node.active = false;
            this.giftButton.node.active = false;
            this.infoButton.node.active = false;
            this.wptRegInfo.active = false;
            this.status.node.parent.active = true;
            this.status.node.parent.getComponent(cc.Sprite).spriteFrame = this.statusSprite[recordIsExpired ? 1 : 0];
            this.showExpiredDate(!!expiry,expiry);
            if (MTTConnector.instance.isPKW) this.status.node.color = new cc.Color().fromHEX(recordIsExpired ? "#999999" : "#01AFA8");
            else if (MTTConnector.instance.isWPK){
                this.toolName.node.color = new cc.Color().fromHEX("#4D5958");
            }
            this.status.string = Translate(recordIsExpired ? Translation.BAG.BUTTON_LABEL.EXPIRED : Translation.BAG.BUTTON_LABEL.USED);
        }
        else{
            this.redeemButton.node.active = false;

            if (MTTConnector.instance.isWPK){
                this.toolName.node.color = new cc.Color().fromHEX("#01AFA8");
                this.body.height = 162;
                this.toolDetail.height = 48;
            }

            switch (data.Type) {
                case ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline:
                case ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel:
                    const consumeInfos = cc.vv.DataManager.toolConsumeInWpt;
                    const wptRegTool = consumeInfos.find((regTool:any) => regTool.ToolsInBackpackId === data.Id);
                    const isRegWithThisTool = !!wptRegTool;
                    // if (MTTConnector.instance.isWPK){
                    //     this.toolUseButton.node.active = !isRegWithThisTool;
                    //     this.giftButton.node.active = true;
                    //     this.giftButton.interactable = data.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline && !isRegWithThisTool;
                    // }
                    // else {
                        this.toolUseButton.node.active = !isRegWithThisTool;
                        if(this._tupleData.Giftable){
                            if(data.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline || 
                                data.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel){
                                    if(!isRegWithThisTool){
                                        this.giftButton.node.active = true;
                                    }
                            }
                        }else{
                            this.giftButton.node.active = false;
                        }
                    // }
                    this.regButton.node.active = false;
                    this.infoButton.node.active = isRegWithThisTool;
                    this.status.node.parent.active = isRegWithThisTool;
                    this.toolSpecific.node.active = !isRegWithThisTool;
                    this.wptRegInfo.active = isRegWithThisTool;
                    if (MTTConnector.instance.isWPK) {
                        this.body.height = isRegWithThisTool ? 242 : 162;
                        this.toolDetail.height = isRegWithThisTool ? 106 : 48;
                    }
                    if (isRegWithThisTool) {
                        this.status.string = Translate(Translation.BAG.BUTTON_LABEL.REGISTERED);
                        this.toolSpecific.node.parent.active = false;
                        this.wptRegName.string = wptRegTool.UserName;
                        this.showExpiredDate(!!expiry,expiry);
                        if (data.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline) {
                            this.setWPTOfflineToolSpecificString();
                            this.wptGroup.string = wptRegTool.Group + Translate(Translation.BAG.REGISTER_GROUP);
                            this.wptGroup.node.parent.active = true;
                        }
                        else if(data.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel_Mall || data.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel){
                            this.setWPTOfflineToolSpecificString();
                            //this.toolSpecific.node.parent.active = true;
                            this.wptGroup.node.parent.active = false;
                        }
                        else {
                            this.wptGroup.node.parent.active = false;
                        }
                    }
                    break;
                default:
                    this.toolUseButton.node.active = false;
                    this.regButton.node.active = this.isMttTicket();
                    this.toolSpecific.node.active = true;
                    this.status.node.parent.active = false;
                    this.wptRegInfo.active = false;
                    this.giftButton.node.active = false;
                    this.infoButton.node.active = false;
                    break;
            }
        }
        if (MTTConnector.instance.isWPK) {
            this.buttonSet.active = this.toolUseButton.node.active ||
                this.regButton.node.active ||
                this.giftButton.node.active ||
                this.infoButton.node.active ||
                this.redeemButton.node.active;
        }
        //handle description
        if(data.Description.length>0){
            this.supplementNode.active = true;
            this.toolDescription.string = data.Description;
        }
        // else{
        this.supplementNode.active = false;
        // }
        this.supplementNode.parent.getComponent(cc.Layout).updateLayout();
        if (MTTConnector.instance.isWPK) {
            this.toolDetail.getComponent(cc.Layout).updateLayout();
        }
        this.node.getComponent(cc.Layout).updateLayout();
        MTTConnector.instance.cv.resMgr.adaptWidget(this.node, true);
    }

    setToolSpecificString(content:string){
        if(!cc.isValid(this._tupleData)){return;}
        let ticketId = this._tupleData.Id;
        if(!this._tupleData.Giftable){
            //cc.vv.ConsoleLog.log("tupleData",Translate(Translation.BAG.REDEEM_TYPE.FALSE) + tupleDatatoolId);
            this.toolSpecific.string = content;
            this.toolTicketId.string = Translate(Translation.BAG.TICKET_ID) +" "+ticketId;
            this.separationPoint.active = true;
            this.toolTicketId.node.active = true;
        }else{
            this.separationPoint.active = false;
            this.toolTicketId.node.active = false;
            this.toolSpecific.string = content
        }
    }

    setWPTOfflineToolSpecificString(){
        if(!cc.isValid(this._tupleData)){return}
        let ticketId = this._tupleData.Id;
        let otoolSpecific = this.toolSpecific.string;
        if(!this._tupleData.Giftable){
            this.toolTicketId.string = Translate(Translation.BAG.TICKET_ID) + " " +ticketId;
            this.toolSpecific.node.active = true;
            this.separationPoint.active = true;
            this.toolTicketId.node.active = true;
            this.toolSpecific.node.parent.active = true;
        }else{
            this.toolSpecific.string = otoolSpecific;
            this.toolSpecific.node.active = true;
            this.toolSpecific.node.parent.active = true;
        }
    }

    isMttTicket() {
        const data = this._tupleData;
        return (
                data.Type == ProtoBuf.commonProto.TOOL_TYPE.ticket ||
                data.Type == ProtoBuf.commonProto.TOOL_TYPE.free_ticket ||
                data.Type == ProtoBuf.commonProto.TOOL_TYPE.Satellite_Ticket
            ) &&
            data.Config.ForCategory.indexOf(ProtoBuf.commonProto.GAME_CATEGORY.MTT) >= 0;
    }

    onUseClicked(){
        switch(this._tupleData.Type){
            case ProtoBuf.commonProto.TOOL_TYPE.ticket:
            case ProtoBuf.commonProto.TOOL_TYPE.coupon:
                let hints = Translate(Translation.BAG.DIALOG.TICKET_COUPON); // "线上门票/折扣券请参与比赛使用"
                this._hallScript.callPopUpBox(hints,undefined);
                break;
            case ProtoBuf.commonProto.TOOL_TYPE.gift:
                cc.vv.AssetsManager.showDialogBox(
                    // Translate(Translation.ERROR_CODE_PKW.TITLE),
                    "",
                    Translate(Translation.BAG.DIALOG.GIFT), // "确定使用背包吗？使用背包请与客服联系"
                    false, [
                        {
                            type: 0,
                            text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.CANCEL), // "取消"
                            // color:new cc.Color().fromHEX("#9B9B9B"),
                            callback: undefined,
                        },
                        {
                            type: 1,
                            text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK), // "确定"
                            callback: ()=>{
                                let node = cc.director.getScene().getComponentInChildren("BagPrefab").node;
                                this._hallScript.onClickCustomerService(node);
                            },
                        },
                    ]
                );
                break;
            case ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline:
                this._bagPage.showWptRegisterPage(this._tupleData.Id);
                break;
            case ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel:
                this._bagPage.showWptHotelRegisterPage(this._tupleData.Id);
                break;
        }
    }
    onRedeemClicked(){
        let text:string = cc.js.formatStr(Translate(Translation.BAG.DIALOG.RED_POCKET), NodePage.roundValue(this._tupleData.SellRatio/100*this._tupleData.Value)); // `该背包可兑换${NodePage.roundValue(this._tupleData.SellRatio/100*this._tupleData.Value)}金币，兑换成功后，金币会加到你的个人账户`;
        cc.vv.AssetsManager.showDialogBox(
            // Translate(Translation.ERROR_CODE_PKW.TITLE),
            "",
            text,
            false, [
                {
                    type: 0,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.CANCEL), // "取消"
                    // color:new cc.Color().fromHEX("#9B9B9B"),
                    callback: undefined,
                },
                {
                    type: 1,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK), // "确定"
                    callback: ()=>{
                        this._hallScript.showLoading();
                        let inputData = {
                            ToolInBackpackId:this._tupleData.Id,
                            UserId:cc.vv.DataManager.userId
                        };
                        // if(WorldWebSocket.checkNetwork("requestToolInBackpackRedeem")) return;
                        cc.vv.DataManager.worldNetwork.requestToolInBackpackRedeem(inputData,(msg:any)=>{
                            cc.vv.ConsoleLog.log("redeem",msg);
                            let hall = this._hallScript;
                            if(msg && (!msg.ErrorCode)){
                                cc.director.getScene().getComponentInChildren(BagPrefab).removeList();//todo:optimize with offset
                                cc.director.getScene().getComponentInChildren(BagPrefab).setPage();
                                let hints = Translate(Translation.BAG.DIALOG.REDEEM_SUCCESS); // "兑换成功"
                                this._hallScript.hidLoading(()=>{
                                    hall.callPopUpBox(hints,undefined);
                                });
                            }
                            else{
                                this._hallScript.hidLoading(()=>{
                                    hall.callPopUpBox(Translate("ERROR_CODE_PKW."+msg.ErrorCode),undefined);
                                });
                            }
                        });
                    },
                },
            ]
        );
    }
    onGiftClicked() {
        this._bagPage.showWptGiftDialog(true);
        this._bagPage._wptGiftDialog.setGiftInfo(this._tupleData);
    }
    onInfoClicked() {
        if (this._tupleData.Type == ProtoBuf.commonProto.TOOL_TYPE.WPT_Offline_Hotel){
            this._bagPage.showWptHotelRegisterPage(this._tupleData.Id);
        }else{
            this._bagPage.showWptRegisterPage(this._tupleData.Id);
        }
    }
    onRegisterClicked() {
        cc.vv.ConsoleLog.log("onRegisterClicked", this._tupleData);
        this._bagPage.openMttToolIdFiltered(this._tupleData.Id);
    }
}
