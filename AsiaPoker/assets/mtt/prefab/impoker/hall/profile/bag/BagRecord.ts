import { AndroidBackButton } from "../../../../../script/common/AndroidBackButton";
import BasicScene from "../../../../../script/common/BasicScene";
import { Translation } from "../../../../../script/common/lang";
import { httpApis } from "../../../../../script/common/net/httpApis";
import { commonProto, modelProto } from "../../../../../script/common/pb/commonProto";
import { Translate } from "../../../../../script/common/Translator";
import { BagTuplePrefab } from "./BagTuplePrefab";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BagRecord extends BasicScene {
    @property(cc.Prefab) tuplePrefab: cc.Prefab = null;

    @property(cc.ScrollView) listView: cc.ScrollView = null;

    @property(cc.Label) emptyLabel: cc.Label = null;

    @property(cc.EditBox) searchBar: cc.EditBox = null;

    @property(cc.Button) searchButton: cc.Button = null;

    @property(cc.Node) bagRecord: cc.Node = null;

    @property(cc.Layout) innerNodelayout: cc.Layout = null;

    @property(cc.Node) innerNodeSafearea: cc.Node = null;

    @property(cc.Node) headerStandard: cc.Node = null;

    @property(cc.Node) sortingInputbar: cc.Node = null;

    @property(cc.Node) innerNodeScrollView: cc.Node = null;

    @property(cc.Layout) layout: cc.Layout = null;

    @property(cc.Node) safearea: cc.Node = null;

    @property(cc.Node) subPage: cc.Node = null;

    @property(cc.Node) messageList: cc.Node = null;

    _spacing: number = 0;

    _titleHeight: number = 0;

    _tupleHeight: number = 0;

    _tuplePerPage: number = 0;

    _lastContentY: number = 0;

    _dataList: any[] = [];

    dataList: any[] = [];

    _dialog: cc.Node = null;

    // LIFE-CYCLE CALLBACKS:
    onLoad() {
        super.onLoad();

        this.initBasicScene();
    }

    onDestroy() {
        super.onDestroy();

        this.destroyBasicScene();
    }

    start() {
        let scrollViewWidget: cc.Widget = this.listView.getComponent(cc.Widget);

        if (cc.isValid(scrollViewWidget)) {
            scrollViewWidget.top = 0;
        }
    }

    update(dt) {
        if (this.node.active) {
            // update scrollview tuple
            if (this.listView.content.childrenCount > 0) {
                // store value for consistent
                const currentContentY = this.listView.content.y;

                const scrollViewHeight = this.listView.node.height;

                // calculate content movement
                const diffY = currentContentY - this._lastContentY;

                const contentMoveUp = diffY > 0;

                const contentMoveDown = diffY < 0;

                // get the first and the last tuple
                const firstTuple = this.listView.content.children[0];

                const lastTuple = this.listView.content.children[this.listView.content.childrenCount - 1];

                // check movement and move tuple if needed
                if (contentMoveUp) {
                    // check the first tuple is out of screen and move to last
                    if (currentContentY + firstTuple.y > firstTuple.height) {
                        // set tuple data
                        const tuple = firstTuple.getComponent(BagTuplePrefab);

                        const data = this.dataList[this._tuplePerPage * ++tuple._currentPage + tuple._index];

                        const isTitle = typeof data === "string";

                        if (data !== undefined) tuple.setInfo(data, isTitle, false, !isTitle);

                        // set tuple position
                        firstTuple.y = lastTuple.y - lastTuple.height - this._spacing;

                        firstTuple.setSiblingIndex(this.listView.content.childrenCount);
                    }
                }
                else if (contentMoveDown) {
                    // check the last tuple is out of screen and move to first
                    if (currentContentY + lastTuple.y < - scrollViewHeight) {
                        // set tuple data
                        const tuple = lastTuple.getComponent(BagTuplePrefab);

                        const data = this.dataList[this._tuplePerPage * --tuple._currentPage + tuple._index];

                        const isTitle = typeof data === "string";

                        if (data !== undefined) tuple.setInfo(data, isTitle, false, !isTitle);

                        // set tuple position
                        lastTuple.y = firstTuple.y + lastTuple.height + this._spacing;

                        lastTuple.setSiblingIndex(0);
                    }
                }

                // store last content y position
                this._lastContentY = currentContentY;
            }
        }
    }

    // page function
    resetBagRecordPage() {
        if (this._dialog) {
            this._dialog.active = false;

            this._dialog = null;
        }

        this.closeAllPopUp();

        this.resetPage();

        this.setPage();
    }

    setPage(callback?: () => any) {
        if (cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
            AndroidBackButton.getInstance().addBackFunction("BagRecordPrefab", this.onBackClicked.bind(this));
        }

        this.showLoading();

        this.updateToolListHttp(() => {
            if (!cc.isValid(this)) { return; }

            cc.vv.ConsoleLog.log("record check->", cc.vv.DataManager.backPackToolRecord);

            this._dataList = this.sortList();

            this.addList();

            this.hideLoading();

            if (callback && callback instanceof Function) {
                callback();
            }
        }, true);
    }

    resetPage() {
        this.listView.scrollToTop(0);

        this.removeList();
    }

    // list function
    sortList() {
        let list: any[] = cc.vv.DataManager.backPackToolRecord;

        return list.sort((a, b) => { return (b.Consumed ? b.Consumed.getTime() : (b.Expired || b.Expiry).getTime()) - (a.Consumed ? a.Consumed.getTime() : (a.Expired || a.Expiry).getTime()) });
    }

    removeList() {
        this.listView.content.destroyAllChildren();
    }

    addList() {
        // group data list by date
        let groupByDate = {};

        for (let i = 0; i < this._dataList.length; i++) {
            let date = this._dataList[i].Consumed || (this._dataList[i].Expired || this._dataList[i].Expiry);

            let time = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
            //let time = `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;

            if (!groupByDate.hasOwnProperty(time)) {
                groupByDate[time] = [];
            }

            groupByDate[time].push(this._dataList[i]);
        }

        let dateList = Object.keys(groupByDate);

        // check time to show today, yesterday or date
        let showDate = (time: string) => {
            let date = new Date(time);

            let now = cc.vv.DataManager.getNow();

            if (date.getFullYear() === now.getFullYear() && date.getMonth() === now.getMonth()) {
                if (date.getDate() === now.getDate()) {
                    return Translate(Translation.PKW_BAG.TODAY);
                }
                else if (date.getDate() === now.getDate() - 1) {
                    return Translate(Translation.PKW_BAG.YESTERDAY);
                }
            }

            return time;
        };

        // reset data list
        this.dataList = [];

        // set data list and count how many title and data tuples
        let titleTuples: number = 0;

        let dataTuples: number = 0;

        for (let i = 0; i < dateList.length; i++) {
            this.dataList.push(showDate(dateList[i]));

            titleTuples++;

            for (let j = 0; j < groupByDate[dateList[i]].length; j++) {
                this.dataList.push(groupByDate[dateList[i]][j]);

                dataTuples++;
            }
        }

        // store tuple data
        this._spacing = this.listView.content.getComponent(cc.Layout).spacingY;

        this._titleHeight = this.tuplePrefab.data.getChildByName("tuple_header").height + this._spacing;

        this._tupleHeight = this.tuplePrefab.data.getChildByName("background").height + this._spacing;

        // 1 for spare tuple (make scrolling continuous)
        this._tuplePerPage = Math.ceil(this.listView.node.height / this._titleHeight) + 1;

        // create variable for calculate content height
        let contentHeight = 0;

        // calculate total node needed
        const createNodes = this._tuplePerPage;

        // reuse or create node and set tuple index
        const currentNodes = this.listView.content.childrenCount;

        let temp: cc.Node;

        for (let i = 0; i < createNodes || i < this.dataList.length; i++) {
            // check for reuse or instantiate node
            if (i < currentNodes) {
                temp = this.listView.content.children[i];
            }
            else if (i <= createNodes) {
                temp = cc.instantiate(this.tuplePrefab);

                // set node parent
                temp.parent = this.listView.content;
            }

            // set name for debug
            temp.name = `${i}`;

            // get tuple script
            const tuple = temp.getComponent(BagTuplePrefab);

            // set node index
            tuple.setIndex(i);

            // init tuple data and position
            const data = this.dataList[i];

            const isTitle = typeof data === "string";

            if (data !== undefined) {
                tuple.setInfo(data, isTitle, false, !isTitle);

                temp.y = - contentHeight;

                // accumulate content height
                contentHeight += temp.height + this._spacing;
            }
            else {
                temp.y = - (contentHeight + this._tupleHeight * (i - this.dataList.length));
            }

            // destroy extra tuple
            if (i === this.dataList.length - 1 && i >= createNodes) {
                temp.parent = null;

                temp.destroy();
            }
        }

        // set content height by calculated value
        this.listView.content.height = contentHeight;

        // set empty label
        this.emptyLabel.node.active = this._dataList.length === 0;
    }

    // click function

    onBackClicked() {
        if (cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
            AndroidBackButton.getInstance().removeBackFunction("BagRecordPrefab");
        }
    }

    onSearchClicked() {

    }

    // common function

    closeAllPopUp() {
        this.dialogController.hideAllDialogBox();
    }

    callPopUpBox(hints: string, callback: Function, options: any = null, title = Translate(Translation.ERROR_CODE_PKW.TITLE)) {
        cc.vv.ConsoleLog.log("callPopUpBox", hints);

        if (!options) {
            options = [
                {
                    type: 0,
                    text: Translate(Translation.MESSAGE_DIALOG_BLOCKER.OK),
                    callback: callback,
                },
            ];
        }

        this.dialogController.showDialogBox(title, hints, false, options, this.node.parent);
    }

    updateToolListHttp(callback?: () => any, isRecord: boolean = false) {
/*
        let toolList: number[] = [];

        httpApis.requestUserToolInBackPack((msg: commonProto.User_Tool_In_Backpacks_Response) => {
            cc.vv.ConsoleLog.log("getMyToolIds_RES:", msg.ErrorCode, (isRecord ? msg.ToolConsumptions.length : msg.ToolInBackpacks.length));

            for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)) {
                // cc.vv.ConsoleLog.log(myTools);
                toolList.push(myTools.ToolId);
            }

            if (toolList.length > 0) {
                let toolData = { ToolIds: toolList };

                httpApis.requestToolInfo(toolData, (msgT: commonProto.Tool_Info_Response) => {
                    cc.vv.ConsoleLog.log("getMyToolsInfo_RES:", msgT.ErrorCode, msgT.ToolInfos.length);

                    let myBackPack: any[] = [];

                    for (const myTools of (isRecord ? msg.ToolConsumptions : msg.ToolInBackpacks)) {
                        let infoS = msgT.ToolInfos.find((tool: modelProto.ITool) => { return tool.Id == myTools.ToolId; });

                        if (infoS) {
                            //some tools may be deleted
                            let myBackPackT: any = myTools;

                            if (!myBackPackT.Created) { myBackPackT.created = infoS.Created; cc.vv.ConsoleLog.log("empty Created!"); }

                            myBackPackT.Value = infoS.Value;

                            myBackPackT.IconUrl = infoS.IconUrl;

                            myBackPackT.Name = cc.vv.DataManager.i18DataFromServer(infoS.Name, infoS.NameI18N);

                            myBackPackT.SellRatio = infoS.SellRatio;

                            myBackPackT.Type = infoS.Type;

                            myBackPackT.Description = cc.vv.DataManager.i18DataFromServer(infoS.Description, infoS.DescriptionI18N);
                            if(!myBackPackT.Expiry) myBackPackT.Expiry = infoS.Expiry;
                            if(infoS.Config.startsWith("\"{") && infoS.Config.endsWith("}\"")) {
                                infoS.Config = infoS.Config.slice(1, -1).split("\\\"").join("\"");
                            }

                            myBackPackT.Config = infoS.Config ? JSON.parse(infoS.Config) : { ForCategory: [], Aof: false };

                            if ((!!myBackPackT.Config) && (typeof myBackPackT.Config === "object")) {
                                if (Array.isArray(myBackPackT.Config.ForCategory)) {
                                    myBackPackT.Config.ForCategory.forEach((element: any, index: number, array: any[]) => { array[index] = Number(element); });
                                }
                                else {
                                    myBackPackT.Config.ForCategory = [];
                                }
                            }
                            else {
                                myBackPackT.Config = { ForCategory: [], Aof: false };
                            }

                            myBackPack.push(myBackPackT);
                        }
                    }

                    if (isRecord) {
                        cc.vv.DataManager.backPackToolRecord = myBackPack;
                    }
                    else {
                        cc.vv.DataManager.backPackTool = myBackPack;
                    }

                    if (callback) {
                        callback();
                    }
                }, () => {
                    this.callPopUpBox(Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE), () => { });
                });
            }
            else {
                if (isRecord) {
                    cc.vv.DataManager.backPackToolRecord = [];
                }
                else {
                    cc.vv.DataManager.backPackTool = [];
                }

                if (callback) {
                    callback();
                }
            }
        }, () => {
            this.callPopUpBox(Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE), () => { });
        });
*/
        cc.vv.DataManager.updateToolListHttp(callback, isRecord, () => {
            cc.vv.ConsoleLog.log('httpApis.requestUserToolInBackPack onerror');
            this.callPopUpBox(Translate(Translation.WITHDRAW_POPUP.WARNING.NETWORK_FAILURE), ()=>{});
        });
    }
}
