import CustomScrollView from "../../../../../../script/common/tools/CustomScrollView";
import HoldemTournamentDetailsBlindLevelRow from "./HoldemTournamentDetailsBlindLevelRow";
import Holdem_Basic_Item from "../../../../../../script/holdem/Holdem_Basic_Item";
import {globalConfig} from "../../../../../../script/common/mttconfig";
import {Translate} from "../../../../../../script/common/Translator";
import {Translation} from "../../../../../../script/common/lang";
import CustomWidget from "../../../../../../script/common/CustomWidget";
import HoldemTournamentDetails from "./HoldemTournamentDetails";

const {ccclass, property} = cc._decorator;

@ccclass
export default class HoldemTournamentDetailsBlindLevel extends Holdem_Basic_Item {

    @property(CustomScrollView)
    blindScrollView:CustomScrollView = null;
    @property(cc.Label)
    blindHeader:cc.Label = null;
    @property(cc.Label)
    blindDurationHeader:cc.Label = null;

    blindRiseType:number = 0;
    private _blindList:any[] = [];
    private _currentLevel:number = 0;
    get roomCurrentLevel():number {
        return this.holdemRoom.store.currentBlindLevel;
    }

    holdemTournamentDetail:HoldemTournamentDetails = null;

    onLoad () {
        this.blindScrollView.view.node.on(CustomWidget.EventType.ALIGNMENT_UPDATE, ()=> {
            this.updateBlindLevelList(this._blindList);
        });
    }

    start () {

    }

    init()
    {
    }

    setHoldemTournamentDetail(holdemTournamentDetail:HoldemTournamentDetails) {
        this.holdemTournamentDetail = holdemTournamentDetail;
    }

    setBlindRiseType(blindRiseType:number) {
        this.blindRiseType = blindRiseType;
        this.blindDurationHeader.string = blindRiseType == globalConfig.BLIND_RISE_TYPE.HANDS ? Translate(Translation.TOURNAMENT_DETAIL.TAB.BLIND_LEVEL.HEADER.HANDS) : Translate(Translation.TOURNAMENT_DETAIL.TAB.BLIND_LEVEL.HEADER.DURATION);
    }

    // update (dt) {}

    resetPage() {
        this.blindScrollView.resetScrollView();
        this._blindList = [];
    }

    updateBlindLevelList(blindList:any[]) {
        let room = this.holdemRoom;
        this.blindHeader.string = room._gameViewType == globalConfig.MTT_GAME_MODE.SHORT_DECK ? "" : Translate(Translation.TOURNAMENT_DETAIL.TAB.BLIND_LEVEL.HEADER.BLIND);
        let isSameLength = blindList.length === this._blindList.length;
        blindList.forEach((blindLevel, i) => {
            if (!blindLevel.Level) {
                blindLevel.Level = i+1;
            }
        });
        this._blindList = blindList;
        if (!isSameLength) {
            this.blindScrollView.loadItem(this._blindList, (item) => {
                item.getComponent(HoldemTournamentDetailsBlindLevelRow).setHoldemRoom(this.holdemRoom);
            });
            this._currentLevel = this.roomCurrentLevel;
        } else {
            this.refreshBlindLevelList();
        }
    }

    refreshBlindLevelList() {
        if (this._currentLevel != this.roomCurrentLevel) {
            this.blindScrollView.updateList(this._blindList);
            this._currentLevel = this.roomCurrentLevel;
        }
    }

}
