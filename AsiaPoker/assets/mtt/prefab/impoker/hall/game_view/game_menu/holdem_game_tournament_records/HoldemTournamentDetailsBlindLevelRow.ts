import {FormatParser} from "../../../../../../script/common/tools/FormatParser";
import CustomScrollViewItem from "../../../../../../script/common/tools/CustomScrollViewItem";
import {Holdem_Room} from "../../../../../../script/holdem/Holdem_Room_ts";
import {globalConfig} from "../../../../../../script/common/mttconfig";
import MTTConnector from "../../../../../../script/common/MTTConnector";
import CommonTools from "../../../../../../script/common/CommonTools";

const {ccclass, property} = cc._decorator;

@ccclass
export default class HoldemTournamentDetailsBlindLevelRow extends CustomScrollViewItem {

    @property(cc.Label)
    levelLabel: cc.Label = null;
    @property(cc.Node)
    resurrectIcon:cc.Node = null;
    @property(cc.Label)
    blindLabel:cc.Label = null;
    @property(cc.Label)
    anteLabel:cc.Label = null;
    @property(cc.Label)
    durationLabel:cc.Label = null;

    // onLoad () {}

    start () {

    }

    // update (dt) {}

    setData (data:any) {
        super.setData(data);

        let rowLevel = data.Level;
        let currentLevel = this.holdemRoom.store.currentBlindLevel;
        let terminateLevel = this.holdemRoom.store.MTTDetail ? this.holdemRoom.store.MTTDetail.LevelStopSignup : -1;
        let isCurrentLevel = rowLevel == currentLevel;
        let isShortDeck = this.holdemRoom._gameViewType == globalConfig.MTT_GAME_MODE.SHORT_DECK;
        let blindHand = this.holdemRoom.store.blindHands;

        let labelColor = new cc.Color().fromHEX(isCurrentLevel ?
            MTTConnector.instance.tournamentDetailColorCode.blindLevel.highLightColor :
            MTTConnector.instance.tournamentDetailColorCode.blindLevel.normalColor);

        this.levelLabel.string = rowLevel.toString();
        this.levelLabel.node.color = labelColor;
        this.levelLabel._isBold = isCurrentLevel;
        CommonTools.instance.updateRenderData(this.levelLabel);

        this.resurrectIcon.active = rowLevel < terminateLevel;

        this.blindLabel.string = isShortDeck ? "" : FormatParser.ScientificNotation(data.SmallBlind) + "/" + FormatParser.ScientificNotation(data.BigBlind);
        this.blindLabel.node.color = labelColor;
        this.blindLabel._isBold = isCurrentLevel;
        CommonTools.instance.updateRenderData(this.blindLabel);

        this.anteLabel.string = FormatParser.ScientificNotation(data.Ante, 2);
        this.anteLabel.node.color = labelColor;
        this.anteLabel._isBold = isCurrentLevel;
        CommonTools.instance.updateRenderData(this.anteLabel);

        this.durationLabel.string = blindHand > 0 ? blindHand.toString() : FormatParser.RoundToDecimal(this.holdemRoom.store.blindLevelTime / 60, 1) + "m";
        this.durationLabel.node.color = labelColor;
        this.durationLabel._isBold = isCurrentLevel;
        CommonTools.instance.updateRenderData(this.durationLabel);
        
    }
}
