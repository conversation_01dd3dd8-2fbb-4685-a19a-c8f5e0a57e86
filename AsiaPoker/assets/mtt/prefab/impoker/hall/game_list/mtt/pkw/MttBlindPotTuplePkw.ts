import {MttBlindPotTableTuple} from "../mtt_script/MttBlindPotTableTuple";
import UtilsPkw from "./UtilsPkw";
import {NodePage} from "../../../../../../script/common/NodePage";
import {FormatParser} from "../../../../../../script/common/tools/FormatParser";
import {Translate} from "../../../../../../script/common/Translator";
import {Translation} from "../../../../../../script/common/lang";

const {ccclass, property} = cc._decorator;

@ccclass
export class MttBlindPotTuplePkw extends MttBlindPotTableTuple {

    @property(cc.Label)
    durationLbl: cc.Label = null;
    @property(cc.Sprite)
    tupleBg:cc.Sprite = null;
    @property([cc.SpriteFrame])
    bgsf: cc.SpriteFrame[] = [];


    // onLoad () {}

    // start () {
    //
    // }

    // update (dt) {}

    setInfo(tupleInfo: any, tupleId: number, levelStopSignUp: number, isShortDesk: boolean, blindHand: number, levelDuration: number = 0){
        this._tupleId = tupleId;
        this.level.string = tupleInfo.Level;
        this.tupleBg.spriteFrame = this.bgsf[tupleId%2];
        this.stopIcon.active = (tupleInfo.Level < levelStopSignUp);
        if(tupleInfo.BasePot){
            this.selectTableAttributes(1);
            this.basePot.string = FormatParser.ScientificNotation(NodePage.roundValue(tupleInfo.BasePot));
        }
        else{
            this.selectTableAttributes(0);
            this.ante.string = FormatParser.ScientificNotation(NodePage.roundValue(tupleInfo.Ante));
            this.blind.string = isShortDesk ? "" : FormatParser.ScientificNotation(tupleInfo.SmallBlind) + "/" + FormatParser.ScientificNotation(tupleInfo.BigBlind);
        }
        this.durationLbl.string = blindHand > 0 ? blindHand.toString() : FormatParser.RoundToDecimal(levelDuration / 60, 1) + Translate(Translation.TIME.MINUTE);
    }
}
