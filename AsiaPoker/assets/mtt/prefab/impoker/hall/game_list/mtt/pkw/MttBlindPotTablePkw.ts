import {MttBlindPotTable} from "../mtt_script/MttBlindPotTable";
import {MttBlindPotTuplePkw} from "./MttBlindPotTuplePkw";
import {Translate} from "../../../../../../script/common/Translator";
import {Translation} from "../../../../../../script/common/lang";

const {ccclass, property} = cc._decorator;

@ccclass
export class MttBlindPotTablePkw extends MttBlindPotTable {

    @property([cc.Label])
    attrSet1Labels:cc.Label[] = [];

    spawnNum = 26;
    levelDuration = 0;
    // onLoad () {}

    // start () {
    //
    // }

    update (dt:any) {
        if(this.blindPotList.length<1){return;}
        this.updateTimer+=dt;
        if(this.updateTimer > this.updateInterval){
            this.updateTimer = 0;
            let isDown = this.scrollView.content.y < this.lastContentPosY;
            let offset = (this._tupleHeight + this.spacing) * this.tuples.length;

            for (let i = 0; i < this.tuples.length; i++) {
                let viewPos = this.getPositionInView(this.tuples[i]);
                if (isDown) {
                    // if away from buffer zone and not reaching top of content
                    if (viewPos.y < -this.bufferZone && this.tuples[i].y + offset < 0) {
                        this.tuples[i].y = this.tuples[i].y + offset;
                        let tupleScript = this.tuples[i].getComponent(MttBlindPotTuplePkw);
                        let tupleId = tupleScript._tupleId - this.tuples.length; // update item id
                        tupleScript.setInfo(this.blindPotList[tupleId], tupleId, this.levelStopSignUp, this._isShortDesk, this._blindHand, this.levelDuration);
                    }
                } else {
                    // if away from buffer zone and not reaching bottom of content
                    if (viewPos.y > this.bufferZone&& this.tuples[i].y - offset > -this.scrollView.content.height) {
                        this.tuples[i].y = this.tuples[i].y - offset;
                        let tupleScript = this.tuples[i].getComponent(MttBlindPotTuplePkw);
                        let tupleId = tupleScript._tupleId + this.tuples.length;
                        tupleScript.setInfo(this.blindPotList[tupleId], tupleId, this.levelStopSignUp, this._isShortDesk, this._blindHand, this.levelDuration);
                    }
                }
            }
            // update lastContentPosY
            this.lastContentPosY = this.scrollView.content.y;
        }
    }

    selectTableAttributes(option:number){
        if(!option){
            this.attrSet1Labels[1].string = this._isShortDesk ? "" : Translate(Translation.MTT_HALL.TAB.HALL.MTT_BLIND_LIST.COLUMN.BLIND);
            this.attrSet1Labels[3].string = Translate(this._blindHand > 0 ? Translation.TOURNAMENT_DETAIL.TAB.BLIND_LEVEL.HEADER.HANDS : Translation.MTT_HALL.TAB.HALL.MTT_BLIND_LIST.COLUMN.LEVEL_DURATION);
        }
        for(let i=0;i<this.tableAttributes.length;i++){
            this.tableAttributes[i].active = (i==option);
        }
    }

    setInfo(blindPotList:any[],levelStopSignUp:number){
        this.blindPotList = blindPotList;
        this.levelStopSignUp = levelStopSignUp;
        // this.remark.string = cc.js.formatStr(Translate(Translation.MTT_HALL.TAB.HALL.MTT_BLIND_LIST.NOTE_STOP_SIGN_UP), levelStopSignUp); // `终止报名：第${levelStopSignUp}盲注级别`

        this.scrollView.node.getComponent(cc.Widget).updateAlignment();
        this.scrollView.content.height = Math.max(0, (this._tupleHeight+this.spacing)*this.blindPotList.length-this.spacing);
        this.scrollView.content.y = this.scrollView.content.parent.height/2;
        this.bufferZone = this.scrollView.node.height*0.5+this._tupleHeight*1.5;
        let temp:cc.Node;
        for(let i =0; (i< this.spawnNum)&&(i<blindPotList.length);i++){
            // BasePot;
            // ConfigId;
            // Id;
            // Level;
            temp = cc.instantiate(this.tableTuplePrefab);
            temp.parent = this.scrollView.content;
            temp.setPosition(0,  - this._tupleHeight * (0.5 + i) - this.spacing * (i));
            let tempScript = temp.getComponent(MttBlindPotTuplePkw);
            tempScript.setInfo(blindPotList[i], i, levelStopSignUp, this._isShortDesk, this._blindHand, this.levelDuration);
            this.tuples.push(temp);
        }
    }
}
