import { TableView } from "../../Script/common/tools/TableView";

const { ccclass, property } = cc._decorator;
@ccclass
export class TabelViewTestCell extends cc.Component {
    @property(cc.Label) txt_desc: cc.Label = null;
    @property(cc.Label) txt_detail: cc.Label = null;

    private _index: number = 0;
    private _tabelView: TableView = null;

    protected onLoad(): void {
        let btn: cc.Button = this.getComponent(cc.Button)
        if (btn) {
            btn.node.on("click", (event: cc.Event): void => {
                if (!this._tabelView) return;
                // this._tabelView.refreshView();
                // this._tabelView.reloadView();
                this._tabelView.removeCells(this._index, true);
            });
        }
    }

    protected start(): void {
    }

    updateSVReuseData(index: number, data: any, view: TableView): void {
        this._index = index;
        this._tabelView = view;
        this.txt_desc.string = data;

        let viewCount: number = view.getCellViewCount();
        let totalCount: number = view.getCellTotalCount();
        this.txt_detail.string = `id = ${index}, viewCount = ${viewCount}, totalCount = ${totalCount}`;
    }
}
