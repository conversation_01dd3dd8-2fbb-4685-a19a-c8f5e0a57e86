import { ResourceManager } from "../../Script/common/tools/ResourceManager";
import { TableView } from "../../Script/common/tools/TableView";
import { TabelViewTestCell } from "./TabelViewTestCell";

const { ccclass, property } = cc._decorator;
@ccclass
export class TabelViewTest extends cc.Component {
    @property(cc.Button) btn_show: cc.Button = null;
    @property(cc.Button) btn_reload: cc.Button = null;
    @property(cc.Button) btn_mask: cc.Button = null;
    @property(cc.Button) btn_pophide: cc.Button = null;
    @property(cc.Button) btn_scale: cc.Button = null;
    @property(cc.Button) btn_delete: cc.Button = null;
    @property(cc.Button) btn_horizontal: cc.Button = null;
    @property(cc.Button) btn_vertical: cc.Button = null;
    @property(cc.Button) btn_add: cc.Button = null;
    @property(cc.Button) btn_minus: cc.Button = null;

    @property(cc.ScrollView) scrollview_horizontal: cc.ScrollView = null;
    @property(cc.ScrollView) scrollview_vertical: cc.ScrollView = null;

    private _tableView: TableView = null;
    private _ishorizontal: boolean = true;
    private _popHide: boolean = false;

    protected onLoad(): void {
        this.btn_horizontal.node.on("click", (event: cc.Event): void => {
            this._switchViewDir(true);
        });

        this.btn_vertical.node.on("click", (event: cc.Event): void => {
            this._switchViewDir(false);
        });

        this.btn_show.node.on("click", (event: cc.Event): void => {
            if (!this._tableView) return;
            this._tableView.node.active = !this._tableView.node.active;
        });

        this.btn_reload.node.on("click", (event: cc.Event): void => {
            this._reloadView();
        });

        this.btn_mask.node.on("click", (event: cc.Event): void => {
            if (!this._tableView || !this._tableView.node.active) return;

            let mask_horizontal: cc.Mask = this.scrollview_horizontal.getComponent(cc.Mask);
            if (mask_horizontal) mask_horizontal.enabled = !mask_horizontal.enabled;

            let mask_vertical: cc.Mask = this.scrollview_vertical.getComponent(cc.Mask);
            if (mask_vertical) mask_vertical.enabled = !mask_vertical.enabled;
        });

        this.btn_pophide.node.on("click", (event: cc.Event): void => {
            if (!this._tableView) return;
            this._popHide = !this._popHide;
        });

        this.btn_scale.node.on("click", (event: cc.Event): void => {
            if (!this._tableView || !this._tableView.node.active) return;

            if (this._tableView.cellScale === 1) {
                this._tableView.cellScale = 0.5;
            }
            else if (this._tableView.cellScale === 0.5) {
                this._tableView.cellScale = 1;
            }
            this._tableView.reloadView(false);
        });

        this.btn_delete.node.on("click", (event: cc.Event): void => {
            if (!this._tableView || !this._tableView.node.active) return;

            let vDeleteIdx: number[] = [];
            let totalCount: number = this._tableView.getCellTotalCount();
            for (let i = 0; i < 3; ++i) {
                let index: number = Math.floor(Math.random() * totalCount);
                vDeleteIdx.push(index);
            }
            this._tableView.removeCells(vDeleteIdx, true);
        });

        this.btn_add.node.on("click", (event: cc.Event): void => {
            if (!this._tableView || !this._tableView.node.active) return;

            let sz: cc.Size = this._tableView.node.getContentSize();
            sz.width += 10;
            sz.height += 10;
            this._tableView.resetScrollVewSize(sz);
            this._tableView.node.removeComponent(cc.Widget);
            ResourceManager.getInstance().adaptWidget(this._tableView.node, true);
            this._tableView.reloadView(false);
        });

        this.btn_minus.node.on("click", (event: cc.Event): void => {
            if (!this._tableView || !this._tableView.node.active) return;

            let sz: cc.Size = this._tableView.node.getContentSize();
            sz.width -= 10;
            sz.height -= 10;
            this._tableView.resetScrollVewSize(sz);
            this._tableView.node.removeComponent(cc.Widget);
            ResourceManager.getInstance().adaptWidget(this._tableView.node, true);
            this._tableView.reloadView(false);
        });
    }

    protected start(): void {
        // 默认显示纵向
        this._switchViewDir(false);
    }

    onSVEventScrollTouchUp(arg: cc.ScrollView, tableview: TableView): void {
        // console.log("Test-Class: scrollview_horizontal touch-up");
    }

    onSVEventBounceLeft(arg: cc.ScrollView, tableview: TableView): void {
        console.log("Test-Class: scrollview_horizontal bounce-left");
        if (this._popHide) tableview.node.active = false;
    }

    onSVEventBounceRight(arg: cc.ScrollView, tableview: TableView): void {
        console.log("Test-Class: scrollview_horizontal bounce-right");
        if (this._popHide) tableview.node.active = false;
    }

    onSVEventBounceTop(arg: cc.ScrollView, tableview: TableView): void {
        console.log("Test-Class: scrollview_horizontal bounce-top");
        if (this._popHide) tableview.node.active = false;
    }

    onSVEventBounceBottom(arg: cc.ScrollView, tableview: TableView): void {
        console.log("Test-Class: scrollview_horizontal bounce-bottom");
        if (this._popHide) tableview.node.active = false;
    }

    /**
     * 切换横纵视图
     * @param horizontal 
     */
    private _switchViewDir(horizontal: boolean): void {
        this._ishorizontal = horizontal;
        if (this._ishorizontal) {
            this.scrollview_vertical.node.active = false;
            this.scrollview_horizontal.node.active = true;
            this._tableView = this.scrollview_horizontal.getComponent(TableView);
        }
        else {
            this.scrollview_vertical.node.active = true;
            this.scrollview_horizontal.node.active = false;
            this._tableView = this.scrollview_vertical.getComponent(TableView);
        }

        let datas: any[] = [];
        let typeEx: number = this._ishorizontal ? 0 : 2;

        // push 单个数据
        for (let i = 0; i < 30; ++i) {
            datas.push({ prefab_type: i % 2 + typeEx, prefab_component: TabelViewTestCell, prefab_datas: `data_${i}` })
        }

        // // push 数组数据
        // let array1: any[] = [];
        // let array2: any[] = [];
        // for (let i = 0; i < 3; ++i) {
        //     array1.push(`array1_${i}`);
        //     array2.push(`array2_${i}`);
        // }
        // datas.push({ prefab_type: 0 + typeEx, prefab_component: TabelViewTestCell, prefab_datas: array1 })
        // datas.push({ prefab_type: 1 + typeEx, prefab_component: "TabelViewTestCell", prefab_datas: array2 })

        this._tableView.bindData(datas);
        this._tableView.bindScrollEventTarget(this);
        this._tableView.reloadView();
    }

    private _reloadView(): void {
        if (!this._tableView) return;
        this._tableView.node.active = true;

        let datas: any[] = [];
        let typeEx: number = this._ishorizontal ? 0 : 2;
        let count: number = 1 + Math.floor(Math.random() * 19);
        for (let i = 0; i < count; ++i) {
            datas.push({ prefab_type: (i % 2 + typeEx), prefab_component: TabelViewTestCell, prefab_datas: `count${count}_d1_${i}` });
        }

        this._tableView.bindData(datas);
        this._tableView.reloadView(false);
    }
}
