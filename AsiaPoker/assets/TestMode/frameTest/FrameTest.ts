import cv from "../../Script/components/lobby/cv";
import { CustomToggle } from "../../Script/components/lobby/customToggle/CustomToggle";

const { ccclass, property } = cc._decorator;
@ccclass
export class FrameTest extends cc.Component {
    @property(cc.Node) node_content: cc.Node = null;
    @property(cc.Label) txt_count: cc.Label = null;
    @property(cc.EditBox) edt_row: cc.EditBox = null;
    @property(cc.EditBox) edt_col: cc.EditBox = null;
    @property(cc.Prefab) prefa_coin_1: cc.Prefab = null;
    @property(cc.Prefab) prefa_coin_2: cc.Prefab = null;
    @property(cc.SpriteFrame) coin_frame: cc.SpriteFrame = null;
    @property(CustomToggle) vToggle: CustomToggle[] = [];
    @property(CustomToggle) toggle_act: CustomToggle = null;

    protected onLoad(): void {
        cc.debug.setDisplayStats(true);
        cv.initBaseClass();
        cv.resMgr.loadBaseResource((): void => {
            cv.init();

            this.scheduleOnce((elapsed: number): void => {
                cv.StatusView.show(false);
                cv.StatusView.showElectricImgs(false);
                cv.StatusView.showLeftCircles(false);
                cv.StatusView.showSystemTime(false);
            }, 2);
        });

        this.toggle_act.node.on("toggle", this._onToggleAct, this);

        for (let i = 0; i < this.vToggle.length; ++i) {
            this.vToggle[i].node.on("toggle", (toggle: cc.Toggle): void => { this._onToggleEvent(toggle, i); }, this);
        }

        this.edt_row.node.on("editing-did-began", (event: cc.Event): void => {
            this.toggle_act.uncheck();
            for (let i = 0; i < this.vToggle.length; ++i) {
                this.vToggle[i].isChecked = false;
            }
        });

        this.edt_col.node.on("editing-did-began", (event: cc.Event): void => {
            this.toggle_act.uncheck();
            for (let i = 0; i < this.vToggle.length; ++i) {
                this.vToggle[i].isChecked = false;
            }
        });
    }

    protected start(): void {
    }

    private _onToggleAct(toggle: cc.Toggle): void {
        this._generateAction(toggle.isChecked);
    }

    private _onToggleEvent(toggle: cc.Toggle, index: number): void {
        this.node_content.destroyAllChildren();
        this.node_content.removeAllChildren(true);
        for (let i = 0; i < this.node_content.children.length; ++i) {
            this.node_content.children[i].destroy();
        }

        let row: number = Number(this.edt_row.string);
        let col: number = Number(this.edt_col.string);
        row = Math.max(row, 1);
        col = Math.max(col, 1);
        this._generateCoin(index, row, col);
    }

    /**
     * 生成金币实例
     * @param type 
     * @param row 
     * @param col 
     */
    private _generateCoin(type: number, row: number, col: number): void {
        let w: number = 0;
        let h: number = 0;
        let inst: cc.Node = null;
        switch (type) {
            case 0: {
                w = this.prefa_coin_1.data.width;
                h = this.prefa_coin_1.data.height;
                inst = cc.instantiate(this.prefa_coin_1);
            } break;

            case 1: {
                w = this.prefa_coin_2.data.width;
                h = this.prefa_coin_2.data.height;
                inst = cc.instantiate(this.prefa_coin_2);
            } break;

            case 2: {
                w = this.coin_frame.getRect().width;
                h = this.coin_frame.getRect().height;
                inst = new cc.Node();
                let sp: cc.Sprite = inst.addComponent(cc.Sprite);
                sp.type = cc.Sprite.Type.SIMPLE;
                sp.sizeMode = cc.Sprite.SizeMode.RAW;
                sp.trim = false;
                sp.spriteFrame = this.coin_frame;
            } break;

            default: break;
        }

        let offset_x: number = cc.winSize.width / (col + 1);
        let offset_y: number = cc.winSize.height / (row + 1);
        let start_x: number = -this.node.width * this.node.anchorX;
        let start_y: number = this.node.height * this.node.anchorY;

        for (let i = 0; i < row; ++i) {
            for (let j = 0; j < col; ++j) {
                let x: number = start_x + offset_x * (j + 1);
                let y: number = start_y - offset_y * (i + 1);
                let node: cc.Node = cc.instantiate(inst);
                node.setPosition(x, y);
                this.node_content.addChild(node);
            }
        }

        this.txt_count.string = "同屏数量: " + col * row;
    }

    private _generateAction(run: boolean): void {
        for (let i = 0; i < this.node_content.children.length; ++i) {
            let node: cc.Node = this.node_content.children[i];
            node.stopAllActions();
            node.scale = 1.0;
            if (run) {
                let st_mall: cc.ActionInterval = cc.scaleTo(0.5, 0.8);
                let st_big = cc.scaleTo(0.5, 1.2);
                st_mall = st_mall.easing(cc.easeIn(1.0));
                st_big = st_big.easing(cc.easeOut(1.0));
                let seq = cc.sequence(st_mall, st_big);
                node.runAction(cc.repeatForever(seq));
            }
        }
    }
}
